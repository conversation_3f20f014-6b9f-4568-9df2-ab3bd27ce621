name: Install Dependencies

on:
  workflow_call:
    inputs:
      node_version:
        required: true
        type: string

jobs:
  dependencies:
    runs-on: ubuntu-latest
    name: Install Dependencies
    steps:
      - uses: actions/checkout@v3

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn config get cacheFolder)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - uses: actions/cache@v3
        id: node-modules-cache # use this to check for `cache-hit` (`steps.node-modules-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-nodemodules-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-nodemodules-

      - uses: actions/setup-node@v3
        name: Use Node.js ${{ inputs.node_version }}
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        with:
          node-version: ${{ inputs.node_version }}

      - name: Install Deps
        run: yarn --immutable
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
