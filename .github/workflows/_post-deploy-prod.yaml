name: Post production deployment

on:
  workflow_dispatch:

jobs:
  post-deploy-prod:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Fetch production caches
        run: curl https://api.heni.com/queryCache?queryType=getPrimaryCollectionsWithDropConfigHeader&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://api.heni.com/queryCache?queryType=getLastEditionsDropConfig&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://api.heni.com/queryCache?queryType=getExcludedRegisterInterestArtworks&revalidate_key=${{ secrets.INVALIDATION_TOKEN }}

      - name: Install Dependencies
        run: yarn --immutable

      - name: Merge staging -> preview
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: staging
          target_branch: preview
          message: Merge staging into preview
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Fetch preview deployment hook
        run: curl ${{ secrets.VERCEL_DEPLOY_PREVIEW_HOOK }}

      - name: Run Prerender command
        run: ./scripts/prerender-heni-websites-pages.sh
