name: Pull Request

on:
  pull_request:
    types: [labeled]

jobs:
  install-dependencies:
    uses: ./.github/workflows/install-dependencies.yaml
    with:
      node_version: "18"

  validate-frontend:
    needs: install-dependencies
    uses: ./.github/workflows/code-validation.yaml
    with:
      node_version: "18"

  unit-tests-frontend:
    strategy:
      matrix:
        command:
          [
            "run-features-tests",
            "run-global-v5-tests",
            "run-websites-tests-1",
            "run-websites-tests-2",
            "run-websites-tests-3",
          ]
    needs: install-dependencies
    uses: ./.github/workflows/run-unit-tests.yaml
    secrets:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
    with:
      node_version: "18"
      command: ${{ matrix.command }}
