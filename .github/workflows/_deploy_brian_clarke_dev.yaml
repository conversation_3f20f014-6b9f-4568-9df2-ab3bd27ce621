name: Dev Deployment - <PERSON>
env:
  VERCEL_DEPLOY_DEV_HOOK: ${{ secrets.DEPLOY_BRIAN_CLARKE_DEV_HOOK }}

on:
  workflow_dispatch:

jobs:
  deploy-dev:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Fetch dev deployment hook
        run: curl ${{ secrets.DEPLOY_BRIAN_CLARKE_DEV_HOOK }}
