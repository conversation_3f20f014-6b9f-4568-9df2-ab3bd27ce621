name: Unit Tests

on:
  workflow_call:
    secrets:
      TURBO_TOKEN:
        required: true
      TURBO_TEAM:
        required: true
    inputs:
      node_version:
        required: true
        type: string
      command:
        required: true
        type: string
jobs:
  execute:
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn config get cacheFolder)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - uses: actions/cache@v3
        id: node-modules-cache # use this to check for `cache-hit` (`steps.node-modules-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-nodemodules-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-nodemodules-

      - name: Use Node.js ${{ inputs.node_version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ inputs.node_version }}
          cache: yarn

      - name: Install Dependencies
        run: yarn --immutable

      - name: Run command
        run: ./scripts/${{ inputs.command }}.sh
