name: Staging Deployment

on:
  push:
    branches:
      - staging

jobs:
  deploy-dev:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Fetch staging caches
        run: curl https://space-whale.no-zero.net/queryCache?queryType=getPrimaryCollectionsWithDropConfigHeader&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://space-whale.no-zero.net/queryCache?queryType=getLastEditionsDropConfig&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://space-whale.no-zero.net/queryCache?queryType=getExcludedRegisterInterestArtworks&revalidate_key=${{ secrets.INVALIDATION_TOKEN }}
