name: Code Validation

on:
  workflow_call:
    inputs:
      node_version:
        required: true
        type: string
jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn config get cacheFolder)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - uses: actions/cache@v3
        id: node-modules-cache # use this to check for `cache-hit` (`steps.node-modules-cache.outputs.cache-hit != 'true'`)
        with:
          path: node_modules
          key: ${{ runner.os }}-nodemodules-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-nodemodules-

      - name: Use Node.js ${{ inputs.node_version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ inputs.node_version }}
          cache: yarn

      - name: Install Dependencies
        run: yarn --immutable

      - name: Rename heni-websites env file
        run: mv ./heni-websites/.example.env ./heni-websites/.env

      - name: Rename arteye env file
        run: mv ./arteye/.example.env ./arteye/.env

      - name: Rename production-services env file
        run: mv ./production-services/.example.env ./production-services/.env

      - name: Rename pca-image-archiver env file
        run: mv ./pca-image-archiver/.example.env ./pca-image-archiver/.env

      - name: Rename keith-cunningham env file
        run: mv ./keith-cunningham/.example.env ./keith-cunningham/.env

      - name: Rename brian-clarke env file
        run: mv ./brian-clarke/.example.env ./brian-clarke/.env

      - name: Rename art-data env file
        run: mv ./art-data/.example.env ./art-data/.env

      - name: Rename publish-dashboard env file
        run: mv ./publish-dashboard/.example.env ./publish-dashboard/.env

      - name: Rename heni-websites-utils env file
        run: mv ./heni-websites-utils/.example.env ./heni-websites-utils/.env

      - name: Rename libraries env file
        run: mv ./libraries/.example.env ./libraries/.env

      - name: Check linting
        run: yarn run lint

      - name: Check code
        run: yarn run check
