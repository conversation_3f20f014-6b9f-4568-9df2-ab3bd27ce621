name: Dev Deployment
env:
  VERCEL_DEPLOY_DEV_HOOK: ${{ secrets.VERCEL_DEPLOY_DEV_HOOK }}

on:
  push:
    branches:
      - main

jobs:
  deploy-dev:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Fetch dev deployment hook
        run: curl ${{ secrets.VERCEL_DEPLOY_DEV_HOOK }}

      - name: Fetch dev caches
        run: curl https://persian-apple.no-zero.net/queryCache?queryType=getPrimaryCollectionsWithDropConfigHeader&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://persian-apple.no-zero.net/queryCache?queryType=getLastEditionsDropConfig&revalidate_key=${{ secrets.INVALIDATION_TOKEN }} && curl https://persian-apple.no-zero.net/queryCache?queryType=getExcludedRegisterInterestArtworks&revalidate_key=${{ secrets.INVALIDATION_TOKEN }}

      - name: Merge main -> dev-preview
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: main
          target_branch: dev-preview
          message: Merge main into dev-preview
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Fetch dev deployment hook
        run: curl ${{ secrets.VERCEL_DEPLOY_DEV_PREVIEW_HOOK }}
