/* eslint-disable consistent-default-export-name/default-export-match-filename */
import type { CodegenConfig } from '@graphql-codegen/cli';
import { BasicDevConfig } from './src/lib/constants/basic-dev-config';

const codegenCustom: CodegenConfig = {
	hooks: { afterAllFileWrite: ['prettier --write', 'eslint --fix'] },
	schema: [
		{
			[BasicDevConfig.CustomGraphqlApiUrl]: {
				headers: {
					authorization: `Bearer ${BasicDevConfig.GraphqlApiKey}`,
				},
			},
		},
	],
	documents: 'src/**/custom-queries/**/!(*.generated).{ts,tsx}',
	generates: {
		'./src/gql/types-custom.ts': {
			plugins: ['typescript'],
		},
		'./src/': {
			preset: 'near-operation-file',
			presetConfig: {
				extension: '.generated.ts',
				baseTypesPath: './gql/types-custom.ts',
				folder: '__generated__',
			},
			plugins: ['typescript-operations', 'typed-document-node'],
		},
	},
};
export default codegenCustom;
