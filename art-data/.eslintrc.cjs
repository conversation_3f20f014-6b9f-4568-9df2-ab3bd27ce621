const websitesPatterns = ['editions', 'nft'].flatMap((context) => [
	`**/../${context}/**`,
	`**/../websites/${context}/**`,
]);

const featuresPatterns = ['editions'].flatMap((context) => [
	`**/../${context}/**`,
	`**/../websites/${context}/**`,
]);

const globalPatterns = [
	`**/../global/**`,
	`**/../websites/**`,
	`**/../features/**`,
];

module.exports = {
	root: true,
	extends: [
		'eslint:recommended',
		'plugin:consistent-default-export-name/fixed',
		'plugin:import/recommended',
		'plugin:@typescript-eslint/recommended',
		'plugin:svelte/recommended',
		'prettier',
	],
	parser: '@typescript-eslint/parser',
	plugins: ['@typescript-eslint'],
	parserOptions: {
		sourceType: 'module',
		ecmaVersion: 2020,
		extraFileExtensions: ['.svelte'],
	},
	env: {
		browser: true,
		es2017: true,
		node: true,
	},
	overrides: [
		{
			files: ['*.svelte'],
			parser: 'svelte-eslint-parser',
			parserOptions: {
				parser: '@typescript-eslint/parser',
			},
		},
	],
	rules: {
		'import/no-unresolved': 'off',
		'import/order': [
			'error',
			{
				alphabetize: {
					order: 'asc',
					caseInsensitive: true,
				},
			},
		],
		'no-restricted-imports': [
			'error',
			{
				patterns: [
					...websitesPatterns,
					...featuresPatterns,
					...globalPatterns,
				].filter((value, index, array) => array.indexOf(value) === index),
			},
		],
		'@typescript-eslint/consistent-type-imports': [
			'error',
			{
				disallowTypeAnnotations: false,
			},
		],
	},
};
