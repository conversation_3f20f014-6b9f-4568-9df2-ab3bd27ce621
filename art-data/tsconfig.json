{
	"extends": "./.svelte-kit/tsconfig.json",
	"compilerOptions": {
		"allowJs": true,
		"checkJs": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"resolveJsonModule": true,
		"skipLibCheck": true,
		"sourceMap": true,
		"strict": true,
		"ignoreDeprecations": "5.0",
		"importsNotUsedAsValues": "preserve",
		"verbatimModuleSyntax": false,
		"types": ["vitest/globals", "vitest-dom/extend-expect"]
	},
	//
	// If you want to overwrite includes/excludes, make sure to copy over the relevant includes/excludes
	// from the referenced tsconfig.json - TypeScript does not merge them in
	"exclude": [
		"./node_modules/**",
		"./.svelte-kit/[!ambient.d.ts]**",
		"./src/service-worker.js",
		"./src/service-worker.ts",
		"./src/service-worker.d.ts",
		"./src/gql/**"
	]
}
