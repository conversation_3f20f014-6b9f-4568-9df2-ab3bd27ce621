import { sentrySvelteKit } from '@sentry/sveltekit';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig((({
	mode,
}: {
	mode: Parameters<typeof loadEnv>[0];
}) => {
	const env = loadEnv(mode, process.cwd());

	return {
		plugins: [
			...(env.VITE_APP_ENV === 'production' ? [sentrySvelteKit()] : []),
			sveltekit(),
		],
		test: {
			globals: true,
			environment: 'jsdom',
			setupFiles: './src/setupTests.js',
		},
	};
}) as unknown as Parameters<typeof defineConfig>[0]);
