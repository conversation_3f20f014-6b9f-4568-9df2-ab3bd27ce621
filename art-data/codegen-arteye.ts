/* eslint-disable consistent-default-export-name/default-export-match-filename */
import type { CodegenConfig } from '@graphql-codegen/cli';
import { BasicDevConfig } from './src/lib/constants/basic-dev-config';

const codegenArteye: CodegenConfig = {
	hooks: { afterOneFileWrite: ['prettier --write', 'eslint --fix'] },
	schema: [
		{
			[BasicDevConfig.ArteyeGraphqlApiUrl]: {
				headers: {
					authorization: `Bearer ${BasicDevConfig.ArteyeGraphqlApiKey}`,
				},
			},
		},
	],
	documents: 'src/**/arteye-queries/**/!(*.generated).{ts,tsx}',
	generates: {
		'./src/gql/types-arteye.ts': {
			plugins: ['typescript'],
		},
		'./src/': {
			preset: 'near-operation-file',
			presetConfig: {
				extension: '.generated.ts',
				baseTypesPath: './gql/types-arteye.ts',
				folder: '__generated__',
			},
			plugins: ['typescript-operations', 'typed-document-node'],
		},
	},
};

export default codegenArteye;
