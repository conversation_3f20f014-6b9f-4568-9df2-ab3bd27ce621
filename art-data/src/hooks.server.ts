import { handleErrorWithSentry, sentryHandle } from '@sentry/sveltekit';
import * as Sentry from '@sentry/sveltekit';
import { sequence } from '@sveltejs/kit/hooks';
import { PUBLIC_APP_ENV } from '$env/static/public';
import { generateHooks } from '$global/features/auth/routes/generateHooks/generateHooks';
import { Cookies } from '$lib/constants/cookies';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { getAdditionalCookieData } from '$lib/utils/getAdditionalCookieData/getAdditionalCookieData';
import { AuthRefreshDocument } from '$lib/websites/login/system-queries/__generated__/refresh.generated';

export const authHandle = generateHooks({
	cookieKey: Cookies.User,
	RefreshDocument: AuthRefreshDocument,
	gqlClient: gqlClientSystem,
	env: PUBLIC_APP_ENV,
	getAdditionalCookieData: getAdditionalCookieData,
});

Sentry.init({
	dsn: 'https://<EMAIL>/10',
	tracesSampleRate: 1.0,
});

// If you have custom handlers, make sure to place them after `sentryHandle()` in the `sequence` function.
export const handle = sequence(sentryHandle(), authHandle);

// If you have a custom error handler, pass it to `handleErrorWithSentry`
export const handleError = handleErrorWithSentry();
