<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import { page } from '$app/state';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { imagesFinalizeReview } from '$lib/features/final-review/utils/imagesFinalizeReview/imagesFinalizeReview';
	import { UpdateVisitArtworkDocument } from '$lib/queries/__generated__/updateVisitArtwork.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { GalleryOfferingsFinalReview } from '$lib/websites/gallery-offerings/pages/GalleryOfferingsFinalReview';

	const updateVisitArtwork = createMutation(
		getMutation(
			UpdateVisitArtworkDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);
</script>

<GalleryOfferingsFinalReview
	updateArtwork={updateVisitArtwork}
	updateChecklist={null}
	finalizeReview={imagesFinalizeReview}
/>
