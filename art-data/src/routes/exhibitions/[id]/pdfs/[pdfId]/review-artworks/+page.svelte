<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import { page } from '$app/state';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { UpdatePdfArtworkDocument } from '$lib/features/edit-extracted-images/queries/__generated__/updatePdfArtwork.generated';
	import { pdfFinalizeReview } from '$lib/features/final-review/utils/pdfFinalizeReview/pdfFinalizeReview';
	import { UpdatePdfDocument } from '$lib/queries/__generated__/updatePdf.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { ExhibitionsFinalReview } from '$lib/websites/exhibitions/pages/ExhibitionsFinalReview';

	const updatePdf = createMutation(
		getMutation(
			UpdatePdfDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updatePdfArtwork = createMutation(
		getMutation(
			UpdatePdfArtworkDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);
</script>

<ExhibitionsFinalReview
	updateArtwork={updatePdfArtwork}
	updateChecklist={updatePdf}
	finalizeReview={pdfFinalizeReview}
/>
