import { redirect } from '@sveltejs/kit';
import type { HomePageServerLoadEvent } from './types';
import { Cookies } from '$lib/constants/cookies';
import { Roles } from '$lib/constants/roles';
import { Routes } from '$lib/constants/routes';

export const load = async ({ parent, cookies }: HomePageServerLoadEvent) => {
	const parentData = await parent();

	// if (parentData.user.role === Roles.Admin) {
	// 	redirect(303, Routes.Welcome);
	// }

	// if (parentData.user.role === Roles.Freelancer) {
	// 	redirect(303, Routes.WelcomeFreelancer);
	// }

	// cookies.delete(Cookies.User, { path: Routes.Home });
	// redirect(303, Routes.Login);

	redirect(303, Routes.Welcome);
};
