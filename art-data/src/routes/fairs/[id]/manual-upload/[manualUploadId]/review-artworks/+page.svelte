<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import { page } from '$app/state';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { manualUploadFinalizeReview } from '$lib/features/final-review/utils/manualUploadFinalizeReview/manualUploadFinalizeReview';
	import { UpdateManuallyAddedArtworkDocument } from '$lib/queries/__generated__/updateManuallyAddedArtwork.generated';
	import { UpdateManualUploadDocument } from '$lib/queries/__generated__/updateManualUpload.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { FairsFinalReview } from '$lib/websites/fairs/pages/FairsFinalReview';

	const updateManualUpload = createMutation(
		getMutation(
			UpdateManualUploadDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updateManuallyAddedArtwork = createMutation(
		getMutation(
			UpdateManuallyAddedArtworkDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);
</script>

<FairsFinalReview
	updateArtwork={updateManuallyAddedArtwork}
	updateChecklist={updateManualUpload}
	finalizeReview={manualUploadFinalizeReview}
/>
