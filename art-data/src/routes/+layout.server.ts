import { generateLayoutServer } from '$global/features/auth/routes/generateLayoutServer/generateLayoutServer';
import { Cookies } from '$lib/constants/cookies';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import type { User } from '$lib/types/types';
import type { GetRoleQuery } from '$lib/websites/login/system-queries/__generated__/getRole.generated';
import { GetRoleDocument } from '$lib/websites/login/system-queries/__generated__/getRole.generated';

export const load = generateLayoutServer<
	typeof GetRoleDocument,
	GetRoleQuery,
	User
>({
	cookieKey: Cookies.User,
	GetRoleDocument,
	gqlClient: gqlClientSystem,
	getRoleCallback: async ({ response, user }) => {
		return {
			...user,
			firstName: response.users_me?.first_name ?? '',
			role: response.users_me?.role?.name ?? '',
		};
	},
});
