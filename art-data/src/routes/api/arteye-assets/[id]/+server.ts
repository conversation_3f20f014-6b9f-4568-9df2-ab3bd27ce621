import type { ServerLoadEvent } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { Config } from '$lib/constants/config';

export const GET = async ({ params, url }: ServerLoadEvent) => {
	if (!params.id) {
		error(403, 'Forbidden');
	}

	const accessToken = url.searchParams.get('access_token');

	if (!accessToken) {
		error(403, 'Forbidden');
	}

	return fetch(
		`${Config.ArteyeGraphqlApiDomain}/assets/${params.id}${
			accessToken ? `?access_token=${accessToken}` : ''
		}`
	);
};
