import { QueryClient } from '@tanstack/svelte-query';
import type { RootLayoutLoad } from './types';
import { browser } from '$app/environment';

export const load: RootLayoutLoad = async ({ data }) => {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				retry: false,
				enabled: browser,
				staleTime: Infinity,
				refetchOnMount: false,
				refetchOnWindowFocus: false,
			},
		},
	});

	return {
		...data,
		queryClient,
	};
};
