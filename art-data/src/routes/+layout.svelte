<script lang="ts">
	import '../app.css';
	import { QueryClientProvider } from '@tanstack/svelte-query';
	import type { RootLayoutData } from './types';
	import { Toasts } from '$global/components/Toasts';
	import { Header } from '$lib/features/navigation/components/Header';

	interface Props {
		data: RootLayoutData;
		children?: import('svelte').Snippet;
	}

	let { data, children }: Props = $props();
</script>

<QueryClientProvider client={data.queryClient}>
	<Header />
	{@render children?.()}
</QueryClientProvider>

<Toasts />
