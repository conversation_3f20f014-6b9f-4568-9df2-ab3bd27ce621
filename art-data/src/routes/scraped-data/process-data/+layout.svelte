<script lang="ts">
	import { onMount } from 'svelte';
	import { formatEntity } from '$lib/websites/scraped-data/pages/ProcessDataEntities/EntityAutocomplete';
	import { GetEntitiesDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { formatArtist } from '$lib/websites/scraped-data/pages/ProcessDataArtistInfo/ArtistAutocomplete/ArtistAutocomplete.svelte';
	import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
	import { GetLegacyIdDocument } from '$lib/custom-arteye-queries/__generated__/getLegacyId.generated';
	import { GetArtistsDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getArtists.generated';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
	import { GetScrapedEntitiesDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/getScrapedEntities.generated';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { GetScrapedArtistsDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/getScrapedArtists.generated';
	import { page } from '$app/state';
	import type { LayoutData } from './$types';
	import type { GetArtistsQuery } from '$lib/websites/scraped-data/arteye-queries/__generated__/getArtists.generated';
	import { fetchScrapedArtworks } from '$lib/websites/scraped-data/utils/fetchScrapedArtworks/fetchScrapedArtworks';
	import { getArtworkFeedsStore } from '$lib/websites/scraped-data/utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { getPageData } from '$global/utils/getPageData/getPageData';

	const {
		checkEventsAreMatches,
		artworkFeeds,
		scrapedArtworksStore,
		artistSelectedOptionsStore,
		entitiesSelectedOptionsStore,
		scrapedEntitiesStore,
		scrapedArtistsStore,
	} = getArtworkFeedsStore();

	interface Props {
		children: import('svelte').Snippet;
	}

	let data = getPageData<LayoutData>(page.data);
	let { children }: Props = $props();

	onMount(() => {
		checkEventsAreMatches();

		fetchScrapedArtworks(data, $artworkFeeds.scrapedArtworkIds).then(
			(artworks) => {
				scrapedArtworksStore.set(artworks);
			}
		);

		const fetchArtists = async () => {
			const scrapedArtistsRes = await gqlClientCustom.request(
				GetScrapedArtistsDocument,
				{
					input: {
						scrapedArtworkIds: $artworkFeeds.scrapedArtworkIds,
					},
				},
				getAuthorizationHeaders(data)
			);

			const scrapedArtistsArr = (
				scrapedArtistsRes?.getScrapedArtists || []
			)?.map((artist) => ({
				...artist,
			}));

			const scrapedArtistProcessedIds = scrapedArtistsArr
				.map((scrapedArtist) => scrapedArtist?.processed_artist_id)
				.filter(Boolean) as string[];

			let processedArtists: GetArtistsQuery['artist'] = [];

			if (scrapedArtistProcessedIds.length) {
				const processedArtistsRes = await gqlClientArteye.request(
					GetArtistsDocument,
					{
						filter: { id: { _in: scrapedArtistProcessedIds } },
					},
					getAuthorizationHeaders({
						user: { access_token: data.user?.arteye_token },
					})
				);

				processedArtists = processedArtistsRes?.artist;

				if (!isOnDev()) {
					const legacyIdRes = await Promise.all(
						processedArtists.map((processedArtist) =>
							gqlClientCustomArteye.request(
								GetLegacyIdDocument,
								{ id: processedArtist.id, collection: 'artist' },
								getAuthorizationHeaders({
									user: { access_token: page.data.user.arteye_token },
								})
							)
						)
					);

					processedArtists = processedArtists.map((processedArtist, i) => ({
						...processedArtist,
						legacy_id: legacyIdRes[i].getLegacyId?.legacyId,
					}));
				}
			}

			let manualArtistId = 0;

			const artistSelectedOptions = (scrapedArtistsArr || []).reduce(
				(accumulator: Record<string, OptionType | null>, scrapedArtist) => {
					if (!scrapedArtist) {
						return accumulator;
					}

					const processedArtist = processedArtists.find(
						(artist) => artist.id === scrapedArtist?.processed_artist_id
					);

					const selectedOptionValue = processedArtist
						? (formatArtist(
								processedArtist as Parameters<typeof formatArtist>[0]
							) as OptionType)
						: null;

					if (!scrapedArtist?.scraped_artist_id) {
						manualArtistId = manualArtistId + 1;
						scrapedArtist.scraped_artist_id = `${manualArtistId - 1}`;
						return {
							...accumulator,
							[`${manualArtistId - 1}`]: selectedOptionValue,
						};
					}

					return {
						...accumulator,
						[scrapedArtist.scraped_artist_id]: selectedOptionValue,
					};
				},
				{}
			);

			artistSelectedOptionsStore.set(artistSelectedOptions);

			scrapedArtistsStore.set([...scrapedArtistsArr] as Parameters<
				Parameters<typeof scrapedArtistsStore.subscribe>[0]
			>[0]);
		};

		const fetchEntities = async () => {
			let entitiesSelectedOptions: null | Record<string, OptionType | null> =
				null;

			const scrapedEntitiesRes = await gqlClientCustom.request(
				GetScrapedEntitiesDocument,
				{
					input: {
						scrapedArtworkIds: $artworkFeeds.scrapedArtworkIds,
					},
				},
				getAuthorizationHeaders(data)
			);

			const scrapedEntities = scrapedEntitiesRes?.getScrapedEntities;
			scrapedEntitiesStore.set(scrapedEntities);

			const scrapedEntitiesProcessedIds = scrapedEntities
				.map((scrapedEntity) => scrapedEntity?.processed_entity_id)
				.filter(Boolean) as string[];

			if (!scrapedEntitiesProcessedIds.length) {
				entitiesSelectedOptions = scrapedEntities.reduce(
					(accumulator, scrapedEntity) => ({
						...accumulator,
						[scrapedEntity.id]: null,
					}),
					{}
				);

				entitiesSelectedOptionsStore.set(entitiesSelectedOptions);
				return;
			}

			const processedEntitiesRes = await gqlClientArteye.request(
				GetEntitiesDocument,
				{
					filter: { id: { _in: scrapedEntitiesProcessedIds } },
				},
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			const processedEntities = processedEntitiesRes?.entity;

			entitiesSelectedOptions = scrapedEntities.reduce(
				(accumulator, scrapedEntity) => {
					const processedEntity = processedEntities.find(
						(pEntity) => pEntity.id === scrapedEntity.processed_entity_id
					);

					return {
						...accumulator,
						[scrapedEntity.id]: processedEntity
							? (formatEntity(
									processedEntity as Parameters<typeof formatEntity>[0]
								) as OptionType)
							: null,
					};
				},
				{}
			);

			entitiesSelectedOptionsStore.set(entitiesSelectedOptions);
		};

		fetchArtists();
		fetchEntities();
	});
</script>

{@render children()}
