import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetFavouriteArtistsDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getFavouriteArtists.generated';
import type { ScrapedDataPageServerLoadEvent } from '$routes/scraped-data/types';

export const load = async ({ parent }: ScrapedDataPageServerLoadEvent) => {
	const data = await parent();
	const favouriteArtistsRes = await gqlClientArteye.request(
		GetFavouriteArtistsDocument,
		{ filter: { status: { key: { _neq: 'archived' } } } },
		getAuthorizationHeaders({
			user: { access_token: data.user?.arteye_token },
		})
	);

	const usersRes = await gqlClientSystem.request(
		GetUsersDocument,
		{},
		getAuthorizationHeaders({ user: { access_token: data.user?.access_token } })
	);

	return {
		...data,
		favouriteArtists: favouriteArtistsRes.favourite_artist,
		users: usersRes.users,
	};
};
