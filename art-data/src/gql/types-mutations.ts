export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	JSON: { input: any; output: any };
};

export enum AmlCustomerType {
	Individual = 'INDIVIDUAL',
	LegalEntity = 'LEGAL_ENTITY',
}

export type AmlFormInput = {
	additional_purchaser?: InputMaybe<Scalars['Boolean']['input']>;
	additional_purchaser_contact_info?: InputMaybe<Scalars['String']['input']>;
	customer_type?: InputMaybe<AmlCustomerType>;
	date_of_birth?: InputMaybe<Scalars['String']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	invoice_address_1?: InputMaybe<Scalars['String']['input']>;
	invoice_address_2?: InputMaybe<Scalars['String']['input']>;
	invoice_city?: InputMaybe<Scalars['String']['input']>;
	invoice_country?: InputMaybe<Scalars['String']['input']>;
	invoice_postal_code?: InputMaybe<Scalars['String']['input']>;
	invoice_region?: InputMaybe<Scalars['String']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	legal_entity_name?: InputMaybe<Scalars['String']['input']>;
	primary_nationality?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_address_1?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_address_2?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_city?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_country?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_postal_code?: InputMaybe<Scalars['String']['input']>;
	registered_legal_entity_region?: InputMaybe<Scalars['String']['input']>;
	request_id: Scalars['String']['input'];
	residential_address_1?: InputMaybe<Scalars['String']['input']>;
	residential_address_2?: InputMaybe<Scalars['String']['input']>;
	residential_city?: InputMaybe<Scalars['String']['input']>;
	residential_country?: InputMaybe<Scalars['String']['input']>;
	residential_postal_code?: InputMaybe<Scalars['String']['input']>;
	residential_region?: InputMaybe<Scalars['String']['input']>;
	shipping_address_1?: InputMaybe<Scalars['String']['input']>;
	shipping_address_2?: InputMaybe<Scalars['String']['input']>;
	shipping_city?: InputMaybe<Scalars['String']['input']>;
	shipping_country?: InputMaybe<Scalars['String']['input']>;
	shipping_postal_code?: InputMaybe<Scalars['String']['input']>;
	shipping_region?: InputMaybe<Scalars['String']['input']>;
};

export enum AmlRequestStatus {
	Complete = 'COMPLETE',
	NotFound = 'NOT_FOUND',
	Processing = 'PROCESSING',
	Valid = 'VALID',
}

export type ApplicationInfo = {
	__typename?: 'ApplicationInfo';
	about_yourself?: Maybe<Scalars['String']['output']>;
	accept_partial_fulfilment: Scalars['Boolean']['output'];
	contact_info_sharing?: Maybe<Array<Maybe<ContactInfoSharing>>>;
	discord_id?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	instagram_handle?: Maybe<Scalars['String']['output']>;
	linkedin_username?: Maybe<Scalars['String']['output']>;
	order: Scalars['String']['output'];
	twitter_handle?: Maybe<Scalars['String']['output']>;
	wallet_address?: Maybe<Scalars['String']['output']>;
};

export type Article = {
	__typename?: 'Article';
	artists?: Maybe<Array<Maybe<ArticleArtists>>>;
	attribution?: Maybe<Scalars['String']['output']>;
	categories?: Maybe<Array<Maybe<ArticleCategories>>>;
	contentLang?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['String']['output']>;
	date_updated?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	domain?: Maybe<Scalars['String']['output']>;
	favicon?: Maybe<Scalars['String']['output']>;
	geotag?: Maybe<ArticleInfo>;
	globalCategory?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	originalImageUrl?: Maybe<Scalars['String']['output']>;
	publishDate?: Maybe<Scalars['Date']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	url?: Maybe<Scalars['String']['output']>;
	visibility?: Maybe<Scalars['String']['output']>;
};

export type ArticleArtists = {
	__typename?: 'ArticleArtists';
	HENI_News_Artists_id?: Maybe<ArticleInfo>;
};

export type ArticleCategories = {
	__typename?: 'ArticleCategories';
	HENI_News_Categories_id?: Maybe<ArticleInfo>;
};

export type ArticleInfo = {
	__typename?: 'ArticleInfo';
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type CognitoUserPreferences = {
	__typename?: 'CognitoUserPreferences';
	id: Scalars['ID']['output'];
	newsletter_opt_in?: Maybe<Scalars['Boolean']['output']>;
	saved_tags?: Maybe<Array<Maybe<SavedTag>>>;
};

export type CognitoUserPreferencesInput = {
	newsletter_opt_in?: InputMaybe<Scalars['Boolean']['input']>;
	saved_tags?: InputMaybe<Array<InputMaybe<SavedTagInput>>>;
};

export type ContactInfoSharing = {
	__typename?: 'ContactInfoSharing';
	name: Scalars['String']['output'];
	opt_in: Scalars['Boolean']['output'];
};

export type ContactInfoSharingInput = {
	name: Scalars['String']['input'];
	opt_in: Scalars['Boolean']['input'];
};

export type CreateApplicationInfoInput = {
	about_yourself?: InputMaybe<Scalars['String']['input']>;
	accept_partial_fulfilment: Scalars['Boolean']['input'];
	contact_info_sharing?: InputMaybe<Array<InputMaybe<ContactInfoSharingInput>>>;
	discord_id?: InputMaybe<Scalars['String']['input']>;
	instagram_handle?: InputMaybe<Scalars['String']['input']>;
	linkedin_username?: InputMaybe<Scalars['String']['input']>;
	order: Scalars['String']['input'];
	twitter_handle?: InputMaybe<Scalars['String']['input']>;
	wallet_address?: InputMaybe<Scalars['String']['input']>;
};

export type Images = {
	__typename?: 'Images';
	banner_hex?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	order_index?: Maybe<Scalars['Int']['output']>;
};

export type Mutation = {
	__typename?: 'Mutation';
	burnNftTokens: Scalars['Boolean']['output'];
	claim?: Maybe<Scalars['JSON']['output']>;
	createApplicationInfo?: Maybe<ApplicationInfo>;
	createNftTokenProducts: Array<Maybe<Scalars['String']['output']>>;
	createToken?: Maybe<TokenResponse>;
	deleteCognitoUserPreferences?: Maybe<Scalars['Boolean']['output']>;
	emailsignup?: Maybe<NewsletterResponse>;
	primaryapplicationregister?: Maybe<PrimaryApplicationRegistrationResponse>;
	register?: Maybe<Scalars['JSON']['output']>;
	submitAMLForm: SubmitAmlFormResponse;
	updateCognitoUserPreferences?: Maybe<CognitoUserPreferences>;
	upsertAMLForm: UpsertAmlFormResponse;
};

export type MutationBurnNftTokensArgs = {
	nftTokenIds: Array<Scalars['String']['input']>;
	walletAddress: Scalars['String']['input'];
};

export type MutationClaimArgs = {
	code: Scalars['String']['input'];
	wallet_address: Scalars['String']['input'];
};

export type MutationCreateApplicationInfoArgs = {
	input: CreateApplicationInfoInput;
};

export type MutationCreateNftTokenProductsArgs = {
	nftTokenVariants: Array<NftTokenVariant>;
	productType: NftProductTypes;
};

export type MutationCreateTokenArgs = {
	type?: InputMaybe<TokenType>;
};

export type MutationEmailsignupArgs = {
	email: Scalars['String']['input'];
	list_id?: InputMaybe<Scalars['Int']['input']>;
};

export type MutationPrimaryapplicationregisterArgs = {
	city: Scalars['String']['input'];
	company?: InputMaybe<Scalars['String']['input']>;
	country: Scalars['String']['input'];
	discord_handle?: InputMaybe<Scalars['String']['input']>;
	email_address: Scalars['String']['input'];
	fingerprint_request_id?: InputMaybe<Scalars['String']['input']>;
	g_recaptcha_response?: InputMaybe<Scalars['String']['input']>;
	ig_handle?: InputMaybe<Scalars['String']['input']>;
	ip_address?: InputMaybe<Scalars['String']['input']>;
	linkedin_profile?: InputMaybe<Scalars['String']['input']>;
	name: Scalars['String']['input'];
	pay_by_bank_transfer?: InputMaybe<Scalars['ID']['input']>;
	pay_by_cryptocurrency?: InputMaybe<Scalars['ID']['input']>;
	pay_by_currency_nfts?: InputMaybe<Scalars['ID']['input']>;
	pay_by_currency_physicals?: InputMaybe<Scalars['ID']['input']>;
	phone_number: Scalars['String']['input'];
	post_zip_code: Scalars['String']['input'];
	preference_five?: InputMaybe<Scalars['ID']['input']>;
	preference_four?: InputMaybe<Scalars['ID']['input']>;
	preference_one?: InputMaybe<Scalars['ID']['input']>;
	preference_three?: InputMaybe<Scalars['ID']['input']>;
	preference_two?: InputMaybe<Scalars['ID']['input']>;
	primary_collection: Scalars['Int']['input'];
	street_address: Scalars['String']['input'];
	subscribe_to_newsletter?: InputMaybe<Scalars['ID']['input']>;
	supporting_text_about_collection?: InputMaybe<Scalars['String']['input']>;
	supporting_text_about_collector?: InputMaybe<Scalars['String']['input']>;
	timestamp: Scalars['String']['input'];
	twitter_handle?: InputMaybe<Scalars['String']['input']>;
	user_agent?: InputMaybe<Scalars['String']['input']>;
	visitor?: InputMaybe<Scalars['String']['input']>;
	wallet_address?: InputMaybe<Scalars['String']['input']>;
};

export type MutationRegisterArgs = {
	email: Scalars['String']['input'];
	event: Scalars['Int']['input'];
};

export type MutationSubmitAmlFormArgs = {
	request_id: Scalars['String']['input'];
};

export type MutationUpdateCognitoUserPreferencesArgs = {
	cognitoUserPreferences: CognitoUserPreferencesInput;
};

export type MutationUpsertAmlFormArgs = {
	input: AmlFormInput;
};

export type NewsletterResponse = {
	__typename?: 'NewsletterResponse';
	email: Scalars['String']['output'];
	list_id: Scalars['Int']['output'];
};

export type NftCollection = {
	__typename?: 'NftCollection';
	artist?: Maybe<Scalars['String']['output']>;
	banner_hex?: Maybe<Scalars['String']['output']>;
	contract_address: Scalars['String']['output'];
	description?: Maybe<Scalars['String']['output']>;
	include_nft_number_in_title?: Maybe<Scalars['Boolean']['output']>;
	networks?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	num_items?: Maybe<Scalars['Int']['output']>;
	slug?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
};

export enum NftProductTypes {
	Print = 'PRINT',
	Redemption = 'REDEMPTION',
}

export type NftToken = {
	__typename?: 'NftToken';
	featured_image?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	network?: Maybe<Scalars['String']['output']>;
	nft_collection: NftCollection;
	nft_number: Scalars['String']['output'];
	title: Scalars['String']['output'];
	token_id: Scalars['String']['output'];
};

export type PrimaryApplicationRegistrationResponse = {
	__typename?: 'PrimaryApplicationRegistrationResponse';
	application_reference?: Maybe<Scalars['String']['output']>;
	errors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	message?: Maybe<Scalars['String']['output']>;
	preferences?: Maybe<Scalars['JSON']['output']>;
	success: Scalars['Boolean']['output'];
};

export type Query = {
	__typename?: 'Query';
	exampleAuth?: Maybe<Scalars['JSON']['output']>;
	getCognitoUserPreferences?: Maybe<CognitoUserPreferences>;
	getNftOwnershipByWallet: Scalars['Boolean']['output'];
	listNftsByWallet: Array<Maybe<NftToken>>;
	nftOriginalImages: Array<Maybe<Scalars['String']['output']>>;
	searchArticles: Array<Maybe<Article>>;
	validateAMLRequest: AmlRequestStatus;
};

export type QueryGetNftOwnershipByWalletArgs = {
	contractAddress: Scalars['String']['input'];
	tokenId: Scalars['String']['input'];
	walletAddress: Scalars['String']['input'];
};

export type QueryListNftsByWalletArgs = {
	contractAddress?: InputMaybe<Scalars['String']['input']>;
	walletAddress: Scalars['String']['input'];
};

export type QueryNftOriginalImagesArgs = {
	contractAddress: Scalars['String']['input'];
	signature?: InputMaybe<Scalars['String']['input']>;
	tokenId: Scalars['String']['input'];
	transientTokenId?: InputMaybe<Scalars['String']['input']>;
	variants?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QuerySearchArticlesArgs = {
	categoryId?: InputMaybe<Scalars['String']['input']>;
	globalCategory?: InputMaybe<Scalars['String']['input']>;
	limit: Scalars['Int']['input'];
	page?: InputMaybe<Scalars['String']['input']>;
	searchPhrase: Scalars['String']['input'];
	visibility?: InputMaybe<Scalars['String']['input']>;
};

export type QueryValidateAmlRequestArgs = {
	request_id: Scalars['String']['input'];
};

export type SavedTag = {
	__typename?: 'SavedTag';
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	type: SavedTagType;
};

export type SavedTagInput = {
	id: Scalars['ID']['input'];
	type: SavedTagType;
};

export enum SavedTagType {
	Artist = 'ARTIST',
	Geotag = 'GEOTAG',
}

export type SubmitAmlFormResponse = {
	__typename?: 'SubmitAMLFormResponse';
	aml_id: Scalars['String']['output'];
	identity_verification_client_secret?: Maybe<Scalars['String']['output']>;
	identity_verification_url?: Maybe<Scalars['String']['output']>;
	request_id: Scalars['String']['output'];
};

export type TokenResponse = {
	__typename?: 'TokenResponse';
	nonce: Scalars['String']['output'];
	tokenId: Scalars['String']['output'];
};

export enum TokenType {
	ColorRhythms = 'COLOR_RHYTHMS',
	Empresses = 'EMPRESSES',
	GreatExpectations = 'GREAT_EXPECTATIONS',
	GreetingsFromGizah = 'GREETINGS_FROM_GIZAH',
	Spins = 'SPINS',
	TheCurrency = 'THE_CURRENCY',
}

export type UpsertAmlFormResponse = {
	__typename?: 'UpsertAMLFormResponse';
	aml_id: Scalars['String']['output'];
	request_id: Scalars['String']['output'];
};

export type Directus_Files = {
	__typename?: 'directus_files';
	description?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	gallery?: Maybe<Array<Maybe<Images>>>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type NftTokenVariant = {
	id: Scalars['String']['input'];
	variant?: InputMaybe<Scalars['String']['input']>;
};
