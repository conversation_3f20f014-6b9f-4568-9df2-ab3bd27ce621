export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	DateOrYear: { input: any; output: any };
	DateTime: { input: any; output: any };
	Time: { input: any; output: any };
};

export type ActivityAndSource = {
	__typename?: 'ActivityAndSource';
	artwork_activity: Artwork_Activity;
	pipeline_source?: Maybe<Pipeline_Source>;
};

export type ActivitySearchInput = {
	activityFilters?: InputMaybe<ArtworkActivitySearchFilters>;
	artworkFilters?: InputMaybe<ArtworkSearchFilters>;
	commonFilters?: InputMaybe<CommonSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	paginationDirection?: InputMaybe<PaginationDirection>;
	paginationToken?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<ActivitySearchSort>>>;
};

export type ActivitySearchResponse = {
	__typename?: 'ActivitySearchResponse';
	activities: Array<Maybe<ActivityAndSource>>;
	meta: Metadata;
	tokens: Array<Maybe<IdToken>>;
};

export type ActivitySearchSort = {
	direction?: InputMaybe<SortDirection>;
	field: ActivitySearchSortField;
};

export enum ActivitySearchSortField {
	ActivityDate = 'ActivityDate',
	ArtistName = 'ArtistName',
	ArtworkActivityCount = 'ArtworkActivityCount',
	ArtworkTitle = 'ArtworkTitle',
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	HighEstimate = 'HighEstimate',
	KnownPrice = 'KnownPrice',
	LotNumber = 'LotNumber',
	LowEstimate = 'LowEstimate',
	Price = 'Price',
	SaleAmount = 'SaleAmount',
	SaleNumber = 'SaleNumber',
}

export type ActivityTransfer = {
	__typename?: 'ActivityTransfer';
	activity: Scalars['String']['output'];
	from_artwork: Scalars['String']['output'];
	to_artwork: Scalars['String']['output'];
};

export type ActivityTransferResponse = {
	__typename?: 'ActivityTransferResponse';
	activitiesTransferred: Array<ActivityTransfer>;
};

export type ArtistMatch = {
	__typename?: 'ArtistMatch';
	artist: Scalars['String']['output'];
	datasource?: Maybe<Scalars['String']['output']>;
	matchedArtistId?: Maybe<Scalars['String']['output']>;
	matchedReferenceId?: Maybe<Scalars['Int']['output']>;
	yearBirth?: Maybe<Scalars['Int']['output']>;
};

export type ArtistSearchFilters = {
	isFavorite?: InputMaybe<Scalars['Boolean']['input']>;
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	nationality?: InputMaybe<Scalars['String']['input']>;
	yearOfBirth?: InputMaybe<IntValueFilter>;
	yearOfDeath?: InputMaybe<IntValueFilter>;
};

export type ArtistSearchInput = {
	filters?: InputMaybe<ArtistSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<ArtistSearchSort>>>;
};

export type ArtistSearchResponse = {
	__typename?: 'ArtistSearchResponse';
	data: Array<Maybe<Artist>>;
};

export type ArtistSearchSort = {
	direction: SortDirection;
	field: ArtistSearchSortField;
};

export enum ArtistSearchSortField {
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	Name = 'Name',
	Nationality = 'Nationality',
	NumberOfArtworks = 'NumberOfArtworks',
	YearOfBirth = 'YearOfBirth',
}

export type ArtworkActivitySearchFilters = {
	activityDate?: InputMaybe<DateValueFilter>;
	activityIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	activityStatus?: InputMaybe<
		Array<InputMaybe<Artwork_Activity_Status_Type_Enum>>
	>;
	activityType?: InputMaybe<Array<InputMaybe<Artwork_Activity_Type_Enum>>>;
	association?: InputMaybe<AssociationFilter>;
	auctionHouseNameOrId?: InputMaybe<Scalars['String']['input']>;
	auctionNameOrId?: InputMaybe<Scalars['String']['input']>;
	auctionType?: InputMaybe<Array<InputMaybe<Auction_Type_Enum>>>;
	excludeActivityIds?: InputMaybe<
		Array<InputMaybe<Scalars['String']['input']>>
	>;
	exhibitionDate?: InputMaybe<DateValueFilter>;
	exhibitionTitleOrId?: InputMaybe<Scalars['String']['input']>;
	exhibitionVenueAddress?: InputMaybe<LocationFilter>;
	fairDate?: InputMaybe<DateValueFilter>;
	fairExhibitorNameOrId?: InputMaybe<Scalars['String']['input']>;
	fairTitleOrId?: InputMaybe<Scalars['String']['input']>;
	galleryLocation?: InputMaybe<LocationFilter>;
	galleryNameOrId?: InputMaybe<Scalars['String']['input']>;
	knownPrice?: InputMaybe<FloatValueFilter>;
	listingType?: InputMaybe<Array<InputMaybe<Artwork_Listing_Type_Enum>>>;
	lotNumber?: InputMaybe<Scalars['String']['input']>;
	saleAmount?: InputMaybe<FloatValueFilter>;
	saleName?: InputMaybe<Scalars['String']['input']>;
	saleNumber?: InputMaybe<Scalars['String']['input']>;
};

export type ArtworkAndSource = {
	__typename?: 'ArtworkAndSource';
	artwork: Artwork;
	pipeline_source?: Maybe<Pipeline_Source>;
};

export type ArtworkSearchFilters = {
	artistIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	artistNameOrId?: InputMaybe<Scalars['String']['input']>;
	artistReferenceIds?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
	artworkCrid?: InputMaybe<Scalars['String']['input']>;
	artworkDepth?: InputMaybe<FloatValueFilter>;
	artworkHeight?: InputMaybe<FloatValueFilter>;
	artworkIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	artworkMedia?: InputMaybe<Scalars['String']['input']>;
	artworkTitleOrId?: InputMaybe<Scalars['String']['input']>;
	artworkType?: InputMaybe<Array<InputMaybe<Artwork_Type_Enum>>>;
	artworkWidth?: InputMaybe<FloatValueFilter>;
	categoryTags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	editionNumber?: InputMaybe<Scalars['String']['input']>;
	editionSizeTotal?: InputMaybe<IntValueFilter>;
	excludeArtworkIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	executedYear?: InputMaybe<IntValueFilter>;
	hasImage?: InputMaybe<Scalars['Boolean']['input']>;
	heniArtworkType?: InputMaybe<Array<InputMaybe<Heni_Artwork_Type_Enum>>>;
	isBundle?: InputMaybe<Scalars['Boolean']['input']>;
	isFullSet?: InputMaybe<Scalars['Boolean']['input']>;
	isHeniArtwork?: InputMaybe<Scalars['Boolean']['input']>;
	numberOfArtworks?: InputMaybe<IntValueFilter>;
	numberOfPieces?: InputMaybe<IntValueFilter>;
	seriesCrid?: InputMaybe<Scalars['String']['input']>;
	seriesSize?: InputMaybe<IntValueFilter>;
	seriesTitleOrId?: InputMaybe<Scalars['String']['input']>;
	showFavouriteArtists?: InputMaybe<Scalars['Boolean']['input']>;
	subjectTags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	unlimitedEdition?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ArtworkSearchResponse = {
	__typename?: 'ArtworkSearchResponse';
	artworks: Array<Maybe<ArtworkAndSource>>;
	meta: Metadata;
	tokens: Array<Maybe<IdToken>>;
};

export type AssociationFilter = {
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Artwork_Activity_Association_Type_Enum>;
};

export type AuctionSearchFilters = {
	auctionHouseOrId?: InputMaybe<Scalars['String']['input']>;
	auctionNameOrId?: InputMaybe<Scalars['String']['input']>;
	auctionType?: InputMaybe<Array<InputMaybe<Auction_Type_Enum>>>;
	endDate?: InputMaybe<DateValueFilter>;
	isUpcoming?: InputMaybe<Scalars['Boolean']['input']>;
	localEndDate?: InputMaybe<DateValueFilter>;
	localStartDate?: InputMaybe<DateValueFilter>;
	saleNumber?: InputMaybe<Scalars['String']['input']>;
	startDate?: InputMaybe<DateValueFilter>;
};

export type AuctionSearchInput = {
	filters?: InputMaybe<AuctionSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<AuctionSearchSort>>>;
};

export type AuctionSearchResponse = {
	__typename?: 'AuctionSearchResponse';
	data: Array<Auction>;
};

export type AuctionSearchSort = {
	direction: SortDirection;
	field: AuctionSearchSortField;
};

export enum AuctionSearchSortField {
	AuctionEndDate = 'AuctionEndDate',
	AuctionHouse = 'AuctionHouse',
	AuctionName = 'AuctionName',
	AuctionStartDate = 'AuctionStartDate',
	AuctionType = 'AuctionType',
	LocalAuctionEndDate = 'LocalAuctionEndDate',
	LocalAuctionStartDate = 'LocalAuctionStartDate',
	NumberOfLots = 'NumberOfLots',
	SaleNumber = 'SaleNumber',
}

export type CollectionNoteFilter = {
	artistNameOrId?: InputMaybe<Scalars['String']['input']>;
	artworkNameOrId?: InputMaybe<Scalars['String']['input']>;
	artworkSeriesTitleOrId?: InputMaybe<Scalars['String']['input']>;
};

export type CommonSearchFilters = {
	datasource?: InputMaybe<Scalars['String']['input']>;
	datasourceId?: InputMaybe<Scalars['String']['input']>;
	dateCreated?: InputMaybe<DateValueFilter>;
	dateUpdated?: InputMaybe<DateValueFilter>;
	ingestionSource?: InputMaybe<Scalars['String']['input']>;
	ingestionSourceId?: InputMaybe<Scalars['String']['input']>;
	userCreated?: InputMaybe<Scalars['String']['input']>;
	userUpdated?: InputMaybe<Scalars['String']['input']>;
};

export type DatasourceResponse = {
	__typename?: 'DatasourceResponse';
	data_source?: Maybe<Scalars['String']['output']>;
	data_source_id?: Maybe<Scalars['String']['output']>;
	id: Scalars['String']['output'];
	ingestion_source?: Maybe<Scalars['String']['output']>;
	ingestion_source_id?: Maybe<Scalars['String']['output']>;
	type: Pipeline_Source_Type_Enum;
};

export type DateValueFilter = {
	max?: InputMaybe<Scalars['DateOrYear']['input']>;
	min: Scalars['DateOrYear']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type EntitySearchFilters = {
	attributes?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Enum>>>;
	location?: InputMaybe<LocationFilter>;
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	owns?: InputMaybe<CollectionNoteFilter>;
	type?: InputMaybe<Entity_Type_Enum>;
	wants?: InputMaybe<CollectionNoteFilter>;
};

export type EntitySearchInput = {
	filters?: InputMaybe<EntitySearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<EntitySearchSort>>>;
};

export type EntitySearchResponse = {
	__typename?: 'EntitySearchResponse';
	data: Array<Maybe<Entity>>;
};

export type EntitySearchSort = {
	direction: SortDirection;
	field: EntitySearchSortField;
};

export enum EntitySearchSortField {
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	EntityType = 'EntityType',
	Name = 'Name',
	NumberOfActivities = 'NumberOfActivities',
}

export type ExhibitionSearchFilters = {
	artistNameOrId?: InputMaybe<Scalars['String']['input']>;
	endDate?: InputMaybe<DateValueFilter>;
	localEndDate?: InputMaybe<DateValueFilter>;
	localStartDate?: InputMaybe<DateValueFilter>;
	location?: InputMaybe<LocationFilter>;
	organiserNameOrId?: InputMaybe<Scalars['String']['input']>;
	startDate?: InputMaybe<DateValueFilter>;
	title?: InputMaybe<Scalars['String']['input']>;
};

export type ExhibitionSearchInput = {
	filters?: InputMaybe<ExhibitionSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<ExhibitionSearchSort>>>;
};

export type ExhibitionSearchResponse = {
	__typename?: 'ExhibitionSearchResponse';
	data: Array<Exhibition>;
};

export type ExhibitionSearchSort = {
	direction: SortDirection;
	field: ExhibitionSearchSortField;
};

export enum ExhibitionSearchSortField {
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	EndDate = 'EndDate',
	LocalEndDate = 'LocalEndDate',
	LocalStartDate = 'LocalStartDate',
	Location = 'Location',
	NumberOfListings = 'NumberOfListings',
	StartDate = 'StartDate',
	Title = 'Title',
}

export type Failed_Jobs = {
	__typename?: 'Failed_Jobs';
	data?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	reason?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type FairSearchFilters = {
	endDate?: InputMaybe<DateValueFilter>;
	exhibitorName?: InputMaybe<Scalars['String']['input']>;
	localEndDate?: InputMaybe<DateValueFilter>;
	localStartDate?: InputMaybe<DateValueFilter>;
	location?: InputMaybe<LocationFilter>;
	organisationName?: InputMaybe<Scalars['String']['input']>;
	startDate?: InputMaybe<DateValueFilter>;
	titleOrId?: InputMaybe<Scalars['String']['input']>;
};

export type FairSearchInput = {
	filters?: InputMaybe<FairSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<FairSearchSort>>>;
};

export type FairSearchResponse = {
	__typename?: 'FairSearchResponse';
	data: Array<Fair>;
};

export type FairSearchSort = {
	direction: SortDirection;
	field: FairSearchSortField;
};

export enum FairSearchSortField {
	City = 'City',
	Country = 'Country',
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	EndDate = 'EndDate',
	ExhibitorName = 'ExhibitorName',
	LocalEndDate = 'LocalEndDate',
	LocalStartDate = 'LocalStartDate',
	NumberOfExhibitors = 'NumberOfExhibitors',
	OrganisationName = 'OrganisationName',
	StartDate = 'StartDate',
	Title = 'Title',
}

export type FloatValueFilter = {
	max?: InputMaybe<Scalars['Float']['input']>;
	min: Scalars['Float']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type GallerySearchFilters = {
	attributes?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Enum>>>;
	location?: InputMaybe<LocationFilter>;
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	represents?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Array<InputMaybe<Organisation_Type_Enum>>>;
	yearDissolved?: InputMaybe<IntValueFilter>;
	yearFounded?: InputMaybe<IntValueFilter>;
};

export type GallerySearchInput = {
	filters?: InputMaybe<GallerySearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<GallerySearchSort>>>;
};

export type GallerySearchResponse = {
	__typename?: 'GallerySearchResponse';
	data: Array<Gallery>;
};

export type GallerySearchSort = {
	direction: SortDirection;
	field: GallerySearchSortField;
};

export enum GallerySearchSortField {
	City = 'City',
	Country = 'Country',
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	Name = 'Name',
	NumberOfActivities = 'NumberOfActivities',
	Type = 'Type',
	YearDissolved = 'YearDissolved',
	YearFounded = 'YearFounded',
}

export type IdToken = {
	__typename?: 'IdToken';
	id: Scalars['String']['output'];
	score: Scalars['Float']['output'];
	token: Scalars['String']['output'];
	version: Scalars['Int']['output'];
};

export type IntValueFilter = {
	max?: InputMaybe<Scalars['Int']['input']>;
	min: Scalars['Int']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type LegacyIdSearchResponse = {
	__typename?: 'LegacyIdSearchResponse';
	legacyId?: Maybe<Scalars['String']['output']>;
};

export type LocationFilter = {
	cityNameOrCode?: InputMaybe<Scalars['String']['input']>;
	countryNameOrCode?: InputMaybe<Scalars['String']['input']>;
};

export type LocationSearchFilter = {
	name?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Location_Type_Enum>;
};

export type LocationSearchInput = {
	filters?: InputMaybe<LocationSearchFilter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<LocationSearchSort>>>;
};

export type LocationSearchResponse = {
	__typename?: 'LocationSearchResponse';
	data: Array<Location>;
};

export type LocationSearchSort = {
	direction: SortDirection;
	field: LocationSearchSortField;
};

export enum LocationSearchSortField {
	Code = 'Code',
	Name = 'Name',
}

export type MatchArtistInput = {
	artist?: InputMaybe<Scalars['String']['input']>;
	datasource?: InputMaybe<Scalars['String']['input']>;
	yearBirth?: InputMaybe<Scalars['Int']['input']>;
};

export type Metadata = {
	__typename?: 'Metadata';
	duration: Scalars['Float']['output'];
	elasticSearchDuration: Scalars['Float']['output'];
	nextToken?: Maybe<Scalars['String']['output']>;
	previousToken?: Maybe<Scalars['String']['output']>;
	totalCount: Scalars['Int']['output'];
};

export type Mutation = {
	__typename?: 'Mutation';
	transferActivities: ActivityTransferResponse;
	updateAndReturnActivity: Array<Artwork_Activity>;
	updateAndReturnArtwork: Array<Artwork>;
};

export type MutationTransferActivitiesArgs = {
	fromArtworkId: Scalars['String']['input'];
	toArtworkId: Scalars['String']['input'];
};

export type MutationUpdateAndReturnActivityArgs = {
	activityIds: Array<InputMaybe<Scalars['String']['input']>>;
};

export type MutationUpdateAndReturnArtworkArgs = {
	artworks: Array<InputMaybe<Scalars['String']['input']>>;
};

export type NationalitySearchFilter = {
	name?: InputMaybe<Scalars['String']['input']>;
};

export type NationalitySearchInput = {
	filters?: InputMaybe<NationalitySearchFilter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<LocationSearchSort>>>;
};

export type OrganisationSearchFilters = {
	attributes?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Enum>>>;
	location?: InputMaybe<LocationFilter>;
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Array<InputMaybe<Organisation_Type_Enum>>>;
	yearDissolved?: InputMaybe<IntValueFilter>;
	yearFounded?: InputMaybe<IntValueFilter>;
};

export type OrganisationSearchInput = {
	filters?: InputMaybe<OrganisationSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<OrganisationSearchSort>>>;
};

export type OrganisationSearchResponse = {
	__typename?: 'OrganisationSearchResponse';
	data: Array<Organisation>;
};

export type OrganisationSearchSort = {
	direction: SortDirection;
	field: OrganisationSearchSortField;
};

export enum OrganisationSearchSortField {
	City = 'City',
	Country = 'Country',
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	Name = 'Name',
	NumberOfActivities = 'NumberOfActivities',
	Type = 'Type',
	YearDissolved = 'YearDissolved',
	YearFounded = 'YearFounded',
}

export enum PaginationDirection {
	Next = 'NEXT',
	Previous = 'PREVIOUS',
}

export type PersonSearchFilters = {
	attributes?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Enum>>>;
	location?: InputMaybe<LocationFilter>;
	nameOrId?: InputMaybe<Scalars['String']['input']>;
	nationality?: InputMaybe<Scalars['String']['input']>;
	owns?: InputMaybe<CollectionNoteFilter>;
	type?: InputMaybe<Array<InputMaybe<Person_Type_Enum>>>;
	wants?: InputMaybe<CollectionNoteFilter>;
	yearOfBirth?: InputMaybe<IntValueFilter>;
	yearOfDeath?: InputMaybe<IntValueFilter>;
};

export type PersonSearchInput = {
	filters?: InputMaybe<PersonSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<PersonSearchSort>>>;
};

export type PersonSearchResponse = {
	__typename?: 'PersonSearchResponse';
	data: Array<Maybe<Person>>;
};

export type PersonSearchSort = {
	direction: SortDirection;
	field: PersonSearchSortField;
};

export enum PersonSearchSortField {
	DateCreated = 'DateCreated',
	DateUpdated = 'DateUpdated',
	Name = 'Name',
	Nationality = 'Nationality',
	NumberOfActivities = 'NumberOfActivities',
	PersonType = 'PersonType',
	YearOfBirth = 'YearOfBirth',
}

export type Query = {
	__typename?: 'Query';
	activitySearch: ActivitySearchResponse;
	artistSearch: ArtistSearchResponse;
	artistSearchCount: Scalars['Int']['output'];
	artworkSearch: ArtworkSearchResponse;
	auctionSearch: AuctionSearchResponse;
	auctionSearchCount: Scalars['Int']['output'];
	entitySearch: EntitySearchResponse;
	entitySearchCount: Scalars['Int']['output'];
	exhibitionSearch: ExhibitionSearchResponse;
	exhibitionSearchCount: Scalars['Int']['output'];
	fairSearch: FairSearchResponse;
	fairSearchCount: Scalars['Int']['output'];
	gallerySearch: GallerySearchResponse;
	gallerySearchCount: Scalars['Int']['output'];
	getDatasource?: Maybe<DatasourceResponse>;
	getLegacyId?: Maybe<LegacyIdSearchResponse>;
	locationSearch: LocationSearchResponse;
	locationSearchCount: Scalars['Int']['output'];
	matchArtist?: Maybe<Array<Maybe<ArtistMatch>>>;
	nationalitySearch: LocationSearchResponse;
	nationalitySearchCount: Scalars['Int']['output'];
	organisationSearch: OrganisationSearchResponse;
	organisationSearchCount: Scalars['Int']['output'];
	personSearch: PersonSearchResponse;
	personSearchCount: Scalars['Int']['output'];
};

export type QueryActivitySearchArgs = {
	input: ActivitySearchInput;
};

export type QueryArtistSearchArgs = {
	input: ArtistSearchInput;
};

export type QueryArtistSearchCountArgs = {
	input: ArtistSearchInput;
};

export type QueryArtworkSearchArgs = {
	input: ActivitySearchInput;
};

export type QueryAuctionSearchArgs = {
	input: AuctionSearchInput;
};

export type QueryAuctionSearchCountArgs = {
	input: AuctionSearchInput;
};

export type QueryEntitySearchArgs = {
	input: EntitySearchInput;
};

export type QueryEntitySearchCountArgs = {
	input: EntitySearchInput;
};

export type QueryExhibitionSearchArgs = {
	input: ExhibitionSearchInput;
};

export type QueryExhibitionSearchCountArgs = {
	input: ExhibitionSearchInput;
};

export type QueryFairSearchArgs = {
	input: FairSearchInput;
};

export type QueryFairSearchCountArgs = {
	input: FairSearchInput;
};

export type QueryGallerySearchArgs = {
	input: GallerySearchInput;
};

export type QueryGallerySearchCountArgs = {
	input: GallerySearchInput;
};

export type QueryGetDatasourceArgs = {
	collection: Scalars['String']['input'];
	id: Scalars['String']['input'];
};

export type QueryGetLegacyIdArgs = {
	collection: Scalars['String']['input'];
	id: Scalars['String']['input'];
};

export type QueryLocationSearchArgs = {
	input: LocationSearchInput;
};

export type QueryLocationSearchCountArgs = {
	input: LocationSearchInput;
};

export type QueryMatchArtistArgs = {
	input: Array<InputMaybe<MatchArtistInput>>;
};

export type QueryNationalitySearchArgs = {
	input: NationalitySearchInput;
};

export type QueryNationalitySearchCountArgs = {
	input: NationalitySearchInput;
};

export type QueryOrganisationSearchArgs = {
	input: OrganisationSearchInput;
};

export type QueryOrganisationSearchCountArgs = {
	input: OrganisationSearchInput;
};

export type QueryPersonSearchArgs = {
	input: PersonSearchInput;
};

export type QueryPersonSearchCountArgs = {
	input: PersonSearchInput;
};

export enum SortDirection {
	Asc = 'ASC',
	Desc = 'DESC',
}

export type StringValueFilter = {
	max?: InputMaybe<Scalars['String']['input']>;
	min: Scalars['String']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export enum ValueFilterOperator {
	Between = 'Between',
	Equal = 'Equal',
	GreaterThan = 'GreaterThan',
	GreaterThanOrEqual = 'GreaterThanOrEqual',
	LessThan = 'LessThan',
	LessThanOrEqual = 'LessThanOrEqual',
}

export type Activity_Transfer = {
	__typename?: 'activity_transfer';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	from_artwork?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
	to_artwork?: Maybe<Artwork>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artist = {
	__typename?: 'artist';
	aggregations?: Maybe<Array<Maybe<Artist_Aggregation>>>;
	artwork_series?: Maybe<Array<Maybe<Artwork_Series_Artist>>>;
	artworks?: Maybe<Array<Maybe<Artwork_Artist>>>;
	awards?: Maybe<Array<Maybe<Artist_Award>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	exhibitions?: Maybe<Array<Maybe<Exhibition_Artist>>>;
	id?: Maybe<Scalars['String']['output']>;
	is_favourite?: Maybe<Array<Maybe<Favourite_Artist>>>;
	person?: Maybe<Person>;
	raw_info?: Maybe<Array<Maybe<Artist_Raw_Info>>>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_active_from?: Maybe<Scalars['Int']['output']>;
	year_active_to?: Maybe<Scalars['Int']['output']>;
};

export type Artist_Aggregation = {
	__typename?: 'artist_aggregation';
	artist?: Maybe<Artist>;
	artwork_count?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Artist_Auction_Record = {
	__typename?: 'artist_auction_record';
	amount?: Maybe<Currency_Amount>;
	artist?: Maybe<Artist>;
	artwork?: Maybe<Artwork>;
	auction_lot?: Maybe<Auction_Lot>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type Artist_Award = {
	__typename?: 'artist_award';
	artist?: Maybe<Artist>;
	award?: Maybe<Award>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	win_type?: Maybe<Win_Type>;
	year?: Maybe<Scalars['Int']['output']>;
};

export type Artist_Raw_Info = {
	__typename?: 'artist_raw_info';
	artist?: Maybe<Artist>;
	id?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artist_Raw_Info_Type>;
	value?: Maybe<Scalars['String']['output']>;
};

export type Artist_Raw_Info_Type = {
	__typename?: 'artist_raw_info_type';
	key?: Maybe<Artist_Raw_Info_Type_Enum>;
};

export enum Artist_Raw_Info_Type_Enum {
	Nationality = 'NATIONALITY',
	YearActiveEnd = 'YEAR_ACTIVE_END',
	YearActiveStart = 'YEAR_ACTIVE_START',
	YearBirth = 'YEAR_BIRTH',
	YearDeath = 'YEAR_DEATH',
}

export type Artwork = {
	__typename?: 'artwork';
	activities_transferred_from?: Maybe<Array<Maybe<Artwork_Activity_Transfer>>>;
	activities_transferred_to?: Maybe<Array<Maybe<Artwork_Activity_Transfer>>>;
	activity?: Maybe<Array<Maybe<Artwork_Activity_Artwork>>>;
	additional_info?: Maybe<Scalars['String']['output']>;
	aggregations?: Maybe<Array<Maybe<Artwork_Aggregation>>>;
	artists?: Maybe<Array<Maybe<Artwork_Artist>>>;
	artwork_type?: Maybe<Artwork_Type>;
	collaborator_organisations?: Maybe<Array<Maybe<Artwork_Organisation>>>;
	crid?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_type?: Maybe<Artwork_Dimension_Type>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	edition_info?: Maybe<Edition_Info>;
	execution_end_year?: Maybe<Scalars['Int']['output']>;
	execution_start_year?: Maybe<Scalars['Int']['output']>;
	heni_artwork_type?: Maybe<Heni_Artwork_Type>;
	id?: Maybe<Scalars['String']['output']>;
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	media?: Maybe<Scalars['String']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	number_of_pieces?: Maybe<Scalars['Int']['output']>;
	primary_image?: Maybe<Directus_Files>;
	raw_info?: Maybe<Array<Maybe<Artwork_Raw_Info>>>;
	series?: Maybe<Artwork_Series>;
	status?: Maybe<Status>;
	tags?: Maybe<Array<Maybe<Artwork_Tag>>>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity = {
	__typename?: 'artwork_activity';
	activity_artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	activity_status?: Maybe<Array<Maybe<Artwork_Activity_Status>>>;
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artworks?: Maybe<Array<Maybe<Artwork_Activity_Artwork>>>;
	associations?: Maybe<Array<Maybe<Artwork_Activity_Association>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	latest_activity_status?: Maybe<Artwork_Activity_Status>;
	notes?: Maybe<Scalars['String']['output']>;
	source_page_url?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	transfers?: Maybe<Array<Maybe<Artwork_Activity_Transfer>>>;
	type?: Maybe<Artwork_Activity_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Artwork = {
	__typename?: 'artwork_activity_artwork';
	artwork?: Maybe<Artwork>;
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	edition_number?: Maybe<Scalars['String']['output']>;
	edition_number_legacy?: Maybe<Scalars['String']['output']>;
	edition_number_type?: Maybe<Edition_Number_Type>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Artwork_Info = {
	__typename?: 'artwork_activity_artwork_info';
	additional_images?: Maybe<Array<Maybe<Artwork_Activity_Image>>>;
	artwork_activity?: Maybe<Array<Maybe<Artwork_Activity>>>;
	condition?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	exhibition?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	lead?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	primary_image?: Maybe<Directus_Files>;
	provenance?: Maybe<Scalars['String']['output']>;
	raw_artwork_description?: Maybe<Scalars['String']['output']>;
	raw_info?: Maybe<Array<Maybe<Artwork_Activity_Artwork_Raw_Info>>>;
	reference_files?: Maybe<Array<Maybe<Artwork_Activity_Artwork_Info_Files>>>;
	series_size?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Artwork_Info_Files = {
	__typename?: 'artwork_activity_artwork_info_files';
	artwork_activity_artwork_info_id?: Maybe<Artwork_Activity_Artwork_Info>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
};

export type Artwork_Activity_Artwork_Raw_Info = {
	__typename?: 'artwork_activity_artwork_raw_info';
	artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	id?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Raw_Info_Type>;
	value?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Activity_Association = {
	__typename?: 'artwork_activity_association';
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	read_only?: Maybe<Scalars['Boolean']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Artwork_Activity_Association_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Association_Type = {
	__typename?: 'artwork_activity_association_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Activity_Association_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
	supported_activity_types?: Maybe<
		Array<Maybe<Artwork_Activity_Association_Type_Artwork_Activity_Type>>
	>;
};

export type Artwork_Activity_Association_Type_Artwork_Activity_Type = {
	__typename?: 'artwork_activity_association_type_artwork_activity_type';
	artwork_activity_association_type_key?: Maybe<Artwork_Activity_Association_Type>;
	artwork_activity_type_key?: Maybe<Artwork_Activity_Type>;
	id?: Maybe<Scalars['Int']['output']>;
};

export enum Artwork_Activity_Association_Type_Enum {
	ArtistEstate = 'ARTIST_ESTATE',
	Bailee = 'BAILEE',
	Bailor = 'BAILOR',
	Bidder = 'BIDDER',
	BidderAgent = 'BIDDER_AGENT',
	BidderClient = 'BIDDER_CLIENT',
	Borrower = 'BORROWER',
	Broker = 'BROKER',
	Buyer = 'BUYER',
	BuyerAgent = 'BUYER_AGENT',
	Consignee = 'CONSIGNEE',
	Consignor = 'CONSIGNOR',
	Dealer = 'DEALER',
	Donee = 'DONEE',
	Donor = 'DONOR',
	Guarantor = 'GUARANTOR',
	GuessBuyer = 'GUESS_BUYER',
	GuessSeller = 'GUESS_SELLER',
	Lender = 'LENDER',
	Lessee = 'LESSEE',
	Lessor = 'LESSOR',
	Loanee = 'LOANEE',
	Loaner = 'LOANER',
	Owner = 'OWNER',
	PotentialBuyer = 'POTENTIAL_BUYER',
	Restitutee = 'RESTITUTEE',
	Restitutor = 'RESTITUTOR',
	Seller = 'SELLER',
	SellerAgent = 'SELLER_AGENT',
	Underbidder = 'UNDERBIDDER',
}

export type Artwork_Activity_Image = {
	__typename?: 'artwork_activity_image';
	artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image?: Maybe<Directus_Files>;
	status?: Maybe<Status>;
	type?: Maybe<Artwork_Activity_Image_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Image_Type = {
	__typename?: 'artwork_activity_image_type';
	key?: Maybe<Artwork_Activity_Image_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Activity_Image_Type_Enum {
	Artwork = 'ARTWORK',
	Label = 'LABEL',
	Other = 'OTHER',
}

export type Artwork_Activity_Status = {
	__typename?: 'artwork_activity_status';
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	type?: Maybe<Artwork_Activity_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Status_Type = {
	__typename?: 'artwork_activity_status_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Activity_Status_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	supported_activity_types?: Maybe<
		Array<Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type>>
	>;
};

export type Artwork_Activity_Status_Type_Artwork_Activity_Type = {
	__typename?: 'artwork_activity_status_type_artwork_activity_type';
	artwork_activity_status_type_key?: Maybe<Artwork_Activity_Status_Type>;
	artwork_activity_type_key?: Maybe<Artwork_Activity_Type>;
	id?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Activity_Status_Type_Enum {
	Activated = 'ACTIVATED',
	BoughtIn = 'BOUGHT_IN',
	Declined = 'DECLINED',
	ForSale = 'FOR_SALE',
	Gifted = 'GIFTED',
	Loaned = 'LOANED',
	NotForSale = 'NOT_FOR_SALE',
	NotReported = 'NOT_REPORTED',
	Observed = 'OBSERVED',
	Owned = 'OWNED',
	Permanent = 'PERMANENT',
	Reserved = 'RESERVED',
	Restituted = 'RESTITUTED',
	Sold = 'SOLD',
	Terminated = 'TERMINATED',
	Unlisted = 'UNLISTED',
	Upcoming = 'UPCOMING',
	Withdrawn = 'WITHDRAWN',
}

export type Artwork_Activity_Transfer = {
	__typename?: 'artwork_activity_transfer';
	activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	from_artwork?: Maybe<Artwork>;
	id?: Maybe<Scalars['Int']['output']>;
	to_artwork?: Maybe<Artwork>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Type = {
	__typename?: 'artwork_activity_type';
	artwork_activity_status_types?: Maybe<
		Array<Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type>>
	>;
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Activity_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Activity_Type_Enum {
	Auction = 'AUCTION',
	Bailment = 'BAILMENT',
	CatalogueRaisonne = 'CATALOGUE_RAISONNE',
	Collateralised = 'COLLATERALISED',
	Collection = 'COLLECTION',
	Consignment = 'CONSIGNMENT',
	Database = 'DATABASE',
	Gift = 'GIFT',
	LegacyEmail = 'LEGACY_EMAIL',
	LegacyEstimate = 'LEGACY_ESTIMATE',
	LegacyLiterature = 'LEGACY_LITERATURE',
	LegacyLotDetails = 'LEGACY_LOT_DETAILS',
	LegacyOther = 'LEGACY_OTHER',
	LegacyUnsold = 'LEGACY_UNSOLD',
	Ownership = 'OWNERSHIP',
	PrivateSale = 'PRIVATE_SALE',
	Restitution = 'RESTITUTION',
	SocialMedia = 'SOCIAL_MEDIA',
	Wanted = 'WANTED',
}

export type Artwork_Activity_View = {
	__typename?: 'artwork_activity_view';
	activity_artwork_info?: Maybe<Scalars['String']['output']>;
	activity_status?: Maybe<Scalars['String']['output']>;
	artwork_listing?: Maybe<Scalars['String']['output']>;
	associations?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['String']['output']>;
	date_updated?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	source_page_url?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	timestamp?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Aggregation = {
	__typename?: 'artwork_aggregation';
	activity_count?: Maybe<Scalars['Int']['output']>;
	artwork?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Artist = {
	__typename?: 'artwork_artist';
	artist_id?: Maybe<Artist>;
	artwork_id?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Dimension_Type = {
	__typename?: 'artwork_dimension_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Dimension_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Dimension_Type_Enum {
	LargestArtwork = 'LARGEST_ARTWORK',
	OverallSize = 'OVERALL_SIZE',
	PerArtwork = 'PER_ARTWORK',
}

export type Artwork_Listing = {
	__typename?: 'artwork_listing';
	artwork_activity?: Maybe<Artwork_Activity>;
	auction_lot?: Maybe<Auction_Lot>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	exhibition_listing?: Maybe<Exhibition_Artwork_Listing>;
	fair_listing?: Maybe<Fair_Artwork_Listing>;
	gallery_listing?: Maybe<Gallery_Artwork_Listing>;
	id?: Maybe<Scalars['String']['output']>;
	known_price?: Maybe<Currency_Amount>;
	listing_type?: Maybe<Artwork_Listing_Type>;
	price_high_estimate?: Maybe<Currency_Amount>;
	price_low_estimate?: Maybe<Currency_Amount>;
	sale_amount?: Maybe<Currency_Amount>;
	shipping?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Listing_Super_Type = {
	__typename?: 'artwork_listing_super_type';
	key?: Maybe<Artwork_Listing_Super_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Listing_Super_Type_Enum {
	Auction = 'AUCTION',
	Private = 'PRIVATE',
}

export type Artwork_Listing_Type = {
	__typename?: 'artwork_listing_type';
	key?: Maybe<Artwork_Listing_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
	super_type?: Maybe<Artwork_Listing_Super_Type>;
};

export enum Artwork_Listing_Type_Enum {
	Auction = 'AUCTION',
	Exhibition = 'EXHIBITION',
	Fair = 'FAIR',
	Gallery = 'GALLERY',
	Private = 'PRIVATE',
}

export type Artwork_Organisation = {
	__typename?: 'artwork_organisation';
	artwork_id?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
	organisation_id?: Maybe<Organisation>;
};

export type Artwork_Raw_Info = {
	__typename?: 'artwork_raw_info';
	artwork?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Raw_Info_Type>;
	value?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Raw_Info_Type = {
	__typename?: 'artwork_raw_info_type';
	key?: Maybe<Artwork_Raw_Info_Type_Enum>;
};

export enum Artwork_Raw_Info_Type_Enum {
	Artist = 'ARTIST',
	Dimensions = 'DIMENSIONS',
	Edition = 'EDITION',
	Executed = 'EXECUTED',
	Medium = 'MEDIUM',
}

export type Artwork_Series = {
	__typename?: 'artwork_series';
	artists?: Maybe<Array<Maybe<Artwork_Series_Artist>>>;
	artworks?: Maybe<Array<Maybe<Artwork>>>;
	crid?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	execution_end_year?: Maybe<Scalars['Int']['output']>;
	execution_start_year?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image?: Maybe<Directus_Files>;
	is_heni_series?: Maybe<Scalars['Boolean']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	parent_series?: Maybe<Artwork_Series>;
	status?: Maybe<Status>;
	sub_series?: Maybe<Array<Maybe<Artwork_Series>>>;
	tags?: Maybe<Array<Maybe<Artwork_Series_Tag>>>;
	tags_last_updated?: Maybe<Scalars['DateTime']['output']>;
	tags_last_updated_by?: Maybe<Directus_Users>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Series_Artist = {
	__typename?: 'artwork_series_artist';
	artist_id?: Maybe<Artist>;
	artwork_series_id?: Maybe<Artwork_Series>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Series_Tag = {
	__typename?: 'artwork_series_tag';
	artwork_series_id?: Maybe<Artwork_Series>;
	id?: Maybe<Scalars['String']['output']>;
	tag_tag?: Maybe<Tag>;
};

export type Artwork_Tag = {
	__typename?: 'artwork_tag';
	artwork_id?: Maybe<Artwork>;
	id?: Maybe<Scalars['String']['output']>;
	tag_tag?: Maybe<Tag>;
};

export type Artwork_Type = {
	__typename?: 'artwork_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Type_Enum {
	Collage = 'COLLAGE',
	DigitalArtNft = 'DIGITAL_ART_NFT',
	Drawing = 'DRAWING',
	Other = 'OTHER',
	Painting = 'PAINTING',
	Photography = 'PHOTOGRAPHY',
	Primary = 'PRIMARY',
	Print = 'PRINT',
	Sculpture = 'SCULPTURE',
	Unknown = 'UNKNOWN',
}

export type Artwork_View = {
	__typename?: 'artwork_view';
	additional_info?: Maybe<Scalars['String']['output']>;
	artists?: Maybe<Scalars['String']['output']>;
	artwork_type?: Maybe<Scalars['String']['output']>;
	collaborator_organisations?: Maybe<Scalars['String']['output']>;
	crid?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['String']['output']>;
	date_updated?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_type?: Maybe<Scalars['String']['output']>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	edition_info?: Maybe<Scalars['String']['output']>;
	execution_end_year?: Maybe<Scalars['Int']['output']>;
	execution_start_year?: Maybe<Scalars['Int']['output']>;
	heni_artwork_type?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	media?: Maybe<Scalars['String']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	number_of_pieces?: Maybe<Scalars['Int']['output']>;
	primary_image?: Maybe<Scalars['String']['output']>;
	series?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	tags?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Auction = {
	__typename?: 'auction';
	aggregations?: Maybe<Array<Maybe<Auction_Aggregation>>>;
	auction_end_date?: Maybe<Scalars['DateTime']['output']>;
	auction_groups?: Maybe<Array<Maybe<Auction_Group_Auction>>>;
	auction_house?: Maybe<Auction_House>;
	auction_start_date?: Maybe<Scalars['DateTime']['output']>;
	auction_timezone?: Maybe<Timezone>;
	auction_types?: Maybe<Array<Maybe<Auction_Auction_Type>>>;
	clients?: Maybe<Array<Maybe<Auction_Client>>>;
	cover_page_image?: Maybe<Directus_Files>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	local_auction_end_date?: Maybe<Scalars['DateTime']['output']>;
	local_auction_start_date?: Maybe<Scalars['DateTime']['output']>;
	lots?: Maybe<Array<Maybe<Auction_Lot>>>;
	sale_name?: Maybe<Scalars['String']['output']>;
	sale_number?: Maybe<Scalars['String']['output']>;
	sale_url?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Aggregation = {
	__typename?: 'auction_aggregation';
	auction?: Maybe<Auction>;
	id?: Maybe<Scalars['String']['output']>;
	lot_count?: Maybe<Scalars['Int']['output']>;
};

export type Auction_Auction_Type = {
	__typename?: 'auction_auction_type';
	auction_id?: Maybe<Auction>;
	auction_type_key?: Maybe<Auction_Type>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Auction_Bid = {
	__typename?: 'auction_bid';
	amount?: Maybe<Currency_Amount>;
	auction_lot?: Maybe<Auction_Lot>;
	bidder?: Maybe<Auction_Lot_Bidder>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Bidder_Type = {
	__typename?: 'auction_bidder_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Auction_Bidder_Type_Enum>;
	label?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Auction_Bidder_Type_Enum {
	Advance = 'ADVANCE',
	Chandelier = 'CHANDELIER',
	Commission = 'COMMISSION',
	Online = 'ONLINE',
	Phone = 'PHONE',
	Room = 'ROOM',
}

export type Auction_Client = {
	__typename?: 'auction_client';
	auction?: Maybe<Auction>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	paddle_number?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Group = {
	__typename?: 'auction_group';
	aggregations?: Maybe<Array<Maybe<Auction_Group_Aggregation>>>;
	auctions?: Maybe<Array<Maybe<Auction_Group_Auction>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Group_Aggregation = {
	__typename?: 'auction_group_aggregation';
	auction_count?: Maybe<Scalars['Int']['output']>;
	auction_group?: Maybe<Auction_Group>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Auction_Group_Auction = {
	__typename?: 'auction_group_auction';
	auction_group_id?: Maybe<Auction_Group>;
	auction_id?: Maybe<Auction>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Directus_Users>;
};

export type Auction_House = {
	__typename?: 'auction_house';
	auctions?: Maybe<Array<Maybe<Auction>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Array<Maybe<Entity>>>;
	id?: Maybe<Scalars['String']['output']>;
	organisation?: Maybe<Organisation>;
	premiums?: Maybe<Array<Maybe<Auction_House_Premium>>>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_House_Buyers_Premium = {
	__typename?: 'auction_house_buyers_premium';
	auction_house_premium?: Maybe<Auction_House_Premium>;
	band_high_amount?: Maybe<Currency_Amount>;
	band_low_amount?: Maybe<Currency_Amount>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	premium_rate?: Maybe<Scalars['Float']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_House_Premium = {
	__typename?: 'auction_house_premium';
	auction_house?: Maybe<Auction_House>;
	buyers_premiums?: Maybe<Array<Maybe<Auction_House_Buyers_Premium>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	overhead_premium_rate?: Maybe<Scalars['Float']['output']>;
	sellers_premium_rate?: Maybe<Scalars['Float']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year?: Maybe<Scalars['Int']['output']>;
};

export type Auction_Lot = {
	__typename?: 'auction_lot';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	attributes?: Maybe<Array<Maybe<Auction_Lot_Attribute>>>;
	auction?: Maybe<Auction>;
	auctioneer?: Maybe<Person>;
	bidders?: Maybe<Array<Maybe<Auction_Lot_Bidder>>>;
	bids?: Maybe<Array<Maybe<Auction_Bid>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	hammer_timestamp?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	lot_notes?: Maybe<Scalars['String']['output']>;
	lot_number?: Maybe<Scalars['String']['output']>;
	sale_amount_includes_premium?: Maybe<Scalars['Boolean']['output']>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	starting_bid_amount?: Maybe<Scalars['Float']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	video_file_name?: Maybe<Scalars['String']['output']>;
	video_hammer_time_seconds?: Maybe<Scalars['String']['output']>;
	winning_bid?: Maybe<Auction_Bid>;
};

export type Auction_Lot_Attribute = {
	__typename?: 'auction_lot_attribute';
	amount?: Maybe<Currency_Amount>;
	auction_lot?: Maybe<Auction_Lot>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Auction_Lot_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Lot_Attribute_Type = {
	__typename?: 'auction_lot_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Auction_Lot_Attribute_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Auction_Lot_Attribute_Type_Enum {
	CatalogueHighlight = 'CATALOGUE_HIGHLIGHT',
	Guaranteed = 'GUARANTEED',
	IrrevocableBid = 'IRREVOCABLE_BID',
	NoReserve = 'NO_RESERVE',
	OnCover = 'ON_COVER',
	OwnershipInterest = 'OWNERSHIP_INTEREST',
}

export type Auction_Lot_Bidder = {
	__typename?: 'auction_lot_bidder';
	bidder?: Maybe<Entity>;
	bidder_type?: Maybe<Auction_Bidder_Type>;
	bids?: Maybe<Array<Maybe<Auction_Bid>>>;
	client?: Maybe<Auction_Client>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	location_number?: Maybe<Scalars['Int']['output']>;
	lot?: Maybe<Auction_Lot>;
	notes?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Type = {
	__typename?: 'auction_type';
	key?: Maybe<Auction_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Auction_Type_Enum {
	Bin = 'BIN',
	Charity = 'CHARITY',
	Live = 'LIVE',
	Online = 'ONLINE',
	Sealed = 'SEALED',
}

export type Author = {
	__typename?: 'author';
	artwork_id?: Maybe<Artwork>;
	entity_id?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	is_duplicate?: Maybe<Scalars['Boolean']['output']>;
};

export type Award = {
	__typename?: 'award';
	artists?: Maybe<Array<Maybe<Artist_Award>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	institution?: Maybe<Organisation>;
	name?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_award_created?: Maybe<Scalars['Int']['output']>;
};

export type Collection_Note = {
	__typename?: 'collection_note';
	artist?: Maybe<Artist>;
	artwork_series?: Maybe<Artwork_Series>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	type?: Maybe<Collection_Note_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Collection_Note_Type = {
	__typename?: 'collection_note_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Collection_Note_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Collection_Note_Type_Enum {
	Collection = 'COLLECTION',
	Wanted = 'WANTED',
}

export type Currency = {
	__typename?: 'currency';
	code?: Maybe<Scalars['String']['output']>;
	full_name?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	symbol?: Maybe<Scalars['String']['output']>;
};

export type Currency_Amount = {
	__typename?: 'currency_amount';
	amount?: Maybe<Scalars['Float']['output']>;
	conversion_timestamp?: Maybe<Scalars['DateTime']['output']>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	usd_amount?: Maybe<Scalars['Float']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Currency_Rate = {
	__typename?: 'currency_rate';
	currency?: Maybe<Currency>;
	id?: Maybe<Scalars['String']['output']>;
	rate_usd?: Maybe<Scalars['Float']['output']>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type Data_Source = {
	__typename?: 'data_source';
	name?: Maybe<Scalars['String']['output']>;
};

export type Directus_Files = {
	__typename?: 'directus_files';
	description?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	filename_download?: Maybe<Scalars['String']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	storage?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Folders = {
	__typename?: 'directus_folders';
	id: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	parent?: Maybe<Directus_Folders>;
};

export type Directus_Users = {
	__typename?: 'directus_users';
	email?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	last_name?: Maybe<Scalars['String']['output']>;
	role?: Maybe<Scalars['String']['output']>;
};

export type Edition_Info = {
	__typename?: 'edition_info';
	artists_proof_size?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	edition_size_total?: Maybe<Scalars['Int']['output']>;
	general_proof_size?: Maybe<Scalars['Int']['output']>;
	hors_de_commerce_size?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	is_numbered?: Maybe<Scalars['Boolean']['output']>;
	is_unknown?: Maybe<Scalars['Boolean']['output']>;
	is_unlimited?: Maybe<Scalars['Boolean']['output']>;
	regular_edition_size?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Edition_Number_Type = {
	__typename?: 'edition_number_type';
	key?: Maybe<Edition_Number_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Edition_Number_Type_Enum {
	ArtistsProof = 'ARTISTS_PROOF',
	GeneralProof = 'GENERAL_PROOF',
	HorsDeCommerce = 'HORS_DE_COMMERCE',
	Regular = 'REGULAR',
	Unknown = 'UNKNOWN',
}

export type Entity = {
	__typename?: 'entity';
	activities?: Maybe<Array<Maybe<Artwork_Activity_Association>>>;
	additional_images?: Maybe<Array<Maybe<Entity_Images>>>;
	addresses?: Maybe<Array<Maybe<Entity_Address>>>;
	aggregations?: Maybe<Array<Maybe<Entity_Aggregation>>>;
	artist?: Maybe<Artist>;
	attributes?: Maybe<Array<Maybe<Entity_Attribute>>>;
	auction_house?: Maybe<Auction_House>;
	collection_notes?: Maybe<Array<Maybe<Collection_Note>>>;
	contact_details?: Maybe<Array<Maybe<Entity_Contact_Detail>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	fair_organisation?: Maybe<Fair_Organisation>;
	gallery?: Maybe<Gallery>;
	id?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	name_clean?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Array<Maybe<Entity_Note>>>;
	organisation?: Maybe<Organisation>;
	person?: Maybe<Person>;
	profile_image?: Maybe<Directus_Files>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Entity_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_Address = {
	__typename?: 'entity_address';
	city?: Maybe<Location>;
	country?: Maybe<Location>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	line_1?: Maybe<Scalars['String']['output']>;
	line_2?: Maybe<Scalars['String']['output']>;
	line_3?: Maybe<Scalars['String']['output']>;
	post_code?: Maybe<Scalars['String']['output']>;
	region?: Maybe<Location>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_Aggregation = {
	__typename?: 'entity_aggregation';
	activity_count?: Maybe<Scalars['Int']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Entity_Attribute = {
	__typename?: 'entity_attribute';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Entity_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_Attribute_Type = {
	__typename?: 'entity_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Entity_Attribute_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Entity_Attribute_Type_Enum {
	Super = 'SUPER',
	Top = 'TOP',
}

export type Entity_Contact_Detail = {
	__typename?: 'entity_contact_detail';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Entity_Contact_Detail_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	value?: Maybe<Scalars['String']['output']>;
};

export type Entity_Contact_Detail_Type = {
	__typename?: 'entity_contact_detail_type';
	key?: Maybe<Entity_Contact_Detail_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Entity_Contact_Detail_Type_Enum {
	Email = 'EMAIL',
	Instagram = 'INSTAGRAM',
	Phone = 'PHONE',
	Twitter = 'TWITTER',
	Website = 'WEBSITE',
}

export type Entity_Images = {
	__typename?: 'entity_images';
	directus_files_id?: Maybe<Directus_Files>;
	entity_id?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Entity_Note = {
	__typename?: 'entity_note';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	type?: Maybe<Entity_Note_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_Note_Type = {
	__typename?: 'entity_note_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Entity_Note_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Entity_Note_Type_Enum {
	Education = 'EDUCATION',
	General = 'GENERAL',
	Secure = 'SECURE',
}

export type Entity_Type = {
	__typename?: 'entity_type';
	key?: Maybe<Entity_Type_Enum>;
};

export enum Entity_Type_Enum {
	Organisation = 'organisation',
	Person = 'person',
}

export type Exhibition = {
	__typename?: 'exhibition';
	additional_info?: Maybe<Scalars['String']['output']>;
	aggregations?: Maybe<Array<Maybe<Exhibition_Aggregation>>>;
	artists?: Maybe<Array<Maybe<Exhibition_Artist>>>;
	artwork_listings?: Maybe<Array<Maybe<Exhibition_Artwork_Listing>>>;
	attributes?: Maybe<Array<Maybe<Exhibition_Attribute>>>;
	cover_image?: Maybe<Directus_Files>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	exhibition_url?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	local_end_date?: Maybe<Scalars['DateTime']['output']>;
	local_start_date?: Maybe<Scalars['DateTime']['output']>;
	organisers?: Maybe<Array<Maybe<Exhibition_Organisers>>>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	status?: Maybe<Status>;
	timezone?: Maybe<Timezone>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	venue_address_1?: Maybe<Scalars['String']['output']>;
	venue_address_2?: Maybe<Scalars['String']['output']>;
	venue_address_3?: Maybe<Scalars['String']['output']>;
	venue_city?: Maybe<Location>;
	venue_country?: Maybe<Location>;
	venue_post_code?: Maybe<Scalars['String']['output']>;
};

export type Exhibition_Aggregation = {
	__typename?: 'exhibition_aggregation';
	artwork_listing_count?: Maybe<Scalars['Int']['output']>;
	exhibition?: Maybe<Exhibition>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Exhibition_Artist = {
	__typename?: 'exhibition_artist';
	artist_id?: Maybe<Artist>;
	exhibition_id?: Maybe<Exhibition>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Exhibition_Artwork_Listing = {
	__typename?: 'exhibition_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	exhibition?: Maybe<Exhibition>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Exhibition_Attribute = {
	__typename?: 'exhibition_attribute';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	exhibition?: Maybe<Exhibition>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Exhibition_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Exhibition_Attribute_Type = {
	__typename?: 'exhibition_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Exhibition_Attribute_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Exhibition_Attribute_Type_Enum {
	Commercial = 'COMMERCIAL',
	NonCommercial = 'NON_COMMERCIAL',
	Online = 'ONLINE',
	Physical = 'PHYSICAL',
}

export type Exhibition_Organisers = {
	__typename?: 'exhibition_organisers';
	entity_id?: Maybe<Entity>;
	exhibition_id?: Maybe<Exhibition>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Fair = {
	__typename?: 'fair';
	aggregations?: Maybe<Array<Maybe<Fair_Aggregation>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	exhibitors?: Maybe<Array<Maybe<Fair_Exhibitor>>>;
	fair_organisation?: Maybe<Fair_Organisation>;
	fair_url?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	local_end_date?: Maybe<Scalars['DateTime']['output']>;
	local_start_date?: Maybe<Scalars['DateTime']['output']>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	status?: Maybe<Status>;
	timezone?: Maybe<Timezone>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	venue_address_1?: Maybe<Scalars['String']['output']>;
	venue_address_2?: Maybe<Scalars['String']['output']>;
	venue_address_3?: Maybe<Scalars['String']['output']>;
	venue_city?: Maybe<Location>;
	venue_country?: Maybe<Location>;
	venue_post_code?: Maybe<Scalars['String']['output']>;
};

export type Fair_Aggregation = {
	__typename?: 'fair_aggregation';
	exhibitor_count?: Maybe<Scalars['Int']['output']>;
	fair?: Maybe<Fair>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Fair_Artwork_Listing = {
	__typename?: 'fair_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	fair_exhibitor?: Maybe<Fair_Exhibitor>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Fair_Exhibitor = {
	__typename?: 'fair_exhibitor';
	artwork_listings?: Maybe<Array<Maybe<Fair_Artwork_Listing>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	fair?: Maybe<Fair>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Fair_Organisation = {
	__typename?: 'fair_organisation';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Array<Maybe<Entity>>>;
	fairs?: Maybe<Array<Maybe<Fair>>>;
	id?: Maybe<Scalars['String']['output']>;
	organisation?: Maybe<Organisation>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Favourite_Artist = {
	__typename?: 'favourite_artist';
	artist?: Maybe<Artist>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Feature_Flag = {
	__typename?: 'feature_flag';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	enabled?: Maybe<Scalars['Boolean']['output']>;
	key?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gallery = {
	__typename?: 'gallery';
	artist_representation?: Maybe<Array<Maybe<Gallery_Representation>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	gallery_artwork_listings?: Maybe<Array<Maybe<Gallery_Artwork_Listing>>>;
	id?: Maybe<Scalars['String']['output']>;
	organisation?: Maybe<Organisation>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gallery_Artwork_Listing = {
	__typename?: 'gallery_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	gallery?: Maybe<Gallery>;
	id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gallery_Representation = {
	__typename?: 'gallery_representation';
	additional_info?: Maybe<Scalars['String']['output']>;
	artist?: Maybe<Artist>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	email_date?: Maybe<Scalars['DateTime']['output']>;
	email_subject?: Maybe<Scalars['String']['output']>;
	gallery?: Maybe<Gallery>;
	id?: Maybe<Scalars['Int']['output']>;
	image?: Maybe<Directus_Files>;
	news_url?: Maybe<Scalars['String']['output']>;
	receiver?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gender = {
	__typename?: 'gender';
	key?: Maybe<Gender_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Gender_Enum {
	F = 'F',
	M = 'M',
}

export type Heni_Artwork_Type = {
	__typename?: 'heni_artwork_type';
	key?: Maybe<Heni_Artwork_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Heni_Artwork_Type_Enum {
	Edition = 'EDITION',
	Nft = 'NFT',
	Primary = 'PRIMARY',
}

export type Ingestion_Progress = {
	__typename?: 'ingestion_progress';
	completed_at?: Maybe<Scalars['DateTime']['output']>;
	error_message?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	index_name?: Maybe<Scalars['String']['output']>;
	index_version?: Maybe<Scalars['String']['output']>;
	last_processed_id?: Maybe<Scalars['String']['output']>;
	started_at?: Maybe<Scalars['DateTime']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	table_name?: Maybe<Scalars['String']['output']>;
	total_processed?: Maybe<Scalars['Int']['output']>;
	updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type Location = {
	__typename?: 'location';
	code?: Maybe<Scalars['String']['output']>;
	country?: Maybe<Location>;
	country_nationality?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	parent?: Maybe<Location>;
	short_code?: Maybe<Scalars['String']['output']>;
	subdivisions?: Maybe<Array<Maybe<Location>>>;
	timezone?: Maybe<Timezone>;
	type?: Maybe<Location_Type>;
};

export type Location_Type = {
	__typename?: 'location_type';
	key?: Maybe<Location_Type_Enum>;
};

export enum Location_Type_Enum {
	City = 'CITY',
	Country = 'COUNTRY',
	Region = 'REGION',
}

export type Organisation = {
	__typename?: 'organisation';
	artworks?: Maybe<Array<Maybe<Artwork_Organisation>>>;
	children?: Maybe<Array<Maybe<Organisation>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	location?: Maybe<Location>;
	name?: Maybe<Scalars['String']['output']>;
	parent?: Maybe<Organisation>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Array<Maybe<Organisation_Organisation_Type>>>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_dissolved?: Maybe<Scalars['Int']['output']>;
	year_founded?: Maybe<Scalars['Int']['output']>;
};

export type Organisation_Organisation_Type = {
	__typename?: 'organisation_organisation_type';
	id?: Maybe<Scalars['String']['output']>;
	organisation_id?: Maybe<Organisation>;
	organisation_type_key?: Maybe<Organisation_Type>;
};

export type Organisation_Type = {
	__typename?: 'organisation_type';
	key?: Maybe<Organisation_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Organisation_Type_Enum {
	AuctionHouse = 'AUCTION_HOUSE',
	Collection = 'COLLECTION',
	Company = 'COMPANY',
	EducationalInstitution = 'EDUCATIONAL_INSTITUTION',
	Estate = 'ESTATE',
	Fair = 'FAIR',
	FinanceCompany = 'FINANCE_COMPANY',
	Foundation = 'FOUNDATION',
	Fund = 'FUND',
	Gallery = 'GALLERY',
	Institution = 'INSTITUTION',
	Library = 'LIBRARY',
	Museum = 'MUSEUM',
	Publisher = 'PUBLISHER',
	Trust = 'TRUST',
	University = 'UNIVERSITY',
}

export type Person = {
	__typename?: 'person';
	biography?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	entity?: Maybe<Entity>;
	first_name?: Maybe<Scalars['String']['output']>;
	gender?: Maybe<Gender>;
	id?: Maybe<Scalars['String']['output']>;
	industry?: Maybe<Scalars['String']['output']>;
	job_title?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	middle_name?: Maybe<Scalars['String']['output']>;
	nationalities?: Maybe<Array<Maybe<Person_Nationality>>>;
	net_worth_usd?: Maybe<Scalars['Float']['output']>;
	preferred_name?: Maybe<Scalars['String']['output']>;
	reference_id?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Array<Maybe<Person_Person_Type>>>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type Person_Nationality = {
	__typename?: 'person_nationality';
	country?: Maybe<Location>;
	id?: Maybe<Scalars['String']['output']>;
	person?: Maybe<Person>;
};

export type Person_Person_Type = {
	__typename?: 'person_person_type';
	id?: Maybe<Scalars['String']['output']>;
	person_id?: Maybe<Person>;
	person_type_key?: Maybe<Person_Type>;
};

export type Person_Type = {
	__typename?: 'person_type';
	key?: Maybe<Person_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Person_Type_Enum {
	Artist = 'ARTIST',
	ArtAdvisor = 'ART_ADVISOR',
	AuctionHouseSpecialist = 'AUCTION_HOUSE_SPECIALIST',
	Collector = 'COLLECTOR',
	Curator = 'CURATOR',
	Dealer = 'DEALER',
	Director = 'DIRECTOR',
}

export type Pipeline_Info = {
	__typename?: 'pipeline_info';
	description?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	item?: Maybe<Array<Maybe<Pipeline_Info_Item>>>;
	meta?: Maybe<Scalars['String']['output']>;
	step?: Maybe<Pipeline_Step>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	type?: Maybe<Pipeline_Info_Type>;
	unprocessed_source_data?: Maybe<Array<Maybe<Pipeline_Info_Unprocessed>>>;
};

export type Pipeline_Info_Item = {
	__typename?: 'pipeline_info_item';
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	item?: Maybe<Scalars['String']['output']>;
	pipeline_info_id?: Maybe<Pipeline_Info>;
};

export type Pipeline_Info_Type = {
	__typename?: 'pipeline_info_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Pipeline_Info_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Pipeline_Info_Type_Enum {
	DroppedRecord = 'DROPPED_RECORD',
	Duplicate = 'DUPLICATE',
	Error = 'ERROR',
	LowConfidenceMatch = 'LOW_CONFIDENCE_MATCH',
	NewArtist = 'NEW_ARTIST',
}

export type Pipeline_Info_Unprocessed = {
	__typename?: 'pipeline_info_unprocessed';
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	item?: Maybe<Scalars['String']['output']>;
	pipeline_info_id?: Maybe<Pipeline_Info>;
};

export type Pipeline_Source = {
	__typename?: 'pipeline_source';
	data_source?: Maybe<Scalars['String']['output']>;
	data_source_id?: Maybe<Scalars['String']['output']>;
	id: Scalars['String']['output'];
	ingestion_source?: Maybe<Scalars['String']['output']>;
	ingestion_source_id?: Maybe<Scalars['String']['output']>;
	last_updated?: Maybe<Scalars['DateTime']['output']>;
	processed_item?: Maybe<Array<Maybe<Pipeline_Source_Processed>>>;
	type?: Maybe<Pipeline_Source_Type>;
	unprocessed_source_data?: Maybe<Array<Maybe<Pipeline_Source_Unprocessed>>>;
};

export type Pipeline_Source_Processed = {
	__typename?: 'pipeline_source_processed';
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	item?: Maybe<Scalars['String']['output']>;
	pipeline_source_id?: Maybe<Pipeline_Source>;
};

export type Pipeline_Source_Type = {
	__typename?: 'pipeline_source_type';
	key?: Maybe<Pipeline_Source_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Pipeline_Source_Type_Enum {
	Arteye = 'ARTEYE',
	Ingestion = 'INGESTION',
}

export type Pipeline_Source_Unprocessed = {
	__typename?: 'pipeline_source_unprocessed';
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	item?: Maybe<Scalars['String']['output']>;
	pipeline_source_id?: Maybe<Pipeline_Source>;
};

export type Pipeline_Step = {
	__typename?: 'pipeline_step';
	key?: Maybe<Pipeline_Step_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Pipeline_Step_Enum {
	Deduplication = 'DEDUPLICATION',
	PostPreprocessingDq = 'POST_PREPROCESSING_DQ',
	PostTransformDq = 'POST_TRANSFORM_DQ',
	Preprocessing = 'PREPROCESSING',
	Transform = 'TRANSFORM',
}

export type Relationship = {
	__typename?: 'relationship';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	from_entity?: Maybe<Entity>;
	id?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	status?: Maybe<Status>;
	to_entity?: Maybe<Entity>;
	type?: Maybe<Relationship_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Relationship_Entity_Type = {
	__typename?: 'relationship_entity_type';
	key?: Maybe<Relationship_Entity_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Relationship_Entity_Type_Enum {
	Advisor = 'ADVISOR',
	Agent = 'AGENT',
	Alias = 'ALIAS',
	BoardMember = 'BOARD_MEMBER',
	BusinessPartner = 'BUSINESS_PARTNER',
	Buyer = 'BUYER',
	Ceo = 'CEO',
	Chairman = 'CHAIRMAN',
	Child = 'CHILD',
	Client = 'CLIENT',
	Cofounder = 'COFOUNDER',
	Cofunder = 'COFUNDER',
	Colleague = 'COLLEAGUE',
	Collection = 'COLLECTION',
	Collector = 'COLLECTOR',
	College = 'COLLEGE',
	Company = 'COMPANY',
	Coo = 'COO',
	Council = 'COUNCIL',
	CoChairman = 'CO_CHAIRMAN',
	Curator = 'CURATOR',
	Dealer = 'DEALER',
	Director = 'DIRECTOR',
	Donor = 'DONOR',
	Employee = 'EMPLOYEE',
	Employer = 'EMPLOYER',
	ExSpouse = 'EX_SPOUSE',
	Fair = 'FAIR',
	Family = 'FAMILY',
	Foundation = 'FOUNDATION',
	Founder = 'FOUNDER',
	Friend = 'FRIEND',
	Gallery = 'GALLERY',
	Group = 'GROUP',
	Institution = 'INSTITUTION',
	Library = 'LIBRARY',
	Member = 'MEMBER',
	Museum = 'MUSEUM',
	Organisation = 'ORGANISATION',
	OtherFamily = 'OTHER_FAMILY',
	Owner = 'OWNER',
	Parent = 'PARENT',
	Partner = 'PARTNER',
	President = 'PRESIDENT',
	Professional = 'PROFESSIONAL',
	Recipient = 'RECIPIENT',
	Seller = 'SELLER',
	Sibling = 'SIBLING',
	Spouse = 'SPOUSE',
	Subsidiary = 'SUBSIDIARY',
	TradingVehicle = 'TRADING_VEHICLE',
	Trustee = 'TRUSTEE',
	ViceChairman = 'VICE_CHAIRMAN',
}

export type Relationship_Type = {
	__typename?: 'relationship_type';
	from_entity_type?: Maybe<Relationship_Entity_Type>;
	key?: Maybe<Relationship_Type_Enum>;
	to_entity_type?: Maybe<Relationship_Entity_Type>;
};

export enum Relationship_Type_Enum {
	AdvisorClient = 'ADVISOR_CLIENT',
	AgentGallery = 'AGENT_GALLERY',
	AliasAlias = 'ALIAS_ALIAS',
	BoardMemberCompany = 'BOARD_MEMBER_COMPANY',
	BoardMemberMuseum = 'BOARD_MEMBER_MUSEUM',
	BusinessPartnerBusinessPartner = 'BUSINESS_PARTNER_BUSINESS_PARTNER',
	BuyerSeller = 'BUYER_SELLER',
	CeoCompany = 'CEO_COMPANY',
	CeoMuseum = 'CEO_MUSEUM',
	ChairmanCompany = 'CHAIRMAN_COMPANY',
	ChildParent = 'CHILD_PARENT',
	ClientAdvisor = 'CLIENT_ADVISOR',
	ClientDealer = 'CLIENT_DEALER',
	CofounderCompany = 'COFOUNDER_COMPANY',
	CofounderFair = 'COFOUNDER_FAIR',
	CofounderMuseum = 'COFOUNDER_MUSEUM',
	ColleagueColleague = 'COLLEAGUE_COLLEAGUE',
	CollectionFounder = 'COLLECTION_FOUNDER',
	CollectorAdvisor = 'COLLECTOR_ADVISOR',
	CollectorFounder = 'COLLECTOR_FOUNDER',
	CollectorMuseum = 'COLLECTOR_MUSEUM',
	CompanyAdvisor = 'COMPANY_ADVISOR',
	CompanyCofounder = 'COMPANY_COFOUNDER',
	CompanyCoChairman = 'COMPANY_CO_CHAIRMAN',
	CompanyEmployee = 'COMPANY_EMPLOYEE',
	CompanyFounder = 'COMPANY_FOUNDER',
	CompanyOwner = 'COMPANY_OWNER',
	CooCompany = 'COO_COMPANY',
	CuratorCompany = 'CURATOR_COMPANY',
	DealerClient = 'DEALER_CLIENT',
	DealerGallery = 'DEALER_GALLERY',
	DirectorCompany = 'DIRECTOR_COMPANY',
	DirectorFoundation = 'DIRECTOR_FOUNDATION',
	DirectorGallery = 'DIRECTOR_GALLERY',
	DirectorMuseum = 'DIRECTOR_MUSEUM',
	DonorCollege = 'DONOR_COLLEGE',
	DonorFoundation = 'DONOR_FOUNDATION',
	DonorGallery = 'DONOR_GALLERY',
	DonorInstitution = 'DONOR_INSTITUTION',
	DonorLibrary = 'DONOR_LIBRARY',
	DonorMuseum = 'DONOR_MUSEUM',
	DonorRecipient = 'DONOR_RECIPIENT',
	EmployeeCompany = 'EMPLOYEE_COMPANY',
	EmployeeEmployer = 'EMPLOYEE_EMPLOYER',
	EmployeeMuseum = 'EMPLOYEE_MUSEUM',
	ExSpouseExSpouse = 'EX_SPOUSE_EX_SPOUSE',
	FamilyFamily = 'FAMILY_FAMILY',
	FoundationFounder = 'FOUNDATION_FOUNDER',
	FounderChairman = 'FOUNDER_CHAIRMAN',
	FounderCompany = 'FOUNDER_COMPANY',
	FounderFoundation = 'FOUNDER_FOUNDATION',
	FounderGallery = 'FOUNDER_GALLERY',
	FounderMuseum = 'FOUNDER_MUSEUM',
	FriendFriend = 'FRIEND_FRIEND',
	GalleryCouncil = 'GALLERY_COUNCIL',
	GalleryDirector = 'GALLERY_DIRECTOR',
	InstitutionPresident = 'INSTITUTION_PRESIDENT',
	MemberFoundation = 'MEMBER_FOUNDATION',
	MemberGroup = 'MEMBER_GROUP',
	MuseumChairman = 'MUSEUM_CHAIRMAN',
	MuseumDirector = 'MUSEUM_DIRECTOR',
	OrganisationCofunder = 'ORGANISATION_COFUNDER',
	OtherFamilyOtherFamily = 'OTHER_FAMILY_OTHER_FAMILY',
	OwnerCollection = 'OWNER_COLLECTION',
	OwnerCompany = 'OWNER_COMPANY',
	OwnerFoundation = 'OWNER_FOUNDATION',
	OwnerGallery = 'OWNER_GALLERY',
	OwnerMuseum = 'OWNER_MUSEUM',
	ParentChild = 'PARENT_CHILD',
	PartnerCompany = 'PARTNER_COMPANY',
	PartnerPartner = 'PARTNER_PARTNER',
	PresidentCompany = 'PRESIDENT_COMPANY',
	ProfessionalProfessional = 'PROFESSIONAL_PROFESSIONAL',
	SiblingSibling = 'SIBLING_SIBLING',
	SpouseSpouse = 'SPOUSE_SPOUSE',
	SubsidiaryCompany = 'SUBSIDIARY_COMPANY',
	TradingVehicleCompany = 'TRADING_VEHICLE_COMPANY',
	TrusteeCompany = 'TRUSTEE_COMPANY',
	TrusteeMuseum = 'TRUSTEE_MUSEUM',
	ViceChairmanCompany = 'VICE_CHAIRMAN_COMPANY',
}

export type Report = {
	__typename?: 'report';
	config?: Maybe<Report_Config>;
	csv_file?: Maybe<Directus_Files>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	error_message?: Maybe<Scalars['String']['output']>;
	html_file?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['String']['output']>;
	params?: Maybe<Scalars['String']['output']>;
	pdf_file?: Maybe<Directus_Files>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Report_Config = {
	__typename?: 'report_config';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	folder_for_uploads?: Maybe<Directus_Folders>;
	id?: Maybe<Scalars['String']['output']>;
	params?: Maybe<Array<Maybe<Report_Config_Report_Param>>>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Report_Config_Report_Param = {
	__typename?: 'report_config_report_param';
	id?: Maybe<Scalars['Int']['output']>;
	report_config_id?: Maybe<Report_Config>;
	report_param_id?: Maybe<Report_Param>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Report_Param = {
	__typename?: 'report_param';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	label?: Maybe<Scalars['String']['output']>;
	param_name?: Maybe<Scalars['String']['output']>;
	required?: Maybe<Scalars['Boolean']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Search_Preset = {
	__typename?: 'search_preset';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	title?: Maybe<Scalars['String']['output']>;
	url?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Status = {
	__typename?: 'status';
	key?: Maybe<Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Status_Enum {
	Archived = 'archived',
	Published = 'published',
}

export type Tag = {
	__typename?: 'tag';
	status?: Maybe<Status>;
	tag?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Tag_Type>;
};

export type Tag_Type = {
	__typename?: 'tag_type';
	key?: Maybe<Tag_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Tag_Type_Enum {
	Category = 'CATEGORY',
	Subject = 'SUBJECT',
}

export type Timezone = {
	__typename?: 'timezone';
	offset?: Maybe<Scalars['Int']['output']>;
	offset_dst?: Maybe<Scalars['Int']['output']>;
	timezone?: Maybe<Scalars['String']['output']>;
};

export type Validation_Error = {
	__typename?: 'validation_error';
	column?: Maybe<Scalars['String']['output']>;
	error_description?: Maybe<Scalars['String']['output']>;
	error_id?: Maybe<Scalars['String']['output']>;
	table?: Maybe<Scalars['String']['output']>;
	validation_error_event?: Maybe<Array<Maybe<Validation_Error_Event>>>;
};

export type Validation_Error_Event = {
	__typename?: 'validation_error_event';
	error_id?: Maybe<Validation_Error>;
	event_id?: Maybe<Scalars['String']['output']>;
	master_record_id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Validation_Status>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type Validation_Status = {
	__typename?: 'validation_status';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Validation_Status_Enum>;
};

export enum Validation_Status_Enum {
	Failed = 'FAILED',
	Fixed = 'FIXED',
	Pending = 'PENDING',
}

export type Win_Type = {
	__typename?: 'win_type';
	description?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Win_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Win_Type_Enum {
	Nominee = 'NOMINEE',
	Winner = 'WINNER',
}
