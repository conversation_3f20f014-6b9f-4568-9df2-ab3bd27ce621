import { BasicDevConfig } from './basic-dev-config';
import { PUBLIC_APP_ENV } from '$env/static/public';

const env = PUBLIC_APP_ENV || 'dev';

interface AppConfig {
	ClientGraphqlApiDomain: string;
	GraphqlApiDomain: string;
	CustomArteyeGraphqlApiUrl: string;
	GraphqlApiUrl: string;
	CustomGraphqlApiUrl: string;
	GraphqlApiKey: string;
	SystemGraphqlApiUrl: string;
	Domain: string;
	ArteyeGraphqlApiDomain: string;
	ArteyeGraphqlApiUrl: string;
	ArteyeGraphqlApiKey: string;
	PhoneAuthenticationUrl: string;
	ArteyeDomain: string;
}

export const ProjectName = 'art-data';
export const AssetsUrl = 'https://resources.heni.com';

export const DevConfig: AppConfig = Object.freeze({
	...BasicDevConfig,
	Domain: PUBLIC_APP_ENV
		? 'https://summer-breeze.no-zero.net'
		: 'http://localhost:5173',
	ClientGraphqlApiDomain: BasicDevConfig.GraphqlApiDomain,
	GraphqlApiDomain:
		PUBLIC_APP_ENV === 'dev'
			? 'http://localhost:3003'
			: 'https://deep-tree.no-zero.net',
});

// TypeGenerationApiKey not necessary for staging as the key is only used to generate the typed documents
export const StagingConfig: AppConfig = Object.freeze({
	ClientGraphqlApiDomain: 'https://shady-autumn.no-zero.net',
	GraphqlApiDomain: 'http://0.0.0.0:4006',
	// GraphqlApiDomain: 'https://shady-autumn.no-zero.net',
	GraphqlApiKey: '',
	CustomGraphqlApiUrl: 'https://shady-autumn.no-zero.net/custom-graphql',
	GraphqlApiUrl: 'https://shady-autumn.no-zero.net/graphql',
	SystemGraphqlApiUrl: 'https://shady-autumn.no-zero.net/graphql/system',
	Domain: 'https://purple-patch.no-zero.net',
	CustomArteyeGraphqlApiUrl:
		'https://baby-bratwurst.no-zero.net/custom-graphql',
	ArteyeGraphqlApiUrl: 'https://baby-bratwurst.no-zero.net/graphql',
	ArteyeGraphqlApiDomain: 'https://baby-bratwurst.no-zero.net',
	ArteyeGraphqlApiKey: 'W3mXriQXPyIma8J-GzLRKOZY1sCd3JnT',
	PhoneAuthenticationUrl: 'https://fair-authentication.toons.byorl.org',
	ArteyeDomain: 'https://sandy-monkey.no-zero.net',
});

// GraphqlApiKey not necessary for production as the key is only used to generate the typed documents
export const ProductionConfig: AppConfig = Object.freeze({
	ClientGraphqlApiDomain: 'https://general-lordship.no-zero.net',
	GraphqlApiDomain: 'http://0.0.0.0:3003',
	// GraphqlApiDomain: 'https://general-lordship.no-zero.net',
	GraphqlApiKey: '',
	CustomGraphqlApiUrl: 'https://general-lordship.no-zero.net/custom-graphql',
	GraphqlApiUrl: 'https://general-lordship.no-zero.net/graphql',
	SystemGraphqlApiUrl: 'https://general-lordship.no-zero.net/graphql/system',
	Domain: 'https://classic-craft.no-zero.net',
	CustomArteyeGraphqlApiUrl: 'https://marginal-mist.no-zero.net/custom-graphql',
	ArteyeGraphqlApiUrl: 'https://marginal-mist.no-zero.net/graphql',
	ArteyeGraphqlApiDomain: 'https://marginal-mist.no-zero.net',
	ArteyeGraphqlApiKey: 'W3mXriQXPyIma8J-GzLRKOZY1sCd3JnT',
	PhoneAuthenticationUrl: 'https://fair-authentication.toons.byorl.org',
	ArteyeDomain: 'https://sharp-monster.no-zero.net',
});

export const Config = (() => {
	switch (env) {
		case 'production':
			return ProductionConfig;
		case 'staging':
			return StagingConfig;
		default:
			return DevConfig;
	}
})();
