export enum Routes {
	Home = '/',
	AddData = '/add-data',
	Login = '/login',
	FairsHome = '/fairs',
	FairsAdd = '/fairs/add',
	AddMoreImages = '/images/add',
	AddIcloudImages = '/images/add-icloud',
	AssignIcloudImages = '/images/add-icloud/assign',
	ScrapedData = '/scraped-data',
	ScrapedDataPreview = '/scraped-data/preview',
	ScrapedDataMatchEvents = '/scraped-data/match-events',
	ScrapedDataProcessDataImages = '/scraped-data/process-data/images',
	ScrapedDataProcessDataArtworkDetails = '/scraped-data/process-data/artwork-details',
	ScrapedDataProcessDataArtistInfo = '/scraped-data/process-data/artist-info',
	ScrapedDataProcessDataExecuted = '/scraped-data/process-data/executed',
	ScrapedDataProcessDataDimensions = '/scraped-data/process-data/dimensions',
	ScrapedDataProcessDataEditionsInfo = '/scraped-data/process-data/editions-info',
	ScrapedDataProcessDataActivityListingInfo = '/scraped-data/process-data/activity-listing-info',
	ScrapedDataProcessDataAuctionInfo = '/scraped-data/process-data/auction-info',
	ScrapedDataProcessDataEntities = '/scraped-data/process-data/entities',
	ScrapedDataProvenanceLinking = '/scraped-data/process-data/provenance-linking',
	ScrapedDataProcessDataFinalise = '/scraped-data/process-data/finalise',
	CropImages = '/images/crop',
	ViewImages = '/images/view',
	EditExtractedImages = '/edit-extracted-images',
	GalleryOfferingsHome = '/gallery-offerings',
	GalleryOfferingsAdd = '/gallery-offerings/add',
	ExhibitionsHome = '/exhibitions',
	ExhibitionsAdd = '/exhibitions/add',
	MatchArtworks = '/images/match-artworks',
	ViewArtworks = '/view-artworks',
	ReviewArtworks = '/review-artworks',
	ViewCompleted = '/view-completed',
	ViewMatches = '/view-matches',
	ManualUpload = '/manual-upload',
	ManualUploadAdd = '/manual-upload/add',
	Artlogic = '/artlogic',
	ArtlogicAdd = '/artlogic/add',
	Pdfs = '/pdfs',
	PdfsAdd = '/pdfs/add',
	Welcome = '/welcome',
	Visits = '/visits',
	WelcomeFreelancer = '/welcome-freelancer',
	Phones = '/phones',
	PhonesAdd = '/phones/add',
	PhonesStatuses = '/phones-statuses',
}
