export enum SearchRange {
	EqualTo = 'equal',
	Between = 'between',
	GreaterThan = 'greater_than',
	LessThan = 'less_than',
}

export const SEARCH_RANGE_OPTIONS = [
	{ label: 'Equal to', value: SearchRange.EqualTo },
	{ label: 'Between', value: SearchRange.Between },
	{ label: 'Greater than', value: SearchRange.GreaterThan },
	{ label: 'Less than', value: SearchRange.LessThan },
];

export const SEARCH_RANGE_PLACEHOLDERS_FULL: Record<string, string> = {
	[SearchRange.EqualTo]: 'DD/MM/YYYY',
	[SearchRange.Between]: 'DD/MM/YYYY-DD/MM/YYYY',
	[SearchRange.GreaterThan]: 'DD/MM/YYYY',
	[SearchRange.LessThan]: 'DD/MM/YYYY',
};
