<script lang="ts">
	import classNames from 'classnames';

	interface Props {
		disabled?: boolean | undefined;
		children?: import('svelte').Snippet;
		onclick?: () => void;
	}

	let { disabled = undefined, children, onclick }: Props = $props();
</script>

<li class={classNames('w-full ', { 'hover:bg-gray-100': !disabled })}>
	<button
		class={classNames(
			'flex h-full w-full items-center justify-between px-3 py-1.5',
			{
				'cursor-not-allowed [&>p]:text-gray-400 [&_path]:fill-gray-400':
					disabled,
			}
		)}
		{onclick}
		type="button"
	>
		{@render children?.()}
	</button>
</li>
