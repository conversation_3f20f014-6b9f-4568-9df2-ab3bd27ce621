<script lang="ts">
	import classNames from 'classnames';
	import { navigating } from '$app/state';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';

	interface Props {
		backHref?: string;
		visible?: boolean;
		loading: boolean;
		disabled: boolean;
		nextLabel?: string;
		backLabel?: string;
		onSaveClick: () => void;
		children?: import('svelte').Snippet;
	}

	let {
		backHref = '',
		visible = false,
		loading,
		disabled,
		nextLabel = '',
		backLabel = '',
		onSaveClick,
		children,
	}: Props = $props();

	const dataCy = 'page-save-bar';
</script>

{#if visible}
	<div
		class={classNames(
			'fixed bottom-0 z-50 flex w-[100vw] border-t border-gray-200 bg-white px-8 py-4',
			backHref ? 'justify-between' : 'justify-end'
		)}
	>
		{#if backHref}
			<LinkButton
				variant="secondary"
				disabled={loading || !!navigating.complete}
				dataCy={`${dataCy}-back`}
				size="md"
				href={backHref}
			>
				{backLabel || 'Back'}
			</LinkButton>
		{/if}

		<div class="flex items-center gap-4">
			{@render children?.()}

			<Button
				{loading}
				disabled={loading || !!navigating.complete || disabled}
				dataCy={`${dataCy}-save-changes`}
				size="md"
				onclick={onSaveClick}
			>
				{nextLabel || 'Save Changes'}
			</Button>
		</div>
	</div>
{/if}
