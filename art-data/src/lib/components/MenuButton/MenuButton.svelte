<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { Button } from '$global/components/Button';

	interface Props {
		dataCy: string;
		class?: string;
		onclick?: () => void;
	}

	let { onclick, ...props }: Props = $props();
</script>

<Button
	size="xs"
	dataCy={props.dataCy}
	type="button"
	{onclick}
	variant="secondary"
	class={twMerge('h-[2rem] w-[2rem]', props.class)}
>
	<span class="inline-block translate-y-[-0.25rem]">...</span>
</Button>
