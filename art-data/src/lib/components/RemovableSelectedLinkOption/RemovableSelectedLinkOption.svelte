<script lang="ts">
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import {
		type Option,
		type OptionClasses,
	} from '$global/components/QueryAutocomplete/types';

	interface Props {
		handleClick?: undefined | (() => void);
		dataCy: string;
		option: Option;
		classes?: OptionClasses;
	}

	let {
		handleClick = undefined,
		dataCy,
		option,
		classes = {},
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-selected-option`);
</script>

<div
	data-cy={dataCyPrefix}
	class="flex flex-col gap-2 rounded border border-gray-200 p-3"
>
	<LinkOption dataCy={dataCyPrefix} {option} {classes} />
	{#if handleClick}
		<Button
			variant="secondary"
			class="ml-[-0.25rem] max-w-[5.25rem] !border-0"
			size="xs"
			dataCy={`${dataCyPrefix}-remove`}
			onclick={handleClick}
		>
			{#snippet leading()}
				<CrossIcon class="mr-1 h-4 w-4" />
			{/snippet}
			remove
		</Button>
	{/if}
</div>
