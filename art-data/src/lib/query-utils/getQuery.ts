// TODO : write unit tests

import type { TypedDocumentNode } from '@graphql-typed-document-node/core';
import { type Variables } from 'graphql-request';
import { gqlClient } from '../gqlClient';
import { getQuery as globalGetQuery } from '$global/query-utils/getQuery';

export const getQuery = <T, V extends Variables = Variables>(
	document: TypedDocumentNode<T, V>,
	variables?: V,
	requestHeaders?: Parameters<typeof gqlClient.request>[0]['requestHeaders']
) => globalGetQuery(gqlClient, document, variables, requestHeaders);
