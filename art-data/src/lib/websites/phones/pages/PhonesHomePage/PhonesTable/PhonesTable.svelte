<script lang="ts">
	import dayjs from 'dayjs';
	import { PHONES_LIMIT } from '../utils/phonesHomePageLoad/phonesHomePageLoad';
	import { page } from '$app/state';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getInfiniteData } from '$global/utils/infinite-loading/getInfiniteData';
	import { infiniteQuery } from '$global/utils/infinite-loading/infiniteQuery';
	import { Routes } from '$lib/constants/routes';
	import {
		GetPhoneRegistrationDocument,
		type GetPhoneRegistrationQuery,
	} from '$lib/queries/__generated__/getPhoneRegistrations.generated';
	import { getGqlClient } from '$lib/utils/getGqlClient/getGqlClient';
	import type { PhonesHomePageData } from '$routes/phones/types';

	interface Props {
		pageNumber: number;
		onClickPage: (event: Event | undefined, page?: number | null) => void;
	}

	let { pageNumber, onClickPage }: Props = $props();

	let queryVariables = $derived(page.data.queryVariables);
	const gqlClient = getGqlClient(page.data as PhonesHomePageData);

	let query = $derived(
		infiniteQuery({
			gqlClient,
			variables: queryVariables,
			document: GetPhoneRegistrationDocument,
			limit: PHONES_LIMIT,
		})
	);

	let phoneRegistrations = $derived(
		getInfiniteData({
			query: $query,
			transform: (data) => {
				return data.Visit_Phone_Registration;
			},
		})
	);

	let count = $derived(
		$query.data?.pages[0]?.Visit_Phone_Registration_aggregated?.[0]
			?.countDistinct?.id || 0
	);

	const actionCellWidth = '0rem';
	const headers = [
		'Phone',
		'Photographer',
		'Start Date',
		'End Date',
		'Location',
		'Date Created',
		'Edit Registration',
	];

	const formatPhoneRegistration = (
		phone: GetPhoneRegistrationQuery['Visit_Phone_Registration'][number]
	) => [
		phone.phone?.id,
		[phone.photographer?.first_name, phone.photographer?.last_name]
			.filter(Boolean)
			.join(' '),
		dayjs(phone?.start_date).format('DD/MM/YYYY HH:mm'),
		dayjs(phone?.end_date).format('DD/MM/YYYY HH:mm'),
		phone?.location,
		dayjs(phone?.date_created).format('DD/MM/YYYY'),
	];

	const dataCyPrefix = 'phones-home-table';
</script>

<div class="mb-6 flex items-end justify-between gap-4">
	<Txt variant="h5"
		>{#if count}{count} results{/if}</Txt
	>

	<div class="flex items-center gap-4">
		<LinkButton
			href={`${Routes.PhonesStatuses}`}
			variant="secondary"
			dataCy={`${dataCyPrefix}-phone-status`}
			size="md">phone statuses</LinkButton
		>

		<LinkButton
			href={`${Routes.PhonesAdd}`}
			dataCy={`${dataCyPrefix}-register`}
			size="md">register phone</LinkButton
		>
	</div>
</div>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	{#if phoneRegistrations}
		<TableBody dataCy={dataCyPrefix}>
			{#each phoneRegistrations as phoneRegistration, i}
				<TableRow index={i} dataCy={dataCyPrefix}>
					{@const formattedPhoneRegistration =
						formatPhoneRegistration(phoneRegistration)}
					{#each formattedPhoneRegistration as formattedPhoneRegistrationValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							content={formattedPhoneRegistrationValue}
						>
							{formattedPhoneRegistrationValue}
						</TableCell>
					{/each}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
					>
						{#snippet custom()}
							<Txt
								component="a"
								variant="body2"
								class="text-blue-500"
								href={`${Routes.Phones}/${phoneRegistration.id}`}>Edit</Txt
							>
						{/snippet}
					</TableCell>
				</TableRow>
			{/each}

			{#if !phoneRegistrations.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No phone registration to display</TableNoResults
				>
			{/if}
		</TableBody>
	{/if}
</table>

{#if $query.isFetching}
	<div class="mt-3 flex justify-center">
		<CircularProgress dataCy={dataCyPrefix} />
	</div>
{:else}
	<div class="mt-2 flex items-center justify-between">
		{#if count}
			<Txt variant="body3">
				Showing {(pageNumber - 1) * PHONES_LIMIT + 1} - {(pageNumber - 1) *
					PHONES_LIMIT +
					(phoneRegistrations?.length || 0)} of {count}
				results
			</Txt>
		{/if}
		<Pagination
			onClick={onClickPage}
			dataCy={dataCyPrefix}
			currentPage={pageNumber}
			limit={PHONES_LIMIT}
			total={count}
		/>
	</div>
{/if}
