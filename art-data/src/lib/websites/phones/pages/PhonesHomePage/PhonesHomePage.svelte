<script lang="ts">
	import { PhonesTable } from './PhonesTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'phones-home';

	const crumbs = [{ label: 'Home', href: Routes.Home }, { label: 'Phones' }];

	let pageQueryParam = $derived(page.url.searchParams.get('page'));
	let pageNumber = $derived(pageQueryParam ? +pageQueryParam : 1);

	const getSearchParamsObj = (page?: number | null) => {
		return {
			...(page &&
				page > 1 && {
					page: `${page}`,
				}),
		};
	};

	const handleSubmit = (event: Event | undefined, page?: number | null) => {
		const searchParamsObj = getSearchParamsObj(page);
		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<PhonesTable {pageNumber} onClickPage={handleSubmit} />
	</Container>
</PageBody>
