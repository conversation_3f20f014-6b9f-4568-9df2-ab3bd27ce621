import { type QueryClient } from '@tanstack/svelte-query';
import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetPhoneRegistrationDocument } from '$lib/queries/__generated__/getPhoneRegistrations.generated';
import { getQuery } from '$lib/query-utils/getQuery';
import { type GetExhibitionsTableQueryVariables } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
import type { PhonesHomePageLoadEvent } from '$routes/phones/types';

export const PHONES_LIMIT = 10;
export const PHONES_STALE_TIME = 1000 * 60 * 60;

export const phonesHomePageLoad = async ({
	parent,
	url,
}: PhonesHomePageLoadEvent) => {
	const parentData = await parent();
	const queryParamsVariables = getInitialVariables(url.searchParams);
	const queryClient: QueryClient = parentData.queryClient;

	const queryVariables: GetExhibitionsTableQueryVariables = {
		sort: '-date_created',
		limit: PHONES_LIMIT,
		...(queryParamsVariables.page && {
			offset: PHONES_LIMIT * (queryParamsVariables.page - 1),
		}),
	};

	queryClient.prefetchInfiniteQuery({
		...getQuery(
			GetPhoneRegistrationDocument,
			queryVariables,
			getAuthorizationHeaders(parentData)
		),
		initialPageParam: 0,
		staleTime: PHONES_STALE_TIME,
	});

	return {
		...parentData,
		queryVariables,
	};
};
