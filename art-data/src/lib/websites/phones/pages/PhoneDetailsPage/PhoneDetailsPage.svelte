<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Select } from '$global/components/Select';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepDescription } from '$global/features/form/components/StepDescription';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { StepTitle } from '$global/features/form/components/StepTitle';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type {
		Create_Visit_Phone_Registration_Input,
		Update_Visit_Phone_Registration_Input,
	} from '$gql/types';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { CreatePhoneRegistrationDocument } from '$lib/queries/__generated__/createPhoneRegistration.generated';
	import { UpdatePhoneRegistrationDocument } from '$lib/queries/__generated__/updatePhoneRegistration.generated';
	import type { PhonesIdPageData } from '$routes/phones/[id]/types';

	let submitting = $state(false);

	const dataCyPrefix = 'phone-details';
	let data = $derived(getPageData<PhonesIdPageData>(page.data));

	let originalPhoneDetails = $derived(data.phoneDetails);

	let phoneOptions = $derived(
		data.phones.map((phone) => ({
			label: `Phone ${phone.id}`,
			value: phone.id,
		}))
	);

	let userOptions = $derived(
		data.users.map((user) => ({
			label: [user.first_name, user.last_name].filter(Boolean).join(' '),
			value: user.id,
		}))
	);

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		submitting = true;

		const phone = data.phones.find((phone) => phone.id === phoneDetails.phone);

		if (!phone) {
			submitting = false;
			return;
		}

		const payload = {
			location: phoneDetails.location,
			photographer: phoneDetails.photographer,
			phone: {
				id: phone?.id,
				icloud_account: phone?.icloud_account,
				authentication_status: {
					key: `${phone?.authentication_status?.key}`,
				},
			},
			start_date: `${phoneDetails?.start_date}T${phoneDetails?.start_time}:00.000Z`,
			end_date: `${phoneDetails?.end_date}T${phoneDetails?.end_time}:00.000Z`,
		};

		if (page.params.id) {
			await gqlClient.request(
				UpdatePhoneRegistrationDocument,
				{
					id: page.params.id,
					data: payload as Update_Visit_Phone_Registration_Input,
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'The phone registration has been successfully updated',
			});
		} else {
			await gqlClient.request(
				CreatePhoneRegistrationDocument,
				{ data: payload as Create_Visit_Phone_Registration_Input },
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'The phone registration has been successfully created',
			});
		}

		(window as unknown as { location: string }).location = Routes.Phones;
		submitting = false;
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Phones', href: Routes.Phones },
		{ label: page.params.id ? 'Edit' : 'Add' },
	];

	let phoneDetails = $state({
		location: '',
		phone: '',
		photographer: '',
		start_time: '',
		end_time: '',
		start_date: '',
		end_date: '',
	});

	// eslint-disable-next-line svelte/valid-compile
	let basePhoneDetails = $state({ ...phoneDetails });
	let isReady = $state(false);

	onMount(() => {
		if (originalPhoneDetails) {
			phoneDetails = {
				location: originalPhoneDetails?.location || '',
				phone: originalPhoneDetails?.phone?.id || '',
				photographer: originalPhoneDetails?.photographer?.id || '',
				start_time: originalPhoneDetails?.start_time || '',
				end_time: originalPhoneDetails?.end_time || '',
				start_date: originalPhoneDetails?.start_date || '',
				end_date: originalPhoneDetails?.end_date || '',
			};

			basePhoneDetails = { ...phoneDetails };
		}

		isReady = true;
	});

	let blur = $state(
		// eslint-disable-next-line svelte/valid-compile
		Object.keys(phoneDetails || {}).reduce(
			(accumulator, key) => ({
				...accumulator,
				[key]: false,
			}),
			{}
		) as Record<keyof typeof phoneDetails, boolean>
	);

	const isValidTime = (startTime: string) => {
		const hours = +startTime.slice(0, 2);
		const minutes = +startTime.slice(3, 5);
		return (
			startTime.length === 5 &&
			!isNaN(hours) &&
			!isNaN(minutes) &&
			hours <= 23 &&
			hours >= 0 &&
			minutes >= 0 &&
			minutes <= 59 &&
			startTime[2] === ':'
		);
	};

	let getTimeFieldError = $derived((field: keyof typeof phoneDetails) => {
		if (!blur[field]) {
			return '';
		}

		if (!phoneDetails[field]) {
			return 'This field must be provided';
		}

		if (!isValidTime(phoneDetails[field])) {
			return 'The format is not correct';
		}

		return '';
	});
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<StepTitle>Phone registration</StepTitle>
	<StepDescription>
		Complete this form to register a phone to a user for an event
	</StepDescription>
	{#if isReady}
		<form onsubmit={handleSubmit} class="mb-[4rem] sm:mb-[7rem]">
			<StepContainer>
				<div class="col-span-2">
					<Select
						ariaLabel="select a phone"
						dataCy={`${dataCyPrefix}-phone`}
						name="phone"
						required
						label="Phone"
						placeholder="Phone"
						options={phoneOptions}
						error={!phoneDetails.phone && blur.phone
							? 'This field must be provided'
							: ''}
						bind:value={phoneDetails.phone}
						onblur={() => {
							blur.phone = true;
						}}
					/>
				</div>

				<div class="col-span-2">
					<Select
						placeholder="Photographer"
						ariaLabel="select a photographer"
						dataCy={`${dataCyPrefix}-photographer`}
						name="photographer"
						label="Photographer"
						required
						options={userOptions}
						error={!phoneDetails.photographer && blur.photographer
							? 'This field must be provided'
							: ''}
						bind:value={phoneDetails.photographer}
						onblur={() => {
							blur.photographer = true;
						}}
					/>
				</div>

				<StepInput
					dataCy={`${dataCyPrefix}-location`}
					label="Location"
					class="col-span-2"
					placeholder="Location"
					id="location"
					name="location"
					required
					stopPropagationWhenPressingEnter
					error={!phoneDetails.location && blur.location
						? 'This field must be provided'
						: ''}
					bind:value={phoneDetails.location}
					onblur={() => {
						blur.location = true;
					}}
				/>

				<StepInput
					dataCy={`${dataCyPrefix}-start-time`}
					label="Start Time"
					placeholder="Start Time"
					id="start-time"
					name="start-time"
					required
					stopPropagationWhenPressingEnter
					error={getTimeFieldError('start_time')}
					bind:value={phoneDetails.start_time}
					helper="Must be in 24-hour time notation"
					onblur={() => {
						blur.start_time = true;
					}}
				/>

				<StepInput
					dataCy={`${dataCyPrefix}-start-date`}
					label="Start Date"
					placeholder="DD/MM/YYYY"
					id="start-date"
					class="col-span-1"
					name="start-date"
					type="date"
					required
					stopPropagationWhenPressingEnter
					error={!phoneDetails.start_date && blur.start_date
						? 'This field must be provided'
						: ''}
					bind:value={phoneDetails.start_date}
					onblur={() => {
						blur.start_date = true;
					}}
				/>

				<StepInput
					dataCy={`${dataCyPrefix}-end-time`}
					label="End Time"
					placeholder="End Time"
					id="end-time"
					name="end-time"
					required
					stopPropagationWhenPressingEnter
					error={getTimeFieldError('end_time')}
					bind:value={phoneDetails.end_time}
					helper="Must be in 24-hour time notation"
					onblur={() => {
						blur.end_time = true;
					}}
				/>

				<StepInput
					dataCy={`${dataCyPrefix}-end-date`}
					label="End Date"
					placeholder="DD/MM/YYYY"
					id="end-date"
					class="col-span-1"
					name="end-date"
					type="date"
					required
					stopPropagationWhenPressingEnter
					error={!phoneDetails.start_date && blur.start_date
						? 'This field must be provided'
						: ''}
					bind:value={phoneDetails.end_date}
					onblur={() => {
						blur.end_date = true;
					}}
				/>
			</StepContainer>

			<StepButtons
				backButtonProps={{
					href: Routes.Phones,
				}}
				continueButtonProps={{
					disabled:
						Object.values(phoneDetails).some((detail) => !detail) ||
						!!getTimeFieldError('start_time') ||
						!!getTimeFieldError('end_time') ||
						JSON.stringify(phoneDetails) === JSON.stringify(basePhoneDetails),
					loading: submitting,
				}}
			>
				{!page.params.id ? 'Register Phone' : 'Update'}
			</StepButtons>
		</form>
	{/if}
</PageBody>
