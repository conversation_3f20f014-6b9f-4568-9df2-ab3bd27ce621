import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetPhoneRegistrationDocument } from '$lib/queries/__generated__/getPhoneRegistrations.generated';
import {
	GetPhonesDocument,
	type GetPhonesQuery,
} from '$lib/queries/__generated__/getPhones.generated';
import {
	GetUsersDocument,
	type GetUsersQuery,
} from '$lib/system-queries/__generated__/getUsers.generated';
import type { PhonesIdPageServerLoadEvent } from '$routes/phones/[id]/types';

export const phoneDetailsServerLoad = async ({
	params,
	parent,
}: PhonesIdPageServerLoadEvent) => {
	dayjs.extend(utc);
	const parentData = await parent();
	let phoneDetails = null;

	if (params.id) {
		const phoneDetailsResponse = await gqlClient.request(
			GetPhoneRegistrationDocument,
			{ filter: { id: { _eq: params.id } } },
			getAuthorizationHeaders(parentData)
		);

		phoneDetails = phoneDetailsResponse?.Visit_Phone_Registration?.[0];

		if (!phoneDetails) {
			error(404, 'Not found');
		}

		phoneDetails = {
			...phoneDetails,
			start_date: dayjs.utc(phoneDetails.start_date).format('YYYY-MM-DD'),
			start_time: dayjs.utc(phoneDetails.start_date).format('HH:mm'),
			end_date: dayjs.utc(phoneDetails.end_date).format('YYYY-MM-DD'),
			end_time: dayjs.utc(phoneDetails.end_date).format('HH:mm'),
		};
	}

	const promises = [
		gqlClient.request(
			GetPhonesDocument,
			{},
			getAuthorizationHeaders(parentData)
		),
		gqlClientSystem.request(
			GetUsersDocument,
			{},
			getAuthorizationHeaders(parentData)
		),
	];

	const [phonesResponse, usersResponse] = await Promise.all(promises);
	const phones = (phonesResponse as GetPhonesQuery)?.Visit_Phone;
	const users = (usersResponse as GetUsersQuery)?.users;

	return {
		phoneDetails,
		phones,
		users,
	};
};
