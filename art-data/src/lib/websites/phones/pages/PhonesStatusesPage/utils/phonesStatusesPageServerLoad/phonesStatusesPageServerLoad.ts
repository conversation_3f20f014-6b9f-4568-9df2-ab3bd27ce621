import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetPhonesDocument } from '$lib/queries/__generated__/getPhones.generated';
import type { PhonesStatusesPageServerLoadEvent } from '$routes/phones-statuses/types';

export const phonesStatusesPageServerLoad = async ({
	parent,
}: PhonesStatusesPageServerLoadEvent) => {
	const parentData = await parent();
	const phonesResponse = await gqlClient.request(
		GetPhonesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);
	const phones = phonesResponse?.Visit_Phone;

	return {
		...parentData,
		phones,
	};
};
