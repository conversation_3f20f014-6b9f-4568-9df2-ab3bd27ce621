<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { PhoneAuthenticationModal } from './PhoneAuthenticationModal';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import {
		TableHeader,
		TableBody,
		TableHeaderRow,
		getCellWidth,
		TableNoResults,
		TableRow,
		TableCell,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { capitalizeFirstLetters } from '$global/utils/capitalizeFirstLetters';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import type { GetPhonesQuery } from '$lib/queries/__generated__/getPhones.generated';
	import type { PhonesStatusesPageData } from '$routes/phones-statuses/types';

	let selectedPhoneId: string | null = $state(null);
	const dialogStores = createDialog();

	const handleCloseModal = () => {
		selectedPhoneId = null;
		dialogStores.states.open.set(false);
	};

	const handleClickAuthenticate = async (id: string) => {
		await fetch(`${Config.PhoneAuthenticationUrl}/attempt_login`, {
			method: 'POST',
			body: JSON.stringify({ phone_id: id }),
			headers: {
				'Content-Type': 'application/json',
			},
		});

		selectedPhoneId = id;
		dialogStores.states.open.set(true);
	};

	let data = $derived(getPageData<PhonesStatusesPageData>(page.data));
	let phones = $derived(data.phones);

	const headers = ['Phone', 'Status', 'Actions'];
	const actionCellWidth = '10rem';
	const dataCyPrefix = 'phones-statuses';

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Phones', href: Routes.Phones },
		{ label: 'Statuses' },
	];

	const formatPhone = (phone: GetPhonesQuery['Visit_Phone'][number]) => [
		`Phone ${phone.id}`,
		capitalizeFirstLetters(`${phone.authentication_status?.key}`.toLowerCase()),
	];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<Txt variant="h4" class="mb-1">Phone status and authentication</Txt>
		<Txt variant="body2" class="mb-4"
			>You can view the status and re-authenticate phones below. A verification
			code will be sent to the phone.</Txt
		>
		<table class="w-full table-fixed bg-white">
			<TableHeaderRow dataCy={dataCyPrefix}>
				{#each headers as header, i}
					<TableHeader
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
					>
						{header}
					</TableHeader>
				{/each}
			</TableHeaderRow>
			{#if phones}
				<TableBody dataCy={dataCyPrefix}>
					{#each phones as phone, i}
						<TableRow index={i} dataCy={dataCyPrefix}>
							{@const formattedPhone = formatPhone(phone)}
							{#each formattedPhone as formattedPhoneValue}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
									content={formattedPhoneValue}
									class={classNames({
										'[&>div>p]:text-red-500':
											formattedPhoneValue === 'Unauthenticated',
									})}
								>
									{formattedPhoneValue}
								</TableCell>
							{/each}
							<TableCell
								dataCy={dataCyPrefix}
								width={getCellWidth(i, actionCellWidth, headers)}
							>
								{#if `${phone.authentication_status?.key}`.toLowerCase() === 'unauthenticated'}
									<button
										onclick={() => {
											handleClickAuthenticate(phone.id);
										}}
									>
										<Txt variant="body2" class="text-blue-500">Authenticate</Txt
										></button
									>
								{/if}
							</TableCell>
						</TableRow>
					{/each}

					{#if !phones.length}
						<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
							>No phones to display</TableNoResults
						>
					{/if}
				</TableBody>
			{/if}
		</table>
	</Container>
</PageBody>

{#if selectedPhoneId}
	<PhoneAuthenticationModal
		onClose={handleCloseModal}
		{dialogStores}
		phoneId={selectedPhoneId}
	/>
{/if}
