<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { goto } from '$app/navigation';
	import { Dialog } from '$global/components/Dialog';
	import { Input } from '$global/components/Input';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepError } from '$global/features/form/components/StepError';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		onClose: () => void;
		dialogStores: ReturnType<typeof createDialog>;
		phoneId: string;
	}

	let { onClose, dialogStores, phoneId }: Props = $props();

	let value = $state('');
	let errorMessage = $state('');
	let submitting = $state(false);

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		errorMessage = '';
		submitting = true;

		try {
			const response = await fetch(
				`${Config.PhoneAuthenticationUrl}/authenticate`,
				{
					method: 'POST',
					body: JSON.stringify({
						phone_id: phoneId,
						authentication_code: value,
					}),
					headers: {
						'Content-Type': 'application/json',
					},
				}
			);

			const responseJson = await response.json();

			if (!response.ok || responseJson.result === 'failure') {
				throw new Error();
			} else {
				showToast({
					variant: 'success',
					message: 'The phone has been successfully authenticated',
				});

				onClose();
				goto(Routes.PhonesStatuses, { invalidateAll: true });
			}
		} catch {
			errorMessage = 'Invalid authentication code';
			submitting = false;
		}
	};
</script>

<Dialog
	{onClose}
	dataCy="phone-authentication"
	{dialogStores}
	title="Phone authentication"
>
	<form onsubmit={handleSubmit}>
		<div class="mx-auto max-w-[27.5rem]">
			<Txt variant="body2" class="mb-5 text-center">
				Please enter the authentication code below.
			</Txt>

			<Input
				classes={{ labelWrapper: 'mb-1' }}
				name="code"
				dataCy="phone-authentication"
				bind:value
				label="Authentication code"
				required
			/>
		</div>

		<StepButtons
			onclick={onClose}
			backButtonProps={{
				disabled: submitting,
			}}
			continueButtonProps={{
				loading: submitting,
			}}
		>
			Submit
			{#snippet error()}
				<span>
					{#if errorMessage}
						<StepError class="normal-case">
							{errorMessage}
						</StepError>
					{/if}
				</span>
			{/snippet}
		</StepButtons>
	</form>
</Dialog>
