import { gql } from 'graphql-tag';

export const GET_EXHIBITIONS_TABLE = gql`
	query getExhibitionsTable(
		$filter: Ingestion_Exhibition_filter
		$sort: [String]
		$offset: Int
		$limit: Int
	) {
		Ingestion_Exhibition_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
		Ingestion_Exhibition(
			filter: $filter
			sort: $sort
			limit: $limit
			offset: $offset
		) {
			id
			title
			date_created
			event_review_status {
				key
				name
			}
			processed_exhibition {
				processed_exhibition_id
				title
				start_date
				end_date
				location
				organisers
			}
			ingestion_data {
				item {
					... on Manual_Upload {
						high_priority
						manually_added_artworks {
							id
							status {
								key
							}
						}
					}
					... on PDF {
						high_priority
						artworks {
							id
							status {
								key
							}
						}
					}
					... on Artlogic_Link {
						high_priority
					}
				}
			}
			visit {
				id
				artworks {
					id
					status {
						key
					}
				}
			}
		}
	}
`;
