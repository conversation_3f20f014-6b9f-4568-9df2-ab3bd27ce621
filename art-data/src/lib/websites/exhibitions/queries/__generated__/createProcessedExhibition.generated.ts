import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateProcessedExhibitionMutationVariables = Types.Exact<{
	data: Types.Create_Processed_Exhibition_Input;
}>;

export type CreateProcessedExhibitionMutation = {
	__typename?: 'Mutation';
	create_Processed_Exhibition_item?: {
		__typename?: 'Processed_Exhibition';
		processed_exhibition_id: string;
		title: string;
	} | null;
};

export const CreateProcessedExhibitionDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createProcessedExhibition' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: {
								kind: 'Name',
								value: 'create_Processed_Exhibition_input',
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_Processed_Exhibition_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_exhibition_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateProcessedExhibitionMutation,
	CreateProcessedExhibitionMutationVariables
>;
