import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetProcessedExhibitionQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Processed_Exhibition_Filter>;
}>;

export type GetProcessedExhibitionQuery = {
	__typename?: 'Query';
	Processed_Exhibition: Array<{
		__typename?: 'Processed_Exhibition';
		processed_exhibition_id: string;
		title: string;
	}>;
};

export const GetProcessedExhibitionDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getProcessedExhibition' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Processed_Exhibition_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Processed_Exhibition' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_exhibition_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetProcessedExhibitionQuery,
	GetProcessedExhibitionQueryVariables
>;
