import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetExhibitionsWithChecklistsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Ingestion_Exhibition_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetExhibitionsWithChecklistsQuery = {
	__typename?: 'Query';
	Ingestion_Exhibition: Array<{
		__typename?: 'Ingestion_Exhibition';
		id: string;
		title?: string | null;
		processed_exhibition?: {
			__typename?: 'Processed_Exhibition';
			processed_exhibition_id: string;
			title: string;
			start_date?: any | null;
			end_date?: any | null;
			location?: string | null;
			organisers?: string | null;
			artists?: any | null;
		} | null;
		visit?: {
			__typename?: 'Visit';
			id: string;
			visit_images?: Array<{
				__typename?: 'Visit_Image';
				id: string;
				image_taken_date?: any | null;
				extracted_best_guess_coordinates_from_api?: any | null;
				data_admin_submitted_coordinates?: string | null;
				source?: string | null;
				photographer?: {
					__typename?: 'directus_users';
					id?: string | null;
					last_name?: string | null;
					first_name?: string | null;
				} | null;
				user_created?: {
					__typename?: 'directus_users';
					id?: string | null;
					last_name?: string | null;
					first_name?: string | null;
				} | null;
				status?: {
					__typename?: 'Visit_Image_Status';
					key: string;
					name?: string | null;
				} | null;
				perspective_cropped_image_without_dimensions?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
				rectangular_cropped_image?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
				original_uncropped_image?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
			} | null> | null;
		} | null;
		matches?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| { __typename?: 'Artlogic_Link' }
				| {
						__typename?: 'Manual_Upload';
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
						} | null> | null;
				  }
				| {
						__typename?: 'PDF';
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
						} | null> | null;
				  }
				| null;
		} | null> | null;
		ready_for_review?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| { __typename?: 'Artlogic_Link' }
				| {
						__typename?: 'Manual_Upload';
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
						} | null> | null;
				  }
				| {
						__typename?: 'PDF';
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
						} | null> | null;
				  }
				| null;
		} | null> | null;
		skipped?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| { __typename?: 'Artlogic_Link' }
				| {
						__typename?: 'Manual_Upload';
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
						} | null> | null;
				  }
				| {
						__typename?: 'PDF';
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
						} | null> | null;
				  }
				| null;
		} | null> | null;
		reviewed_and_submitted?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| { __typename?: 'Artlogic_Link' }
				| {
						__typename?: 'Manual_Upload';
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
						} | null> | null;
				  }
				| {
						__typename?: 'PDF';
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
						} | null> | null;
				  }
				| null;
		} | null> | null;
		ingestion_data?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| {
						__typename: 'Artlogic_Link';
						id: string;
						title: string;
						high_priority?: boolean | null;
						includes_prices?: boolean | null;
						url: string;
						review_status?: {
							__typename?: 'Checklist_Review_Status';
							key: string;
							name?: string | null;
						} | null;
						status?: {
							__typename?: 'Artlogic_Link_Status';
							name?: string | null;
							key: string;
						} | null;
						processed_gallery?: {
							__typename?: 'Processed_Organisation';
							name: string;
						} | null;
						user_created?: {
							__typename?: 'directus_users';
							first_name?: string | null;
							last_name?: string | null;
						} | null;
						art_event_feed?: {
							__typename?: 'art_event_feed';
							artwork_feed?: Array<{
								__typename?: 'artwork_feed';
								id: string;
							} | null> | null;
						} | null;
						receipt_info?: {
							__typename?: 'Receipt_Information';
							id: string;
							receive_date?: any | null;
							receiver?: string | null;
							sender?: string | null;
						} | null;
				  }
				| {
						__typename: 'Manual_Upload';
						id: string;
						title: string;
						submitted_for_review?: boolean | null;
						for_freelancers?: boolean | null;
						high_priority?: boolean | null;
						includes_prices?: boolean | null;
						user_created?: {
							__typename?: 'directus_users';
							last_name?: string | null;
							first_name?: string | null;
						} | null;
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
							status?: {
								__typename?: 'Artwork_Status_Type';
								key: string;
							} | null;
						} | null> | null;
						receipt_info?: {
							__typename?: 'Receipt_Information';
							receive_date?: any | null;
							receiver?: string | null;
							sender?: string | null;
						} | null;
						processed_fair_exhibitor_org?: {
							__typename?: 'Processed_Organisation';
							entity?: string | null;
							id: string;
							location?: string | null;
							name: string;
							type?: string | null;
						} | null;
						review_status?: {
							__typename?: 'Checklist_Review_Status';
							key: string;
							name?: string | null;
						} | null;
				  }
				| {
						__typename: 'PDF';
						includes_prices?: boolean | null;
						submitted_for_review?: boolean | null;
						for_freelancers?: boolean | null;
						high_priority?: boolean | null;
						id: string;
						title: string;
						review_status?: {
							__typename?: 'Checklist_Review_Status';
							key: string;
							name?: string | null;
						} | null;
						receipt_info?: {
							__typename?: 'Receipt_Information';
							receive_date?: any | null;
							sender?: string | null;
							receiver?: string | null;
						} | null;
						processed_fair_exhibitor_org?: {
							__typename?: 'Processed_Organisation';
							entity?: string | null;
							id: string;
							location?: string | null;
							name: string;
							type?: string | null;
						} | null;
						pdf_file?: {
							__typename?: 'directus_files';
							filename_disk?: string | null;
						} | null;
						user_created?: {
							__typename?: 'directus_users';
							first_name?: string | null;
							last_name?: string | null;
						} | null;
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
							status?: {
								__typename?: 'Artwork_Status_Type';
								key: string;
							} | null;
						} | null> | null;
						status?: { __typename?: 'PDF_Status'; key: string } | null;
				  }
				| null;
		} | null> | null;
	}>;
};

export const GetExhibitionsWithChecklistsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getExhibitionsWithChecklists' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Ingestion_Exhibition_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Ingestion_Exhibition' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_exhibition' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'processed_exhibition_id',
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisers' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'visit_images' },
												arguments: [
													{
														kind: 'Argument',
														name: { kind: 'Name', value: 'limit' },
														value: { kind: 'IntValue', value: '10000' },
													},
												],
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image_taken_date' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'photographer' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'user_created' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value:
																	'extracted_best_guess_coordinates_from_api',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'data_admin_submitted_coordinates',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value:
																	'perspective_cropped_image_without_dimensions',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'rectangular_cropped_image',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'source' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'original_uncropped_image',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'matches' },
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_nin',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value: 'DRAFT',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_nin',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value: 'DRAFT',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'ready_for_review' },
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'AWAITING_REVIEW',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'AWAITING_REVIEW',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'skipped' },
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'SKIPPED',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'SKIPPED',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'reviewed_and_submitted' },
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'REVIEWED_AND_SUBMITTED',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'COMPLETED',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'AWAITING_INGESTION',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'INGESTION_FAILED',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'filter' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'status',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: 'key',
																										},
																										value: {
																											kind: 'ObjectValue',
																											fields: [
																												{
																													kind: 'ObjectField',
																													name: {
																														kind: 'Name',
																														value: '_in',
																													},
																													value: {
																														kind: 'ListValue',
																														values: [
																															{
																																kind: 'StringValue',
																																value:
																																	'REVIEWED_AND_SUBMITTED',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'COMPLETED',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'AWAITING_INGESTION',
																																block: false,
																															},
																															{
																																kind: 'StringValue',
																																value:
																																	'INGESTION_FAILED',
																																block: false,
																															},
																														],
																													},
																												},
																											],
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: '__typename' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'submitted_for_review',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'user_created',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'last_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'first_name',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'status',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'for_freelancers',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'includes_prices',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'receipt_info',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receive_date',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receiver',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'sender',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processed_fair_exhibitor_org',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'review_status',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'review_status',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: '__typename' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'includes_prices',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'submitted_for_review',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'receipt_info',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receive_date',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'sender',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receiver',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processed_fair_exhibitor_org',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'pdf_file' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'filename_disk',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'user_created',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'first_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'last_name',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'for_freelancers',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'status',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'status' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Artlogic_Link' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'review_status',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: '__typename' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'includes_prices',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'status' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processed_gallery',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'user_created',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'first_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'last_name',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'art_event_feed',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'artwork_feed',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'id',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'url' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'receipt_info',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receive_date',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'receiver',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'sender',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetExhibitionsWithChecklistsQuery,
	GetExhibitionsWithChecklistsQueryVariables
>;
