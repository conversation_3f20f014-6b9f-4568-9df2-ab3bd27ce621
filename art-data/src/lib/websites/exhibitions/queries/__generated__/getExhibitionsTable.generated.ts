import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetExhibitionsTableQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Ingestion_Exhibition_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetExhibitionsTableQuery = {
	__typename?: 'Query';
	Ingestion_Exhibition_aggregated: Array<{
		__typename?: 'Ingestion_Exhibition_aggregated';
		countDistinct?: {
			__typename?: 'Ingestion_Exhibition_aggregated_count';
			id?: number | null;
		} | null;
	}>;
	Ingestion_Exhibition: Array<{
		__typename?: 'Ingestion_Exhibition';
		id: string;
		title?: string | null;
		date_created?: any | null;
		event_review_status?: {
			__typename?: 'Event_Review_Status';
			key: string;
			name?: string | null;
		} | null;
		processed_exhibition?: {
			__typename?: 'Processed_Exhibition';
			processed_exhibition_id: string;
			title: string;
			start_date?: any | null;
			end_date?: any | null;
			location?: string | null;
			organisers?: string | null;
		} | null;
		ingestion_data?: Array<{
			__typename?: 'Ingestion_Exhibition_ingestion_data';
			item?:
				| { __typename?: 'Artlogic_Link'; high_priority?: boolean | null }
				| {
						__typename?: 'Manual_Upload';
						high_priority?: boolean | null;
						manually_added_artworks?: Array<{
							__typename?: 'Manually_Added_Artwork';
							id: string;
							status?: {
								__typename?: 'Artwork_Status_Type';
								key: string;
							} | null;
						} | null> | null;
				  }
				| {
						__typename?: 'PDF';
						high_priority?: boolean | null;
						artworks?: Array<{
							__typename?: 'PDF_Artwork';
							id: string;
							status?: {
								__typename?: 'Artwork_Status_Type';
								key: string;
							} | null;
						} | null> | null;
				  }
				| null;
		} | null> | null;
		visit?: {
			__typename?: 'Visit';
			id: string;
			artworks?: Array<{
				__typename?: 'Visit_Artwork';
				id: string;
				status?: { __typename?: 'Artwork_Status_Type'; key: string } | null;
			} | null> | null;
		} | null;
	}>;
};

export const GetExhibitionsTableDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getExhibitionsTable' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Ingestion_Exhibition_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Ingestion_Exhibition_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Ingestion_Exhibition' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'event_review_status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_exhibition' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'processed_exhibition_id',
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisers' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'item' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Manual_Upload' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'manually_added_artworks',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'status',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'PDF' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'status',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'InlineFragment',
															typeCondition: {
																kind: 'NamedType',
																name: { kind: 'Name', value: 'Artlogic_Link' },
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'high_priority',
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworks' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetExhibitionsTableQuery,
	GetExhibitionsTableQueryVariables
>;
