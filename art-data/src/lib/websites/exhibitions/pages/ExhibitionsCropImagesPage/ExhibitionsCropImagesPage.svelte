<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ImageCroppingPage } from '$lib/features/crop-images/ImageCroppingPage';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { ExhibitionsIdImagesCropPageData } from '$routes/exhibitions/[id]/images/crop/types';

	let data = $derived(getPageData<ExhibitionsIdImagesCropPageData>(page.data));
	let exhibition = $derived(data.exhibition);
	let visitImages = $derived(data.visitImages);
	let photographer = $derived(data.photographer);
	let date = $derived(data.date);

	const dataCyPrefix = 'exhibitions';

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${exhibition.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Image Cropping' },
	]);

	let buttonProps = $derived({
		label: 'back to exhibition page',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	});
</script>

<ImageCroppingPage
	{visitImages}
	{photographer}
	title={`${exhibition.title}`}
	{crumbs}
	dataCy={dataCyPrefix}
	{buttonProps}
/>
