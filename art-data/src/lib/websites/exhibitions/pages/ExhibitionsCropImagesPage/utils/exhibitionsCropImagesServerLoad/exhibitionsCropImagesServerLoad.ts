import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { cropImagesServerLoad } from '$lib/features/crop-images/ImageCroppingPage/utils/cropImagesServerLoad/cropImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdImagesCropPageServerLoadEvent } from '$routes/exhibitions/[id]/images/crop/types';

export const exhibitionsCropImagesServerLoad = async ({
	parent,
	params,
	url,
}: ExhibitionsIdImagesCropPageServerLoadEvent) => {
	const data = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const visitId = exhibition?.visit?.id;

	if (!visitId) {
		error(404, 'There is no visit associated to this exhibition');
	}

	const { visitImages, photographer } = await cropImagesServerLoad(
		{ visitId, date: dateParam, photographer: photographerParam },
		data
	);

	return {
		exhibition,
		visitImages,
		photographer,
		date: dateParam,
	};
};
