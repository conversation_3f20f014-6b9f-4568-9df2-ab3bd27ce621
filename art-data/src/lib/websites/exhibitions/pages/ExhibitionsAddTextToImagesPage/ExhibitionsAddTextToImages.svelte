<script lang="ts">
	import { ExhibitionsDetails } from '../../components/ExhibitionsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AddTextToImages } from '$lib/features/add-text-to-images/components/AddTextToImages';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import type { ExhibitionsIdManualUploadManualUploadIdAddDataPageData } from '$routes/exhibitions/[id]/manual-upload/[manualUploadId]/add-data/types';

	const dataCyPrefix = 'exhibitions-add-text-to-images';

	let data = $derived(
		getPageData<ExhibitionsIdManualUploadManualUploadIdAddDataPageData>(
			page.data
		)
	);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);
	let artist = $derived(data.exhibitionArtist);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Add Text To Images' },
	]);

	const buttonProps = {
		label: 'Back to Exhibition Page',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	};

	const button2Props = {
		label: 'back to Exhibitions',
		href: `${Routes.ExhibitionsHome}`,
	};
</script>

<ArtworkTextTableContext>
	<PageBody>
		<AddTextToImages
			detailsString="Exhibition details"
			title={`${data?.exhibition.title}`}
			{crumbs}
			slotLabel="Exhibition the information is going to be added to"
			dataCy={dataCyPrefix}
			{data}
			{buttonProps}
			{button2Props}
			{artworkSaleStatuses}
			{artist}
		>
			{#if data.exhibition.processed_exhibition}
				<ExhibitionsDetails
					dataCy={dataCyPrefix}
					exhibition={{
						title: `${data.exhibition?.title}`,
						id: data.exhibition?.processed_exhibition?.processed_exhibition_id,
						organisers: [
							{
								entity_id: {
									name: `${data.exhibition?.processed_exhibition?.organisers}`,
								},
							},
						],
						venue_city: {
							name: `${data.exhibition?.processed_exhibition?.location}`,
							code: `${data.exhibition?.processed_exhibition?.location}`,
						},
						local_start_date: data.exhibition?.processed_exhibition?.start_date,
						local_end_date: data.exhibition?.processed_exhibition?.end_date,
					}}
				/>
			{/if}
		</AddTextToImages>
	</PageBody>
</ArtworkTextTableContext>
