import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { addTextToImagesServerLoad } from '$lib/features/add-text-to-images/utils/addTextToImagesServerLoad/addTextToImagesServerLoad';
import type { GetArteyeArtistsQuery } from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
import { GetArteyeArtistsDocument } from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetExhibitionsWithChecklistsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsWithChecklists.generated';
import type { ExhibitionsIdManualUploadManualUploadIdAddDataPageServerLoadEvent } from '$routes/exhibitions/[id]/manual-upload/[manualUploadId]/add-data/types';

export const exhibitionsAddTextToImagesServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdManualUploadManualUploadIdAddDataPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];

	if (!exhibition) {
		error(404, 'Not found');
	}

	const addTextToImagesServerLoadResponse = await addTextToImagesServerLoad(
		params.manualUploadId,
		data
	);

	let exhibitionArtist: null | GetArteyeArtistsQuery['artist'][number] = null;

	const exhibitionArtistId = exhibition?.processed_exhibition?.artists?.[0]?.id;

	if (exhibitionArtistId) {
		const exhibitionArtistRes = await gqlClientArteye.request(
			GetArteyeArtistsDocument,
			{ filter: { id: { _eq: exhibitionArtistId } } },
			getAuthorizationHeaders({
				user: { access_token: data.user?.arteye_token },
			})
		);

		exhibitionArtist = exhibitionArtistRes?.artist?.[0];
	}

	return {
		...data,
		...addTextToImagesServerLoadResponse,
		exhibition,
		exhibitionArtist,
	};
};
