import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdImagesMatchArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/images/match-artworks/types';

export const exhibitionsMatchArtworksWithLabelsServerLoad = async ({
	parent,
	params,
	url,
}: ExhibitionsIdImagesMatchArtworksPageServerLoadEvent) => {
	const data = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const visitId = exhibition?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographerParam } } },
		getAuthorizationHeaders(data)
	);

	const photographer = usersResponse.users?.[0];

	return {
		exhibition,
		visitId,
		dateParam,
		photographer,
	};
};
