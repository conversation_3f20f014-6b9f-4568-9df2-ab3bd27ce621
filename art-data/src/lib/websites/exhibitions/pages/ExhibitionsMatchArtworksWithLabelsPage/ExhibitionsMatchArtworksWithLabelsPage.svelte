<script lang="ts">
	import classNames from 'classnames';
	import { ExhibitionsDetails } from '../../components/ExhibitionsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { MatchArtworksWithLabels } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels';
	import { MatchArtworksWithLabelsTableContext } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels/MatchArtworksWithLabelsTableContext';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { ExhibitionsIdImagesMatchArtworksPageData } from '$routes/exhibitions/[id]/images/match-artworks/types';

	const dataCy = 'exhibitions-match-artworks-with-labels';

	let submittingDiscardedImages = $state(false);
	let fetching = $state(false);
	let submitting = $state(false);

	let data = $derived(
		getPageData<ExhibitionsIdImagesMatchArtworksPageData>(page.data)
	);
	let visitId = $derived(data.visitId);
	let date = $derived(data.dateParam);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Match artworks with labels' },
	]);
</script>

<MatchArtworksWithLabelsTableContext>
	<PageBody
		class={classNames({
			'pointer-events-none':
				submitting || submittingDiscardedImages || fetching,
		})}
	>
		<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
		<MatchArtworksWithLabels
			bind:submittingDiscardedImages
			bind:fetching
			bind:submitting
			slotLabel="Exhibition details"
			title={`${data?.exhibition?.processed_exhibition?.title}`}
			{visitId}
			{dataCy}
			{date}
			photographerId={photographer.id}
		>
			{#if data.exhibition.processed_exhibition}
				<ExhibitionsDetails
					{dataCy}
					exhibition={{
						title: `${data.exhibition?.title}`,
						id: data.exhibition?.processed_exhibition?.processed_exhibition_id,
						organisers: [
							{
								entity_id: {
									name: `${data.exhibition?.processed_exhibition?.organisers}`,
								},
							},
						],
						venue_city: {
							name: `${data.exhibition?.processed_exhibition?.location}`,
							code: `${data.exhibition?.processed_exhibition?.location}`,
						},
						local_start_date: data.exhibition?.processed_exhibition?.start_date,
						local_end_date: data.exhibition?.processed_exhibition?.end_date,
					}}
				/>
			{/if}
		</MatchArtworksWithLabels>
	</PageBody>
</MatchArtworksWithLabelsTableContext>
