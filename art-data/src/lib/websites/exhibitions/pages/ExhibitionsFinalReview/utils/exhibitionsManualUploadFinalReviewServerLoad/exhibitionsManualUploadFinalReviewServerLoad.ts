import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { manualUploadfinalReviewServerLoad } from '$lib/features/final-review/utils/manualUploadFinalReviewServerLoad/manualUploadFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsTableDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
import type { ExhibitionsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/manual-upload/[manualUploadId]/review-artworks/types';

export const exhibitionsManualUploadFinalReviewServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionExhibitionResponse = await gqlClient.request(
		GetExhibitionsTableDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const name =
		ingestionExhibitionResponse?.Ingestion_Exhibition?.[0]?.processed_exhibition
			?.title;

	const finalReviewResponse = await manualUploadfinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
