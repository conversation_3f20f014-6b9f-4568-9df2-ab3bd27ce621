import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { pdfFinalReviewServerLoad } from '$lib/features/final-review/utils/pdfFinalReviewServerLoad/pdfFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsTableDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
import type { ExhibitionsIdPdfsPdfIdReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/pdfs/[pdfId]/review-artworks/types';

export const exhibitionsPdfFinalReviewServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPdfsPdfIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionExhibitionResponse = await gqlClient.request(
		GetExhibitionsTableDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const processedExhibition =
		ingestionExhibitionResponse?.Ingestion_Exhibition?.[0]
			?.processed_exhibition;

	const name = processedExhibition?.title;

	const finalReviewResponse = await pdfFinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
