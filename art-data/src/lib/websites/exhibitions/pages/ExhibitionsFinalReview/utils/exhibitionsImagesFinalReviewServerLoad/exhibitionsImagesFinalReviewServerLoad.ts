import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { imagesFinalReviewServerLoad } from '$lib/features/final-review/utils/imagesFinalReviewServerLoad/imagesFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsTableDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
import type { ExhibitionsIdImagesReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/images/review-artworks/types';

export const exhibitionsImagesFinalReviewServerLoad = async ({
	parent,
	params,
	url,
}: ExhibitionsIdImagesReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const ingestionExhibitionResponse = await gqlClient.request(
		GetExhibitionsTableDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const exhibition = ingestionExhibitionResponse?.Ingestion_Exhibition?.[0];

	const name = exhibition?.processed_exhibition?.title;
	const visitId = exhibition?.visit?.id;

	if (!visitId) {
		error(404, 'No visit attached to this exhibition');
	}

	const finalReviewResponse = await imagesFinalReviewServerLoad({
		parent,
		date: dateParam,
		photographerId: photographerParam,
		visitId,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
