<script lang="ts">
	import { ExhibitionsDetails } from '../../components/ExhibitionsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { UploadList } from '$lib/features/upload-list/components/UploadList';

	import {
		type UploadListArtlogicLink,
		type UploadListManualUpload,
		type UploadListPdf,
		IngestionDataTypenames,
	} from '$lib/types';

	import { extractItemsFromIngestionData } from '$lib/utils/extractItemsFromIngestionData/extractItemsFromIngestionData';
	import type { ExhibitionsIdPageData } from '$routes/exhibitions/[id]/types';

	let data = $derived(getPageData<ExhibitionsIdPageData>(page.data));
	let exhibition = $derived(data.exhibition);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{ label: `${data?.exhibition?.processed_exhibition?.title}` },
	]);

	let pdfs = $derived(
		extractItemsFromIngestionData(
			data.exhibition.ingestion_data,
			data.exhibition.ready_for_review,
			data.exhibition.reviewed_and_submitted,
			data.exhibition.matches,
			data.exhibition.skipped,
			IngestionDataTypenames.Pdf
		) as UploadListPdf[]
	);

	let artlogicLinks = $derived(
		extractItemsFromIngestionData(
			data.exhibition.ingestion_data,
			data.exhibition.ready_for_review,
			data.exhibition.reviewed_and_submitted,
			data.exhibition.matches,
			data.exhibition.skipped,
			IngestionDataTypenames.ArtlogicLink
		) as UploadListArtlogicLink[]
	);

	let manualUploads = $derived(
		extractItemsFromIngestionData(
			data.exhibition.ingestion_data,
			data.exhibition.ready_for_review,
			data.exhibition.reviewed_and_submitted,
			data.exhibition.matches,
			data.exhibition.skipped,
			IngestionDataTypenames.ManualUpload
		) as UploadListManualUpload[]
	);
</script>

<UploadList
	{crumbs}
	{pdfs}
	visitArtworks={data.visitArtworks}
	visit={exhibition.visit}
	{manualUploads}
	{artlogicLinks}
	event="exhibition"
	dataCy="exhibitions"
	subtitle="Exhibition details"
	title="Exhibition Upload List"
	firstTabTitle="Exhibition checklists"
	collectionName="Ingestion_Exhibition"
>
	{#if exhibition?.processed_exhibition}
		<ExhibitionsDetails
			dataCy="exhibitions-upload-list"
			exhibition={{
				title: `${exhibition?.title}`,
				id: `${data.exhibition?.processed_exhibition?.processed_exhibition_id}`,
				organisers: [
					{
						entity_id: {
							name: `${exhibition?.processed_exhibition?.organisers}`,
						},
					},
				],
				venue_city: {
					name: `${exhibition?.processed_exhibition?.location}`,
					code: `${exhibition?.processed_exhibition?.location}`,
				},
				local_start_date: exhibition?.processed_exhibition?.start_date,
				local_end_date: exhibition?.processed_exhibition?.end_date,
			}}
		/>
	{/if}
</UploadList>
