import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetLegacyIdDocument } from '$lib/custom-arteye-queries/__generated__/getLegacyId.generated';
import { getAddPdfSuperform } from '$lib/features/add-pdf/utils/getAddPdfSuperform/getAddPdfSuperform';
import { GetArteyeArtistsDocument } from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdPageServerLoadEvent } from '$routes/exhibitions/[id]/types';

export const exhibitionsAddPdfServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const forms = await getAddPdfSuperform();

	const artist = exhibition?.processed_exhibition?.artists?.[0];

	// TODO art-data: legacy and reference ids should ideally be in processed artist collections
	if (artist) {
		const [referenceIdRes, legacyIdRes] = await Promise.all([
			gqlClientArteye.request(
				GetArteyeArtistsDocument,
				{ filter: { id: { _eq: artist.id } } },
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			),
			!isOnDev()
				? gqlClientCustomArteye.request(
						GetLegacyIdDocument,
						{ id: artist.id, collection: 'artist' },
						getAuthorizationHeaders({
							user: { access_token: data.user?.arteye_token },
						})
					)
				: { getLegacyId: { legacyId: null } },
		]);

		if (exhibition.processed_exhibition?.artists) {
			exhibition.processed_exhibition.artists[0] = {
				...artist,
				reference_id: referenceIdRes?.artist?.[0]?.reference_id,
				legacy_id: legacyIdRes?.getLegacyId?.legacyId,
			};
		}
	}

	return {
		...data,
		...forms,
		exhibition,
	};
};
