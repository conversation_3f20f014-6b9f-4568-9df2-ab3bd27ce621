<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddPdf } from '$lib/features/add-pdf/components/AddPdf';
	import type { ExhibitionsIdPdfsAddPageData } from '$routes/exhibitions/[id]/pdfs/add/types';

	let data = $derived(getPageData<ExhibitionsIdPdfsAddPageData>(page.data));
	let exhibition = $derived(data.exhibition);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'Add PDF' },
	]);
</script>

<AddPdf
	key="exhibitions"
	collection="Ingestion_Exhibition"
	{crumbs}
	artist={exhibition?.processed_exhibition?.artists?.length === 1
		? exhibition?.processed_exhibition?.artists[0]
		: null}
	title={exhibition.title}
	buttonProps={{
		label: 'Back to exhibition page',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to exhibitions list',
		href: Routes.ExhibitionsHome,
	}}
/>
