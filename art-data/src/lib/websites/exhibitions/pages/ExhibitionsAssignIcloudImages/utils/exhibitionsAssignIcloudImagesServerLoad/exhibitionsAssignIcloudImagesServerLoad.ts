import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsWithChecklistsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsWithChecklists.generated';
import type { ExhibitionsIdImagesAddIcloudAssignPageServerLoadEvent } from '$routes/exhibitions/[id]/images/add-icloud/assign/types';

export const exhibitionsAssignIcloudImagesServerLoad = async ({
	params,
	parent,
}: ExhibitionsIdImagesAddIcloudAssignPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];

	if (!exhibition?.visit) {
		error(404, 'Not found');
	}

	return { exhibition };
};
