<script lang="ts">
	import { ExhibitionsDetails } from '../../components/ExhibitionsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AssignIcloudImages } from '$lib/features/add-more-images/components/AssignIcloudImages';
	import type { ExhibitionsIdImagesAddIcloudAssignPageData } from '$routes/exhibitions/[id]/images/add-icloud/assign/types';

	let pageNumber = $state(1);

	let data = $derived(
		getPageData<ExhibitionsIdImagesAddIcloudAssignPageData>(page.data)
	);
	let exhibition = $derived(data.exhibition);
	let images = $derived(data.images);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'Assign Icloud Images' },
	]);
</script>

<PageBody>
	<Breadcrumbs
		dataCy={'assign-icloud-images'}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<AssignIcloudImages
		visitId={exhibition?.visit?.id}
		{images}
		title={`${exhibition?.processed_exhibition?.title}`}
		bind:pageNumber
		dataCy="exhibitions"
	>
		{#if data.exhibition?.processed_exhibition}
			<ExhibitionsDetails
				dataCy={'assign-icloud-images'}
				exhibition={{
					title: `${data.exhibition?.title}`,
					id: data.exhibition?.processed_exhibition?.processed_exhibition_id,
					organisers: [
						{
							entity_id: {
								name: `${data.exhibition?.processed_exhibition?.organisers}`,
							},
						},
					],
					venue_city: {
						name: `${data.exhibition?.processed_exhibition?.location}`,
						code: `${data.exhibition?.processed_exhibition?.location}`,
					},
					local_start_date: data.exhibition?.processed_exhibition?.start_date,
					local_end_date: data.exhibition?.processed_exhibition?.end_date,
				}}
			/>
		{/if}
	</AssignIcloudImages>
</PageBody>
