import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAddManualUploadSuperform } from '$lib/features/add-manual-upload/utils/getAddManualUploadSuperform/getAddManualUploadSuperform';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdManualUploadAddPageServerLoadEvent } from '$routes/exhibitions/[id]/manual-upload/add/types';

export const exhibitionsAddManualUploadServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdManualUploadAddPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{
			filter: { id: { _eq: params.id } },
		},
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const forms = await getAddManualUploadSuperform();
	const dropzoneUrlForms = await getDropzoneUrlDialogSuperform();

	return {
		...data,
		...forms,
		...dropzoneUrlForms,
		exhibition,
	};
};
