<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddManualUpload } from '$lib/features/add-manual-upload/components/AddManualUpload';
	import type { ExhibitionsIdManualUploadAddPageData } from '$routes/exhibitions/[id]/manual-upload/add/types';

	let data = $derived(
		getPageData<ExhibitionsIdManualUploadAddPageData>(page.data)
	);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${data.exhibition.id}`,
		},
		{ label: 'Add Manual Upload' },
	]);
</script>

<AddManualUpload
	{crumbs}
	backButtonHref={`${Routes.ExhibitionsHome}/${page.params.id}`}
	title={`${data.exhibition.title}`}
	key="exhibitions"
	collection="Ingestion_Exhibition"
	buttonProps={{
		label: 'Back to exhibition page',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to exhibitions list',
		href: Routes.ExhibitionsHome,
	}}
/>
