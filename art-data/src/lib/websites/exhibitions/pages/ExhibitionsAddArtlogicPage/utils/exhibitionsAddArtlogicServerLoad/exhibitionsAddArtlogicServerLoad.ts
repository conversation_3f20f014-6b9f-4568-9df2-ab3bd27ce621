import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetLegacyIdDocument } from '$lib/custom-arteye-queries/__generated__/getLegacyId.generated';
import type { ArtlogicGallery } from '$lib/features/add-artlogic/types';
import { getAddArtlogicSuperform } from '$lib/features/add-artlogic/utils/getAddArtlogicSuperform/getAddArtlogicSuperform';
import { GetArteyeArtistsDocument } from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import { GetArteyeExhibitionsDocument } from '$lib/websites/exhibitions/arteye-queries/__generated__/getArteyeExhibitions.generated';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdPageServerLoadEvent } from '$routes/exhibitions/[id]/types';

export const exhibitionsAddArtlogicServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const forms = await getAddArtlogicSuperform();

	let gallery: null | ArtlogicGallery = null;

	if (exhibition.processed_exhibition?.processed_exhibition_id) {
		const exhibitionRes = await gqlClientArteye.request(
			GetArteyeExhibitionsDocument,
			{
				filter: {
					id: { _eq: exhibition.processed_exhibition?.processed_exhibition_id },
				},
			},
			getAuthorizationHeaders({
				user: { access_token: data.user?.arteye_token },
			})
		);

		const organiserGallery = (
			exhibitionRes?.exhibition?.[0]?.organisers || []
		)?.find((organiser) => !!organiser?.entity_id?.gallery?.id);

		if (organiserGallery) {
			gallery = {
				id: organiserGallery.entity_id?.gallery?.id,
				organisation: {
					entity: {
						name: organiserGallery.entity_id?.name,
					},
					location: organiserGallery.entity_id?.organisation
						?.location as ArtlogicGallery['organisation']['location'],
				},
			};
		}
	}

	const artist = exhibition?.processed_exhibition?.artists?.[0];

	// TODO art-data: legacy and reference ids should ideally be in processed artist collections
	if (artist) {
		const [referenceIdRes, legacyIdRes] = await Promise.all([
			gqlClientArteye.request(
				GetArteyeArtistsDocument,
				{ filter: { id: { _eq: artist.id } } },
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			),
			!isOnDev()
				? gqlClientCustomArteye.request(
						GetLegacyIdDocument,
						{ id: artist.id, collection: 'artist' },
						getAuthorizationHeaders({
							user: { access_token: data.user?.arteye_token },
						})
					)
				: { getLegacyId: { legacyId: null } },
		]);

		if (exhibition.processed_exhibition?.artists) {
			exhibition.processed_exhibition.artists[0] = {
				...artist,
				reference_id: referenceIdRes?.artist?.[0]?.reference_id,
				legacy_id: legacyIdRes?.getLegacyId?.legacyId,
			};
		}
	}

	return {
		...data,
		...forms,
		exhibition,
		gallery,
	};
};
