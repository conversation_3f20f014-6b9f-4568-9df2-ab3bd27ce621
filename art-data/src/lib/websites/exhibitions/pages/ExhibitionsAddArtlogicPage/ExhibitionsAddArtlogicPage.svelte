<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddArtlogic } from '$lib/features/add-artlogic/components/AddArtlogic';
	import type { ExhibitionsIdArtlogicAddPageData } from '$routes/exhibitions/[id]/artlogic/add/types';

	let data = $derived(getPageData<ExhibitionsIdArtlogicAddPageData>(page.data));

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'Add Artlogic link' },
	]);
</script>

<AddArtlogic
	{crumbs}
	gallery={data?.gallery}
	artist={data?.exhibition?.processed_exhibition?.artists?.length === 1
		? data?.exhibition?.processed_exhibition?.artists[0]
		: null}
	title={`${data?.exhibition?.title}`}
	key="exhibitions"
	collection="Ingestion_Exhibition"
	buttonProps={{
		label: 'Back to exhibition page',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to exhibitions list',
		href: Routes.ExhibitionsHome,
	}}
/>
