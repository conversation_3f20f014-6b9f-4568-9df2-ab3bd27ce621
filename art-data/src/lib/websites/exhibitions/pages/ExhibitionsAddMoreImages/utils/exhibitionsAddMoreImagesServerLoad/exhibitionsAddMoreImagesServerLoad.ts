import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetExhibitionsWithChecklistsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsWithChecklists.generated';
import type { ExhibitionsIdImagesAddPageServerLoadEvent } from '$routes/exhibitions/[id]/images/add/types';

export const exhibitionsAddMoreImagesServerLoad = async ({
	params,
	parent,
}: ExhibitionsIdImagesAddPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];

	if (!exhibition?.visit) {
		error(404, 'Not found');
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{},
		getAuthorizationHeaders(data)
	);

	const users = usersResponse.users;

	if (!users) {
		error(404, 'Not found');
	}

	return { exhibition, users };
};
