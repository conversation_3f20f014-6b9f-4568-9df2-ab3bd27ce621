<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddMoreImages } from '$lib/features/add-more-images/components/AddMoreImages';
	import type { ExhibitionsIdImagesAddPageData } from '$routes/exhibitions/[id]/images/add/types';

	let data = $derived(getPageData<ExhibitionsIdImagesAddPageData>(page.data));
	let exhibition = $derived(data.exhibition);
	let users = $derived(data.users);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'Add More Images' },
	]);
</script>

<AddMoreImages
	dataCy="fairs"
	{users}
	visitId={`${exhibition?.visit?.id}`}
	title={`${exhibition?.title}`}
	{crumbs}
	backButtonLabel="Back to exhibition page"
/>
