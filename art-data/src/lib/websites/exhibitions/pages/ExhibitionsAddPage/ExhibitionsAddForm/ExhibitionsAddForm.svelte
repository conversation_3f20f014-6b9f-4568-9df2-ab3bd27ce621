<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import dayjs from 'dayjs';
	import customParseFormat from 'dayjs/plugin/customParseFormat';
	import { writable } from 'svelte/store';
	import { CannotFindExhibition } from './CannotFindExhibition';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepDescription } from '$global/features/form/components/StepDescription';
	import { StepError } from '$global/features/form/components/StepError';
	import { StepTitle } from '$global/features/form/components/StepTitle';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { RemovableSelectedLinkOption } from '$lib/components/RemovableSelectedLinkOption';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { VARIOUS_ARTISTS_ID } from '$lib/constants/various_artists';
	import {
		GetArteyeArtistsDocument,
		type GetArteyeArtistsQuery,
	} from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { getMutation } from '$lib/query-utils/getMutation';
	import {
		GetArteyeExhibitionsDocument,
		type GetArteyeExhibitionsQuery,
		type GetArteyeExhibitionsQueryVariables,
	} from '$lib/websites/exhibitions/arteye-queries/__generated__/getArteyeExhibitions.generated';
	import { CreateIngestionExhibitionDocument } from '$lib/websites/exhibitions/queries/__generated__/createIngestionExhibition.generated';
	import { CreateProcessedExhibitionDocument } from '$lib/websites/exhibitions/queries/__generated__/createProcessedExhibition.generated';
	import { GetExhibitionsTableDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
	import { GetProcessedExhibitionDocument } from '$lib/websites/exhibitions/queries/__generated__/getProcessedExhibition.generated';
	import { getExhibitionsDetails } from '$lib/websites/exhibitions/utils/getExhibitionsDetails/getExhibitionsDetails';
	import type { ExhibitionsAddPageData } from '$routes/exhibitions/add/types';

	dayjs.extend(customParseFormat);

	const dataCyPrefix = 'exhibitions-add';

	let submitting = $state(false);
	let exhibitionExistsError = $state(false);
	let value = $state(writable(''));
	let selectedOption: OptionType | null = $state(null);

	const getVariables = (value: string): GetArteyeExhibitionsQueryVariables => {
		return {
			limit: 10,
			filter: {
				_and: [
					{
						_or: [
							...(value ? [{ title: { _icontains: value } }] : []),
							...(value && isUuidValid(value) ? [{ id: { _eq: value } }] : []),
							...(value
								? [
										{
											organisers: {
												entity_id: {
													name: {
														_icontains: value,
													},
												},
											},
										},
									]
								: []),
						],
					},
					{ status: { key: { _neq: 'archived' } } },
				],
			},
		};
	};

	const createProcessedExhibition = createMutation(
		getMutation(
			CreateProcessedExhibitionDocument,
			getAuthorizationHeaders(page.data as ExhibitionsAddPageData)
		)
	);

	const createIngestionExhibition = createMutation(
		getMutation(
			CreateIngestionExhibitionDocument,
			getAuthorizationHeaders(page.data as ExhibitionsAddPageData)
		)
	);

	const getOptions = (data: GetArteyeExhibitionsQuery | undefined) => {
		return (data?.exhibition || [])?.map(getExhibitionsDetails);
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		const urlParts = selectedOption?.line2?.split('/') || [];
		const id = urlParts?.[urlParts?.length - 1];

		submitting = true;
		exhibitionExistsError = false;

		const getIngestionExhibitionResponse = await gqlClient.request(
			GetExhibitionsTableDocument,
			{
				filter: {
					processed_exhibition: {
						processed_exhibition_id: { _eq: id },
					},
				},
			},
			getAuthorizationHeaders(page.data as ExhibitionsAddPageData)
		);

		if (getIngestionExhibitionResponse?.Ingestion_Exhibition?.[0]) {
			submitting = false;
			exhibitionExistsError = true;
			return;
		}

		let processedExhibition;

		const getProcessedExhibitionResponse = await gqlClient.request(
			GetProcessedExhibitionDocument,
			{
				filter: { processed_exhibition_id: { _eq: id } },
			},
			getAuthorizationHeaders(page.data as ExhibitionsAddPageData)
		);

		processedExhibition =
			getProcessedExhibitionResponse?.Processed_Exhibition?.[0];

		let artistIds: string[] = [];
		let artists: GetArteyeArtistsQuery['artist'] = [];

		if (!processedExhibition) {
			if (selectedOption?.line1Suffix) {
				artistIds = selectedOption?.line1Suffix?.split(', ');

				const artistPromises = artistIds.map((artistId) =>
					gqlClientArteye.request(
						GetArteyeArtistsDocument,
						{
							filter: {
								_and: [
									{ id: { _eq: artistId } },
									{ status: { key: { _neq: 'archived' } } },
								],
							},
						},
						getAuthorizationHeaders({
							user: { access_token: page.data.user.arteye_token },
						})
					)
				);

				const artistRes = await Promise.all(artistPromises);
				artists = artistRes?.map((artist) => artist?.artist?.[0]);
			}

			const createProcessedExhibitionResponse =
				await $createProcessedExhibition.mutateAsync({
					data: {
						processed_exhibition_id: id,
						title: `${selectedOption?.line1}`,
						...(selectedOption?.line7 && {
							start_date: selectedOption?.line7,
						}),
						...(selectedOption?.line8 && {
							end_date: selectedOption?.line8,
						}),
						organisers: selectedOption?.line3,
						location: selectedOption?.line4,
						artists: artists?.map((arteyeArtist) => ({
							id: arteyeArtist?.id,
							first_name: arteyeArtist?.person?.first_name,
							last_name: arteyeArtist?.person?.last_name,
							...(arteyeArtist?.person?.year_birth && {
								year_birth: arteyeArtist?.person?.year_birth,
							}),
							...(arteyeArtist?.person?.year_death && {
								year_death: arteyeArtist?.person?.year_death,
							}),
							...(arteyeArtist?.person?.nationalities?.length && {
								nationality:
									arteyeArtist?.person?.nationalities?.[0]?.country?.code,
							}),
						})),
					},
				});

			processedExhibition =
				createProcessedExhibitionResponse?.create_Processed_Exhibition_item;
		}

		if (!processedExhibition) {
			submitting = false;
			return;
		}

		const response = await $createIngestionExhibition.mutateAsync({
			input: {
				processed_exhibition: {
					processed_exhibition_id: processedExhibition.processed_exhibition_id,
					title: processedExhibition.title,
				},
				title: `${selectedOption?.line1}`,
			},
		});

		submitting = false;
		(window as unknown as { location: string }).location =
			`${Routes.ExhibitionsHome}/${response?.create_Ingestion_Exhibition_item?.id}`;
	};
</script>

<div>
	<StepTitle>Add data about an exhibition</StepTitle>
	<StepDescription>
		Try searching for the exhibition below. If you cannot find the exhibition,
		click "create new" to add it on Arteye, then return here to select it in the
		search field below.
	</StepDescription>

	<StepContainer>
		<form onsubmit={handleSubmit} class="col-span-2 mb-[4rem] sm:mb-[7rem]">
			<QueryAutocomplete
				label="Search for exhibition"
				name="exhibition"
				dataCy={`${dataCyPrefix}-exhibitions`}
				placeholder="Start by typing to search by exhibition name, exhibition ID, or organiser"
				document={GetArteyeExhibitionsDocument}
				graphQlClient={gqlClientArteye}
				emptyValueResponse={{ exhibition: [] }}
				{getOptions}
				{getVariables}
				required
				requestHeaders={getAuthorizationHeaders({
					user: { access_token: page.data.user.arteye_token },
				})}
				classes={{
					input: 'text-[0.6875rem]',
					listWithOptions: 'pb-[3rem]',
					longList: '!max-h-[244px] !min-h-[244px]',
					option: {
						line1Suffix: 'hidden',
						line3: 'font-[500] line-clamp-2',
						line5: 'float-left inline',
						line6: 'float-left inline ml-1',
						line7: 'hidden',
						line8: 'hidden',
					},
					selectedOption: {
						line1Suffix: 'hidden',
						line3: 'font-[500] line-clamp-2',
						line5: 'float-left inline',
						line6: 'float-left inline ml-1',
						line7: 'hidden',
						line8: 'hidden',
					},
				}}
				OptionComponent={LinkOption}
				SelectedOptionComponent={RemovableSelectedLinkOption}
				{value}
				bind:selectedOption
			>
				{#snippet list()}
					<div
						class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
					>
						<CannotFindExhibition />
					</div>
				{/snippet}

				{#snippet noResults()}
					<div class="flex flex-col items-center">
						<NoResults
							class="mb-2"
							dataCy={`${dataCyPrefix}-exhibitions-autocomplete`}
							>No exhibitions found</NoResults
						>
						<CannotFindExhibition />
					</div>
				{/snippet}
			</QueryAutocomplete>

			<StepButtons
				backButtonProps={{
					href: Routes.ExhibitionsHome,
				}}
				continueButtonProps={{
					disabled: !selectedOption,
					loading: submitting,
				}}
			>
				Continue
				{#snippet error()}
					<span>
						{#if exhibitionExistsError}
							<StepError class="normal-case">
								This exhibition has already been added to an Ingestion
								Exhibition. Please search for it on the exhibitions home page.
							</StepError>
						{/if}
					</span>
				{/snippet}
			</StepButtons>
		</form>
	</StepContainer>
</div>
