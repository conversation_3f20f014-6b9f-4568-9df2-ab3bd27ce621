<script lang="ts">
	import { ExhibitionsAddForm } from './ExhibitionsAddForm';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'exhibitions-home';

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{ label: 'Add New Exhibition' },
	];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<ExhibitionsAddForm />
</PageBody>
