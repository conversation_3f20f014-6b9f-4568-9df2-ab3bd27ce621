<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { onMount } from 'svelte';
	import { EXHIBITIONS_LIMIT } from '../utils/exhibitionsHomePageLoad/exhibitionsHomePageLoad';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { getInfiniteData } from '$global/utils/infinite-loading/getInfiniteData';
	import { infiniteQuery } from '$global/utils/infinite-loading/infiniteQuery';
	import { Routes } from '$lib/constants/routes';
	import { getGqlClient } from '$lib/utils/getGqlClient/getGqlClient';
	import { hasEventPriorityItems } from '$lib/utils/hasEventPriorityItems/hasEventPriorityItems';
	import {
		GetExhibitionsTableDocument,
		type GetExhibitionsTableQuery,
	} from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
	import type { ExhibitionsHomePageData } from '$routes/exhibitions/types';

	interface Props {
		pageNumber: number;
		onClickPage: (e: Event | undefined, page?: number | null) => void;
	}

	let { pageNumber, onClickPage }: Props = $props();

	let isReady = false;

	let queryVariables = $derived(page.data.queryVariables);
	const gqlClient = getGqlClient(page.data as ExhibitionsHomePageData);

	onMount(() => {
		isReady = true;
	});

	let query = $derived(
		infiniteQuery({
			gqlClient,
			variables: queryVariables,
			document: GetExhibitionsTableDocument,
			limit: EXHIBITIONS_LIMIT,
		})
	);

	let exhibitions = $derived(
		getInfiniteData({
			query: $query,
			transform: (data) => {
				return data.Ingestion_Exhibition;
			},
		})
	);

	let count = $derived(
		$query.data?.pages[0]?.Ingestion_Exhibition_aggregated?.[0]?.countDistinct
			?.id || 0
	);

	const headers = [
		'Exhibition title',
		'Organisers',
		'Location',
		'Received date',
		'Start date',
		'End date',
		'Has Priority Items',
	];

	const formatExhibition = (
		exhibition: GetExhibitionsTableQuery['Ingestion_Exhibition'][number]
	) => {
		return [
			exhibition.processed_exhibition?.title,
			exhibition.processed_exhibition?.organisers || '',
			exhibition.processed_exhibition?.location || '',
			dayjs(exhibition.date_created).format('DD/MM/YYYY'),
			exhibition.processed_exhibition?.start_date
				? dayjs(exhibition.processed_exhibition?.start_date).format(
						'DD/MM/YYYY'
					)
				: '',
			exhibition.processed_exhibition?.end_date
				? dayjs(exhibition.processed_exhibition?.end_date).format('DD/MM/YYYY')
				: '',
			hasEventPriorityItems(exhibition.ingestion_data) ? 'Yes' : 'No',
		];
	};

	const dataCyPrefix = 'exhibitions-home-table';
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, '0rem', headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	{#if exhibitions}
		<TableBody dataCy={dataCyPrefix}>
			{#each exhibitions as exhibition, i}
				<TableRow
					class={classNames('cursor-pointer', {
						'bg-green-100': exhibition.event_review_status?.key === 'COMPLETED',
						'bg-red-100':
							exhibition.event_review_status?.key === 'REQUIRES_ATTENTION',
					})}
					index={i}
					dataCy={dataCyPrefix}
					onclick={() => goto(`${Routes.ExhibitionsHome}/${exhibition.id}`)}
				>
					{@const formattedExhibition = formatExhibition(exhibition)}
					{#each formattedExhibition as formattedExhibitionValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, '0rem', headers)}
							content={formattedExhibitionValue}
							classes={{
								text: classNames({
									'text-red-500':
										j === formattedExhibition.length - 2 &&
										formattedExhibitionValue === 'Yes',
								}),
							}}
						>
							{formattedExhibitionValue}
						</TableCell>
					{/each}
				</TableRow>
			{/each}

			{#if !exhibitions.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No exhibitions to display</TableNoResults
				>
			{/if}
		</TableBody>
	{/if}
</table>

{#if $query.isFetching}
	<div class="mt-3 flex justify-center">
		<CircularProgress dataCy={dataCyPrefix} />
	</div>
{:else}
	<div class="mt-2 flex justify-end">
		{#key pageNumber}
			{#key count}
				<Pagination
					onClick={onClickPage}
					dataCy={dataCyPrefix}
					currentPage={pageNumber}
					limit={EXHIBITIONS_LIMIT}
					total={count}
				/>
			{/key}
		{/key}
	</div>
{/if}
