import { type QueryClient } from '@tanstack/svelte-query';
import dayjs from 'dayjs';
import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { EventStatus } from '$lib/constants/event-status';
import { Priority } from '$lib/constants/priority';
import { getQuery } from '$lib/query-utils/getQuery';
import {
	GetExhibitionsTableDocument,
	type GetExhibitionsTableQueryVariables,
} from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsTable.generated';
import type { ExhibitionsHomePageLoadEvent } from '$routes/exhibitions/types';

export const EXHIBITIONS_LIMIT = 20;
export const EXHIBITIONS_STALE_TIME = 1000 * 60 * 60;

export const exhibitionsHomePageLoad = async ({
	parent,
	url,
}: ExhibitionsHomePageLoadEvent) => {
	const parentData = await parent();
	const queryParamsVariables = getInitialVariables(url.searchParams);
	const queryClient: QueryClient = parentData.queryClient;

	const queryVariables: GetExhibitionsTableQueryVariables = {
		sort: [queryParamsVariables.sort],
		limit: EXHIBITIONS_LIMIT,
		...(queryParamsVariables.page && {
			offset: EXHIBITIONS_LIMIT * (queryParamsVariables.page - 1),
		}),
		filter: {
			_and: [
				...(queryParamsVariables.status &&
				queryParamsVariables.status !== EventStatus.All
					? [
							{
								event_review_status: {
									key: { _eq: queryParamsVariables.status },
								},
							},
						]
					: []),
				...(queryParamsVariables.search
					? [
							{
								_or: [
									{ title: { _icontains: queryParamsVariables.search } },
									{
										processed_exhibition: {
											organisers: { _icontains: queryParamsVariables.search },
										},
									},
								],
							},
						]
					: []),
				...(queryParamsVariables.priority === Priority.High
					? [
							{
								_or: [
									{
										ingestion_data: {
											item__PDF: { high_priority: { _eq: true } },
										},
									},
									{
										ingestion_data: {
											item__Manual_Upload: { high_priority: { _eq: true } },
										},
									},
									{
										ingestion_data: {
											item__Artlogic_Link: { high_priority: { _eq: true } },
										},
									},
								],
							},
						]
					: []),
				...(() => {
					if (queryParamsVariables.from && queryParamsVariables.to) {
						return [
							{
								_and: [
									{
										date_created: {
											_gte: dayjs(queryParamsVariables.from).toISOString(),
										},
									},
									{
										date_created: {
											_lte: dayjs(queryParamsVariables.to)
												.endOf('day')
												.toISOString(),
										},
									},
								],
							},
						];
					}

					if (queryParamsVariables.from) {
						return [
							{
								date_created: {
									_gte: dayjs(queryParamsVariables.from).toISOString(),
								},
							},
						];
					}

					if (queryParamsVariables.to) {
						return [
							{
								date_created: {
									_lte: dayjs(queryParamsVariables.to)
										.endOf('day')
										.toISOString(),
								},
							},
						];
					}

					return [];
				})(),
			],
		},
	};

	queryClient.prefetchInfiniteQuery({
		...getQuery(
			GetExhibitionsTableDocument,
			queryVariables,
			getAuthorizationHeaders(parentData)
		),
		initialPageParam: 0,
		staleTime: EXHIBITIONS_STALE_TIME,
	});

	return {
		...parentData,
		queryVariables,
	};
};
