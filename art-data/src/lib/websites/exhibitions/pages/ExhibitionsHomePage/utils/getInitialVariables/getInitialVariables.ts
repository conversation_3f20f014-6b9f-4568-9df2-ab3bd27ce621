import { ExhibitionsFieldNames, EXHIBITIONS_SORT_OPTIONS } from '../../types';
import { EVENT_STATUS_OPTIONS } from '$lib/constants/event-status';
import { PRIORITY_OPTIONS } from '$lib/constants/priority';

export const getInitialVariables = (searchParams: URLSearchParams) => {
	const queryParams = Object.fromEntries(searchParams);

	return {
		[ExhibitionsFieldNames.Page]: +queryParams[ExhibitionsFieldNames.Page] || 1,
		[ExhibitionsFieldNames.Search]:
			queryParams[ExhibitionsFieldNames.Search] || '',
		[ExhibitionsFieldNames.Priority]:
			queryParams[ExhibitionsFieldNames.Priority] || PRIORITY_OPTIONS[0].value,
		[ExhibitionsFieldNames.Status]:
			queryParams[ExhibitionsFieldNames.Status] ||
			EVENT_STATUS_OPTIONS[0].value,
		[ExhibitionsFieldNames.Sort]:
			queryParams[ExhibitionsFieldNames.Sort] ||
			EXHIBITIONS_SORT_OPTIONS[1].value,
		[ExhibitionsFieldNames.From]: queryParams[ExhibitionsFieldNames.From],
		[ExhibitionsFieldNames.To]: queryParams[ExhibitionsFieldNames.To],
	};
};
