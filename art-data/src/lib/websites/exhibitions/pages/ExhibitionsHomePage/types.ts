export enum ExhibitionsFieldNames {
	Search = 'search',
	From = 'from',
	To = 'to',
	Priority = 'priority',
	Status = 'status',
	Sort = 'sort',
	Page = 'page',
}

export const EXHIBITIONS_SORT_OPTIONS = [
	{ label: 'DATE RECEIVED ASC', value: 'date_created' },
	{ label: 'DATE RECEIVED DESC', value: '-date_created' },
	{ label: 'START DATE ASC', value: 'processed_exhibition.start_date' },
	{ label: 'START DATE DESC', value: '-processed_exhibition.start_date' },
	{ label: 'END DATE ASC', value: 'processed_exhibition.end_date' },
	{ label: 'END DATE DESC', value: '-processed_exhibition.end_date' },
	{ label: 'DATE UPDATED ASC', value: 'date_updated' },
	{ label: 'DATE UPDATED DESC', value: '-date_updated' },
	{ label: 'TITLE ASC', value: 'title' },
	{ label: 'TITLE DESC', value: '-title' },
	{ label: 'ORGANISERS ASC', value: 'processed_exhibition.organisers' },
	{ label: 'ORGANISERS DESC', value: '-processed_exhibition.organisers' },
];
