<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddMoreIcloudImages } from '$lib/features/add-more-images/components/AddMoreIcloudImages';
	import type { ExhibitionsIdImagesAddIcloudPageData } from '$routes/exhibitions/[id]/images/add-icloud/types';

	let data = $derived(
		getPageData<ExhibitionsIdImagesAddIcloudPageData>(page.data)
	);
	let exhibition = $derived(data.exhibition);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'Add More Icloud Images' },
	]);
</script>

<AddMoreIcloudImages dataCy="exhibitions" {crumbs} />
