import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { editExtractedImagesServerLoad } from '$lib/features/edit-extracted-images/utils/editExtractedImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsWithChecklistsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitionsWithChecklists.generated';
import type { ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent } from '$routes/exhibitions/[id]/pdfs/[pdfId]/edit-extracted-images/types';

export const exhibitionsEditExtractedImagesServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];

	if (!exhibition) {
		error(404, 'Not found');
	}

	const editExtractedImagesResponse = await editExtractedImagesServerLoad({
		params,
		data,
	});

	return {
		...data,
		exhibition,
		...editExtractedImagesResponse,
	};
};
