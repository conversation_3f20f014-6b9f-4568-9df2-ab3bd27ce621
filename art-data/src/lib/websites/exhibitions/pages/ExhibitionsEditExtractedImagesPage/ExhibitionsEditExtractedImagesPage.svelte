<script lang="ts">
	import { ExhibitionsDetails } from '../../components/ExhibitionsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import { EditExtractedImages } from '$lib/features/edit-extracted-images/components/EditExtractedImages';
	import type { ExhibitionsIdPdfsPdfIdEditExtractedImagesPageData } from '$routes/exhibitions/[id]/pdfs/[pdfId]/edit-extracted-images/types';

	let data = $derived(
		getPageData<ExhibitionsIdPdfsPdfIdEditExtractedImagesPageData>(page.data)
	);
	let exhibition = $derived(data.exhibition);
	let pdfFile = $derived(data.file);
	let pdfAiExtractedText = $derived(data.pdfAiExtractedText);
	let pdfTitle = $derived(data.pdfTitle);
	let pdfArtworkFormatMethod = $derived(data.pdfArtworkFormatMethod);
	let pdfArtist = $derived(data.pdfArtist);
	let pdfArtistCountry = $derived(data.pdfArtistCountry);
	let artworks = $derived(data.artworks);
	let pages = $derived(data.pages);
	let discardedImages = $derived(data.discardedImages);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${exhibition.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Edit Extracted Images' },
	]);

	const dataCy = 'exhibitions';

	const buttonProps = {
		label: 'back to exhibition uploads',
		href: `${Routes.ExhibitionsHome}/${page.params.id}`,
	};
</script>

<ArtworkTextTableContext>
	<EditExtractedImages
		{pdfArtist}
		{pdfAiExtractedText}
		{pdfTitle}
		{pdfArtistCountry}
		{pdfArtworkFormatMethod}
		title={`${exhibition.title}`}
		{artworks}
		{pages}
		{discardedImages}
		pdfLink={pdfFile}
		slotLabel="Exhbition the information is going to be added to"
		{dataCy}
		{crumbs}
		{buttonProps}
		{artworkSaleStatuses}
	>
		{#if data.exhibition}
			<ExhibitionsDetails
				{dataCy}
				exhibition={{
					title: `${data.exhibition?.title}`,
					id: `${data.exhibition?.processed_exhibition?.processed_exhibition_id}`,
					organisers: [
						{
							entity_id: {
								name: `${data.exhibition?.processed_exhibition?.organisers}`,
							},
						},
					],
					venue_city: {
						name: `${data.exhibition?.processed_exhibition?.location}`,
						code: `${data.exhibition?.processed_exhibition?.location}`,
					},
					local_start_date: data.exhibition?.processed_exhibition?.start_date,
					local_end_date: data.exhibition?.processed_exhibition?.end_date,
				}}
			/>
		{/if}
	</EditExtractedImages>
</ArtworkTextTableContext>
