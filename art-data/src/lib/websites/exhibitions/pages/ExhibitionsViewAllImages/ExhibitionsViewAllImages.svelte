<script lang="ts">
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { ViewAllImages } from '$lib/features/view-all-images/components/ViewAllImages';
	import type { ExhibitionsIdImagesViewPageData } from '$routes/exhibitions/[id]/images/view/types';

	const dataCy = 'exhibitions-view-all-images';

	let data = $derived(getPageData<ExhibitionsIdImagesViewPageData>(page.data));
	let images = $derived(data.images);
	let users = $derived(data.users);
	let total = $derived(data.total);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${data?.exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{ label: 'View all images' },
	]);
</script>

<PageBody>
	<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
	<ViewAllImages
		title={`${data.exhibition.title}`}
		{dataCy}
		{images}
		{users}
		{total}
		backButtonLabel="Back to exhibition page"
		backButtonHref={`${Routes.ExhibitionsHome}/${page.params.id}`}
	/>
</PageBody>
