import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { viewAllImagesPageLoad } from '$lib/features/view-all-images/utils/viewAllImagesPageLoad/viewAllImagesPageLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';
import type { ExhibitionsIdImagesViewPageLoadEvent } from '$routes/exhibitions/[id]/images/view/types';

export const VISIT_IMAGES_LIMIT = 20;

export const exhibitionsViewAllImagesPageLoad = async ({
	parent,
	url,
	params,
}: ExhibitionsIdImagesViewPageLoadEvent) => {
	const data = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const visitId = exhibition?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const viewAllImagesResponse = await viewAllImagesPageLoad({
		url,
		data,
		visitId,
	});

	return {
		exhibition,
		...viewAllImagesResponse,
	};
};
