import dayjs from 'dayjs';
import type { GetArteyeExhibitionsQuery } from '../../arteye-queries/__generated__/getArteyeExhibitions.generated';
import { Config } from '$lib/constants/config';

export const getExhibitionsDetails = (
	exhibition: GetArteyeExhibitionsQuery['exhibition'][number]
) => {
	const exhibitionArtists = (exhibition?.artists || [])
		.map((artist) => artist?.artist_id?.id)
		.join(', ');

	return {
		line1: exhibition?.title,
		line1Suffix: exhibitionArtists,
		line2: `${Config.ArteyeDomain}/exhibitions/${exhibition?.id}`,
		line3: exhibition?.organisers
			?.map((organiser) => organiser?.entity_id?.name)
			?.join(', '),
		line4: `${
			exhibition?.venue_city?.name ? `${exhibition?.venue_city?.name} ` : ''
		}${
			exhibition?.venue_country?.name
				? `${exhibition?.venue_city?.name ? ' - ' : ''}${
						exhibition?.venue_country?.name
					}`
				: ''
		}`,
		line5: exhibition?.local_start_date
			? `${dayjs(exhibition?.local_start_date).format('DD/MM/YYYY')} `
			: '',
		line6: exhibition?.local_end_date
			? `${exhibition?.local_start_date ? ' - ' : ''}${dayjs(
					exhibition?.local_end_date
				).format('DD/MM/YYYY')}`
			: '',
		line7: exhibition?.local_start_date
			? `${dayjs(exhibition?.local_start_date).format('YYYY-MM-DDTHH:mm:ss')}`
			: '',
		line8: exhibition?.local_end_date
			? `${dayjs(exhibition?.local_end_date).format('YYYY-MM-DDTHH:mm:ss')}`
			: '',
	};
};
