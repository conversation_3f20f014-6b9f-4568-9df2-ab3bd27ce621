<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import type { GetArteyeExhibitionsQuery } from '../../arteye-queries/__generated__/getArteyeExhibitions.generated';
	import { getExhibitionsDetails } from '../../utils/getExhibitionsDetails/getExhibitionsDetails';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { type OptionClasses } from '$global/components/QueryAutocomplete/types';

	interface Props {
		exhibition: GetArteyeExhibitionsQuery['exhibition'][number];
		dataCy: string;
		classes?: OptionClasses;
		class?: string;
	}

	let {
		exhibition,
		dataCy,
		classes = {
			line1Suffix: 'hidden',
			line3: 'font-[500] line-clamp-2',
			line5: 'float-left inline',
			line6: 'float-left inline ml-1',
			line7: 'hidden',
			line8: 'hidden',
		},
		...rest
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-details`);
	let option = $derived(getExhibitionsDetails(exhibition));
</script>

<div
	data-cy={dataCyPrefix}
	class={twMerge(
		'flex flex-col gap-2 rounded border border-gray-200 p-3',
		rest.class
	)}
>
	<LinkOption dataCy={dataCyPrefix} {option} {classes} />
</div>
