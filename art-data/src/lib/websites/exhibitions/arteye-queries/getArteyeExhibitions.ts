import { gql } from 'graphql-tag';

export const GET_ARTEYE_EXHIBITIONS = gql`
	query getArteyeExhibitions(
		$filter: exhibition_filter
		$sort: [String]
		$limit: Int
	) {
		exhibition(filter: $filter, sort: $sort, limit: $limit) {
			id
			local_start_date
			local_end_date
			title
			venue_city {
				code
				name
			}
			venue_country {
				name
			}
			organisers {
				entity_id {
					name
					gallery {
						id
					}
					organisation {
						location {
							code
							name
						}
					}
				}
			}
			artists {
				artist_id {
					id
				}
			}
		}
	}
`;
