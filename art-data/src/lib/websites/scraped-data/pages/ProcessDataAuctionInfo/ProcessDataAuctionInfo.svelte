<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';
	import { LotAttributesKeys } from './constants/lot-attributes-keys';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Checkbox } from '$global/components/Checkbox';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Update_Artwork_Details_Input } from '$gql/types';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { UpdateArtworkDetailsDocument } from '$lib/features/final-review/queries/__generated__/updateArtworkDetails.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataActivityListingInfoPageData } from '$routes/scraped-data/process-data/activity-listing-info/types';

	const dialogStores = createDialog();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.DateCreatedAsc);
	let updatedIds: string[] = [];

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataActivityListingInfoPageData>(page.data)
	);

	let sortedScrapedArtworks: null | GetScrapedArtworksQuery['Scraped_Artwork'] =
		$state(null);

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);
	};

	const { scrapedArtworksStore, saveStage, showMatchEventsWarning } =
		getArtworkFeedsStore();

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			sortedScrapedArtworks = [...$scrapedArtworksStore].sort((a, b) =>
				sortFn(a, b, sort)
			);
		}
	});

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.LotNumberAsc:
					return {
						field: scrapedArtwork.artwork_details?.lot_number,
						type: 'string',
					};
				case ArtworkDetailsSort.LotNumberDesc:
					return {
						field: scrapedArtwork.artwork_details?.lot_number,
						type: 'string',
					};
				case ArtworkDetailsSort.GuaranteedAsc:
				case ArtworkDetailsSort.GuaranteedDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.GuaranteedSale
						),
						type: 'number',
					};
				case ArtworkDetailsSort.IbAsc:
				case ArtworkDetailsSort.IbDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.IrrevocableBid
						),
						type: 'number',
					};
				case ArtworkDetailsSort.OwnedByAuctionHouseAsc:
				case ArtworkDetailsSort.OwnedByAuctionHouseDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.OwnershipInterest
						),
						type: 'number',
					};
				case ArtworkDetailsSort.CatalogueHighlightAsc:
				case ArtworkDetailsSort.CatalogueHighlightDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.CatalogueHighlight
						),
						type: 'number',
					};
				case ArtworkDetailsSort.NoReserveAsc:
				case ArtworkDetailsSort.NoReserveDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.NoReserve
						),
						type: 'number',
					};
				case ArtworkDetailsSort.OnCoverAsc:
				case ArtworkDetailsSort.OnCoverDesc:
					return {
						field: +!!scrapedArtwork.artwork_details?.lot_attributes?.find(
							(attribute) =>
								attribute?.artwork_lot_symbol_lookup_key?.key ===
								LotAttributesKeys.OnCover
						),
						type: 'number',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(
				scrapedArtworkA as Parameters<typeof getTextField>[0]
			) as Parameters<typeof compareScrapedArtworkFields>[0],
			getTextField(
				scrapedArtworkB as Parameters<typeof getTextField>[0]
			) as Parameters<typeof compareScrapedArtworkFields>[1],
			sort
		);
	};

	onMount(() => {
		saveStage();
	});

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		try {
			if (updatedIds.length) {
				submitting = true;

				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(
						artworkDetails: NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details']
					) => ({
						...(artworkDetails?.lot_attributes && {
							lot_attributes: artworkDetails?.lot_attributes.map((item) => ({
								artwork_lot_symbol_lookup_key:
									item?.artwork_lot_symbol_lookup_key?.key,
							})),
						}),
					}),
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			submitting = false;

			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataEntities);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Lot number',
		'Guaranteed lot',
		'IB/Irrevocable bid',
		'Owned by auction house',
		'Catalogue highlight',
		'No reserve',
		'On cover',
	];

	const dataCyPrefix = 'scraped-data-process-auction-info';

	const handleChangeBoolean = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		bool: boolean | string,
		key: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			const currentArtwork = sortedScrapedArtworks[updatedArtworkIndex];
			const existingLotAttributes =
				currentArtwork?.artwork_details?.lot_attributes || [];

			const keyObjectIndex = existingLotAttributes.findIndex(
				(attr) => attr?.artwork_lot_symbol_lookup_key?.key === key
			);

			let updatedLotAttributes;

			if (bool) {
				if (keyObjectIndex === -1) {
					updatedLotAttributes = [
						...existingLotAttributes,
						{
							artwork_lot_symbol_lookup_key: { key },
						},
					];
				} else {
					updatedLotAttributes = existingLotAttributes;
				}
			} else {
				if (keyObjectIndex !== -1) {
					updatedLotAttributes = existingLotAttributes.filter(
						(_, index) => index !== keyObjectIndex
					);
				} else {
					updatedLotAttributes = existingLotAttributes;
				}
			}

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...currentArtwork,
				artwork_details: {
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					...currentArtwork.artwork_details,
					lot_attributes: updatedLotAttributes?.filter(
						(lotAttribute) => lotAttribute?.artwork_lot_symbol_lookup_key
					),
				},
			};
		}
	};
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={7} />

			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={twMerge(
									classNames({
										'w-[136px]': i === 0,
										// 'w-[170px]': i === 1,
									}),
									'border'
								)}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each sortedScrapedArtworks as scrapedArtwork, i}
							{@const isInProgress = !isScrapedArtworkProcessed(scrapedArtwork)}
							<TableRow index={i} dataCy={dataCyPrefix}>
								<TableCell dataCy={dataCyPrefix}>
									<a
										target="_blank"
										rel="noopener noreferrer"
										href={getImageUrl(
											scrapedArtwork?.images?.[0]?.directus_files_id?.id
										)}
										class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
									>
										<img
											alt=""
											class="max-h-full max-w-full"
											src={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
										/>
									</a>

									<LinkButton
										dataCy={dataCyPrefix}
										href={scrapedArtwork.artwork_feed?.url}
										disabled={!scrapedArtwork.artwork_feed?.url}
										newTab
										variant="secondary"
										size="sm"
										fullWidth
										>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
										{#snippet trailing()}
											{#if scrapedArtwork.artwork_feed?.url}
												<ExternalIcon />
											{/if}
										{/snippet}</LinkButton
									>
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="max-w-60 whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#if scrapedArtwork.artwork_details}
										<Txt variant="body3">
											{scrapedArtwork?.artwork_details?.lot_number || ''}
										</Txt>
									{/if}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-guaranteed-sale`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.GuaranteedSale
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.GuaranteedSale
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.GuaranteedSale
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-irrevocable-bid`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.IrrevocableBid
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.IrrevocableBid
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.IrrevocableBid
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-ownership-interest`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.OwnershipInterest
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.OwnershipInterest
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.OwnershipInterest
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-catalogue-highlight`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.CatalogueHighlight
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.CatalogueHighlight
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.CatalogueHighlight
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-no-reserve`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.NoReserve
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.NoReserve
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.NoReserve
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} class="px-2">
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-on-cover`}
													checked={scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.OnCover
													) || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															LotAttributesKeys.OnCover
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork?.artwork_details?.lot_attributes?.some(
														(item) =>
															item?.artwork_lot_symbol_lookup_key?.key ===
															LotAttributesKeys.OnCover
													)
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={twMerge(
										classNames({
											'w-[140px]': !i,
										}),
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_number || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3">
											{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.GuaranteedSale
											)
												? 'Yes'
												: 'No'}
										</Txt>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.IrrevocableBid
											)
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.OwnershipInterest
											)
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.CatalogueHighlight
											)
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.NoReserve
											)
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_attributes?.some(
												(item) =>
													item?.artwork_lot_symbol_lookup_key?.key ===
													LotAttributesKeys.OnCover
											)
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={false}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
