export { default as ProcessDataArtworkDetails } from './ProcessDataArtworkDetails.svelte';

export enum ArtworkDetailsSort {
	DateCreatedAsc = 'DEFAULT',
	DescriptionAsc = 'DESCRIPTION (A-Z)',
	DescriptionDesc = 'DESCRIPTION (Z-A)',
	ArtistTextAsc = 'ARTIST TEXT (A-Z)',
	ArtistTextDesc = 'ARTIST TEXT (Z-A)',
	ArtworkTitleAsc = 'ARTWORK TITLE (A-Z)',
	ArtworkTitleDesc = 'ARTWORK TITLE (Z-A)',
	MediaAsc = 'MEDIA (A-Z)',
	MediaDesc = 'MEDIA (Z-A)',
	CridTextAsc = 'CRID TEXT (A-Z)',
	CridTextDesc = 'CRID TEXT (Z-A)',
	CridAsc = 'CRID (A-Z)',
	CridDesc = 'CRID (Z-A)',
}
