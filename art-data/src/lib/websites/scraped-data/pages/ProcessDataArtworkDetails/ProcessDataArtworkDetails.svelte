<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataArtworkDetailsPageData } from '$routes/scraped-data/process-data/artwork-details/types';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';

	const dialogStores = createDialog();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.DateCreatedAsc);
	let updatedIds: string[] = [];

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtworkDetailsPageData>(page.data)
	);

	let sortedScrapedArtworks = $state(null) as
		| GetScrapedArtworksQuery['Scraped_Artwork']
		| null;

	const { saveStage, showMatchEventsWarning, scrapedArtworksStore } =
		getArtworkFeedsStore();

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);
	};

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			sortedScrapedArtworks = [...$scrapedArtworksStore].sort((a, b) =>
				sortFn(a, b, sort)
			);
		}
	});

	onMount(() => {
		saveStage();
	});

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.DescriptionAsc:
				case ArtworkDetailsSort.DescriptionDesc:
					return {
						field: scrapedArtwork.artwork_details?.description,
						type: 'string',
					};
				case ArtworkDetailsSort.ArtistTextAsc:
				case ArtworkDetailsSort.ArtistTextDesc:
					return {
						field: scrapedArtwork.artwork_details?.artist_text,
						type: 'string',
					};
				case ArtworkDetailsSort.ArtworkTitleAsc:
				case ArtworkDetailsSort.ArtworkTitleDesc:
					return {
						field: scrapedArtwork.artwork_details?.title,
						type: 'string',
					};
				case ArtworkDetailsSort.MediaAsc:
				case ArtworkDetailsSort.MediaDesc:
					return {
						field: scrapedArtwork.artwork_details?.media,
						type: 'string',
					};
				case ArtworkDetailsSort.CridAsc:
				case ArtworkDetailsSort.CridDesc:
					return {
						field: scrapedArtwork.artwork_details?.crid,
						type: 'string',
					};
				case ArtworkDetailsSort.CridTextAsc:
				case ArtworkDetailsSort.CridTextDesc:
					return {
						field: scrapedArtwork.artwork_feed?.cr_number,
						type: 'string',
					};
				default:
					return null;
			}
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(
				scrapedArtworkB as unknown as NonNullable<
					typeof sortedScrapedArtworks
				>[number]
			) as Parameters<typeof compareScrapedArtworkFields>[1],
			sort
		);
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			if (updatedIds.length) {
				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(
						artworkDetails: NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details']
					) => ({
						artist_text: artworkDetails?.artist_text,
						crid: artworkDetails?.crid,
						title: artworkDetails?.title,
						media: artworkDetails?.media,
					}),
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			submitting = false;

			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataArtistInfo);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Artist Text',
		'Artwork Title',
		'Media',
		'CR Text',
		'CRID',
	];

	const dataCyPrefix = 'scraped-data-process-artwork-details';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={1} />

			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<Select
						onchange={(e: SelectChangeEvent) => {
							sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
						}}
						bind:value={sort}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={classNames({
									'w-[118px]': !i,
									// 'w-[270px]': i === 1,
									// 'w-[200px]': i === 5 || i === 6,
									// 'w-[500px]': i === 2 || i === 3 || i === 4,
								})}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each sortedScrapedArtworks as scrapedArtwork, i}
							{@const isInProgress = !isScrapedArtworkProcessed(scrapedArtwork)}

							<TableRow index={i} dataCy={dataCyPrefix}>
								<TableCell dataCy={dataCyPrefix} class="px-0 py-2 pl-2">
									<div class="flex max-h-[138px] flex-col">
										<a
											target="_blank"
											rel="noopener noreferrer"
											href={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
											class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
										>
											<img
												alt=""
												class="max-h-full max-w-full"
												src={getImageUrl(
													scrapedArtwork?.images?.[0]?.directus_files_id?.id
												)}
											/>
										</a>

										<LinkButton
											dataCy={dataCyPrefix}
											href={scrapedArtwork.artwork_feed?.url}
											disabled={!scrapedArtwork.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											fullWidth
											>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
											{#snippet trailing()}
												{#if scrapedArtwork.artwork_feed?.url}
													<ExternalIcon />
												{/if}
											{/snippet}</LinkButton
										>
									</div>
								</TableCell>

								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="max-w-60 whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									{/snippet}
								</TableCell>

								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													size="sm"
													dataCy={dataCyPrefix}
													name="artist_text"
													rows={5}
													onchange={() =>
														handleChangeArtworkDetails(scrapedArtwork)}
													bind:value={
														scrapedArtwork.artwork_details.artist_text
													}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.artist_text}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>

								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="artwork_title"
													rows={5}
													size="sm"
													onchange={() =>
														handleChangeArtworkDetails(scrapedArtwork)}
													bind:value={scrapedArtwork.artwork_details.title}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.title}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="media"
													size="sm"
													rows={5}
													onchange={() =>
														handleChangeArtworkDetails(scrapedArtwork)}
													bind:value={scrapedArtwork.artwork_details.media}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.media}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									<Txt variant="body3">
										{scrapedArtwork?.artwork_feed?.cr_number || ''}
									</Txt>
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="crid"
													rows={5}
													size="sm"
													onchange={() =>
														handleChangeArtworkDetails(scrapedArtwork)}
													bind:value={scrapedArtwork.artwork_details.crid}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.crid}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={classNames(
										{
											'w-[140px]': !i,
											'w-[276px] border': i === 1,
										},
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{artwork?.artwork_details?.artist_text || ''}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{artwork?.artwork_details?.title || ''}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{artwork?.artwork_details?.media || ''}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{artwork?.artwork_feed?.cr_number || ''}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{artwork?.artwork_details?.crid || ''}
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={false}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
