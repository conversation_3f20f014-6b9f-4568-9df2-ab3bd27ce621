<script lang="ts">
	import AuctionHouseAutoComplete, {
		formatActionHouse,
	} from './AuctionHouseAutoComplete/AuctionHouseAutoComplete.svelte';
	import { page } from '$app/state';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import type { SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { CreateAuctionItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createAuction.generated';
	import type { GetEntitiesQuery } from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';
	import type { ScrapedDataMatchEventsPageData } from '$routes/scraped-data/match-events/types';

	interface Props {
		auctionDetailsForm: CreateAuctionItemMutationVariables['data'] & {
			auction_start_date_date?: string | null | undefined;
			auction_start_date_time?: string | null | undefined;
			auction_end_date_date?: string | null | undefined;
			auction_end_date_time?: string | null | undefined;
		};
		auctionHouseSelectedOption: OptionType | null;
	}

	let {
		auctionDetailsForm = $bindable(),
		auctionHouseSelectedOption = $bindable(),
	}: Props = $props();

	let data = $derived(getPageData<ScrapedDataMatchEventsPageData>(page.data));
	let auctionTypes = $derived(data.auctionTypes);
	let currencies = $derived(data.currencies);
	let timezones = $derived(data.timezones);

	let timezonesOptions = $derived(
		timezones?.map((timezone) => ({
			label: `${timezone?.timezone}`,
			value: `${timezone?.timezone}`,
		}))
	);

	let auctionTypeOptions = $derived(
		auctionTypes?.map((auction) => ({
			label: `${auction?.name}`,
			value: `${auction?.key}`,
		}))
	);

	let selectedTimezone = $state(
		auctionDetailsForm?.auction_timezone
			? auctionDetailsForm?.auction_timezone.timezone
			: null
	);

	let currencyOptions = $derived(
		currencies?.map((currency) => ({
			label: `${currency?.code}`,
			value: `${currency?.code}`,
		}))
	);

	const dataCy = 'create-new-auction';

	let selectedCurrency = $state(
		auctionDetailsForm?.currency ? auctionDetailsForm?.currency.code : null
	);

	const handleInputChange =
		(field: keyof typeof auctionDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			auctionDetailsForm = {
				...auctionDetailsForm,
				[field]: value,
			};
		};

	const handleChangeTimezone = (event: SelectChangeEvent) => {
		const timezone = timezones.find(
			(timezone) => timezone.timezone === event.detail.value
		);

		if (timezone) {
			auctionDetailsForm = {
				...auctionDetailsForm,
				auction_timezone: timezone,
			};
		}
	};

	const handleAuctionTypesChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		let auctionTypes = selected.map((sel) => {
			let match = (auctionDetailsForm.auction_types || []).find(
				(type) => type?.auction_type_key?.key === sel.value
			);

			if (match) {
				return match;
			} else {
				return {
					auction_type_key: { key: sel.value, name: sel.label },
					auction_id: { id: auctionDetailsForm.id },
				};
			}
		}) as CreateAuctionItemMutationVariables['data']['auction_types'];

		auctionDetailsForm = {
			...auctionDetailsForm,
			auction_types: auctionTypes,
		};
	};

	const handleChangeCurrency = (event: SelectChangeEvent) => {
		const currency = currencyOptions.find(
			(currency) => currency.value === event.detail.value
		);

		if (currency) {
			auctionDetailsForm = {
				...auctionDetailsForm,
				currency: {
					name: currency.label,
					code: currency.value,
				},
			};
		}
	};

	const handleRemoveAuctionHouse = () => {
		auctionDetailsForm = {
			...auctionDetailsForm,
			auction_house: null,
		};
	};

	const handleAuctionHouseChange = (e: {
		detail: {
			value: NonNullable<GetEntitiesQuery['entity'][number]>;
		};
	}) => {
		auctionDetailsForm = {
			...auctionDetailsForm,
			auction_house: {
				organisation: e.detail.value.organisation,
			},
		};

		auctionHouseSelectedOption = formatActionHouse(e.detail.value);
		return Promise.resolve();
	};
</script>

<div class="grid max-w-[600px] grid-cols-2 gap-4">
	<div class="col-span-2">
		<InputLabel {dataCy} required variant="label3" class="mb-2"
			>Auction house</InputLabel
		>
		<AuctionHouseAutoComplete
			bind:selectedOption={auctionHouseSelectedOption}
			{dataCy}
			onRemoveSelectedOption={handleRemoveAuctionHouse}
			onChange={handleAuctionHouseChange}
		/>
	</div>

	<div class="col-span-1">
		<MultiSelect
			name="auction-types"
			dataCy={`${dataCy}-auction-types`}
			label="Auction type/s"
			required
			selected={(auctionDetailsForm?.auction_types || []).map(
				(auction_type) => ({
					label: `${auction_type?.auction_type_key?.name}`,
					value: `${auction_type?.auction_type_key?.key}`,
				})
			)}
			placeholder="Type or select"
			options={auctionTypeOptions}
			class="col-span-1"
			size="sm"
			onChange={handleAuctionTypesChange}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-sale-name`}
			name="sale-name"
			required
			placeholder=""
			label="Sale name"
			value={auctionDetailsForm?.sale_name || ''}
			size="sm"
			onkeyup={handleInputChange('sale_name')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-sale-number`}
			name="sale-number"
			placeholder=""
			label="Sale number"
			classes={{ labelWrapper: '[&>div]:min-h-[16px]' }}
			value={getDayFromDirectus(auctionDetailsForm.sale_number || '')}
			size="sm"
			onkeyup={handleInputChange('sale_number')}
		/>
	</div>

	<div class="col-span-1">
		<InputLabel {dataCy} required variant="label3" class="mb-2"
			>Currency</InputLabel
		>
		<Select
			ariaLabel="Select a currency"
			dataCy={`${dataCy}-currency`}
			name="currency"
			placeholder="currency"
			options={currencyOptions}
			bind:value={selectedCurrency}
			size="sm"
			onchange={handleChangeCurrency}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-sale-url`}
			name="sale-url"
			placeholder=""
			label="Sale url"
			value={auctionDetailsForm?.sale_url || ''}
			size="sm"
			onkeyup={handleInputChange('sale_url')}
		/>
	</div>

	<div class="col-span-1">
		<InputLabel class="mb-2" dataCy={`${dataCy}-auction-start-datetime`}
			>Auction start date</InputLabel
		>
		<div class="flex gap-1">
			<Input
				dataCy={`${dataCy}-auction-start-date`}
				type="date"
				name="auction-start-date"
				placeholder=""
				value={getDayFromDirectus(
					auctionDetailsForm.auction_start_date_date || ''
				)}
				size="sm"
				classes={{ wrapper: 'flex-1', labelWrapper: '[&>div]:min-h-[16px]' }}
				onkeyup={handleInputChange('auction_start_date_date')}
				onchange={handleInputChange('auction_start_date_date')}
			/>
			<Input
				dataCy={`${dataCy}-auction-start-time`}
				type="time"
				name="auction-start-time"
				placeholder=""
				class="min-w-[110px]"
				value={auctionDetailsForm.auction_start_date_time || ''}
				size="sm"
				onkeyup={handleInputChange('auction_start_date_time')}
				onchange={handleInputChange('auction_start_date_time')}
			/>
		</div>
	</div>
	<div class="col-span-1">
		<InputLabel class="mb-2" dataCy={`${dataCy}-auction-end-datetime`}
			>Auction end date</InputLabel
		>
		<div class="flex gap-1">
			<Input
				dataCy={`${dataCy}-auction-end-date`}
				name="auction-end-date"
				type="date"
				placeholder=""
				classes={{ labelWrapper: '[&>div]:min-h-[16px]', wrapper: 'flex-1' }}
				value={getDayFromDirectus(
					auctionDetailsForm.auction_end_date_date || ''
				)}
				size="sm"
				onkeyup={handleInputChange('auction_end_date_date')}
				onchange={handleInputChange('auction_end_date_date')}
			/>
			<Input
				dataCy={`${dataCy}-auction-end-time`}
				type="time"
				name="auction-end-time"
				placeholder=""
				class="min-w-[110px]"
				value={auctionDetailsForm.auction_end_date_time || ''}
				size="sm"
				onkeyup={handleInputChange('auction_end_date_time')}
				onchange={handleInputChange('auction_end_date_time')}
			/>
		</div>
	</div>

	<div class="col-span-1">
		<InputLabel {dataCy} variant="label3" class="mb-2">Time zone</InputLabel>
		<Select
			ariaLabel="Select a time zone"
			dataCy={`${dataCy}-time-zone`}
			name="time-zone"
			placeholder="-Select-"
			options={timezonesOptions}
			bind:value={selectedTimezone}
			size="sm"
			onchange={handleChangeTimezone}
		/>
	</div>
</div>
