<script lang="ts">
	import dayjs from 'dayjs';
	import { CreateNewAuction } from './CreateNewAuction';
	import { MatchExistingAuction } from './MatchExistingAuction';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';
	import type { CreateAuctionItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createAuction.generated';
	import type { AuctionSearchQuery } from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/auctionSearch.generated';

	interface Props {
		auctionMatches?: AuctionSearchQuery['auctionSearch']['data'];
		matchId: string;
		auctionDetailsForm: CreateAuctionItemMutationVariables['data'] & {
			auction_start_date_date?: string | null | undefined;
			auction_start_date_time?: string | null | undefined;
			auction_end_date_date?: string | null | undefined;
			auction_end_date_time?: string | null | undefined;
		};
		auctionHouseSelectedOption: OptionType | null;
		auctionSelectedOption: OptionType | null;
	}

	let {
		auctionMatches = [],
		matchId = $bindable(),
		auctionDetailsForm = $bindable(),
		auctionHouseSelectedOption = $bindable(),
		auctionSelectedOption = $bindable(),
	}: Props = $props();

	const dataCy = 'match-events-auction-form';
</script>

{#each auctionMatches.slice(0, 5) as auctionMatch}
	{@const auctionInfo = [
		auctionMatch.auction_house?.organisation?.name,
		auctionMatch.local_auction_end_date || auctionMatch.local_auction_start_date
			? dayjs(
					auctionMatch.local_auction_end_date ||
						auctionMatch.local_auction_start_date
				).format('DD/MM/YYYY')
			: null,
		auctionMatch.currency?.code,
		auctionMatch.sale_number,
	]
		.filter(Boolean)
		.join(', ')}

	<div class="mb-2 flex items-center border border-gray-200 p-3">
		<Radio
			bind:group={matchId}
			value={`${auctionMatch.id}`}
			dataCy="match-events-auction-form"
		/>
		<div class="ml-3.5 leading-[1.25rem]">
			<Txt variant="label3" class="inline">Match with</Txt>
			<Txt
				class="text-blue-500 inline"
				variant="body2"
				component="a"
				target="_blank"
				rel="noopener noreferrer"
				href={`${Config.ArteyeDomain}/auctions/${auctionMatch.id}`}
				>{auctionMatch.sale_name}</Txt
			>
			{#if auctionInfo}
				<Txt class="ml-[2px] text-gray-500 inline" variant="body2"
					>({auctionInfo})</Txt
				>
			{/if}
		</div>
	</div>
{/each}

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="match"
			dataCy="match-events-match-auction"
		/>
		<div class="pl-1.5">Match with auction ID</div>
	</InputLabel>

	{#if matchId === 'match'}
		<div class="ml-[31px] mt-8 pb-8">
			<MatchExistingAuction bind:auctionSelectedOption />
		</div>
	{/if}
</div>

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="new"
			dataCy="match-events-auction-form"
		/>
		<div class="pl-1.5">Create new auction</div>
	</InputLabel>

	{#if matchId === 'new'}
		<div class="ml-[31px] mt-8 pb-8">
			<CreateNewAuction
				bind:auctionHouseSelectedOption
				bind:auctionDetailsForm
			/>
		</div>
	{/if}
</div>

<div class="mb-8">
	<LinkButton
		variant="secondary"
		size="md"
		{dataCy}
		href={`${Config.ArteyeDomain}/auctions`}
		newTab
	>
		search on arteye
		{#snippet trailing()}
			<ExternalIcon />
		{/snippet}
	</LinkButton>
</div>
