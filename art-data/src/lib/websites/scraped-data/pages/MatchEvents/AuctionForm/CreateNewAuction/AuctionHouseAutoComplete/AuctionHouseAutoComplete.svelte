<script lang="ts" module>
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Config } from '$lib/constants/config';

	export const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			limit: 50,
			sort: ['organisation.location.name', '-aggregations.activity_count'],
			filter: {
				_and: [
					{
						organisation: {
							type: {
								organisation_type_key: {
									key: {
										_eq: 'AUCTION_HOUSE',
									},
								},
							},
						},
					},
					{ status: { key: { _neq: 'archived' } } },

					...(() => {
						if (!value) {
							return [];
						}

						if (isUuidValid(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [{ name: { _icontains: value } }];
					})(),
				],
			},
		};
	};

	export const formatEntity = (entity: GetEntitiesQuery['entity'][number]) => {
		const url = (() => {
			if (entity.type?.key === 'person') {
				if (entity?.artist) {
					return `${Config.ArteyeDomain}/artists/${entity?.artist?.id}`;
				} else {
					return `${Config.ArteyeDomain}/people/${entity?.person?.id}`;
				}
			}
			return `${Config.ArteyeDomain}/organisations/${entity?.organisation?.id}`;
		})();

		const location = [
			entity?.organisation?.location?.name,
			entity?.organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
	export const formatActionHouse = (
		auctionHouse: NonNullable<GetEntitiesQuery['entity'][number]>
	) => {
		const url = (() => {
			return `${Config.ArteyeDomain}/organisations/${auctionHouse?.organisation?.id}`;
		})();

		const location = [
			auctionHouse?.organisation?.location?.name,
			auctionHouse?.organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${auctionHouse?.organisation?.name}`,
			line2: url,
			line3: `${auctionHouse?.organisation?.type?.[0]?.organisation_type_key?.key}`,
			line4: `${auctionHouse?.organisation?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import type {
		GetEntitiesQuery,
		GetEntitiesQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';
	import { GetEntitiesDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	let entities: GetEntitiesQuery['entity'] | [] = [];

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemoveSelectedOption: () => void;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: NonNullable<GetEntitiesQuery['entity'][number]>;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		onRemoveSelectedOption,
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getOptions = (data: GetEntitiesQuery | undefined) => {
		entities = data?.entity || [];
		return [...(data?.entity || []).map(formatEntity)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		emptyValueResponse={{ entity: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			longList: '!max-h-[225px] !min-h-[auto] z-[10] bg-[white]',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		{getOptions}
		{getVariables}
		{onRemoveSelectedOption}
		document={GetEntitiesDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-auction-house-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				onRemoveSelectedOption();
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
