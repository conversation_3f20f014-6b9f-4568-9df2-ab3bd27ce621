<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import LinkOption from '$global/components/QueryAutocomplete/LinkOption/LinkOption.svelte';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetAuctionsDocument,
		type GetAuctionsQuery,
		type GetAuctionsQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getAuctions.generated';

	interface Props {
		auctionSelectedOption: OptionType | null;
	}

	const value = writable('');
	let { auctionSelectedOption = $bindable() }: Props = $props();

	const getVariables = (value: string): GetAuctionsQueryVariables => {
		return isUuidValid(value)
			? {
					filter: {
						id: {
							_eq: value,
						},
					},
				}
			: {};
	};

	const formatAuction = (auction: GetAuctionsQuery['auction'][number]) => {
		const auctionInfo = [
			auction?.auction_house?.organisation?.name,
			auction?.auction_end_date || auction?.auction_start_date
				? dayjs(auction.auction_end_date || auction.auction_start_date).format(
						'DD/MM/YYYY'
					)
				: '',
			auction?.currency?.code,
			auction?.sale_number,
		].join(', ');

		return {
			line1: auction.sale_name,
			line1Suffix: auctionInfo ? `(${auctionInfo})` : '',
			line2: auction.id,
		};
	};

	const getOptions = (data: GetAuctionsQuery | undefined) => {
		return (data?.auction || []).map(formatAuction);
	};

	const handleRemoveSelectedOption = () => {
		auctionSelectedOption = null;
		value.set('');
	};
</script>

<div class={'relative'}>
	<QueryAutocomplete
		size="sm"
		autocomplete="nope"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="gallery"
		label="Auction ID"
		required
		dataCy={`match-existing-auction`}
		placeholder="Enter auction ID"
		emptyValueResponse={{ auction: [] }}
		onRemoveSelectedOption={handleRemoveSelectedOption}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line2: 'hidden',
			},
			selectedOption: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				button: 'max-w-full',
				wrapper: 'py-1.5 px-2 bg-white border border-gray-200',
				line2: 'hidden',
			},
		}}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!auctionSelectedOption })}
		{getOptions}
		{getVariables}
		document={GetAuctionsDocument}
		{value}
		bind:selectedOption={auctionSelectedOption}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`match-existing-auction-city-autocomplete`}
				>No auction found.</NoResults
			>{/snippet}
	</QueryAutocomplete>
	{#if auctionSelectedOption}
		<button
			class="absolute right-[2rem] top-[2.375rem]"
			onclick={handleRemoveSelectedOption}
		>
			<CrossIcon class="h-4 w-4" />
		</button>
	{/if}
</div>
