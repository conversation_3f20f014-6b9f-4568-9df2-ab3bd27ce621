<script lang="ts" module>
	export const getVariables = (
		value: string
	): OrganisationSearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : -1,
				filters: {
					nameOrId: value,
				},
			},
		};
	};

	export const formatOrganisation = (
		organisation: OrganisationSearchQuery['organisationSearch']['data'][number]
	) => {
		const url = (() => {
			return `${Config.ArteyeDomain}/organisations/${organisation?.id}`;
		})();

		const location = [
			organisation?.location?.name,
			organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${organisation?.name}`,
			line2: url,
			line4: `${organisation?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Config } from '$lib/constants/config';
	import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
	import {
		OrganisationSearchDocument,
		type OrganisationSearchQuery,
		type OrganisationSearchQueryVariables,
	} from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/organisationSearch.generated';

	let entities: OrganisationSearchQuery['organisationSearch']['data'] = [];

	interface Props {
		error: string;
		onBlur: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OrganisationSearchQuery['organisationSearch']['data'][number];
					};
			  }) => Promise<void>);
	}

	let {
		error,
		onBlur,
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getOptions = (data: OrganisationSearchQuery | undefined) => {
		entities = data?.organisationSearch?.data || [];
		return [...(data?.organisationSearch?.data || []).map(formatOrganisation)];
	};

	const handleRemoveSelectedOption = () => {
		if (onChange) {
			onChange({
				detail: {
					value: null,
				},
			} as unknown as Parameters<typeof onChange>[0]);
		}
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		onRemoveSelectedOption={handleRemoveSelectedOption}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		{onBlur}
		getError={() => error}
		emptyValueResponse={{
			organisationSearch: { data: [] },
			organisationSearchCount: 0,
		}}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientCustomArteye}
		classes={{
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		{getOptions}
		{getVariables}
		document={OrganisationSearchDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-organisers-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				handleRemoveSelectedOption();
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
