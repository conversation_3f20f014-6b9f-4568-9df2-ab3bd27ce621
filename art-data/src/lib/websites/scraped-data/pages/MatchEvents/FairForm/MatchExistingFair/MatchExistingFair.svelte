<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import LinkOption from '$global/components/QueryAutocomplete/LinkOption/LinkOption.svelte';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetFairsDocument,
		type GetFairsQuery,
		type GetFairsQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getFairs.generated';

	interface Props {
		fairSelectedOption: OptionType | null;
	}

	const value = writable('');
	let { fairSelectedOption = $bindable() }: Props = $props();

	const getVariables = (value: string): GetFairsQueryVariables => {
		return isUuidValid(value)
			? {
					filter: {
						id: {
							_eq: value,
						},
					},
				}
			: {};
	};

	const formatFair = (fair: GetFairsQuery['fair'][number]) => {
		const fairInfo = [
			fair.venue_city?.name,
			fair.venue_country?.name,
			[
				fair.start_date ? dayjs(fair.start_date).format('DD/MM/YYYY') : null,
				fair.end_date ? dayjs(fair.end_date).format('DD/MM/YYYY') : null,
			]
				.filter(Boolean)
				.join(' - '),
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: fair.title,
			line1Suffix: fairInfo ? `(${fairInfo})` : '',
			line2: fair.id,
		};
	};

	const getOptions = (data: GetFairsQuery | undefined) => {
		return (data?.fair || []).map(formatFair);
	};

	const handleRemoveSelectedOption = () => {
		fairSelectedOption = null;
		value.set('');
	};
</script>

<div class={'relative'}>
	<QueryAutocomplete
		size="sm"
		autocomplete="nope"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="gallery"
		label="Fair ID"
		required
		dataCy={`match-existing-fair`}
		placeholder="Enter fair ID"
		emptyValueResponse={{ fair: [] }}
		onRemoveSelectedOption={handleRemoveSelectedOption}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line2: 'hidden',
			},
			selectedOption: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				button: 'max-w-full',
				wrapper: 'py-1.5 px-2 bg-white border border-gray-200',
				line2: 'hidden',
			},
		}}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!fairSelectedOption })}
		{getOptions}
		{getVariables}
		document={GetFairsDocument}
		{value}
		bind:selectedOption={fairSelectedOption}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`match-existing-fair-city-autocomplete`}
				>No fair found.</NoResults
			>{/snippet}
	</QueryAutocomplete>
	{#if fairSelectedOption}
		<button
			class="absolute right-[2rem] top-[2.375rem]"
			onclick={handleRemoveSelectedOption}
		>
			<CrossIcon class="h-4 w-4" />
		</button>
	{/if}
</div>
