<script lang="ts">
	import dayjs from 'dayjs';
	import { CreateNewFair } from './CreateNewFair';
	import { MatchExistingFair } from './MatchExistingFair';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';
	import type { CreateFairItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createFair.generated';
	import type { FairSearchQuery } from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/fairSearch.generated';

	interface Props {
		matchId: string;
		fairMatches?: FairSearchQuery['fairSearch']['data'];
		fairDetailsForm: CreateFairItemMutationVariables['data'];
		fairSelectedOption: OptionType | null;
		fairOrganisationSelectedOption: OptionType | null;
		fairCitySelectedOption: OptionType | null;
	}

	let {
		matchId = $bindable(),
		fairMatches = [],
		fairDetailsForm = $bindable(),
		fairSelectedOption = $bindable(),
		fairOrganisationSelectedOption = $bindable(),
		fairCitySelectedOption = $bindable(),
	}: Props = $props();

	const dataCy = 'match-events-auction-form';
</script>

{#each fairMatches.slice(0, 5) as fairMatch}
	{@const fairInfo = [
		fairMatch.venue_city?.name,
		fairMatch.venue_country?.name,
		[
			fairMatch.local_start_date
				? dayjs(fairMatch.local_start_date).format('DD/MM/YYYY')
				: null,
			fairMatch.local_end_date
				? dayjs(fairMatch.local_end_date).format('DD/MM/YYYY')
				: null,
		]
			.filter(Boolean)
			.join(' - '),
	]
		.filter(Boolean)
		.join(', ')}

	<div class="mb-2 flex items-center border border-gray-200 p-3">
		<Radio
			bind:group={matchId}
			value={`${fairMatch.id}`}
			dataCy="match-events-auction-form"
		/>
		<div class="ml-3.5 leading-[1.25rem]">
			<Txt variant="label3" class="inline">Match with</Txt>
			<Txt
				class="text-blue-500 inline"
				variant="body2"
				component="a"
				target="_blank"
				rel="noopener noreferrer"
				href={`${Config.ArteyeDomain}/fairs/${fairMatch.id}`}
				>{fairMatch.title}</Txt
			>
			{#if fairInfo}
				<Txt class="ml-[2px] text-gray-500 inline" variant="body2"
					>({fairInfo})</Txt
				>
			{/if}
		</div>
	</div>
{/each}

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="match"
			dataCy="match-events-match-auction"
		/>
		<div class="pl-1.5">Match with fair ID</div>
	</InputLabel>

	{#if matchId === 'match'}
		<div class="ml-[31px] mt-8 pb-8">
			<MatchExistingFair bind:fairSelectedOption />
		</div>
	{/if}
</div>

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="new"
			dataCy="match-events-gallery-form"
		/>
		<div class="pl-1.5">Create new fair</div>
	</InputLabel>

	{#if matchId === 'new'}
		<div class="ml-[31px] mt-8 pb-8">
			<CreateNewFair
				bind:fairDetailsForm
				bind:fairCitySelectedOption
				bind:fairOrganisationSelectedOption
			/>
		</div>
	{/if}
</div>

<div class="mb-8">
	<LinkButton
		variant="secondary"
		size="md"
		{dataCy}
		href={`${Config.ArteyeDomain}/fairs`}
		newTab
	>
		search on arteye
		{#snippet trailing()}
			<ExternalIcon />
		{/snippet}
	</LinkButton>
</div>
