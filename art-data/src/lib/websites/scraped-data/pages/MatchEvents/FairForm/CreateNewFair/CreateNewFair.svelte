<script lang="ts">
	import { writable } from 'svelte/store';
	import { CityAutocomplete } from './CityAutocomplete';
	import { FairOrganiserAutoComplete } from './FairOrganiserAutoComplete';
	import { formatOrganisation } from './FairOrganiserAutoComplete/FairOrganiserAutoComplete.svelte';
	import { FairAddressesFieldName } from '.';
	import { page } from '$app/state';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Select } from '$global/components/Select';
	import { type SelectChangeEvent } from '$global/components/Select';
	import {
		TableBody,
		TableHeader,
		TableRow,
		TableHeaderRow,
		TableCell,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';
	import type { Create_Fair_Input } from '$gql/types-arteye';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import type { CreateFairItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createFair.generated';
	import { GetFairOrganisationsDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getFairOrganisations.generated';
	import type { OrganisationSearchQuery } from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/organisationSearch.generated';
	import type { ScrapedDataMatchEventsPageData } from '$routes/scraped-data/match-events/types';

	interface Props {
		fairDetailsForm: CreateFairItemMutationVariables['data'];
		fairCitySelectedOption: OptionType | null;
		fairOrganisationSelectedOption: OptionType | null;
	}

	let {
		fairDetailsForm = $bindable(),
		fairCitySelectedOption = $bindable(),
		fairOrganisationSelectedOption = $bindable(),
	}: Props = $props();

	let data = $derived(getPageData<ScrapedDataMatchEventsPageData>(page.data));
	let timezones = $derived(data.timezones);

	let timezonesOptions = $derived(
		timezones?.map((timezone) => ({
			label: `${timezone?.timezone}`,
			value: `${timezone?.timezone}`,
		}))
	);

	let blur = $state({
		title: false,
		startDate: false,
		endDate: false,
		organiser: false,
		url: false,
	});

	let selectedTimezone = $state(
		fairDetailsForm?.timezone ? fairDetailsForm?.timezone.timezone : null
	);

	const dataCy = 'fair-details-card';
	const cityValue = writable('');
	const handleInputChange =
		(field: keyof CreateFairItemMutationVariables['data']) =>
		(event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			fairDetailsForm = { ...fairDetailsForm, [field]: value };
		};

	const handleOrganiserChange = async (e: {
		detail: {
			value:
				| OrganisationSearchQuery['organisationSearch']['data'][number]
				| null;
		};
	}) => {
		const organiser = e.detail.value;
		let fairOrganisationId: string | null | undefined;

		if (organiser) {
			const fairOrganisationRes = await gqlClientArteye.request(
				GetFairOrganisationsDocument,
				{
					filter: {
						organisation: { id: { _eq: organiser?.id } },
					},
				},
				getAuthorizationHeaders({
					user: { access_token: page.data.user.arteye_token },
				})
			);

			fairOrganisationId = fairOrganisationRes?.fair_organisation?.[0]?.id;
		}

		fairDetailsForm = {
			...fairDetailsForm,
			fair_organisation: (fairOrganisationId
				? { id: fairOrganisationId }
				: {
						organisation: organiser
							? {
									id: organiser?.id,
									name: organiser?.name,
									type: organiser?.type,
								}
							: null,
					}) as Create_Fair_Input['fair_organisation'],
		};

		fairOrganisationSelectedOption = e.detail.value
			? formatOrganisation(e.detail.value)
			: null;

		return Promise.resolve();
	};

	const handleChangeCity = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const cityOption = e.detail.value;
		const country = cityOption.line4 ? JSON.parse(cityOption.line4) : null;

		fairDetailsForm = {
			...fairDetailsForm,
			venue_city: {
				name: `${cityOption.line5}`,
				short_code: `${cityOption.line2}`,
				code: `${cityOption.line3}`,
			},
			venue_country: country,
		};

		return Promise.resolve();
	};

	const handleCityClear = () => {
		fairDetailsForm = {
			...fairDetailsForm,
			venue_city: null,
			venue_country: null,
		};
	};

	const handleChangeTimezone = (event: SelectChangeEvent) => {
		const timezone = timezones.find(
			(timezone) => timezone.timezone === event.detail.value
		);

		if (timezone) {
			fairDetailsForm = {
				...fairDetailsForm,
				timezone,
			};
		}
	};

	const headers = [
		{
			fieldName: FairAddressesFieldName.City,
			title: 'Town/City',
		},
		{
			fieldName: FairAddressesFieldName.Line1,
			title: 'Street address 1',
		},
		{
			fieldName: FairAddressesFieldName.Line2,
			title: 'Street address 2',
		},
		{
			fieldName: FairAddressesFieldName.Line3,
			title: 'Street address 3',
		},
	];
</script>

<div class="mb-4 grid max-w-[1000px] grid-cols-4 gap-4">
	<div class="col-span-2">
		<InputLabel {dataCy} required variant="label3" class="mb-2"
			>Fair organisation</InputLabel
		>
		<FairOrganiserAutoComplete
			onBlur={() => {
				blur.organiser = true;
			}}
			error={!fairDetailsForm.fair_organisation && blur.organiser
				? 'Please provide an organiser'
				: ''}
			bind:selectedOption={fairOrganisationSelectedOption}
			{dataCy}
			onChange={handleOrganiserChange}
		/>
	</div>

	<div class="col-span-2">
		<Input
			dataCy={`${dataCy}-title`}
			name="title"
			placeholder="Fair Title"
			label="Fair Title"
			required
			value={fairDetailsForm.title || ''}
			onblur={() => {
				blur.title = true;
			}}
			error={!fairDetailsForm.title && blur.title
				? 'Please provide a title'
				: ''}
			size="sm"
			onkeyup={handleInputChange('title')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-fairUrl`}
			name="fairUrl"
			placeholder="Fair URL"
			label="Fair URL"
			value={fairDetailsForm.fair_url || ''}
			onblur={() => {
				blur.url = true;
			}}
			error={fairDetailsForm.fair_url &&
			!isValidUrl(fairDetailsForm.fair_url) &&
			blur.url
				? 'Please provide a valid URL'
				: ''}
			size="sm"
			onkeyup={handleInputChange('fair_url')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-startDate`}
			name="startDate"
			placeholder="Start Date"
			required
			label="Start Date"
			type="date"
			onblur={() => {
				blur.startDate = true;
			}}
			error={!fairDetailsForm.start_date && blur.startDate
				? 'Please provide a start date'
				: ''}
			value={getDayFromDirectus(fairDetailsForm.start_date || '')}
			size="sm"
			onkeyup={handleInputChange('start_date')}
			onchange={handleInputChange('start_date')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-endDate`}
			name="endDate"
			placeholder="End Date"
			required
			label="End Date"
			type="date"
			onblur={() => {
				blur.endDate = true;
			}}
			error={!fairDetailsForm.end_date && blur.endDate
				? 'Please provide an end date'
				: ''}
			value={getDayFromDirectus(fairDetailsForm.end_date || '')}
			size="sm"
			onkeyup={handleInputChange('end_date')}
			onchange={handleInputChange('end_date')}
		/>
	</div>

	<div class="col-span-1">
		<InputLabel {dataCy} required variant="label3" class="mb-2"
			>Time zone</InputLabel
		>
		<Select
			ariaLabel="Select a time zone"
			dataCy={`${dataCy}-time-zone`}
			name="time-zone"
			placeholder="-Select-"
			options={timezonesOptions}
			bind:value={selectedTimezone}
			size="sm"
			onchange={handleChangeTimezone}
		/>
	</div>

	<div class="col-span-4 mb-4">
		<Txt variant="label3" class="mb-2">Fair Address</Txt>
		<table class="w-full table-fixed rounded-md bg-white">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					<TableHeader {dataCy}>
						{header.title}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				<TableRow index={0} {dataCy}>
					{#each headers as header}
						{#if header.fieldName === FairAddressesFieldName.City}
							<TableCell {dataCy} class="">
								{#snippet custom()}
									<CityAutocomplete
										{dataCy}
										disabled={false}
										onRemoveSelectedOption={handleCityClear}
										bind:selectedOption={fairCitySelectedOption}
										onChange={handleChangeCity}
										value={cityValue}
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line1}
							<TableCell {dataCy} class="py-0">
								<Input
									dataCy={`${dataCy}-line-1`}
									name="line-1"
									placeholder="line 1"
									value={fairDetailsForm.venue_address_1}
									onkeyup={handleInputChange('venue_address_1')}
									size="sm"
								/>
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line2}
							<TableCell {dataCy} class="py-0">
								<Input
									dataCy={`${dataCy}-line-2`}
									name="line-2"
									placeholder="line 2"
									value={fairDetailsForm.venue_address_2}
									onkeyup={handleInputChange('venue_address_2')}
									size="sm"
								/>
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line3}
							<TableCell {dataCy} class="py-0">
								<Input
									dataCy={`${dataCy}-line-3`}
									name="line-3"
									placeholder="line 3"
									value={fairDetailsForm.venue_address_3}
									onkeyup={handleInputChange('venue_address_3')}
									size="sm"
								/>
							</TableCell>
						{/if}
					{/each}
				</TableRow>
			</TableBody>
		</table>
	</div>
</div>
