<script lang="ts">
	import {
		OrganisersFieldName,
		ExhibitionOrganisersRow,
	} from './ExhibitionOrganisersRow';
	import type { OrganiserType } from './ExhibitionOrganisersRow/ExhibitionOrganisersRow.svelte';
	import { formatEntityOrganiser } from './ExhibitionOrganisersRow/OrganisersNameAutoComplete/OrganisersNameAutoComplete.svelte';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import type { GetEntitiesQuery } from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	const headers = [
		{
			title: 'Name',
			fieldName: OrganisersFieldName.Name,
		},
		{ title: '' },
	];

	interface Props {
		organisers?: OrganiserType[];
		onChange: (entities: OrganiserType[]) => void;
	}

	let { organisers = $bindable([]), onChange }: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(organisers || []).map((organiser) => {
			if (!organiser?.id) return null;

			return formatEntityOrganiser(
				organiser as GetEntitiesQuery['entity'][number]
			);
		})
	);

	const onOrganiserChange =
		(index: number) =>
		(entity: GetEntitiesQuery['entity'][number] | Record<never, never>) => {
			const entities = (organisers || []).map((organiser, i) =>
				i === index
					? {
							...entity,
							isNew: organiser.isNew,
							isDeleted: organiser.isDeleted,
						}
					: organiser
			) as OrganiserType[];
			onChange(entities);
		};

	const handleOrganiserDelete = (
		entity: GetEntitiesQuery['entity'][number]
	) => {
		const entities = (organisers || []).map((organiser) => {
			if (organiser?.id === entity.id) {
				return {
					...organiser,
					isDeleted: true,
				};
			}
			return organiser;
		});

		onChange(entities);
	};

	const dataCy = 'organisers';

	const handleOrganiserAdd = () => {
		organisers = [
			...(organisers || []).map((organiser) => organiser),
			{
				isNew: true,
			},
		] as OrganiserType[];

		selectedOptions = [...selectedOptions, null];
	};
</script>

<div>
	<InputLabel {dataCy} required variant="label3" class="mb-2"
		>Organisers</InputLabel
	>
	<table class="w-full table-fixed rounded-md bg-white">
		<TableHeaderRow {dataCy}>
			{#each headers as header}
				{#if header.fieldName}
					<TableHeader {dataCy}>
						{header.title}
					</TableHeader>
				{:else}
					<TableHeader {dataCy} class="flex justify-end">
						<Button
							size="sm"
							onclick={handleOrganiserAdd}
							dataCy={`${dataCy}-add`}
							class="h-[2rem] w-[2rem] px-0"
							variant="secondary"
						>
							<PlusIcon class="h-3 w-3" />
						</Button>
					</TableHeader>
				{/if}
			{/each}
		</TableHeaderRow>

		<TableBody {dataCy}>
			{#if organisers}
				{#each organisers as organiser, index}
					{#if !organiser.isDeleted}
						<ExhibitionOrganisersRow
							{headers}
							{organiser}
							{index}
							bind:selectedOption={selectedOptions[index]}
							onChange={onOrganiserChange(index)}
							onDelete={handleOrganiserDelete}
						/>
					{/if}
				{/each}
			{/if}
		</TableBody>
	</table>
</div>
