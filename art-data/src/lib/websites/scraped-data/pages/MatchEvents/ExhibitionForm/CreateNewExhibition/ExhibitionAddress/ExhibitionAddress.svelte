<script lang="ts">
	import { writable } from 'svelte/store';
	import { CityAutocomplete } from '../../../FairForm/CreateNewFair/CityAutocomplete';
	import { ExhibitionAddressesFieldName } from '.';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableBody,
		TableCell,
		TableHeader,
		TableHeaderRow,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import type { CreateExhibitionItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createExhibition.generated';

	interface Props {
		exhibitionCitySelectedOption: OptionType | null;
		exhibitionDetailsForm: CreateExhibitionItemMutationVariables['data'];
		handleInputChange: (
			field: keyof typeof exhibitionDetailsForm
		) => (event: Event) => void;
	}

	let {
		exhibitionCitySelectedOption = $bindable(),
		exhibitionDetailsForm = $bindable(),
		handleInputChange,
	}: Props = $props();

	const dataCy = 'addresses';

	let cityValue = writable('');

	const headers = [
		{
			fieldName: ExhibitionAddressesFieldName.City,
			title: 'Town/City',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line1,
			title: 'Street address 1',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line2,
			title: 'Street address 2',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line3,
			title: 'Street address 3',
		},
	];

	const handleChangeCity = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const cityOption = e.detail.value;
		const country = cityOption.line4 ? JSON.parse(cityOption.line4) : null;

		exhibitionDetailsForm = {
			...exhibitionDetailsForm,
			venue_city: {
				name: `${cityOption.line5}`,
				short_code: `${cityOption.line2}`,
				code: `${cityOption.line3}`,
			},
			venue_country: country,
		};

		return Promise.resolve();
	};

	const handleCityClear = () => {
		exhibitionDetailsForm = {
			...exhibitionDetailsForm,
			venue_city: null,
			venue_country: null,
		};
	};
</script>

<div class="col-span-5 mb-8">
	<Txt variant="label3" class="mb-2">Exhibition Address</Txt>
	<table class="w-full table-fixed rounded-md bg-white">
		<TableHeaderRow {dataCy}>
			{#each headers as header}
				<TableHeader {dataCy}>
					{header.title}
				</TableHeader>
			{/each}
		</TableHeaderRow>

		<TableBody {dataCy}>
			<TableRow index={0} {dataCy}>
				{#each headers as header}
					{#if header.fieldName === ExhibitionAddressesFieldName.City}
						<TableCell {dataCy} class="">
							{#snippet custom()}
								<CityAutocomplete
									{dataCy}
									disabled={false}
									onRemoveSelectedOption={handleCityClear}
									bind:selectedOption={exhibitionCitySelectedOption}
									onChange={handleChangeCity}
									value={cityValue}
								/>
							{/snippet}
						</TableCell>
					{:else if header.fieldName === ExhibitionAddressesFieldName.Line1}
						<TableCell {dataCy} class="py-0">
							<Input
								dataCy={`${dataCy}-line-1`}
								name="line-1"
								placeholder="line 1"
								value={exhibitionDetailsForm.venue_address_1}
								onkeyup={handleInputChange('venue_address_1')}
								size="sm"
							/>
						</TableCell>
					{:else if header.fieldName === ExhibitionAddressesFieldName.Line2}
						<TableCell {dataCy} class="py-0">
							<Input
								dataCy={`${dataCy}-line-2`}
								name="line-2"
								placeholder="line 2"
								value={exhibitionDetailsForm.venue_address_2}
								onkeyup={handleInputChange('venue_address_2')}
								size="sm"
							/>
						</TableCell>
					{:else if header.fieldName === ExhibitionAddressesFieldName.Line3}
						<TableCell {dataCy} class="py-0">
							<Input
								dataCy={`${dataCy}-line-3`}
								name="line-3"
								placeholder="line 3"
								value={exhibitionDetailsForm.venue_address_3}
								onkeyup={handleInputChange('venue_address_3')}
								size="sm"
							/>
						</TableCell>
					{:else if header.fieldName === ExhibitionAddressesFieldName.PostCode}
						<TableCell {dataCy} class="py-0">
							<Input
								dataCy={`${dataCy}-postcode`}
								name="postcode"
								placeholder="postcode"
								value={exhibitionDetailsForm.venue_post_code}
								onkeyup={handleInputChange(
									ExhibitionAddressesFieldName.PostCode
								)}
								size="sm"
							/>
						</TableCell>
					{/if}
				{/each}
			</TableRow>
		</TableBody>
	</table>
</div>
