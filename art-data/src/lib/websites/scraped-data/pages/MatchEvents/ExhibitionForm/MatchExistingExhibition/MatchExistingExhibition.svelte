<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetExhibitionsDocument,
		type GetExhibitionsQuery,
		type GetExhibitionsQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getExhibitions.generated';

	interface Props {
		exhibitionSelectedOption: OptionType | null;
	}

	const value = writable('');
	let { exhibitionSelectedOption = $bindable() }: Props = $props();

	const getVariables = (value: string): GetExhibitionsQueryVariables => {
		return isUuidValid(value)
			? {
					filter: {
						id: {
							_eq: value,
						},
					},
				}
			: {};
	};

	const formatExhibition = (
		exhibition: GetExhibitionsQuery['exhibition'][number]
	) => {
		const exhibitionInfo = [
			exhibition.organisers?.[0]?.entity_id?.name,
			exhibition.organisers?.[0]?.entity_id?.addresses?.[0]?.city?.name,
			exhibition.organisers?.[0]?.entity_id?.addresses?.[0]?.country?.name,
			[
				exhibition.start_date
					? dayjs(exhibition.start_date).format('DD/MM/YYYY')
					: null,
				exhibition.end_date
					? dayjs(exhibition.end_date).format('DD/MM/YYYY')
					: null,
			]
				.filter(Boolean)
				.join(' - '),
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: exhibition.title,
			line1Suffix: exhibitionInfo ? `(${exhibitionInfo})` : '',
			line2: exhibition.id,
		};
	};

	const getOptions = (data: GetExhibitionsQuery | undefined) => {
		return (data?.exhibition || []).map(formatExhibition);
	};

	const handleRemoveSelectedOption = () => {
		exhibitionSelectedOption = null;
		value.set('');
	};
</script>

<div class={'relative'}>
	<QueryAutocomplete
		size="sm"
		autocomplete="nope"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="gallery"
		label="Exhibition ID"
		required
		dataCy={`match-existing-exhibition`}
		placeholder="Enter exhibition ID"
		emptyValueResponse={{
			exhibition: [],
			exhibition_aggregated: [{ count: { id: 0 } }],
		}}
		onRemoveSelectedOption={handleRemoveSelectedOption}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line2: 'hidden',
			},
			selectedOption: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				button: 'max-w-full',
				wrapper: 'py-1.5 px-2 bg-white border border-gray-200',
				line2: 'hidden',
			},
		}}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		class={classNames({
			'max-w-[calc(100%-20px)]': !!exhibitionSelectedOption,
		})}
		{getOptions}
		{getVariables}
		document={GetExhibitionsDocument}
		{value}
		bind:selectedOption={exhibitionSelectedOption}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`match-existing-exhibition-city-autocomplete`}
				>No exhibition found.</NoResults
			>{/snippet}
	</QueryAutocomplete>
	{#if exhibitionSelectedOption}
		<button
			class="absolute right-[2rem] top-[2.375rem]"
			onclick={handleRemoveSelectedOption}
		>
			<CrossIcon class="h-4 w-4" />
		</button>
	{/if}
</div>
