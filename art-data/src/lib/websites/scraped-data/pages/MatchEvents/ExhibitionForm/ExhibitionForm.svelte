<script lang="ts">
	import dayjs from 'dayjs';
	import { CreateNewExhibition } from './CreateNewExhibition';
	import type { ExhibitionImageType } from './CreateNewExhibition/CreateNewExhibition.svelte';
	import type { OrganiserType } from './CreateNewExhibition/ExhibitionOrganisers/ExhibitionOrganisersRow/ExhibitionOrganisersRow.svelte';
	import { MatchExistingExhibition } from './MatchExistingExhibition';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';
	import type { CreateExhibitionItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createExhibition.generated';
	import type { ExhibitionSearchQuery } from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/exhibitionSearch.generated';

	interface Props {
		exhibitionImage: ExhibitionImageType;
		exhibitionMatches?: ExhibitionSearchQuery['exhibitionSearch']['data'];
		matchId: string;
		exhibitionSelectedOption: OptionType | null;
		exhibitionCitySelectedOption: OptionType | null;
		exhibitionDetailsForm: Omit<
			CreateExhibitionItemMutationVariables['data'],
			'organisers'
		> & {
			organisers?: OrganiserType[];
		};
	}

	let {
		exhibitionImage = $bindable(),
		exhibitionSelectedOption = $bindable(),
		exhibitionMatches = [],
		matchId = $bindable(),
		exhibitionCitySelectedOption = $bindable(),
		exhibitionDetailsForm = $bindable(),
	}: Props = $props();

	const dataCy = 'match-events-exhibition-form';
</script>

{#each exhibitionMatches.slice(0, 5) as exhibitionMatch}
	{@const exhibitionInfo = [
		exhibitionMatch.organisers?.[0]?.entity_id?.name,
		exhibitionMatch.organisers?.[0]?.entity_id?.addresses?.[0]?.city?.name,
		exhibitionMatch.organisers?.[0]?.entity_id?.addresses?.[0]?.country?.name,
		[
			exhibitionMatch.local_start_date
				? dayjs(exhibitionMatch.local_start_date).format('DD/MM/YYYY')
				: null,
			exhibitionMatch.local_end_date
				? dayjs(exhibitionMatch.local_end_date).format('DD/MM/YYYY')
				: null,
		]
			.filter(Boolean)
			.join(' - '),
	]
		.filter(Boolean)
		.join(', ')}

	<div class="mb-2 flex items-center border border-gray-200 p-3">
		<Radio
			bind:group={matchId}
			value={`${exhibitionMatch.id}`}
			dataCy="match-events-auction-form"
		/>
		<div class="ml-3.5 leading-[1.25rem]">
			<Txt variant="label3" class="inline">Match with</Txt>
			<Txt
				class="text-blue-500 inline"
				variant="body2"
				component="a"
				target="_blank"
				rel="noopener noreferrer"
				href={`${Config.ArteyeDomain}/fairs/${exhibitionMatch.id}`}
				>{exhibitionMatch.title}</Txt
			>
			{#if exhibitionInfo}
				<Txt class="ml-[2px] text-gray-500 inline" variant="body2"
					>({exhibitionInfo})</Txt
				>
			{/if}
		</div>
	</div>
{/each}

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="match"
			dataCy="match-events-match-exhibition"
		/>
		<div class="pl-1.5">Match with exhibition ID</div>
	</InputLabel>

	{#if matchId === 'match'}
		<div class="ml-[31px] mt-8 pb-8">
			<MatchExistingExhibition bind:exhibitionSelectedOption />
		</div>
	{/if}
</div>

<div class="mb-2 items-center border border-gray-200 p-3">
	<InputLabel {dataCy} classes={{ container: 'mr-2 w-auto' }}>
		<Radio
			bind:group={matchId}
			value="new"
			dataCy="match-events-gallery-form"
		/>
		<div class="pl-1.5">Create new exhibition</div>
	</InputLabel>

	{#if matchId === 'new'}
		<div class="ml-[31px] mt-8 pb-8">
			<CreateNewExhibition
				bind:exhibitionImage
				bind:exhibitionCitySelectedOption
				bind:exhibitionDetailsForm
			/>
		</div>
	{/if}
</div>

<div class="mb-8">
	<LinkButton
		variant="secondary"
		size="md"
		{dataCy}
		href={`${Config.ArteyeDomain}/exhibitions`}
		newTab
	>
		search on arteye
		{#snippet trailing()}
			<ExternalIcon />
		{/snippet}
	</LinkButton>
</div>
