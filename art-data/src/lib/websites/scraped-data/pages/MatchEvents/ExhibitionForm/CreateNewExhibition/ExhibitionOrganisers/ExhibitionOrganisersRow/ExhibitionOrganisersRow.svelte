<script lang="ts" module>
	export type OrganiserType = NonNullable<
		NonNullable<GetEntitiesQuery['entity']>[number]
	> & {
		isDeleted?: boolean;
		isNew?: boolean;
	};
</script>

<script lang="ts">
	import { OrganisersNameAutoComplete } from './OrganisersNameAutoComplete';
	import { OrganisersFieldName } from '.';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import type { GetEntitiesQuery } from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	interface Props {
		index: number;
		organiser: OrganiserType;
		headers: { fieldName?: OrganisersFieldName }[];
		selectedOption?: OptionType | null;
		onChange: (
			organiser: GetEntitiesQuery['entity'][number] | Record<never, never>
		) => void;
		onDelete: (organiser: GetEntitiesQuery['entity'][number]) => void;
	}

	let {
		index,
		organiser,
		headers,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
	}: Props = $props();

	const dataCy = 'organiser-row';

	const handleDeleteOrganiser = () => {
		onDelete(organiser);
	};

	const onRemoveSelectedEntity = () => {
		onChange({});
	};

	const handleEntityChange = (e: {
		detail: {
			value: GetEntitiesQuery['entity'][number];
		};
	}) => {
		onChange(e.detail.value);

		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<Button
					onclick={handleDeleteOrganiser}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</TableActionCell>
		{:else if header.fieldName === OrganisersFieldName.Name}
			<TableCell {dataCy}>
				{#snippet custom()}
					<OrganisersNameAutoComplete
						bind:selectedOption
						{onRemoveSelectedEntity}
						{dataCy}
						onChange={handleEntityChange}
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
