<script lang="ts" module>
	export type ExhibitionImageType =
		| (NonNullable<
				CreateExhibitionItemMutationVariables['data']['cover_image']
		  > & { file?: File; url?: string | null })
		| null
		| undefined;
</script>

<script lang="ts">
	import { ExhibitionAddress } from './ExhibitionAddress';
	import { ExhibitionOrganisers } from './ExhibitionOrganisers';
	import { type OrganiserType } from './ExhibitionOrganisers/ExhibitionOrganisersRow/ExhibitionOrganisersRow.svelte';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Dropzone } from '$global/components/Dropzone';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { type SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
	import { type CreateExhibitionItemMutationVariables } from '$lib/websites/scraped-data/arteye-queries/__generated__/createExhibition.generated';
	import type { ScrapedDataMatchEventsPageData } from '$routes/scraped-data/match-events/types';

	interface Props {
		exhibitionImage: ExhibitionImageType;
		exhibitionCitySelectedOption: OptionType | null;
		exhibitionDetailsForm: Omit<
			CreateExhibitionItemMutationVariables['data'],
			'organisers'
		> & {
			organisers?: OrganiserType[];
		};
	}

	let {
		exhibitionImage = $bindable(),
		exhibitionCitySelectedOption = $bindable(),
		exhibitionDetailsForm = $bindable(),
	}: Props = $props();

	let selectedTimezone = $state(
		exhibitionDetailsForm?.timezone
			? exhibitionDetailsForm?.timezone.timezone
			: null
	);

	let data = $derived(getPageData<ScrapedDataMatchEventsPageData>(page.data));
	let timezones = $derived(data.timezones);

	let timezonesOptions = $derived(
		timezones?.map((timezone) => ({
			label: `${timezone?.timezone}`,
			value: `${timezone?.timezone}`,
		}))
	);
	let attributeTypes = $derived(data.attributeTypes);

	let attributeOptions = $derived(
		attributeTypes?.map((attribute) => ({
			label: `${attribute?.name}`,
			value: `${attribute?.key}`,
		}))
	);

	const dataCy = 'create-new-exhibition';

	const accept = ['image/png', 'image/jpeg'];
	const maxSize = 100000000;

	let files: DropzoneFile[] = $state([]);

	const handleSubmitProfileImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		exhibitionImage = {
			file: newFile,
			url: base64Image.url,
			filename_download: '',
			id: '',
			storage: '',
		};
	};

	const handleChangeTimezone = (event: SelectChangeEvent) => {
		const timezone = timezones.find(
			(timezone) => timezone.timezone === event.detail.value
		);

		if (timezone) {
			exhibitionDetailsForm = {
				...exhibitionDetailsForm,
				timezone,
			};
		}
	};

	const handleClickDeleteImage = () => {
		exhibitionImage = null;
	};

	const handleOrganisersChange = (entities: OrganiserType[]) => {
		exhibitionDetailsForm = {
			...exhibitionDetailsForm,
			organisers: entities,
		};
	};

	const handleInputChange =
		(field: keyof typeof exhibitionDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;

			exhibitionDetailsForm = {
				...exhibitionDetailsForm,
				[field]: value,
			};
		};

	const handleAttributeTypesChange = (
		event: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		let attributes = selected.map((sel) => {
			let match = (exhibitionDetailsForm.attributes || []).find(
				(ex) => ex?.type?.key === sel.value
			);

			if (match) {
				return match;
			} else {
				return {
					type: { key: sel.value, name: sel.label },
					exhibition: { id: exhibitionDetailsForm.id },
				};
			}
		}) as CreateExhibitionItemMutationVariables['data']['organisers'];

		exhibitionDetailsForm = {
			...exhibitionDetailsForm,
			attributes,
		};
	};
</script>

<div class="mb-4 grid grid-cols-5 gap-4">
	<div class="col-span-2">
		<Input
			dataCy={`${dataCy}-title`}
			name="title"
			placeholder="Title"
			required
			label="Title"
			value={exhibitionDetailsForm.title || ''}
			size="sm"
			onkeyup={handleInputChange('title')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-exhibitionUrl`}
			name="exhibitionUrl"
			placeholder="Exhibition URL"
			label="Exhibition URL"
			value={exhibitionDetailsForm.exhibition_url || ''}
			size="sm"
			onkeyup={handleInputChange('exhibition_url')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-startDate`}
			name="startDate"
			placeholder="Start Date"
			label="Start Date"
			type="date"
			value={getDayFromDirectus(exhibitionDetailsForm.start_date || '')}
			size="sm"
			onkeyup={handleInputChange('start_date')}
			onchange={handleInputChange('start_date')}
		/>
	</div>

	<div class="col-span-1">
		<Input
			dataCy={`${dataCy}-endDate`}
			name="endDate"
			placeholder="End Date"
			label="End Date"
			type="date"
			value={getDayFromDirectus(exhibitionDetailsForm.end_date || '')}
			size="sm"
			onkeyup={handleInputChange('end_date')}
			onchange={handleInputChange('end_date')}
		/>
	</div>

	<div class="col-span-1">
		<InputLabel {dataCy} required variant="label3" class="mb-2"
			>Time zone</InputLabel
		>
		<Select
			ariaLabel="Select a time zone"
			dataCy={`${dataCy}-time-zone`}
			name="time-zone"
			placeholder="-Select-"
			options={timezonesOptions}
			bind:value={selectedTimezone}
			size="sm"
			onchange={handleChangeTimezone}
		/>
	</div>
</div>

<div class="mb-8 grid grid-cols-5 gap-4">
	<div class="col-span-4">
		<Input
			dataCy={`${dataCy}-description`}
			name="description"
			placeholder="Description"
			rows={5}
			label="Description"
			value={exhibitionDetailsForm?.description || ''}
			size="sm"
			onkeyup={handleInputChange('description')}
		/>
	</div>

	<MultiSelect
		name="attributes"
		dataCy={`${dataCy}-attributes`}
		label="Attributes"
		required
		selected={(exhibitionDetailsForm.attributes || []).map((attribute) => ({
			label: `${attribute?.type?.name}`,
			value: `${attribute?.type?.key}`,
		}))}
		placeholder="Type or select"
		options={attributeOptions}
		class="col-span-1"
		size="sm"
		onChange={handleAttributeTypesChange}
	/>
</div>

<ExhibitionAddress
	bind:exhibitionCitySelectedOption
	bind:exhibitionDetailsForm
	{handleInputChange}
/>
<ExhibitionOrganisers
	organisers={exhibitionDetailsForm.organisers}
	onChange={handleOrganisersChange}
/>

<InputLabel dataCy={`${dataCy}-cover-image`} class="mb-2 mt-8"
	>Upload cover image</InputLabel
>
<div class="relative flex w-full items-center">
	<div class="relative flex w-full items-center justify-center">
		{#if exhibitionImage}
			<img
				class="max-h-40 object-contain"
				src={exhibitionImage.url}
				alt="profile"
			/>

			<Button
				dataCy={`${dataCy}-delete-image`}
				class="absolute bottom-2 right-2 h-[2rem] w-[2rem] px-0"
				variant="secondary"
				onclick={handleClickDeleteImage}
			>
				<BinIcon class="h-3 w-3" />
			</Button>
		{:else}
			<Dropzone
				{maxSize}
				bind:files
				{dataCy}
				class="h-full w-full"
				showFiles={false}
				{accept}
				onSubmitFiles={handleSubmitProfileImage}
			/>
		{/if}
	</div>
</div>
