<script lang="ts" module>
	export const formatEntityOrganiser = (
		entity: GetEntitiesQuery['entity'][number]
	) => {
		const url = (() => {
			if (entity?.type?.key === 'person') {
				if (entity?.artist) {
					return `${Config.ArteyeDomain}/artists/${entity?.artist?.id}`;
				} else {
					return `${Config.ArteyeDomain}/people/${entity?.person?.id}`;
				}
			}

			return `${Config.ArteyeDomain}/organisations/${entity?.organisation?.id}`;
		})();

		const location = (
			entity?.type?.key === 'person'
				? [
						entity?.addresses?.[0]?.city?.name,
						entity?.addresses?.[0]?.country?.name,
					]
				: [
						entity?.organisation?.location?.name,
						entity?.organisation?.location?.country?.name,
					]
		)
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Config } from '$lib/constants/config';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetEntitiesDocument,
		type GetEntitiesQuery,
		type GetEntitiesQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	let entities: GetEntitiesQuery['entity'] | [] = [];

	interface Props {
		onRemoveSelectedEntity: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: GetEntitiesQuery['entity'][number];
					};
			  }) => Promise<void>);
	}

	let {
		onRemoveSelectedEntity,
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: (() => {
				if (!value) {
					return { status: { key: { _neq: 'archived' } } };
				}

				if (!isUuidValid(value)) {
					return {
						_and: [
							{ name: { _icontains: value } },
							{ status: { key: { _neq: 'archived' } } },
						],
					};
				}

				return {
					_and: [
						{
							_or: [
								{
									id: { _eq: value },
								},
								{
									artist: {
										id: { _eq: value },
									},
								},
								{
									organisation: {
										id: { _eq: value },
									},
								},
								{ gallery: { id: { _eq: value } } },
							],
						},
						{ status: { key: { _neq: 'archived' } } },
					],
				};
			})(),
		};
	};

	const getOptions = (data: GetEntitiesQuery | undefined) => {
		entities = data?.entity || [];
		return [...(data?.entity || []).map(formatEntityOrganiser)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		onRemoveSelectedOption={onRemoveSelectedEntity}
		emptyValueResponse={{ entity: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		{getOptions}
		{getVariables}
		document={GetEntitiesDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-organisers-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				onRemoveSelectedEntity();
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
