<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import timezone from 'dayjs/plugin/timezone';
	import { onMount } from 'svelte';
	import type { CreateAuctionItemMutationVariables } from '../../arteye-queries/__generated__/createAuction.generated';
	import type { CreateExhibitionItemMutationVariables } from '../../arteye-queries/__generated__/createExhibition.generated';
	import type { CreateFairItemMutationVariables } from '../../arteye-queries/__generated__/createFair.generated';
	import type { GetEntitiesQuery } from '../../arteye-queries/__generated__/getEntities.generated';
	import { GetEntitiesDocument } from '../../arteye-queries/__generated__/getEntities.generated';
	import { GetFairOrganisationsDocument } from '../../arteye-queries/__generated__/getFairOrganisations.generated';
	import { GetLocationsDocument } from '../../arteye-queries/__generated__/getLocations.generated';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import {
		AuctionSearchDocument,
		type AuctionSearchQuery,
	} from '../../custom-arteye-queries/__generated__/auctionSearch.generated';
	import {
		ExhibitionSearchDocument,
		type ExhibitionSearchQuery,
	} from '../../custom-arteye-queries/__generated__/exhibitionSearch.generated';
	import {
		FairSearchDocument,
		type FairSearchQuery,
	} from '../../custom-arteye-queries/__generated__/fairSearch.generated';
	import { OrganisationSearchDocument } from '../../custom-arteye-queries/__generated__/organisationSearch.generated';
	import { GetScrapedEventsDocument } from '../../custom-queries/__generated__/getScrapedEvents.generated';
	import type { GetScrapedEventsQuery } from '../../custom-queries/__generated__/getScrapedEvents.generated';
	import { createAuction } from '../../utils/createAuction/createAuction';
	import { createExhibition } from '../../utils/createExhibition/createExhibition';
	import { createFair } from '../../utils/createFair/createFair';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { AuctionForm } from './AuctionForm';
	import { getVariables } from './AuctionForm/CreateNewAuction/AuctionHouseAutoComplete';
	import { formatEntity } from './AuctionForm/CreateNewAuction/AuctionHouseAutoComplete/AuctionHouseAutoComplete.svelte';
	import { ExhibitionForm } from './ExhibitionForm';
	import type { ExhibitionImageType } from './ExhibitionForm/CreateNewExhibition/CreateNewExhibition.svelte';
	import type { OrganiserType } from './ExhibitionForm/CreateNewExhibition/ExhibitionOrganisers/ExhibitionOrganisersRow/ExhibitionOrganisersRow.svelte';
	import { FairForm } from './FairForm';
	import {
		formatLocation,
		getCityVariables,
	} from './FairForm/CreateNewFair/CityAutocomplete/CityAutocomplete.svelte';
	import { getVariables as getFairOrganisationsVariables } from './FairForm/CreateNewFair/FairOrganiserAutoComplete';
	import { formatOrganisation } from './FairForm/CreateNewFair/FairOrganiserAutoComplete/FairOrganiserAutoComplete.svelte';
	import { ShowMatchedEvents } from './ShowMatchedEvents';
	import { areTimezonesValid } from './utils/areTimezonesValid/areTimezonesValid';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { capitalizeFirstLetters } from '$global/utils/capitalizeFirstLetters';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';
	import { scrollToTop } from '$global/utils/scrollToTop';
	import type { Create_Fair_Input } from '$gql/types-arteye';
	import {
		AuctionSearchSortField,
		ExhibitionSearchSortField,
		FairSearchSortField,
		SortDirection,
	} from '$gql/types-arteye-custom';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
	import { UpdateScrapedEventDocument } from '$lib/queries/__generated__/updateScrapedEvent.generated';
	import type { ScrapedDataMatchEventsPageData } from '$routes/scraped-data/match-events/types';

	type Event =
		GetScrapedEventsQuery['getScrapedArtworks']['unsentScrapedEvents'][number]['art_event_feed']; // GetArtworkFeedQuery['artwork_feed'][number]['event_id'];

	let unsentScrapedEvents:
		| null
		| GetScrapedEventsQuery['getScrapedArtworks']['unsentScrapedEvents'] =
		$state(null);
	let eventsToMatch: null | undefined | Event[] = $state(null);

	let data = $derived(getPageData<ScrapedDataMatchEventsPageData>(page.data));
	let timezones = $derived(data.timezones);
	let currentEventIndex: number | undefined = $state();

	dayjs.extend(timezone);

	$effect(() => {
		currentEventIndex = (eventsToMatch || []).findIndex((event) => {
			return !event?.processor_review_event?.[0]?.processed_event_id;
		});
	});

	let currentEvent = $derived(
		currentEventIndex !== undefined && currentEventIndex > -1
			? eventsToMatch?.[currentEventIndex]
			: undefined
	);
	let loading = $state(true);
	let showLandingPage = $state(true);
	let submitting = $state(false);
	let matchId = $state('');

	// Auction state
	let auctionMatches: AuctionSearchQuery['auctionSearch']['data'] = $state([]);
	let auctionSelectedOption: OptionType | null = $state(null);
	let auctionHouseSelectedOption: OptionType | null = $state(null);
	let auctionDetailsForm: CreateAuctionItemMutationVariables['data'] & {
		auction_start_date_date?: string | null | undefined;
		auction_start_date_time?: string | null | undefined;
		auction_end_date_date?: string | null | undefined;
		auction_end_date_time?: string | null | undefined;
	} = $state({});

	// Fair state
	let fairMatches: FairSearchQuery['fairSearch']['data'] = $state([]);
	let fairSelectedOption: OptionType | null = $state(null);
	let fairCitySelectedOption: OptionType | null = $state(null);
	let fairOrganisationSelectedOption: OptionType | null = $state(null);
	let fairDetailsForm: CreateFairItemMutationVariables['data'] = $state({
		title: '',
	});

	// Exhibition state
	let exhibitionMatches: ExhibitionSearchQuery['exhibitionSearch']['data'] =
		$state([]);
	let exhibitionSelectedOption: OptionType | null = $state(null);
	let exhibitionCitySelectedOption: OptionType | null = $state(null);
	let exhibitionImage: ExhibitionImageType = $state(null);
	let exhibitionDetailsForm: Omit<
		CreateExhibitionItemMutationVariables['data'],
		'organisers'
	> & {
		organisers?: OrganiserType[];
	} = $state({
		title: '',
	});

	const dataCyPrefix = 'scraped-data-match-events';
	const { artworkFeeds, saveStage } = getArtworkFeedsStore();

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Match Events' },
	];

	let isFair = $derived(
		`${(currentEvent as Event)?.event_type?.key?.toLowerCase()}` === 'fair'
	);

	let isExhibition = $derived(
		`${(currentEvent as Event)?.event_type?.key?.toLowerCase()}`.includes(
			'exhibition'
		)
	);

	let isAuction = $derived(
		`${(currentEvent as Event)?.event_type?.key?.toLowerCase()}`.includes(
			'auction'
		)
	);

	let isExhibitionInvalid = $derived(
		!exhibitionDetailsForm?.title ||
			!(exhibitionDetailsForm?.organisers || []).filter(
				(organiser) => !organiser?.isDeleted
			).length ||
			!(exhibitionDetailsForm?.organisers || []).every(
				(organiser) =>
					organiser?.isDeleted || (!organiser?.isDeleted && organiser?.id)
			)
	);

	let isFairInvalid = $derived(
		(fairDetailsForm?.fair_url && !isValidUrl(fairDetailsForm?.fair_url)) ||
			!fairDetailsForm?.fair_organisation ||
			!fairDetailsForm?.title ||
			!fairDetailsForm?.start_date ||
			!fairDetailsForm?.end_date ||
			dayjs(fairDetailsForm?.start_date) > dayjs(fairDetailsForm?.end_date)
	);

	let isAuctionInvalid = $derived(
		!auctionDetailsForm.auction_house ||
			!auctionDetailsForm?.auction_types?.length ||
			!auctionDetailsForm?.sale_name ||
			!auctionDetailsForm?.currency /* ||
			!auctionDetailsForm?.auction_timezone ||
			(!!auctionDetailsForm?.auction_start_date_date &&
				!auctionDetailsForm?.auction_start_date_time) ||
			(!!auctionDetailsForm?.auction_start_date_time &&
				!auctionDetailsForm?.auction_start_date_date) ||
			(!!auctionDetailsForm?.auction_end_date_date &&
				!auctionDetailsForm?.auction_end_date_time) ||
			(!!auctionDetailsForm?.auction_end_date_time &&
				!auctionDetailsForm?.auction_end_date_date) */
	);

	let isInvalid = $derived(
		(() => {
			if (isAuction) {
				return isAuctionInvalid;
			}

			if (isFair) {
				return isFairInvalid;
			}

			if (isExhibition) {
				return isExhibitionInvalid;
			}

			return true;
		})()
	);

	const handleFetchAuctionHouse = async (value: string) => {
		const auctionHouseRes = await gqlClientArteye.request(
			GetEntitiesDocument,
			getVariables(value),
			getAuthorizationHeaders({
				user: { access_token: data.user?.arteye_token },
			})
		);

		const auctionHouse = auctionHouseRes?.entity?.[0];

		if (auctionHouse) {
			auctionHouseSelectedOption = formatEntity(auctionHouse);
			return auctionHouse;
		}

		return null;
	};

	const handleFetchExhibitionOrganiserAndCity = async (
		organiserValue: string | null | undefined,
		cityValue: string | null | undefined
	) => {
		let res: {
			exhibitionOrganiser: Parameters<typeof formatEntity>[0] | null;
			city: Parameters<typeof formatLocation>[0] | null;
		} = {
			exhibitionOrganiser: null,
			city: null,
		};

		if (organiserValue) {
			const exhibitionOrganiserRes = await gqlClientArteye.request(
				GetEntitiesDocument,
				{ filter: { name: { _eq: organiserValue } } },
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			res.exhibitionOrganiser = exhibitionOrganiserRes?.entity?.[0];
		}

		if (cityValue) {
			const cityRes = await gqlClientArteye.request(
				GetLocationsDocument,
				getCityVariables(cityValue),
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			res.city = cityRes?.location?.[0];

			if (res.city) {
				exhibitionCitySelectedOption = formatLocation(res.city);
			}
		}

		return res;
	};

	const handleFetchFairOrganiserAndCity = async (
		organiserValue: string | null | undefined,
		cityValue: string | null | undefined
	) => {
		let res: {
			fairOrganiser: Parameters<typeof formatOrganisation>[0] | null;
			city: Parameters<typeof formatLocation>[0] | null;
		} = {
			fairOrganiser: null,
			city: null,
		};

		if (organiserValue) {
			const fairOrganiserRes = await gqlClientCustomArteye.request(
				OrganisationSearchDocument,
				getFairOrganisationsVariables(organiserValue),
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			res.fairOrganiser = fairOrganiserRes?.organisationSearch?.data?.[0];

			if (res.fairOrganiser) {
				fairOrganisationSelectedOption = formatOrganisation(res.fairOrganiser);
			}
		}

		if (cityValue) {
			const cityRes = await gqlClientArteye.request(
				GetLocationsDocument,
				getCityVariables(cityValue),
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			res.city = cityRes?.location?.[0];

			if (res.city) {
				fairCitySelectedOption = formatLocation(res.city);
			}
		}

		return res;
	};

	const prefillExhibitionForm = async (
		event: Event,
		entities: null | {
			exhibitionOrganiser: Parameters<typeof formatEntity>[0] | null;
			city: Parameters<typeof formatLocation>[0] | null;
		}
	) => {
		const { startTz, endTz } = areTimezonesValid(
			event?.starts_at_tz,
			event?.ends_at_tz
		);

		const exhibitionMatchesRes = await gqlClientCustomArteye.request(
			ExhibitionSearchDocument,
			{
				input: {
					sort: [
						{
							field: ExhibitionSearchSortField.StartDate,
							direction: SortDirection.Desc,
						},
					],
					filters: {
						...(event?.title && {
							title: event?.title,
						}),
						...(event?.organization && {
							organiserNameOrId: event?.organization,
						}),
						...(event?.starts_at && {
							localStartDate: {
								min: dayjs(event?.starts_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
						...(event?.ends_at && {
							localEndDate: {
								min: dayjs(event?.ends_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
					},
				},
			},
			getAuthorizationHeaders({
				user: { access_token: page.data.user.arteye_token },
			})
		);

		exhibitionMatches = exhibitionMatchesRes?.exhibitionSearch?.data;

		exhibitionDetailsForm = {
			title: event?.title || '',
			exhibition_url:
				(event?.url || event?.pdf_url) &&
				isValidUrl((event?.url || event?.pdf_url) as string)
					? event?.url || event?.pdf_url
					: '',
			start_date: event?.starts_at
				? dayjs(event?.starts_at)
						.tz(startTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			end_date: event?.ends_at
				? dayjs(event?.ends_at)
						.tz(endTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			description: event?.description,
			organisers: [
				...(entities?.exhibitionOrganiser
					? [entities?.exhibitionOrganiser]
					: []),
			],
			timezone:
				event?.starts_at_tz || event?.ends_at_tz
					? timezones.find(
							(timezone) =>
								timezone.timezone ===
									(event?.starts_at_tz || event?.ends_at_tz) ||
								timezone.timezone ===
									`Etc/${event?.starts_at_tz || event?.ends_at_tz}`
						)
					: null,
			...(entities?.city && {
				venue_city: {
					name: `${entities?.city?.name}`,
					short_code: `${entities?.city?.short_code}`,
					code: `${entities?.city?.code}`,
				},
			}),
			...(entities?.city?.country && {
				venue_country: {
					name: `${entities?.city?.country?.name}`,
					short_code: `${entities?.city?.country?.short_code}`,
					code: `${entities?.city?.country?.code}`,
				},
			}),
		};
	};

	const prefillFairForm = async (
		event: Event,
		entities: null | {
			fairOrganiser: Parameters<typeof formatOrganisation>[0] | null;
			city: Parameters<typeof formatLocation>[0] | null;
		}
	) => {
		const { startTz, endTz } = areTimezonesValid(
			event?.starts_at_tz,
			event?.ends_at_tz
		);

		const fairMatchesRes = await gqlClientCustomArteye.request(
			FairSearchDocument,
			{
				input: {
					sort: [
						{
							field: FairSearchSortField.StartDate,
							direction: SortDirection.Desc,
						},
					],
					filters: {
						...(event?.title && {
							titleOrId: event?.title,
						}),
						...(event?.location &&
							event?.location !== 'Online' && {
								location: {
									cityNameOrCode: event?.location,
								},
							}),
						...(event?.starts_at && {
							localStartDate: {
								min: dayjs(event?.starts_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
						...(event?.ends_at && {
							localEndDate: {
								min: dayjs(event?.ends_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
					},
				},
			},
			getAuthorizationHeaders({
				user: { access_token: page.data.user.arteye_token },
			})
		);

		fairMatches = fairMatchesRes?.fairSearch?.data;

		let fairOrganisationId: string | null | undefined;

		if (entities?.fairOrganiser) {
			const fairOrganisationRes = await gqlClientArteye.request(
				GetFairOrganisationsDocument,
				{
					filter: {
						organisation: { id: { _eq: entities?.fairOrganiser?.id } },
					},
				},
				getAuthorizationHeaders({
					user: { access_token: page.data.user.arteye_token },
				})
			);

			fairOrganisationId = fairOrganisationRes?.fair_organisation?.[0]?.id;
		}

		fairDetailsForm = {
			title: event?.title || '',
			fair_url:
				(event?.url || event?.pdf_url) &&
				isValidUrl((event?.url || event?.pdf_url) as string)
					? event?.url || event?.pdf_url
					: '',
			start_date: event?.starts_at
				? dayjs(event?.starts_at)
						.tz(startTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			end_date: event?.ends_at
				? dayjs(event?.ends_at)
						.tz(endTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			fair_organisation: (fairOrganisationId
				? { id: fairOrganisationId }
				: {
						organisation: entities?.fairOrganiser
							? {
									id: entities?.fairOrganiser?.id,
									name: entities?.fairOrganiser?.name,
									type: entities?.fairOrganiser?.type,
								}
							: null,
					}) as Create_Fair_Input['fair_organisation'],
			timezone:
				event?.starts_at_tz || event?.ends_at_tz
					? timezones.find(
							(timezone) =>
								timezone.timezone ===
									(event?.starts_at_tz || event?.ends_at_tz) ||
								timezone.timezone ===
									`Etc/${event?.starts_at_tz || event?.ends_at_tz}`
						)
					: null,
			...(entities?.city && {
				venue_city: {
					name: `${entities?.city?.name}`,
					short_code: `${entities?.city?.short_code}`,
					code: `${entities?.city?.code}`,
				},
			}),
			...(entities?.city?.country && {
				venue_country: {
					name: `${entities?.city?.country?.name}`,
					short_code: `${entities?.city?.country?.short_code}`,
					code: `${entities?.city?.country?.code}`,
				},
			}),
		};
	};

	const prefillAuctionForm = async (
		event: Event,
		auctionHouseEntity: GetEntitiesQuery['entity'][number] | null
	) => {
		const { startTz, endTz } = areTimezonesValid(
			event?.starts_at_tz,
			event?.ends_at_tz
		);

		const auctionMatchesRes = await gqlClientCustomArteye.request(
			AuctionSearchDocument,
			{
				input: {
					sort: [
						{
							field: AuctionSearchSortField.AuctionStartDate,
							direction: SortDirection.Desc,
						},
					],
					filters: {
						...(event?.title && {
							auctionNameOrId: event?.title,
						}),
						...(event?.organization && {
							auctionHouseOrId: event?.organization,
						}),
						...(event?.starts_at && {
							localStartDate: {
								min: dayjs(event?.starts_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
						...(event?.ends_at && {
							localEndDate: {
								min: dayjs(event?.ends_at).format('YYYY-MM-DD'),
								operator: ValueFilterOperator.Equal,
							},
						}),
					},
				},
			},
			getAuthorizationHeaders({
				user: { access_token: page.data.user.arteye_token },
			})
		);

		auctionMatches = auctionMatchesRes?.auctionSearch?.data;

		const currency = event?.artwork_feed?.find(
			(feed) => feed?.price_currency
		)?.price_currency;

		auctionDetailsForm = {
			...(auctionHouseEntity && {
				auction_house: {
					organisation: auctionHouseEntity.organisation,
				},
			}),
			currency: currency
				? {
						code: `${currency.code}`,
						name: `${currency.name}`,
					}
				: null,
			sale_name: event?.title || '',
			sale_number: event?.sale_number || '',
			sale_url:
				(event?.url || event?.pdf_url) &&
				isValidUrl((event?.url || event?.pdf_url) as string)
					? event?.url || event?.pdf_url
					: '',
			auction_start_date_date: event?.starts_at
				? dayjs(event?.starts_at)
						.tz(startTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			auction_start_date_time: event?.starts_at
				? dayjs(event?.starts_at)
						.tz(startTz || 'UTC')
						.format('HH:mm')
				: null,
			auction_end_date_date: event?.ends_at
				? dayjs(event?.ends_at)
						.tz(endTz || 'UTC')
						.format('YYYY-MM-DD')
				: null,
			auction_end_date_time: event?.ends_at
				? dayjs(event?.ends_at)
						.tz(endTz || 'UTC')
						.format('HH:mm')
				: null,
			auction_timezone:
				event?.starts_at_tz || event?.ends_at_tz
					? timezones.find(
							(timezone) =>
								timezone.timezone ===
									(event?.starts_at_tz || event?.ends_at_tz) ||
								timezone.timezone ===
									`Etc/${event?.starts_at_tz || event?.ends_at_tz}`
						)
					: null,
			auction_types: [
				...(event?.is_charity_fundraiser
					? [
							{
								auction_type_key: {
									key: 'CHARITY',
									name: 'charity',
								},
								auction_id: {},
							},
						]
					: []),
				...(event?.event_type?.key?.startsWith('online')
					? [
							{
								auction_type_key: {
									key: 'ONLINE',
									name: 'Online',
								},
								auction_id: {},
							},
						]
					: []),
				...(event?.event_type?.key?.startsWith('live')
					? [
							{
								auction_type_key: {
									key: 'LIVE',
									name: 'Live',
								},
								auction_id: {},
							},
						]
					: []),
			],
		};
	};

	const prefillForm = async (event: Event) => {
		if (event?.event_type?.key?.includes('auction')) {
			let auctionHouseEntity = null;

			if (event.organization) {
				auctionHouseEntity = await handleFetchAuctionHouse(event.organization);
			}

			await prefillAuctionForm(event, auctionHouseEntity);
		} else if (event?.event_type?.key?.includes('exhibition')) {
			let entities = null;

			if (event.location || event.organization) {
				entities = await handleFetchExhibitionOrganiserAndCity(
					event.organization,
					event.location
				);
			}

			await prefillExhibitionForm(event, entities);
		} else if (event?.event_type?.key?.includes('fair')) {
			let entities = null;

			if (event.location || event.organization) {
				entities = await handleFetchFairOrganiserAndCity(
					event.organization,
					event.location
				);
			}

			await prefillFairForm(event, entities);
		}

		loading = false;
	};

	onMount(() => {
		const fetchArtworkFeeds = async () => {
			saveStage();
			const res = await gqlClientCustom.request(
				GetScrapedEventsDocument,
				{
					input: { ids: $artworkFeeds.currentIds },
				},
				getAuthorizationHeaders(data)
			);

			unsentScrapedEvents = res.getScrapedArtworks.unsentScrapedEvents.filter(
				(scrapedEvent) =>
					!['private_sale', 'museum_acquisition'].includes(
						scrapedEvent?.art_event_feed?.event_type?.key?.toLowerCase() as string
					)
			);

			eventsToMatch = unsentScrapedEvents.map(
				(artworkFeed) => artworkFeed?.art_event_feed
			);

			prefillForm(eventsToMatch[0]);
		};

		fetchArtworkFeeds();
	});

	const handleClickNext = () => {
		submitting = true;
		goto(Routes.ScrapedDataProcessDataImages);
	};

	const handleSaveClick = async () => {
		if (!currentEvent || !eventsToMatch || currentEventIndex === undefined) {
			return;
		}

		submitting = true;
		let newId;

		if (matchId === 'new') {
			if (isAuction) {
				const newAuction = await createAuction({
					auctionDetailsForm,
					headers: getAuthorizationHeaders({
						user: { access_token: data.user?.arteye_token },
					}),
				});

				newId = newAuction?.id;
			}

			if (isFair) {
				const newFair = await createFair({
					fairDetailsForm,
					headers: getAuthorizationHeaders({
						user: { access_token: data.user?.arteye_token },
					}),
				});

				newId = newFair?.id;
			}

			if (isExhibition) {
				const newExhibition = await createExhibition({
					exhibitionDetailsForm,
					exhibitionImage,
					headers: getAuthorizationHeaders(data),
				});

				newId = newExhibition?.id;
			}
		}

		const matchedId = (() => {
			if (matchId !== 'match') {
				return '';
			}

			if (isExhibition) {
				return exhibitionSelectedOption?.line2;
			}

			if (isFair) {
				return fairSelectedOption?.line2;
			}

			if (isAuction) {
				return auctionSelectedOption?.line2;
			}
		})();

		try {
			const id = (unsentScrapedEvents || []).find(
				(scrapedEvent) =>
					scrapedEvent?.art_event_feed?.id ===
					eventsToMatch?.[currentEventIndex as number]?.id
			)?.id;

			await gqlClient.request(
				UpdateScrapedEventDocument,
				{
					id: `${id}`,
					data: {
						processed_event_id: newId || matchedId || matchId,
					},
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'The event has been successfully matched.',
			});

			if (
				currentEventIndex !== undefined &&
				currentEventIndex + 1 < eventsToMatch.length - 1
			) {
				loading = true;
				await prefillForm(eventsToMatch[currentEventIndex + 1]);
				currentEventIndex = currentEventIndex + 1;
				scrollToTop();

				matchId = '';
				auctionHouseSelectedOption = null;
				auctionDetailsForm = {};
			} else {
				window.location.reload();
			}
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while matching the event.',
			});
		} finally {
			submitting = false;
		}
	};

	const formatBoolean = (value: boolean | null | undefined) =>
		[null, undefined].includes(value as null | undefined)
			? null
			: value
				? 'Yes'
				: 'No';

	const formatDate = (date: string) =>
		date ? dayjs(date).format('MMM D, YYYY HH:mm') : null;

	const formatDateWithTimezone = (date: string, tz: string) =>
		date ? dayjs(date).tz(tz).format('MMM D, YYYY HH:mm') : null;

	const formatUser = (
		user:
			| {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
			  }
			| null
			| undefined
	) =>
		user ? [user?.first_name, user?.last_name].filter(Boolean).join(' ') : null;

	let infos = $derived(
		(() => {
			const { startTz, endTz } = areTimezonesValid(
				(currentEvent as Event)?.starts_at_tz,
				(currentEvent as Event)?.ends_at_tz
			);

			return [
				{
					label: 'Artlogic Link',
					value: (currentEvent as Event)?.artlogic_link?.[0]?.url,
				},
				{
					label: 'Artwork Feed',
					value: (currentEvent as Event)?.artwork_feed?.[0]?.url,
				},
				{ label: 'Crawl Job', value: (currentEvent as Event)?.crawl_job },
				{
					label: 'Created At',
					value: formatDate((currentEvent as Event)?.created_at),
				},
				{
					label: 'Created By',
					value: formatUser((currentEvent as Event)?.created_by),
				},
				{ label: 'Data Source', value: (currentEvent as Event)?.data_source },
				{
					label: 'Description',
					value: (currentEvent as Event)?.description,
				},
				{
					label: 'Ends At',
					value: formatDateWithTimezone(
						(currentEvent as Event)?.ends_at,
						endTz || 'UTC'
					),
				},
				{
					label: 'Ends At TZ',
					value: (currentEvent as Event)?.ends_at_tz
						? `${(currentEvent as Event)?.ends_at_tz}${endTz ? '' : ' (Cannot convert to local time, scraped timezone invalid)'}`
						: null,
				},
				{
					label: 'Event type',
					value: capitalizeFirstLetters(
						((currentEvent as Event)?.event_type?.key || '').replaceAll(
							'_',
							' '
						)
					),
				},
				{ label: 'External ID', value: (currentEvent as Event)?.external_id },
				{ label: 'ID', value: (currentEvent as Event)?.id },
				{ label: 'Image URL', value: (currentEvent as Event)?.image_url },
				{
					label: 'Is Charity Fundraiser',
					value: formatBoolean((currentEvent as Event)?.is_charity_fundraiser),
				},
				{
					label: 'Is Closed',
					value: formatBoolean((currentEvent as Event)?.is_closed),
				},
				{ label: 'Location', value: (currentEvent as Event)?.location },
				{ label: 'Organization', value: (currentEvent as Event)?.organization },
				{ label: 'PDF URL', value: (currentEvent as Event)?.pdf_url },
				{ label: 'Sale Number', value: (currentEvent as Event)?.sale_number },
				{
					label: 'Starts At',
					value: formatDateWithTimezone(
						(currentEvent as Event)?.starts_at,
						startTz || 'UTC'
					),
				},
				{
					label: 'Starts At TZ',
					value: (currentEvent as Event)?.starts_at_tz
						? `${(currentEvent as Event)?.starts_at_tz}${startTz ? '' : ' (Cannot convert to local time, scraped timezone invalid)'}`
						: null,
				},
				{ label: 'Title', value: (currentEvent as Event)?.title },
				{
					label: 'Updated At',
					value: formatDate((currentEvent as Event)?.updated_at),
				},
				{
					label: 'Updated By',
					value: formatUser((currentEvent as Event)?.updated_by),
				},
				{ label: 'URL', value: (currentEvent as Event)?.url },
			];
		})()
	);

	let missingInfo = $derived(
		[
			...infos.map((info) => (!info.value ? info.label : '')).filter(Boolean),
			...((currentEvent as Event)?.starts_at_tz ? [] : ['Start at timezone']),
			...((currentEvent as Event)?.ends_at_tz ? [] : ['Ends at timezone']),
		].join(', ')
	);
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6 flex items-center justify-between">
			<div>
				<ScrapedDataTabs activeTab={1} />

				{#if !showLandingPage && eventsToMatch?.length && currentEventIndex !== undefined}
					<Txt variant="h5"
						>Event {currentEventIndex + 1} of {eventsToMatch?.length}</Txt
					>
					<Txt variant="body2">
						It's important to check if the scraped activities you have selected
						match with an event that is already in the database.
					</Txt>
				{/if}
			</div>
		</div>

		{#if loading}
			<CircularProgress dataCy={dataCyPrefix} />
		{:else if eventsToMatch && !eventsToMatch.length}
			<ShowMatchedEvents />
		{:else if showLandingPage && eventsToMatch && eventsToMatch.length}
			<div
				class="flex flex-col items-center border border-gray-200 bg-gray-100 p-16 pb-24 mt-[-32px]"
			>
				<Txt variant="h6" class="my-4"
					>You have {eventsToMatch.length}
					{eventsToMatch.length === 1 ? 'event' : 'events'} to match</Txt
				>
				<Button
					size="md"
					class="mb-1"
					variant="primary"
					dataCy={dataCyPrefix}
					onclick={() => {
						showLandingPage = false;
					}}>start matching</Button
				>
			</div>
		{:else}
			<Txt variant="h6" class="mb-4">Scraped information</Txt>

			<div
				class={classNames('border border-gray-200 bg-gray-100 px-2', {
					'mb-12': !missingInfo,
					'mb-4': missingInfo,
				})}
			>
				{#each infos as info, i}
					{#if info.value}
						<div
							class={classNames('py-1 flex gap-4', {
								'border-t border-gray-200':
									i !== infos.findIndex((info) => !!info.value),
							})}
						>
							<Txt variant="body2" class="mr-4 inline-block min-w-[200px]">
								{info.label}
							</Txt>

							{#if info.label.includes('URL')}
								<a target="_blank" rel="noopener noreferrer" href={info.value}>
									<Txt
										variant="body2"
										component="span"
										class="w-full inline-block break-words text-blue-500"
									>
										{info.value}
									</Txt>
								</a>
							{:else}
								<Txt variant="body2" class="inline-block w-full">
									{info.value}
								</Txt>
							{/if}
						</div>
					{/if}
				{/each}
			</div>

			{#if missingInfo}
				<div class="mb-12 border border-gray-200 bg-gray-100 px-2 py-3">
					<Txt variant="label4">
						No scraped info available: {missingInfo}
					</Txt>
				</div>
			{/if}

			<Txt variant="label2" class="mb-2">Select an action</Txt>

			{#if isAuction}
				<AuctionForm
					bind:auctionHouseSelectedOption
					bind:auctionSelectedOption
					bind:auctionDetailsForm
					bind:matchId
					{auctionMatches}
				/>
			{:else if isFair}
				<FairForm
					bind:fairCitySelectedOption
					bind:fairSelectedOption
					bind:fairOrganisationSelectedOption
					bind:fairDetailsForm
					bind:matchId
					{fairMatches}
				/>
			{:else if isExhibition}
				<ExhibitionForm
					bind:exhibitionCitySelectedOption
					bind:exhibitionSelectedOption
					bind:exhibitionImage
					bind:exhibitionDetailsForm
					bind:matchId
					{exhibitionMatches}
				/>
			{/if}
		{/if}
	</Container>
</PageBody>

<PageSaveBar
	visible={!!eventsToMatch}
	backHref={Routes.ScrapedDataPreview}
	backLabel="Back to preview data"
	disabled={eventsToMatch && !eventsToMatch.length
		? false
		: submitting || !matchId || (matchId === 'new' ? isInvalid : false)}
	onSaveClick={eventsToMatch && !eventsToMatch.length
		? handleClickNext
		: handleSaveClick}
	loading={submitting}
	nextLabel={eventsToMatch && !eventsToMatch.length
		? 'continue'
		: 'save changes'}
/>
