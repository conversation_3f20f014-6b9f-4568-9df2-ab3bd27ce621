<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { TickIcon } from '$global/assets/styled-icons/TickIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Link } from '$global/components/Link';
	import {
		TableHeaderRow,
		TableHeader,
		TableRow,
		getCellWidth,
		TableCell,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Config } from '$lib/constants/config';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { UpdateScrapedEventDocument } from '$lib/queries/__generated__/updateScrapedEvent.generated';
	import {
		GetAuctionsDocument,
		type GetAuctionsQuery,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getAuctions.generated';
	import {
		GetExhibitionsDocument,
		type GetExhibitionsQuery,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getExhibitions.generated';
	import {
		GetFairsDocument,
		type GetFairsQuery,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getFairs.generated';
	import {
		GetScrapedEventsDocument,
		type GetScrapedEventsQuery,
	} from '$lib/websites/scraped-data/custom-queries/__generated__/getScrapedEvents.generated';
	import { getArtworkFeedsStore } from '$lib/websites/scraped-data/utils/getArtworkFeedsStore/getArtworkFeedsStore';

	let matchedEvents: GetScrapedEventsQuery['getScrapedArtworks']['scrapedEvents'] =
		$state([]);

	let matchedAuctions: null | GetAuctionsQuery['auction'] = $state(null);
	let matchedFairs: null | GetFairsQuery['fair'] = $state(null);
	let matchedExhibitions: null | GetExhibitionsQuery['exhibition'] =
		$state(null);

	let unmatchedAuctionId = $state('');
	let unmatchedFairId = $state('');
	let unmatchedExhibitionId = $state('');

	const { artworkFeeds } = getArtworkFeedsStore();

	onMount(() => {
		const findMatchedEvents = async () => {
			const res = await gqlClientCustom.request(
				GetScrapedEventsDocument,
				{
					input: { ids: $artworkFeeds.currentIds },
				},
				getAuthorizationHeaders({
					user: { access_token: page.data.user.access_token },
				})
			);

			matchedEvents = res.getScrapedArtworks.scrapedEvents?.filter(
				(event) =>
					!['private_sale', 'museum_acquisition'].includes(
						event?.art_event_feed?.event_type?.key as string
					)
			);

			if (
				matchedEvents.every((matchedEvent) => matchedEvent?.processed_event_id)
			) {
				const findEventsIds = (type: string) =>
					matchedEvents?.reduce((accumulator: string[], matchedEvent) => {
						if (
							matchedEvent?.art_event_feed?.event_type?.key
								?.toLowerCase()
								?.includes(type)
						) {
							return [...accumulator, `${matchedEvent?.processed_event_id}`];
						}

						return accumulator;
					}, []);

				const auctionEventsIds = findEventsIds('auction');
				const exhibitionEventsIds = findEventsIds('exhibition');
				const fairEventsIds = findEventsIds('fair');

				const auctionEventsPromise = auctionEventsIds.length
					? gqlClientArteye.request(
							GetAuctionsDocument,
							{
								filter: { id: { _in: auctionEventsIds } },
							},
							getAuthorizationHeaders({
								user: { access_token: page.data.user.arteye_token },
							})
						)
					: { auction: [] };

				const exhibitionEventsPromise = exhibitionEventsIds.length
					? gqlClientArteye.request(
							GetExhibitionsDocument,
							{
								filter: { id: { _in: exhibitionEventsIds } },
							},
							getAuthorizationHeaders({
								user: { access_token: page.data.user.arteye_token },
							})
						)
					: { exhibition: [] };

				const fairEventsPromise = fairEventsIds.length
					? gqlClientArteye.request(
							GetFairsDocument,
							{
								filter: { id: { _in: fairEventsIds } },
							},
							getAuthorizationHeaders({
								user: { access_token: page.data.user.arteye_token },
							})
						)
					: { fair: [] };

				const [auctionsRes, exhibitionsRes, fairsRes] = await Promise.all([
					auctionEventsPromise,
					exhibitionEventsPromise,
					fairEventsPromise,
				]);

				matchedAuctions = auctionsRes?.auction;
				matchedExhibitions = exhibitionsRes?.exhibition;
				matchedFairs = fairsRes?.fair;
			}
		};

		findMatchedEvents();
	});

	const dataCyPrefix = 'scraped-data-matched-events';
</script>

<div class="mt-[-1.5rem] flex gap-4">
	<TickIcon class="h-12 w-12" />
	<div>
		<Txt variant="h5">All events have been matched</Txt>
		<Txt>You can find the details of the matched events below (if any)</Txt>
	</div>
</div>

<div class="mb-8">
	{#if !matchedAuctions || !matchedExhibitions || !matchedFairs}
		<CircularProgress dataCy={dataCyPrefix} />
	{:else}
		{#if matchedAuctions?.length}
			{@const headers = ['Auction name', 'ID', 'View auction']}

			<Txt variant="h6" class="mt-8 border border-b-0 border-gray-200 p-3">
				{matchedAuctions.length} auctions:
			</Txt>
			<table class=" w-full table-fixed bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader
							dataCy={dataCyPrefix}
							width={getCellWidth(i, '0rem', headers)}
						>
							{header}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#each matchedAuctions as matchedAuction, i}
						<TableRow index={i} dataCy={dataCyPrefix}>
							{@const formattedAuction = [
								matchedAuction.sale_name,
								matchedAuction.id,
							]}
							{#each formattedAuction as formattedAuctionValue}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, '0rem', headers)}
									content={formattedAuctionValue}
								>
									{formattedAuctionValue}
								</TableCell>
							{/each}
							<TableCell
								dataCy={dataCyPrefix}
								width={getCellWidth(i, '0rem', headers)}
							>
								{#snippet custom()}
									<div class="flex gap-2">
										<Link
											class="flex items-center gap-1"
											rel="noopener noreferrer"
											target="_blank"
											href={`${Config.ArteyeDomain}/auctions/${matchedAuction.id}`}
										>
											<Txt
												variant="body2"
												class="text-blue-500"
												component="span">View Auction</Txt
											>

											<ExternalIcon class="h-4 w-4 stroke-blue-500" />
										</Link>

										<Button
											variant="secondary"
											dataCy="unmatch-events"
											size="xs"
											loading={matchedAuction.id === unmatchedAuctionId}
											onclick={async () => {
												unmatchedAuctionId = matchedAuction.id;

												await gqlClient.request(
													UpdateScrapedEventDocument,
													{
														id: matchedEvents.find(
															(event) =>
																event.processed_event_id === matchedAuction.id
														)?.id as string,
														data: {
															processed_event_id: null,
														},
													},
													getAuthorizationHeaders(
														page.data as { user: { access_token: string } }
													)
												);

												window.location.reload();
											}}
										>
											unmatch event
										</Button>
									</div>
								{/snippet}
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</table>
		{/if}

		{#if matchedExhibitions?.length}
			{@const headers = ['Exhibition name', 'ID', 'View exhibition']}

			<Txt variant="h6" class="mt-8 border border-b-0 border-gray-200 p-3">
				{matchedExhibitions.length} exhibitions:
			</Txt>
			<table class=" w-full table-fixed bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader
							dataCy={dataCyPrefix}
							width={getCellWidth(i, '0rem', headers)}
						>
							{header}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#each matchedExhibitions as matchedExhibition, i}
						<TableRow index={i} dataCy={dataCyPrefix}>
							{@const formattedExhibition = [
								matchedExhibition.title,
								matchedExhibition.id,
							]}
							{#each formattedExhibition as formattedExhibitionValue}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, '0rem', headers)}
									content={formattedExhibitionValue}
								>
									{formattedExhibitionValue}
								</TableCell>
							{/each}
							<TableCell
								dataCy={dataCyPrefix}
								width={getCellWidth(i, '0rem', headers)}
							>
								{#snippet custom()}
									<div class="flex gap-2">
										<Link
											class="flex items-center gap-1"
											rel="noopener noreferrer"
											target="_blank"
											href={`${Config.ArteyeDomain}/exhibitions/${matchedExhibition.id}`}
										>
											<Txt
												variant="body2"
												class="text-blue-500"
												component="span">View Exhibition</Txt
											>

											<ExternalIcon class="h-4 w-4 stroke-blue-500" />
										</Link>

										<Button
											variant="secondary"
											dataCy="unmatch-events"
											size="xs"
											loading={matchedExhibition.id === unmatchedExhibitionId}
											onclick={async () => {
												unmatchedExhibitionId = matchedExhibition.id;

												await gqlClient.request(
													UpdateScrapedEventDocument,
													{
														id: matchedEvents.find(
															(event) =>
																event.processed_event_id ===
																unmatchedExhibitionId
														)?.id as string,
														data: {
															processed_event_id: null,
														},
													},
													getAuthorizationHeaders(
														page.data as { user: { access_token: string } }
													)
												);

												window.location.reload();
											}}
										>
											unmatch event
										</Button>
									</div>
								{/snippet}
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</table>
		{/if}

		{#if matchedFairs?.length}
			{@const headers = ['Fair name', 'ID', 'View fair']}

			<Txt variant="h6" class="mt-8 border border-b-0 border-gray-200 p-3">
				{matchedFairs.length} fairs:
			</Txt>
			<table class=" w-full table-fixed bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader
							dataCy={dataCyPrefix}
							width={getCellWidth(i, '0rem', headers)}
						>
							{header}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#each matchedFairs as matchedFair, i}
						<TableRow index={i} dataCy={dataCyPrefix}>
							{@const formattedFair = [matchedFair.title, matchedFair.id]}
							{#each formattedFair as formattedFairValue}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, '0rem', headers)}
									content={formattedFairValue}
								>
									{formattedFairValue}
								</TableCell>
							{/each}
							<TableCell
								dataCy={dataCyPrefix}
								width={getCellWidth(i, '0rem', headers)}
							>
								{#snippet custom()}
									<div class="flex gap-2">
										<Link
											class="flex items-center gap-1"
											rel="noopener noreferrer"
											target="_blank"
											href={`${Config.ArteyeDomain}/fairs/${matchedFair.id}`}
										>
											<Txt
												variant="body2"
												class="text-blue-500"
												component="span">View Fair</Txt
											>

											<ExternalIcon class="h-4 w-4 stroke-blue-500" />
										</Link>

										<Button
											variant="secondary"
											dataCy="unmatch-events"
											size="xs"
											loading={matchedFair.id === unmatchedFairId}
											onclick={async () => {
												unmatchedFairId = matchedFair.id;

												await gqlClient.request(
													UpdateScrapedEventDocument,
													{
														id: matchedEvents.find(
															(event) =>
																event.processed_event_id === unmatchedFairId
														)?.id as string,
														data: {
															processed_event_id: null,
														},
													},
													getAuthorizationHeaders(
														page.data as { user: { access_token: string } }
													)
												);

												window.location.reload();
											}}
										>
											unmatch event
										</Button>
									</div>
								{/snippet}
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</table>
		{/if}
	{/if}
</div>
