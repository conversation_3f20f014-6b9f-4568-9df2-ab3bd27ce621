import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetExhibitionAttributeTypesDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getAttributes.generated';
import { GetAuctionTypesDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getAuctionTypes.generated';
import { GetCurrencyDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getCurrencies.generated';
import { GetTimezoneDocument } from '$lib/websites/scraped-data/arteye-queries/__generated__/getTimezone.generated';
import type { ScrapedDataMatchEventsPageServerLoadEvent } from '$routes/scraped-data/match-events/types';

export const matchEventsServerLoad = async ({
	parent,
}: ScrapedDataMatchEventsPageServerLoadEvent) => {
	const parentData = await parent();

	const auctionTypeReq = gqlClientArteye.request(
		GetAuctionTypesDocument,
		{},
		getAuthorizationHeaders({
			user: { access_token: parentData.user?.arteye_token },
		})
	);

	const currencyReq = gqlClientArteye.request(
		GetCurrencyDocument,
		{
			limit: -1,
		},
		getAuthorizationHeaders({
			user: { access_token: parentData.user?.arteye_token },
		})
	);

	const timeZoneReq = gqlClientArteye.request(
		GetTimezoneDocument,
		{
			limit: -1,
		},
		getAuthorizationHeaders({
			user: { access_token: parentData.user?.arteye_token },
		})
	);

	const attributesReq = gqlClientArteye.request(
		GetExhibitionAttributeTypesDocument,
		{},
		getAuthorizationHeaders({
			user: { access_token: parentData.user?.arteye_token },
		})
	);

	const [auctionTypeRes, currencyRes, timeZoneRes, attributesRes] =
		await Promise.all([
			auctionTypeReq,
			currencyReq,
			timeZoneReq,
			attributesReq,
		]);

	return {
		timezones: timeZoneRes.timezone,
		attributeTypes: attributesRes.exhibition_attribute_type,
		auctionTypes: auctionTypeRes.auction_type,
		currencies: currencyRes.currency,
	};
};
