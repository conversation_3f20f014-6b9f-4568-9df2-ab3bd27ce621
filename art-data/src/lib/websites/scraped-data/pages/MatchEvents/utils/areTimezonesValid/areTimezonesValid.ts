import dayjs from 'dayjs';

export const areTimezonesValid = (
	starts_at_tz: string | null | undefined,
	ends_at_tz: string | null | undefined
) => {
	let startsAtTz = '';
	let endsAtTz = '';

	try {
		if (starts_at_tz) {
			dayjs().tz(starts_at_tz as string);
			startsAtTz = starts_at_tz as string;
		}
	} catch {
		startsAtTz = '';
	}

	try {
		if (!startsAtTz && starts_at_tz) {
			dayjs().tz(`Etc/${starts_at_tz}`);
			startsAtTz = `Etc/${starts_at_tz}`;
		}
	} catch {
		startsAtTz = '';
	}

	try {
		if (ends_at_tz) {
			dayjs().tz(ends_at_tz as string);
			endsAtTz = ends_at_tz as string;
		}
	} catch {
		endsAtTz = '';
	}

	try {
		if (ends_at_tz && !endsAtTz) {
			dayjs().tz(`Etc/${ends_at_tz}`);
			endsAtTz = `Etc/${ends_at_tz}`;
		}
	} catch {
		endsAtTz = '';
	}

	return {
		startTz: startsAtTz,
		endTz: endsAtTz,
	};
};
