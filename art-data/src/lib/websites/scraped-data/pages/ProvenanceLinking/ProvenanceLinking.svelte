<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import {
		GetActivityDocument,
		type GetActivityQuery,
	} from '../../arteye-queries/__generated__/getActivity.generated';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { MatchProvenanceDocument } from '../../custom-queries/__generated__/matchProvenance.generated';
	import {
		SubmitProvenanceLinkingDocument,
		type SubmitProvenanceLinkingMutation,
	} from '../../custom-queries/__generated__/submitProvenanceLinking.generated';
	import { fetchScrapedArtworks } from '../../utils/fetchScrapedArtworks/fetchScrapedArtworks';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { TickIcon } from '$global/assets/styled-icons/TickIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableCell,
	} from '$global/components/Table';
	import { TableRow } from '$global/components/Table/TableRow';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { UpdateScrapedArtworkDocument } from '$lib/queries/__generated__/updateScrapedArtwork.generated';
	import {
		getArteyeImageUrl,
		getImageUrl,
	} from '$lib/utils/getImageUrl/getImageUrl';
	import type { ScrapedDataProcessDataFinalisePageData } from '$routes/scraped-data/process-data/finalise/types';

	interface Props {
		class?: string;
	}

	let { ...props }: Props = $props();

	let data = $derived(
		getPageData<ScrapedDataProcessDataFinalisePageData>(page.data)
	);
	let showConfirmation = $state(false);
	let showIngestionError = $state(false);
	let submitting = $state(false);

	let activityIds: null | string[] = $state(null);
	let submittedProvenanceLinkingEntries:
		| null
		| SubmitProvenanceLinkingMutation['submitProvenanceLinking']['activitiesTransferred'] =
		$state(null);
	let scrapedArtworks: null | Awaited<ReturnType<typeof fetchScrapedArtworks>> =
		$state(null);
	let matchedArtworks:
		| null
		| (GetActivityQuery['artwork_activity'][number] | null | undefined)[] =
		$state(null);

	const {
		artworkFeeds,
		resetCurrentIds,
		saveStage,
		showMatchEventsWarning,
		checkEventsAreMatches,
		scrapedArtworksStore,
	} = getArtworkFeedsStore();

	const fetchProvenance = async () => {
		if (!$scrapedArtworksStore) {
			return;
		}

		const completedScrapedArtworks = [...$scrapedArtworksStore]?.filter(
			(artwork) => artwork?.status?.key === 'COMPLETED'
		);

		showIngestionError = !![...$scrapedArtworksStore]?.find((artwork) =>
			isScrapedArtworkProcessed(artwork)
		);

		if (!completedScrapedArtworks.length) {
			activityIds = [];
			matchedArtworks = [];
			return;
		}

		const artworkIds = completedScrapedArtworks.map(
			(artwork) => artwork.id
		) as string[];

		const matchProvenanceRes = await gqlClientCustom.request(
			MatchProvenanceDocument,
			{ ids: artworkIds },
			getAuthorizationHeaders(data)
		);

		const provenances = matchProvenanceRes?.matchProvenance;

		activityIds = completedScrapedArtworks.map(
			(artwork) =>
				provenances?.find(
					(provenance) =>
						provenance?.processed_activity_id === artwork?.processed_activity_id
				)?.provenance_matched_activity_id || ''
		);

		const searchActivityIds = activityIds.filter(Boolean) as string[];

		if (searchActivityIds.length) {
			const artworkActivitiesRes = await gqlClientArteye.request(
				GetActivityDocument,
				{
					filter: {
						id: { _in: searchActivityIds },
					},
				},
				getAuthorizationHeaders({
					user: { access_token: data.user?.arteye_token },
				})
			);

			matchedArtworks = activityIds.map((activityId) => {
				if (!activityId) {
					return null;
				}

				return artworkActivitiesRes?.artwork_activity?.find(
					(artworkActivity) => artworkActivity.id === activityId
				);
			});
		} else {
			matchedArtworks = activityIds.map(() => null);
		}

		scrapedArtworks = [...completedScrapedArtworks];
	};

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !scrapedArtworks) {
			fetchProvenance();
		}
	});

	onMount(() => {
		saveStage();
		checkEventsAreMatches();
	});

	const handleSubmit = async () => {
		if (!matchedArtworks || !scrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			for (let i = 0; i < matchedArtworks?.length; i++) {
				if (
					!matchedArtworks[i] ||
					matchedArtworks[i]?.id ===
						scrapedArtworks?.[i]?.provenance_matched_activity_id
				) {
					continue;
				}

				await gqlClient.request(
					UpdateScrapedArtworkDocument,
					{
						id: scrapedArtworks?.[i].id as string,
						data: {
							provenance_matched_activity_id: matchedArtworks[i]?.id,
							provenance_matched_artwork_id:
								matchedArtworks[i]?.artworks?.[0]?.artwork?.id,
						},
					},
					getAuthorizationHeaders(data)
				);
			}

			const artworkIds = scrapedArtworks.map(
				(artwork) => artwork.id
			) as string[];

			const submitProvenanceLinkingRes = await gqlClientCustom.request(
				SubmitProvenanceLinkingDocument,
				{ ids: artworkIds },
				getAuthorizationHeaders(data)
			);

			submittedProvenanceLinkingEntries =
				submitProvenanceLinkingRes?.submitProvenanceLinking
					?.activitiesTransferred;

			showConfirmation = true;
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the provenance linking update.',
			});
		} finally {
			submitting = false;
		}
	};

	const handleClickReturnToScrapedData = () => {
		resetCurrentIds([], []);
		goto(Routes.ScrapedData);
	};

	const handleChangeActivityId = async (e: Event, i: number) => {
		if (!matchedArtworks) {
			return;
		}

		const value = (e.target as EventTarget & { value: string })?.value;

		if (!isUuidValid(value)) {
			matchedArtworks[i] = null;
			return;
		}

		const artworkActivityRes = await gqlClientArteye.request(
			GetActivityDocument,
			{
				filter: {
					id: { _eq: value },
				},
			},
			getAuthorizationHeaders({
				user: { access_token: data.user?.arteye_token },
			})
		);

		matchedArtworks[i] = artworkActivityRes?.artwork_activity?.[0];
	};

	const headers = [
		'Artwork image',
		'Description',
		'Provenance lines',
		'Matched Artwork',
		'Activity ID',
	];

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Provenance linking' },
	];

	const dataCyPrefix = 'scraped-data-provenance-linking';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={3} />

			{#if !matchedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else if !showConfirmation && scrapedArtworks && activityIds}
				{#if !scrapedArtworks.length}
					<div
						class="flex flex-col items-center border border-gray-200 bg-gray-100 p-16 pb-24 mt-[-16px]"
					>
						<Txt variant="h6" class="my-4"
							>{showIngestionError
								? 'Ingestion has failed for every selected artwork. Please contact customer support'
								: 'You must complete the Process Data stage before you can review provenance linking'}</Txt
						>

						<LinkButton
							size="md"
							class="mb-1"
							variant="secondary"
							dataCy={dataCyPrefix}
							href={showIngestionError
								? Routes.ScrapedDataProcessDataFinalise
								: Routes.ScrapedDataProcessDataImages}
						>
							{#snippet leading()}
								<ChevronLeftIcon />
							{/snippet}
							go to process data</LinkButton
						>
					</div>
				{:else}
					<Txt variant="h4" class="mb-4">Provenance Linking</Txt>

					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix} class={props.class}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={classNames({
										'w-[140px]': !i,
										'w-[180px]': i === 2,
										'border-l': i === 3,
									})}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>
						<TableBody dataCy={dataCyPrefix}>
							{#each scrapedArtworks as scrapedArtwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell
										dataCy={dataCyPrefix}
										class="px-0 py-2 pl-2 align-top"
									>
										<div class="flex max-h-[170px] flex-col">
											<a
												target="_blank"
												rel="noopener noreferrer"
												href={getImageUrl(
													scrapedArtwork?.images?.[0]?.directus_files_id?.id
												)}
												class="flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
											>
												<img
													alt=""
													class="max-h-full max-w-full"
													src={getImageUrl(
														scrapedArtwork?.images?.[0]?.directus_files_id?.id
													)}
												/>
											</a>

											<LinkButton
												dataCy={dataCyPrefix}
												href={scrapedArtwork.artwork_feed?.url}
												disabled={!scrapedArtwork.artwork_feed?.url}
												newTab
												variant="secondary"
												class="mt-1 w-[110px]"
												size="sm"
												fullWidth
												>{scrapedArtwork.artwork_feed?.url
													? 'Source'
													: 'No URL'}
												{#snippet trailing()}
													{#if scrapedArtwork.artwork_feed?.url}
														<ExternalIcon />
													{/if}
												{/snippet}</LinkButton
											>
										</div>
									</TableCell>

									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{#snippet custom()}
											<Txt variant="body3">
												{scrapedArtwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>

									<TableCell dataCy={dataCyPrefix} textVariant="body3" wrap>
										{#snippet custom()}
											<Txt variant="body3">
												{#if scrapedArtwork?.extracted_provenance_lines}
													{#each scrapedArtwork?.extracted_provenance_lines as provenance_line, i}
														{provenance_line.line}
														{#if i !== scrapedArtwork?.extracted_provenance_lines.length - 1}
															<br />
														{/if}
													{/each}
												{/if}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} class="align-top">
										{#snippet custom()}
											{#if matchedArtworks?.[i]}
												<div class="flex gap-1">
													<div
														style:height="90px"
														class="flex w-[90px] min-w-[90px] items-center justify-center bg-gray-200 p-2"
													>
														<img
															alt=""
															class="max-h-full max-w-full"
															src={getArteyeImageUrl(
																matchedArtworks[i]?.artworks?.[0]?.artwork
																	?.primary_image?.id,
																data.user?.arteye_token
															)}
														/>
													</div>
													<div class="mt-[-3px]">
														<Txt variant="body3"
															>{matchedArtworks[i]?.artworks?.[0]?.artwork
																?.artists?.[0]?.artist_id?.person?.entity
																?.name}</Txt
														>
														<Txt variant="body3"
															>{matchedArtworks[i]?.artworks?.[0]?.artwork
																?.title}</Txt
														>
														<Txt variant="body3"
															>{matchedArtworks[i]?.artworks?.[0]?.artwork
																?.media}</Txt
														>
													</div>
												</div>
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} class="align-top">
										<Input
											dataCy={dataCyPrefix}
											name="activity_id"
											rows={3}
											onkeyup={(e) => handleChangeActivityId(e, i)}
											bind:value={activityIds[i]}
										/>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				{/if}
			{:else if showConfirmation && submittedProvenanceLinkingEntries}
				{@const confirmationHeaders = [
					'Existing artwork in database',
					'Matched activity ID',
					'New artwork in database',
				]}

				<div class="mb-4 flex gap-4">
					<TickIcon class="h-12 w-12" />
					<div class="flex-col justify-between">
						<Txt variant="h5">Provenance linking complete</Txt>
						<Txt variant="body2">
							{#if submittedProvenanceLinkingEntries.length}
								You have linked the provenance for {submittedProvenanceLinkingEntries.length}
								records. A summary can be found below.
							{:else}
								No artwork has been linked.
							{/if}
						</Txt>
					</div>
				</div>

				{#if submittedProvenanceLinkingEntries.length}
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix} class={props.class}>
							{#each confirmationHeaders as header, i}
								<TableHeader dataCy={dataCyPrefix}>
									{#snippet custom()}
										<div class="flex items-center gap-1.5">
											<Txt variant="label3">{header}</Txt>
											<InfoTooltip
												dataCy={dataCyPrefix}
												content={(() => {
													if (!i) {
														return 'This is the artwork that provenance activities are merged into';
													}

													if (i === 1) {
														return 'This was found as part of the provenance';
													}

													return 'TODO art-data';
												})()}
											/>
										</div>
									{/snippet}
								</TableHeader>
							{/each}
						</TableHeaderRow>
						<TableBody dataCy={dataCyPrefix}>
							{#each submittedProvenanceLinkingEntries as submittedProvenanceLinkingEntry, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}
										>{submittedProvenanceLinkingEntry.from_artwork ||
											''}</TableCell
									>
									<TableCell dataCy={dataCyPrefix}
										>{submittedProvenanceLinkingEntry.activity || ''}</TableCell
									>
									<TableCell dataCy={dataCyPrefix}
										>{submittedProvenanceLinkingEntry.to_artwork ||
											''}</TableCell
									>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				{/if}
			{/if}
		</div>
	</Container>
</PageBody>

{#if matchedArtworks?.length}
	<PageSaveBar
		loading={submitting}
		disabled={false}
		onSaveClick={showConfirmation
			? handleClickReturnToScrapedData
			: handleSubmit}
		visible
		nextLabel={showConfirmation
			? 'Return to scraped data'
			: `Link provenance for ${matchedArtworks.length} records`}
		backHref={showConfirmation ? undefined : Routes.ScrapedData}
	>
		{#if !showConfirmation}
			<Button
				onclick={handleClickReturnToScrapedData}
				variant="secondary"
				dataCy={`${dataCyPrefix}-return`}
				size="md"
			>
				Return to scraped data
			</Button>
		{/if}
	</PageSaveBar>
{/if}
