<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	// eslint-disable-next-line import/default
	import type Sortable from 'sortablejs';
	import { twMerge } from 'tailwind-merge';
	import { ProcessDataImageCarouselType } from '.';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel';
	import {
		DEFAULT_STATIC_CLASSNAME,
		DEFAULT_GROUP,
		ImageCarousel,
	} from '$global/components/ImageCarousel';
	import { AddNewImageDialog } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels/MatchArtworksWithLabelsTable/MatchArtworksWithLabelsTableRow/ImageCarouselWithMenu/AddNewImageDialog';
	import { getIdSuffix } from '$lib/websites/scraped-data/utils/getIdSuffix/getIdSuffix';
	import { getProcessDataImagesContext } from '$lib/websites/scraped-data/utils/getProcessDataImagesContext/getProcessDataImagesContext';

	interface Props {
		updatedIndexes: number[];
		index: number;
		type: ProcessDataImageCarouselType;
		dataCy: string;
		class?: string;
		readonly: boolean;
		onDeleteImage?: () => void;
	}

	let {
		updatedIndexes = $bindable(),
		index,
		type,
		dataCy,
		onDeleteImage,
		readonly,
		...rest
	}: Props = $props();

	const dialogStores = createDialog();

	const {
		artworkImages,
		artworkMainImages,
		handleDragImage,
		handleDeleteImage,
		handleMoveImage,
	} = getProcessDataImagesContext();

	const handleDropEnd = (
		event: Sortable.SortableEvent,
		images: ImageCarouselImage[]
	) => {
		const { oldIndex = 0, newIndex = 0 } = event;
		const fromListIndex = event.from.id.split('#')[1];
		const movedItem = images[oldIndex];

		if (event.to !== event.from) {
			const toListIndex = event.to.id.split('#')[1];

			if (toListIndex.includes('main')) {
				const toListRowIndex = +toListIndex.split('-')[0];
				if ($artworkMainImages[toListRowIndex].images.length) {
					handleDragImage(
						$artworkMainImages[toListRowIndex].images[0],
						0,
						`${toListRowIndex}-main`,
						`${toListRowIndex}-other`
					);
				}
			}

			handleDragImage(movedItem, newIndex, fromListIndex, toListIndex);
			updatedIndexes = Array.from(
				new Set([
					...updatedIndexes,
					+fromListIndex.split('-')[0],
					+toListIndex.split('-')[0],
				] as number[])
			);
		} else {
			handleMoveImage(movedItem, oldIndex, newIndex, fromListIndex);
			updatedIndexes = Array.from(
				new Set([...updatedIndexes, +fromListIndex.split('-')[0]] as number[])
			);
		}
	};

	// Has to be 2 when empty so that we can insert an image on top of the placeholder
	// SortableJS considers the placeholder as an item of its own
	let handlePut: Sortable.GroupOptions['put'] = (to) =>
		to.el.children.length < 3;

	const handleClickPlusIcon = () => {
		dialogStores.states.open.set(true);
	};

	const handleDelete = (image: ImageCarouselImage) => {
		handleDeleteImage(idSuffix, image);
		if (onDeleteImage) {
			onDeleteImage();
		}
		updatedIndexes = Array.from(
			new Set([...updatedIndexes, index] as number[])
		);
	};

	const handleAddImage = () => {
		updatedIndexes = Array.from(
			new Set([...updatedIndexes, index] as number[])
		);
	};

	let idSuffix = $derived(getIdSuffix(index, type));
	let images = $derived(
		type === ProcessDataImageCarouselType.Other
			? $artworkImages[index]?.images
			: $artworkMainImages[index]?.images
	);
</script>

<div class="relative">
	<ImageCarousel
		{dataCy}
		onClickDelete={readonly ? undefined : handleDelete}
		id={`image-carousel-${+new Date()}-#${idSuffix}`}
		{images}
		onEnd={handleDropEnd}
		class={twMerge('min-h-[8.875rem] border border-gray-200 p-4', rest.class)}
		filter={`.${DEFAULT_STATIC_CLASSNAME}`}
		classes={{
			item: 'z-20 [&>div>div]:p-1',
			imageCarouselClasses: {
				imageClasses: { whiteBorderImageClasses: { image: 'h-auto' } },
			},
		}}
		group={{
			name: DEFAULT_GROUP,
			...(type === ProcessDataImageCarouselType.Main && {
				put: handlePut,
			}),
		}}
	>
		{#snippet placeholder()}
			<li
				class={classNames(
					'absolute flex max-h-[6.75rem] min-h-[6.75rem] min-w-[6.75rem] max-w-[6.75rem] items-center justify-center border border-gray-200 bg-[#efefef]',
					DEFAULT_STATIC_CLASSNAME
				)}
			></li>
		{/snippet}
	</ImageCarousel>

	{#if !readonly && ((type === ProcessDataImageCarouselType.Main && !images.length) || type !== ProcessDataImageCarouselType.Main)}
		<Button
			size="sm"
			dataCy={`${dataCy}-add`}
			class="absolute bottom-2 right-2 z-20 h-[1.5rem] w-[1.5rem] px-0"
			variant="secondary"
			onclick={handleClickPlusIcon}
		>
			<PlusIcon class="h-3 w-3" />
		</Button>
	{/if}
</div>

<AddNewImageDialog
	onAddImage={handleAddImage}
	contextFn={getProcessDataImagesContext}
	{dialogStores}
	toList={idSuffix}
/>
