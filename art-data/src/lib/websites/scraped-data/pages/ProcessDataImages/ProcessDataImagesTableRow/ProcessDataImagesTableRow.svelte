<script lang="ts">
	import classNames from 'classnames';
	import {
		ProcessDataImageCarousel,
		ProcessDataImageCarouselType,
	} from './ProcessDataImageCarousel';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { LinkButton } from '$global/components/LinkButton';
	import { TableRow, TableCell } from '$global/components/Table';
	import { getProcessDataImagesContext } from '$lib/websites/scraped-data/utils/getProcessDataImagesContext/getProcessDataImagesContext';

	const dataCyPrefix = 'process-data-images';

	interface Props {
		sourceUrl: string | null | undefined;
		updatedIndexes?: number[];
		index: number;
		readonly: boolean;
	}

	let {
		sourceUrl,
		updatedIndexes = $bindable([]),
		index,
		readonly,
	}: Props = $props();

	const { isRowInvalid } = getProcessDataImagesContext();

	const { artworkImages, handleDragImage } = getProcessDataImagesContext();

	const handleDeleteMainImage = (index: number) => {
		if ($artworkImages[index].images.length) {
			handleDragImage(
				$artworkImages[index].images[0],
				0,
				`${index}-other`,
				`${index}-main`
			);
		}
	};
</script>

<TableRow {index} dataCy={dataCyPrefix}>
	<TableCell dataCy={dataCyPrefix} class="relative">
		{#snippet custom()}
			<div class="absolute bottom-2 w-full">
				<LinkButton
					dataCy={dataCyPrefix}
					variant="secondary"
					size="xs"
					newTab
					disabled={!sourceUrl}
					href={sourceUrl}
				>
					{sourceUrl ? 'Source' : 'No URL'}
					{#snippet trailing()}
						{#if sourceUrl}
							<ExternalIcon />
						{/if}
					{/snippet}
				</LinkButton>
			</div>
		{/snippet}
	</TableCell>
	<TableCell
		dataCy={dataCyPrefix}
		class={classNames({ 'pointer-events-none': readonly })}
	>
		{#snippet custom()}
			<div>
				<ProcessDataImageCarousel
					{index}
					{readonly}
					onDeleteImage={() => {
						handleDeleteMainImage(index);
					}}
					type={ProcessDataImageCarouselType.Main}
					dataCy={`${dataCyPrefix}-main`}
					bind:updatedIndexes
					class={classNames('overflow-hidden', {
						'border-red-500': isRowInvalid(index),
					})}
				/>
			</div>
		{/snippet}
	</TableCell>
	<TableCell
		dataCy={dataCyPrefix}
		class={classNames({ 'pointer-events-none': readonly })}
	>
		{#snippet custom()}
			<div>
				<ProcessDataImageCarousel
					{index}
					{readonly}
					type={ProcessDataImageCarouselType.Other}
					dataCy={`${dataCyPrefix}-other`}
					bind:updatedIndexes
				/>
			</div>
		{/snippet}
	</TableCell>
</TableRow>
