<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { ProcessDataImagesTableRow } from '../ProcessDataImagesTableRow';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import { getProcessDataImagesContext } from '$lib/websites/scraped-data/utils/getProcessDataImagesContext/getProcessDataImagesContext';
	import { isScrapedArtworkProcessed } from '$lib/websites/scraped-data/utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { CUSTOM_EXTENSIONS_USER } from '$lib/constants/custom-extensions-user';

	interface Props {
		scrapedArtworks: GetScrapedArtworksQuery['Scraped_Artwork'];
		updatedIndexes: number[];
	}

	let { scrapedArtworks, updatedIndexes = $bindable() }: Props = $props();

	const { artworkImages, artworkMainImages } = getProcessDataImagesContext();
	const headers = ['Source URL', 'Main image', 'Artwork images'];
	const dataCyPrefix = 'process-data-images-table';

	onMount(() => {
		artworkMainImages.set(
			scrapedArtworks.map((scrapedArtwork) => {
				const areArtworkImagesUploaded =
					isScrapedArtworkProcessed(scrapedArtwork) ||
					(scrapedArtwork?.user_updated !== null &&
						scrapedArtwork?.user_updated?.id !== CUSTOM_EXTENSIONS_USER);

				const firstImage = areArtworkImagesUploaded
					? scrapedArtwork?.images?.[0]
					: scrapedArtwork?.artwork_feed?.primary_image
						? { directus_files_id: scrapedArtwork?.artwork_feed?.primary_image }
						: null;

				return {
					images: firstImage
						? [
								{
									id: `${firstImage.directus_files_id?.id}`,
									filename_disk: `${firstImage.directus_files_id?.filename_disk}`,
									width: firstImage.directus_files_id?.width || 1,
									height: firstImage.directus_files_id?.height || 1,
									alt: '',
									url: `${getImageUrl(
										firstImage.directus_files_id?.id,
										'256px'
									)}`,
								},
							]
						: [],
					page: undefined,
				};
			})
		);

		artworkImages.set(
			scrapedArtworks.map((scrapedArtwork) => {
				const areArtworkImagesUploaded = scrapedArtwork?.user_updated !== null;

				if (areArtworkImagesUploaded) {
					return {
						page: undefined,
						images: (scrapedArtwork?.images || [])?.slice(1).map((image) => ({
							id: `${image?.directus_files_id?.id}`,
							filename_disk: `${image?.directus_files_id?.filename_disk}`,
							width: image?.directus_files_id?.width || 1,
							height: image?.directus_files_id?.height || 1,
							alt: '',
							url: `${getImageUrl(image?.directus_files_id?.id, '256px')}`,
						})),
					};
				}

				return {
					page: undefined,
					images: (scrapedArtwork?.artwork_feed?.extra_images || []).map(
						(image) => ({
							id: `${image?.file_id?.id}`,
							filename_disk: `${image?.file_id?.filename_disk}`,
							width: image?.file_id?.width || 1,
							height: image?.file_id?.height || 1,
							alt: '',
							url: `${getImageUrl(image?.file_id?.id, '256px')}`,
						})
					),
				};
			})
		);
	});
</script>

{#if $artworkImages.length}
	<table class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white">
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader
					dataCy={dataCyPrefix}
					class={classNames({
						'w-[120px]': !i,
						'w-[170px]': i === 1,
					})}
				>
					{header}
				</TableHeader>
			{/each}
		</TableHeaderRow>

		<TableBody dataCy={dataCyPrefix}>
			{#key $artworkImages}
				{#key $artworkMainImages}
					{#each scrapedArtworks as scrapedArtwork, i}
						<ProcessDataImagesTableRow
							readonly={isScrapedArtworkProcessed(scrapedArtwork)}
							sourceUrl={scrapedArtwork.artwork_feed?.url}
							index={i}
							bind:updatedIndexes
						/>
					{/each}
				{/key}
			{/key}
		</TableBody>
	</table>
{/if}
