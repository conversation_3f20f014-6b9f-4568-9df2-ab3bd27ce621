<script lang="ts">
	import { setContext } from 'svelte';
	import { Contexts } from '$lib/constants/contexts';
	import { createProcessDataImagesContext } from '$lib/websites/scraped-data/utils/createProcessDataImagesContext/createProcessDataImagesContext';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	setContext(Contexts.ProcessDataImages, createProcessDataImagesContext());
</script>

{@render children?.()}
