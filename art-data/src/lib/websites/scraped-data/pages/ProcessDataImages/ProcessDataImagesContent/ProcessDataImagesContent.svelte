<script lang="ts">
	import classNames from 'classnames';
	import { compareScrapedArtworkFields } from '$lib/websites/scraped-data/utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { ProcessDataTabs } from '../../../components/ProcessDataTabs';
	import { ScrapedDataTabs } from '../../../components/ScrapedDataTabs';
	import { ProcessDataImagesTable } from '../ProcessDataImagesTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import {
		UpdateScrapedArtworkDocument,
		type UpdateScrapedArtworkMutation,
	} from '$lib/queries/__generated__/updateScrapedArtwork.generated';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';
	import { MatchedEventsWarning } from '$lib/websites/scraped-data/components/MatchedEventsWarning';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import { getArtworkFeedsStore } from '$lib/websites/scraped-data/utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { getProcessDataImagesContext } from '$lib/websites/scraped-data/utils/getProcessDataImagesContext/getProcessDataImagesContext';
	import type { ScrapedDataProcessDataImagesPageData } from '$routes/scraped-data/process-data/images/types';
	import { isScrapedArtworkProcessed } from '$lib/websites/scraped-data/utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { CUSTOM_EXTENSIONS_USER } from '$lib/constants/custom-extensions-user';

	let data = $derived(
		getPageData<ScrapedDataProcessDataImagesPageData>(page.data)
	);

	let submitting = $state(false);
	let updatedIndexes: number[] = $state([]);
	let scrapedArtworks: null | GetScrapedArtworksQuery['Scraped_Artwork'] =
		$state(null);

	const { scrapedArtworksStore, showMatchEventsWarning } =
		getArtworkFeedsStore();

	const { artworkImages, isRowInvalid, artworkMainImages } =
		getProcessDataImagesContext();

	const areArtworkImagesUploaded = (
		scrapedArtwork: GetScrapedArtworksQuery['Scraped_Artwork'][number]
	) => {
		return (
			isScrapedArtworkProcessed(scrapedArtwork) ||
			(scrapedArtwork?.user_updated !== null &&
				scrapedArtwork?.user_updated?.id !== CUSTOM_EXTENSIONS_USER)
		);
	};

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !scrapedArtworks) {
			scrapedArtworks = [...$scrapedArtworksStore].sort((a, b) => sortFn(a, b));
			updatedIndexes = scrapedArtworks
				?.map((artwork, i) => (areArtworkImagesUploaded(artwork) ? null : i))
				.filter((i) => i !== null) as number[];
		}
	});

	const dataCyPrefix = 'scraped-data-process-images';

	const handleSaveRow = async (rowIndex: number) => {
		if (!scrapedArtworks) {
			return;
		}

		const mainImagePayload = await (async () => {
			const mainImage = $artworkMainImages[rowIndex].images[0];
			if (!mainImage) {
				return null;
			}

			if (mainImage.file) {
				const uploadFileResponse = await uploadFile(
					mainImage.file,
					getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					)
				);

				return {
					directus_files_id: uploadFileResponse,
				};
			}

			if (!mainImage.url.startsWith('/api')) {
				const uplodedImageResponse = await fetch('/api/upload-file-from-url', {
					method: 'POST',
					headers: getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					body: JSON.stringify({ url: mainImage.url }),
				});

				const uploadFileResponse = await uplodedImageResponse.json();
				return {
					directus_files_id: uploadFileResponse,
				};
			}

			return {
				directus_files_id: {
					id: mainImage.id,
				},
			};
		})();

		const otherImagesPayload = await (async () => {
			const images = $artworkImages[rowIndex].images;
			if (!images.length) {
				return [];
			}

			const otherImages = await Promise.all(
				images.map(async (image) => {
					if (image.file) {
						const uploadFileResponse = await uploadFile(
							image.file,
							getAuthorizationHeaders(
								page.data as { user: { access_token: string } }
							)
						);

						return Promise.resolve({
							directus_files_id: uploadFileResponse,
						});
					}

					if (!image.url.startsWith('/api')) {
						const uplodedImageResponse = await fetch(
							'/api/upload-file-from-url',
							{
								method: 'POST',
								headers: getAuthorizationHeaders(
									page.data as { user: { access_token: string } }
								),
								body: JSON.stringify({ url: image.url }),
							}
						);

						const uploadFileResponse = await uplodedImageResponse.json();
						return Promise.resolve({
							directus_files_id: uploadFileResponse,
						});
					}

					return Promise.resolve({
						directus_files_id: {
							id: image.id,
						},
					});
				})
			);

			return otherImages;
		})();

		if (!$scrapedArtworksStore) {
			return;
		}

		return gqlClient.request(
			UpdateScrapedArtworkDocument,
			{
				id: `${scrapedArtworks[rowIndex].id}`,
				data: {
					images: [mainImagePayload, ...otherImagesPayload].filter(Boolean),
				},
			},
			getAuthorizationHeaders(data)
		);
	};

	const handleSaveClick = async () => {
		try {
			if (!$scrapedArtworksStore) {
				return;
			}

			submitting = true;
			const rowPromises = updatedIndexes.map((index) => handleSaveRow(index));
			const newScrapedArtworks = (await Promise.all(
				rowPromises
			)) as UpdateScrapedArtworkMutation[];

			let newScrapedArtworksStore = [...($scrapedArtworksStore || [])];

			newScrapedArtworks.forEach((newScrapedArtwork) => {
				const newScrapedArtworkIndex = $scrapedArtworksStore?.findIndex(
					(artwork) =>
						artwork.id === newScrapedArtwork.update_Scraped_Artwork_item?.id
				);

				newScrapedArtworksStore[newScrapedArtworkIndex] =
					newScrapedArtwork.update_Scraped_Artwork_item as NonNullable<
						typeof newScrapedArtwork.update_Scraped_Artwork_item
					>;
			});

			scrapedArtworksStore.set(newScrapedArtworksStore);

			showToast({
				variant: 'success',
				message: 'The update was successful',
			});

			goto(Routes.ScrapedDataProcessDataArtworkDetails);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update',
			});

			submitting = false;
		}
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof scrapedArtworks>[number]
		) => {
			return {
				field: scrapedArtwork.artwork_feed?.created_at,
				type: 'date',
			};
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(
				scrapedArtworkB as unknown as NonNullable<
					typeof scrapedArtworks
				>[number]
			) as Parameters<typeof compareScrapedArtworkFields>[1],
			'DEFAULT'
		);
	};
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={0} />

			{#if scrapedArtworks?.length && $showMatchEventsWarning !== null}
				{#if $showMatchEventsWarning}
					<MatchedEventsWarning />
				{:else}
					<Txt class="mb-4 mt-[-0.5rem]" variant="body2">
						Confirm the images are correct below by dragging the main image into
						the first column and removing any incorrect images.
					</Txt>

					<div id="process-data-images-bin" class="hidden"></div>

					<ProcessDataImagesTable bind:updatedIndexes {scrapedArtworks} />
				{/if}
			{:else}
				<CircularProgress dataCy={dataCyPrefix} />
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	visible
	backHref={Routes.ScrapedDataMatchEvents}
	backLabel="Back to match events"
	disabled={!scrapedArtworks?.length ||
		$showMatchEventsWarning ||
		submitting ||
		$artworkImages.some((_, index) => isRowInvalid(index))}
	onSaveClick={handleSaveClick}
	loading={submitting}
/>
