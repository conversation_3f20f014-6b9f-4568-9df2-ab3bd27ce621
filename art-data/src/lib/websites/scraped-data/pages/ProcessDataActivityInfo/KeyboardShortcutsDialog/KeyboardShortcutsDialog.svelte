<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
	}

	let { dialogStores }: Props = $props();

	const entries = [
		{ label: 'CMD+CLICK on the date time cells', description: 'Toggle a cell' },
		{ label: 'Enter', description: 'Confirm changes' },
		{ label: 'Escape', description: 'Cancel changes' },
	];
</script>

<Dialog
	title="Keyboard shortcuts"
	showOverlay
	{dialogStores}
	dataCy="keyboard-shortcuts"
	onClose={() => {
		dialogStores.states.open.set(false);
	}}
	class="h-auto max-w-[31.25rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-6 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	<Txt variant="body2" class="mb-6">
		<p class="mb-4">
			These shortcuts are available during the multi-line datetime update
			process that can be triggered by clicking one of the fast clock buttons
			next to the time inputs. Clicking one of this button will determine the
			reference datetime value for the other entries that will be updated.
		</p>

		{#each entries as { label, description }}
			<p class="flex items-center">
				<Txt component="span" variant="label3">
					{label}&nbsp;:&nbsp;
				</Txt>
				<Txt component="span" variant="body2">
					{description}
				</Txt>
			</p>
		{/each}
	</Txt>

	<Button
		dataCy={`keyboard-shortcuts-back`}
		onclick={() => {
			dialogStores.states.open.set(false);
		}}
		fullWidth
		size="md">back</Button
	>
</Dialog>
