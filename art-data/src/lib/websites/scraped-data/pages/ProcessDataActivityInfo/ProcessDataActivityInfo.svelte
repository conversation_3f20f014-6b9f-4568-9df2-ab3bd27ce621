<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { onMount } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';
	import { KeyboardShortcutsDialog } from './KeyboardShortcutsDialog';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { FastClockIcon } from '$global/assets/icons/FastClockIcon';
	import { TickIcon } from '$global/assets/icons/TickIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { capitalizeFirstLetters } from '$global/utils/capitalizeFirstLetters';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Artwork_Activity_Status_Type_Enum } from '$gql/types-arteye-custom';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { UpdateArtworkDetailsDocument } from '$lib/features/final-review/queries/__generated__/updateArtworkDetails.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataActivityListingInfoPageData } from '$routes/scraped-data/process-data/activity-listing-info/types';

	const dialogStores = createDialog();
	const shortcutsDialogStores = createDialog();

	let timeSelection: number[] = $state([]);
	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.DateCreatedAsc);
	let updatedIds: string[] = [];

	const handleClickTimeCell = (e: MouseEvent, i: number) => {
		if (e.ctrlKey || e.metaKey) {
			if (timeSelection.length && i !== timeSelection[0]) {
				if (timeSelection.includes(i)) {
					timeSelection = timeSelection.filter((index) => index !== i);
				} else {
					timeSelection = [...timeSelection, i];
				}
			}
		}
	};

	const handleInitTimeSelection = (i: number) => {
		timeSelection = [i];
	};

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataActivityListingInfoPageData>(page.data)
	);

	let sortedScrapedArtworks:
		| null
		| (GetScrapedArtworksQuery['Scraped_Artwork'][number] & {
				artwork_details: GetScrapedArtworksQuery['Scraped_Artwork'][number]['artwork_details'] & {
					sale_date_date: null | string | undefined;
					sale_date_time: null | string | undefined;
				};
		  })[] = $state(null);

	const { scrapedArtworksStore, saveStage, showMatchEventsWarning } =
		getArtworkFeedsStore();

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.CurrencyAsc:
					return {
						field: scrapedArtwork.artwork_details?.currency?.code,
						type: 'string',
					};
				case ArtworkDetailsSort.CurrencyDesc:
					return {
						field: scrapedArtwork.artwork_details?.currency?.code,
						type: 'string',
					};
				case ArtworkDetailsSort.LowEstimateAsc:
					return {
						field: scrapedArtwork.artwork_details?.estimate_low,
						type: 'number',
					};
				case ArtworkDetailsSort.LowEstimateDesc:
					return {
						field: scrapedArtwork.artwork_details?.estimate_low,
						type: 'number',
					};
				case ArtworkDetailsSort.HighEstimateAsc:
					return {
						field: scrapedArtwork.artwork_details?.estimate_high,
						type: 'number',
					};
				case ArtworkDetailsSort.HighEstimateDesc:
					return {
						field: scrapedArtwork.artwork_details?.estimate_high,
						type: 'number',
					};
				case ArtworkDetailsSort.PriceAsc:
				case ArtworkDetailsSort.PriceDesc: {
					return {
						field: scrapedArtwork.artwork_details?.price,
						type: 'number',
					};
				}
				case ArtworkDetailsSort.ActivityDateAsc:
					return {
						field: scrapedArtwork.artwork_details?.sale_date_date,
						type: 'date',
					};
				case ArtworkDetailsSort.ActivityDateDesc:
					return {
						field: scrapedArtwork.artwork_details?.sale_date_date,
						type: 'date',
					};
				case ArtworkDetailsSort.ActivityStatusAsc:
					return {
						field: scrapedArtwork.artwork_details?.sale_status,
						type: 'string',
					};
				case ArtworkDetailsSort.ActivityStatusDesc:
					return {
						field: scrapedArtwork.artwork_details?.sale_status,
						type: 'string',
					};
				case ArtworkDetailsSort.ActivityTimeAsc:
					return {
						field: scrapedArtwork.artwork_details?.sale_date_time,
						type: 'string',
					};
				case ArtworkDetailsSort.ActivityTimeDesc:
					return {
						field: scrapedArtwork.artwork_details?.sale_date_time,
						type: 'string',
					};
				case ArtworkDetailsSort.LotNumberAsc:
					return {
						field: scrapedArtwork.artwork_details?.lot_number,
						type: 'string',
					};
				case ArtworkDetailsSort.LotNumberDesc:
					return {
						field: scrapedArtwork.artwork_details?.lot_number,
						type: 'string',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(
				scrapedArtworkA as Parameters<typeof getTextField>[0]
			) as Parameters<typeof compareScrapedArtworkFields>[0],
			getTextField(
				scrapedArtworkB as Parameters<typeof getTextField>[0]
			) as Parameters<typeof compareScrapedArtworkFields>[1],
			sort
		);
	};

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);

		lastSorted = +new Date();
	};

	let lastSorted = $state(+new Date());

	const getDateFromDateString = (date: string | undefined) =>
		date?.split('T')?.[0]?.slice(0, 10) || null;

	const getTimeFromDateString = (date: string | undefined) =>
		date?.split('T')?.[1]?.slice(0, 5) || null;

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			sortedScrapedArtworks = [...$scrapedArtworksStore]
				.map((artwork) => {
					return {
						...artwork,
						artwork_details: {
							...artwork.artwork_details,
							id: `${artwork.artwork_details?.id}`,
							sale_date_date: getDateFromDateString(
								artwork.artwork_details?.sale_date
							),
							sale_date_time: getTimeFromDateString(
								artwork.artwork_details?.sale_date
							),
							...(!artwork.artwork_details?.currency?.code && {
								currency: currencies.find(
									(currency) => currency.code === 'USD'
								),
							}),
						},
					};
				})
				.map((artwork) => {
					const options = getOptions(artwork);
					return {
						...artwork,
						artwork_details: {
							...artwork.artwork_details,
							sale_status: options
								.map((option) => option.value)
								.includes(artwork.artwork_details?.sale_status as string)
								? (artwork.artwork_details?.sale_status as string)
								: options[0].value,
						},
					};
				})
				.sort((a, b) => sortFn(a, b, sort));
		}
	});

	const handleSubmitTimeSelection = () => {
		const refArtworkDetailsId =
			sortedScrapedArtworks?.[timeSelection[0]]?.artwork_details?.id;

		const refSrapedArtwork = sortedScrapedArtworks?.find(
			(scrapedArtwork) =>
				scrapedArtwork?.artwork_details?.id === refArtworkDetailsId
		);

		const timeValue = refSrapedArtwork?.artwork_details.sale_date_time;
		const dateValue = refSrapedArtwork?.artwork_details.sale_date_date;

		const affectedScrapedArtworks = timeSelection?.map(
			(i) => sortedScrapedArtworks?.[i]
		);

		affectedScrapedArtworks.forEach((scrapedArtwork) => {
			handleChangeArtworkDetails(
				scrapedArtwork as NonNullable<typeof scrapedArtwork>
			);
		});

		const affectedScrapedArtworkIds = affectedScrapedArtworks.map(
			(scrapedArtwork) => scrapedArtwork?.artwork_details?.id
		);

		sortedScrapedArtworks = (sortedScrapedArtworks || []).map(
			(scrapedArtwork) => {
				const scrapedArtworkDetailsId = scrapedArtwork?.artwork_details?.id;
				if (affectedScrapedArtworkIds.includes(scrapedArtworkDetailsId)) {
					return {
						...scrapedArtwork,
						artwork_details: {
							...scrapedArtwork.artwork_details,
							sale_date_date: dateValue,
							sale_date_time: timeValue,
						},
					};
				}

				return scrapedArtwork;
			}
		);

		sortedScrapedArtworks = (sortedScrapedArtworks || []).map(
			(scrapedArtwork) => {
				const scrapedArtworkDetailsId = scrapedArtwork?.artwork_details?.id;
				if (affectedScrapedArtworkIds.includes(scrapedArtworkDetailsId)) {
					return {
						...scrapedArtwork,
						artwork_details: {
							...scrapedArtwork.artwork_details,
							sale_date_date: dateValue,
							sale_date_time: timeValue,
						},
					};
				}

				return scrapedArtwork;
			}
		) as typeof sortedScrapedArtworks;

		timeSelection = [];
	};

	onMount(() => {
		document.addEventListener('keydown', (e) => {
			if (timeSelection.length) {
				if (e.key === 'Escape') {
					e.preventDefault();
					timeSelection = [];
				}

				if (e.key === 'Enter') {
					e.preventDefault();
					handleSubmitTimeSelection();
				}
			}
		});

		saveStage();
	});

	let currencies = $derived(data.currencies);

	let currencyOptions = $derived(
		currencies?.map((currency) => ({
			label: `${currency?.code}`,
			value: `${currency?.code}`,
		}))
	);

	// const updateSortedArtworks = (
	// 	scrapedArtwork: NonNullable<
	// 		GetScrapedArtworksQuery['Scraped_Artwork']
	// 	>[number],
	// 	value: string | null | undefined,
	// 	fieldName: string
	// ) => {
	// 	const updatedSortedArtworkIndex = sortedScrapedArtworks.findIndex(
	// 		(artwork) =>
	// 			artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
	// 	);

	// 	if (updatedSortedArtworkIndex !== -1) {
	// 		const sortedScrapedArtworksCopy = [...sortedScrapedArtworks];

	// 		sortedScrapedArtworksCopy[updatedSortedArtworkIndex] = {
	// 			...sortedScrapedArtworksCopy[updatedSortedArtworkIndex],
	// 			artwork_details: {
	// 				...sortedScrapedArtworksCopy[updatedSortedArtworkIndex]
	// 					.artwork_details,
	// 				id: sortedScrapedArtworksCopy[updatedSortedArtworkIndex]
	// 					?.artwork_details?.id as string,
	// 				[fieldName]: (() => {
	// 					if (!value) {
	// 						return '';
	// 					}

	// 					if (fieldName === 'sale_date_time') {
	// 						return value;
	// 					}

	// 					const dateBits = value.split('/');
	// 					return `${dateBits[2]}-${dateBits[1]}-${dateBits[0]}`;
	// 				})(),
	// 			},
	// 		};

	// 		sortedScrapedArtworks = sortedScrapedArtworksCopy.map((artwork) => {
	// 			const options = getOptions(artwork as Parameters<typeof getOptions>[0]);

	// 			return {
	// 				...artwork,
	// 				artwork_details: {
	// 					...artwork.artwork_details,
	// 					sale_status: options
	// 						.map((option) => option.value)
	// 						.includes(artwork.artwork_details?.sale_status as string)
	// 						? (artwork.artwork_details?.sale_status as string)
	// 						: options[0].value,
	// 				},
	// 			};
	// 		}) as typeof sortedScrapedArtworks;
	// 	}
	// };

	const handleValueChange = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e: {
			target:
				| (EventTarget & { value?: string | undefined; name?: string })
				| null;
		},
		fieldName: string,
		number: boolean
	) => {
		if (!sortedScrapedArtworks || e.target?.value === undefined) {
			return;
		}

		const newValue =
			e.target.value === '' ? null : number ? +e.target.value : e.target.value;

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: newValue,
				},
			};
		}
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			if (updatedIds.length) {
				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(artworkDetailsCopy) => {
						const artworkDetails = artworkDetailsCopy as NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details'] & {
							sale_date_date: null | string | undefined;
							sale_date_time: null | string | undefined;
						};

						return {
							currency: {
								code: artworkDetails?.currency?.code,
								name: artworkDetails?.currency?.name,
							},
							estimate_low: [null, undefined]?.includes(
								artworkDetails?.estimate_low as null | undefined
							)
								? null
								: +(artworkDetails?.estimate_low as number),
							estimate_high: [null, undefined]?.includes(
								artworkDetails?.estimate_high as null | undefined
							)
								? null
								: +(artworkDetails?.estimate_high as number),
							price: [null, undefined]?.includes(
								artworkDetails?.price as null | undefined
							)
								? null
								: +(artworkDetails?.price as number),
							price_includes_premium: !!artworkDetails?.price_includes_premium,
							sale_status: artworkDetails?.sale_status,
							lot_number: artworkDetails?.lot_number,
							sale_date: (() => {
								if (!artworkDetails?.sale_date_date) {
									return null;
								}

								if (!artworkDetails?.sale_date_time) {
									return `${artworkDetails?.sale_date_date}T00:00:00Z`;
								}

								return `${artworkDetails?.sale_date_date}T${artworkDetails?.sale_date_time}Z`;
							})(),
						};
					},
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		} finally {
			submitting = false;
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataAuctionInfo);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Scraped price text',
		'Currency',
		'Low est',
		'High est',
		'Price',
		'Inc premium',
		'Activity status',
		'Activity date',
		'Activity time',
		'Lot no.',
	];

	const dataCyPrefix = 'scraped-data-process-activity-info';

	const handleChangeBoolean = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		bool: boolean | string,
		fieldName: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: Boolean(bool),
				},
			};
		}
	};

	const handleChangeCurrency = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e: {
			detail: {
				value: string;
			};
		}
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					currency: {
						code: e.detail?.value as string,
						name: currencyOptions.find(
							(currency) => currency.value === e.detail?.value
						)?.label as string,
					},
				},
			};
		}
	};

	const getOptions = (
		scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
	) => {
		const isActivityInTheFuture =
			!!scrapedArtwork.artwork_details?.sale_date_date &&
			dayjs().unix() <
				dayjs(
					`${scrapedArtwork.artwork_details?.sale_date_date}${scrapedArtwork.artwork_details?.sale_date_time ? `T${scrapedArtwork.artwork_details?.sale_date_time}${scrapedArtwork.artwork_details?.sale_date_time?.length === 5 ? ':00' : ''}` : ''}Z`
				).unix();

		return (
			getIsPrivateMarket(scrapedArtwork)
				? [
						Artwork_Activity_Status_Type_Enum.ForSale,
						Artwork_Activity_Status_Type_Enum.Sold,
						Artwork_Activity_Status_Type_Enum.NotForSale,
						Artwork_Activity_Status_Type_Enum.Reserved,
						Artwork_Activity_Status_Type_Enum.Unlisted,
						Artwork_Activity_Status_Type_Enum.Loaned,
						Artwork_Activity_Status_Type_Enum.Gifted,
					]
				: [
						...(isActivityInTheFuture
							? [Artwork_Activity_Status_Type_Enum.Upcoming]
							: []),
						Artwork_Activity_Status_Type_Enum.Sold,
						Artwork_Activity_Status_Type_Enum.BoughtIn,
						Artwork_Activity_Status_Type_Enum.Withdrawn,
						Artwork_Activity_Status_Type_Enum.NotReported,
					]
		)
			.map((value: string) => ({
				value,
				label: capitalizeFirstLetters(value.replaceAll('_', ' ').toLowerCase()),
			}))
			.sort((optionA, optionB) => {
				if (optionA.label < optionB.label) {
					return -1;
				}
				if (optionA.label > optionB.label) {
					return 1;
				}
				return 0;
			});
	};

	const getIsPrivateMarket = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		return (
			scrapedArtwork?.artwork_feed?.event_id?.event_type?.processor_type ===
			'PRIVATE_MARKET'
		);
	};

	const handleChangeSaleStatus = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e: {
			detail: {
				value: string;
			};
		}
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					sale_status: e.detail?.value,
				},
			};
		}
	};
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={6} />
			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<div class="flex gap-2">
						<Button
							dataCy="view-keyboard-shortcuts"
							variant="secondary"
							size="md"
							onclick={() => shortcutsDialogStores.states.open.set(true)}
							>view keyboard shortcuts</Button
						>
						<Select
							bind:value={sort}
							onchange={(e) => {
								sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
							}}
							ariaLabel="Sort entries"
							name="sort"
							class="[&>button>p]:uppercase"
							dataCy={`${dataCyPrefix}-sort`}
							selectedPrefix="SORT BY:"
							options={Object.values(ArtworkDetailsSort).map((sort) => ({
								label: sort,
								value: sort,
							}))}
							classes={{
								placeholder: 'font-medium tracking-widest',
								menu: 'z-50',
							}}
						/>
					</div>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={twMerge(
									classNames({
										'w-[136px]': i === 0,
										// 'w-[170px]': [1, 8].includes(i),
										// 'w-[130px]': i === 2,
										// 'w-[100px]': i === 3,
										// 'w-[110px]': i === 7,
										// 'w-[120px]': [4, 5, 6].includes(i),
										// 'w-[125px]': [9].includes(i),
										'w-[155px]':
											[10].includes(i) &&
											sortedScrapedArtworks.some(
												(scrapedArtwork) =>
													!isScrapedArtworkProcessed(scrapedArtwork)
											),
										// 'w-[80px]': i === 11,
									}),
									'border'
								)}
							>
								{#snippet custom()}
									<div class="flex items-center">
										<Txt variant="label3">
											{header}
										</Txt>
										{#if i === 7}
											<InfoTooltip
												dataCy="inc-premium"
												content="Includes any premium fees. Hammer price does not include premium."
											/>
										{/if}
									</div>
								{/snippet}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#key lastSorted}
							{#each sortedScrapedArtworks as scrapedArtwork, i}
								{@const isInProgress =
									!isScrapedArtworkProcessed(scrapedArtwork)}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<a
											target="_blank"
											rel="noopener noreferrer"
											href={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
											class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
										>
											<img
												alt=""
												class="max-h-full max-w-full"
												src={getImageUrl(
													scrapedArtwork?.images?.[0]?.directus_files_id?.id
												)}
											/>
										</a>

										<LinkButton
											dataCy={dataCyPrefix}
											href={scrapedArtwork.artwork_feed?.url}
											disabled={!scrapedArtwork.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											fullWidth
											>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
											{#snippet trailing()}
												{#if scrapedArtwork.artwork_feed?.url}
													<ExternalIcon />
												{/if}
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap>
										<Txt variant="body3" class="whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap>
										{#if scrapedArtwork.artwork_details}
											<Txt variant="body3">
												{scrapedArtwork?.artwork_feed?.price || ''}
											</Txt>
										{/if}
									</TableCell>
									<TableCell dataCy={dataCyPrefix} class="px-2">
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Select
														size="sm"
														ariaLabel="Select a type of currency"
														dataCy={dataCyPrefix}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleChangeCurrency(scrapedArtwork, e);
														}}
														value={scrapedArtwork.artwork_details?.currency
															?.code || ''}
														name="currency"
														options={currencyOptions}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork?.artwork_details?.currency?.code ||
															''}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Input
														size="sm"
														dataCy={dataCyPrefix}
														name="estimate_low"
														onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
														onkeyup={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'estimate_low',
																false
															);
														}}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'estimate_low',
																false
															);
														}}
														value={`${
															scrapedArtwork.artwork_details.estimate_low ===
															null
																? ''
																: scrapedArtwork.artwork_details.estimate_low
														}`}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork.artwork_details.estimate_low ===
														null
															? ''
															: scrapedArtwork.artwork_details.estimate_low}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Input
														size="sm"
														dataCy={dataCyPrefix}
														name="estimate_high"
														onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
														onkeyup={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'estimate_high',
																false
															);
														}}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'estimate_high',
																false
															);
														}}
														value={`${
															scrapedArtwork.artwork_details.estimate_high ===
															null
																? ''
																: scrapedArtwork.artwork_details.estimate_high
														}`}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork.artwork_details.estimate_high ===
														null
															? ''
															: scrapedArtwork.artwork_details.estimate_high}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Input
														size="sm"
														dataCy={dataCyPrefix}
														name="price"
														onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
														onkeyup={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'price',
																false
															);
														}}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'price',
																false
															);
														}}
														value={`${
															scrapedArtwork.artwork_details.price === null
																? ''
																: scrapedArtwork.artwork_details.price
														}`}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork.artwork_details.price === null
															? ''
															: scrapedArtwork.artwork_details.price}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Checkbox
														dataCy={`${dataCyPrefix}-price-includes-premium`}
														checked={scrapedArtwork.artwork_details
															?.price_includes_premium || false}
														onChange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleChangeBoolean(
																scrapedArtwork,
																e,
																'price_includes_premium'
															);
														}}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork.artwork_details
															?.price_includes_premium
															? 'Yes'
															: 'No'}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													{@const options = getOptions(
														scrapedArtwork as Parameters<typeof getOptions>[0]
													)}

													<Select
														size="sm"
														ariaLabel="Select activity status"
														dataCy={dataCyPrefix}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleChangeSaleStatus(scrapedArtwork, e);
														}}
														value={scrapedArtwork.artwork_details
															?.sale_status as string}
														name="sale_status"
														{options}
													/>
												{:else}
													<Txt variant="body3">
														{capitalizeFirstLetters(
															(
																scrapedArtwork?.artwork_details?.sale_status ||
																''
															)
																.replaceAll('_', ' ')
																.toLowerCase()
														)}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
									<TableCell
										onclick={(e) => {
											handleClickTimeCell(e, i);
										}}
										dataCy={dataCyPrefix}
										class={classNames('px-1', {
											'border-l border-blue-400 relative':
												timeSelection.length && i === timeSelection[0],
											'bg-blue-100': timeSelection.includes(i),
										})}
									>
										{#snippet custom()}
											{#if isInProgress}
												{#if i === timeSelection[0]}
													<div
														class="h-[1px] bg-blue-400 w-full left-0 absolute top-[-1px]"
													></div>
												{/if}
												<Input
													dataCy={`${dataCyPrefix}-activity-date`}
													name="activity-date"
													placeholder="Activity Date"
													type="date"
													value={(
														scrapedArtwork as NonNullable<
															typeof sortedScrapedArtworks
														>[number]
													)?.artwork_details?.sale_date_date}
													size="sm"
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'sale_date_date',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'sale_date_date',
															false
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{(
														scrapedArtwork as NonNullable<
															typeof sortedScrapedArtworks
														>[number]
													)?.artwork_details?.sale_date_date
														? dayjs(
																(
																	scrapedArtwork as NonNullable<
																		typeof sortedScrapedArtworks
																	>[number]
																)?.artwork_details?.sale_date_date
															).format('DD/MM/YYYY')
														: ''}
												</Txt>
											{/if}
										{/snippet}
									</TableCell>
									<TableCell
										onclick={(e) => {
											handleClickTimeCell(e, i);
										}}
										dataCy={dataCyPrefix}
										class={classNames('px-1', {
											'border-r border-blue-400 relative':
												timeSelection.length && i === timeSelection[0],
											'bg-blue-100 ': timeSelection.includes(i),
										})}
									>
										{#snippet custom()}
											{#if isInProgress}
												{#if i === timeSelection[0]}
													<div
														class="h-[1px] bg-blue-400 w-full left-0 absolute top-[-1px]"
													></div>
												{/if}
												<div class="flex gap-1">
													<Input
														dataCy={`${dataCyPrefix}-activity-time`}
														name="activity-time"
														placeholder="Activity Time"
														type="time"
														value={(
															scrapedArtwork as NonNullable<
																typeof sortedScrapedArtworks
															>[number]
														)?.artwork_details?.sale_date_time}
														size="sm"
														onkeyup={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'sale_date_time',
																false
															);
														}}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'sale_date_time',
																false
															);
														}}
													/>

													<Button
														disabled={!!timeSelection.length &&
															timeSelection[0] !== i}
														onclick={() => {
															if (!timeSelection.length) {
																handleInitTimeSelection(i);
															} else if (timeSelection[0] === i) {
																handleSubmitTimeSelection();
															}
														}}
														class="w-[34px] h-[34px] outline-none"
														variant="secondary"
														dataCy="expand-time"
														size="sm"
														>{#if !timeSelection.length}<FastClockIcon
															/>{:else if timeSelection[0] === i}<TickIcon
															/>{/if}</Button
													>
												</div>
											{:else}
												<Txt variant="body3">
													{(
														scrapedArtwork as NonNullable<
															typeof sortedScrapedArtworks
														>[number]
													)?.artwork_details?.sale_date_time || ''}
												</Txt>
											{/if}
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if scrapedArtwork.artwork_details}
												{#if isInProgress}
													<Input
														size="sm"
														dataCy={dataCyPrefix}
														name="lot_number"
														onkeyup={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'lot_number',
																false
															);
														}}
														onchange={(e) => {
															handleChangeArtworkDetails(scrapedArtwork);
															handleValueChange(
																scrapedArtwork,
																e,
																'lot_number',
																false
															);
														}}
														value={`${
															scrapedArtwork.artwork_details.lot_number === null
																? ''
																: scrapedArtwork.artwork_details.lot_number
														}`}
													/>
												{:else}
													<Txt variant="body3">
														{scrapedArtwork.artwork_details.lot_number === null
															? ''
															: scrapedArtwork.artwork_details.lot_number}
													</Txt>
												{/if}
											{/if}
										{/snippet}
									</TableCell>
								</TableRow>
							{/each}
						{/key}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={twMerge(
										classNames({
											'w-[136px]': !i,
										}),
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3">
											{artwork?.artwork_feed?.price || ''}
										</Txt>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.currency?.code || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.estimate_low || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.estimate_high || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.price || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.price_includes_premium
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{capitalizeFirstLetters(
												(artwork?.artwork_details?.sale_status || '')
													.replaceAll('_', ' ')
													.toLowerCase()
											)}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.sale_date_date
												? dayjs(
														artwork?.artwork_details?.sale_date_date
													).format('DD/MM/YYYY')
												: artwork?.artwork_details?.sale_date
													? dayjs(
															getDateFromDateString(
																artwork?.artwork_details?.sale_date
															)
														).format('DD/MM/YYYY')
													: ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.sale_date_time ||
												getTimeFromDateString(
													artwork?.artwork_details?.sale_date
												) ||
												''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.lot_number || ''}</Txt
										>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>

				<KeyboardShortcutsDialog dialogStores={shortcutsDialogStores} />
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={false}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
