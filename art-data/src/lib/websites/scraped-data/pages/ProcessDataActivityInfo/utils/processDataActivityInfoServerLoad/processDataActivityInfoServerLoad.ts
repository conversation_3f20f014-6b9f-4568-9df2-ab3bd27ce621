import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetCurrenciesDocument } from '$lib/features/final-review/queries/__generated__/getCurrencies.generated';
import { gqlClient } from '$lib/gqlClient';
import type { ScrapedDataProcessDataImagesPageServerLoadEvent } from '$routes/scraped-data/process-data/images/types';

export const processDataActivityInfoServerLoad = async ({
	parent,
}: ScrapedDataProcessDataImagesPageServerLoadEvent) => {
	const parentData = await parent();

	const currencyResponse = await gqlClient.request(
		GetCurrenciesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const currencies = currencyResponse?.currency;
	return { currencies };
};
