export enum ArtworkStatusTypes {
	ForSale = 'FOR_SALE',
	Upcoming = 'UPCOMING',
	BoughtIn = 'BOUGHT_IN',
	Withdrawn = 'WITHDRAWN',
	Reserved = 'RESERVED',
	NotForSale = 'NOT_FOR_SALE',
	Unlisted = 'UNLISTED',
	Declined = 'DECLINED',
	Sold = 'SOLD',
	NotReported = 'NOT_REPORTED',
	Loaned = 'LOANED',
	Permanent = 'PERMANENT',
	Owned = 'OWNED',
	Activated = 'ACTIVATED',
	Terminated = 'TERMINATED',
	Observed = 'OBSERVED',
	Restituted = 'RESTITUTED',
	Gifted = 'GIFTED',
}
