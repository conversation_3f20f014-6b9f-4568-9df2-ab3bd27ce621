<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { FetchStatsDocument } from '../../custom-queries/__generated__/fetchStats.generated';
	import { fetchScrapedArtworks } from '../../utils/fetchScrapedArtworks/fetchScrapedArtworks';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { STAGE_FIELD_MAPPING } from './constants/stage_field_mapping';
	import { SubmittingArtworksDialog } from './SubmittingArtworksDialog';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ErrorIcon } from '$global/assets/styled-icons/ErrorIcon';
	import { TickIcon } from '$global/assets/styled-icons/TickIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Select } from '$global/components/Select';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableCell,
	} from '$global/components/Table';
	import TableRow from '$global/components/Table/TableRow/TableRow.svelte';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { IngestArtworksDocument } from '../../custom-queries/__generated__/ingestArtworks.generated';
	import { ArtworkSourceType } from '$gql/types-custom';
	import { UpdateScrapedArtworksDocument } from '$lib/queries/__generated__/updateScrapedArtworks.generated';
	import type { ScrapedDataProcessDataFinalisePageData } from '$routes/scraped-data/process-data/finalise/types';

	interface Props {
		class?: string;
	}

	let { ...props }: Props = $props();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.StageAsc);
	let showConfirmation = $state(false);
	let showStats = $state(false);
	let loadingStats = $state(false);

	let data = $derived(
		getPageData<ScrapedDataProcessDataFinalisePageData>(page.data)
	);
	let stats = $state(null as null | (string | number | null | undefined)[][]);
	let submitting = $state(false);
	let scrapedArtworks = $state(
		null as null | Awaited<ReturnType<typeof fetchScrapedArtworks>>
	);

	let nbArtworksIngested: null | number = $state(null);
	let nbArtworksIngestionFailed: null | number = $state(null);

	const setNbArtworks = (
		artworks: Awaited<ReturnType<typeof fetchScrapedArtworks>>
	) => {
		const findArtworksNumber = (status: string) =>
			artworks.filter((artwork) => artwork.status?.key === status).length;

		nbArtworksIngested = findArtworksNumber('COMPLETED');
		nbArtworksIngestionFailed = findArtworksNumber('INGESTION_FAILED');
	};

	onMount(() => {
		saveStage();
	});

	const {
		artworkFeeds,
		setSubmittingArtworks,
		saveStage,
		resetCurrentIds,
		showMatchEventsWarning,
		scrapedArtworksStore,
	} = getArtworkFeedsStore();

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !scrapedArtworks) {
			const artworks = [...$scrapedArtworksStore];
			setNbArtworks(artworks);

			if (
				$artworkFeeds.submittingArtworks &&
				nbArtworksIngested !== null &&
				nbArtworksIngestionFailed !== null &&
				nbArtworksIngested + nbArtworksIngestionFailed < artworks.length
			) {
				submittingArtworksDialogStores.states.open.set(true);
			}

			scrapedArtworks = artworks;
		}
	});

	$effect(() => {
		if (
			nbArtworksIngested !== null &&
			scrapedArtworks &&
			nbArtworksIngestionFailed !== null &&
			nbArtworksIngested + nbArtworksIngestionFailed === scrapedArtworks.length
		) {
			setSubmittingArtworks(false);
			showConfirmation = true;
		}
	});

	const submittingArtworksDialogStores = createDialog();

	const handleClickFetchStats = async () => {
		loadingStats = true;

		const statsRes = await gqlClientCustom.request(
			FetchStatsDocument,
			{
				input: {
					scrapedArtworkIds: (scrapedArtworks || []).map(
						(scrapedArtwork) => scrapedArtwork.id
					),
				},
			},
			getAuthorizationHeaders(data)
		);

		stats = statsRes?.fetchStats?.stats
			?.map((stat) => [
				stat.field
					? STAGE_FIELD_MAPPING[stat.field as keyof typeof STAGE_FIELD_MAPPING]
					: null,
				stat.field,
				stat.reviewed,
				stat.changed,
				stat.changedPercentage,
			])
			.filter((row) => row[0])
			.sort((rowA, rowB) => (rowA[0] as number) - (rowB[0] as number));

		showStats = true;
		loadingStats = false;
	};

	const handleSubmit = async () => {
		submitting = true;

		try {
			const updateScrapedArtworksRes = await gqlClient.request(
				UpdateScrapedArtworksDocument,
				{
					ids: scrapedArtworks?.map((artwork) => artwork?.id) as string[],
					data: {
						status: {
							key: 'REVIEWED_AND_SUBMITTED',
							name: 'Reviewed and Submitted',
						},
					},
				},
				getAuthorizationHeaders(data)
			);

			scrapedArtworksStore.set(
				updateScrapedArtworksRes.update_Scraped_Artwork_items
			);

			await gqlClientCustom.request(
				IngestArtworksDocument,
				{
					input: {
						source_type: ArtworkSourceType.Scraped,
						artwork_ids: scrapedArtworks?.map(
							(artwork) => artwork?.id
						) as string[],
					},
				},
				getAuthorizationHeaders(data)
			);

			setSubmittingArtworks(true);
			submittingArtworksDialogStores.states.open.set(true);
		} catch {
			submitting = false;

			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		}
	};

	const headers = ['Stage', 'Field', 'Reviewed', 'Changed', '% Changed'];

	let sortedStats = $derived(
		[...(stats || [])]?.sort((statA, statB) => {
			const index = (() => {
				switch (sort) {
					case ArtworkDetailsSort.FieldAsc:
					case ArtworkDetailsSort.FieldDesc:
						return 1;
					case ArtworkDetailsSort.ReviewedAsc:
					case ArtworkDetailsSort.ReviewedDesc:
						return 2;
					case ArtworkDetailsSort.ChangedAsc:
					case ArtworkDetailsSort.ChangedDesc:
						return 3;
					case ArtworkDetailsSort.PcChangeAsc:
					case ArtworkDetailsSort.PcChangeDesc:
						return 4;
					case ArtworkDetailsSort.StageAsc:
					case ArtworkDetailsSort.StageDesc:
					default:
						return 0;
				}
			})();

			if (index === 1) {
				if ((statA?.[index] as number) < (statB?.[index] as number)) {
					return sort.endsWith('ASC') ? -1 : 1;
				}
				if ((statA?.[index] as number) > (statB?.[index] as number)) {
					return sort.endsWith('ASC') ? 1 : -1;
				}
				return 0;
			}

			if (sort.endsWith('DESC')) {
				return (statB?.[index] as number) - (statA?.[index] as number);
			} else {
				return (statA?.[index] as number) - (statB?.[index] as number);
			}
		})
	);

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	let completedArtworkIds = $derived(
		scrapedArtworks
			?.filter((artwork) => artwork?.status?.key === 'COMPLETED')
			?.map((artwork) => JSON.parse(`${artwork?.created_artwork_id}`).id)
			?.join(',')
	);

	let completedActivityIds = $derived(
		scrapedArtworks
			?.filter((artwork) => artwork?.status?.key === 'COMPLETED')
			?.map((artwork) => artwork?.processed_activity_id)
			?.join(',')
	);

	const handleClickCopyArtworkIds = () => {
		if (scrapedArtworks) {
			navigator.clipboard.writeText(`${completedArtworkIds}`);
		}
	};

	const handleClickCopyActivityIds = () => {
		if (scrapedArtworks) {
			navigator.clipboard.writeText(`${completedActivityIds}`);
		}
	};

	const handleClickCopyFailedArtworkIds = () => {
		if (scrapedArtworks) {
			navigator.clipboard.writeText(
				scrapedArtworks
					?.filter((artwork) => artwork?.status?.key === 'INGESTION_FAILED')
					?.map((artwork) => artwork?.id)
					?.join(',')
			);
		}
	};

	const dataCyPrefix = 'scraped-data-process-finalise';
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={9} />

			{#if !scrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else if !showConfirmation}
				{#if showStats}
					<div
						class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
					>
						<Txt variant="body2">Write instructions about this stage here.</Txt>
						<Select
							bind:value={sort}
							ariaLabel="Sort entries"
							name="sort"
							class="[&>button>p]:uppercase"
							dataCy={`${dataCyPrefix}-sort`}
							selectedPrefix="SORT BY:"
							options={Object.values(ArtworkDetailsSort).map((sort) => ({
								label: sort,
								value: sort,
							}))}
							classes={{
								placeholder: 'font-medium tracking-widest',
								menu: 'z-50',
							}}
						/>
					</div>

					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix} class={props.class}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={classNames({
										'w-[140px]': !i,
									})}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>
						{#if sortedStats}
							<TableBody dataCy={dataCyPrefix}>
								{#each sortedStats as stat, i}
									<TableRow index={i} dataCy={dataCyPrefix}>
										<TableCell dataCy={dataCyPrefix}>{stat[0]}</TableCell>
										<TableCell dataCy={dataCyPrefix}>{stat[1]}</TableCell>
										<TableCell dataCy={dataCyPrefix}>{stat[2]}</TableCell>
										<TableCell dataCy={dataCyPrefix}>{stat[3]}</TableCell>
										<TableCell dataCy={dataCyPrefix}>{stat[4]}</TableCell>
									</TableRow>
								{/each}
							</TableBody>
						{/if}
					</table>
				{:else}
					<div
						class="flex h-[250px] w-full items-center justify-center bg-gray-100"
					>
						<div class="flex flex-col items-center">
							<Txt variant="h4" class="mb-6">You can fetch stats here</Txt>
							<Button
								size="md"
								dataCy="fetch-stats"
								onclick={handleClickFetchStats}
								loading={loadingStats}
							>
								Fetch Stats
							</Button>
						</div>
					</div>
				{/if}
			{:else}
				<div
					class="my-10 flex flex-col items-center border border-gray-200 bg-gray-100 p-10 pb-16"
				>
					{#if nbArtworksIngested}
						<TickIcon class="h-12 w-12" />
					{:else}
						<ErrorIcon class="h-12 w-12" />
					{/if}

					<Txt variant="h3" class="my-4">
						{nbArtworksIngested
							? 'Data submitted to Arteye'
							: 'All items submitted have failed to process'}
					</Txt>

					{#if nbArtworksIngested}
						<Txt variant="body3">
							Ingestion completed for {nbArtworksIngested} artwork(s)
						</Txt>
						<Button
							size="md"
							class="mt-2 mb-1 w-[350px]"
							variant="secondary"
							dataCy={dataCyPrefix}
							onclick={handleClickCopyArtworkIds}
							>Copy {nbArtworksIngested} artwork ids</Button
						>
						<Button
							size="md"
							class="w-[350px]"
							variant="secondary"
							dataCy={dataCyPrefix}
							onclick={handleClickCopyActivityIds}
							>Copy {nbArtworksIngested} activity ids</Button
						>
					{/if}

					{#if nbArtworksIngestionFailed}
						<Txt class="text-red-500 mt-4" variant="body3">
							Ingestion failed for {nbArtworksIngestionFailed} artwork(s)
						</Txt>
						<Button
							size="md"
							class="w-[350px] mt-2"
							variant="secondary"
							dataCy={dataCyPrefix}
							onclick={handleClickCopyFailedArtworkIds}
							>Copy failed scraped artworks ids</Button
						>
					{/if}

					{#if nbArtworksIngested}
						<Txt
							class="block mt-2 text-center text-blue-500"
							rel="noopener noreferrer"
							target="_blank"
							component="a"
							variant="body2"
							href={`${Config.ArteyeDomain}/artworks-and-activities?searchType=multipleIds${encodeURIComponent(`&type=artworkIds&artworkIds`)}=${completedArtworkIds}`}
						>
							View artworks in Arteye
						</Txt>
						<Txt
							variant="body2"
							component="a"
							href={`${Config.ArteyeDomain}/artworks-and-activities?searchType=multipleIds${encodeURIComponent(`&type=activityIds&activityIds`)}=${completedActivityIds}`}
							class="block mt-2 text-center text-blue-500"
							rel="noopener noreferrer"
							target="_blank"
						>
							View activities in Arteye
						</Txt>
					{/if}
				</div>
			{/if}
		</div>
	</Container>
</PageBody>

{#if nbArtworksIngested !== null && nbArtworksIngestionFailed !== null && !!scrapedArtworks && nbArtworksIngested + nbArtworksIngestionFailed < scrapedArtworks.length}
	<SubmittingArtworksDialog
		bind:scrapedArtworks
		dialogStores={submittingArtworksDialogStores}
		{setNbArtworks}
		{nbArtworksIngested}
		{nbArtworksIngestionFailed}
	/>
{/if}

{#if !showConfirmation}
	<PageSaveBar
		loading={submitting}
		disabled={false}
		onSaveClick={handleSubmit}
		visible={!!scrapedArtworks?.length}
		nextLabel={'Submit to arteye'}
		backHref={Routes.ScrapedDataProcessDataEntities}
	/>
{:else}
	<PageSaveBar
		loading={false}
		disabled={!nbArtworksIngested}
		onSaveClick={() => {
			goto(Routes.ScrapedDataProvenanceLinking);
		}}
		visible={!!scrapedArtworks?.length}
		nextLabel={'Continue to provenance linking'}
	>
		<Button
			onclick={() => {
				goto(Routes.ScrapedData);
				resetCurrentIds([], []);
			}}
			variant="secondary"
			dataCy={`${dataCyPrefix}-return`}
			size="md"
		>
			Return to scraped data
		</Button>
	</PageSaveBar>
{/if}
