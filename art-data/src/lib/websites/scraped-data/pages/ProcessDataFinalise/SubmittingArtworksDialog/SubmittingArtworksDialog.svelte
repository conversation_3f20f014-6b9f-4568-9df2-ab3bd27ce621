<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import { fetchScrapedArtworks } from '$lib/websites/scraped-data/utils/fetchScrapedArtworks/fetchScrapedArtworks';
	import { getArtworkFeedsStore } from '$lib/websites/scraped-data/utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import type { ScrapedDataProcessDataFinalisePageData } from '$routes/scraped-data/process-data/finalise/types';

	interface Props {
		scrapedArtworks: Awaited<ReturnType<typeof fetchScrapedArtworks>>;
		dialogStores: ReturnType<typeof createDialog>;
		nbArtworksIngested: number;
		nbArtworksIngestionFailed: number;
		setNbArtworks: (
			artworks: Awaited<ReturnType<typeof fetchScrapedArtworks>>
		) => void;
	}

	const dataCyPrefix = 'submitting-artworks';
	const { artworkFeeds, scrapedArtworksStore } = getArtworkFeedsStore();

	// const handleClickSkip = () => {
	// 	goto(Routes.ScrapedDataProvenanceLinking);
	// };

	let {
		scrapedArtworks = $bindable(),
		dialogStores,
		setNbArtworks,
		nbArtworksIngested,
		nbArtworksIngestionFailed,
	}: Props = $props();

	onMount(() => {
		let intervalId: ReturnType<typeof setInterval>;

		intervalId = setInterval(() => {
			fetchScrapedArtworks(
				page.data as ScrapedDataProcessDataFinalisePageData,
				$artworkFeeds.scrapedArtworkIds
			).then((artworks) => {
				scrapedArtworks = artworks;
				scrapedArtworksStore.set(artworks);
				setNbArtworks(artworks);
			});
		}, 10000);

		return () => {
			clearInterval(intervalId);
		};
	});
</script>

<Dialog
	shouldClose={false}
	showCloseIcon={false}
	dataCy={dataCyPrefix}
	{dialogStores}
	title="Submitting data"
>
	<CircularProgress dataCy={dataCyPrefix} class="w-8 h-8" />
	<Txt variant="body1" class="text-center mt-4">
		{nbArtworksIngested} / {$artworkFeeds.currentIds.length} item(s) ingested
	</Txt>
	<Txt variant="body2" class="text-center mt-2 text-red-500">
		Ingestion failed for {nbArtworksIngestionFailed} item(s)
	</Txt>

	<!-- {#if nbArtworksIngested >= 1}
		<div class="flex relative my-8">
			<hr class="w-full" />
			<Txt
				class="text-center bg-white absolute left-[50%] translate-x-[-50%] mt-[-12px] px-4"
				>OR</Txt
			>
		</div>
		<div class="flex justify-center">
			<Button
				onclick={handleClickSkip}
				variant="secondary"
				size="md"
				dataCy={`${dataCyPrefix}-skip`}>SKIP TO PROVENANCE LINKING</Button
			>
		</div>
		<Txt class="text-gray-500 text-center mt-3" variant="body3">
			You don't necessarily have to wait for the artworks to be ingested.
			However, you won't have access to the related processed artwork and
			activity ids.
		</Txt>
	{/if} -->
</Dialog>
