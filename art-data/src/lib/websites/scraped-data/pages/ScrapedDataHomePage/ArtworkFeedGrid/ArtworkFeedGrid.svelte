<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { Checkbox } from '$global/components/Checkbox';
	import { InputLabel } from '$global/components/InputLabel';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';
	import { WhiteBorderImageContainer } from '$global/components/WhiteBorderImageContainer';
	import { capitalizeFirstLetters } from '$global/utils/capitalizeFirstLetters';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetArtworkFeedQuery } from '$lib/websites/scraped-data/queries/__generated__/getArtworkFeed.generated';
	import { CUSTOM_EXTENSIONS_USER } from '$lib/constants/custom-extensions-user';

	interface Props {
		ids: string[];
		data: GetArtworkFeedQuery['artwork_feed'];
	}

	let { ids = $bindable(), data }: Props = $props();

	let dataCy = 'artwork-feed-grid';

	const handleToggleArtwork = (
		checked: string | boolean,
		artworkId: string
	) => {
		const selection = window.getSelection();
		if (selection?.toString().length) {
			return;
		}

		if (checked) {
			ids = [...ids, artworkId];
		} else {
			ids = ids.filter((id) => id !== artworkId);
		}
	};

	const handleChangeAll = (checked: string | boolean) => {
		if (checked === true) {
			ids = data.map((artwork) => artwork.id);
		} else {
			ids = [];
		}
	};

	const findSymbol = (
		artwork: GetArtworkFeedQuery['artwork_feed'][number],
		symbolKey: string
	) =>
		artwork.lot_symbols?.find(
			(symbol) => symbol?.lot_symbol?.key === symbolKey
		);
</script>

{#snippet lotSymbols(artwork: GetArtworkFeedQuery['artwork_feed'][number])}
	<div class="absolute right-1 top-1 z-10 flex">
		{#if findSymbol(artwork, 'guaranteed_sale')}
			<Tooltip
				class="text-black m-1 mb-0 flex h-[26px] w-[26px] items-center justify-center rounded-full bg-white text-xs font-bold border border-gray-200"
				classes={{
					content: 'w-60',
				}}
				{dataCy}
				content={'Guaranteed: The auction house agrees to guarantee to the seller that, whatever the outcome of the auction, the seller will receive a minimum sale price for the work or collection.'}
			>
				<div>G</div>
			</Tooltip>
		{/if}
		{#if findSymbol(artwork, 'irrevocable_bid')}
			<Tooltip
				class="text-black m-1 mb-0 flex h-[26px] w-[26px] items-center justify-center rounded-full bg-white text-xs font-bold border border-gray-200"
				classes={{
					content: 'w-60',
				}}
				{dataCy}
				content={'Irrevocable bid/Third Party Guarantee: an undisclosed bid on a piece agreed on before the auction.'}
			>
				<div>IB</div>
			</Tooltip>
		{/if}
	</div>
{/snippet}

{#if !data.length}
	<Txt variant="body2" class="text-center text-gray-500">No results</Txt>
{:else}
	<div class="mb-4 flex items-center justify-between">
		<div>
			<InputLabel {dataCy}>
				<Checkbox
					{dataCy}
					checked={ids.length === data.length
						? true
						: ids.length
							? 'indeterminate'
							: false}
					onChange={handleChangeAll}
					valueAfterIntermediate={false}
				/>
				{ids.length} selected of {data.length} results
			</InputLabel>
		</div>

		<Txt variant="body2">Max 1,000 results</Txt>
	</div>

	<div class="grid grid-cols-4 gap-4">
		{#each data as artwork}
			{@const scrapedArtworkDetais =
				artwork.processor_review_artwork?.[0]?.artwork_details}
			{@const artworkInfo = [
				artwork.cr_number,
				artwork.year_made,
				artwork.mediums,
				artwork.dimensions,
				artwork.edition,
			].filter(Boolean)}

			<button
				type="button"
				onmousedown={() => {
					if (window.getSelection()?.toString().length) {
						window.getSelection()?.empty();
					}
				}}
				onclick={() =>
					handleToggleArtwork(!ids.includes(artwork.id), artwork.id)}
				class={classNames(
					'flex flex-col border  py-4',
					ids.includes(artwork.id) ? 'border-blue-500' : 'border-gray-200'
				)}
			>
				<div class="px-4 w-full relative">
					<div class="flex justify-between w-full gap-2">
						<Txt variant="body2" class="text-left font-[500] select-text">
							{[
								capitalizeFirstLetters(
									(artwork.event_id?.event_type?.key || '').split('_').join(' ')
								),
								artwork.sale_starts_at
									? dayjs(artwork.sale_starts_at).format('DD MMMM YYYY')
									: '',
							]
								.filter(Boolean)
								.join(', ')}
						</Txt>
						<Checkbox
							size="sm"
							dataCy={`${dataCy}-artwork`}
							checked={ids.includes(artwork.id)}
						/>
					</div>
					{#if artwork.event_id?.organization}
						<Txt variant="body2" class="text-left select-text">
							{artwork.event_id.organization}
						</Txt>
					{/if}
					{#if artwork.lot_title}
						<Txt variant="body2" class="text-left select-text">
							{artwork.lot_title}
						</Txt>
					{/if}
					{#if artwork.lot_number}
						<Txt variant="body2" class="text-left  select-text">
							Lot #{artwork.lot_number}
						</Txt>
					{/if}
					{#if (artwork.price_currency?.symbol && artwork.price_amount) || artwork.sale_status?.key}
						<div class="mt-1 flex flex-col items-start gap-2">
							{#if artwork.price_currency?.symbol && artwork.price_amount}
								<Txt variant="h6" component="span" class="select-text">
									{artwork.price_currency?.symbol}{artwork.price_amount}
								</Txt>
							{:else if (artwork.estimate_low || artwork.estimate_high) && artwork.estimate_currency?.symbol}
								<Txt
									variant="h6"
									component="span"
									class="text-gray-500 select-text"
								>
									Est. {[
										artwork.estimate_low
											? `${artwork.estimate_currency?.symbol}${artwork.estimate_low}`
											: '',
										artwork.estimate_high
											? `${artwork.estimate_currency?.symbol}${artwork.estimate_high}`
											: '',
									]
										.filter(Boolean)
										.join(' - ')}
								</Txt>
							{/if}
							{#if artwork.sale_status?.key}
								<div
									class="rounded border border-gray-200 bg-gray-100 px-3 py-1.5"
								>
									<Txt variant="label3" class="select-text">
										{capitalizeFirstLetters(
											artwork.sale_status.key.split('_').join(' ')
										)}
									</Txt>
								</div>
							{/if}
						</div>
					{/if}

					<!-- custom extensions user id -->
					{#if scrapedArtworkDetais?.user_updated?.id && scrapedArtworkDetais?.user_updated?.id !== CUSTOM_EXTENSIONS_USER}
						{@const firstName = scrapedArtworkDetais?.user_updated?.first_name}
						{@const lastName = scrapedArtworkDetais?.user_updated?.last_name}
						<Tooltip
							class="text-black absolute right-1 bottom-0 z-10 m-2 mb-0 flex h-[26px] w-[26px] items-center justify-center rounded-full bg-white text-xs font-bold border border-gray-200"
							classes={{
								content: 'w-60',
							}}
							{dataCy}
							content={[firstName, lastName].filter(Boolean).join(' ')}
						>
							<div>
								{[
									firstName?.slice(0, 1)?.toUpperCase(),
									lastName?.slice(0, 1)?.toUpperCase(),
								]
									.filter(Boolean)
									.join('')}
							</div>
						</Tooltip>
					{/if}
				</div>

				<div
					class={classNames(
						'my-4 min-w-full border-y border-gray-200 relative',
						{
							'border-x': artwork?.primary_image?.id,
							'min-h-[45px]':
								!artwork?.primary_image?.id &&
								(findSymbol(artwork, 'irrevocable_bid') ||
									findSymbol(artwork, 'guaranteed_sale')),
							'border-b-0':
								!artwork?.primary_image?.id &&
								!findSymbol(artwork, 'irrevocable_bid') &&
								!findSymbol(artwork, 'guaranteed_sale'),
						}
					)}
				>
					{@render lotSymbols(artwork)}
					{#if artwork?.primary_image?.id}
						<WhiteBorderImageContainer
							class={classNames(
								'h-[18.125rem] p-[0.5rem] [&>div]:max-h-[18.125rem]'
							)}
							image={{
								width: artwork?.primary_image?.width || 1,
								height: artwork?.primary_image?.height || 1,
								url: `${getImageUrl(artwork?.primary_image?.id)}`,
							}}
							alt={''}
							whiteBorderFix
							{dataCy}
						/>
					{/if}
				</div>

				{#if artwork.artist || artwork.artist_clean_name}
					<Txt
						variant="body2"
						class="mx-4 mb-1 text-left font-[500] text-gray-500 select-text"
					>
						{#if artwork.artist_clean_name}
							{artwork.artist_clean_name}
							{artwork.artist_nationality?.country_nationality ||
							artwork.artist_year_born ||
							artwork.artist_year_died
								? ` (${[
										artwork.artist_nationality?.country_nationality || '',
										[
											artwork.artist_year_born || '',
											artwork.artist_year_died || '',
										]
											.filter(Boolean)
											.join(' - '),
									]
										.filter(Boolean)
										.join(', ')})`
								: ''}
						{:else if artwork.artist}
							{artwork.artist}
						{/if}
					</Txt>
				{/if}
				{#if artwork.title}
					<Txt variant="h6" class="mx-4 mb-2 text-left select-text">
						{artwork.title}
					</Txt>
				{/if}
				{#if artwork.artist || artwork.artist_clean_name || artwork.title}
					<hr class="mx-auto mb-2 w-[calc(100%-2rem)]" />
				{/if}

				{#each artworkInfo as info}
					<Txt variant="body2" class="mx-4 text-left select-text">
						{info}
					</Txt>
				{/each}
			</button>
		{/each}
	</div>
{/if}
