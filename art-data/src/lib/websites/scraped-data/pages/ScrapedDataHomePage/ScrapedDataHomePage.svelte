<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import customParseFormat from 'dayjs/plugin/customParseFormat';
	import { type SearchScrapeDataQuery } from '../../custom-queries/__generated__/searchScrapeData.generated';
	import { GetArtworkFeedDocument } from '../../queries/__generated__/getArtworkFeed.generated';
	import type { GetArtworkFeedQuery } from '../../queries/__generated__/getArtworkFeed.generated';
	import { UpdateArtworkFeedItemsDocument } from '../../queries/__generated__/updateArtworkFeeds.generated';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { AllScrapedDataForm } from './AllScrapedDataForm';
	import { ArtworkFeedForm } from './ArtworkFeedForm';
	import { ArtworkFeedGrid } from './ArtworkFeedGrid';
	import { ScrapedDataHomePageTable } from './ScrapedDataHomePageTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Dialog } from '$global/components/Dialog';
	import { LinkButton } from '$global/components/LinkButton';
	import { Tabs } from '$global/components/Tabs';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { SearchRange } from '$lib/constants/search-range-options';
	import { gqlClient } from '$lib/gqlClient';

	dayjs.extend(customParseFormat);
	const dataCyPrefix = 'scraped-data';
	let searching = $state(false);

	const confirmationDialogStores = createDialog();

	const handleClickCloseConfirmationDialog = () => {
		confirmationDialogStores.states.open.set(false);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data' },
	];

	let activeTab = $state(0);
	let submitting = $state(false);
	let archiving = $state(false);
	let submittingProvenanceLinking = $state(false);

	// All scraped data state
	let pageNumber = $state(1);
	let showProvenanceLinkingButton = $state(false);
	let searchScrapeDataIds: string[] = $state([]);
	let searchScrapeData: SearchScrapeDataQuery['searchScrapeData'] = $state();
	let submittedGroupBy: { value: string; label: string }[] = $state([]);
	let allScrapedDataForm:
		| undefined
		| { searchScrapeData: () => Promise<SearchScrapeDataQuery> } = $state();

	// Upcoming lots state
	let upcomingLotsData: null | undefined | GetArtworkFeedQuery['artwork_feed'] =
		$state();
	let upcomingLotsIds: string[] = $state([]);
	let upcomingLotsForm:
		| undefined
		| { searchArtworkFeed: () => Promise<GetArtworkFeedQuery> } = $state();

	// Recent results state
	let recentResultsData:
		| null
		| undefined
		| GetArtworkFeedQuery['artwork_feed'] = $state();
	let recentResultsIds: string[] = $state([]);
	let recentResultsForm:
		| undefined
		| { searchArtworkFeed: () => Promise<GetArtworkFeedQuery> } = $state();

	// Private market state
	let privateMarketData:
		| undefined
		| null
		| GetArtworkFeedQuery['artwork_feed'] = $state();
	let privateMarketIds: string[] = $state([]);
	let privateMarketForm:
		| undefined
		| { searchArtworkFeed: () => Promise<GetArtworkFeedQuery> } = $state();

	const { setUnprocessedIds, setCurrentIds, artworkFeeds } =
		getArtworkFeedsStore();

	const tabs = [
		{ title: 'All scraped data', id: '1' },
		{ title: 'Upcoming lots', id: '2' },
		{ title: 'Recents results', id: '3' },
		{ title: 'Private market', id: '4' },
	];

	const handleClickSearch = async (e: Event | undefined) => {
		e?.preventDefault();
		searching = true;
		pageNumber = 1;

		try {
			switch (activeTab) {
				case 0: {
					if (!allScrapedDataForm) {
						return;
					}

					searchScrapeData = null;
					searchScrapeDataIds = [];

					const searchScrapeDataRes =
						await allScrapedDataForm?.searchScrapeData();

					searchScrapeData = searchScrapeDataRes?.searchScrapeData;
					break;
				}
				case 1: {
					if (!upcomingLotsForm) {
						return;
					}

					upcomingLotsData = null;
					upcomingLotsIds = [];

					const upcomingLotsRes = await upcomingLotsForm?.searchArtworkFeed();

					upcomingLotsData = upcomingLotsRes?.artwork_feed;
					break;
				}
				case 2: {
					if (!recentResultsForm) {
						return;
					}

					recentResultsData = null;
					recentResultsIds = [];

					const recentResultsRes = await recentResultsForm?.searchArtworkFeed();

					recentResultsData = recentResultsRes?.artwork_feed;
					break;
				}
				case 3: {
					if (!privateMarketForm) {
						return;
					}

					privateMarketData = null;
					privateMarketIds = [];

					const privateMarketRes = await privateMarketForm?.searchArtworkFeed();

					privateMarketData = privateMarketRes?.artwork_feed;
					break;
				}
			}
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong.',
			});
		} finally {
			searching = false;
		}
	};

	let getCurrentData = $derived(() => {
		switch (activeTab) {
			case 0: {
				return searchScrapeData;
			}
			case 1: {
				return upcomingLotsData;
			}
			case 2: {
				return recentResultsData;
			}
			case 3: {
				return privateMarketData;
			}
			default: {
				return null;
			}
		}
	});

	let getCurrentIds = $derived(() => {
		switch (activeTab) {
			case 0: {
				return searchScrapeDataIds;
			}
			case 1: {
				return upcomingLotsIds;
			}
			case 2: {
				return recentResultsIds;
			}
			case 3: {
				return privateMarketIds;
			}
			default: {
				return [];
			}
		}
	});

	const handleClickArchive = async (e: Event | undefined) => {
		archiving = true;

		try {
			await gqlClient.request(
				UpdateArtworkFeedItemsDocument,
				{
					ids: getCurrentIds(),
					data: { archived: true },
				},
				getAuthorizationHeaders({
					user: { access_token: page.data.user.access_token },
				})
			);

			searchScrapeData = null;
			searchScrapeDataIds = [];

			await handleClickSearch(e);

			showToast({
				message: 'Selected artworks have been successfully archived.',
				variant: 'success',
			});
		} catch {
			showToast({
				message: 'Something went wrong when archiving the selected artworks.',
				variant: 'error',
			});
		} finally {
			archiving = false;
		}
	};

	const handleReviewProvenanceLinkingClick = async () => {
		submittingProvenanceLinking = true;
		setUnprocessedIds(getCurrentIds());

		const artworkFeedsRes = await gqlClient.request(
			GetArtworkFeedDocument,
			{
				limit: -1,
				filter: {
					id: { _in: getCurrentIds() },
				},
			},
			getAuthorizationHeaders({
				user: { access_token: page.data.user.access_token },
			})
		);

		const scrapedArtworkIds = artworkFeedsRes?.artwork_feed?.map(
			(feed) => feed?.processor_review_artwork?.[0]?.id
		) as string[];

		setCurrentIds(getCurrentIds(), scrapedArtworkIds);
		goto(Routes.ScrapedDataProvenanceLinking);
	};

	const handleSaveClick = () => {
		submitting = true;
		setUnprocessedIds(getCurrentIds());
		goto(Routes.ScrapedDataPreview);
	};
</script>

<PageBody
	class={classNames({
		'pointer-events-none':
			searching || submitting || archiving || submittingProvenanceLinking,
	})}
>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container class="max-w-none" dataCy={dataCyPrefix}>
		<div class="mb-6 flex items-center justify-between">
			<Txt variant="h3">Scraped Data</Txt>
		</div>

		{#if $artworkFeeds.stage}
			<div class="bg-gray-0 my-3 rounded border border-amber-500 p-4">
				<Txt
					variant="body3"
					class="text-amber-800"
					dataCy={`${dataCyPrefix}-warning`}
				>
					You haven't completed the review you previously started. Please
					complete it <a class="underline" href={$artworkFeeds.stage}>here</a>.
				</Txt>
			</div>
		{/if}

		<form onsubmit={handleClickSearch} class="mb-8 border border-gray-200 pb-4">
			<div class="border-b border-gray-200 px-4">
				<Tabs
					class="mb-[-1px]"
					dataCy={dataCyPrefix}
					bind:activeTab
					{tabs}
					fullLine={false}
					classes={{ container: 'mt-0' }}
				/>
			</div>

			{#if !activeTab}
				<AllScrapedDataForm
					bind:showProvenanceLinkingButton
					bind:submittedGroupBy
					bind:this={allScrapedDataForm}
					{searching}
				/>
			{:else if activeTab === 1}
				<ArtworkFeedForm
					label="Search upcoming lots from favourite artists"
					bind:showProvenanceLinkingButton
					bind:this={upcomingLotsForm}
					{searching}
					filters={[
						{
							event_id: { event_type: { processor_type: { _eq: 'AUCTION' } } },
						},
						{
							sale_starts_at: {
								_gte: dayjs().toISOString(),
							},
						},
					]}
					validateDate={(date: string) => {
						return dayjs(date, 'DD/MM/YYYY', true).unix() >=
							dayjs().startOf('day').unix()
							? ''
							: 'Must be in the future';
					}}
				/>
			{:else if activeTab === 2}
				<ArtworkFeedForm
					label="Search recent results from favourite artists"
					bind:showProvenanceLinkingButton
					bind:this={recentResultsForm}
					{searching}
					filters={[
						{
							event_id: { event_type: { processor_type: { _eq: 'AUCTION' } } },
						},
						{ sale_status: { key: { _eq: 'sold' } } },
						{
							sale_starts_at: {
								_between: [
									dayjs().subtract(3, 'month').toISOString(),
									dayjs().toISOString(),
								],
							},
						},
					]}
					validateDate={(date: string) => {
						return dayjs(date, 'DD/MM/YYYY', true).unix() <
							dayjs().endOf('day').unix()
							? ''
							: 'Must be in the past';
					}}
				/>
			{:else if activeTab === 3}
				<ArtworkFeedForm
					label="Search private market from favourite artists"
					bind:showProvenanceLinkingButton
					bind:this={privateMarketForm}
					showPrivateMarketInput={true}
					defaultDateRange={SearchRange.GreaterThan}
					defaultDate={dayjs().subtract(10, 'days')}
					{searching}
					filters={[
						{
							event_id: {
								event_type: {
									processor_type: {
										_eq: 'PRIVATE_MARKET',
									},
								},
							},
						},
					]}
				/>
			{/if}
		</form>

		{#if searching}
			<div class="flex justify-end">
				<CircularProgress dataCy={dataCyPrefix} />
			</div>
		{:else if searchScrapeData && !activeTab}
			<ScrapedDataHomePageTable
				data={searchScrapeData}
				groupBy={submittedGroupBy}
				bind:pageNumber
				bind:ids={searchScrapeDataIds}
			/>
		{:else if upcomingLotsData && activeTab === 1}
			<ArtworkFeedGrid data={upcomingLotsData} bind:ids={upcomingLotsIds} />
		{:else if recentResultsData && activeTab === 2}
			<ArtworkFeedGrid data={recentResultsData} bind:ids={recentResultsIds} />
		{:else if privateMarketData && activeTab === 3}
			<ArtworkFeedGrid data={privateMarketData} bind:ids={privateMarketIds} />
		{/if}
	</Container>
</PageBody>

<PageSaveBar
	visible={!!getCurrentData()}
	disabled={!getCurrentIds()?.length ||
		submitting ||
		archiving ||
		submittingProvenanceLinking}
	onSaveClick={$artworkFeeds.stage
		? () => {
				confirmationDialogStores.states.open.set(true);
			}
		: handleSaveClick}
	loading={submitting}
	nextLabel={!activeTab || (activeTab && !getCurrentIds()?.length)
		? 'Continue with selected'
		: `Continue with ${getCurrentIds().length} selected`}
>
	{#if !activeTab}
		<Button
			disabled={!getCurrentIds()?.length ||
				submitting ||
				archiving ||
				submittingProvenanceLinking}
			onclick={handleClickArchive}
			variant="secondary"
			loading={archiving}
			dataCy={`${dataCyPrefix}-archive`}
			size="md"
		>
			ARCHIVE SELECTED
		</Button>

		{#if showProvenanceLinkingButton}
			<Button
				disabled={!getCurrentIds()?.length ||
					submitting ||
					archiving ||
					submittingProvenanceLinking}
				onclick={handleReviewProvenanceLinkingClick}
				variant="primary"
				loading={submittingProvenanceLinking}
				dataCy={`${dataCyPrefix}-provenance-linking`}
				size="md"
			>
				REVIEW PROVENANCE LINKING
			</Button>
		{/if}
	{:else if activeTab && getCurrentData()}
		<Button
			onclick={() => {
				if (getCurrentIds().length === (getCurrentData() as object[])?.length) {
					switch (activeTab) {
						case 1: {
							upcomingLotsIds = [];
							return;
						}
						case 2: {
							recentResultsIds = [];
							return;
						}
						case 3: {
							privateMarketIds = [];
							return;
						}
						default: {
							return;
						}
					}
				} else {
					switch (activeTab) {
						case 1: {
							upcomingLotsIds = (upcomingLotsData as { id: string }[]).map(
								(artwork) => artwork.id
							);
							return;
						}
						case 2: {
							recentResultsIds = (recentResultsData as { id: string }[]).map(
								(artwork) => artwork.id
							);
							return;
						}
						case 3: {
							privateMarketIds = (privateMarketData as { id: string }[]).map(
								(artwork) => artwork.id
							);
							return;
						}
						default: {
							return;
						}
					}
				}
			}}
			variant="secondary"
			dataCy={`${dataCyPrefix}-archive`}
			size="md"
		>
			{getCurrentIds().length === (getCurrentData() as object[])?.length
				? 'DE-SELECT ALL'
				: `SELECT ALL ${(getCurrentData() as object[])?.length}`}
		</Button>
	{/if}
</PageSaveBar>

<Dialog
	dataCy={dataCyPrefix}
	dialogStores={confirmationDialogStores}
	title={`Do you really want to continue ?`}
	shouldClose={false}
	showCloseIcon={false}
	onClose={handleClickCloseConfirmationDialog}
>
	<Txt class="text-center" variant="body1"
		>You haven't completed the review you previously started. Starting a new
		review process will erase your previous progress.</Txt
	>

	<div class="mt-6 flex gap-2 xs:flex-col">
		<Button
			disabled={submitting}
			dataCy={`${dataCyPrefix}-cancel`}
			size="md"
			variant="secondary"
			onclick={handleSaveClick}>start new review</Button
		>
		<LinkButton
			class="[&>button]:w-full"
			loading={submitting}
			href={$artworkFeeds.stage}
			dataCy={`${dataCyPrefix}-confirm`}
			size="md"
			>resume ongoing review
		</LinkButton>
	</div>
</Dialog>
