<script lang="ts">
	import classNames from 'classnames';
	import { GROUP_BY_OPTIONS } from '../types';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect';
	import { Radio } from '$global/components/Radio';
	import { NO_VALUE, Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { capitalizeFirstLetters } from '$global/utils/capitalizeFirstLetters';
	import {
		ScrapedDataType,
		GroupBy,
		SortOrder,
		DataFilter,
	} from '$gql/types-custom';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { SearchScrapeDataDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/searchScrapeData.generated';
	import type { ScrapedDataPageData } from '$routes/scraped-data/types';

	interface Props {
		searching: boolean;
		showProvenanceLinkingButton: boolean;
		submittedGroupBy?: { value: string; label: string }[];
	}

	let {
		showProvenanceLinkingButton = $bindable(),
		searching,
		submittedGroupBy = $bindable([]),
	}: Props = $props();

	const getDefaultGroupBy = () =>
		GROUP_BY_OPTIONS.filter((option) => option.default);

	const sortOptions = [
		GroupBy.Datasource,
		GroupBy.Dealer,
		GroupBy.SaleName,
		GroupBy.Artist,
		GroupBy.Type,
		GroupBy.SaleDate,
		GroupBy.DateScraped,
		GroupBy.UserAssigned,
	].flatMap((fieldName) =>
		[SortOrder.Asc, SortOrder.Desc].map((order) => {
			const isAscDesc = [GroupBy.DateScraped, GroupBy.SaleDate].includes(
				fieldName
			);

			const fieldNameLabels = {
				[GroupBy.Artist]: 'Artist',
				[GroupBy.Datasource]: 'Source',
				[GroupBy.DateScraped]: 'Date scraped',
				[GroupBy.Dealer]: 'Dealer',
				[GroupBy.SaleDate]: 'Sale date',
				[GroupBy.SaleName]: 'Sale name',
				[GroupBy.Type]: 'Type',
				[GroupBy.UserAssigned]: 'Processing By',
			};

			const orderLabel = (() => {
				if (isAscDesc) {
					return order;
				}

				return order === SortOrder.Asc ? '(A-Z)' : '(Z-A)';
			})();

			return {
				value: `${fieldName}-${order}`,
				label: `${fieldNameLabels[fieldName]} ${orderLabel}`,
			};
		})
	);

	let dataCyPrefix = 'all-scraped-data-form';
	let users = $derived((page.data as ScrapedDataPageData).users);
	let sourceValue = $state('');
	let dealerValue = $state('');
	let saleNameValue = $state('');
	let artistValue = $state('');
	let saleDateValue = $state('');
	let typeValue: ScrapedDataType | typeof NO_VALUE = $state(NO_VALUE);
	let dateScrapedValue = $state('');
	let processingByValue = $state(NO_VALUE);
	let sortValue: MultiSelectOption[] = $state([
		sortOptions.find(
			(option) => option.value === `${GroupBy.SaleDate}-${SortOrder.Desc}`
		),
	] as MultiSelectOption[]);

	let showProcessed = $state(DataFilter.Unprocessed);
	let groupBy: { value: GroupBy; label: string }[] =
		$state(getDefaultGroupBy());

	export const searchScrapeData = () => {
		submittedGroupBy = groupBy;
		showProvenanceLinkingButton = showProcessed === DataFilter.Processed;
		return gqlClientCustom.request(
			SearchScrapeDataDocument,
			{
				input: {
					groupBy: groupBy.map((groupByEntry) => groupByEntry.value),
					limit: -1,
					sort: sortValue.map((sortEntry) => ({
						field: sortEntry.value.split('-')[0] as GroupBy,
						order: sortEntry.value.split('-')[1] as SortOrder,
					})),
					search: {
						dataFilter: showProcessed,
						...(sourceValue && {
							[GroupBy.Datasource]: sourceValue,
						}),
						...(dealerValue && {
							[GroupBy.Dealer]: dealerValue,
						}),
						...(saleNameValue && {
							[GroupBy.SaleName]: saleNameValue,
						}),
						...(artistValue && {
							[GroupBy.Artist]: artistValue,
						}),
						...(saleDateValue && {
							[GroupBy.SaleDate]: saleDateValue,
						}),
						...(typeValue &&
							typeValue !== NO_VALUE && {
								[GroupBy.Type]: typeValue,
							}),
						...(dateScrapedValue && {
							[GroupBy.DateScraped]: dateScrapedValue,
						}),
						...(processingByValue &&
							processingByValue !== NO_VALUE && {
								userAssignedId: processingByValue,
							}),
					},
				},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);
	};

	export function handleClickReset() {
		sourceValue = '';
		dealerValue = '';
		saleNameValue = '';
		artistValue = '';
		saleDateValue = '';
		typeValue = NO_VALUE;
		dateScrapedValue = '';
		processingByValue = NO_VALUE;
		sortValue = [];
		showProcessed = DataFilter.All;
		groupBy = getDefaultGroupBy();
	}

	const handleChangeGroupByCheckbox =
		(groupByOption: { value: GroupBy; label: string }) =>
		(selected: boolean | string) => {
			if (selected) {
				groupBy = [...groupBy, groupByOption];
			} else {
				groupBy = groupBy.filter(
					(existingGroupByOption) =>
						existingGroupByOption.value !== groupByOption.value
				);
			}
		};
</script>

<div class="grid grid-cols-4 gap-4 px-4 pt-4">
	<div class="col-span-1">
		<Input
			label="Source"
			name="source"
			dataCy={`${dataCyPrefix}-source`}
			placeholder="source"
			bind:value={sourceValue}
		/>
	</div>

	<div class="col-span-1">
		<Input
			label="Dealer"
			name="dealer"
			dataCy={`${dataCyPrefix}-dealer`}
			placeholder="dealer"
			bind:value={dealerValue}
		/>
	</div>

	<div class="col-span-1">
		<Input
			label="Sale name"
			name="sale-name"
			dataCy={`${dataCyPrefix}-sale-name`}
			placeholder="sale name"
			bind:value={saleNameValue}
		/>
	</div>

	<div class="col-span-1">
		<Input
			name="artist"
			label="Artist"
			dataCy={`${dataCyPrefix}-artist`}
			placeholder="artist"
			bind:value={artistValue}
		/>
	</div>
</div>

<div class="grid grid-cols-4 gap-4 px-4 pt-4">
	<div class="col-span-1">
		<Input
			bind:value={saleDateValue}
			placeholder="sale date"
			name="sale-date"
			label="Sale date"
			dataCy={`${dataCyPrefix}-sale-date`}
			type="date"
		/>
	</div>

	<div class="col-span-1">
		<Select
			ariaLabel="Select a type"
			options={[
				{ label: 'type', value: NO_VALUE },
				...Object.values(ScrapedDataType).map((type) => ({
					label: type,
					value: type,
				})),
			]}
			name="type"
			label="Type"
			dataCy={`${dataCyPrefix}-type`}
			placeholder="type"
			bind:value={typeValue}
		/>
	</div>

	<div class="col-span-1">
		<Input
			bind:value={dateScrapedValue}
			placeholder="date scraped"
			name="date-scraped"
			label="Date scraped"
			dataCy={`${dataCyPrefix}-date-scraped`}
			type="date"
		/>
	</div>

	<div class="col-span-1">
		<Select
			ariaLabel="Select a user"
			options={[
				{ label: 'processing by', value: NO_VALUE },
				...users
					.map((user) => ({
						label: [user.first_name, user.last_name]
							.filter(Boolean)
							.join(' ')
							.toLowerCase(),
						value: user.id,
					}))
					.sort((userA, userB) => {
						if (userA.label < userB.label) {
							return -1;
						}
						if (userA.label > userB.label) {
							return 1;
						}
						return 0;
					})
					.map((user) => ({
						...user,
						label: capitalizeFirstLetters(user.label),
					})),
			]}
			name="processing-by"
			label="Processing by"
			dataCy={`${dataCyPrefix}-processing-by`}
			placeholder="processing by"
			bind:value={processingByValue}
		/>
	</div>
</div>

<div class="grid grid-cols-4 gap-4 px-4 py-4">
	<div class="col-span-1">
		<MultiSelect
			name="sort"
			dataCy={`${dataCyPrefix}-sort`}
			label="Sort by"
			bind:selected={sortValue}
			placeholder="Type or select"
			maxSelect={1}
			class={classNames({
				'[&_div>svg]:hidden [&_button]:right-[14px] [&>div>svelte-css-wrapper]:pointer-events-none':
					sortValue.length,
			})}
			options={sortOptions}
		/>
	</div>

	<div class="col-span-1 flex items-center pt-6 gap-4">
		<Txt variant="label3">Show:</Txt>
		<InputLabel dataCy={`${dataCyPrefix}-show-all`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-all-label`}
				name={DataFilter.All}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.All}
			/>

			<div>All</div>
		</InputLabel>

		<InputLabel dataCy={`${dataCyPrefix}-show-processed`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-show-processed-label`}
				name={DataFilter.Processed}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.Processed}
			/>

			<div>Processed Only</div>
		</InputLabel>

		<InputLabel dataCy={`${dataCyPrefix}-show-unprocessed`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-show-unprocessed-label`}
				name={DataFilter.Unprocessed}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.Unprocessed}
			/>

			<div>Unprocessed Only</div>
		</InputLabel>
	</div>
</div>
<div class="px-4 pb-4">
	<div class="border border-gray-200 bg-gray-100 p-4">
		<InputLabel dataCy={dataCyPrefix} class="mb-4">Group by</InputLabel>
		<div class="flex items-start gap-4">
			{#each GROUP_BY_OPTIONS as groupByOption}
				<InputLabel
					dataCy={dataCyPrefix}
					variant="body3"
					classes={{ container: 'w-auto gap-1', wrapper: 'gap-1' }}
				>
					<Checkbox
						size="sm"
						dataCy={`${dataCyPrefix}`}
						name={groupByOption.value}
						checked={!!groupBy.find(
							(existingGroupByOption) =>
								existingGroupByOption.value === groupByOption.value
						)}
						onChange={handleChangeGroupByCheckbox(groupByOption)}
					/>

					<div class="pl-0.5">{groupByOption.label}</div>
				</InputLabel>
			{/each}
		</div>
	</div>
</div>

<div class="grid grid-cols-4 gap-4 border-t border-gray-200 px-4 pt-4">
	<Button
		disabled={!groupBy.length || searching}
		dataCy={dataCyPrefix}
		size="md">search</Button
	>

	<Button
		variant="secondary"
		type="button"
		dataCy={dataCyPrefix}
		onclick={handleClickReset}
		size="md">clear search fields</Button
	>
</div>
