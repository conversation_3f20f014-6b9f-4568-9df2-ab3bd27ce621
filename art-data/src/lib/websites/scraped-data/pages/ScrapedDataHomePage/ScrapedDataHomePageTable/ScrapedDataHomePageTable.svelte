<script lang="ts">
	import dayjs from 'dayjs';
	import { Checkbox } from '$global/components/Checkbox';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { GroupBy } from '$gql/types-custom';
	import { type SearchScrapeDataQuery } from '$lib/websites/scraped-data/custom-queries/__generated__/searchScrapeData.generated';

	interface Props {
		ids: string[];
		pageNumber: number;
		data: NonNullable<SearchScrapeDataQuery['searchScrapeData']>;
		groupBy: { value: string; label: string }[];
	}

	let {
		ids = $bindable(),
		pageNumber = $bindable(),
		data,
		groupBy,
	}: Props = $props();

	const LIMIT = 20;
	const checkboxWidth = '3rem';
	const actionCellWidth = '7rem';
	let headers = $derived([
		'',
		...groupBy.map((groupByOption) => groupByOption.label),
		'No artworks',
	]);

	let rows = $derived(
		(data.data || []).slice((pageNumber - 1) * LIMIT, pageNumber * LIMIT)
	);

	let formatRow = $derived((row: (typeof rows)[number]) => [
		...groupBy.map((groupByOption) => {
			const rowValue = row?.[groupByOption.value as keyof typeof row];
			if (groupByOption.value === GroupBy.DateScraped) {
				return dayjs(rowValue).format('DD/MM/YYYY - HH:mm');
			}

			if (groupByOption.value === GroupBy.SaleDate) {
				return dayjs(rowValue).format('DD/MM/YYYY');
			}
			return rowValue || '-';
		}),
		row?.count,
	]);

	const dataCyPrefix = 'scraped-data-table';

	const handleCheckboxChange =
		(newIds: string[]) => (selected: string | boolean) => {
			if (selected) {
				ids = [...ids, ...newIds];
			} else {
				ids = ids.filter((id) => !newIds.includes(id));
			}
		};

	let width = $derived(
		`calc((100% - ${actionCellWidth} - ${checkboxWidth} - 5rem) / ${headers?.length})`
	);
</script>

{#if data.data}
	<div class="mb-4 flex justify-end">
		<Txt variant="label4">
			{ids.length} artworks selected
		</Txt>
	</div>
{/if}
<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			{#if !i}
				<TableHeader dataCy={dataCyPrefix} width={checkboxWidth} />
			{:else}
				<TableHeader dataCy={dataCyPrefix} {width}>
					{header}
				</TableHeader>
			{/if}
		{/each}
	</TableHeaderRow>

	<TableBody dataCy={dataCyPrefix}>
		{#each rows as row, i}
			{#if row?.artworkFeedIds}
				{@const checked = row.artworkFeedIds
					.split(',')
					.every((id) => ids.includes(id))}
				<TableRow
					index={i}
					dataCy={dataCyPrefix}
					onclick={() =>
						handleCheckboxChange((row.artworkFeedIds as string).split(','))(
							!checked
						)}
				>
					<TableActionCell dataCy={dataCyPrefix} width={checkboxWidth}>
						<Checkbox dataCy={dataCyPrefix} size="sm" {checked} />
					</TableActionCell>
					{@const formattedRow = formatRow(row)}
					{#each formattedRow as formattedRowValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							{width}
							content={formattedRowValue}
						>
							{formattedRowValue}
						</TableCell>
					{/each}
				</TableRow>
			{/if}
		{/each}

		{#if !data.data?.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No data to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>

<div class="mt-2 flex items-center justify-between">
	{#if data.data?.length}
		<Txt variant="body3">
			Showing {(pageNumber - 1) * LIMIT + 1} - {(pageNumber - 1) * LIMIT +
				rows.length} of {data.total}
			results
		</Txt>
	{/if}

	{#key pageNumber}
		{#key data.total}
			<Pagination
				classes={{ button: 'max-w-[none]' }}
				onClick={(e: Event | undefined, newPage: number) => {
					pageNumber = newPage;
				}}
				dataCy={dataCyPrefix}
				currentPage={pageNumber}
				limit={LIMIT}
				total={data.total}
			/>
		{/key}
	{/key}
</div>
