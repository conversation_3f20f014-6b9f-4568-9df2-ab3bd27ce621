import { GroupBy } from '$gql/types-custom';

export enum ScrapedDataFieldNames {
	Source = 'source',
	Dealer = 'dealer',
	SaleName = 'saleName',
	Artist = 'artist',
	SaleDate = 'saleDate',
	Type = 'type',
	DateScraped = 'dateScraped',
	ProcessingBy = 'processingBy',
	Sort = 'sort',
	Page = 'page',
	ShowProcessed = 'showProcessed',
}

export const TYPE_SORT_OPTIONS = [];

export const ARTWORK_FEED_SORT_OPTIONS = [
	{ value: 'sale_starts_at', label: 'SALE DATE (EARLIEST)' },
	{ value: '-sale_starts_at', label: 'SALE DATE (LATEST)' },
	{ value: 'artist_clean_name', label: 'ARTIST NAME (A-Z)' },
	{ value: '-artist_clean_name', label: 'ARTIST NAME (Z-A)' },
];

export const PRIVATE_MARKET_ACTIVITY_OPTIONS = [
	{ value: 'GALLERY', label: 'Gallery' },
	{ value: 'FAIR', label: 'Fair' },
	{ value: 'EXHIBITION', label: 'Exhibition' },
	{ value: 'PRIVATE_SALE', label: 'Private sale' },
	{ value: 'CATALOGUE_RAISONNE', label: 'Catalogue raisonne' },
	{ value: 'MUSEUM_ACQUISITION', label: 'Museum acquisition' },
];

export const GROUP_BY_OPTIONS = [
	{ label: 'Source', value: GroupBy.Datasource, default: true },
	{ label: 'Dealer', value: GroupBy.Dealer, default: false },
	{ label: 'Sale Name', value: GroupBy.SaleName, default: true },
	{ label: 'Artist', value: GroupBy.Artist, default: false },
	{ label: 'Sale Date', value: GroupBy.SaleDate, default: true },
	{ label: 'Type', value: GroupBy.Type, default: false },
	{ label: 'Date Scraped', value: GroupBy.DateScraped, default: false },
	{ label: 'Processing By', value: GroupBy.UserAssigned, default: true },
];
