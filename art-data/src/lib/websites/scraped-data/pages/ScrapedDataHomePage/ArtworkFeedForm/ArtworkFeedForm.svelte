<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import {
		ARTWORK_FEED_SORT_OPTIONS,
		PRIVATE_MARKET_ACTIVITY_OPTIONS,
	} from '../types';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Artwork_Feed_Filter, InputMaybe } from '$gql/types';
	import { ARTWORK_IN_PROGRESS_STATUSES } from '$lib/constants/artwork-in-progress-statuses';
	import {
		SEARCH_RANGE_OPTIONS,
		SEARCH_RANGE_PLACEHOLDERS_FULL,
		SearchRange,
	} from '$lib/constants/search-range-options';
	import { gqlClient } from '$lib/gqlClient';
	import { getSearchRangeFilter } from '$lib/utils/getSearchRange/getSearchRange';
	import {
		GetArtworkFeedDocument,
		type GetArtworkFeedQueryVariables,
	} from '$lib/websites/scraped-data/queries/__generated__/getArtworkFeed.generated';
	import { type ScrapedDataPageData } from '$routes/scraped-data/types';
	import { DataFilter } from '$gql/types-custom';

	let data = $derived(getPageData<ScrapedDataPageData>(page.data));
	let favouriteArtists = $derived(data.favouriteArtists);

	let artistOptions = $derived(
		(favouriteArtists || []).map((artist) => ({
			label: [
				artist.artist?.person?.first_name,
				artist.artist?.person?.middle_name,
				artist.artist?.person?.last_name,
			]
				.filter(Boolean)
				.join(' '),
			value: `${artist.artist?.person?.first_name}//${artist.artist?.person?.last_name}`,
		}))
	);

	interface Props {
		defaultDateRange?: SearchRange;
		defaultDate?: dayjs.Dayjs;
		showProvenanceLinkingButton: boolean;
		searching: boolean;
		label: string;
		filters: InputMaybe<Artwork_Feed_Filter>[];
		showPrivateMarketInput?: boolean;
		validateDate?: (date: string) => string;
	}

	let {
		defaultDateRange,
		defaultDate,
		searching,
		showProvenanceLinkingButton = $bindable(),
		label,
		filters,
		showPrivateMarketInput = false,
		validateDate,
	}: Props = $props();

	let dataCyPrefix = 'all-scraped-data-form';

	let artistValue: MultiSelectOption[] = $state([]);
	let auctionHouseValue = $state('');
	let entityValue = $state('');
	let dataSourceValue = $state('');
	let saleDate = $state(defaultDate ? defaultDate.format('DD/MM/YYYY') : '');
	let saleDateRange = $state(defaultDateRange || SearchRange.Between);
	let sortValue: MultiSelectOption[] = $state([]);
	let privateMarketActivityValue: MultiSelectOption[] = $state([]);
	let excludeItemsInProgress = $state(false);
	let showProcessed = $state(DataFilter.Unprocessed);
	let saleDateBlur = $state(false);

	const isDateValid = (date: string) => {
		return dayjs(date, 'DD/MM/YYYY', true).isValid();
	};

	let saleDateError = $derived(
		(() => {
			if (!saleDate) {
				return '';
			}

			if (saleDateRange === SearchRange.Between) {
				const saleDates = saleDate.split('-');

				let error =
					saleDates.every((s) => isDateValid(s.trim())) &&
					saleDates.length === 2 &&
					dayjs(saleDates[0], 'DD/MM/YYYY', true).unix() <
						dayjs(saleDates[1], 'DD/MM/YYYY', true).unix()
						? ''
						: 'Invalid range';

				if (!error && validateDate) {
					error =
						saleDates.map((s) => validateDate(s)).filter(Boolean)?.[0] || '';
				}

				return error;
			}

			let error = isDateValid(saleDate.trim()) ? '' : 'Invalid date';

			if (!error && validateDate) {
				error = validateDate(saleDate) || '';
			}

			return error;
		})()
	);

	const formatArtistOptionsFilter = (options: MultiSelectOption[]) => {
		return [
			{
				_or: options.map((artist) => {
					let firstNameFilter: null | GetArtworkFeedQueryVariables['filter'] =
						null;

					let lastNameFilter: null | GetArtworkFeedQueryVariables['filter'] =
						null;

					const [firstName, lastName] = artist.value.split('//');

					if (firstName !== 'null' && firstName !== 'undefined' && firstName) {
						firstNameFilter = {
							artist_clean_name: { _icontains: firstName },
						};
					}

					if (lastName !== 'null' && lastName !== 'undefined' && lastName) {
						lastNameFilter = {
							artist_clean_name: { _icontains: lastName },
						};
					}

					return {
						_and: [firstNameFilter, lastNameFilter].filter(Boolean),
					};
				}),
			},
		];
	};

	export const searchArtworkFeed = () => {
		showProvenanceLinkingButton = showProcessed === DataFilter.Processed;
		return gqlClient.request(
			GetArtworkFeedDocument,
			{
				limit: 1000,
				sort: sortValue.length
					? sortValue.map((sortOption) => sortOption.value)
					: ['-sale_starts_at'],
				filter: {
					_and: [
						...filters,
						{ archived: { _eq: false } },
						...(() => {
							if (showProcessed === DataFilter.Processed) {
								return [
									{
										processor_review_artwork: {
											status: {
												key: {
													_in: ARTWORK_IN_PROGRESS_STATUSES,
												},
											},
										},
									},
								];
							}

							if (showProcessed === DataFilter.Unprocessed) {
								return [
									{
										_or: [
											{
												processor_review_artwork: {
													status: {
														key: {
															_nin: ARTWORK_IN_PROGRESS_STATUSES,
														},
													},
												},
											},
											{
												processor_review_artwork: {
													status: {
														key: {
															_null: true,
														},
													},
												},
											},
										],
									},
								];
							}

							return [];
						})(),
						...(dataSourceValue && showPrivateMarketInput
							? [
									{
										data_source: { _icontains: dataSourceValue },
									},
								]
							: []),
						...(privateMarketActivityValue.length && showPrivateMarketInput
							? [
									{
										event_id: {
											event_type: {
												listing_type: {
													_in: privateMarketActivityValue.map(
														(activity) => activity.value
													),
												},
											},
										},
									},
								]
							: []),
						...(excludeItemsInProgress
							? [
									{
										processor_review_artwork: {
											user_updated: { id: { _nnull: true } },
										},
									},
								]
							: []),
						...(saleDate
							? [
									{
										sale_starts_at: getSearchRangeFilter({
											value: saleDate,
											range: saleDateRange as SearchRange,
										}),
									} as NonNullable<GetArtworkFeedQueryVariables['filter']>,
								]
							: []),
						...(auctionHouseValue
							? [
									{
										event_id: {
											organization: { _icontains: auctionHouseValue },
										},
									},
								]
							: []),
						...(artistValue?.length
							? formatArtistOptionsFilter(artistValue)
							: formatArtistOptionsFilter(artistOptions)),
					],
				},
			},
			getAuthorizationHeaders(data)
		);
	};

	const handleKeyDown = (e: KeyboardEvent) => {
		switch (e.key) {
			case '<': {
				saleDateRange = SearchRange.LessThan;
				e.preventDefault();
				return;
			}
			case '>': {
				saleDateRange = SearchRange.GreaterThan;
				e.preventDefault();
				return;
			}
			case '=': {
				saleDateRange = SearchRange.EqualTo;
				e.preventDefault();
				return;
			}
			default: {
				return;
			}
		}
	};

	export function handleClickReset() {
		artistValue = [];
		auctionHouseValue = '';
		saleDate = defaultDate ? defaultDate.format('DD/MM/YYYY') : '';
		entityValue = '';
		dataSourceValue = '';
		privateMarketActivityValue = [];
		saleDateRange = defaultDateRange || SearchRange.Between;
		sortValue = [];
		excludeItemsInProgress = false;
		showProcessed = DataFilter.All;
		saleDateBlur = false;
	}
</script>

<Txt class="mx-4 mt-6" variant="label3">{label}</Txt>

<div class="grid grid-cols-4 gap-4 px-4 pt-4">
	<div class="col-span-1">
		<MultiSelect
			name="favourite_artist"
			dataCy={`${dataCyPrefix}-favourite-artist`}
			label="Favourite Artist"
			bind:selected={artistValue}
			placeholder="Select favourite artist"
			options={artistOptions}
		/>
	</div>

	<div class="col-span-1">
		{#if showPrivateMarketInput}
			<Input
				label="Entity name"
				name="entity_name"
				dataCy={`${dataCyPrefix}-entity-name`}
				placeholder="Type a name"
				bind:value={entityValue}
			/>
		{:else}
			<Input
				label="Auction house name"
				name="auction_house_name"
				dataCy={`${dataCyPrefix}-auction-house-name`}
				placeholder="Auction house name"
				bind:value={auctionHouseValue}
			/>
		{/if}
	</div>

	<div class="col-span-2">
		<InputWithSelect
			size="sm"
			label="Sale Date (DD/MM/YYYY)"
			dataCy={`${dataCyPrefix}-sale-date`}
			name="sale_date"
			bind:selectValue={saleDateRange}
			bind:inputValue={saleDate}
			tooltip="TODO art-data"
			options={SEARCH_RANGE_OPTIONS}
			error={saleDateBlur ? saleDateError : ''}
			onkeydown={handleKeyDown}
			onblur={() => {
				saleDateBlur = true;
			}}
			placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[saleDateRange]}
		/>
	</div>
</div>

<div class="grid grid-cols-4 gap-4 px-4 py-4">
	<div class="col-span-1">
		<MultiSelect
			name="sort"
			dataCy={`${dataCyPrefix}-sort`}
			label="Sort by"
			bind:selected={sortValue}
			placeholder="Type or select"
			options={ARTWORK_FEED_SORT_OPTIONS}
			maxSelect={1}
			class={classNames({
				'[&_div>svg]:hidden [&_button]:right-[14px] [&>div>svelte-css-wrapper]:pointer-events-none':
					sortValue.length,
			})}
		/>
	</div>

	{#if showPrivateMarketInput}
		<div class="col-span-1">
			<Input
				label="Data source"
				name="data_source"
				dataCy={`${dataCyPrefix}-data-source`}
				placeholder="Type a data source"
				bind:value={dataSourceValue}
			/>
		</div>
		<div class="col-span-1">
			<MultiSelect
				name="private_market_activity"
				dataCy={`${dataCyPrefix}-private-market-activity`}
				label="Activity type"
				bind:selected={privateMarketActivityValue}
				placeholder="Type or select"
				options={PRIVATE_MARKET_ACTIVITY_OPTIONS}
			/>
		</div>
	{/if}

	<div class="col-span-1 flex items-center pt-6">
		<InputLabel dataCy={`${dataCyPrefix}-exclude-items`} variant="body3">
			<Checkbox
				dataCy={`${dataCyPrefix}-exclude-items-label`}
				name="exclude_items"
				class="mt-[-0.125rem]"
				bind:checked={excludeItemsInProgress}
			/>

			<div class="pl-1.5">Exclude items in progress</div>
		</InputLabel>
	</div>

	<div class="col-span-1 flex items-center pt-6 gap-4">
		<Txt variant="label3">Show:</Txt>
		<InputLabel dataCy={`${dataCyPrefix}-show-all`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-all-label`}
				name={DataFilter.All}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.All}
			/>

			<div>All</div>
		</InputLabel>

		<InputLabel dataCy={`${dataCyPrefix}-show-processed`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-show-processed-label`}
				name={DataFilter.Processed}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.Processed}
			/>

			<div>Processed Only</div>
		</InputLabel>

		<InputLabel dataCy={`${dataCyPrefix}-show-unprocessed`} variant="body3">
			<Radio
				dataCy={`${dataCyPrefix}-show-unprocessed-label`}
				name={DataFilter.Unprocessed}
				class="mt-[-0.125rem]"
				bind:group={showProcessed}
				value={DataFilter.Unprocessed}
			/>

			<div>Unprocessed Only</div>
		</InputLabel>
	</div>
</div>

<div class="grid grid-cols-4 gap-4 border-t border-gray-200 px-4 pt-4">
	<Button
		disabled={searching || !!saleDateError}
		dataCy={dataCyPrefix}
		size="md">search</Button
	>

	<Button
		variant="secondary"
		type="button"
		dataCy={dataCyPrefix}
		onclick={handleClickReset}
		size="md">clear search fields</Button
	>
</div>
