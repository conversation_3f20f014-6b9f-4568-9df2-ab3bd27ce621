<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { GetEntitiesDocument } from '../../arteye-queries/__generated__/getEntities.generated';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import {
		GetScrapedEntitiesDocument,
		type GetScrapedEntitiesQuery,
	} from '../../custom-queries/__generated__/getScrapedEntities.generated';
	import { fetchScrapedArtworks } from '../../utils/fetchScrapedArtworks/fetchScrapedArtworks';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { EntityAutocomplete, formatEntity } from './EntityAutocomplete';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Select } from '$global/components/Select';
	import {
		TableHeader,
		TableHeaderRow,
		TableBody,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import {
		UpdateScrapedEntityDocument,
		type UpdateScrapedEntityMutation,
	} from '$lib/queries/__generated__/updateScrapedEntity.generated';
	import type { ScrapedDataProcessDataEntitiesPageData } from '$routes/scraped-data/process-data/entities/types';
	import type { GetScrapedEntitiesResponseItem } from '$gql/types-custom';

	interface Props {
		class?: string;
	}

	let { ...props }: Props = $props();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.ScrapedEntityAsc);
	let updatedIds: string[] = [];
	let entitiesSelectedOptions: null | Record<string, OptionType | null> =
		$state(null);

	let lastSorted = $state(+new Date());
	let data = $derived(
		getPageData<ScrapedDataProcessDataEntitiesPageData>(page.data)
	);

	const {
		artworkFeeds,
		saveStage,
		showMatchEventsWarning,
		entitiesSelectedOptionsStore,
		scrapedEntitiesStore,
		scrapedArtworksStore,
	} = getArtworkFeedsStore();
	const dialogStores = createDialog();

	let isInProgress = $derived(
		!!$scrapedArtworksStore?.find(
			(scrapedArtwork) => !isScrapedArtworkProcessed(scrapedArtwork)
		)
	);

	$effect(() => {
		if (
			$entitiesSelectedOptionsStore &&
			!submitting &&
			!entitiesSelectedOptions
		) {
			entitiesSelectedOptions = $entitiesSelectedOptionsStore;
		}
	});

	let sortedScrapedEntities = $state(
		null as null | GetScrapedEntitiesQuery['getScrapedEntities']
	);

	$effect(() => {
		if ($scrapedEntitiesStore && !submitting && !sortedScrapedEntities) {
			sortedScrapedEntities = [...$scrapedEntitiesStore].sort((a, b) =>
				sortFn(a, b, sort)
			);
		}
	});

	onMount(() => {
		saveStage();
	});

	const handleChangeScrapedEntity = (scrapedEntityId: string) => {
		updatedIds = Array.from(new Set([...updatedIds, scrapedEntityId]));
	};

	const sortFn = (
		scrapedEntityA: NonNullable<
			GetScrapedEntitiesQuery['getScrapedEntities']
		>[number],
		scrapedEntityB: NonNullable<
			GetScrapedEntitiesQuery['getScrapedEntities']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedEntity: NonNullable<typeof sortedScrapedEntities>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.ScrapedEntityAsc:
				case ArtworkDetailsSort.ScrapedEntityDesc:
					return scrapedEntity.associated_entity_feed?.name?.toLowerCase();
				case ArtworkDetailsSort.EntityAsc:
				case ArtworkDetailsSort.EntityDesc:
					return entitiesSelectedOptions?.[
						scrapedEntity?.id
					]?.line1?.toLowerCase();
			}
		};

		const aText = getTextField(scrapedEntityA) || '';
		const bText = getTextField(scrapedEntityB) || '';

		if (aText < bText) {
			return sort.endsWith('ASC') ? -1 : 1;
		}
		if (aText > bText) {
			return sort.endsWith('ASC') ? 1 : -1;
		}

		return 0;
	};

	const sortScrapedEntities = async (sort: ArtworkDetailsSort) => {
		sortedScrapedEntities = [...(sortedScrapedEntities || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);

		lastSorted = +new Date();
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		try {
			submitting = true;

			if (sortedScrapedEntities?.length) {
				if (updatedIds.length) {
					const updateScrapedEntityPromises = updatedIds.map((updatedId) => {
						const correspondingScrapedEntity = (
							sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>
						).find((scrapedEntity) => scrapedEntity?.id === updatedId);

						if (!correspondingScrapedEntity?.id) {
							return null;
						}

						return gqlClient.request(
							UpdateScrapedEntityDocument,
							{
								id: updatedId,
								data: {
									processed_entity_id: entitiesSelectedOptions?.[
										correspondingScrapedEntity?.id
									]
										? entitiesSelectedOptions?.[correspondingScrapedEntity?.id]
												?.line4
										: null,
								},
							},
							getAuthorizationHeaders(data)
						);
					});

					const newScrapedEntities = (await Promise.all(
						updateScrapedEntityPromises
					)) as UpdateScrapedEntityMutation[];
					let newScrapedEntitiesStore = [...($scrapedEntitiesStore || [])];

					newScrapedEntities.forEach((newScrapedEntity) => {
						const newScrapedEntityIndex = $scrapedEntitiesStore?.findIndex(
							(entity) =>
								entity.id === newScrapedEntity.update_Scraped_Entity_item?.id
						);

						if (!newScrapedEntityIndex) {
							return;
						}

						newScrapedEntitiesStore[newScrapedEntityIndex] =
							newScrapedEntity.update_Scraped_Entity_item as GetScrapedEntitiesResponseItem;
					});

					scrapedEntitiesStore.set(newScrapedEntitiesStore);
				}

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});

				dialogStores.states.open.set(true);
			} else {
				handleContinue();
			}
		} catch {
			submitting = false;
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataFinalise);
	};

	const findScrapedEntity = (scrapedEntityId: string) =>
		(
			sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>
		).findIndex((scrapedEntity) => scrapedEntity.id === scrapedEntityId);

	const handleChangeEntity = async (
		scrapedEntityId: string,
		{ detail: { value: entity } }: { detail: { value: OptionType } }
	) => {
		const scrapedEntityIndex = findScrapedEntity(scrapedEntityId);
		(sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>)[
			scrapedEntityIndex
		].processed_entity_id = entity?.line4;
		(sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>)[
			scrapedEntityIndex
		].processed_reference_id = JSON.parse(entity?.line5 as string) || null;
		handleChangeScrapedEntity(scrapedEntityId);

		return Promise.resolve();
	};

	const handleRemoveEntity = (scrapedEntityId: string) => {
		const scrapedEntityIndex = findScrapedEntity(scrapedEntityId);
		(sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>)[
			scrapedEntityIndex
		].processed_entity_id = null;
		(sortedScrapedEntities as NonNullable<typeof sortedScrapedEntities>)[
			scrapedEntityIndex
		].processed_reference_id = null;
		handleChangeScrapedEntity(scrapedEntityId);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const dataCyPrefix = 'scraped-data-process-entities';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={8} />
			{#if !sortedScrapedEntities || $showMatchEventsWarning === null || !$scrapedArtworksStore}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else if !sortedScrapedEntities?.length}
				<Txt variant="label3" class="text-center">No entities to match</Txt>
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedEntities(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix} class={props.class}>
						<TableHeader dataCy={dataCyPrefix} width="20%"
							>Scraped value</TableHeader
						>
						<TableHeader dataCy={dataCyPrefix} width="40%">
							Entity<Mandatory /><Txt
								component="span"
								variant="body2"
								class="font-[400] text-gray-500"
								>{` Edit value if required`}</Txt
							>
						</TableHeader>
					</TableHeaderRow>
					{#if entitiesSelectedOptions}
						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedEntities as scrapedEntity, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										{scrapedEntity?.associated_entity_feed?.name}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											{#if entitiesSelectedOptions}
												<div class="w-2/3">
													<EntityAutocomplete
														{lastSorted}
														disabled={!isInProgress}
														onChange={(e) =>
															handleChangeEntity(scrapedEntity.id, e)}
														onRemoveSelectedOption={() =>
															handleRemoveEntity(scrapedEntity.id)}
														bind:selectedOption={
															entitiesSelectedOptions[scrapedEntity.id]
														}
														dataCy={dataCyPrefix}
													/>
												</div>
											{/if}
										{/snippet}
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					{/if}
				</table>
			{/if}
		</div>
	</Container>
</PageBody>

<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
	<table class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white">
		<TableHeaderRow dataCy={dataCyPrefix} class={props.class}>
			<TableHeader dataCy={dataCyPrefix}>Scraped value</TableHeader>
			<TableHeader dataCy={dataCyPrefix}>
				Entity<Mandatory /><Txt
					component="span"
					variant="body2"
					class="font-[400] text-gray-500">{` Edit value if required`}</Txt
				>
			</TableHeader>
		</TableHeaderRow>
		{#if entitiesSelectedOptions && sortedScrapedEntities}
			<TableBody dataCy={dataCyPrefix}>
				{#each sortedScrapedEntities as scrapedEntity, i}
					<TableRow index={i} dataCy={dataCyPrefix}>
						<TableCell dataCy={dataCyPrefix}>
							{scrapedEntity?.associated_entity_feed?.name}
						</TableCell>
						<TableCell dataCy={dataCyPrefix}>
							{#snippet custom()}
								{#if entitiesSelectedOptions?.[scrapedEntity.id]}
									<EntityAutocomplete
										disabled
										{lastSorted}
										onChange={(e) => handleChangeEntity(scrapedEntity.id, e)}
										onRemoveSelectedOption={() =>
											handleRemoveEntity(scrapedEntity.id)}
										bind:selectedOption={
											entitiesSelectedOptions[scrapedEntity.id]
										}
										dataCy={dataCyPrefix}
									/>
								{/if}
							{/snippet}
						</TableCell>
					</TableRow>
				{/each}
			</TableBody>
		{/if}
	</table>
</RecordsSaveDialog>

<PageSaveBar
	loading={submitting}
	disabled={!entitiesSelectedOptions ||
		Object.values(entitiesSelectedOptions).some(
			(entitySelectedOption) => !entitySelectedOption?.line4
		)}
	onSaveClick={isInProgress ? handleSubmit : handleContinue}
	visible={!!sortedScrapedEntities && !!$scrapedArtworksStore}
	nextLabel={isInProgress ? 'Save and continue' : 'Continue'}
	backHref={Routes.ScrapedDataProcessDataArtworkDetails}
/>
