<script lang="ts" module>
	export const formatEntity = (entity: GetEntitiesQuery['entity'][number]) => {
		const url = (() => {
			if (entity.type?.key === 'person') {
				if (entity?.artist) {
					return `${Config.ArteyeDomain}/artists/${entity?.artist?.id}`;
				} else {
					return `${Config.ArteyeDomain}/people/${entity?.person?.id}`;
				}
			}

			return `${Config.ArteyeDomain}/organisations/${entity?.organisation?.id}`;
		})();

		const formatPersonSuffix = (
			person: GetEntitiesQuery['entity'][number]['person']
		) => {
			const suffix = [
				person?.year_birth ? `b. ${person.year_birth}` : '',
				person?.nationalities?.[0]?.country?.country_nationality,
			]
				.filter(Boolean)
				.join(', ');

			return suffix ? `(${suffix})` : '';
		};

		return {
			line1: `${entity.name}`,
			line1Suffix: (() => {
				if (entity.type?.key === 'person') {
					if (entity?.artist) {
						return formatPersonSuffix(entity?.artist?.person);
					} else {
						return formatPersonSuffix(entity?.person);
					}
				}

				return entity?.organisation?.location?.name
					? `(${entity?.organisation?.location?.name})`
					: '';
			})(),
			line2: url,
			line3: `${entity.type?.key}`,
			line4: `${entity.id}`,
			line5: JSON.stringify(entity?.reference_id || null),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Config } from '$lib/constants/config';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetEntitiesDocument,
		type GetEntitiesQuery,
		type GetEntitiesQueryVariables,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	interface Props {
		lastSorted: number;
		disabled: boolean;
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemoveSelectedOption?: undefined | (() => void);
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		lastSorted,
		disabled,
		placeholder = 'Search entities',
		dataCy,
		selectedOption = $bindable(null),
		onRemoveSelectedOption = undefined,
		onChange = undefined,
	}: Props = $props();

	$effect(() => {
		if (lastSorted) {
			value.set('');
		}
	});

	const value = writable('');

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			filter: value
				? {
						_and: [
							...(() => {
								if (!value) {
									return [];
								}

								if (isUuidValid(value)) {
									return [{ id: { _eq: value } }];
								}

								if (!isNaN(+value)) {
									return [{ reference_id: { _eq: value } }];
								}

								return [{ name: { _icontains: value } }];
							})(),
							{ status: { key: { _neq: 'archived' } } },
						],
					}
				: { status: { key: { _neq: 'archived' } } },
		};
	};

	const getOptions = (queryData: GetEntitiesQuery | undefined) => {
		return [...(queryData?.entity || []).map(formatEntity)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		{onRemoveSelectedOption}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		emptyValueResponse={{ entity: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientArteye}
		classes={{
			longList: '!max-h-[232px] !min-h-[232px]',
			listWithOptions:
				'pb-[2.25rem] !max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
			},
		}}
		class={classNames({
			'max-w-[calc(100%-16px)]': !!selectedOption && !disabled,
		})}
		requestHeaders={getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})}
		{getOptions}
		{getVariables}
		document={GetEntitiesDocument}
		{value}
		bind:selectedOption
		{onChange}
	>
		{#snippet list()}
			<div
				class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white py-2 px-[0.875rem]"
			>
				<Txt
					component="div"
					variant="body2"
					class="text-gray-500 items-center flex gap-1"
					><span
						>Cannot find the entity ?{' '}<a
							target="_blank"
							href={`${Config.ArteyeDomain}/organisations`}
							class="text-blue-500"
							>Search on Arteye <ExternalIcon
								class="inline mt-[-2px] w-3 h-3 fill-blue-500 stroke-blue-500"
							/></a
						></span
					>
				</Txt>
			</div>
		{/snippet}
		{#snippet noResults()}
			<NoResults class="text-left" dataCy={`${dataCy}-entity-autocomplete`}>
				No entity found.{' '}

				<Txt
					component="div"
					variant="body2"
					class="text-gray-500 font-normal flex gap-1"
					><span
						>Cannot find the entity ?{' '}<a
							target="_blank"
							href={Config.ArteyeDomain}
							class="text-blue-500"
							>Search on Arteye <ExternalIcon
								class="w-3 h-3 fill-blue-500 stroke-blue-500 mt-[-2px] inline"
							/></a
						></span
					></Txt
				>
			</NoResults>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption && !disabled}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				if (onRemoveSelectedOption) {
					onRemoveSelectedOption();
					value.set('');
				}
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
