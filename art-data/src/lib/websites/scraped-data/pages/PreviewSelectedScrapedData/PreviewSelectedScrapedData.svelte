<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import timezone from 'dayjs/plugin/timezone';
	import { onMount } from 'svelte';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { GetArtworkFeedDocument } from '../../queries/__generated__/getArtworkFeed.generated';
	import type { GetArtworkFeedQuery } from '../../queries/__generated__/getArtworkFeed.generated';
	import { UpdateArtworkFeedItemsDocument } from '../../queries/__generated__/updateArtworkFeeds.generated';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { areTimezonesValid } from '../MatchEvents/utils/areTimezonesValid/areTimezonesValid';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Container } from '$global/components/Container';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import {
		TableBody,
		TableCell,
		TableHeaderRow,
		TableNoResults,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { UpdateArtworkDetailsBatchDocument } from '$lib/features/final-review/queries/__generated__/updateArtworkDetailsBatch.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import { CreateScrapedArtworksDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/createScrapedArtworks.generated';
	import type { ScrapedDataPreviewPageData } from '$routes/scraped-data/preview/types';

	let isReady = $state(false);
	let checkboxesInterval: number[] = [];
	let rows: GetArtworkFeedQuery['artwork_feed'] = $state([]);
	let consistentValues: { label: string; value: string }[] = $state([]);
	let columnsWithMissingValuesOnly: string[] = $state([]);
	let columnsWithMissingValues: string[] = $state([]);
	let startDateTz = $state('');
	let endDateTz = $state('');

	dayjs.extend(timezone);

	onMount(() => {
		const handleMount = async () => {
			document.addEventListener('keydown', (e) => {
				if (e.key === 'Tab' || (e.key.includes('Arrow') && e.metaKey)) {
					e.preventDefault();
				}
				if (checkboxesInterval.length && e.metaKey) {
					if (e.key === 'ArrowDown') {
						const nextCheckboxIndex =
							checkboxesInterval[2] +
							1 +
							rows
								.slice(checkboxesInterval[2] + 1)
								.findIndex(
									(row) =>
										!isScrapedArtworkProcessed(
											row.processor_review_artwork?.[0]
										)
								);

						if (rows[nextCheckboxIndex]) {
							rows[nextCheckboxIndex].archived =
								rows[checkboxesInterval[1]].archived;

							checkboxesInterval[2] = checkboxesInterval[2] + 1;
						}
					} else if (e.key === 'ArrowUp') {
						const prevCheckboxIndex =
							checkboxesInterval[0] +
							-1 -
							rows
								.slice(0, checkboxesInterval[0] + -1)
								.reverse()
								.findIndex(
									(row) =>
										!isScrapedArtworkProcessed(
											row.processor_review_artwork?.[0]
										)
								);

						if (prevCheckboxIndex >= 0) {
							rows[prevCheckboxIndex].archived =
								rows[checkboxesInterval[1]].archived;

							checkboxesInterval[0] = checkboxesInterval[0] - 1;
						}
					}
				}
			});

			if ($artworkFeeds.currentIds.length) {
				nbArtworksValue = `${$artworkFeeds.currentIds.length}`;
				await fetchArtworkFeeds($artworkFeeds.currentIds);
				isReady = true;
			} else {
				isReady = true;
			}
		};

		handleMount();
	});

	const { artworkFeeds, setCurrentIds, setUnprocessedIds } =
		getArtworkFeedsStore();

	const handleSaveClick = async () => {
		submitting = true;

		const idsToArchive = rows
			?.filter((row) => row.archived)
			?.map((row) => row?.id);

		const idsToProcess = rows
			?.filter((row) => !row.archived)
			?.map((row) => row?.id);

		try {
			if (idsToArchive.length) {
				await gqlClient.request(
					UpdateArtworkFeedItemsDocument,
					{
						ids: idsToArchive,
						data: { archived: true },
					},
					getAuthorizationHeaders(page.data as ScrapedDataPreviewPageData)
				);
			}

			if (idsToProcess.length) {
				const scrapedArtworksRes = await gqlClientCustom.request(
					CreateScrapedArtworksDocument,
					{ input: { ids: idsToProcess } },
					getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				);

				const scrapedArtworks =
					scrapedArtworksRes?.getScrapedArtworks?.scrapedArtworks;

				const scrapedArtworksIds = scrapedArtworks.map(
					(scrapedArtwork) => scrapedArtwork?.id
				);

				const scrapedArtworksDetailsIds = scrapedArtworks.map(
					(scrapedArtwork) => scrapedArtwork.artwork_details?.id
				);

				await gqlClient.request(
					UpdateArtworkDetailsBatchDocument,
					{
						ids: scrapedArtworksDetailsIds as string[],
						data: {},
					},
					getAuthorizationHeaders(page.data as ScrapedDataPreviewPageData)
				);

				setUnprocessedIds(idsToProcess);
				setCurrentIds(idsToProcess, scrapedArtworksIds as string[]);
			} else {
				setUnprocessedIds([]);
			}

			if (idsToArchive.length) {
				showToast({
					message: 'Selected artworks have been successfully archived.',
					variant: 'success',
				});
			}

			goto(
				idsToProcess.length ? Routes.ScrapedDataMatchEvents : Routes.ScrapedData
			);
		} catch {
			showToast({
				message: 'Something went wrong when archiving the selected artworks.',
				variant: 'error',
			});

			submitting = false;
		}
	};

	const formatBoolean = (value: boolean | null | undefined) =>
		[null, undefined].includes(value as null | undefined)
			? '-'
			: value
				? 'Yes'
				: 'No';

	const formatName = (
		user:
			| {
					first_name?: string | null;
					last_name?: string | null;
			  }
			| null
			| undefined
	) => {
		if (!user) {
			return '-';
		}

		return [user.first_name, user.last_name].filter(Boolean).join(' ');
	};

	const formatRow = (
		row: NonNullable<GetArtworkFeedQuery['artwork_feed'][number]>
	) => [
		row.event_id?.url || '-',
		row.event_id?.data_source || '-',
		row.event_id?.starts_at
			? `${dayjs(row.event_id?.starts_at)
					.tz(startDateTz || 'UTC')
					.format(
						'MMM D, YYYY HH:mm'
					)}${startDateTz ? '' : ' (Cannot convert to local time, scraped timezone invalid)'}`
			: '-',
		row.event_id?.starts_at_tz || '-',
		row.event_id?.ends_at
			? `${dayjs(row.event_id?.ends_at)
					.tz(endDateTz || 'UTC')
					.format(
						'MMM D, YYYY HH:mm'
					)}${endDateTz ? '' : ' (Cannot convert to local time, scraped timezone invalid)'}`
			: '-',
		row.event_id?.ends_at_tz || '-',
		formatBoolean(row.event_id?.is_closed),
		row.event_id?.sale_number || '-',
		row.event_id?.title || '-',
		row.event_id?.description || '-',
		formatBoolean(row.event_id?.is_charity_fundraiser),
		row.event_id?.organization || '-',
		row.event_id?.location || '-',
		row.event_id?.image_url || '-',
		row.event_id?.pdf_url || '-',
		row.event_id?.created_at
			? dayjs(row.event_id?.created_at).format('MMM D, YYYY HH:mm')
			: '-',
		formatName(row.event_id?.created_by),
		row.event_id?.updated_at
			? dayjs(row.event_id?.updated_at).format('MMM D, YYYY HH:mm')
			: '-',
		formatName(row.event_id?.updated_by),
		row.event_id?.event_type?.key || '-',
		row.url || '-',
		row.data_source || '-',
		row.image_url || '-',
		'-', //row.extra_image_urls.join(', ') || '-',
		row.artist || '-',
		row.description || '-',
		row.title || '-',
		row.mediums || '-',
		row.created_at ? dayjs(row.created_at).format('MMM D, YYYY HH:mm') : '-',
		formatName(row.created_by),
		row.updated_at ? dayjs(row.updated_at).format('MMM D, YYYY HH:mm') : '-',
		formatName(row.updated_by),
		row.dimensions || '-',
		row.dimensions_uom?.name || '-',
		row.edition || '-',
		row.estimate || '-',
		row.estimate_currency?.code || '-',
		row.estimate_low || '-',
		row.estimate_high || '-',
		row.provenance || '-',
		row.literature || '-',
		row.exhibited || '-',
		row.cr_number || '-',
		row.auction_records || '-',
		row.inscriptions || '-',
		row.inscription_date || '-',
		row.inscription_position || '-',
		row.price || '-',
		row.price_amount || '-',
		row.price_currency?.code || '-',
		formatBoolean(row.price_includes_bp),
		row.sale_starts_at
			? dayjs(row.sale_starts_at).format('MMM D, YYYY HH:mm')
			: '-',
		row.sale_starts_at_tz || '-',
		row.sale_ends_at
			? dayjs(row.sale_ends_at).format('MMM D, YYYY HH:mm')
			: '-',
		row.sale_ends_at_tz || '-',
		row.lot_number || '-',
		row.lot_lead || '-',
		row.lot_title || '-',
		row.lot_description || '-',
		row.lot_condition || '-',
		row.lot_shipping || '-',
		row.saleroom_notice || '-',
		row.year_made || '-',
		row.event_id?.id || '-',
	];

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data' },
	];

	const headers = [
		'Artwork',
		'Archive',
		'Event URL',
		'Event Data Source',
		'Event Starts At',
		'Event Starts At Tz',
		'Event Ends At',
		'Event Ends At Tz',
		'Event Is Closed',
		'Event Sale Number',
		'Event Title',
		'Event Description',
		'Event Is Charity Fundraiser',
		'Event Organization',
		'Event Location',
		'Event Image URL',
		'Event PDF URL',
		'Event Created At',
		'Event Created By',
		'Event Updated At',
		'Event Updated By',
		'Event Type',
		'URL',
		'Data Source',
		'Image URL',
		'Extra Image URLS',
		'Artist',
		'Description',
		'Title',
		'Mediums',
		'Created At',
		'Created By',
		'Updated At',
		'Updated By',
		'Dimensions',
		'Dimensions UOM',
		'Edition',
		'Estimate',
		'Currency',
		'Estimate Low',
		'Estimate High',
		'Provenance',
		'Literature',
		'Exhibited',
		'CR Text',
		'Auction Records',
		'Inscriptions',
		'Inscriptions Date',
		'Inscriptions Position',
		'Price',
		'Price Amount',
		'Price Currency',
		'Price Includes BP',
		'Sale Starts At',
		'Sale Starts At Tz',
		'Sale Ends At',
		'Sale Ends At Tz',
		'Lot Number',
		'Lot Lead',
		'Lot Title',
		'Lot Description',
		'Lot Condition',
		'Lot Shipping',
		'Saleroom Notice',
		'Year Made',
		'Event ID',
	];

	const dataCyPrefix = 'preview-scraped-data';
	const cellWidth = '140px';
	const descriptionCellWidth = '300px';

	let showArtworkImages = $state(true);
	let showArtworkImagesOnFetch = $state(true);
	let nbArtworksValue = $state('500');
	let searching = $state(false);
	let submitting = $state(false);

	let columnIndexesToHide = $derived([
		...(showArtworkImagesOnFetch ? [] : [0]),
		...consistentValues.map((consistentValue) =>
			headers.findIndex((header) => header === consistentValue.label)
		),
	]);

	const fetchArtworkFeeds = async (ids: string[]) => {
		const artworkFeedsRes = await gqlClient.request(
			GetArtworkFeedDocument,
			{
				limit: -1,
				filter: {
					id: { _in: ids },
				},
				sort: ['created_at'],
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		rows = artworkFeedsRes?.artwork_feed || [];

		if (rows.length) {
			const { startTz, endTz } = areTimezonesValid(
				rows?.[0]?.event_id?.starts_at_tz,
				rows?.[0]?.event_id?.ends_at_tz
			);

			startDateTz = startTz;
			endDateTz = endTz;

			consistentValues = formatRow(rows[0])
				.map((rowValue, rowValueIndex) => {
					if (
						rows.every((row) => formatRow(row)[rowValueIndex] === rowValue) &&
						rowValue !== '-'
					) {
						return { value: rowValue, label: headers.slice(2)[rowValueIndex] };
					}
					return null;
				})
				.filter(Boolean) as { label: string; value: string }[];

			columnsWithMissingValuesOnly = headers.filter((header, i) => {
				if (i === 1) {
					return false;
				}

				if (!i) {
					return rows.every((row) => !row.primary_image);
				}

				return rows.every((row) => formatRow(row)[i - 2] === '-');
			});

			columnsWithMissingValues = rows.reduce((accumulator: string[], row) => {
				let columns: string[] = [];

				if (!row.primary_image) {
					columns = [...columns, headers[0]];
				}

				formatRow(row).forEach((rowValue, j) => {
					if (rowValue === '-') {
						columns = [...columns, headers.slice(2)[j]];
					}
				});

				return Array.from(new Set([...accumulator, ...columns]));
			}, []);
		} else {
			columnsWithMissingValuesOnly = [];
			consistentValues = [];
			columnsWithMissingValues = [];
		}
	};

	const handleClickFetch = async () => {
		searching = true;
		showArtworkImagesOnFetch = showArtworkImages;
		await fetchArtworkFeeds(
			$artworkFeeds.unprocessedIds.slice(0, +nbArtworksValue)
		);
		searching = false;
	};

	const handleArchiveChange = (index: number) => {
		checkboxesInterval = [index, index, index];
		rows = rows.map((row, i) => {
			if (i !== index) {
				return row;
			}

			return {
				...row,
				archived: !row.archived,
			};
		});
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	{#if isReady}
		<Container dataCy={dataCyPrefix} class="max-w-none">
			{#if typeof window !== 'undefined'}
				<div class="mb-3 flex items-center justify-between">
					<div>
						<ScrapedDataTabs disabled activeTab={0} />
						<Txt variant="label3">
							There are {$artworkFeeds.unprocessedIds.length} artworks in your selection.
							Select amount of images you want to fetch
						</Txt>
					</div>
				</div>

				<div class="flex items-center">
					<Input
						name="nbArtworks"
						dataCy={dataCyPrefix}
						type="number"
						disabled={!!$artworkFeeds.currentIds.length}
						onkeydown={handleKeyDownNumbersOnly}
						bind:value={nbArtworksValue}
						class="mr-4 w-[200px]"
					/>

					<Button
						onclick={handleClickFetch}
						dataCy={dataCyPrefix}
						loading={searching}
						size="md"
						class="mr-4"
						disabled={!!$artworkFeeds.currentIds.length ||
							!nbArtworksValue ||
							isNaN(+nbArtworksValue) ||
							+nbArtworksValue < 1 ||
							+nbArtworksValue > 500}>fetch selected artworks</Button
					>

					<InputLabel dataCy={`${dataCyPrefix}-high-priority`} variant="body3">
						<Checkbox
							disabled={!!$artworkFeeds.currentIds.length}
							dataCy={`${dataCyPrefix}-high-priority-label`}
							name="show_artwork_images"
							class="mt-[-0.125rem]"
							bind:checked={showArtworkImages}
						/>

						<div class="pl-1.5">Show artwork images</div>
					</InputLabel>
				</div>

				<Txt variant="body3" class="mt-0.5 text-gray-500">Max 500</Txt>
			{/if}

			{#if rows.length}
				<table class="mt-4 w-full table-fixed bg-white">
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								class={classNames({
									hidden:
										columnsWithMissingValuesOnly.includes(header) ||
										columnIndexesToHide.includes(i),
								})}
								dataCy={dataCyPrefix}
								width={header.includes('Description') || header.includes('URL')
									? descriptionCellWidth
									: cellWidth}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each rows as row, i}
							<TableRow index={i} dataCy={dataCyPrefix}>
								{#if showArtworkImagesOnFetch && !columnsWithMissingValuesOnly.includes(headers[0])}
									<TableCell width={cellWidth} dataCy={dataCyPrefix}>
										{#if row.primary_image?.id}
											<Image
												dataCy={dataCyPrefix}
												classes={{
													whiteBorderImageClasses: { image: 'h-auto' },
												}}
												image={{
													width: row.primary_image.width || 1,
													height: row.primary_image.height || 1,
													filename_disk: row.primary_image.filename_disk || '',
													url: `${getImageUrl(row.primary_image?.id)}`,
													alt: '',
												}}
											/>
										{:else}
											<div></div>
										{/if}
									</TableCell>
								{/if}
								<TableCell width={cellWidth} dataCy={dataCyPrefix}>
									{#if !isScrapedArtworkProcessed(rows[i].processor_review_artwork?.[0])}
										<Checkbox
											id={`archive-checkbox-${i}`}
											dataCy={dataCyPrefix}
											size="sm"
											onChange={() => handleArchiveChange(i)}
											checked={rows[i].archived}
										/>
									{/if}
								</TableCell>
								{@const formattedRow = formatRow(row)}
								{#each formattedRow as formattedRowValue, j}
									{#if [9, 34].includes(j)}
										<TableCell
											width={descriptionCellWidth}
											dataCy={dataCyPrefix}
											class={classNames('[&_p]:max-h-[125px] overflow-scroll', {
												hidden:
													columnsWithMissingValuesOnly.includes(
														headers[j + 2]
													) || columnIndexesToHide.includes(j + 2),
											})}
										>
											{#snippet custom()}
												<Txt
													variant="body2"
													class={classNames('pr-2', {
														'whitespace-pre-line':
															formattedRowValue?.includes?.('\n'),
													})}
												>
													{formattedRowValue}
												</Txt>
											{/snippet}
										</TableCell>
									{:else}
										<TableCell
											dataCy={dataCyPrefix}
											width={cellWidth}
											wrap
											class={classNames(
												'[&_p]:break-words [&_p]:max-h-[125px] [&_p]:overflow-auto',
												{
													'whitespace-pre-line':
														formattedRowValue?.includes?.('\n'),
													hidden:
														columnsWithMissingValuesOnly.includes(
															headers[j + 2]
														) || columnIndexesToHide.includes(j + 2),
												}
											)}
										>
											{formattedRowValue}
										</TableCell>
									{/if}
								{/each}
							</TableRow>
						{/each}

						{#if !rows?.length}
							<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
								>No data to display</TableNoResults
							>
						{/if}
					</TableBody>
				</table>

				<div class="mt-4 border border-gray-200 bg-gray-100 p-4">
					<Txt variant="label4" class="mb-2">Columns with missing values:</Txt>
					<Txt variant="body2">
						{columnsWithMissingValues?.length
							? columnsWithMissingValues.join(', ')
							: 'None'}
					</Txt>
				</div>

				<div class="my-4 border border-gray-200 bg-gray-100 p-4">
					<Txt variant="label4" class="mb-2">Rows with single values:</Txt>
					{#if consistentValues.length}
						{#each consistentValues as consistentValue}
							<hr class="mt-1" />
							<div class="mt-1 flex items-center">
								<Txt variant="body2" class="w-[250px]">
									{consistentValue.label}
								</Txt>
								<Txt variant="body2">
									{consistentValue.value}
								</Txt>
							</div>
						{/each}
					{:else}
						<Txt variant="body2">None</Txt>
					{/if}
				</div>
			{/if}
		</Container>
	{/if}
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={submitting}
	onSaveClick={handleSaveClick}
	visible={!!rows.length && isReady}
	backHref={Routes.ScrapedData}
	nextLabel="SAVE AND CONTINUE"
/>
