<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataArtworkDetailsPageData } from '$routes/scraped-data/process-data/artwork-details/types';

	const dialogStores = createDialog();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.DateCreatedAsc);
	let updatedIds: string[] = [];
	let sortedScrapedArtworks: null | GetScrapedArtworksQuery['Scraped_Artwork'] =
		$state(null);

	const { saveStage, showMatchEventsWarning, scrapedArtworksStore } =
		getArtworkFeedsStore();

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);
	};

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			sortedScrapedArtworks = [...$scrapedArtworksStore].sort((a, b) =>
				sortFn(a, b, sort)
			);
		}
	});

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.ExecutedTextAsc:
					return {
						field: scrapedArtwork.artwork_feed?.year_made,
						type: 'number',
					};
				case ArtworkDetailsSort.ExecutedTextDesc:
					return {
						field: scrapedArtwork.artwork_feed?.year_made,
						type: 'number',
					};
				case ArtworkDetailsSort.ExecutedYearStartAsc:
					return {
						field: scrapedArtwork.artwork_details?.executed_year_start,
						type: 'number',
					};
				case ArtworkDetailsSort.ExecutedYearStartDesc:
					return {
						field: scrapedArtwork.artwork_details?.executed_year_start,
						type: 'number',
					};
				case ArtworkDetailsSort.ExecutedYearEndAsc:
					return {
						field: scrapedArtwork.artwork_details?.executed_year_end,
						type: 'number',
					};
				case ArtworkDetailsSort.ExecutedYearEndDesc:
					return {
						field: scrapedArtwork.artwork_details?.executed_year_end,
						type: 'number',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(scrapedArtworkB) as Parameters<
				typeof compareScrapedArtworkFields
			>[1],
			sort
		);
	};

	onMount(() => {
		saveStage();
	});

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtworkDetailsPageData>(page.data)
	);

	const handleExecutedYearChange = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e: {
			target:
				| (EventTarget & { value?: string | undefined; name?: string })
				| null;
		},
		fieldName: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1 && e.target?.value !== undefined) {
			const year = parseInt(e.target.value, 10);

			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: year,
				},
			};
		}
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			if (updatedIds.length) {
				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(
						artworkDetails: NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details']
					) => ({
						executed_year_start: artworkDetails?.executed_year_start,
						executed_year_end: artworkDetails?.executed_year_end,
					}),
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		} finally {
			submitting = false;
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataDimensions);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Executed text',
		'Executed start year',
		'Executed end year',
	];

	const dataCyPrefix = 'scraped-data-process-executed';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={3} />
			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2"
						>Artists with suggested auto-matches are listed below. If there is
						no auto-match, you are required to create a new artist with the data
						in the last four columns. If the auto-match suggestion is incorrect,
						remove it to search for another artist in the database or create a
						new artist.</Txt
					>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={twMerge(
									classNames({
										'w-[140px]': i === 0,
										// 'w-[118px]': !i,
									}),
									'border'
								)}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each sortedScrapedArtworks as scrapedArtwork, i}
							{@const isInProgress = !isScrapedArtworkProcessed(scrapedArtwork)}

							<TableRow index={i} dataCy={dataCyPrefix}>
								<TableCell dataCy={dataCyPrefix} class="px-0 py-2 pl-2">
									<div class="flex max-h-[138px] flex-col">
										<a
											target="_blank"
											rel="noopener noreferrer"
											href={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
											class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
										>
											<img
												alt=""
												class="max-h-full max-w-full"
												src={getImageUrl(
													scrapedArtwork?.images?.[0]?.directus_files_id?.id
												)}
											/>
										</a>

										<LinkButton
											dataCy={dataCyPrefix}
											href={scrapedArtwork.artwork_feed?.url}
											disabled={!scrapedArtwork.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											fullWidth
											>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
											{#snippet trailing()}
												{#if scrapedArtwork.artwork_feed?.url}
													<ExternalIcon />
												{/if}
											{/snippet}</LinkButton
										>
									</div>
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3">
											{scrapedArtwork?.artwork_feed?.year_made || ''}
										</Txt>
									{/snippet}
								</TableCell>

								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="executed_year_start"
													maxlength={4}
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleExecutedYearChange(
															scrapedArtwork,
															e,
															'executed_year_start'
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleExecutedYearChange(
															scrapedArtwork,
															e,
															'executed_year_start'
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.executed_year_start || ''
													}` || ''}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.executed_year_start}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="executed_year_end"
													maxlength={4}
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleExecutedYearChange(
															scrapedArtwork,
															e,
															'executed_year_end'
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleExecutedYearChange(
															scrapedArtwork,
															e,
															'executed_year_end'
														);
													}}
													value={`${
														scrapedArtwork.artwork_details.executed_year_end ||
														''
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.executed_year_end}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={twMerge(
										classNames({
											'w-[170px]': !i,
										}),
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_feed?.year_made || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.executed_year_start ||
												''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.executed_year_end || ''}</Txt
										>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={false}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
