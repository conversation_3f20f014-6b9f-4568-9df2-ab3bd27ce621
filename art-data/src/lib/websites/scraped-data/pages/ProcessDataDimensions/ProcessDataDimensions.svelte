<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Checkbox } from '$global/components/Checkbox';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataArtworkDetailsPageData } from '$routes/scraped-data/process-data/artwork-details/types';

	const dialogStores = createDialog();

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.DateCreatedAsc);
	let updatedIds: string[] = [];

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtworkDetailsPageData>(page.data)
	);

	const { saveStage, showMatchEventsWarning, scrapedArtworksStore } =
		getArtworkFeedsStore();

	let sortedScrapedArtworks: null | GetScrapedArtworksQuery['Scraped_Artwork'] =
		$state(null);

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);
	};

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			sortedScrapedArtworks = [...$scrapedArtworksStore].sort((a, b) =>
				sortFn(a, b, sort)
			);
		}
	});

	// const DIMENSIONS_TYPE_OPTIONS = [
	// 	{
	// 		label: 'Each Artwork',
	// 		value: Artwork_Dimension_Type_Enum.PerArtwork,
	// 	},
	// 	{
	// 		label: 'Largest Artwork',
	// 		value: Artwork_Dimension_Type_Enum.LargestArtwork,
	// 	},
	// 	{
	// 		label: 'Overall Size',
	// 		value: Artwork_Dimension_Type_Enum.OverallSize,
	// 	},
	// ];

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.DimensionsTextAsc:
				case ArtworkDetailsSort.DimensionsTextDesc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions,
						type: 'string',
					};
				case ArtworkDetailsSort.HeightAsc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_height_cm,
						type: 'number',
					};
				case ArtworkDetailsSort.HeightDesc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_height_cm,
						type: 'number',
					};
				case ArtworkDetailsSort.WidthAsc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_width_cm,
						type: 'number',
					};
				case ArtworkDetailsSort.WidthDesc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_width_cm,
						type: 'number',
					};
				case ArtworkDetailsSort.DepthAsc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_depth_cm,
						type: 'number',
					};
				case ArtworkDetailsSort.DepthDesc:
					return {
						field: scrapedArtwork.artwork_details?.dimensions_depth_cm,
						type: 'number',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(scrapedArtworkB) as Parameters<
				typeof compareScrapedArtworkFields
			>[1],
			sort
		);
	};

	onMount(() => {
		saveStage();
	});

	const handleChangeBoolean = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		bool: boolean | string,
		fieldName: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: Boolean(bool),
				},
			};
		}
	};

	const handleValueChange = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e: {
			target:
				| (EventTarget & { value?: string | undefined; name?: string })
				| null;
		},
		fieldName: string,
		int: boolean
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1 && e.target?.value !== undefined) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: int ? parseInt(e.target.value, 10) : e.target.value,
				},
			};
		}
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			if (updatedIds.length) {
				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(
						artworkDetails: NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details']
					) => ({
						...(artworkDetails?.dimensions && {
							dimensions: artworkDetails.dimensions,
						}),
						dimensions_height_cm: [null, undefined].includes(
							artworkDetails?.dimensions_height_cm as null | undefined
						)
							? null
							: +(artworkDetails?.dimensions_height_cm as number),
						dimensions_width_cm: [null, undefined].includes(
							artworkDetails?.dimensions_width_cm as null | undefined
						)
							? null
							: +(artworkDetails?.dimensions_width_cm as number),
						dimensions_depth_cm: [null, undefined].includes(
							artworkDetails?.dimensions_depth_cm as null | undefined
						)
							? null
							: +(artworkDetails?.dimensions_depth_cm as number),

						...(artworkDetails?.dimension_type?.key && {
							dimension_type: {
								key: artworkDetails.dimension_type.key,
							},
						}),
						number_of_artworks: [null, undefined].includes(
							artworkDetails?.number_of_artworks as null | undefined
						)
							? null
							: +(artworkDetails?.number_of_artworks as number),
						number_of_pieces: [null, undefined].includes(
							artworkDetails?.number_of_pieces as null | undefined
						)
							? null
							: +(artworkDetails?.number_of_pieces as number),

						...(artworkDetails?.is_full_set && {
							is_full_set: artworkDetails.is_full_set,
						}),
						...(artworkDetails?.is_bundle && {
							is_bundle: artworkDetails.is_bundle,
						}),
					}),
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		} finally {
			submitting = false;
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataEditionsInfo);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Dimensions text',
		'Height (cm)',
		'Width (cm)',
		'Depth (cm)',
		'No. artworks',
		'No. pieces',
		'Is full set',
		'Is bundle',
	];

	const dataCyPrefix = 'scraped-data-process-dimensions';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={4} />
			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={twMerge(
									classNames({
										'w-[136px]': !i,
										// 'w-[185px]': i === 6,
										// 'w-[90px]': [3, 4, 5, 7, 8].includes(i),
										// 'w-[70px]': [9, 10].includes(i),
									}),
									'border'
								)}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each sortedScrapedArtworks as scrapedArtwork, i}
							{@const isInProgress = !isScrapedArtworkProcessed(scrapedArtwork)}
							<TableRow index={i} dataCy={dataCyPrefix}>
								<TableCell dataCy={dataCyPrefix}>
									<a
										target="_blank"
										rel="noopener noreferrer"
										href={getImageUrl(
											scrapedArtwork?.images?.[0]?.directus_files_id?.id
										)}
										class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
									>
										<img
											alt=""
											class="max-h-full max-w-full"
											src={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
										/>
									</a>

									<LinkButton
										dataCy={dataCyPrefix}
										href={scrapedArtwork.artwork_feed?.url}
										disabled={!scrapedArtwork.artwork_feed?.url}
										newTab
										variant="secondary"
										size="sm"
										fullWidth
										>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
										{#snippet trailing()}
											{#if scrapedArtwork.artwork_feed?.url}
												<ExternalIcon />
											{/if}
										{/snippet}</LinkButton
									>
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									{/snippet}
								</TableCell>

								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3">
											{scrapedArtwork?.artwork_details?.dimensions || ''}
										</Txt>
									{/snippet}
								</TableCell>

								<!-- <TableCell dataCy={dataCyPrefix}>
									{#if scrapedArtwork.artwork_details}
										<Input
											dataCy={dataCyPrefix}
											name="dimensions"
											rows={4}
											onkeyup={(e) => {
												handleChangeArtworkDetails(scrapedArtwork);
												handleValueChange(
													scrapedArtwork,
													e,
													'dimensions',
													false
												);
											}}
											onchange={(e) => {
												handleChangeArtworkDetails(scrapedArtwork);
												handleValueChange(
													scrapedArtwork,
													e,
													'dimensions',
													false
												);
											}}
											value={`${
												scrapedArtwork.artwork_details.dimensions || ''
											}`}
										/>
									{/if}
								</TableCell> -->
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="dimensions_height_cm"
													onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_height_cm',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_height_cm',
															false
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.dimensions_height_cm === null
															? ''
															: scrapedArtwork.artwork_details
																	.dimensions_height_cm
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details
														.dimensions_height_cm === null
														? ''
														: scrapedArtwork.artwork_details
																.dimensions_height_cm}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="dimensions_width_cm"
													onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_width_cm',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_width_cm',
															false
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.dimensions_width_cm === null
															? ''
															: scrapedArtwork.artwork_details
																	.dimensions_width_cm
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details
														.dimensions_width_cm === null
														? ''
														: scrapedArtwork.artwork_details
																.dimensions_width_cm}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="dimensions_depth_cm"
													onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_depth_cm',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'dimensions_depth_cm',
															false
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.dimensions_depth_cm === null
															? ''
															: scrapedArtwork.artwork_details
																	.dimensions_depth_cm
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details
														.dimensions_depth_cm === null
														? ''
														: scrapedArtwork.artwork_details
																.dimensions_depth_cm}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="number_of_artworks"
													onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'number_of_artworks',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'number_of_artworks',
															false
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.number_of_artworks === null
															? ''
															: scrapedArtwork.artwork_details
																	.number_of_artworks
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.number_of_artworks ===
													null
														? ''
														: scrapedArtwork.artwork_details.number_of_artworks}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="number_of_pieces"
													onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'number_of_pieces',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'number_of_pieces',
															false
														);
													}}
													value={`${
														scrapedArtwork.artwork_details.number_of_pieces ===
														null
															? ''
															: scrapedArtwork.artwork_details.number_of_pieces
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.number_of_pieces ===
													null
														? ''
														: scrapedArtwork.artwork_details.number_of_pieces}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-is-full-set`}
													checked={scrapedArtwork.artwork_details
														?.is_full_set || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															'is_full_set'
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details?.is_full_set
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-is-bundle`}
													checked={scrapedArtwork.artwork_details?.is_bundle ||
														false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(scrapedArtwork, e, 'is_bundle');
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details?.is_bundle
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={twMerge(
										classNames({
											'w-[136px]': !i,
											// 'w-[185px]': i === 6,
											// 'w-[90px]': [3, 4, 5, 7, 8].includes(i),
											// 'w-[70px]': [9, 10].includes(i),
										}),
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.dimensions || ''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.dimensions_height_cm === null
												? ''
												: artwork?.artwork_details?.dimensions_height_cm}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.dimensions_width_cm === null
												? ''
												: artwork?.artwork_details?.dimensions_width_cm}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.dimensions_depth_cm === null
												? ''
												: artwork?.artwork_details?.dimensions_depth_cm}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.number_of_artworks === null
												? ''
												: artwork?.artwork_details?.number_of_artworks}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.number_of_pieces === null
												? ''
												: artwork?.artwork_details?.number_of_pieces}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.is_full_set
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.is_bundle ? 'Yes' : 'No'}</Txt
										>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={false}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
