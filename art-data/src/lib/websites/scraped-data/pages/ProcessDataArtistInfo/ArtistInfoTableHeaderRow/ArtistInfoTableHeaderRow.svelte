<script lang="ts">
	import classNames from 'classnames';
	import { TableHeaderRow, TableHeader } from '$global/components/Table';

	interface Props {
		dataCy: string;
		class?: string;
	}

	let { ...props }: Props = $props();

	const headers = [
		'Scraped Artist Name',
		'Matched artist',
		'Ref. ID',
		'Artist name',
		'Nationality',
		'DOB',
		'DOD',
	];
</script>

<TableHeaderRow dataCy={props.dataCy} class={props.class}>
	{#each headers as header, i}
		<TableHeader
			dataCy={props.dataCy}
			class={classNames({
				// 'w-[265px]': !i,
				// 'w-[250px]': i === 1,
				'w-[80px]': [2, 5, 6].includes(i),
				// 'w-[200px]': i === 3,
				'w-[260px]': i === 4,
				'border-l border-gray-200': i === 1 || i === 3,
			})}
		>
			{header}
		</TableHeader>
	{/each}
</TableHeaderRow>
