export { default as ProcessDataArtistInfo } from './ProcessDataArtistInfo.svelte';

export enum ArtworkDetailsSort {
	ScrapedArtistAsc = 'SCRAPED VALUE (A-Z)',
	ScrapedArtistDesc = 'SCRAPED VALUE (Z-A)',
	MatchedArtistAsc = 'MATCHED ARTIST (A-Z)',
	MatchedArtistDesc = 'MATCHED ARTIST (Z-A)',
	NationalityAsc = 'NATIONALITY (A-Z)',
	NationalityDesc = 'NATIONALITY (Z-A)',
	YearOfBirthAsc = 'YEAR OF BIRTH ASC',
	YearOfBirthDesc = 'YEAR OF BIRTH DESC',
	YearOfDeathAsc = 'YEAR OF DEATH ASC',
	YearOfDeathDesc = 'YEAR OF DEATH DESC',
	ReferenceIdAsc = 'REFERENCE ID ASC',
	ReferenceIdDesc = 'REFERENCE ID DESC',
}
