<script lang="ts" module>
	export const formatArtist = (
		artist: ArtistSearchQuery['artistSearch']['data'][number] & {
			legacy_id?: string | null | undefined;
		}
	) => {
		if (!artist) {
			return { line1: '' };
		}

		const url = `${Config.ArteyeDomain}/artists/${artist?.id}`;
		const artworkCount = artist?.aggregations?.[0]?.artwork_count || 0;
		const yearBirthDeath = getYearBirthDeathString({
			yearBirth: artist.person?.year_birth,
			yearDeath: artist.person?.year_death,
			withWrapper: false,
		});

		const getSubtitle = () => {
			const nationality =
				artist.person?.nationalities?.[0]?.country?.country_nationality;

			if (nationality && yearBirthDeath) {
				return `${nationality}, ${yearBirthDeath}`;
			}

			if (nationality) {
				return `${nationality}`;
			}

			if (yearBirthDeath) {
				return `${yearBirthDeath}`;
			}

			return '';
		};

		return {
			onClick: () => {
				window.open(url, '_blank', 'noopener noreferrer');
			},
			line1: `${artist.person?.entity?.name}`,
			line3: url,
			linkHref: url,
			line4: `${artist.id}`,
			...(artist?.reference_id && {
				line5: `Ref ID: ${artist?.reference_id}`,
			}),
			...(artist?.legacy_id && {
				line6: `Legacy ID: ${artist?.legacy_id}`,
			}),
			line7: artist.person?.nationalities?.[0]?.country?.code,
			line8: artist.person?.year_birth as unknown as string,
			line9: artist.person?.year_death as unknown as string,
			line10: getSubtitle(),
			line11: `${artworkCount} artworks`,
		};
	};

	const getYearBirthDeathString = ({
		yearBirth,
		yearDeath,
		wrapper = ['(', ')'],
		withWrapper = true,
	}: {
		yearBirth: number | string | null | undefined;
		yearDeath: number | string | null | undefined;
		wrapper?: [string, string];
		withWrapper?: boolean;
	}) => {
		const wrapperLeft = withWrapper ? wrapper[0] : '';
		const wrapperRight = withWrapper ? wrapper[1] : '';

		if (yearBirth && yearDeath) {
			return `${wrapperLeft}${yearBirth} - ${yearDeath}${wrapperRight}`;
		}

		if (yearBirth) {
			return `${wrapperLeft}b.${yearBirth}${wrapperRight}`;
		}

		if (yearDeath) {
			return `${wrapperLeft}d.${yearDeath}${wrapperRight}`;
		}

		return '';
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import {
		ArtistSearchSortField,
		SortDirection,
	} from '$gql/types-arteye-custom';
	import { Config } from '$lib/constants/config';
	import { GetLegacyIdDocument } from '$lib/custom-arteye-queries/__generated__/getLegacyId.generated';
	import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
	import {
		ArtistSearchDocument,
		type ArtistSearchQuery,
		type ArtistSearchQueryVariables,
	} from '$lib/websites/scraped-data/custom-arteye-queries/__generated__/artistSearch.generated';

	interface Props {
		class?: string;
		lastSorted: number;
		dataCy: string;
		disabled?: boolean | undefined;
		selectedOption?: OptionType | null;
		onRemove: (artistId: string | null | undefined) => void;
		onChange: (e: {
			detail: {
				value: OptionType;
			};
		}) => Promise<void>;
	}

	let {
		dataCy,
		lastSorted,
		disabled = false,
		selectedOption = $bindable(null),
		onRemove,
		onChange,
		...rest
	}: Props = $props();

	let value = $state(writable(''));

	$effect(() => {
		if (lastSorted) {
			value.set('');
		}
	});

	const getVariables = (value: string): ArtistSearchQueryVariables => {
		return {
			input: {
				limit: 15,
				sort: [
					{
						direction: SortDirection.Desc,
						field: ArtistSearchSortField.NumberOfArtworks,
					},
				],
				filters: {
					nameOrId: value,
				},
			},
		};
	};

	const getOptions = async (value: string) => {
		if (!value) {
			return [];
		}

		const optionsRes = await gqlClientCustomArteye.request(
			ArtistSearchDocument,
			getVariables(value),
			getAuthorizationHeaders({
				user: { access_token: page.data.user.arteye_token },
			})
		);

		if (isOnDev()) {
			return (optionsRes?.artistSearch?.data || [])?.map((artist, i) =>
				formatArtist(artist as NonNullable<typeof artist>)
			);
		}

		const legacyIdPromises = (optionsRes?.artistSearch?.data || [])?.map(
			(artist) =>
				gqlClientCustomArteye.request(
					GetLegacyIdDocument,
					{
						id: artist?.id,
						collection: 'artist',
					},
					getAuthorizationHeaders({
						user: { access_token: page.data.user.arteye_token },
					})
				)
		);

		const legacyIdsRes = await Promise.all(legacyIdPromises);
		return (optionsRes?.artistSearch?.data || [])?.map((artist, i) =>
			formatArtist({
				...artist,
				legacy_id: legacyIdsRes[i]?.getLegacyId?.legacyId,
			})
		);
	};

	const handleRemoveSelectedOption = () => {
		if (selectedOption) {
			onRemove(selectedOption.line4);
		}

		selectedOption = null;
		value.set('');
	};
</script>

<div class="relative">
	<PromiseAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="artists"
		dataCy={`${dataCy}-artists`}
		placeholder="Search by artist name or reference ID"
		onRemoveSelectedOption={handleRemoveSelectedOption}
		showResultsWhenEmpty={false}
		classes={{
			longList: '!max-h-[225px] !min-h-[auto] z-[10] bg-[white]',
			selectedOption: {
				line1Suffix: 'block',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
				wrapper: classNames(
					'py-2 bg-white rounded border border-gray-200 bg-white pl-3 py-1',
					disabled ? 'pr-3' : 'pr-7'
				),
				line7: 'hidden',
				line8: 'hidden',
				line9: 'hidden',
			},
			option: {
				line3: 'hidden',
				line4: 'hidden',
				line7: 'hidden',
				line8: 'hidden',
				line9: 'hidden',
			},
			noResults: 'p-0',
			input: rest.class,
		}}
		{getOptions}
		{onChange}
		{value}
		bind:selectedOption
		debounceTimeout={300}
	>
		{#snippet noResults()}
			<div>
				<NoResults class="py-2 text-left" dataCy={`${dataCy}-artist`}>
					No artist found.
				</NoResults>
			</div>
		{/snippet}
	</PromiseAutocomplete>
	{#if selectedOption && !disabled}
		<button class="absolute right-3 top-3" onclick={handleRemoveSelectedOption}>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
