<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { ArtistInfoTableHeaderRow } from '../ArtistInfoTableHeaderRow';
	import { ArtistInfoTableRow } from '../ArtistInfoTableRow';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { Dialog } from '$global/components/Dialog';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { TableBody } from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Config } from '$lib/constants/config';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		getArteyeImageUrl,
		getImageUrl,
	} from '$lib/utils/getImageUrl/getImageUrl';
	import {
		GetArtworksDocument,
		type GetArtworksQuery,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getArtworks.generated';
	import type { GetScrapedArtistsQuery } from '$lib/websites/scraped-data/custom-queries/__generated__/getScrapedArtists.generated';
	import {
		GetScrapedArtworkImagesDocument,
		type GetScrapedArtworkImagesQuery,
	} from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworkImages.generated';

	interface Props {
		dataCy: string;
		artistSelectedOptions: Record<string, OptionType | null>;
		scrapedArtistDialogIndex: number;
		scrapedArtist: NonNullable<
			GetScrapedArtistsQuery['getScrapedArtists']
		>[number];
		scrapedArtists: GetScrapedArtistsQuery['getScrapedArtists'];
		updatedIds: string[];
	}

	let {
		dataCy,
		artistSelectedOptions = $bindable(),
		scrapedArtistDialogIndex = $bindable(),
		scrapedArtist = $bindable(),
		scrapedArtists = $bindable(),
		updatedIds = $bindable(),
	}: Props = $props();

	const dialogStores = createDialog();

	let artworks: GetArtworksQuery['artwork'] = $state([]);
	let scrapedArtworks: GetScrapedArtworkImagesQuery['Scraped_Artwork'] = $state(
		[]
	);

	$effect(() => {
		const loadArtworks = async () => {
			if (scrapedArtist?.scraped_artist_id) {
				dialogStores.states.open.set(true);

				const scrapedArtworksRes = await gqlClient.request(
					GetScrapedArtworkImagesDocument,
					{
						filter: {
							artwork_feed: {
								artist: { _icontains: scrapedArtist.artist_text_key },
							},
						},
					},
					getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					)
				);

				scrapedArtworks = scrapedArtworksRes.Scraped_Artwork;

				if (artistSelectedOptions[scrapedArtist.scraped_artist_id]) {
					const artworksRes = await gqlClientArteye.request(
						GetArtworksDocument,
						{
							filter: {
								artists: {
									artist_id: {
										id: {
											_eq: artistSelectedOptions[
												scrapedArtist.scraped_artist_id
											]?.line4,
										},
									},
								},
							},
						},
						getAuthorizationHeaders({
							user: { access_token: page.data.user.arteye_token },
						})
					);

					artworks = artworksRes?.artwork;
				}
			} else {
				dialogStores.states.open.set(false);
				artworks = [];
			}
		};

		loadArtworks();
	});
</script>

<Dialog
	{dataCy}
	{dialogStores}
	shouldClose={false}
	showCloseIcon={false}
	class="max-w-[none] sm:px-4"
>
	{#if scrapedArtist}
		<Container {dataCy} class="px-0 max-w-none">
			<table
				class="mb-4 w-full table-fixed border-collapse rounded-b-md bg-white"
			>
				<ArtistInfoTableHeaderRow {dataCy} />
				<TableBody {dataCy}>
					<ArtistInfoTableRow
						lastSorted={1}
						index={0}
						{dataCy}
						bind:scrapedArtists
						bind:updatedIds
						bind:artistSelectedOptions
						bind:scrapedArtist
					/>
				</TableBody>
			</table>

			<div class="mb-4 rounded border border-gray-200 bg-gray-100 p-4">
				<Txt variant="h6" class="mb-2">Scraped artworks by artist</Txt>
				<Txt variant="body2" class="mb-2">
					Below are all the scraped artworks by <span class="font-[500]"
						>'{scrapedArtist?.artist_text_key}'</span
					>
				</Txt>
				<div class="flex flex-wrap gap-x-10 gap-y-2.5">
					{#each scrapedArtworks as scrapedArtwork}
						<div class="w-[7.5rem]">
							<div
								class="flex h-[7.5rem] w-[7.5rem] items-center justify-center bg-gray-200 p-2"
							>
								<img
									alt=""
									class="max-h-full max-w-full"
									src={getImageUrl(
										scrapedArtwork?.artwork_feed?.primary_image?.id,
										page.data.user.access_token
									)}
								/>
							</div>
							<Txt variant="body3" class="mt-1"
								>{scrapedArtwork?.artwork_details?.title}</Txt
							>
						</div>
					{/each}
				</div>
			</div>

			{#if scrapedArtist?.scraped_artist_id && artistSelectedOptions[scrapedArtist?.scraped_artist_id]}
				<div class="mb-4 rounded border border-gray-200 bg-gray-100 p-4">
					<Txt variant="h6" class="mb-2"
						>Artworks by suggested artist match in database</Txt
					>
					<Txt variant="body2" class="mb-2">
						Below are all the artworks already in the database by <a
							target="_blank"
							rel="noopener noreferrer"
							class="text-blue-500"
							href={`${Config.ArteyeDomain}/artists/${
								artistSelectedOptions[scrapedArtist?.scraped_artist_id]?.line4
							}`}
							>{artistSelectedOptions[scrapedArtist?.scraped_artist_id]
								?.line1}</a
						>.
					</Txt>
					<div class="flex flex-wrap gap-x-10 gap-y-2.5">
						{#each artworks as artwork}
							<div class="w-[7.5rem]">
								<div
									class="flex h-[7.5rem] w-[7.5rem] items-center justify-center bg-gray-200 p-2"
								>
									<img
										alt=""
										class="max-h-full max-w-full"
										src={getArteyeImageUrl(
											artwork?.primary_image?.id,
											page.data.user.arteye_token
										)}
									/>
								</div>
								<Txt variant="body3" class="mt-1">{artwork?.title}</Txt>
							</div>
						{/each}
					</div>
				</div>
			{/if}

			<Button
				class="mx-auto w-full max-w-[450px]"
				variant="secondary"
				{dataCy}
				size="md"
				onclick={() => {
					scrapedArtistDialogIndex = -1;
				}}
			>
				back
			</Button>
		</Container>
	{/if}
</Dialog>
