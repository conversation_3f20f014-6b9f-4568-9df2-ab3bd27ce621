<script lang="ts">
	import classNames from 'classnames';
	import { ArtistAutocomplete } from '../ArtistAutocomplete';
	import { page } from '$app/state';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { TableRow, TableCell } from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { GetScrapedArtistsQuery } from '$lib/websites/scraped-data/custom-queries/__generated__/getScrapedArtists.generated';
	import type { ScrapedDataProcessDataArtistInfoPageData } from '$routes/scraped-data/process-data/artist-info/types';

	interface Props {
		index: number;
		lastSorted: number;
		scrapedArtistDialogIndex?: number | undefined;
		dataCy: string;
		artistSelectedOptions: Record<string, OptionType | null>;
		scrapedArtist: NonNullable<
			GetScrapedArtistsQuery['getScrapedArtists']
		>[number];
		readOnly?: boolean;
		scrapedArtists: GetScrapedArtistsQuery['getScrapedArtists'];
		updatedIds: string[];
	}

	let {
		index,
		lastSorted,
		scrapedArtistDialogIndex = $bindable(undefined),
		dataCy,
		readOnly = false,
		artistSelectedOptions = $bindable(),
		scrapedArtist = $bindable(),
		scrapedArtists = $bindable(),
		updatedIds = $bindable(),
	}: Props = $props();

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtistInfoPageData>(page.data)
	);
	let countries = $derived(data.countries);

	let nationalitiesOptions: MultiSelectOption[] = $state([]);
	let isRowInvalid = $derived(
		scrapedArtist?.scraped_artist_id &&
			!artistSelectedOptions[scrapedArtist.scraped_artist_id] &&
			!scrapedArtist.artist_details?.name
	);

	$effect(() => {
		nationalitiesOptions = countries.map<MultiSelectOption>((country) => {
			let label = country?.country_nationality
				? `${country?.country_nationality} (${country?.name})`
				: country?.name;

			return {
				label,
				value: country?.name || '',
			};
		});
	});

	const findScrapedArtistIndex = (scrapedArtistId: string) =>
		scrapedArtists?.findIndex(
			(scrapedArtist) => scrapedArtist?.scraped_artist_id === scrapedArtistId
		);

	const handleChangeScrapedArtist = (scrapedArtistId: string) => {
		updatedIds = Array.from(new Set([...updatedIds, scrapedArtistId]));
	};

	const handleChangeArtist = async (
		scrapedArtistId: string,
		{ detail: { value: artist } }: { detail: { value: OptionType } }
	) => {
		handleChangeScrapedArtist(scrapedArtistId);
		const scrapedArtistIndex = findScrapedArtistIndex(scrapedArtistId);
		if (
			scrapedArtistIndex !== undefined &&
			scrapedArtists?.[scrapedArtistIndex]
		) {
			scrapedArtists[scrapedArtistIndex].processed_artist_id = artist.line4;
			scrapedArtists[scrapedArtistIndex].artist_reference_id =
				artist.line5?.split(': ')[1];
		}

		return Promise.resolve();
	};

	const handleRemoveArtist = (scrapedArtistId: string | null | undefined) => {
		const scrapedArtistIndex = findScrapedArtistIndex(`${scrapedArtistId}`);
		if (
			scrapedArtistIndex === undefined ||
			!scrapedArtists?.[scrapedArtistIndex]
		) {
			return;
		}

		scrapedArtists[scrapedArtistIndex].artist_reference_id = null;
		scrapedArtists[scrapedArtistIndex].processed_artist_id = null;
		handleChangeScrapedArtist(`${scrapedArtistId}`);
	};

	const handleChangeYearBirth = (
		e: Event,
		scrapedArtistId: string | null | undefined
	) => {
		const scrapedArtistIndex = findScrapedArtistIndex(`${scrapedArtistId}`);
		if (
			scrapedArtistIndex === undefined ||
			!scrapedArtists?.[scrapedArtistIndex]
		) {
			return;
		}

		if (scrapedArtists[scrapedArtistIndex]?.artist_details) {
			(
				(
					scrapedArtists[scrapedArtistIndex] as NonNullable<
						NonNullable<typeof scrapedArtists>[number]
					>
				).artist_details as {
					year_birth: number;
				}
			).year_birth = +(e.target as HTMLInputElement).value;

			handleChangeScrapedArtist(
				`${scrapedArtists[scrapedArtistIndex]?.scraped_artist_id}`
			);
		}
	};

	const handleChangeYearDeath = (
		e: Event,
		scrapedArtistId: string | null | undefined
	) => {
		const scrapedArtistIndex = findScrapedArtistIndex(`${scrapedArtistId}`);

		if (
			scrapedArtistIndex === undefined ||
			!scrapedArtists?.[scrapedArtistIndex]
		) {
			return;
		}

		if (scrapedArtists[scrapedArtistIndex]?.artist_details) {
			(
				(
					scrapedArtists[scrapedArtistIndex] as NonNullable<
						NonNullable<typeof scrapedArtists>[number]
					>
				).artist_details as {
					year_death: number;
				}
			).year_death = +(e.target as HTMLInputElement).value;

			handleChangeScrapedArtist(
				`${scrapedArtists[scrapedArtistIndex]?.scraped_artist_id}`
			);
		}
	};

	const handleNationalitiesChange = (
		scrapedArtistId: string,
		selected: MultiSelectOption[]
	) => {
		const scrapedArtistIndex = findScrapedArtistIndex(`${scrapedArtistId}`);
		if (
			scrapedArtistIndex === undefined ||
			!scrapedArtists?.[scrapedArtistIndex]
		) {
			return;
		}

		if (selected?.length) {
			const addedNationality = selected[selected.length - 1];
			const nationalityToAdd = countries?.find(
				(country) => country?.name === addedNationality?.value
			);

			if (scrapedArtists[scrapedArtistIndex]?.artist_details) {
				(
					(
						scrapedArtists[scrapedArtistIndex] as NonNullable<
							NonNullable<typeof scrapedArtists>[number]
						>
					).artist_details as {
						nationality_country_code: NonNullable<
							NonNullable<
								NonNullable<GetScrapedArtistsQuery['getScrapedArtists']>[number]
							>['artist_details']
						>['nationality_country_code'];
					}
				).nationality_country_code = `${nationalityToAdd?.code}`;

				handleChangeScrapedArtist(
					`${scrapedArtists[scrapedArtistIndex]?.scraped_artist_id}`
				);
			}
		} else {
			if (scrapedArtists[scrapedArtistIndex]?.artist_details) {
				(
					(
						scrapedArtists[scrapedArtistIndex] as NonNullable<
							NonNullable<typeof scrapedArtists>[number]
						>
					).artist_details as {
						nationality_country_code: NonNullable<
							NonNullable<
								NonNullable<GetScrapedArtistsQuery['getScrapedArtists']>[number]
							>['artist_details']
						>['nationality_country_code'];
					}
				).nationality_country_code = null;

				handleChangeScrapedArtist(
					`${scrapedArtists[scrapedArtistIndex]?.scraped_artist_id}`
				);
			}
		}
	};
</script>

{#if scrapedArtist?.scraped_artist_id}
	<TableRow {index} {dataCy}>
		<TableCell {dataCy}>
			{#snippet custom()}
				<div>
					<button
						onclick={() => {
							scrapedArtistDialogIndex = index;
						}}
					>
						<Txt component="span" variant="body2" class="underline">
							{scrapedArtist?.artist_text_key || ''}
						</Txt>
					</button>
				</div>
			{/snippet}
		</TableCell>
		<TableCell
			{dataCy}
			class={classNames('border-l border-gray-200', {
				'pointer-events-none': readOnly,
			})}
		>
			{#snippet custom()}
				{#if scrapedArtist.scraped_artist_id && (!readOnly || (readOnly && artistSelectedOptions[scrapedArtist.scraped_artist_id])) && typeof artistSelectedOptions[scrapedArtist.scraped_artist_id] !== 'undefined'}
					<ArtistAutocomplete
						{lastSorted}
						disabled={readOnly}
						class={classNames({ 'border-red-500': isRowInvalid })}
						onRemove={() =>
							handleRemoveArtist(scrapedArtist?.scraped_artist_id)}
						onChange={(artist) =>
							handleChangeArtist(`${scrapedArtist?.scraped_artist_id}`, artist)}
						bind:selectedOption={
							artistSelectedOptions[scrapedArtist.scraped_artist_id]
						}
						{dataCy}
					/>
				{/if}
			{/snippet}
		</TableCell>
		<TableCell {dataCy}>
			{#snippet custom()}
				<Txt variant="body3">
					{scrapedArtist?.artist_reference_id || ''}
				</Txt>
			{/snippet}
		</TableCell>
		<TableCell {dataCy} class="border-l border-gray-200" wrap={readOnly}>
			{#snippet custom()}
				{@const isArtistMatched = !!(
					scrapedArtist.scraped_artist_id &&
					artistSelectedOptions[scrapedArtist?.scraped_artist_id]
				)}
				<div>
					{#if scrapedArtist.artist_details}
						{#if !readOnly}
							{#if !isArtistMatched}
								<Input
									{dataCy}
									name="artwork_title"
									size="sm"
									class={classNames({ 'border-red-500': isRowInvalid })}
									onchange={() =>
										handleChangeScrapedArtist(
											`${scrapedArtist?.scraped_artist_id}`
										)}
									bind:value={scrapedArtist.artist_details.name}
								/>
							{:else}
								<Input
									{dataCy}
									name="artwork_title"
									size="sm"
									class={classNames({ 'border-red-500': isRowInvalid })}
									disabled
									value={artistSelectedOptions[
										scrapedArtist?.scraped_artist_id as string
									]?.line1}
								/>
							{/if}
						{:else}
							<Txt variant="body3">
								{#if isArtistMatched}
									{artistSelectedOptions[
										scrapedArtist?.scraped_artist_id as string
									]?.line1}
								{:else}
									{scrapedArtist.artist_details?.name || ''}
								{/if}
							</Txt>
						{/if}
					{/if}
				</div>
			{/snippet}
		</TableCell>
		<TableCell {dataCy} wrap={readOnly}>
			{#snippet custom()}
				{@const isArtistMatched = !!(
					scrapedArtist.scraped_artist_id &&
					artistSelectedOptions[scrapedArtist?.scraped_artist_id]
				)}
				<div>
					{#if scrapedArtist.artist_details}
						{#if !readOnly}
							{#key scrapedArtist.artist_details.nationality_country_code}
								{#key isArtistMatched}
									<MultiSelect
										label=""
										name="nationalities"
										dataCy={`${dataCy}-nationalities`}
										selected={(
											isArtistMatched
												? artistSelectedOptions[
														scrapedArtist?.scraped_artist_id as string
													]?.line7
												: scrapedArtist.artist_details.nationality_country_code
										)
											? [
													{
														value: isArtistMatched
															? (artistSelectedOptions[
																	scrapedArtist?.scraped_artist_id as string
																]?.line7 as string)
															: `${scrapedArtist.artist_details.nationality_country_code}`,
														label: `${
															countries.find(
																(country) =>
																	country.code ===
																	(isArtistMatched
																		? artistSelectedOptions[
																				scrapedArtist?.scraped_artist_id as string
																			]?.line7
																		: scrapedArtist.artist_details
																				?.nationality_country_code)
															)?.country_nationality
														}`,
													},
												]
											: []}
										size="sm"
										maxSelect={1}
										placeholder="Type or select"
										options={nationalitiesOptions}
										disabled={isArtistMatched}
										onChange={(_, selected) => {
											handleNationalitiesChange(
												`${scrapedArtist?.scraped_artist_id}`,
												selected
											);
										}}
									/>
								{/key}
							{/key}
						{:else}
							<Txt variant="body3"
								>{#if !isArtistMatched}
									{countries.find(
										(country) =>
											country.code ===
											scrapedArtist.artist_details?.nationality_country_code
									)?.country_nationality || ''}{:else}
									{countries.find(
										(country) =>
											country.code ===
											artistSelectedOptions[
												scrapedArtist?.scraped_artist_id as string
											]?.line7
									)?.country_nationality || ''}
								{/if}</Txt
							>
						{/if}
					{/if}
				</div>
			{/snippet}
		</TableCell>
		<TableCell {dataCy} wrap={readOnly}>
			{#snippet custom()}
				{@const isArtistMatched = !!(
					scrapedArtist.scraped_artist_id &&
					artistSelectedOptions[scrapedArtist?.scraped_artist_id]
				)}
				<div>
					{#if !readOnly}
						{#if !isArtistMatched}
							<Input
								{dataCy}
								name="artwork_title"
								size="sm"
								maxlength={4}
								onkeydown={(e) => handleKeyDownNumbersOnly(e, false)}
								onchange={(e) =>
									handleChangeYearBirth(e, scrapedArtist?.scraped_artist_id)}
								value={`${scrapedArtist.artist_details?.year_birth || ''}`}
							/>
						{:else}
							<Input
								{dataCy}
								name="artwork_title"
								size="sm"
								maxlength={4}
								disabled
								value={`${artistSelectedOptions[scrapedArtist?.scraped_artist_id as string]?.line8 || ''}`}
							/>
						{/if}
					{:else}
						<Txt variant="body3"
							>{#if !isArtistMatched}{scrapedArtist.artist_details
									?.year_birth || ''}{:else}{artistSelectedOptions[
									scrapedArtist?.scraped_artist_id as string
								]?.line8 || ''}{/if}</Txt
						>
					{/if}
				</div>
			{/snippet}
		</TableCell>
		<TableCell {dataCy} wrap={readOnly}>
			{#snippet custom()}
				{@const isArtistMatched = !!(
					scrapedArtist.scraped_artist_id &&
					artistSelectedOptions[scrapedArtist?.scraped_artist_id]
				)}
				<div>
					{#if !readOnly}
						{#if !isArtistMatched}
							<Input
								{dataCy}
								name="artwork_title"
								size="sm"
								maxlength={4}
								onkeydown={(e) => handleKeyDownNumbersOnly(e, false)}
								onchange={(e) =>
									handleChangeYearDeath(e, scrapedArtist?.scraped_artist_id)}
								value={`${scrapedArtist.artist_details?.year_death || ''}`}
							/>
						{:else}
							<Input
								{dataCy}
								name="artwork_title"
								size="sm"
								maxlength={4}
								disabled
								value={`${artistSelectedOptions[scrapedArtist?.scraped_artist_id as string]?.line9 || ''}`}
							/>
						{/if}
					{:else}
						<Txt variant="body3"
							>{#if !isArtistMatched}{scrapedArtist.artist_details
									?.year_death || ''}{:else}{artistSelectedOptions[
									scrapedArtist?.scraped_artist_id as string
								]?.line9 || ''}{/if}</Txt
						>
					{/if}
				</div>
			{/snippet}
		</TableCell>
	</TableRow>
{/if}
