<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { type GetArtistsQuery } from '../../arteye-queries/__generated__/getArtists.generated';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { type GetScrapedArtistsQuery } from '../../custom-queries/__generated__/getScrapedArtists.generated';
	import { UpdateScrapedArtistsDocument } from '../../custom-queries/__generated__/updateScrapedArtists.generated';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { ArtistArtworksDialog } from './ArtistArtworksDialog';
	import { ArtistInfoTableHeaderRow } from './ArtistInfoTableHeaderRow';
	import { ArtistInfoTableRow } from './ArtistInfoTableRow';
	import { ArtworkDetailsSort } from '.';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Select } from '$global/components/Select';
	import { TableBody } from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import type { ScrapedDataProcessDataArtistInfoPageData } from '$routes/scraped-data/process-data/artist-info/types';

	const dialogStores = createDialog();

	const {
		artworkFeeds,
		saveStage,
		showMatchEventsWarning,
		scrapedArtworksStore,
		scrapedArtistsStore,
		artistSelectedOptionsStore,
	} = getArtworkFeedsStore();

	onMount(() => {
		saveStage();
	});

	let isInProgress = $derived(
		!!$scrapedArtworksStore?.find(
			(scrapedArtwork) => !isScrapedArtworkProcessed(scrapedArtwork)
		)
	);

	let sort: ArtworkDetailsSort = $state(ArtworkDetailsSort.ScrapedArtistAsc);
	let updatedIds: string[] = $state([]);
	let artistSelectedOptions: null | Record<string, OptionType | null> =
		$state(null);
	let scrapedArtistDialogIndex = $state(-1);

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtistInfoPageData>(page.data)
	);
	let countries = $derived(data.countries);

	const scrapedArtistSortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtistsQuery['getScrapedArtists']
		>[number] & {
			artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
		},
		scrapedArtworkB: NonNullable<
			GetScrapedArtistsQuery['getScrapedArtists']
		>[number] & {
			artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
		},
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtist: NonNullable<
				GetScrapedArtistsQuery['getScrapedArtists']
			>[number] & {
				artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
			}
		) => {
			switch (sort) {
				case ArtworkDetailsSort.ScrapedArtistAsc:
				case ArtworkDetailsSort.ScrapedArtistDesc:
					return { field: scrapedArtist?.artist_text_key, type: 'string' };
				case ArtworkDetailsSort.MatchedArtistAsc:
				case ArtworkDetailsSort.MatchedArtistDesc:
					return {
						field:
							artistSelectedOptions?.[scrapedArtist?.scraped_artist_id || '']
								?.line1,
						type: 'string',
					};
				case ArtworkDetailsSort.NationalityAsc:
				case ArtworkDetailsSort.NationalityDesc:
					return {
						field: countries.find(
							(country) =>
								country.code ===
								scrapedArtist.artist_details?.nationality_country_code
						)?.country_nationality,
						type: 'string',
					};
				case ArtworkDetailsSort.YearOfBirthAsc:
				case ArtworkDetailsSort.YearOfBirthDesc:
					return {
						field: scrapedArtist.artist_details?.year_birth,
						type: 'number',
					};
				case ArtworkDetailsSort.YearOfDeathAsc:
				case ArtworkDetailsSort.YearOfDeathDesc:
					return {
						field: scrapedArtist.artist_details?.year_death,
						type: 'number',
					};
				case ArtworkDetailsSort.ReferenceIdAsc:
				case ArtworkDetailsSort.ReferenceIdDesc:
					return {
						field: scrapedArtist.artist_reference_id,
						type: 'number',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(scrapedArtworkB) as Parameters<
				typeof compareScrapedArtworkFields
			>[1],
			sort
		);
	};

	$effect(() => {
		if ($artistSelectedOptionsStore && !submitting && !artistSelectedOptions) {
			artistSelectedOptions = $artistSelectedOptionsStore;
		}
	});

	let sortedScrapedArtists = $state(
		null as
			| null
			| undefined
			| (NonNullable<GetScrapedArtistsQuery['getScrapedArtists']>[number] & {
					artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
			  })[]
	);

	$effect(() => {
		if ($scrapedArtistsStore && !submitting && !sortedScrapedArtists) {
			sortedScrapedArtists = [...$scrapedArtistsStore].sort((a, b) =>
				scrapedArtistSortFn(a, b, sort)
			);
		}
	});

	const sortScrapedArtists = (sort: ArtworkDetailsSort) => {
		sortedScrapedArtists = [...(sortedScrapedArtists || [])].sort((a, b) =>
			scrapedArtistSortFn(a, b, sort)
		);

		lastSorted = +new Date();
	};

	let lastSorted = $state(+new Date());
	let submitting = $state(false);

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataExecuted);
	};

	const handleSubmit = async () => {
		if (!sortedScrapedArtists) {
			return;
		}

		submitting = true;

		try {
			if (sortedScrapedArtists?.length) {
				if (updatedIds.length) {
					await gqlClientCustom.request(
						UpdateScrapedArtistsDocument,
						{
							input: {
								scrapedArtworkIds: $artworkFeeds.scrapedArtworkIds,
								scrapedArtists: sortedScrapedArtists.map((scrapedArtist) => {
									const { nationality, ...restArtworkDetails } =
										scrapedArtist.artist_details as typeof scrapedArtist.artist_details & {
											nationality: string;
										};

									const updatedScrapedArtist = {
										...scrapedArtist,
										artist_details: restArtworkDetails,
									};

									if (isUuidValid(`${scrapedArtist.scraped_artist_id}`)) {
										return updatedScrapedArtist;
									}

									return {
										...updatedScrapedArtist,
										scraped_artist_id: null,
									};
								}),
							},
						},
						getAuthorizationHeaders(
							page.data as { user: { access_token: string } }
						)
					);
				}

				artistSelectedOptionsStore.set(artistSelectedOptions);
				scrapedArtistsStore.set(sortedScrapedArtists);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});

				dialogStores.states.open.set(true);
			} else {
				handleContinue();
			}
		} catch {
			submitting = false;

			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		}
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const dataCyPrefix = 'scraped-data-process-artist-info';
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={2} />
			{#if !sortedScrapedArtists || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2"
						>Artists with suggested auto-matches are listed below. If there is
						no auto-match, you are required to create a new artist with the data
						in the last four columns. If the auto-match suggestion is incorrect,
						remove it to search for another artist in the database or create a
						new artist.</Txt
					>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedArtists(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<ArtistInfoTableHeaderRow dataCy={dataCyPrefix} />

					{#if artistSelectedOptions}
						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtists as _, i}
								<ArtistInfoTableRow
									index={i}
									{lastSorted}
									readOnly={!isInProgress}
									dataCy={dataCyPrefix}
									bind:scrapedArtists={sortedScrapedArtists}
									bind:scrapedArtistDialogIndex
									bind:updatedIds
									bind:artistSelectedOptions
									bind:scrapedArtist={sortedScrapedArtists[i]}
								/>
							{/each}
						</TableBody>
					{/if}
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<ArtistInfoTableHeaderRow dataCy={dataCyPrefix} />
						{#if artistSelectedOptions}
							<TableBody dataCy={dataCyPrefix}>
								{#each sortedScrapedArtists as _, i}
									<ArtistInfoTableRow
										index={i}
										{lastSorted}
										dataCy={dataCyPrefix}
										readOnly
										bind:scrapedArtists={sortedScrapedArtists}
										bind:scrapedArtistDialogIndex
										bind:updatedIds
										bind:artistSelectedOptions
										bind:scrapedArtist={sortedScrapedArtists[i]}
									/>
								{/each}
							</TableBody>
						{/if}
					</table>
				</RecordsSaveDialog>

				{#if artistSelectedOptions && scrapedArtistDialogIndex > -1}
					<ArtistArtworksDialog
						dataCy={dataCyPrefix}
						bind:scrapedArtists={sortedScrapedArtists}
						bind:updatedIds
						bind:artistSelectedOptions
						bind:scrapedArtistDialogIndex
						bind:scrapedArtist={sortedScrapedArtists[scrapedArtistDialogIndex]}
					/>
				{/if}
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={!sortedScrapedArtists ||
		sortedScrapedArtists.some(
			(sortedScrapedArtist) =>
				sortedScrapedArtist?.scraped_artist_id &&
				!artistSelectedOptions?.[sortedScrapedArtist?.scraped_artist_id] &&
				!sortedScrapedArtist?.artist_details?.name
		)}
	onSaveClick={isInProgress ? handleSubmit : handleContinue}
	visible={!!sortedScrapedArtists}
	nextLabel={isInProgress ? 'Save and continue' : 'Continue'}
	backHref={Routes.ScrapedDataProcessDataArtworkDetails}
/>
