import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetCountriesDocument } from '$lib/queries/__generated__/getCountries.generated';
import type { ScrapedDataProcessDataImagesPageServerLoadEvent } from '$routes/scraped-data/process-data/images/types';

export const processDataArtistInfoServerLoad = async ({
	parent,
}: ScrapedDataProcessDataImagesPageServerLoadEvent) => {
	const parentData = await parent();

	const countriesRes = await gqlClient.request(
		GetCountriesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const countries = countriesRes.country;
	return { countries };
};
