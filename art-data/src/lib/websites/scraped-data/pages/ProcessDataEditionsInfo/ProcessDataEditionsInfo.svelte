<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { MatchedEventsWarning } from '../../components/MatchedEventsWarning';
	import { ProcessDataTabs } from '../../components/ProcessDataTabs';
	import { RecordsSaveDialog } from '../../components/RecordsSaveDialog';
	import { ScrapedDataTabs } from '../../components/ScrapedDataTabs';
	import { compareScrapedArtworkFields } from '../../utils/compareScrapedArtworkFields/compareScrapedArtworkFields';
	import { getArtworkFeedsStore } from '../../utils/getArtworkFeedsStore/getArtworkFeedsStore';
	import { isScrapedArtworkProcessed } from '../../utils/isScrapedArtworkProcessed/isScrapedArtworkProcessed';
	import { updateArtworkDetails } from '../../utils/updateArtworkDetails/updateArtworkDetails';
	import { ArtworkDetailsSort } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Checkbox } from '$global/components/Checkbox';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableHeaderRow,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isIntegerOrLetterOrRomanNumberValid } from '$global/utils/isIntegerOrLetterOrRomanNumberValid/isIntegerOrLetterOrRomanNumberValid';
	import { Edition_Number_Type_Enum } from '$gql/types-arteye-custom';
	import { PageBody } from '$lib/components/PageBody';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { GetScrapedArtworksQuery } from '$lib/websites/scraped-data/queries/__generated__/getScrapedArtworks.generated';
	import type { ScrapedDataProcessDataArtworkDetailsPageData } from '$routes/scraped-data/process-data/artwork-details/types';

	const dialogStores = createDialog();

	interface Props {
		sort?: ArtworkDetailsSort;
		updatedIds?: string[];
	}

	let { sort = ArtworkDetailsSort.DateCreatedAsc, updatedIds = [] }: Props =
		$props();

	const handleChangeArtworkDetails = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number]
	) => {
		updatedIds = Array.from(
			new Set([...updatedIds, `${scrapedArtwork.artwork_details?.id}`])
		);
	};

	let data = $derived(
		getPageData<ScrapedDataProcessDataArtworkDetailsPageData>(page.data)
	);

	let sortedScrapedArtworks: null | GetScrapedArtworksQuery['Scraped_Artwork'] =
		$state(null);

	const {
		artworkFeeds,
		saveStage,
		showMatchEventsWarning,
		scrapedArtworksStore,
	} = getArtworkFeedsStore();

	$effect(() => {
		if ($scrapedArtworksStore && !submitting && !sortedScrapedArtworks) {
			updatedIds = [...$scrapedArtworksStore]
				.filter((artwork) => !artwork.artwork_details?.edition_number_type?.key)
				.map((artwork) => artwork?.artwork_details?.id) as string[];

			sortedScrapedArtworks = [...$scrapedArtworksStore]
				.map((artwork) => {
					const defaultEditionNumberType = (() => {
						if (!artwork.artwork_feed?.edition) {
							return 'UNDEFINED';
						}

						if (artwork.artwork_details?.artist_proof_size) {
							return Edition_Number_Type_Enum.ArtistsProof;
						}

						if (artwork.artwork_details?.house_of_commerce_size) {
							return Edition_Number_Type_Enum.HorsDeCommerce;
						}

						if (artwork.artwork_details?.general_proof_size) {
							return Edition_Number_Type_Enum.GeneralProof;
						}

						return Edition_Number_Type_Enum.Regular;
					})();

					return {
						...artwork,
						artwork_details: {
							...artwork.artwork_details,
							edition_number_type: {
								key:
									artwork.artwork_details?.edition_number_type?.key ||
									defaultEditionNumberType,
							},
						},
					} as GetScrapedArtworksQuery['Scraped_Artwork'][number];
				})
				.sort((a, b) => sortFn(a, b, sort));
		}
	});

	const sortScrapedArtworks = async (sort: ArtworkDetailsSort) => {
		sortedScrapedArtworks = [...(sortedScrapedArtworks || [])].sort((a, b) =>
			sortFn(a, b, sort)
		);
	};

	onMount(() => {
		saveStage();
	});

	const sortFn = (
		scrapedArtworkA: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		scrapedArtworkB: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		sort: ArtworkDetailsSort
	) => {
		const getTextField = (
			scrapedArtwork: NonNullable<typeof sortedScrapedArtworks>[number]
		) => {
			switch (sort) {
				case ArtworkDetailsSort.RawEditionInfoAsc:
				case ArtworkDetailsSort.RawEditionInfoDesc:
					return {
						field: scrapedArtwork.artwork_details?.edition_description,
						type: 'string',
					};
				case ArtworkDetailsSort.DateCreatedAsc:
					return {
						field: scrapedArtwork.artwork_feed?.created_at,
						type: 'date',
					};
				case ArtworkDetailsSort.EditionNumberAsc:
					return {
						field: scrapedArtwork.artwork_details?.edition_number,
						type: 'string',
					};
				case ArtworkDetailsSort.EditionNumberDesc:
					return {
						field: scrapedArtwork.artwork_details?.edition_number,
						type: 'string',
					};
				case ArtworkDetailsSort.EdSizeRegSizeAsc:
					return {
						field: scrapedArtwork.artwork_details?.regular_edition_size,
						type: 'number',
					};
				case ArtworkDetailsSort.EdSizeRegSizeDesc:
					return {
						field: scrapedArtwork.artwork_details?.regular_edition_size,
						type: 'number',
					};
				case ArtworkDetailsSort.ApSizeAsc:
					return {
						field: scrapedArtwork.artwork_details?.artist_proof_size,
						type: 'number',
					};
				case ArtworkDetailsSort.ApSizeDesc:
					return {
						field: scrapedArtwork.artwork_details?.artist_proof_size,
						type: 'number',
					};
				case ArtworkDetailsSort.HcSizeAsc:
					return {
						field: scrapedArtwork.artwork_details?.house_of_commerce_size,
						type: 'number',
					};
				case ArtworkDetailsSort.HcSizeDesc:
					return {
						field: scrapedArtwork.artwork_details?.house_of_commerce_size,
						type: 'number',
					};
				case ArtworkDetailsSort.GpSizeAsc:
					return {
						field: scrapedArtwork.artwork_details?.general_proof_size,
						type: 'number',
					};
				case ArtworkDetailsSort.GpSizeDesc:
					return {
						field: scrapedArtwork.artwork_details?.general_proof_size,
						type: 'number',
					};
				case ArtworkDetailsSort.EdSizeTotalAsc:
					return {
						field: scrapedArtwork.artwork_details?.total_edition_size,
						type: 'number',
					};
				case ArtworkDetailsSort.EdSizeTotalDesc:
					return {
						field: scrapedArtwork.artwork_details?.total_edition_size,
						type: 'number',
					};
			}
		};

		return compareScrapedArtworkFields(
			getTextField(scrapedArtworkA) as Parameters<
				typeof compareScrapedArtworkFields
			>[0],
			getTextField(scrapedArtworkB) as Parameters<
				typeof compareScrapedArtworkFields
			>[1],
			sort
		);
	};

	const handleValueChange = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		e:
			| {
					target:
						| (EventTarget & { value?: string | undefined; name?: string })
						| null;
			  }
			| undefined,
		fieldName: string,
		number: boolean
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1 && e?.target?.value !== undefined) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			const newValue =
				e?.target.value === ''
					? null
					: number
						? +(e?.target.value as string)
						: e?.target.value;

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: newValue,
				},
			};
		}
	};

	let submitting = $state(false);

	const handleSubmit = async () => {
		if (!sortedScrapedArtworks) {
			return;
		}

		submitting = true;

		try {
			if (updatedIds.length) {
				const newScrapedArtworksStore = await updateArtworkDetails(
					updatedIds,
					sortedScrapedArtworks,
					(
						artworkDetails: NonNullable<
							typeof sortedScrapedArtworks
						>[number]['artwork_details']
					) => ({
						edition_number_type: artworkDetails?.edition_number_type?.key,
						edition_number: artworkDetails?.edition_number,
						edition_number_legacy: artworkDetails?.edition_number_legacy,
						regular_edition_size: artworkDetails?.regular_edition_size,
						artist_proof_size: artworkDetails?.artist_proof_size,
						house_of_commerce_size: artworkDetails?.house_of_commerce_size,
						general_proof_size: artworkDetails?.general_proof_size,
						total_edition_size: artworkDetails?.total_edition_size,
						open_edition: artworkDetails?.open_edition,
						edition_is_numbered: artworkDetails?.edition_is_numbered,
						edition_is_unknown: artworkDetails?.edition_is_unknown,
						series_size: artworkDetails?.series_size,
					}),
					getAuthorizationHeaders(data)
				);

				scrapedArtworksStore.set(newScrapedArtworksStore);

				showToast({
					variant: 'success',
					message: 'The update was successful',
				});
			}

			dialogStores.states.open.set(true);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong during the update.',
			});
		} finally {
			submitting = false;
		}
	};

	const handleContinue = () => {
		goto(Routes.ScrapedDataProcessDataActivityListingInfo);
	};

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Scraped Data', href: Routes.ScrapedData },
		{ label: 'Process Data' },
	];

	const headers = [
		'ID',
		'Description',
		'Scrapped Edition Text',
		'Ed type and no.',
		'Raw Edition info',
		'Ed size reg',
		'AP Size',
		'HC Size',
		'GP Size',
		'Ed size total',
		'Unlimited',
		'Numbered',
		'Unknown',
		'Series size',
	];

	const dataCyPrefix = 'scraped-data-process-editions-info';

	const handleChangeBoolean = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		bool: boolean | string,
		fieldName: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					[fieldName]: Boolean(bool),
				},
			};
		}
	};

	const editionNumberTypeOptions = Object.values([
		{
			label: 'AP',
			value: Edition_Number_Type_Enum.ArtistsProof,
		},
		{
			label: 'GP',
			value: Edition_Number_Type_Enum.GeneralProof,
		},
		{
			label: 'HC',
			value: Edition_Number_Type_Enum.HorsDeCommerce,
		},
		{
			label: 'Regular',
			value: Edition_Number_Type_Enum.Regular,
		},
		{
			label: 'Unknown',
			value: Edition_Number_Type_Enum.Unknown,
		},
		{
			label: 'Undefined',
			value: 'UNDEFINED',
		},
	]);

	const handleEditionTypeChange = (
		scrapedArtwork: NonNullable<
			GetScrapedArtworksQuery['Scraped_Artwork']
		>[number],
		editionNumberType: string
	) => {
		if (!sortedScrapedArtworks) {
			return;
		}

		const updatedArtworkIndex = sortedScrapedArtworks.findIndex(
			(artwork) =>
				artwork?.artwork_details?.id === scrapedArtwork.artwork_details?.id
		);

		if (updatedArtworkIndex !== -1) {
			sortedScrapedArtworks = [...sortedScrapedArtworks];

			sortedScrapedArtworks[updatedArtworkIndex] = {
				...sortedScrapedArtworks[updatedArtworkIndex],
				artwork_details: {
					...sortedScrapedArtworks[updatedArtworkIndex].artwork_details,
					id: sortedScrapedArtworks[updatedArtworkIndex]?.artwork_details
						?.id as string,
					edition_number_type: {
						key: editionNumberType as Edition_Number_Type_Enum,
					},
				},
			};
		}
	};
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-none"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-none">
		<div class="mb-6">
			<ScrapedDataTabs activeTab={2} />
			<ProcessDataTabs activeTab={5} />
			{#if !sortedScrapedArtworks || $showMatchEventsWarning === null}
				<CircularProgress dataCy={dataCyPrefix} />
			{:else if $showMatchEventsWarning}
				<MatchedEventsWarning />
			{:else}
				<div
					class="mb-4 mt-[-0.5rem] flex w-full items-center justify-between gap-4"
				>
					<Txt variant="body2">Write instructions about this stage here.</Txt>
					<Select
						bind:value={sort}
						onchange={(e) => {
							sortScrapedArtworks(e.detail.value as ArtworkDetailsSort);
						}}
						ariaLabel="Sort entries"
						name="sort"
						class="[&>button>p]:uppercase"
						dataCy={`${dataCyPrefix}-sort`}
						selectedPrefix="SORT BY:"
						options={Object.values(ArtworkDetailsSort).map((sort) => ({
							label: sort,
							value: sort,
						}))}
						classes={{
							placeholder: 'font-medium tracking-widest',
							menu: 'z-50',
						}}
					/>
				</div>

				<table
					class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
				>
					<TableHeaderRow dataCy={dataCyPrefix}>
						{#each headers as header, i}
							<TableHeader
								dataCy={dataCyPrefix}
								class={twMerge(
									classNames({
										'w-[136px]': i === 0,
										// 'w-[140px]': i === 1,
										'w-[200px]': i === 3,
										// 'w-[75px]': [4, 5, 6, 7, 8].includes(i),
										// 'w-[90px]': [9, 10, 11].includes(i),
									}),
									'border'
								)}
							>
								{header}
							</TableHeader>
						{/each}
					</TableHeaderRow>

					<TableBody dataCy={dataCyPrefix}>
						{#each sortedScrapedArtworks as scrapedArtwork, i}
							{@const isInProgress = !isScrapedArtworkProcessed(scrapedArtwork)}
							<TableRow index={i} dataCy={dataCyPrefix}>
								<TableCell dataCy={dataCyPrefix}>
									<a
										target="_blank"
										rel="noopener noreferrer"
										href={getImageUrl(
											scrapedArtwork?.images?.[0]?.directus_files_id?.id
										)}
										class="mb-1 flex h-[110px] w-[110px] items-center justify-center bg-gray-200 p-2"
									>
										<img
											alt=""
											class="max-h-full max-w-full"
											src={getImageUrl(
												scrapedArtwork?.images?.[0]?.directus_files_id?.id
											)}
										/>
									</a>

									<LinkButton
										dataCy={dataCyPrefix}
										href={scrapedArtwork.artwork_feed?.url}
										disabled={!scrapedArtwork.artwork_feed?.url}
										newTab
										variant="secondary"
										size="sm"
										fullWidth
										>{scrapedArtwork.artwork_feed?.url ? 'Source' : 'No URL'}
										{#snippet trailing()}
											{#if scrapedArtwork.artwork_feed?.url}
												<ExternalIcon />
											{/if}
										{/snippet}</LinkButton
									>
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="max-w-60 whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.description || ''}
										</Txt>
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap textVariant="body3">
									{#snippet custom()}
										<Txt variant="body3" class="max-w-60 whitespace-pre-line">
											{scrapedArtwork?.artwork_details?.edition_description ||
												''}
										</Txt>
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix} wrap>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<InputWithSelect
													size="sm"
													dataCy={`${dataCyPrefix}-edition-type-and-number`}
													name="edition_type_and_name"
													error={(() => {
														const artwork = sortedScrapedArtworks?.find(
															(artwork) =>
																artwork?.artwork_details?.id ===
																scrapedArtwork.artwork_details?.id
														);

														return artwork?.artwork_details?.edition_number &&
															!isIntegerOrLetterOrRomanNumberValid(
																artwork?.artwork_details?.edition_number
															)
															? 'Invalid ed no.'
															: '';
													})()}
													onkeyup={(e?: Event) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'edition_number',
															false
														);
													}}
													onchange={(e?: Event) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'edition_number',
															false
														);
													}}
													selectValue={scrapedArtwork?.artwork_details
														?.edition_number_type?.key || ''}
													inputValue={scrapedArtwork?.artwork_details
														?.edition_number || ''}
													options={editionNumberTypeOptions}
													placeholder="0"
													classes={{
														options: 'z-[60]',
														selectText: 'w-10 truncate overflow-hidden',
														wrapper:
															'[&>div]:min-w-[120px] [&>div>button]:w-full [&>div>button>p]:min-w-[calc(100%-20px)] [&>div>button>p]:text-left',
													}}
													onSelectChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleEditionTypeChange(scrapedArtwork, e?.value);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{[
														scrapedArtwork?.artwork_details?.edition_number_type
															?.key
															? editionNumberTypeOptions?.find(
																	({ value }) =>
																		value ===
																		scrapedArtwork?.artwork_details
																			?.edition_number_type?.key
																)?.label
															: '',
														scrapedArtwork?.artwork_details?.edition_number ||
															'',
													]
														.filter(Boolean)
														.join(' ')}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell wrap dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													size="sm"
													dataCy={dataCyPrefix}
													name="edition_number_legacy"
													rows={5}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'edition_number_legacy',
															false
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'edition_number_legacy',
															false
														);
													}}
													value={scrapedArtwork.artwork_details
														.edition_number_legacy}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.edition_number_legacy}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="regular_edition_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'regular_edition_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'regular_edition_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.regular_edition_size === null
															? ''
															: scrapedArtwork.artwork_details
																	.regular_edition_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details
														.regular_edition_size === null
														? ''
														: scrapedArtwork.artwork_details
																.regular_edition_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="artist_proof_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'artist_proof_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'artist_proof_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details.artist_proof_size ===
														null
															? ''
															: scrapedArtwork.artwork_details.artist_proof_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.artist_proof_size ===
													null
														? ''
														: scrapedArtwork.artwork_details.artist_proof_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="house_of_commerce_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'house_of_commerce_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'house_of_commerce_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.house_of_commerce_size === null
															? ''
															: scrapedArtwork.artwork_details
																	.house_of_commerce_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details
														.house_of_commerce_size === null
														? ''
														: scrapedArtwork.artwork_details
																.house_of_commerce_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="general_proof_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'general_proof_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'general_proof_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.general_proof_size === null
															? ''
															: scrapedArtwork.artwork_details
																	.general_proof_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.general_proof_size ===
													null
														? ''
														: scrapedArtwork.artwork_details.general_proof_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="total_edition_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'total_edition_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'total_edition_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details
															.total_edition_size === null
															? ''
															: scrapedArtwork.artwork_details
																	.total_edition_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.total_edition_size ===
													null
														? ''
														: scrapedArtwork.artwork_details.total_edition_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-open-edition`}
													checked={scrapedArtwork.artwork_details
														.open_edition || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															'open_edition'
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.open_edition
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-edition-is-numbered`}
													checked={scrapedArtwork.artwork_details
														?.edition_is_numbered || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															'edition_is_numbered'
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details?.edition_is_numbered
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Checkbox
													dataCy={`${dataCyPrefix}-edition-is-unknown`}
													checked={scrapedArtwork.artwork_details
														?.edition_is_unknown || false}
													onChange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleChangeBoolean(
															scrapedArtwork,
															e,
															'edition_is_unknown'
														);
													}}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details?.edition_is_unknown
														? 'Yes'
														: 'No'}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										{#if scrapedArtwork.artwork_details}
											{#if isInProgress}
												<Input
													dataCy={dataCyPrefix}
													name="series_size"
													onkeydown={handleKeyDownNumbersOnly}
													onkeyup={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'series_size',
															true
														);
													}}
													onchange={(e) => {
														handleChangeArtworkDetails(scrapedArtwork);
														handleValueChange(
															scrapedArtwork,
															e,
															'series_size',
															true
														);
													}}
													value={`${
														scrapedArtwork.artwork_details.series_size === null
															? ''
															: scrapedArtwork.artwork_details.series_size
													}`}
												/>
											{:else}
												<Txt variant="body3">
													{scrapedArtwork.artwork_details.series_size === null
														? ''
														: scrapedArtwork.artwork_details.series_size}
												</Txt>
											{/if}
										{/if}
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>

				<RecordsSaveDialog {dialogStores} onContinue={handleContinue}>
					<table
						class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white"
					>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									class={twMerge(
										classNames({
											'w-[136px]': i === 0,
											// 'w-[140px]': i === 1,
											'w-[200px]': i === 2,
											// 'w-[90px]': [4, 5, 6, 7, 8, 9, 10].includes(i),
										}),
										'border'
									)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>

						<TableBody dataCy={dataCyPrefix}>
							{#each sortedScrapedArtworks as artwork, i}
								<TableRow index={i} dataCy={dataCyPrefix}>
									<TableCell dataCy={dataCyPrefix}>
										<LinkButton
											dataCy={dataCyPrefix}
											href={artwork?.artwork_feed?.url}
											newTab
											variant="secondary"
											size="sm"
											class="max-w-[140px]"
											fullWidth
											>Source{#snippet trailing()}
												<ExternalIcon />
											{/snippet}</LinkButton
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										{#snippet custom()}
											<Txt variant="body3" class="whitespace-pre-line">
												{artwork?.artwork_details?.description || ''}
											</Txt>
										{/snippet}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3" class="whitespace-pre-line"
											>{artwork?.artwork_details?.edition_description ||
												''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix} textVariant="body3">
										{[
											artwork?.artwork_details?.edition_number_type?.key
												? editionNumberTypeOptions?.find(
														({ value }) =>
															value ===
															artwork?.artwork_details?.edition_number_type?.key
													)?.label
												: '',
											artwork?.artwork_details?.edition_number || '',
										]
											.filter(Boolean)
											.join(' ')}
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.edition_number_legacy ||
												''}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.regular_edition_size === null
												? ''
												: artwork?.artwork_details?.regular_edition_size}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.artist_proof_size === null
												? ''
												: artwork?.artwork_details?.artist_proof_size}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.house_of_commerce_size ===
											null
												? ''
												: artwork?.artwork_details?.house_of_commerce_size}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.general_proof_size === null
												? ''
												: artwork?.artwork_details?.general_proof_size}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.total_edition_size === null
												? ''
												: artwork?.artwork_details?.total_edition_size}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.open_edition
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.edition_is_numbered
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.edition_is_unknown
												? 'Yes'
												: 'No'}</Txt
										>
									</TableCell>
									<TableCell dataCy={dataCyPrefix}>
										<Txt variant="body3"
											>{artwork?.artwork_details?.series_size === null
												? ''
												: artwork?.artwork_details?.series_size}</Txt
										>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</RecordsSaveDialog>
			{/if}
		</div>
	</Container>
</PageBody>

<PageSaveBar
	loading={submitting}
	disabled={!!sortedScrapedArtworks?.some(
		(scrapedArtwork) =>
			scrapedArtwork?.artwork_details?.edition_number &&
			!isIntegerOrLetterOrRomanNumberValid(
				scrapedArtwork?.artwork_details?.edition_number
			)
	)}
	onSaveClick={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? handleContinue
		: handleSubmit}
	visible={!!sortedScrapedArtworks?.length}
	nextLabel={isScrapedArtworkProcessed(sortedScrapedArtworks?.[0])
		? 'Continue'
		: 'Save and continue'}
	backHref={Routes.ScrapedDataProcessDataImages}
/>
