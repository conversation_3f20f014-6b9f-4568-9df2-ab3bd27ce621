import { gql } from 'graphql-tag';

export const GET_SCRAPED_EVENTS = gql`
	query getScrapedEvents($input: GetScrapedArtworksInput!) {
		getScrapedArtworks(input: $input) {
			unsentScrapedEvents {
				id
				processed_event_id
				art_event_feed {
					starts_at
					starts_at_tz
					ends_at
					ends_at_tz
					data_source
					url
					event_type {
						key
					}
					external_id
					description
					title
					sale_number
					created_at
					is_charity_fundraiser
					location
					pdf_url
					organization
					id
					processor_review_event {
						id
						processed_event_id
					}
					artlogic_link {
						url
					}
					artwork_feed {
						url
						price_currency {
							code
							name
							symbol
						}
					}
					crawl_job
					created_by {
						first_name
						last_name
					}
					image_url
					is_closed
					organization
					title
					updated_at
					updated_by {
						first_name
						last_name
					}
				}
			}
			scrapedEvents {
				id
				processed_event_id
				art_event_feed {
					starts_at
					starts_at_tz
					ends_at
					ends_at_tz
					data_source
					url
					event_type {
						key
					}
					external_id
					description
					title
					sale_number
					created_at
					is_charity_fundraiser
					location
					pdf_url
					organization
					id
					processor_review_event {
						id
						processed_event_id
					}
					artlogic_link {
						url
					}
					artwork_feed {
						url
					}
					crawl_job
					created_by {
						first_name
						last_name
					}
					image_url
					is_closed
					organization
					title
					updated_at
					updated_by {
						first_name
						last_name
					}
				}
			}
		}
	}
`;
