import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type UserTokenQueryVariables = Types.Exact<{ [key: string]: never }>;

export type UserTokenQuery = {
	__typename?: 'Query';
	userToken?: { __typename?: 'UserTokenResponse'; token: string } | null;
};

export const UserTokenDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'userToken' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'userToken' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'token' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<UserTokenQuery, UserTokenQueryVariables>;
