import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type UpdateScrapedArtistsMutationVariables = Types.Exact<{
	input?: Types.InputMaybe<Types.UpdateScrapedArtistInput>;
}>;

export type UpdateScrapedArtistsMutation = {
	__typename?: 'Mutation';
	updateScrapedArtists?: Array<{
		__typename?: 'UpdateScrapedArtistsResponseItem';
		scraped_artist_id?: string | null;
	} | null> | null;
};

export const UpdateScrapedArtistsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateScrapedArtists' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'UpdateScrapedArtistInput' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'updateScrapedArtists' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'scraped_artist_id' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateScrapedArtistsMutation,
	UpdateScrapedArtistsMutationVariables
>;
