import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type MatchProvenanceMutationVariables = Types.Exact<{
	ids: Array<Types.Scalars['ID']['input']> | Types.Scalars['ID']['input'];
}>;

export type MatchProvenanceMutation = {
	__typename?: 'Mutation';
	matchProvenance: Array<{
		__typename?: 'MatchProvenanceResult';
		processed_activity_id?: string | null;
		provenance_matched_activity_id?: string | null;
		scraped_artwork_id: string;
		provenance_matched_artwork_id?: string | null;
	} | null>;
};

export const MatchProvenanceDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'matchProvenance' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: {
								kind: 'NonNullType',
								type: {
									kind: 'NamedType',
									name: { kind: 'Name', value: 'ID' },
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'matchProvenance' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_activity_id' },
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'provenance_matched_activity_id',
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'scraped_artwork_id' },
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'provenance_matched_artwork_id',
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	MatchProvenanceMutation,
	MatchProvenanceMutationVariables
>;
