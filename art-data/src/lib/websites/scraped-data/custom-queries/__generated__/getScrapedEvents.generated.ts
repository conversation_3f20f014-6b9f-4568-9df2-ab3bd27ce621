import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type GetScrapedEventsQueryVariables = Types.Exact<{
	input: Types.GetScrapedArtworksInput;
}>;

export type GetScrapedEventsQuery = {
	__typename?: 'Query';
	getScrapedArtworks: {
		__typename?: 'GetScrapedArtworksResponse';
		unsentScrapedEvents: Array<{
			__typename?: 'Scraped_Event';
			id?: string | null;
			processed_event_id?: string | null;
			art_event_feed?: {
				__typename?: 'art_event_feed';
				starts_at?: any | null;
				starts_at_tz?: string | null;
				ends_at?: any | null;
				ends_at_tz?: string | null;
				data_source?: string | null;
				url?: string | null;
				external_id?: string | null;
				description?: string | null;
				title?: string | null;
				sale_number?: string | null;
				created_at?: any | null;
				is_charity_fundraiser?: boolean | null;
				location?: string | null;
				pdf_url?: string | null;
				organization?: string | null;
				id?: string | null;
				crawl_job?: string | null;
				image_url?: string | null;
				is_closed?: boolean | null;
				updated_at?: any | null;
				event_type?: {
					__typename?: 'art_event_type_lookup';
					key?: Types.Art_Event_Type_Lookup_Enum | null;
				} | null;
				processor_review_event?: Array<{
					__typename?: 'Scraped_Event';
					id?: string | null;
					processed_event_id?: string | null;
				} | null> | null;
				artlogic_link?: Array<{
					__typename?: 'Artlogic_Link';
					url?: string | null;
				} | null> | null;
				artwork_feed?: Array<{
					__typename?: 'artwork_feed';
					url?: string | null;
					price_currency?: {
						__typename?: 'currency';
						code?: string | null;
						name?: string | null;
						symbol?: string | null;
					} | null;
				} | null> | null;
				created_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
				updated_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
			} | null;
		}>;
		scrapedEvents: Array<{
			__typename?: 'Scraped_Event';
			id?: string | null;
			processed_event_id?: string | null;
			art_event_feed?: {
				__typename?: 'art_event_feed';
				starts_at?: any | null;
				starts_at_tz?: string | null;
				ends_at?: any | null;
				ends_at_tz?: string | null;
				data_source?: string | null;
				url?: string | null;
				external_id?: string | null;
				description?: string | null;
				title?: string | null;
				sale_number?: string | null;
				created_at?: any | null;
				is_charity_fundraiser?: boolean | null;
				location?: string | null;
				pdf_url?: string | null;
				organization?: string | null;
				id?: string | null;
				crawl_job?: string | null;
				image_url?: string | null;
				is_closed?: boolean | null;
				updated_at?: any | null;
				event_type?: {
					__typename?: 'art_event_type_lookup';
					key?: Types.Art_Event_Type_Lookup_Enum | null;
				} | null;
				processor_review_event?: Array<{
					__typename?: 'Scraped_Event';
					id?: string | null;
					processed_event_id?: string | null;
				} | null> | null;
				artlogic_link?: Array<{
					__typename?: 'Artlogic_Link';
					url?: string | null;
				} | null> | null;
				artwork_feed?: Array<{
					__typename?: 'artwork_feed';
					url?: string | null;
				} | null> | null;
				created_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
				updated_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
			} | null;
		}>;
	};
};

export const GetScrapedEventsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getScrapedEvents' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'GetScrapedArtworksInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'getScrapedArtworks' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'unsentScrapedEvents' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_event_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'art_event_feed' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'starts_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'starts_at_tz' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'ends_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'ends_at_tz' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'data_source' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'event_type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'external_id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'created_at' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'is_charity_fundraiser',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'pdf_url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organization' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'processor_review_event',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processed_event_id',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artlogic_link' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'url' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artwork_feed' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'url' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'price_currency',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'symbol',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'crawl_job' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'created_by' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image_url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'is_closed' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organization' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'updated_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'updated_by' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'scrapedEvents' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_event_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'art_event_feed' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'starts_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'starts_at_tz' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'ends_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'ends_at_tz' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'data_source' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'event_type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'external_id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'created_at' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'is_charity_fundraiser',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'pdf_url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organization' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'processor_review_event',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processed_event_id',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artlogic_link' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'url' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artwork_feed' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'url' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'crawl_job' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'created_by' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image_url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'is_closed' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organization' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'updated_at' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'updated_by' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetScrapedEventsQuery,
	GetScrapedEventsQueryVariables
>;
