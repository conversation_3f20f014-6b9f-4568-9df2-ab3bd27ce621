import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type GetScrapedArtistsQueryVariables = Types.Exact<{
	input?: Types.InputMaybe<Types.GetScrapedArtistsInput>;
}>;

export type GetScrapedArtistsQuery = {
	__typename?: 'Query';
	getScrapedArtists?: Array<{
		__typename?: 'GetScrapedArtistsResponseItem';
		artist_reference_id?: string | null;
		artist_text_key: string;
		data_source?: string | null;
		processed_artist_id?: string | null;
		scraped_artist_id?: string | null;
		artist_details?: {
			__typename?: 'ArtistDetails';
			name?: string | null;
			nationality_country_code?: string | null;
			year_birth?: number | null;
			year_death?: number | null;
		} | null;
	} | null> | null;
};

export const GetScrapedArtistsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getScrapedArtists' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'GetScrapedArtistsInput' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'getScrapedArtists' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_reference_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_text_key' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'data_source' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_artist_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'scraped_artist_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_details' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'nationality_country_code',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetScrapedArtistsQuery,
	GetScrapedArtistsQueryVariables
>;
