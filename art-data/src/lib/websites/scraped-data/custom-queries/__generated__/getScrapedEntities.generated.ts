import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type GetScrapedEntitiesQueryVariables = Types.Exact<{
	input: Types.GetScrapedEntitiesInput;
}>;

export type GetScrapedEntitiesQuery = {
	__typename?: 'Query';
	getScrapedEntities: Array<{
		__typename?: 'GetScrapedEntitiesResponseItem';
		id: string;
		processed_entity_id?: string | null;
		processed_reference_id?: string | null;
		associated_entity_feed?: {
			__typename?: 'associated_entity_feed';
			name?: string | null;
		} | null;
	}>;
};

export const GetScrapedEntitiesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getScrapedEntities' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'GetScrapedEntitiesInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'getScrapedEntities' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associated_entity_feed' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_entity_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_reference_id' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetScrapedEntitiesQuery,
	GetScrapedEntitiesQueryVariables
>;
