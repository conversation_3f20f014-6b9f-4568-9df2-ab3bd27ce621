import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type IngestArtworksMutationVariables = Types.Exact<{
	input: Types.IngestArtworksInput;
}>;

export type IngestArtworksMutation = {
	__typename?: 'Mutation';
	ingestArtworks: {
		__typename?: 'IngestArtworksResponse';
		batch_id: string;
		status: Types.BatchStatus;
		total_artworks: number;
		total_unique_artists: number;
	};
};

export const IngestArtworksDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'ingestArtworks' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'IngestArtworksInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'ingestArtworks' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'batch_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'status' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'total_artworks' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'total_unique_artists' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	IngestArtworksMutation,
	IngestArtworksMutationVariables
>;
