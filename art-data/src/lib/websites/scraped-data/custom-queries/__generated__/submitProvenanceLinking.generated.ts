import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type SubmitProvenanceLinkingMutationVariables = Types.Exact<{
	ids: Array<Types.Scalars['ID']['input']> | Types.Scalars['ID']['input'];
}>;

export type SubmitProvenanceLinkingMutation = {
	__typename?: 'Mutation';
	submitProvenanceLinking: {
		__typename?: 'ActivityTransferResponse';
		activitiesTransferred: Array<{
			__typename?: 'ActivityTransfer';
			activity: string;
			from_artwork: string;
			to_artwork: string;
		}>;
	};
};

export const SubmitProvenanceLinkingDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'submitProvenanceLinking' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: {
								kind: 'NonNullType',
								type: {
									kind: 'NamedType',
									name: { kind: 'Name', value: 'ID' },
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'submitProvenanceLinking' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activitiesTransferred' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'activity' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'to_artwork' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	SubmitProvenanceLinkingMutation,
	SubmitProvenanceLinkingMutationVariables
>;
