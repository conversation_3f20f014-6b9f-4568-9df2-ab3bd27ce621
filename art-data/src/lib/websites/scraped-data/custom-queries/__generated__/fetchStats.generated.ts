import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type FetchStatsQueryVariables = Types.Exact<{
	input: Types.FetchStatsInput;
}>;

export type FetchStatsQuery = {
	__typename?: 'Query';
	fetchStats: {
		__typename?: 'FetchStatsResponse';
		stats: Array<{
			__typename?: 'StatRow';
			changed?: number | null;
			changedPercentage?: number | null;
			field?: string | null;
			reviewed?: number | null;
		}>;
	};
};

export const FetchStatsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'fetchStats' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'FetchStatsInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'fetchStats' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'stats' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'changed' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'changedPercentage' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'field' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reviewed' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<FetchStatsQuery, FetchStatsQueryVariables>;
