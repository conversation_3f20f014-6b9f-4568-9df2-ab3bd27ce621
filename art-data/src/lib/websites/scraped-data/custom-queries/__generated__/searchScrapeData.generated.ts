import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type SearchScrapeDataQueryVariables = Types.Exact<{
	input: Types.SearchScrapeDataInput;
}>;

export type SearchScrapeDataQuery = {
	__typename?: 'Query';
	searchScrapeData?: {
		__typename?: 'SearchScrapeDataResponse';
		total: number;
		data?: Array<{
			__typename?: 'SearchScrapeData';
			artist?: string | null;
			count?: number | null;
			datasource?: string | null;
			dateScraped?: any | null;
			dealer?: string | null;
			saleDate?: any | null;
			saleName?: string | null;
			type?: Types.ScrapedDataType | null;
			userAssigned?: string | null;
			artworkFeedIds?: string | null;
		} | null> | null;
	} | null;
};

export const SearchScrapeDataDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'searchScrapeData' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'SearchScrapeDataInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'searchScrapeData' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'count' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'datasource' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dateScraped' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dealer' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'saleDate' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'saleName' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'userAssigned' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworkFeedIds' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'total' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	SearchScrapeDataQuery,
	SearchScrapeDataQueryVariables
>;
