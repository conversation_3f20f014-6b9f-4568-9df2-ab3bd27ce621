import { gql } from 'graphql-tag';

export const GET_SCRAPED_ARTWORKS = gql`
	query getScrapedArtworks($filter: Scraped_Artwork_filter) {
		Scraped_Artwork(filter: $filter, limit: -1) {
			created_artwork_id
			processed_activity_id
			provenance_matched_activity_id
			provenance_matched_artwork_id
			extracted_provenance_lines
			id
			status {
				key
				name
			}
			images {
				directus_files_id {
					id
					width
					height
					filename_disk
				}
			}
			artwork_details {
				id
				date_created
				artists {
					name
				}
				edition_number_legacy
				artist_text
				description
				title
				media
				crid
				executed_year_start
				executed_year_end
				series_size
				dimensions
				dimensions_width_cm
				dimensions_depth_cm
				dimensions_height_cm
				dimension_type {
					key
					name
				}
				number_of_pieces
				number_of_artworks
				edition_description
				edition_number
				edition_is_unknown
				regular_edition_size
				house_of_commerce_size
				artist_proof_size
				total_edition_size
				general_proof_size
				open_edition
				edition_is_numbered
				estimate_low
				estimate_high
				price
				sale_status
				sale_date
				sale_date_tz
				edition_number_type {
					key
					name
				}
				currency {
					code
					name
				}
				price_includes_premium
				is_full_set
				is_bundle
				open_edition
				is_bundle
				lot_number
				lot_attributes {
					artwork_lot_symbol_lookup_key {
						key
					}
				}
			}
			artwork_feed {
				id
				cr_number
				url
				created_at
				artist
				year_made
				artist_clean_name
				lot_number
				edition
				price
				primary_image {
					id
					width
					height
					filename_disk
				}
				extra_images {
					file_id {
						id
						width
						height
						filename_disk
					}
				}
				event_id {
					event_type {
						processor_type
					}
				}
			}
			user_updated {
				id
				first_name
				last_name
			}
		}
	}
`;
