import { gql } from 'graphql-tag';

export const GET_ARTWORK_FEED = gql`
	query getArtworkFeed(
		$filter: artwork_feed_filter
		$limit: Int
		$sort: [String]
	) {
		artwork_feed(filter: $filter, limit: $limit, sort: $sort) {
			archived
			auction_records
			artist_year_born
			artist_year_died
			lot_condition
			crawl_job
			created_by {
				first_name
				last_name
			}
			updated_at
			updated_by {
				first_name
				last_name
			}
			sale_status {
				key
			}
			created_at
			data_source
			description
			dimensions
			dimensions_uom {
				name
			}
			dimensions_depth
			dimensions_height
			dimensions_width
			edition
			edition_number
			edition_size
			edition_sqn
			estimate
			estimate_currency {
				code
				name
				symbol
			}
			event_id {
				id
				url
				data_source
				starts_at
				starts_at_tz
				ends_at
				ends_at_tz
				is_closed
				sale_number
				title
				description
				is_charity_fundraiser
				organization
				location
				image_url
				pdf_url
				created_at
				created_by {
					first_name
					last_name
				}
				updated_at
				updated_by {
					first_name
					last_name
				}
				event_type {
					key
				}
				processor_review_event {
					id
					processed_event_id
				}
			}
			estimate_high
			estimate_low
			event_external_id
			event_session_sqn
			exhibited
			external_id
			extra_image_urls
			id
			image_url
			literature
			lot_number
			mediums
			price
			price_amount
			price_currency {
				code
				name
				symbol
			}
			lot_symbols {
				lot_symbol {
					key
				}
			}
			price_includes_bp
			primary_image {
				id
				width
				height
				filename_disk
			}
			provenance
			sale_ends_at
			sale_ends_at_tz
			price_amount
			sale_starts_at
			sale_starts_at_tz
			saleroom_notice
			lot_shipping
			lot_lead
			title
			url
			year_made
			year_made_from
			year_made_to
			artist
			artist_clean_name
			artist_nationality {
				country_nationality
			}
			processor_review_artwork {
				id
				status {
					key
					name
				}
				artwork_details {
					user_updated {
						id
						first_name
						last_name
					}
				}
			}
			cr_number
			lot_lead
			lot_description
			inscriptions
			inscription_position
			inscription_date
			lot_title
			lot_shipping
		}
	}
`;
