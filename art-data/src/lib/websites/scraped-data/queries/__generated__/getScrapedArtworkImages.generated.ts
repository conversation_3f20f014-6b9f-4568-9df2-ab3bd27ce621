import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetScrapedArtworkImagesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Scraped_Artwork_Filter>;
}>;

export type GetScrapedArtworkImagesQuery = {
	__typename?: 'Query';
	Scraped_Artwork: Array<{
		__typename?: 'Scraped_Artwork';
		artwork_details?: {
			__typename?: 'Artwork_Details';
			title?: string | null;
		} | null;
		artwork_feed?: {
			__typename?: 'artwork_feed';
			primary_image?: { __typename?: 'directus_files'; id: string } | null;
		} | null;
	}>;
};

export const GetScrapedArtworkImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getScrapedArtworkImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Scraped_Artwork_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Scraped_Artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_details' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_feed' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'primary_image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetScrapedArtworkImagesQuery,
	GetScrapedArtworkImagesQueryVariables
>;
