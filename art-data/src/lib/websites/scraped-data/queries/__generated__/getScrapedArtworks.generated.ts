import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetScrapedArtworksQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Scraped_Artwork_Filter>;
}>;

export type GetScrapedArtworksQuery = {
	__typename?: 'Query';
	Scraped_Artwork: Array<{
		__typename?: 'Scraped_Artwork';
		created_artwork_id?: string | null;
		processed_activity_id?: string | null;
		provenance_matched_activity_id?: string | null;
		provenance_matched_artwork_id?: string | null;
		extracted_provenance_lines?: any | null;
		id: string;
		status?: {
			__typename?: 'Artwork_Status_Type';
			key: string;
			name: string;
		} | null;
		images?: Array<{
			__typename?: 'Scraped_Artwork_files';
			directus_files_id?: {
				__typename?: 'directus_files';
				id: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
			} | null;
		} | null> | null;
		artwork_details?: {
			__typename?: 'Artwork_Details';
			id: string;
			date_created?: any | null;
			edition_number_legacy?: string | null;
			artist_text?: string | null;
			description?: string | null;
			title?: string | null;
			media?: string | null;
			crid?: string | null;
			executed_year_start?: number | null;
			executed_year_end?: number | null;
			series_size?: number | null;
			dimensions?: string | null;
			dimensions_width_cm?: number | null;
			dimensions_depth_cm?: number | null;
			dimensions_height_cm?: number | null;
			number_of_pieces?: number | null;
			number_of_artworks?: number | null;
			edition_description?: string | null;
			edition_number?: string | null;
			edition_is_unknown?: boolean | null;
			regular_edition_size?: number | null;
			house_of_commerce_size?: number | null;
			artist_proof_size?: number | null;
			total_edition_size?: number | null;
			general_proof_size?: number | null;
			open_edition?: boolean | null;
			edition_is_numbered?: boolean | null;
			estimate_low?: number | null;
			estimate_high?: number | null;
			price?: number | null;
			sale_status?: string | null;
			sale_date?: any | null;
			sale_date_tz?: string | null;
			price_includes_premium?: boolean | null;
			is_full_set?: boolean | null;
			is_bundle?: boolean | null;
			lot_number?: string | null;
			artists?: Array<{
				__typename?: 'Artist_Details';
				name?: string | null;
			} | null> | null;
			dimension_type?: {
				__typename?: 'Artwork_Dimension_Type';
				key: string;
				name?: string | null;
			} | null;
			edition_number_type?: {
				__typename?: 'Edition_Number_Type';
				key: string;
				name?: string | null;
			} | null;
			currency?: { __typename?: 'currency'; code: string; name: string } | null;
			lot_attributes?: Array<{
				__typename?: 'Artwork_Details_artwork_lot_symbol_lookup';
				artwork_lot_symbol_lookup_key?: {
					__typename?: 'artwork_lot_symbol_lookup';
					key: string;
				} | null;
			} | null> | null;
		} | null;
		artwork_feed?: {
			__typename?: 'artwork_feed';
			id: string;
			cr_number?: string | null;
			url: string;
			created_at?: any | null;
			artist?: string | null;
			year_made?: string | null;
			artist_clean_name?: string | null;
			lot_number?: string | null;
			edition?: string | null;
			price?: string | null;
			primary_image?: {
				__typename?: 'directus_files';
				id: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
			} | null;
			extra_images?: Array<{
				__typename?: 'artwork_feed_files';
				file_id?: {
					__typename?: 'directus_files';
					id: string;
					width?: number | null;
					height?: number | null;
					filename_disk?: string | null;
				} | null;
			} | null> | null;
			event_id?: {
				__typename?: 'art_event_feed';
				event_type?: {
					__typename?: 'art_event_type_lookup';
					processor_type: string;
				} | null;
			} | null;
		} | null;
		user_updated?: {
			__typename?: 'directus_users';
			id?: string | null;
			first_name?: string | null;
			last_name?: string | null;
		} | null;
	}>;
};

export const GetScrapedArtworksDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getScrapedArtworks' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Scraped_Artwork_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Scraped_Artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '-1' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'created_artwork_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_activity_id' },
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'provenance_matched_activity_id',
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'provenance_matched_artwork_id',
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'extracted_provenance_lines' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_details' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_number_legacy' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_text' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'media' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'executed_year_start' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'executed_year_end' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'series_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dimensions' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dimensions_width_cm' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dimensions_depth_cm' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dimensions_height_cm' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dimension_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'number_of_pieces' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'number_of_artworks' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_description' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_is_unknown' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'regular_edition_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'house_of_commerce_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_proof_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'total_edition_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'general_proof_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'open_edition' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_is_numbered' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'estimate_low' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'estimate_high' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'price' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_status' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_date_tz' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_number_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_includes_premium' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_full_set' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_bundle' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'open_edition' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_bundle' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_attributes' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'artwork_lot_symbol_lookup_key',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_feed' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'cr_number' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'url' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'created_at' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_made' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_clean_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'price' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'primary_image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'extra_images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'file_id' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'event_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'event_type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'processor_type',
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetScrapedArtworksQuery,
	GetScrapedArtworksQueryVariables
>;
