import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetArtworkFeedQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Feed_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetArtworkFeedQuery = {
	__typename?: 'Query';
	artwork_feed: Array<{
		__typename?: 'artwork_feed';
		archived: boolean;
		auction_records?: any | null;
		artist_year_born?: number | null;
		artist_year_died?: number | null;
		lot_condition?: string | null;
		crawl_job?: string | null;
		updated_at?: any | null;
		created_at?: any | null;
		data_source?: string | null;
		description?: string | null;
		dimensions?: string | null;
		dimensions_depth?: number | null;
		dimensions_height?: number | null;
		dimensions_width?: number | null;
		edition?: string | null;
		edition_number?: string | null;
		edition_size?: number | null;
		edition_sqn?: number | null;
		estimate?: string | null;
		estimate_high?: number | null;
		estimate_low?: number | null;
		event_external_id?: string | null;
		event_session_sqn?: number | null;
		exhibited?: string | null;
		external_id?: string | null;
		extra_image_urls?: any | null;
		id: string;
		image_url?: string | null;
		literature?: string | null;
		lot_number?: string | null;
		mediums?: string | null;
		price?: string | null;
		price_amount?: number | null;
		price_includes_bp?: boolean | null;
		provenance?: string | null;
		sale_ends_at?: any | null;
		sale_ends_at_tz?: string | null;
		sale_starts_at?: any | null;
		sale_starts_at_tz?: string | null;
		saleroom_notice?: string | null;
		lot_shipping?: string | null;
		lot_lead?: string | null;
		title?: string | null;
		url: string;
		year_made?: string | null;
		year_made_from?: number | null;
		year_made_to?: number | null;
		artist?: string | null;
		artist_clean_name?: string | null;
		cr_number?: string | null;
		lot_description?: string | null;
		inscriptions?: string | null;
		inscription_position?: string | null;
		inscription_date?: string | null;
		lot_title?: string | null;
		created_by?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		updated_by?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		sale_status?: {
			__typename?: 'artwork_sale_status_lookup';
			key: string;
		} | null;
		dimensions_uom?: { __typename?: 'uom'; name?: string | null } | null;
		estimate_currency?: {
			__typename?: 'currency';
			code: string;
			name: string;
			symbol?: string | null;
		} | null;
		event_id?: {
			__typename?: 'art_event_feed';
			id: string;
			url: string;
			data_source: string;
			starts_at?: any | null;
			starts_at_tz?: string | null;
			ends_at?: any | null;
			ends_at_tz?: string | null;
			is_closed?: boolean | null;
			sale_number?: string | null;
			title?: string | null;
			description?: string | null;
			is_charity_fundraiser?: boolean | null;
			organization?: string | null;
			location?: string | null;
			image_url?: string | null;
			pdf_url?: string | null;
			created_at?: any | null;
			updated_at?: any | null;
			created_by?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			updated_by?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			event_type?: { __typename?: 'art_event_type_lookup'; key: string } | null;
			processor_review_event?: Array<{
				__typename?: 'Scraped_Event';
				id: string;
				processed_event_id?: string | null;
			} | null> | null;
		} | null;
		price_currency?: {
			__typename?: 'currency';
			code: string;
			name: string;
			symbol?: string | null;
		} | null;
		lot_symbols?: Array<{
			__typename?: 'artwork_artwork_lot_symbol_link';
			lot_symbol?: {
				__typename?: 'artwork_lot_symbol_lookup';
				key: string;
			} | null;
		} | null> | null;
		primary_image?: {
			__typename?: 'directus_files';
			id: string;
			width?: number | null;
			height?: number | null;
			filename_disk?: string | null;
		} | null;
		artist_nationality?: {
			__typename?: 'country';
			country_nationality?: string | null;
		} | null;
		processor_review_artwork?: Array<{
			__typename?: 'Scraped_Artwork';
			id: string;
			status?: {
				__typename?: 'Artwork_Status_Type';
				key: string;
				name: string;
			} | null;
			artwork_details?: {
				__typename?: 'Artwork_Details';
				user_updated?: {
					__typename?: 'directus_users';
					id?: string | null;
					first_name?: string | null;
					last_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
	}>;
};

export const GetArtworkFeedDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkFeed' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_feed_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_feed' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'archived' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_records' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_year_born' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_year_died' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_condition' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crawl_job' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'created_by' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'updated_by' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'data_source' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'dimensions' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_uom' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_depth' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_height' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_width' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'edition' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_size' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'edition_sqn' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'estimate' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'estimate_currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'event_id' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'url' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'data_source' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'starts_at' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'starts_at_tz' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'ends_at' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'ends_at_tz' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_closed' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_number' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_charity_fundraiser' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organization' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_url' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'pdf_url' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'created_at' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'created_by' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'updated_at' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'updated_by' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'event_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processor_review_event' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'processed_event_id',
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'estimate_high' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'estimate_low' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'event_external_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'event_session_sqn' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'exhibited' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'external_id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'extra_image_urls' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'image_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'literature' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_number' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'mediums' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'price' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_amount' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_symbols' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_symbol' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_includes_bp' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'primary_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'provenance' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_ends_at' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_ends_at_tz' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_amount' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_starts_at' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_starts_at_tz' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'saleroom_notice' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_shipping' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_lead' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'year_made' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'year_made_from' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'year_made_to' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'artist' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_clean_name' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_nationality' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country_nationality' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processor_review_artwork' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_details' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'user_updated' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'cr_number' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_lead' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_description' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'inscriptions' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'inscription_position' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'inscription_date' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_shipping' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetArtworkFeedQuery, GetArtworkFeedQueryVariables>;
