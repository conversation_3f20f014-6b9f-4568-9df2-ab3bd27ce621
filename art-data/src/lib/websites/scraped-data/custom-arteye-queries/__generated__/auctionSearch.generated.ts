import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye-custom';

export type AuctionSearchQueryVariables = Types.Exact<{
	input: Types.AuctionSearchInput;
}>;

export type AuctionSearchQuery = {
	__typename?: 'Query';
	auctionSearch: {
		__typename?: 'AuctionSearchResponse';
		data: Array<{
			__typename?: 'auction';
			id?: string | null;
			sale_name?: string | null;
			local_auction_start_date?: any | null;
			local_auction_end_date?: any | null;
			sale_number?: string | null;
			auction_house?: {
				__typename?: 'auction_house';
				organisation?: {
					__typename?: 'organisation';
					name?: string | null;
				} | null;
			} | null;
			currency?: {
				__typename?: 'currency';
				name?: string | null;
				code?: string | null;
				symbol?: string | null;
			} | null;
		}>;
	};
};

export const AuctionSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'auctionSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'AuctionSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auctionSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_name' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'local_auction_start_date',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_auction_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_house' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'symbol' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<AuctionSearchQuery, AuctionSearchQueryVariables>;
