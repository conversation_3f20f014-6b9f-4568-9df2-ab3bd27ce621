import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye-custom';

export type ExhibitionSearchQueryVariables = Types.Exact<{
	input: Types.ExhibitionSearchInput;
}>;

export type ExhibitionSearchQuery = {
	__typename?: 'Query';
	exhibitionSearch: {
		__typename?: 'ExhibitionSearchResponse';
		data: Array<{
			__typename?: 'exhibition';
			id?: string | null;
			local_start_date?: any | null;
			local_end_date?: any | null;
			exhibition_url?: string | null;
			title?: string | null;
			description?: string | null;
			venue_address_1?: string | null;
			venue_address_2?: string | null;
			venue_address_3?: string | null;
			venue_post_code?: string | null;
			aggregations?: Array<{
				__typename?: 'exhibition_aggregation';
				artwork_listing_count?: number | null;
			} | null> | null;
			cover_image?: {
				__typename?: 'directus_files';
				id: string;
				title?: string | null;
				type?: string | null;
				storage?: string | null;
				filename_download?: string | null;
				filename_disk?: string | null;
			} | null;
			venue_country?: {
				__typename?: 'location';
				short_code?: string | null;
				code?: string | null;
				name?: string | null;
			} | null;
			venue_city?: {
				__typename?: 'location';
				name?: string | null;
				code?: string | null;
				short_code?: string | null;
				country?: {
					__typename?: 'location';
					code?: string | null;
					name?: string | null;
					short_code?: string | null;
				} | null;
			} | null;
			organisers?: Array<{
				__typename?: 'exhibition_organisers';
				id?: string | null;
				entity_id?: {
					__typename?: 'entity';
					id?: string | null;
					name?: string | null;
					type?: {
						__typename?: 'entity_type';
						key?: Types.Entity_Type_Enum | null;
					} | null;
					artist?: { __typename?: 'artist'; id?: string | null } | null;
					organisation?: {
						__typename?: 'organisation';
						id?: string | null;
						name?: string | null;
					} | null;
					person?: { __typename?: 'person'; id?: string | null } | null;
					addresses?: Array<{
						__typename?: 'entity_address';
						city?: { __typename?: 'location'; name?: string | null } | null;
						country?: { __typename?: 'location'; name?: string | null } | null;
					} | null> | null;
				} | null;
			} | null> | null;
			attributes?: Array<{
				__typename?: 'exhibition_attribute';
				id?: string | null;
				type?: {
					__typename?: 'exhibition_attribute_type';
					key?: Types.Exhibition_Attribute_Type_Enum | null;
					name?: string | null;
				} | null;
				exhibition?: { __typename?: 'exhibition'; id?: string | null } | null;
			} | null> | null;
		}>;
	};
};

export const ExhibitionSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'exhibitionSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'ExhibitionSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'exhibitionSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition_url' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'aggregations' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'artwork_listing_count',
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'cover_image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_address_1' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_address_2' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_address_3' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_post_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_city' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisers' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity_id' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artist' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'person' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'addresses' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'city' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'attributes' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'exhibition' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	ExhibitionSearchQuery,
	ExhibitionSearchQueryVariables
>;
