import { gql } from 'graphql-tag';

export const QUERY = gql`
	query organisationSearch($input: OrganisationSearchInput!) {
		organisationSearch(input: $input) {
			data {
				name
				type {
					organisation_type_key {
						name
						key
					}
				}
				id
				location {
					code
					country_nationality
					name
					short_code

					country {
						code
						country_nationality
						name
						short_code
					}
				}
				entity {
					aggregations {
						activity_count
					}
					name
				}
			}
		}

		organisationSearchCount(input: $input)
	}
`;
