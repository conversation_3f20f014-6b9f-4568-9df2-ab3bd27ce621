import { gql } from 'graphql-tag';

export const QUERY = gql`
	query exhibitionSearch($input: ExhibitionSearchInput!) {
		exhibitionSearch(input: $input) {
			data {
				id
				local_start_date
				local_end_date
				exhibition_url
				title
				description

				aggregations {
					artwork_listing_count
				}

				cover_image {
					id
					title
					type
					storage
					filename_download
					filename_disk
				}
				venue_address_1
				venue_address_2
				venue_address_3
				venue_post_code
				venue_country {
					short_code
					code
					name
				}
				venue_city {
					name
					code
					short_code
					country {
						code
						name
						short_code
					}
				}
				organisers {
					id
					entity_id {
						id
						name
						type {
							key
						}
						artist {
							id
						}
						organisation {
							id
							name
						}
						person {
							id
						}
						addresses {
							city {
								name
							}
							country {
								name
							}
						}
					}
				}
				attributes {
					id
					type {
						key
						name
					}
					exhibition {
						id
					}
				}
			}
		}
	}
`;
