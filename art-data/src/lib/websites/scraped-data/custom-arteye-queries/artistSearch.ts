import { gql } from 'graphql-tag';

export const QUERY = gql`
	query artistSearch($input: ArtistSearchInput!) {
		artistSearch(input: $input) {
			data {
				id
				reference_id
				person {
					year_birth
					year_death
					entity {
						name
					}
					nationalities {
						country {
							code
							country_nationality
							name
							short_code
						}
					}
					type {
						person_type_key {
							key
							name
						}
					}
				}
				aggregations {
					artwork_count
					id
				}
			}
		}
	}
`;
