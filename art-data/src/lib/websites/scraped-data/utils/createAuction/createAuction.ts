import {
	CreateAuctionItemDocument,
	type CreateAuctionItemMutationVariables,
} from '../../arteye-queries/__generated__/createAuction.generated';
import { GetAuctionHouseDocument } from '../../arteye-queries/__generated__/getAuctionHouse.generated';
import { page } from '$app/state';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
import { getUTCDayDate } from '$global/utils/getUTCDayDate/getUTCDayDate';
import { gqlClientArteye } from '$lib/gqlClientArteye';

export const createAuction = async ({
	auctionDetailsForm,
	headers,
}: {
	auctionDetailsForm: CreateAuctionItemMutationVariables['data'] & {
		auction_start_date_date?: string | null | undefined;
		auction_start_date_time?: string | null | undefined;
		auction_end_date_date?: string | null | undefined;
		auction_end_date_time?: string | null | undefined;
	};
	headers: {
		Authorization: string;
	};
}) => {
	const auctionHouseRes = await gqlClientArteye.request(
		GetAuctionHouseDocument,
		{
			filter: {
				_and: [
					{
						organisation: {
							id: { _eq: auctionDetailsForm.auction_house?.organisation?.id },
						},
					},
					{ status: { key: { _neq: 'archived' } } },
				],
			},
		},
		getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})
	);

	const auctionHouse = auctionHouseRes?.auction_house?.[0];

	const {
		auction_end_date,
		auction_end_date_time,
		auction_end_date_date,
		auction_start_date,
		auction_start_date_time,
		auction_start_date_date,
		...restAuctionDetailsForm
	} = auctionDetailsForm;

	const createAuctionRes = await gqlClientArteye.request(
		CreateAuctionItemDocument,
		{
			data: {
				...restAuctionDetailsForm,
				auction_house: {
					...(auctionHouse && {
						id: auctionHouse.id,
					}),
					...auctionDetailsForm.auction_house,
				},
				...(auctionDetailsForm.auction_start_date_date && {
					local_auction_start_date: `${auctionDetailsForm.auction_start_date_date}T${auctionDetailsForm.auction_start_date_time || '00:00'}:00Z`,
				}),
				...(auctionDetailsForm.auction_end_date_date && {
					local_auction_end_date: `${auctionDetailsForm.auction_end_date_date}T${auctionDetailsForm.auction_end_date_time || '00:00'}:00Z`,
				}),
			},
		},
		headers
	);

	return createAuctionRes?.create_auction_item;
};
