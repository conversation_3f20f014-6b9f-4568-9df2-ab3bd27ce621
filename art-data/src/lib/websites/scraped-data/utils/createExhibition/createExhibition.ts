import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { CreateExhibitionItemDocument } from '../../arteye-queries/__generated__/createExhibition.generated';
import type { CreateExhibitionItemMutationVariables } from '../../arteye-queries/__generated__/createExhibition.generated';
import type { ExhibitionImageType } from '../../pages/MatchEvents/ExhibitionForm/CreateNewExhibition/CreateNewExhibition.svelte';
import type { OrganiserType } from '../../pages/MatchEvents/ExhibitionForm/CreateNewExhibition/ExhibitionOrganisers/ExhibitionOrganisersRow/ExhibitionOrganisersRow.svelte';
import { page } from '$app/state';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

dayjs.extend(customParseFormat);

export const createExhibition = async ({
	exhibitionDetailsForm,
	exhibitionImage,
	headers,
}: {
	exhibitionDetailsForm: Omit<
		CreateExhibitionItemMutationVariables['data'],
		'organisers'
	> & {
		organisers?: OrganiserType[];
	};
	exhibitionImage: ExhibitionImageType;
	headers: {
		Authorization: string;
	};
}) => {
	const exhibitionImagePayload = await (async () => {
		if (exhibitionImage) {
			const { url, file, ...restProfileImage } = exhibitionImage;

			if (!file) {
				return restProfileImage;
			}

			const uploadFileResponse = await uploadFile(file, headers);
			return uploadFileResponse;
		}

		return null;
	})();

	const { start_date, end_date, ...restExhibitionDetailsForm } =
		exhibitionDetailsForm;

	const organisers = (exhibitionDetailsForm.organisers || [])
		.filter((organiser) => organiser?.isDeleted !== true)
		.map((organiser) => {
			return {
				entity_id: {
					id: organiser?.id,
					name: organiser?.name,
				},
			};
		});

	const createExhibitionReq = await gqlClientArteye.request(
		CreateExhibitionItemDocument,
		{
			data: {
				...restExhibitionDetailsForm,
				...(start_date && {
					local_start_date: `${start_date}T12:00:00Z`,
				}),
				...(end_date && {
					local_end_date: `${end_date}T12:00:00Z`,
				}),
				organisers,
				attributes: exhibitionDetailsForm.attributes?.map((type) => {
					return {
						id: type?.id,
						type: type?.type,
					};
				}),
				cover_image: exhibitionImagePayload,
				title: exhibitionDetailsForm.title || 'Default Title',
				...(exhibitionDetailsForm.venue_city && {
					venue_city: {
						name: `${exhibitionDetailsForm.venue_city.name}`,
						short_code: `${exhibitionDetailsForm.venue_city.short_code}`,
						code: `${exhibitionDetailsForm.venue_city.code}`,
					},
				}),
				...(exhibitionDetailsForm.venue_country && {
					venue_country: {
						name: `${exhibitionDetailsForm.venue_country.name}`,
						short_code: `${exhibitionDetailsForm.venue_country.short_code}`,
						code: `${exhibitionDetailsForm.venue_country.code}`,
					},
				}),
			},
		},
		getAuthorizationHeaders({
			user: { access_token: page.data.user.arteye_token },
		})
	);

	return createExhibitionReq?.create_exhibition_item;
};
