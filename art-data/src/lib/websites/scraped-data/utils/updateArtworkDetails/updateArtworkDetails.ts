import type { GetScrapedArtworksQuery } from '../../queries/__generated__/getScrapedArtworks.generated';
import {
	UpdateArtworkDetailsDocument,
	type UpdateArtworkDetailsMutationVariables,
} from '$lib/features/final-review/queries/__generated__/updateArtworkDetails.generated';
import { gqlClient } from '$lib/gqlClient';

export const updateArtworkDetails = async (
	updatedIds: string[],
	sortedScrapedArtworks: GetScrapedArtworksQuery['Scraped_Artwork'],
	getData: (
		artworkDetails: GetScrapedArtworksQuery['Scraped_Artwork'][number]['artwork_details']
	) => UpdateArtworkDetailsMutationVariables['data'],
	headers: { Authorization: string }
) => {
	const newSortedScrapedArtworks = [...sortedScrapedArtworks];

	const updateArtworkDetailsPromises = updatedIds.map((updatedId) => {
		const correspondingArtworkDetails = (
			sortedScrapedArtworks as NonNullable<typeof sortedScrapedArtworks>
		).find(
			(scrapedArtwork) => scrapedArtwork?.artwork_details?.id === updatedId
		);

		return gqlClient.request(
			UpdateArtworkDetailsDocument,
			{
				id: updatedId,
				data: getData(correspondingArtworkDetails?.artwork_details),
			},
			headers
		);
	});

	const updatedArtworkDetails = await Promise.all(updateArtworkDetailsPromises);

	updatedArtworkDetails.forEach((updatedArtworkDetail) => {
		const updatedArtworkDetailIndex = newSortedScrapedArtworks.findIndex(
			(newSortedScrapedArtwork) =>
				newSortedScrapedArtwork?.artwork_details?.id ===
				updatedArtworkDetail?.update_Artwork_Details_item?.id
		);

		newSortedScrapedArtworks[updatedArtworkDetailIndex].artwork_details =
			updatedArtworkDetail?.update_Artwork_Details_item;
	});

	return newSortedScrapedArtworks;
};
