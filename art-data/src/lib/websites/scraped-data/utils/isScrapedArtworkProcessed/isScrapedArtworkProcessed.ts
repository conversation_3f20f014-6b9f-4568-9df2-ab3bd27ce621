import { ARTWORK_IN_PROGRESS_STATUSES } from '$lib/constants/artwork-in-progress-statuses';

export const isScrapedArtworkProcessed = (
	scrapedArtwork:
		| {
				status?: {
					__typename?: 'Artwork_Status_Type';
					key?: string | null;
					name?: string | null;
				} | null;
		  }
		| null
		| undefined
) => {
	return ARTWORK_IN_PROGRESS_STATUSES.includes(
		scrapedArtwork?.status?.key as string
	);
};
