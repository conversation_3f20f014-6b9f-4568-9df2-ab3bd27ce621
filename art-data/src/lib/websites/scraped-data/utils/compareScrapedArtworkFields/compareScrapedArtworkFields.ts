import dayjs from 'dayjs';

export const compareScrapedArtworkFields = (
	scrapedArtworkA: null | {
		field: string | number | null | undefined;
		type: 'date' | 'number' | 'string';
	},
	scrapedArtworkB: null | { field: string; type: 'date' | 'number' | 'string' },
	sort: string
) => {
	if (
		[null, undefined].includes(scrapedArtworkA as null | undefined) ||
		[null, undefined].includes(scrapedArtworkB as null | undefined)
	) {
		return 0;
	}

	const getOperand = (
		field: string | number | null | undefined,
		type: 'date' | 'number' | 'string'
	) => {
		if (type === 'date') {
			return dayjs(field).unix();
		} else if (type === 'number' && field !== undefined && field !== null) {
			return +field;
		} else {
			return (field as string | null | undefined)?.toLowerCase();
		}
	};

	const aOperand = getOperand(
		scrapedArtworkA?.field,
		(scrapedArtworkA as NonNullable<typeof scrapedArtworkA>)?.type
	);
	const bOperand = getOperand(
		scrapedArtworkB?.field,
		(scrapedArtworkA as NonNullable<typeof scrapedArtworkB>)?.type
	);

	if (
		[null, undefined].includes(aOperand as null | undefined) &&
		[null, undefined].includes(bOperand as null | undefined)
	) {
		return 0;
	}

	if ([null, undefined].includes(aOperand as null | undefined)) {
		return sort.endsWith('ASC') ||
			sort.endsWith(' N') ||
			sort.endsWith('(A-Z)') ||
			sort === 'DEFAULT'
			? -1
			: 1;
	}

	if ([null, undefined].includes(bOperand as null | undefined)) {
		return sort.endsWith('ASC') ||
			sort.endsWith(' N') ||
			sort.endsWith('(A-Z)') ||
			sort === 'DEFAULT'
			? 1
			: -1;
	}

	if (
		(aOperand as NonNullable<typeof aOperand>) <
		(bOperand as NonNullable<typeof bOperand>)
	) {
		return sort.endsWith('ASC') ||
			sort.endsWith(' N') ||
			sort.endsWith('(A-Z)') ||
			sort === 'DEFAULT'
			? -1
			: 1;
	}
	if (
		(aOperand as NonNullable<typeof aOperand>) >
		(bOperand as NonNullable<typeof bOperand>)
	) {
		return sort.endsWith('ASC') ||
			sort.endsWith(' N') ||
			sort.endsWith('(A-Z)') ||
			sort === 'DEFAULT'
			? 1
			: -1;
	}

	return 0;
};
