import { writable, get } from 'svelte/store';
import { ProcessDataImageCarouselType } from '../../pages/ProcessDataImages/ProcessDataImagesTableRow/ProcessDataImageCarousel';
import type { ImageCarouselImage } from '$global/components/ImageCarousel';
import { getImageDimensionsFromUrl } from '$global/utils/getImageDimensionsFromUrl/getImageDimensionsFromUrl';
import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
import { getImageAlt } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ArtworksTextTableRow';
import { getImageId } from '$lib/features/view-all-images/utils/getInitialVariables/getImageId';
import type { ArtworkImages } from '$lib/types';

export const createProcessDataImagesContext = () => {
	const artworkImages = writable<ArtworkImages[]>([]);
	const artworkMainImages = writable<ArtworkImages[]>([]);

	const isRowInvalid = (index: number) =>
		!get(artworkMainImages)[index].images.length &&
		get(artworkImages)[index].images.length;

	const replaceArtworkImages = (
		newArtworkImages: ArtworkImages,
		index: number
	) => {
		artworkImages.set([
			...get(artworkImages).slice(0, index),
			newArtworkImages,
			...get(artworkImages).slice(index + 1, get(artworkImages).length),
		]);
	};

	const replaceArtworkMainImage = (
		newArtworkMainImages: ArtworkImages,
		index: number
	) => {
		artworkMainImages.set([
			...get(artworkMainImages).slice(0, index),
			newArtworkMainImages,
			...get(artworkMainImages).slice(index + 1, get(artworkMainImages).length),
		]);
	};

	const handleDragFromExternalSource = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		toList: string
	) => {
		const [toListIndex, toListType] = toList.split('-');
		const toArtworkImages = get(
			toListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+toListIndex];

		const imageToAdd = {
			...movedImage,
			id: `${getImageId(movedImage.id)}_${+new Date()}`,
		};

		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, imageToAdd);
		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
		};

		if (toListType === ProcessDataImageCarouselType.Other) {
			replaceArtworkImages(updatedToArtworkImages, +toListIndex);
		} else {
			replaceArtworkMainImage(updatedToArtworkImages, +toListIndex);
		}
	};

	const handleMoveImage = (
		movedImage: ImageCarouselImage,
		oldIndex: number,
		newIndex: number,
		list: string
	) => {
		const [listIndex, listType] = list.split('-');
		const images = get(
			listType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+listIndex];

		const newImages = [...images.images];
		newImages.splice(oldIndex, 1);
		newImages.splice(newIndex, 0, movedImage);

		const storeToUpdate =
			listType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages;

		storeToUpdate.set([
			...get(storeToUpdate).slice(0, +listIndex),
			{ images: newImages, page: undefined },
			...get(storeToUpdate).slice(+listIndex + 1),
		]);
	};

	// Could be simplified in order to only empty the from list and populate the destination list
	// But this implementation can support multiple images in the same list if required
	const handleDragImage = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		fromList: string,
		toList: string
	) => {
		const [fromListIndex, fromListType] = fromList.split('-');
		const fromArtworkImages = get(
			fromListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+fromListIndex];

		const newFromImages = [
			...fromArtworkImages.images.filter((image) => image.id !== movedImage.id),
		];

		const updatedFromArtworkImages = {
			...fromArtworkImages,
			images: [...newFromImages],
		};

		const [toListIndex, toListType] = toList.split('-');
		const toArtworkImages = get(
			toListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+toListIndex];

		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, movedImage);

		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
		};

		if (toListType === fromListType) {
			const storeToUpdate =
				toListType === ProcessDataImageCarouselType.Other
					? artworkImages
					: artworkMainImages;

			if (+fromListIndex < +toListIndex) {
				storeToUpdate.set([
					...get(storeToUpdate).slice(0, +fromListIndex),
					updatedFromArtworkImages,
					...get(storeToUpdate).slice(+fromListIndex + 1, +toListIndex),
					updatedToArtworkImages,
					...get(storeToUpdate).slice(
						+toListIndex + 1,
						get(storeToUpdate).length
					),
				]);
			} else {
				storeToUpdate.set([
					...get(storeToUpdate).slice(0, +toListIndex),
					updatedToArtworkImages,
					...get(storeToUpdate).slice(+toListIndex + 1, +fromListIndex),
					updatedFromArtworkImages,
					...get(storeToUpdate).slice(
						+fromListIndex + 1,
						get(storeToUpdate).length
					),
				]);
			}
		} else {
			if (fromListType === ProcessDataImageCarouselType.Other) {
				replaceArtworkImages(updatedFromArtworkImages, +fromListIndex);
			} else {
				replaceArtworkMainImage(updatedFromArtworkImages, +fromListIndex);
			}

			if (toListType === ProcessDataImageCarouselType.Other) {
				replaceArtworkImages(updatedToArtworkImages, +toListIndex);
			} else {
				replaceArtworkMainImage(updatedToArtworkImages, +toListIndex);
			}
		}
	};

	const handleDeleteImage = (fromList: string, image: ImageCarouselImage) => {
		const [fromListIndex, fromListType] = fromList.split('-');
		const existingArtworkImages = get(
			fromListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+fromListIndex];

		const newImages = existingArtworkImages.images.filter(
			({ id }) => id !== image['id']
		);

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: newImages,
		};

		if (fromListType === ProcessDataImageCarouselType.Other) {
			replaceArtworkImages(updatedArtworkImages, +fromListIndex);
		} else {
			replaceArtworkMainImage(updatedArtworkImages, +fromListIndex);
		}
	};

	const handleLinkImage = async (
		toList: string,
		url: string,
		filename_disk: string
	) => {
		const { width, height } = await getImageDimensionsFromUrl(url);

		const [toListIndex, toListType] = toList.split('-');
		const newImage = {
			id: `${crypto.randomUUID()}-0000`,
			filename_disk,
			alt: getImageAlt(0),
			url,
			width,
			height,
		};

		const existingArtworkImages = get(
			toListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+toListIndex];

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [
				...existingArtworkImages.images,
				newImage,
			] as ArtworkImages['images'],
		};

		if (toListType === ProcessDataImageCarouselType.Other) {
			replaceArtworkImages(updatedArtworkImages, +toListIndex);
		} else {
			replaceArtworkMainImage(updatedArtworkImages, +toListIndex);
		}
	};

	const handleSubmittedFiles = async (toList: string, files: File[]) => {
		const [toListIndex, toListType] = toList.split('-');
		const newImages = (
			await Promise.all(
				files.map(async (file: File) => imageFileToBase64(file))
			)
		).map((newImage, i) => ({
			...newImage,
			id: `${crypto.randomUUID()}-0000`,
			filename_disk: newImage.file.name,
			alt: getImageAlt(i),
		}));

		const existingArtworkImages = get(
			toListType === ProcessDataImageCarouselType.Other
				? artworkImages
				: artworkMainImages
		)[+toListIndex];

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [
				...existingArtworkImages.images,
				...newImages,
			] as ArtworkImages['images'],
		};

		if (toListType === ProcessDataImageCarouselType.Other) {
			replaceArtworkImages(updatedArtworkImages, +toListIndex);
		} else {
			replaceArtworkMainImage(updatedArtworkImages, +toListIndex);
		}
	};

	return {
		handleLinkImage,
		handleMoveImage,
		handleSubmittedFiles,
		artworkImages,
		artworkMainImages,
		handleDragFromExternalSource,
		handleDragImage,
		handleDeleteImage,
		replaceArtworkImages,
		isRowInvalid,
	};
};
