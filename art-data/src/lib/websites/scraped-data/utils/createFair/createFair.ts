import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { CreateFairItemDocument } from '../../arteye-queries/__generated__/createFair.generated';
import type { CreateFairItemMutationVariables } from '../../arteye-queries/__generated__/createFair.generated';
import { gqlClientArteye } from '$lib/gqlClientArteye';

dayjs.extend(customParseFormat);

export const createFair = async ({
	fairDetailsForm,
	headers,
}: {
	fairDetailsForm: CreateFairItemMutationVariables['data'];
	headers: {
		Authorization: string;
	};
}) => {
	const { start_date, end_date, ...restFairDetailsForm } = fairDetailsForm;

	const createFairReq = await gqlClientArteye.request(
		CreateFairItemDocument,
		{
			data: {
				...restFairDetailsForm,
				...(start_date && { local_start_date: `${start_date}T12:00:00Z` }),
				...(end_date && { local_end_date: `${end_date}T12:00:00Z` }),
				...(fairDetailsForm.venue_city && {
					venue_city: {
						name: `${fairDetailsForm.venue_city.name}`,
						short_code: `${fairDetailsForm.venue_city.short_code}`,
						code: `${fairDetailsForm.venue_city.code}`,
					},
				}),
				...(fairDetailsForm.venue_country && {
					venue_country: {
						name: `${fairDetailsForm.venue_country.name}`,
						short_code: `${fairDetailsForm.venue_country.short_code}`,
						code: `${fairDetailsForm.venue_country.code}`,
					},
				}),
			},
		},
		headers
	);

	return createFairReq?.create_fair_item;
};
