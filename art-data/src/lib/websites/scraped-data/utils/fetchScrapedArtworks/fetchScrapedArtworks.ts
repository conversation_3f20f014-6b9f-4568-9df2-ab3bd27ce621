import { GetScrapedArtworksDocument } from '../../queries/__generated__/getScrapedArtworks.generated';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';

export const fetchScrapedArtworks = async (
	parentData: {
		user: { access_token: string } | null;
	},
	ids: string[]
) => {
	const scrapedArtworksRes = await gqlClient.request(
		GetScrapedArtworksDocument,
		{ filter: { id: { _in: ids } } },
		getAuthorizationHeaders(parentData)
	);

	const scrapedArtworks = scrapedArtworksRes?.Scraped_Artwork;

	return scrapedArtworks;
};
