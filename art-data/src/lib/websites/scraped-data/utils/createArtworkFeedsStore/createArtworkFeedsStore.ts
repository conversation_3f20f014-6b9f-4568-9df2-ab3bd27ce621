import { get, writable, type Writable } from 'svelte/store';
import { persisted } from 'svelte-persisted-store';
import type { GetArtistsQuery } from '../../arteye-queries/__generated__/getArtists.generated';
import type { GetScrapedArtistsQuery } from '../../custom-queries/__generated__/getScrapedArtists.generated';
import type { GetScrapedEntitiesQuery } from '../../custom-queries/__generated__/getScrapedEntities.generated';
import { GetScrapedEventsDocument } from '../../custom-queries/__generated__/getScrapedEvents.generated';
import type { fetchScrapedArtworks } from '../fetchScrapedArtworks/fetchScrapedArtworks';
import { page } from '$app/state';
import { type Option as OptionType } from '$global/components/QueryAutocomplete';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { LocalStorageKeys } from '$lib/constants/local-storage-keys';
import { gqlClientCustom } from '$lib/gqlClientCustom';

export type ArtworkFeeds = {
	unprocessedIds: string[];
	scrapedArtworkIds: string[];
	currentIds: string[];
	submittingArtworks: boolean;
	stage: string;
};

export type ArtworkFeedsStore = {
	checkEventsAreMatches: () => Promise<void>;
	showMatchEventsWarning: Writable<boolean | null>;
	artworkFeeds: Writable<ArtworkFeeds>;
	scrapedArtworksStore: Writable<Awaited<
		ReturnType<typeof fetchScrapedArtworks>
	> | null>;
	saveStage: () => void;
	setUnprocessedIds: (unprocessedIds: string[]) => void;
	setCurrentIds: (currentIds: string[], scrapedArtworkIds: string[]) => void;
	resetCurrentIds: (currentIds: string[], scrapedArtworkIds: string[]) => void;
	setSubmittingArtworks: (submittinArtworks: boolean) => void;
	scrapedArtistsStore: Writable<
		| null
		| (NonNullable<GetScrapedArtistsQuery['getScrapedArtists']>[number] & {
				artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
		  })[]
	>;
	scrapedEntitiesStore: Writable<
		null | GetScrapedEntitiesQuery['getScrapedEntities']
	>;
	artistSelectedOptionsStore: Writable<null | Record<
		string,
		OptionType | null
	>>;
	entitiesSelectedOptionsStore: Writable<null | Record<
		string,
		OptionType | null
	>>;
};

export const createArtworkFeedsStore = () => {
	const showMatchEventsWarning = writable(null as boolean | null);
	const scrapedArtworksStore = writable(
		null as Awaited<ReturnType<typeof fetchScrapedArtworks>> | null
	);

	const scrapedArtistsStore = writable(
		null as
			| null
			| (NonNullable<GetScrapedArtistsQuery['getScrapedArtists']>[number] & {
					artworks?: GetArtistsQuery['artist'][number]['artworks'] | undefined;
			  })[]
	);

	const scrapedEntitiesStore = writable(
		null as null | GetScrapedEntitiesQuery['getScrapedEntities']
	);

	const artistSelectedOptionsStore = writable(
		null as null | Record<string, OptionType | null>
	);

	const entitiesSelectedOptionsStore = writable(
		null as null | Record<string, OptionType | null>
	);

	const checkEventsAreMatches = async () => {
		if (get(showMatchEventsWarning)) {
			return;
		}

		const res = await gqlClientCustom.request(
			GetScrapedEventsDocument,
			{
				input: { ids: get(artworkFeeds).currentIds },
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		const eventsToMatch = res.getScrapedArtworks.unsentScrapedEvents
			.map((artworkFeed) => artworkFeed?.art_event_feed)
			?.filter(
				(artEventFeed) =>
					!['private_sale', 'museum_acquisition'].includes(
						artEventFeed?.event_type?.key as string
					)
			);

		showMatchEventsWarning.set(!!eventsToMatch.length);
	};

	const artworkFeeds = persisted(LocalStorageKeys.ArtworFeedIds, {
		unprocessedIds: [],
		scrapedArtworkIds: [],
		currentIds: [],
		submittingArtworks: false,
		stage: '',
	} as ArtworkFeeds);

	const setSubmittingArtworks = (submittingArtworks: boolean) => {
		artworkFeeds.set({
			...get(artworkFeeds),
			submittingArtworks,
		});
	};

	const setUnprocessedIds = (unprocessedIds: string[]) => {
		showMatchEventsWarning.set(null);
		scrapedArtworksStore.set(null);
		scrapedArtistsStore.set(null);
		scrapedEntitiesStore.set(null);
		artistSelectedOptionsStore.set(null);
		entitiesSelectedOptionsStore.set(null);
		artworkFeeds.set({
			stage: '',
			unprocessedIds,
			scrapedArtworkIds: [],
			currentIds: [],
			submittingArtworks: false,
		});
	};

	const setCurrentIds = (currentIds: string[], scrapedArtworkIds: string[]) => {
		showMatchEventsWarning.set(null);
		scrapedArtworksStore.set(null);
		scrapedArtistsStore.set(null);
		scrapedEntitiesStore.set(null);
		artistSelectedOptionsStore.set(null);
		entitiesSelectedOptionsStore.set(null);
		artworkFeeds.set({
			stage: '',
			unprocessedIds: get(artworkFeeds).unprocessedIds,
			currentIds,
			scrapedArtworkIds,
			submittingArtworks: false,
		});
	};

	const resetCurrentIds = (
		currentIds: string[],
		scrapedArtworkIds: string[]
	) => {
		showMatchEventsWarning.set(null);
		scrapedArtworksStore.set(null);
		scrapedArtistsStore.set(null);
		scrapedEntitiesStore.set(null);
		artistSelectedOptionsStore.set(null);
		entitiesSelectedOptionsStore.set(null);
		const oldCurrentIds = get(artworkFeeds).currentIds;

		artworkFeeds.set({
			stage: '',
			submittingArtworks: false,
			unprocessedIds: get(artworkFeeds).unprocessedIds.filter(
				(unprocessedId) => !oldCurrentIds.includes(unprocessedId)
			),
			currentIds,
			scrapedArtworkIds,
		});
	};

	const saveStage = () => {
		artworkFeeds.set({
			...get(artworkFeeds),
			stage: `${page.route.id}`,
		});
	};

	return {
		scrapedEntitiesStore,
		scrapedArtistsStore,
		artistSelectedOptionsStore,
		entitiesSelectedOptionsStore,
		scrapedArtworksStore,
		checkEventsAreMatches,
		showMatchEventsWarning,
		artworkFeeds,
		saveStage,
		setSubmittingArtworks,
		setUnprocessedIds,
		setCurrentIds,
		resetCurrentIds,
	};
};
