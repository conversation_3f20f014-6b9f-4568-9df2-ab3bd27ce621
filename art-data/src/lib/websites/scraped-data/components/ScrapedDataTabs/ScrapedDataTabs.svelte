<script lang="ts">
	import { goto } from '$app/navigation';
	import { Tabs } from '$global/components/Tabs';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		activeTab: number;
		disabled?: boolean;
	}

	let { activeTab, disabled }: Props = $props();

	const tabs = [
		{ title: '1. Preview data', id: '1' },
		{ title: '2. Match events', id: '2', disabled },
		{ title: '3. Process data', id: '3', disabled },
		{ title: '4. Provenance linking', id: '4', disabled },
	];

	const handleClickTab = (activeTab: number) => {
		const url = (() => {
			switch (activeTab) {
				default:
				case 0:
					return `${Routes.ScrapedDataPreview}`;
				case 1:
					return `${Routes.ScrapedDataMatchEvents}`;
				case 2:
					return `${Routes.ScrapedDataProcessDataImages}`;
				case 3:
					return `${Routes.ScrapedDataProvenanceLinking}`;
			}
		})();

		goto(url);
	};
</script>

<div class="mb-8">
	<Tabs
		{tabs}
		onClickTab={handleClickTab}
		dataCy="scraped-data"
		{activeTab}
		class="[&_button>p]:pl-1 [&_button>p]:font-[600] [&_button]:pt-0"
	/>
</div>
