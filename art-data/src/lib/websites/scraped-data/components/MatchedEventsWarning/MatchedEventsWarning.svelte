<script lang="ts">
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { LinkButton } from '$global/components/LinkButton';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
</script>

<div
	class="flex flex-col items-center border border-gray-200 bg-gray-100 p-16 pb-24 mt-[-16px]"
>
	<Txt variant="h6" class="my-4"
		>You must complete the Match Events stage before you can process the data</Txt
	>

	<LinkButton
		size="md"
		class="mb-1"
		variant="secondary"
		dataCy="matched-events-warning"
		href={Routes.ScrapedDataMatchEvents}
	>
		{#snippet leading()}
			<ChevronLeftIcon />
		{/snippet}
		go to match events</LinkButton
	>
</div>
