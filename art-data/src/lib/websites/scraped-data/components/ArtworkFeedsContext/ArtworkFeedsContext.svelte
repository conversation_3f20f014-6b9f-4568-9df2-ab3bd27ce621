<script lang="ts">
	import { setContext } from 'svelte';
	import { createArtworkFeedsStore } from '../../utils/createArtworkFeedsStore/createArtworkFeedsStore';
	import { Contexts } from '$lib/constants/contexts';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	setContext(Contexts.ArtworkFeeds, createArtworkFeedsStore());
</script>

{@render children?.()}
