<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { twMerge } from 'tailwind-merge';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		onContinue: () => void;
		children?: import('svelte').Snippet;
		class?: string;
	}

	let { ...props }: Props = $props();

	const handleClose = () => {
		props.dialogStores.states.open.set(false);
	};
</script>

<Dialog
	title="Records saved"
	showOverlay={true}
	showCloseIcon={false}
	dialogStores={props.dialogStores}
	shouldClose={false}
	dataCy="record-saved"
	class={twMerge(
		'flex max-h-[90vh] max-w-[100%] flex-col sm:py-8',
		props.class
	)}
	classes={{
		title: 'text-left pl-0',
		description: 'text-left',
	}}
	titleVariant="h4"
	description="Below is a summary of the records."
>
	<button
		class="absolute right-4 top-4"
		onclick={() => {
			window.location.reload();
		}}
	>
		<CrossIcon />
	</button>

	<div class="mt-[-1rem] max-h-[calc(100%-200px)] flex-grow overflow-y-scroll">
		{@render props.children?.()}
	</div>

	<div class="mt-8 flex justify-end">
		<Button
			dataCy="continue"
			variant="primary"
			size="md"
			onclick={props.onContinue}>Continue</Button
		>
	</div>
</Dialog>
