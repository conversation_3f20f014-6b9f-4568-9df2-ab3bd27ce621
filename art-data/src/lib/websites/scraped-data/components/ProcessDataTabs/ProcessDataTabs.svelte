<script lang="ts">
	import { goto } from '$app/navigation';
	import { Tabs } from '$global/components/Tabs';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		activeTab: number;
	}

	let { activeTab }: Props = $props();

	const tabs = [
		{ title: 'Images', id: '1' },
		{ title: 'Artwork Details', id: '2' },
		{ title: 'Artist Info', id: '3' },
		{ title: 'Executed', id: '4' },
		{ title: 'Dimensions', id: '5' },
		{ title: 'Editions info', id: '6' },
		{ title: 'Activity listing info', id: '7' },
		{ title: 'Auction info', id: '8' },
		{ title: 'Entity', id: '9' },
		{ title: 'Finalise', id: '10' },
	];

	const handleClickTab = (activeTab: number) => {
		const url = (() => {
			switch (activeTab) {
				default:
				case 0:
					return `${Routes.ScrapedDataProcessDataImages}`;
				case 1:
					return `${Routes.ScrapedDataProcessDataArtworkDetails}`;
				case 2:
					return `${Routes.ScrapedDataProcessDataArtistInfo}`;
				case 3:
					return `${Routes.ScrapedDataProcessDataExecuted}`;
				case 4:
					return `${Routes.ScrapedDataProcessDataDimensions}`;
				case 5:
					return `${Routes.ScrapedDataProcessDataEditionsInfo}`;
				case 6:
					return `${Routes.ScrapedDataProcessDataActivityListingInfo}`;
				case 7:
					return `${Routes.ScrapedDataProcessDataAuctionInfo}`;
				case 8:
					return `${Routes.ScrapedDataProcessDataEntities}`;
				case 9:
					return `${Routes.ScrapedDataProcessDataFinalise}`;
			}
		})();

		goto(url);
	};
</script>

<div class="mb-8">
	<Tabs
		{tabs}
		showIndex
		onClickTab={handleClickTab}
		dataCy="scraped-data"
		{activeTab}
		classes={{
			index:
				'text-blue-500 bg-blue-100 rounded-full w-[20px] mr-2 text-[0.75rem]',
		}}
		class="[&_button>p]:text-[0.875rem] [&_button>p]:font-[600] [&_button>span]:absolute [&_button>span]:bottom-0 [&_button]:relative [&_button]:pt-0"
	/>
</div>
