import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getAuctions(
		$filter: auction_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		auction(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			id
			sale_name
			auction_end_date
			auction_start_date
			auction_house {
				organisation {
					name
				}
			}
			sale_number
			currency {
				name
				code
				symbol
			}
		}
	}
`;
