import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getEntities($filter: entity_filter, $limit: Int, $sort: [String]) {
		entity(filter: $filter, limit: $limit, sort: $sort) {
			id
			type {
				key
			}
			name
			person {
				id
				year_birth
				year_death
				type {
					person_type_key {
						key
						name
					}
				}
				nationalities {
					country {
						country_nationality
					}
				}
			}
			artist {
				id
				person {
					id
					year_birth
					year_death
					type {
						person_type_key {
							key
							name
						}
					}
					nationalities {
						country {
							country_nationality
						}
					}
				}
			}
			reference_id
			organisation {
				id
				name
				type {
					organisation_type_key {
						key
						name
					}
				}
				location {
					code
					name
					short_code
					country {
						name
						code
						short_code
					}
					country_nationality
					type {
						key
					}
				}
			}
			gallery {
				id
			}
			addresses {
				city {
					name
				}
				country {
					name
				}
			}
		}
	}
`;
