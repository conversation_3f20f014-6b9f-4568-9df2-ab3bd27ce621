import { gql } from 'graphql-request';

export const GET_ACTIVITY = gql`
	query getActivity($filter: artwork_activity_filter) {
		artwork_activity(filter: $filter) {
			id
			activity_artwork_info {
				provenance
			}
			artworks {
				artwork {
					id
					title
					media
					primary_image {
						id
					}
					artists {
						artist_id {
							person {
								entity {
									name
								}
							}
						}
					}
				}
			}
		}
	}
`;
