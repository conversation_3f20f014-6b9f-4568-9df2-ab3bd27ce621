import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye';

export type GetAuctionTypesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Auction_Type_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetAuctionTypesQuery = {
	__typename?: 'Query';
	auction_type: Array<{
		__typename?: 'auction_type';
		name: string;
		key: string;
	}>;
};

export const GetAuctionTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAuctionTypes' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'auction_type_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_type' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetAuctionTypesQuery,
	GetAuctionTypesQueryVariables
>;
