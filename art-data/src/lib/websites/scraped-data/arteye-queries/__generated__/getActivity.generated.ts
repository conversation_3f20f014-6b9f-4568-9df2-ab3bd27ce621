import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye';

export type GetActivityQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Filter>;
}>;

export type GetActivityQuery = {
	__typename?: 'Query';
	artwork_activity: Array<{
		__typename?: 'artwork_activity';
		id: string;
		activity_artwork_info?: {
			__typename?: 'artwork_activity_artwork_info';
			provenance?: string | null;
		} | null;
		artworks?: Array<{
			__typename?: 'artwork_activity_artwork';
			artwork?: {
				__typename?: 'artwork';
				id: string;
				title?: string | null;
				media?: string | null;
				primary_image?: { __typename?: 'directus_files'; id: string } | null;
				artists?: Array<{
					__typename?: 'artwork_artist';
					artist_id?: {
						__typename?: 'artist';
						person?: {
							__typename?: 'person';
							entity?: { __typename?: 'entity'; name: string } | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
		} | null> | null;
	}>;
};

export const GetActivityDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getActivity' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activity_artwork_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'provenance' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'media' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'primary_image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artists' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artist_id' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'entity',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetActivityQuery, GetActivityQueryVariables>;
