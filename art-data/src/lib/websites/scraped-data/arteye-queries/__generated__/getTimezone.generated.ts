import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye';

export type GetTimezoneQueryVariables = Types.Exact<{
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	filter?: Types.InputMaybe<Types.Timezone_Filter>;
}>;

export type GetTimezoneQuery = {
	__typename?: 'Query';
	timezone: Array<{
		__typename?: 'timezone';
		offset: number;
		offset_dst: number;
		timezone: string;
	}>;
};

export const GetTimezoneDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getTimezone' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'timezone_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'timezone' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'offset' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'offset_dst' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'timezone' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetTimezoneQuery, GetTimezoneQueryVariables>;
