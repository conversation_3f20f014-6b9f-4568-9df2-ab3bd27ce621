import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye';

export type GetExhibitionsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Exhibition_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetExhibitionsQuery = {
	__typename?: 'Query';
	exhibition: Array<{
		__typename?: 'exhibition';
		id: string;
		start_date?: any | null;
		end_date?: any | null;
		exhibition_url?: string | null;
		title: string;
		description?: string | null;
		venue_address_1?: string | null;
		venue_address_2?: string | null;
		venue_address_3?: string | null;
		venue_post_code?: string | null;
		aggregations?: Array<{
			__typename?: 'exhibition_aggregation';
			artwork_listing_count: number;
		} | null> | null;
		cover_image?: {
			__typename?: 'directus_files';
			id: string;
			title?: string | null;
			type?: string | null;
			storage: string;
			filename_download: string;
			filename_disk?: string | null;
		} | null;
		venue_country?: {
			__typename?: 'location';
			short_code?: string | null;
			code: string;
			name?: string | null;
		} | null;
		venue_city?: {
			__typename?: 'location';
			name?: string | null;
			code: string;
			short_code?: string | null;
			country?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
			} | null;
		} | null;
		organisers?: Array<{
			__typename?: 'exhibition_organisers';
			id: string;
			entity_id?: {
				__typename?: 'entity';
				id: string;
				name: string;
				type?: { __typename?: 'entity_type'; key: string } | null;
				artist?: { __typename?: 'artist'; id: string } | null;
				organisation?: {
					__typename?: 'organisation';
					id: string;
					name: string;
				} | null;
				person?: { __typename?: 'person'; id: string } | null;
				addresses?: Array<{
					__typename?: 'entity_address';
					city?: { __typename?: 'location'; name?: string | null } | null;
					country?: { __typename?: 'location'; name?: string | null } | null;
				} | null> | null;
			} | null;
		} | null> | null;
		attributes?: Array<{
			__typename?: 'exhibition_attribute';
			id: string;
			type?: {
				__typename?: 'exhibition_attribute_type';
				key: string;
				name: string;
			} | null;
			exhibition?: { __typename?: 'exhibition'; id: string } | null;
		} | null> | null;
	}>;
	exhibition_aggregated: Array<{
		__typename?: 'exhibition_aggregated';
		count?: {
			__typename?: 'exhibition_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetExhibitionsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getExhibitions' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'exhibition_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'exhibition' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'start_date' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'end_date' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'exhibition_url' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'aggregations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_listing_count' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'cover_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_1' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_2' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_3' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_post_code' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_country' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_city' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'organisers' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'addresses' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'city' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'attributes' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'exhibition_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'count' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetExhibitionsQuery, GetExhibitionsQueryVariables>;
