import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getArtists($filter: artist_filter, $limit: Int) {
		artist(filter: $filter, limit: $limit) {
			id
			reference_id
			artworks {
				artwork_id {
					primary_image {
						id
					}
				}
			}
			person {
				year_birth
				year_death
				entity {
					name
				}
				nationalities {
					country {
						code
						country_nationality
						name
						short_code
					}
				}
				type {
					person_type_key {
						key
						name
					}
				}
			}
			aggregations {
				artwork_count
				id
			}
		}
	}
`;
