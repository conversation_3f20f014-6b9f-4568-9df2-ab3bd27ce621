<script lang="ts">
	import classNames from 'classnames';
	import { twMerge } from 'tailwind-merge';
	import type { GetArteyeFairsQuery } from '../../arteye-queries/__generated__/getArteyeFairs.generated';
	import { getFairDetails } from '../../utils/getFairDetails/getFairDetails';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { type OptionClasses } from '$global/components/QueryAutocomplete/types';

	interface Props {
		fair: GetArteyeFairsQuery['fair'][number];
		dataCy: string;
		classes?: OptionClasses;
		class?: string;
	}

	let {
		fair,
		dataCy,
		classes = {
			line3: fair?.venue_city?.code === 'null' ? 'hidden' : 'w-fit',
			line4: 'float-left inline',
			line5: 'float-left inline ml-1',
			line6: 'hidden',
			line7: 'hidden',
		},
		...rest
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-details`);
	let option = $derived(getFairDetails(fair));
</script>

<div
	data-cy={dataCyPrefix}
	class={twMerge(
		classNames('flex flex-col gap-2 rounded border border-gray-200 p-3'),
		rest.class
	)}
>
	<LinkOption dataCy={dataCyPrefix} {option} {classes} />
</div>
