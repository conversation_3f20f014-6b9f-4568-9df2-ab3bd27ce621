import dayjs from 'dayjs';
import { type GetArteyeFairsQuery } from '../../arteye-queries/__generated__/getArteyeFairs.generated';
import { Config } from '$lib/constants/config';

export const getFairDetails = (fair: GetArteyeFairsQuery['fair'][number]) => ({
	line1: fair?.title,
	line2: `${Config.ArteyeDomain}/fairs/${fair?.id}`,
	line3: fair?.venue_city?.name || fair?.venue_country?.name,
	line4: fair?.local_start_date
		? dayjs(fair?.local_start_date).format('DD/MM/YYYY')
		: '',
	line5: fair?.local_end_date
		? `${fair?.local_start_date ? ' - ' : ''}${dayjs(
				fair?.local_end_date
			).format('DD/MM/YYYY')}`
		: '',
	line6: fair?.local_start_date
		? dayjs(fair?.local_start_date).format('YYYY-MM-DDTHH:mm:ss')
		: '',
	line7: fair?.local_end_date
		? dayjs(fair?.local_end_date).format('YYYY-MM-DDTHH:mm:ss')
		: '',
});
