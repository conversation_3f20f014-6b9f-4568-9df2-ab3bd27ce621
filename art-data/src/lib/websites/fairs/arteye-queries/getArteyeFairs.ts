import { gql } from 'graphql-tag';

export const GET_ARTEYE_FAIRS = gql`
	query getArteyeFairs($filter: fair_filter, $sort: [String], $limit: Int) {
		fair(filter: $filter, sort: $sort, limit: $limit) {
			id
			title
			local_start_date
			local_end_date
			venue_city {
				name
				code
			}
			venue_country {
				name
				code
			}
			exhibitors {
				entity {
					name
					organisation {
						location {
							name
							code
						}
					}
					gallery {
						id
					}
				}
			}
		}
	}
`;
