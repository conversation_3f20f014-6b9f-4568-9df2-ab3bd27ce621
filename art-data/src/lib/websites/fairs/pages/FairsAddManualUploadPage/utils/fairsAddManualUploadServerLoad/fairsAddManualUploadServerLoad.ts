import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAddManualUploadSuperform } from '$lib/features/add-manual-upload/utils/getAddManualUploadSuperform/getAddManualUploadSuperform';
import { gqlClient } from '$lib/gqlClient';
import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';
import type { FairsIdPageServerLoadEvent } from '$routes/fairs/[id]/types';

export const fairsAddManualUploadServerLoad = async ({
	parent,
	params,
}: FairsIdPageServerLoadEvent) => {
	const data = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairsDocument,
		{
			filter: { id: { _eq: params.id } },
		},
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const forms = await getAddManualUploadSuperform();
	const dropzoneUrlForms = await getDropzoneUrlDialogSuperform();

	return {
		...data,
		...forms,
		...dropzoneUrlForms,
		fair,
	};
};
