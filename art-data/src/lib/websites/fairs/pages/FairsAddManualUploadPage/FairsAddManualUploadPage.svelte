<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddManualUpload } from '$lib/features/add-manual-upload/components/AddManualUpload';
	import type { FairsIdManualUploadAddPageData } from '$routes/fairs/[id]/manual-upload/add/types';

	let data = $derived(getPageData<FairsIdManualUploadAddPageData>(page.data));

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${data.fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'Add Manual Upload' },
	]);
</script>

<AddManualUpload
	{crumbs}
	title={`${data.fair?.processed_fair?.title}`}
	backButtonHref={`${Routes.FairsHome}/${page.params.id}`}
	key="fairs"
	collection="Ingestion_Fair"
	buttonProps={{
		label: 'Back to fair page',
		href: `${Routes.FairsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to fairs list',
		href: Routes.FairsHome,
	}}
/>
