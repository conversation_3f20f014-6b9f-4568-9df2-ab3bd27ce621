<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddArtlogic } from '$lib/features/add-artlogic/components/AddArtlogic';
	import type { FairsIdArtlogicAddPageData } from '$routes/fairs/[id]/artlogic/add/types';

	let data = $derived(getPageData<FairsIdArtlogicAddPageData>(page.data));

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${data?.fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'Add Artlogic link' },
	]);
</script>

<AddArtlogic
	{crumbs}
	gallery={data.gallery}
	title={`${data?.fair?.processed_fair?.title}`}
	key="fairs"
	collection="Ingestion_Fair"
	buttonProps={{
		label: 'Back to fair page',
		href: `${Routes.FairsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to fairs list',
		href: Routes.FairsHome,
	}}
/>
