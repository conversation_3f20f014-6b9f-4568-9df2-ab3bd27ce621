import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import type { ArtlogicGallery } from '$lib/features/add-artlogic/types';
import { getAddArtlogicSuperform } from '$lib/features/add-artlogic/utils/getAddArtlogicSuperform/getAddArtlogicSuperform';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetArteyeFairsDocument } from '$lib/websites/fairs/arteye-queries/__generated__/getArteyeFairs.generated';
import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';
import type { FairsIdPageServerLoadEvent } from '$routes/fairs/[id]/types';

export const fairsAddArtlogicServerLoad = async ({
	parent,
	params,
}: FairsIdPageServerLoadEvent) => {
	const data = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const forms = await getAddArtlogicSuperform();

	let gallery: null | ArtlogicGallery = null;

	const arteyeFairRes = await gqlClientArteye.request(
		GetArteyeFairsDocument,
		{ filter: { id: { _eq: fair.processed_fair?.processed_fair_id } } },
		getAuthorizationHeaders({ user: { access_token: data.user?.arteye_token } })
	);

	const arteyeFairFirstGalleryExhibitor =
		arteyeFairRes?.fair?.[0]?.exhibitors?.find(
			(exhibitor) => exhibitor?.entity?.gallery?.id
		);

	if (arteyeFairFirstGalleryExhibitor) {
		gallery = {
			id: arteyeFairFirstGalleryExhibitor?.entity?.gallery?.id,
			organisation: {
				entity: {
					name: arteyeFairFirstGalleryExhibitor?.entity?.name,
				},
				location: arteyeFairFirstGalleryExhibitor?.entity?.organisation
					?.location as ArtlogicGallery['organisation']['location'],
			},
		};
	}

	return {
		...data,
		...forms,
		fair,
		gallery,
	};
};
