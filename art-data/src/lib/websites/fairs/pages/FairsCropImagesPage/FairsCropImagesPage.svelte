<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ImageCroppingPage } from '$lib/features/crop-images/ImageCroppingPage';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { FairsIdImagesCropPageData } from '$routes/fairs/[id]/images/crop/types';

	let data = $derived(getPageData<FairsIdImagesCropPageData>(page.data));
	let fair = $derived(data.fair);
	let visitImages = $derived(data.visitImages);
	let photographer = $derived(data.photographer);
	let date = $derived(data.date);

	const dataCyPrefix = 'fairs';

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${fair.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Image Cropping' },
	]);

	let buttonProps = $derived({
		label: 'back to fair page',
		href: `${Routes.FairsHome}/${page.params.id}`,
	});
</script>

<ImageCroppingPage
	{visitImages}
	{photographer}
	title={`${fair?.processed_fair?.title}`}
	{crumbs}
	dataCy={dataCyPrefix}
	{buttonProps}
/>
