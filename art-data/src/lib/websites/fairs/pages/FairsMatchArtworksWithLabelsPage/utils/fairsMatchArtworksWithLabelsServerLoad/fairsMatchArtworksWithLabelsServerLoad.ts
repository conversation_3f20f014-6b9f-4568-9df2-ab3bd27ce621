import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsIdImagesMatchArtworksPageServerLoadEvent } from '$routes/fairs/[id]/images/match-artworks/types';

export const fairsMatchArtworksWithLabelsServerLoad = async ({
	parent,
	params,
	url,
}: FairsIdImagesMatchArtworksPageServerLoadEvent) => {
	const data = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const fairsResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const visitId = fair?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographerParam } } },
		getAuthorizationHeaders(data)
	);

	const photographer = usersResponse.users?.[0];

	return {
		fair,
		visitId,
		dateParam,
		photographer,
	};
};
