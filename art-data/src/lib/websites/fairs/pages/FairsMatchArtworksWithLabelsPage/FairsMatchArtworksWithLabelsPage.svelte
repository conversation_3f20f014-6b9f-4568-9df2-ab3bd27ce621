<script lang="ts">
	import classNames from 'classnames';
	import { FairsDetails } from '../../components/FairsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { MatchArtworksWithLabels } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels';
	import { MatchArtworksWithLabelsTableContext } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels/MatchArtworksWithLabelsTableContext';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { FairsIdImagesMatchArtworksPageData } from '$routes/fairs/[id]/images/match-artworks/types';

	const dataCy = 'fairs-match-artworks-with-labels';

	let submittingDiscardedImages = $state(false);
	let fetching = $state(false);
	let submitting = $state(false);

	let data = $derived(
		getPageData<FairsIdImagesMatchArtworksPageData>(page.data)
	);
	let visitId = $derived(data.visitId);
	let date = $derived(data.dateParam);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${data?.fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Match artworks with labels' },
	]);
</script>

<MatchArtworksWithLabelsTableContext>
	<PageBody
		class={classNames({
			'pointer-events-none':
				submitting || submittingDiscardedImages || fetching,
		})}
	>
		<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
		<MatchArtworksWithLabels
			bind:submittingDiscardedImages
			bind:fetching
			bind:submitting
			title={`${data?.fair?.processed_fair?.title}`}
			slotLabel="Fair details"
			{visitId}
			{dataCy}
			{date}
			photographerId={photographer.id}
		>
			{#if data.fair?.processed_fair}
				<FairsDetails
					{dataCy}
					fair={{
						title: data?.fair?.processed_fair?.title,
						id: `${data?.fair?.processed_fair?.processed_fair_id}`,
						local_start_date: data?.fair?.processed_fair?.start_date,
						local_end_date: data?.fair?.processed_fair?.end_date,
						venue_city: {
							code: `${data?.fair?.processed_fair?.location}`,
							name: `${data?.fair?.processed_fair?.location}`,
						},
					}}
				/>
			{/if}
		</MatchArtworksWithLabels>
	</PageBody>
</MatchArtworksWithLabelsTableContext>
