import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsIdImagesAddPageServerLoadEvent } from '$routes/fairs/[id]/images/add/types';

export const fairsAddMoreIcloudImagesServerLoad = async ({
	params,
	parent,
}: FairsIdImagesAddPageServerLoadEvent) => {
	const data = await parent();

	const fairResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairResponse?.Ingestion_Fair?.[0];

	if (!fair?.visit) {
		error(404, 'Not found');
	}

	return { fair };
};
