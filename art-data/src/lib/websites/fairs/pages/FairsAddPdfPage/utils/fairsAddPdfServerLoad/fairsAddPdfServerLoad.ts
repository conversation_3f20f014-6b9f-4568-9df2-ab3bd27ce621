import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAddPdfSuperform } from '$lib/features/add-pdf/utils/getAddPdfSuperform/getAddPdfSuperform';
import { gqlClient } from '$lib/gqlClient';
import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';
import type { FairsIdPageServerLoadEvent } from '$routes/fairs/[id]/types';

export const fairsAddPdfServerLoad = async ({
	parent,
	params,
}: FairsIdPageServerLoadEvent) => {
	const data = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const forms = await getAddPdfSuperform();

	return {
		...data,
		...forms,
		fair,
	};
};
