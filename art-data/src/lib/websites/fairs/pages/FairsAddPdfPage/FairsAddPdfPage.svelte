<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddPdf } from '$lib/features/add-pdf/components/AddPdf';
	import type { FairsIdPdfsAddPageData } from '$routes/fairs/[id]/pdfs/add/types';

	let data = $derived(getPageData<FairsIdPdfsAddPageData>(page.data));
	let fair = $derived(data.fair);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'Add PDF' },
	]);
</script>

<AddPdf
	key="fairs"
	collection="Ingestion_Fair"
	{crumbs}
	title={fair.processed_fair?.title}
	buttonProps={{
		label: 'Back to fair page',
		href: `${Routes.FairsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to fairs list',
		href: Routes.FairsHome,
	}}
/>
