import { error } from '@sveltejs/kit';
import { getVisitsBaseVariables } from '../fairsUploadListPageLoad/fairsUploadListPageLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetCropImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getCropImagesVisits.generated';
import { GetToMatchImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToMatchImagesVisits.generated';
import { GetToReviewImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToReviewImagesVisits.generated';
import { GetUploadedImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getUploadedImagesVisits.generated';
import { gqlClient } from '$lib/gqlClient';
import { GetVisitArtworkDocument } from '$lib/queries/__generated__/getVisitArtwork.generated';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsIdPageServerLoadEvent } from '$routes/fairs/[id]/types';

export const fairsUploadListServerLoad = async ({
	parent,
	params,
}: FairsIdPageServerLoadEvent) => {
	const data = await parent();

	const fairPromise = gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const uploadedImagesPromise = gqlClient.request(
		GetUploadedImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const cropImagesPromise = gqlClient.request(
		GetCropImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const toMatchImagesPromise = gqlClient.request(
		GetToMatchImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const toReviewImagesPromise = await gqlClient.request(
		GetToReviewImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const [
		fairResponse,
		uploadedImages,
		cropImages,
		toMatchImages,
		toReviewImages,
	] = await Promise.all([
		fairPromise,
		uploadedImagesPromise,
		cropImagesPromise,
		toMatchImagesPromise,
		toReviewImagesPromise,
	]);

	const fair = fairResponse?.Ingestion_Fair?.[0];

	if (!fair) {
		error(404, 'Not found');
	}

	const visitArtworks = await (async () => {
		const visitId = fair?.visit?.id;
		if (visitId) {
			const res = await gqlClient.request(
				GetVisitArtworkDocument,
				{ filter: { visit: { id: { _eq: visitId } } }, limit: 10000 },
				getAuthorizationHeaders(data)
			);

			return res?.Visit_Artwork;
		}

		return [];
	})();

	return {
		...data,
		visitArtworks,
		fair,
		uploadedImages,
		cropImages,
		toMatchImages,
		toReviewImages,
	};
};
