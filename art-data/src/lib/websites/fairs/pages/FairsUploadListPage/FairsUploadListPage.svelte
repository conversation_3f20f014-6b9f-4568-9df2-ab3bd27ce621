<script lang="ts">
	import { FairsDetails } from '../../components/FairsDetails';
	import { page } from '$app/state';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import SummaryItem from '$lib/features/upload-list/components/SummaryItem/SummaryItem.svelte';
	import { UploadList } from '$lib/features/upload-list/components/UploadList';

	import {
		type UploadListArtlogicLink,
		type UploadListManualUpload,
		type UploadListPdf,
		IngestionDataTypenames,
	} from '$lib/types';

	import { extractItemsFromIngestionData } from '$lib/utils/extractItemsFromIngestionData/extractItemsFromIngestionData';
	import type { FairsIdPageData } from '$routes/fairs/[id]/types';

	let data = $derived(getPageData<FairsIdPageData>(page.data));
	let fair = $derived(data.fair);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{ label: `${fair.processed_fair?.title}` },
	]);

	let pdfs = $derived(
		extractItemsFromIngestionData(
			data.fair.ingestion_data,
			data.fair.ready_for_review,
			data.fair.reviewed_and_submitted,
			data.fair.matches,
			data.fair.skipped,
			IngestionDataTypenames.Pdf
		) as UploadListPdf[]
	);

	let artlogicLinks = $derived(
		extractItemsFromIngestionData(
			data.fair.ingestion_data,
			data.fair.ready_for_review,
			data.fair.reviewed_and_submitted,
			data.fair.matches,
			data.fair.skipped,
			IngestionDataTypenames.ArtlogicLink
		) as UploadListArtlogicLink[]
	);

	let manualUploads = $derived(
		extractItemsFromIngestionData(
			data.fair.ingestion_data,
			data.fair.ready_for_review,
			data.fair.reviewed_and_submitted,
			data.fair.matches,
			data.fair.skipped,
			IngestionDataTypenames.ManualUpload
		) as UploadListManualUpload[]
	);
</script>

<UploadList
	{crumbs}
	{pdfs}
	visitArtworks={data.visitArtworks}
	visit={fair.visit}
	{manualUploads}
	{artlogicLinks}
	event="fair"
	dataCy="fairs"
	subtitle="Fair details"
	title="Fair Upload List"
	firstTabTitle="Fair checklists"
	collectionName="Ingestion_Fair"
>
	{#if fair?.processed_fair}
		<FairsDetails
			dataCy="fairs-upload-list"
			fair={{
				title: fair?.processed_fair?.title,
				id: `${data?.fair?.processed_fair?.processed_fair_id}`,
				local_start_date: fair?.processed_fair?.start_date,
				local_end_date: fair?.processed_fair?.end_date,
				venue_city: {
					code: `${fair?.processed_fair?.location}`,
					name: `${fair?.processed_fair?.location}`,
				},
			}}
		/>
	{/if}

	<!-- <div slot="summary">
		<Txt variant="h6" class="mb-4">Fair data summary</Txt>
		<div class="flex w-full gap-3">
			<SummaryItem bgColor="#1663B3" label="Checklists received" count={4} />
			<SummaryItem bgColor="#1663B3" label="Fair images uploaded" count={214} />
			<SummaryItem
				bgColor="#E17425"
				label="Fair images pending crops"
				count={134}
			/>
			<SummaryItem
				bgColor="#E17425"
				label="Fair images pending matches"
				count={84}
			/>
			<SummaryItem
				bgColor="#E17425"
				label="Fair images pending review"
				count={49}
			/>
			<SummaryItem
				bgColor="#059669"
				label="Processed fair artworks"
				count={32}
			/>
		</div>
	</div> -->
</UploadList>
