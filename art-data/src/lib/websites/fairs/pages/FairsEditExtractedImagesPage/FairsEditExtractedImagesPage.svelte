<script lang="ts">
	import { FairsDetails } from '../../components/FairsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import { EditExtractedImages } from '$lib/features/edit-extracted-images/components/EditExtractedImages';
	import type { FairsIdPdfsPdfIdEditExtractedImagesPageData } from '$routes/fairs/[id]/pdfs/[pdfId]/edit-extracted-images/types';

	let data = $derived(
		getPageData<FairsIdPdfsPdfIdEditExtractedImagesPageData>(page.data)
	);
	let fair = $derived(data.fair);
	let pdfFile = $derived(data.file);
	let pdfAiExtractedText = $derived(data.pdfAiExtractedText);
	let pdfArtworkFormatMethod = $derived(data.pdfArtworkFormatMethod);
	let pdfTitle = $derived(data.pdfTitle);
	let pdfArtist = $derived(data.pdfArtist);
	let pdfArtistCountry = $derived(data.pdfArtistCountry);
	let artworks = $derived(data.artworks);
	let pages = $derived(data.pages);
	let discardedImages = $derived(data.discardedImages);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${fair.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Edit Extracted Images' },
	]);

	const dataCy = 'fairs';

	const buttonProps = {
		label: 'back to fair uploads',
		href: `${Routes.FairsHome}/${page.params.id}`,
	};
</script>

<ArtworkTextTableContext>
	<EditExtractedImages
		{pdfArtist}
		{pdfArtistCountry}
		{pdfAiExtractedText}
		{pdfArtworkFormatMethod}
		title={`${fair?.processed_fair?.title}`}
		{artworks}
		{pdfTitle}
		{pages}
		{discardedImages}
		pdfLink={pdfFile}
		slotLabel="Fair the information is going to be added to"
		{dataCy}
		{crumbs}
		{buttonProps}
		{artworkSaleStatuses}
	>
		{#if fair}
			<FairsDetails
				dataCy={`${dataCy}-edit-extracted-images`}
				fair={{
					title: `${fair?.processed_fair?.title}`,
					id: `${data?.fair?.processed_fair?.processed_fair_id}`,
					local_start_date: fair?.processed_fair?.start_date,
					local_end_date: fair?.processed_fair?.end_date,
					venue_city: {
						code: `${fair?.processed_fair?.location}`,
						name: `${fair?.processed_fair?.location}`,
					},
				}}
			/>
		{/if}
	</EditExtractedImages>
</ArtworkTextTableContext>
