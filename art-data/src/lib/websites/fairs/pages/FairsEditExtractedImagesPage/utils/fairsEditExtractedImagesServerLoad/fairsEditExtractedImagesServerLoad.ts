import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { editExtractedImagesServerLoad } from '$lib/features/edit-extracted-images/utils/editExtractedImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent } from '$routes/exhibitions/[id]/pdfs/[pdfId]/edit-extracted-images/types';

export const fairsEditExtractedImagesServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent) => {
	const data = await parent();

	const fairResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairResponse?.Ingestion_Fair?.[0];

	if (!fair) {
		error(404, 'Not found');
	}

	const editExtractedImagesResponse = await editExtractedImagesServerLoad({
		params,
		data,
	});

	return {
		...data,
		fair,
		...editExtractedImagesResponse,
	};
};
