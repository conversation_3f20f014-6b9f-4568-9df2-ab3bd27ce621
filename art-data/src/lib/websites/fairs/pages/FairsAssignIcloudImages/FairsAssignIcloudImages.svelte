<script lang="ts">
	import { FairsDetails } from '../../components/FairsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AssignIcloudImages } from '$lib/features/add-more-images/components/AssignIcloudImages';
	import type { FairsIdImagesAddIcloudAssignPageData } from '$routes/fairs/[id]/images/add-icloud/assign/types';

	let pageNumber = $state(1);

	let data = $derived(
		getPageData<FairsIdImagesAddIcloudAssignPageData>(page.data)
	);
	let fair = $derived(data.fair);
	let images = $derived(data.images);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'Assign Icloud Images' },
	]);
</script>

<PageBody>
	<Breadcrumbs
		dataCy={'assign-icloud-images'}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<AssignIcloudImages
		visitId={fair?.visit?.id}
		{images}
		title={`${fair?.processed_fair?.title}`}
		bind:pageNumber
		dataCy="fairs"
	>
		{#if data.fair?.processed_fair}
			<FairsDetails
				dataCy={'assign-icloud-images'}
				fair={{
					title: data?.fair?.processed_fair?.title,
					id: `${data?.fair?.processed_fair?.processed_fair_id}`,
					local_start_date: data?.fair?.processed_fair?.start_date,
					local_end_date: data?.fair?.processed_fair?.end_date,
					venue_city: {
						code: `${data?.fair?.processed_fair?.location}`,
						name: `${data?.fair?.processed_fair?.location}`,
					},
				}}
			/>
		{/if}
	</AssignIcloudImages>
</PageBody>
