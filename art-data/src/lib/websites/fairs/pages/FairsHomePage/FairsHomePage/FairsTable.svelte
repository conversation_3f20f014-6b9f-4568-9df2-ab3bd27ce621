<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { onMount } from 'svelte';
	import { FAIRS_LIMIT } from '../utils/fairsHomePageLoad/fairsHomePageLoad';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getInfiniteData } from '$global/utils/infinite-loading/getInfiniteData';
	import { infiniteQuery } from '$global/utils/infinite-loading/infiniteQuery';
	import { Routes } from '$lib/constants/routes';
	import { getGqlClient } from '$lib/utils/getGqlClient/getGqlClient';
	import { hasEventPriorityItems } from '$lib/utils/hasEventPriorityItems/hasEventPriorityItems';
	import {
		GetFairWithChecklistsDocument,
		type GetFairWithChecklistsQuery,
	} from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
	import type { FairsHomePageData } from '$routes/fairs/types';

	interface Props {
		pageNumber: number;
		onClickPage: (e: Event | undefined, page?: number | null) => void;
	}

	let { pageNumber, onClickPage }: Props = $props();

	let isReady = $state(false);

	let data = $derived(getPageData<FairsHomePageData>(page.data));
	// eslint-disable-next-line svelte/valid-compile
	const gqlClient = getGqlClient(data);
	let queryVariables = $derived(data.queryVariables);

	onMount(() => {
		isReady = true;
	});

	let query = $derived(
		infiniteQuery({
			gqlClient,
			variables: queryVariables,
			document: GetFairWithChecklistsDocument,
			limit: FAIRS_LIMIT,
		})
	);

	let fairs = $derived(
		getInfiniteData({
			query: $query,
			transform: (data) => {
				return data.Ingestion_Fair;
			},
		})
	);

	let count = $derived(
		$query.data?.pages[0]?.Ingestion_Fair_aggregated?.[0]?.countDistinct?.id ||
			0
	);

	const actionCellWidth = '0rem';
	const headers = [
		'Fair title',
		'Location',
		'Received date',
		'Start date',
		'End date',
		'Fair Visit',
		'Has Priority Items',
	];

	const formatFair = (
		fair: GetFairWithChecklistsQuery['Ingestion_Fair'][number]
	) => {
		return [
			fair.processed_fair?.title,
			fair.processed_fair?.location || '',
			dayjs(fair.date_created).format('DD/MM/YYYY'),
			dayjs(fair.processed_fair?.start_date).format('DD/MM/YYYY'),
			dayjs(fair.processed_fair?.end_date).format('DD/MM/YYYY'),
			fair?.visit?.id ? 'Yes' : 'No',
			hasEventPriorityItems(fair.ingestion_data) ? 'Yes' : 'No',
		];
	};

	const dataCyPrefix = 'fairs-home-table';
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>

	{#if fairs}
		<TableBody dataCy={dataCyPrefix}>
			{#each fairs as fair, i}
				<TableRow
					class={classNames('cursor-pointer', {
						'bg-green-100': fair.event_review_status?.key === 'COMPLETED',
						'bg-red-100':
							fair.event_review_status?.key === 'REQUIRES_ATTENTION',
					})}
					index={i}
					dataCy={dataCyPrefix}
					onclick={() => goto(`${Routes.FairsHome}/${fair.id}`)}
				>
					{@const row = formatFair(fair)}
					{#each row as cellValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							content={cellValue}
							classes={{
								text: classNames({
									'text-red-500': j === row.length - 1 && cellValue === 'Yes',
								}),
							}}
						>
							{cellValue}
						</TableCell>
					{/each}
				</TableRow>
			{/each}

			{#if !fairs.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No fairs to display</TableNoResults
				>
			{/if}
		</TableBody>
	{/if}
</table>

{#if $query.isFetching}
	<div class="mt-3 flex justify-center">
		<CircularProgress dataCy={dataCyPrefix} />
	</div>
{:else}
	<div class="mt-2 flex justify-end">
		{#key pageNumber}
			{#key count}
				<Pagination
					onClick={onClickPage}
					dataCy={dataCyPrefix}
					currentPage={pageNumber}
					limit={FAIRS_LIMIT}
					total={count}
				/>
			{/key}
		{/key}
	</div>
{/if}
