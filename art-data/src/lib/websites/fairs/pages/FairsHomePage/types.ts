export enum FairsFieldNames {
	Search = 'search',
	From = 'from',
	To = 'to',
	Status = 'status',
	Priority = 'priority',
	Sort = 'sort',
	Page = 'page',
}

export const FAIRS_SORT_OPTIONS = [
	{ label: 'DATE RECEIVED ASC', value: 'date_created' },
	{ label: 'DATE RECEIVED DESC', value: '-date_created' },
	{ label: 'DATE UPDATED ASC', value: 'date_updated' },
	{ label: 'DATE UPDATED DESC', value: '-date_updated' },
	{ label: 'TITLE ASC', value: 'processed_fair.title' },
	{ label: 'TITLE DESC', value: '-processed_fair.title' },
	{ label: 'START DATE ASC', value: 'processed_fair.start_date' },
	{ label: 'START DATE DESC', value: '-processed_fair.start_date' },
	{ label: 'END DATE ASC', value: 'processed_fair.end_date' },
	{ label: 'END DATE DESC', value: '-processed_fair.end_date' },
];
