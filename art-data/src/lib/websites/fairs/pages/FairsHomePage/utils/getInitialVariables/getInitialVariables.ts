import { FairsFieldNames, FAIRS_SORT_OPTIONS } from '../../types';
import { EVENT_STATUS_OPTIONS } from '$lib/constants/event-status';
import { PRIORITY_OPTIONS } from '$lib/constants/priority';

export const getInitialVariables = (searchParams: URLSearchParams) => {
	const queryParams = Object.fromEntries(searchParams);

	return {
		[FairsFieldNames.Page]: +queryParams[FairsFieldNames.Page] || 1,
		[FairsFieldNames.Search]: queryParams[FairsFieldNames.Search] || '',
		[FairsFieldNames.Priority]:
			queryParams[FairsFieldNames.Priority] || PRIORITY_OPTIONS[0].value,
		[FairsFieldNames.Status]:
			queryParams[FairsFieldNames.Status] || EVENT_STATUS_OPTIONS[0].value,
		[FairsFieldNames.Sort]:
			queryParams[FairsFieldNames.Sort] || FAIRS_SORT_OPTIONS[1].value,
		[FairsFieldNames.From]: queryParams[FairsFieldNames.From],
		[FairsFieldNames.To]: queryParams[FairsFieldNames.To],
	};
};
