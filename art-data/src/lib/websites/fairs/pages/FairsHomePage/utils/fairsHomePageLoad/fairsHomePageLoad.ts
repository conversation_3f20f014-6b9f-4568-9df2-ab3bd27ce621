import { type QueryClient } from '@tanstack/svelte-query';
import dayjs from 'dayjs';
import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { EventStatus } from '$lib/constants/event-status';
import { Priority } from '$lib/constants/priority';
import { getQuery } from '$lib/query-utils/getQuery';
import {
	GetFairWithChecklistsDocument,
	type GetFairWithChecklistsQueryVariables,
} from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsHomePageLoadEvent } from '$routes/fairs/types';

export const FAIRS_LIMIT = 20;
export const FAIRS_STALE_TIME = 1000 * 60 * 60;

export const fairsHomePageLoad = async ({
	parent,
	url,
}: FairsHomePageLoadEvent) => {
	const parentData = await parent();
	const queryParamsVariables = getInitialVariables(url.searchParams);
	const queryClient: QueryClient = parentData.queryClient;

	const statusFilter: NonNullable<
		GetFairWithChecklistsQueryVariables['filter']
	>['_and'] =
		queryParamsVariables.status &&
		queryParamsVariables.status !== EventStatus.All
			? [
					{
						event_review_status: {
							key: { _eq: queryParamsVariables.status },
						},
					},
				]
			: [];

	const searchFilter: NonNullable<
		GetFairWithChecklistsQueryVariables['filter']
	>['_and'] = queryParamsVariables.search
		? [
				{
					_or: [
						{
							processed_fair: {
								title: { _icontains: queryParamsVariables.search },
							},
						},
						{
							processed_fair: {
								location: { _icontains: queryParamsVariables.search },
							},
						},
					],
				},
			]
		: [];

	const priorityHighFilter: NonNullable<
		GetFairWithChecklistsQueryVariables['filter']
	>['_and'] =
		queryParamsVariables.priority === Priority.High
			? [
					{
						_or: [
							{
								ingestion_data: {
									item__PDF: { high_priority: { _eq: true } },
								},
							},
							{
								ingestion_data: {
									item__Manual_Upload: { high_priority: { _eq: true } },
								},
							},
							{
								ingestion_data: {
									item__Artlogic_Link: { high_priority: { _eq: true } },
								},
							},
						],
					},
				]
			: [];

	const datesFilter: NonNullable<
		GetFairWithChecklistsQueryVariables['filter']
	>['_and'] = (() => {
		if (queryParamsVariables.from && queryParamsVariables.to) {
			return [
				{
					_and: [
						{
							date_created: {
								_gte: dayjs(queryParamsVariables.from).toISOString(),
							},
						},
						{
							date_created: {
								_lte: dayjs(queryParamsVariables.to).endOf('day').toISOString(),
							},
						},
					],
				},
			];
		}

		if (queryParamsVariables.from) {
			return [
				{
					date_created: {
						_gte: dayjs(queryParamsVariables.from).toISOString(),
					},
				},
			];
		}

		if (queryParamsVariables.to) {
			return [
				{
					date_created: {
						_lte: dayjs(queryParamsVariables.to).endOf('day').toISOString(),
					},
				},
			];
		}

		return [];
	})();

	const queryVariables: GetFairWithChecklistsQueryVariables = {
		sort: [queryParamsVariables.sort],
		limit: FAIRS_LIMIT,
		...(queryParamsVariables.page && {
			offset: FAIRS_LIMIT * (queryParamsVariables.page - 1),
		}),
		filter: {
			_and: [
				...searchFilter,
				...priorityHighFilter,
				...datesFilter,
				...statusFilter,
			],
		},
	};

	queryClient.prefetchInfiniteQuery({
		...getQuery(
			GetFairWithChecklistsDocument,
			queryVariables,
			getAuthorizationHeaders(parentData)
		),
		initialPageParam: 0,
		staleTime: FAIRS_STALE_TIME,
	});

	return {
		...parentData,
		queryVariables,
	};
};
