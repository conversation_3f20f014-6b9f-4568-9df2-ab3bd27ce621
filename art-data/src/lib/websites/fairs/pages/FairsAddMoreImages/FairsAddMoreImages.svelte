<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddMoreImages } from '$lib/features/add-more-images/components/AddMoreImages';
	import type { FairsIdImagesAddPageData } from '$routes/fairs/[id]/images/add/types';

	let data = $derived(getPageData<FairsIdImagesAddPageData>(page.data));
	let fair = $derived(data.fair);
	let users = $derived(data.users);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'Add More Images' },
	]);
</script>

<AddMoreImages
	dataCy="fairs"
	{users}
	visitId={`${fair?.visit?.id}`}
	title={`${fair?.processed_fair?.title}`}
	{crumbs}
	backButtonLabel="Back to fair page"
/>
