<script lang="ts">
	import { FairsDetails } from '../../components/FairsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AddTextToImages } from '$lib/features/add-text-to-images/components/AddTextToImages';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import type { FairsIdManualUploadManualUploadIdAddDataPageData } from '$routes/fairs/[id]/manual-upload/[manualUploadId]/add-data/types';

	const dataCyPrefix = 'fairs-add-text-to-images';

	let data = $derived(
		getPageData<FairsIdManualUploadManualUploadIdAddDataPageData>(page.data)
	);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${data?.fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Add Text To Images' },
	]);

	const buttonProps = {
		label: 'Back to Fair Page',
		href: `${Routes.FairsHome}/${page.params.id}`,
	};

	const button2Props = {
		label: 'back to Fairs',
		href: `${Routes.FairsHome}`,
	};
</script>

<ArtworkTextTableContext>
	<PageBody>
		<AddTextToImages
			{crumbs}
			detailsString="Fair details"
			title={`${data?.fair?.processed_fair?.title}`}
			slotLabel="Fair the information is going to be added to"
			dataCy={dataCyPrefix}
			{data}
			{buttonProps}
			{button2Props}
			{artworkSaleStatuses}
			artist={null}
		>
			{#if data.fair?.processed_fair}
				<FairsDetails
					dataCy={dataCyPrefix}
					fair={{
						title: data?.fair?.processed_fair?.title,
						id: `${data?.fair?.processed_fair?.processed_fair_id}`,
						local_start_date: data?.fair?.processed_fair?.start_date,
						local_end_date: data?.fair?.processed_fair?.end_date,
						venue_city: {
							code: `${data?.fair?.processed_fair?.location}`,
							name: `${data?.fair?.processed_fair?.location}`,
						},
					}}
				/>
			{/if}
		</AddTextToImages>
	</PageBody>
</ArtworkTextTableContext>
