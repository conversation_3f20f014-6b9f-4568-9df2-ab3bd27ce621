import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { addTextToImagesServerLoad } from '$lib/features/add-text-to-images/utils/addTextToImagesServerLoad/addTextToImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsIdManualUploadManualUploadIdAddDataPageServerLoadEvent } from '$routes/fairs/[id]/manual-upload/[manualUploadId]/add-data/types';

export const fairsAddTextToImagesServerLoad = async ({
	parent,
	params,
}: FairsIdManualUploadManualUploadIdAddDataPageServerLoadEvent) => {
	const data = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];

	const addTextToImagesServerLoadResponse = await addTextToImagesServerLoad(
		params.manualUploadId,
		data
	);

	return {
		...data,
		...addTextToImagesServerLoadResponse,
		fair,
	};
};
