<script lang="ts">
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { ViewAllImages } from '$lib/features/view-all-images/components/ViewAllImages';
	import type { FairsIdImagesViewPageData } from '$routes/fairs/[id]/images/view/types';

	const dataCy = 'fairs-view-all-images';

	let data = $derived(getPageData<FairsIdImagesViewPageData>(page.data));
	let images = $derived(data.images);
	let users = $derived(data.users);
	let total = $derived(data.total);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${data.fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{ label: 'View all images' },
	]);
</script>

<PageBody>
	<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
	<ViewAllImages
		title={`${data.fair?.processed_fair?.title}`}
		{dataCy}
		{images}
		{users}
		{total}
		backButtonLabel="Back to fair page"
		backButtonHref={`${Routes.FairsHome}/${page.params.id}`}
	/>
</PageBody>
