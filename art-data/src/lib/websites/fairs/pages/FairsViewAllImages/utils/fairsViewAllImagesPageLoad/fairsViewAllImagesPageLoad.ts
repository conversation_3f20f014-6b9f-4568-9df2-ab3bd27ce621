import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { viewAllImagesPageLoad } from '$lib/features/view-all-images/utils/viewAllImagesPageLoad/viewAllImagesPageLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';
import type { ExhibitionsIdImagesViewPageLoadEvent } from '$routes/exhibitions/[id]/images/view/types';

export const VISIT_IMAGES_LIMIT = 20;

export const fairsViewAllImagesPageLoad = async ({
	parent,
	url,
	params,
}: ExhibitionsIdImagesViewPageLoadEvent) => {
	const data = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const visitId = fair?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const viewAllImagesResponse = await viewAllImagesPageLoad({
		url,
		data,
		visitId,
	});

	return {
		fair,
		...viewAllImagesResponse,
	};
};
