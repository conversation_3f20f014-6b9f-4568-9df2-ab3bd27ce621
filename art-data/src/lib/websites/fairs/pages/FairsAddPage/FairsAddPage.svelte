<script lang="ts">
	import { FairsAddForm } from './FairsAddForm';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'fairs-home';

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{ label: 'Add New Fairs' },
	];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<FairsAddForm />
</PageBody>
