<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import dayjs from 'dayjs';
	import customParseFormat from 'dayjs/plugin/customParseFormat';
	import { writable } from 'svelte/store';
	import { CannotFindFair } from './CannotFindFair';
	import { page } from '$app/state';
	import { Checkbox } from '$global/components/Checkbox';
	import { InputLabel } from '$global/components/InputLabel';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepDescription } from '$global/features/form/components/StepDescription';
	import { StepError } from '$global/features/form/components/StepError';
	import { StepTitle } from '$global/features/form/components/StepTitle';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { RemovableSelectedLinkOption } from '$lib/components/RemovableSelectedLinkOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { getMutation } from '$lib/query-utils/getMutation';
	import {
		GetArteyeFairsDocument,
		type GetArteyeFairsQuery,
	} from '$lib/websites/fairs/arteye-queries/__generated__/getArteyeFairs.generated';
	import { CreateIngestionFairDocument } from '$lib/websites/fairs/queries/__generated__/createIngestionFair.generated';
	import { CreateProcessedFairDocument } from '$lib/websites/fairs/queries/__generated__/createProcessedFair.generated';
	import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';
	import { GetProcessedFairDocument } from '$lib/websites/fairs/queries/__generated__/getProcessedFair.generated';
	import { getFairDetails } from '$lib/websites/fairs/utils/getFairDetails/getFairDetails';
	import type { FairsAddPageData } from '$routes/fairs/add/types';

	dayjs.extend(customParseFormat);

	const dataCyPrefix = 'fairs-add';

	let fairExistsError = $state(false);
	let priority = $state(false);
	let submitting = $state(false);
	let value = $state(writable(''));
	let selectedOption: OptionType | null = $state(null);

	const getVariables = (value: string) => {
		return {
			limit: 10,
			filter: {
				_and: [
					{
						_or: [
							...(value ? [{ title: { _icontains: value } }] : []),
							...(value
								? [{ venue_city: { name: { _icontains: value } } }]
								: []),
							...(value && isUuidValid(value) ? [{ id: { _eq: value } }] : []),
						],
					},
					{ status: { key: { _neq: 'archived' } } },
				],
			},
		};
	};

	const createProcessedFair = createMutation(
		getMutation(
			CreateProcessedFairDocument,
			getAuthorizationHeaders(page.data as FairsAddPageData)
		)
	);

	const createIngestionFair = createMutation(
		getMutation(
			CreateIngestionFairDocument,
			getAuthorizationHeaders(page.data as FairsAddPageData)
		)
	);

	const getOptions = (data: GetArteyeFairsQuery | undefined) => {
		return (data?.fair || [])?.map(getFairDetails);
	};

	// TODO art-data: priority cannot be handled
	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		const urlParts = selectedOption?.line2?.split('/') || [];
		const id = urlParts?.[urlParts?.length - 1];

		submitting = true;
		fairExistsError = false;

		const getIngestionFairResponse = await gqlClient.request(
			GetFairsDocument,
			{
				filter: {
					processed_fair: {
						processed_fair_id: { _eq: id },
					},
				},
			},
			getAuthorizationHeaders(page.data as FairsAddPageData)
		);

		if (getIngestionFairResponse?.Ingestion_Fair?.[0]) {
			submitting = false;
			fairExistsError = true;
			return;
		}

		let processedFair;

		const getProcessedFairResponse = await gqlClient.request(
			GetProcessedFairDocument,
			{
				filter: { processed_fair_id: { _eq: id } },
			},
			getAuthorizationHeaders(page.data as FairsAddPageData)
		);

		processedFair = getProcessedFairResponse?.Processed_Fair?.[0];

		if (!processedFair) {
			const createProcessedFairResponse =
				await $createProcessedFair.mutateAsync({
					data: {
						processed_fair_id: `${id}`,
						title: `${selectedOption?.line1}`,
						...(selectedOption?.line6 && {
							start_date: selectedOption?.line6,
						}),
						...(selectedOption?.line7 && {
							end_date: selectedOption?.line7,
						}),
						location: selectedOption?.line3,
					},
				});

			processedFair = createProcessedFairResponse?.create_Processed_Fair_item;
		}

		if (!processedFair) {
			submitting = false;
			return;
		}

		const response = await $createIngestionFair.mutateAsync({
			input: {
				is_priority: priority,
				processed_fair: {
					title: `${selectedOption?.line1}`,
					processed_fair_id: `${id}`,
				},
			},
		});

		submitting = false;
		(window as unknown as { location: string }).location =
			`${Routes.FairsHome}/${response?.create_Ingestion_Fair_item?.id}`;
	};
</script>

<div>
	<StepTitle>Add data about a fair</StepTitle>
	<StepDescription>
		Try searching for the fair below. If you cannot find the fair, click "create
		new" to add it on Arteye, then return here to select it in the search field
		below.
	</StepDescription>
	<StepContainer>
		<form onsubmit={handleSubmit} class="col-span-2 mb-[4rem] sm:mb-[7rem]">
			<QueryAutocomplete
				label="Search for fair"
				name="fair"
				dataCy={`${dataCyPrefix}-fair`}
				placeholder="Start typing to search fair title, city or ID"
				document={GetArteyeFairsDocument}
				graphQlClient={gqlClientArteye}
				{getOptions}
				{getVariables}
				required
				requestHeaders={{
					authorization: `Bearer ${page.data.user.arteye_token}`,
				}}
				classes={{
					listWithOptions: 'pb-[3rem]',
					longList: '!max-h-[244px] !min-h-[244px]',
					option: {
						line3: 'w-fit',
						line4: 'float-left inline',
						line5: 'float-left inline ml-1',
						line6: 'hidden',
						line7: 'hidden',
					},
					selectedOption: {
						line3: 'w-fit',
						line4: 'float-left inline',
						line5: 'float-left inline ml-1',
						line6: 'hidden',
						line7: 'hidden',
					},
				}}
				OptionComponent={LinkOption}
				SelectedOptionComponent={RemovableSelectedLinkOption}
				{value}
				bind:selectedOption
			>
				{#snippet list()}
					<div
						class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
					>
						<CannotFindFair />
					</div>
				{/snippet}

				{#snippet noResults()}
					<div class="flex flex-col items-center">
						<NoResults class="mb-2" dataCy={`${dataCyPrefix}-fair-autocomplete`}
							>No fair found</NoResults
						>
						<CannotFindFair />
					</div>
				{/snippet}
			</QueryAutocomplete>

			<InputLabel
				dataCy={`${dataCyPrefix}-priority`}
				variant="body3"
				class="mt-3"
				classes={{ wrapper: 'items-start' }}
			>
				<Checkbox
					name="terms"
					class="mt-[-0.125rem]"
					bind:checked={priority}
					dataCy={`${dataCyPrefix}-terms-label`}
				/>
				<span>This fair is a priority</span>
			</InputLabel>

			<StepButtons
				backButtonProps={{
					href: Routes.FairsHome,
				}}
				continueButtonProps={{
					disabled: !selectedOption,
					loading: submitting,
				}}
			>
				Continue
				{#snippet error()}
					<span>
						{#if fairExistsError}
							<StepError class="normal-case">
								This fair has already been added to an Ingestion Fair. Please
								search for it on the fairs home page.
							</StepError>
						{/if}
					</span>
				{/snippet}
			</StepButtons>
		</form>
	</StepContainer>
</div>
