import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { manualUploadfinalReviewServerLoad } from '$lib/features/final-review/utils/manualUploadFinalReviewServerLoad/manualUploadFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { FairsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent } from '$routes/fairs/[id]/manual-upload/[manualUploadId]/review-artworks/types';

export const fairsManualUploadFinalReviewServerLoad = async ({
	parent,
	params,
}: FairsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionFairResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const name = `${ingestionFairResponse?.Ingestion_Fair?.[0]?.processed_fair?.title}`;

	const finalReviewResponse = await manualUploadfinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name,
	};
};
