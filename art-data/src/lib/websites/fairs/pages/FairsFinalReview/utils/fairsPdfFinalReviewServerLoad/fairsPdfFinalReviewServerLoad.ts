import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { pdfFinalReviewServerLoad } from '$lib/features/final-review/utils/pdfFinalReviewServerLoad/pdfFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { ExhibitionsIdPdfsPdfIdReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/pdfs/[pdfId]/review-artworks/types';

export const fairsPdfFinalReviewServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPdfsPdfIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionFairResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const name = `${ingestionFairResponse?.Ingestion_Fair?.[0]?.processed_fair?.title}`;

	const finalReviewResponse = await pdfFinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
