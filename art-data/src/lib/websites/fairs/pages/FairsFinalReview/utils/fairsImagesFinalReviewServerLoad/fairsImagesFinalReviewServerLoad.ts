import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { imagesFinalReviewServerLoad } from '$lib/features/final-review/utils/imagesFinalReviewServerLoad/imagesFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetFairWithChecklistsDocument } from '$lib/websites/fairs/queries/__generated__/getFairsWithChecklists.generated';
import type { ExhibitionsIdImagesReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/images/review-artworks/types';

export const fairsImagesFinalReviewServerLoad = async ({
	parent,
	params,
	url,
}: ExhibitionsIdImagesReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const ingestionFairResponse = await gqlClient.request(
		GetFairWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const fair = ingestionFairResponse?.Ingestion_Fair?.[0];
	const name = `${fair?.processed_fair?.title}`;
	const visitId = fair?.visit?.id;

	if (!visitId) {
		error(404, 'No visit attached to this fair');
	}

	const finalReviewResponse = await imagesFinalReviewServerLoad({
		parent,
		date: dateParam,
		photographerId: photographerParam,
		visitId,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
