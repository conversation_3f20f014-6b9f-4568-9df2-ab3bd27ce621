import { gql } from 'graphql-tag';

export const GET_FAIR_WITH_CHECKLISTS = gql`
	query getFairWithChecklists(
		$filter: Ingestion_Fair_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		Ingestion_Fair_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
		Ingestion_Fair(
			filter: $filter
			sort: $sort
			limit: $limit
			offset: $offset
		) {
			id
			date_created
			visit {
				id
				artworks {
					id
					status {
						key
					}
				}
				visit_images(limit: 10000) {
					id
					image_taken_date
					photographer {
						id
						last_name
						first_name
					}
					user_created {
						id
						last_name
						first_name
					}
					status {
						key
						name
					}
					source
					extracted_best_guess_coordinates_from_api
					data_admin_submitted_coordinates
					perspective_cropped_image_without_dimensions {
						id
					}
					rectangular_cropped_image {
						id
					}
					original_uncropped_image {
						id
					}
				}
			}
			is_priority
			processed_fair {
				processed_fair_id
				title
				start_date
				end_date
				location
			}

			matches: ingestion_data {
				item {
					... on Manual_Upload {
						manually_added_artworks(
							filter: { status: { key: { _nin: ["DRAFT"] } } }
						) {
							id
						}
					}
					... on PDF {
						artworks(filter: { status: { key: { _nin: ["DRAFT"] } } }) {
							id
						}
					}
				}
			}

			ready_for_review: ingestion_data {
				item {
					... on Manual_Upload {
						manually_added_artworks(
							filter: { status: { key: { _in: ["AWAITING_REVIEW"] } } }
						) {
							id
						}
					}
					... on PDF {
						artworks(
							filter: { status: { key: { _in: ["AWAITING_REVIEW"] } } }
						) {
							id
						}
					}
				}
			}

			skipped: ingestion_data {
				item {
					... on Manual_Upload {
						manually_added_artworks(
							filter: { status: { key: { _in: ["SKIPPED"] } } }
						) {
							id
						}
					}
					... on PDF {
						artworks(filter: { status: { key: { _in: ["SKIPPED"] } } }) {
							id
						}
					}
				}
			}

			reviewed_and_submitted: ingestion_data {
				item {
					... on Manual_Upload {
						manually_added_artworks(
							filter: {
								status: {
									key: {
										_in: [
											"REVIEWED_AND_SUBMITTED"
											"COMPLETED"
											"AWAITING_INGESTION"
											"INGESTION_FAILED"
										]
									}
								}
							}
						) {
							id
						}
					}
					... on PDF {
						artworks(
							filter: {
								status: {
									key: {
										_in: [
											"REVIEWED_AND_SUBMITTED"
											"COMPLETED"
											"AWAITING_INGESTION"
											"INGESTION_FAILED"
										]
									}
								}
							}
						) {
							id
						}
					}
				}
			}

			event_review_status {
				key
				name
			}

			ingestion_data {
				item {
					... on Manual_Upload {
						review_status {
							key
							name
						}
						__typename
						id
						submitted_for_review
						user_created {
							last_name
							first_name
						}
						manually_added_artworks {
							id
							status {
								key
							}
						}
						title
						for_freelancers
						high_priority
						includes_prices
						receipt_info {
							receive_date
							receiver
							sender
						}
						processed_fair_exhibitor_org {
							entity
							id
							location
							name
							type
						}
					}
					... on PDF {
						review_status {
							key
							name
						}
						__typename
						processed_fair_exhibitor_org {
							entity
							id
							location
							name
							type
						}
						includes_prices
						submitted_for_review
						receipt_info {
							receive_date
							sender
							receiver
						}
						pdf_file {
							filename_disk
						}
						user_created {
							first_name
							last_name
						}
						for_freelancers
						high_priority
						id
						title
						artworks {
							id
							status {
								key
							}
						}
						status {
							key
						}
					}
					... on Artlogic_Link {
						review_status {
							key
							name
						}
						__typename
						id
						title
						high_priority
						includes_prices
						status {
							name
							key
						}
						processed_gallery {
							name
						}
						user_created {
							first_name
							last_name
						}
						art_event_feed {
							artwork_feed {
								id
							}
						}
						url
						receipt_info {
							id
							receive_date
							receiver
							sender
						}
					}
				}
			}
		}
	}
`;
