import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetProcessedFairQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Processed_Fair_Filter>;
}>;

export type GetProcessedFairQuery = {
	__typename?: 'Query';
	Processed_Fair: Array<{
		__typename?: 'Processed_Fair';
		processed_fair_id: string;
		title: string;
	}>;
};

export const GetProcessedFairDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getProcessedFair' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Processed_Fair_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Processed_Fair' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetProcessedFairQuery,
	GetProcessedFairQueryVariables
>;
