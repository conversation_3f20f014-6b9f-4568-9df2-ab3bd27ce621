import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateProcessedFairMutationVariables = Types.Exact<{
	data: Types.Create_Processed_Fair_Input;
}>;

export type CreateProcessedFairMutation = {
	__typename?: 'Mutation';
	create_Processed_Fair_item?: {
		__typename?: 'Processed_Fair';
		processed_fair_id: string;
		title: string;
	} | null;
};

export const CreateProcessedFairDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createProcessedFair' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_Processed_Fair_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_Processed_Fair_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateProcessedFairMutation,
	CreateProcessedFairMutationVariables
>;
