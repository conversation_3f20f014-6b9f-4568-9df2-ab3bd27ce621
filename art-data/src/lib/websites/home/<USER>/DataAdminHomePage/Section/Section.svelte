<script lang="ts">
	import { type Component } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import { Txt } from '$global/components/Txt';

	interface Props {
		IconComponent: Component;
		title: string;
		href: string;
		children?: import('svelte').Snippet;
		class?: string;
	}

	let { IconComponent, ...props }: Props = $props();
</script>

<a
	href={props.href}
	class={twMerge(
		'rounded border border-gray-200 bg-gray-0 p-6 pb-7',
		props.class
	)}
>
	<div class="mb-2 flex items-center gap-2">
		<IconComponent />
		<Txt variant="h6">
			{props.title}
		</Txt>
	</div>
	<Txt variant="body2">
		{@render props.children?.()}
	</Txt>
</a>
