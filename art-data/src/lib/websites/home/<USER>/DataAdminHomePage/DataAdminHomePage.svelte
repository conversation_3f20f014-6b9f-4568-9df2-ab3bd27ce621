<script lang="ts">
	import { Section } from './Section';
	import { ExhibitionIcon } from '$global/assets/icons/ExhibitionIcon';
	import { FairIcon } from '$global/assets/icons/FairIcon';
	import { GalleryOfferingIcon } from '$global/assets/icons/GalleryOfferingIcon';
	import { LaptopIcon } from '$global/assets/icons/LaptopIcon';
	import { PhoneIcon } from '$global/assets/icons/PhoneIcon';
	import { Container } from '$global/components/Container';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'home';
</script>

<PageBody class="bg-gray-100 pt-16">
	<Container dataCy={dataCyPrefix}>
		<Txt variant="h3" class="mb-2">Welcome back</Txt>
		<Txt variant="body1" class="mb-10">
			Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi.
			Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis
			ligula consectetur, ultrices mauris. Maecenas vitae mattis tellus. Nullam
			quis imperdiet augue. Vestibulum auctor ornare leo, non suscipit magna
			interdum eu. Curabitur pellentesque nibh nibh, at maximus ante.
		</Txt>
		<Txt variant="h5" class="mb-4">Sections</Txt>
		<div class="grid grid-cols-3 gap-6">
			<Section
				href={Routes.ExhibitionsHome}
				IconComponent={ExhibitionIcon}
				title="Exhibitions"
			>
				Add new and review exhibition data including photos from exhibition
				visits, exhibition checklists and artlogic links
			</Section>

			<Section
				href={Routes.FairsHome}
				IconComponent={FairIcon}
				title="Art fairs"
			>
				Add new and review fair data including photos from fair visits, fair
				checklists and artlogic links
			</Section>

			<Section
				href={Routes.GalleryOfferingsHome}
				IconComponent={GalleryOfferingIcon}
				title="Gallery offerings"
			>
				Add new and review gallery offering data including photos from
				exhibition visits, exhibition checklists and artlogic links
			</Section>

			<Section
				href={Routes.ScrapedData}
				IconComponent={LaptopIcon}
				title="Scraped Data"
			>
				View and process scraped data into Arteye. This was previously referred
				to as the 'Arteye Processor'.
			</Section>

			<Section
				href={Routes.Phones}
				IconComponent={PhoneIcon}
				title="Phones for events"
			>
				View, register and authenticate phones that are going to be taken to
				different events including fairs, exhibitions and gallery visits.
			</Section>
		</div>
	</Container>
</PageBody>
