import { redirect } from '@sveltejs/kit';
import { Roles } from '$lib/constants/roles';
import { Routes } from '$lib/constants/routes';
import type { HomePageServerLoadEvent } from '$routes/types';

export const dataAdminHomePageServerLoad = async ({
	parent,
}: HomePageServerLoadEvent) => {
	const parentData = await parent();

	// if (parentData.user.role !== Roles.Admin) {
	// 	redirect(303, Routes.WelcomeFreelancer);
	// }

	return parentData;
};
