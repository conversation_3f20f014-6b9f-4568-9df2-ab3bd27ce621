<script lang="ts">
	import { AvailableChecklists } from './AvailableChecklists';
	import { CompletedChecklists } from './CompletedChecklists';
	import { Container } from '$global/components/Container';
	import { Tabs } from '$global/components/Tabs';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';

	let activeTab = $state(0);

	const dataCyPrefix = 'home-freelancer';
	const tabs = [
		{ id: '1', title: 'Available Checklists' },
		{ id: '2', title: 'Completed Checklists' },
	];
</script>

<PageBody class="pt-16">
	<Container dataCy={dataCyPrefix}>
		<Txt variant="h3" class="mb-2">Welcome back</Txt>
		<Txt variant="body1" class="mb-10">
			Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi.
			Aliquam in hendrerit urna. Pellentesque sit amet sapien fringilla, mattis
			ligula consectetur, ultrices mauris. Maecenas vitae mattis tellus. Nullam
			quis imperdiet augue. Vestibulum auctor ornare leo, non suscipit magna
			interdum eu. Curabitur pellentesque nibh nibh, at maximus ante.
		</Txt>

		<Tabs
			dataCy={dataCyPrefix}
			bind:activeTab
			{tabs}
			fullLine
			classes={{ container: 'mt-6' }}
		>
			{#if activeTab === 0}
				<AvailableChecklists dataCy={dataCyPrefix} />
			{:else if activeTab === 1}
				<CompletedChecklists dataCy={dataCyPrefix} />
			{/if}
		</Tabs>
	</Container>
</PageBody>
