import { Checklists } from '$lib/constants/checklist';
import { Event } from '$lib/constants/event';
import type { HomePageServerLoadEvent } from '$routes/types';

export const freelancerHomePageServerLoad = async ({
	parent,
}: HomePageServerLoadEvent) => {
	const parentData = await parent();

	// if (parentData.user.role !== Roles.Freelancer) {
	// 	redirect(303, Routes.Welcome);
	// }

	const availableChecklists = [
		{
			uploadName: 'Lorem Ipsum',
			receivedDate: '00/00/0000',
			priority: 'Yes',
			id: 1,
			eventType: Event.Exhibition,
			eventId: 1,
			type: Checklists.Pdf,
		},
		{
			uploadName: 'Lorem Ipsum',
			receivedDate: '00/00/0000',
			priority: 'Yes',
			id: 2,
			eventType: Event.Exhibition,
			eventId: 2,
			type: Checklists.Pdf,
		},
	];

	const completedChecklists = [
		{
			uploadName: 'Lorem Ipsum',
			submittedDate: '00/00/0000',
			priority: 'Yes',
			id: 1,
			artworksSubmitted: 32,
		},
		{
			uploadName: 'Lorem Ipsum',
			submittedDate: '00/00/0000',
			priority: 'Yes',
			id: 2,
			artworksSubmitted: 100,
		},
	];

	return {
		...parentData,
		availableChecklists,
		completedChecklists,
	};
};
