<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { twMerge } from 'tailwind-merge';
	import { AssignDialog } from './AssignDialog';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableHeaderRow,
		TableBody,
		TableCell,
		TableHeader,
		getCellWidth,
		TableRow,
		TableActionCell,
	} from '$global/components/Table';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Event } from '$lib/constants/event';
	import { Routes } from '$lib/constants/routes';
	import type { WelcomeFreelancerPageData } from '$routes/welcome-freelancer/types';

	interface Props {
		dataCy: string;
		class?: string;
	}

	let { ...props }: Props = $props();

	let selectedAvailableChecklistIndex = $state(-1);

	const headers = ['Upload Name', 'Received Date', 'Priority', ''];
	const actionCellWidth = '12rem';
	const dialogStores = createDialog();

	const handleClickMatchArtworks = (index: number) => {
		selectedAvailableChecklistIndex = index;
		dialogStores.states.open.set(true);
	};

	const formatAvailableChecklist = (
		checklist: WelcomeFreelancerPageData['availableChecklists'][number]
	) => {
		return [checklist.uploadName, checklist.receivedDate, checklist.priority];
	};

	const getAvailableChecklistHref = (index: number) => {
		const availableChecklist = availableChecklists[index];
		switch (availableChecklist.eventType) {
			case Event.Exhibition:
				return `${Routes.ExhibitionsHome}/${availableChecklist.eventId}/${availableChecklist.type}/${availableChecklist.id}${Routes.MatchArtworks}`;
			case Event.GalleryOffering:
				return `${Routes.GalleryOfferingsHome}/${availableChecklist.eventId}/${availableChecklist.type}/${availableChecklist.id}${Routes.MatchArtworks}`;
			case Event.Fair:
				return `${Routes.FairsHome}/${availableChecklist.eventId}/${availableChecklist.type}/${availableChecklist.id}${Routes.MatchArtworks}`;
			default:
				return null;
		}
	};

	let dataCyPrefix = $derived(`${props.dataCy}-available-checklists`);
	let data = $derived(getPageData<WelcomeFreelancerPageData>(page.data));
	let availableChecklists = $derived(data.availableChecklists);
	let selectedAvailableChecklistHref = $derived(
		selectedAvailableChecklistIndex >= 0
			? getAvailableChecklistHref(selectedAvailableChecklistIndex)
			: null
	);
</script>

<table class={twMerge('w-full table-fixed bg-white', props.class)}>
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each availableChecklists as availableChecklist, i}
			{@const inProgress = !!(i % 2)}

			<TableRow index={i} dataCy={dataCyPrefix}>
				{#each formatAvailableChecklist(availableChecklist) as availableChecklistValue}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						content={availableChecklistValue}
					>
						{availableChecklistValue}
					</TableCell>
				{/each}

				<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
					{#if inProgress}
						<LinkButton
							dataCy={`${dataCyPrefix}-cell`}
							variant="primary"
							icon
							size="xs"
							href={getAvailableChecklistHref(i)}
						>
							continue matching
							{#snippet trailing()}
								<ChevronRightIcon class="h-3 w-3" />
							{/snippet}
						</LinkButton>
					{:else}
						<Button
							dataCy={`${dataCyPrefix}-cell`}
							variant="primary"
							icon
							size="xs"
							onclick={() => handleClickMatchArtworks(i)}
						>
							match artworks
							{#snippet trailing()}
								<ChevronRightIcon class="h-3 w-3" />
							{/snippet}
						</Button>
					{/if}
				</TableActionCell>
			</TableRow>
		{/each}
	</TableBody>
</table>

{#if selectedAvailableChecklistHref}
	<AssignDialog
		href={selectedAvailableChecklistHref}
		{dialogStores}
		dataCy={dataCyPrefix}
	/>
{/if}
