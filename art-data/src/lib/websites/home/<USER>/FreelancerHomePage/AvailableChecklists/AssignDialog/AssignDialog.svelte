<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { goto } from '$app/navigation';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		href: string;
		dataCy: string;
	}

	let { dialogStores, href, dataCy }: Props = $props();

	const dataCyPrefix = `${dataCy}-assign`;

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};

	const handleClickAssign = () => {
		goto(href);
	};
</script>

<Dialog
	title="Assign yourself this job?"
	showOverlay
	{dialogStores}
	dataCy={dataCyPrefix}
	onClose={handleClose}
	class="h-auto max-w-[31.25rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-2 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	<Txt variant="body2" class="mb-6"
		>To begin matching, you must assign this job to yourself. This means no
		other freelancer can work on this batch of data.
	</Txt>

	<div class="grid grid-cols-5 gap-2">
		<Button
			dataCy={`${dataCyPrefix}-cancel`}
			onclick={handleClose}
			class="border-0"
			variant="secondary"
			size="md"
		>
			cancel
		</Button>

		<Button
			size="md"
			class="col-span-4"
			dataCy={`${dataCyPrefix}-assign`}
			onclick={handleClickAssign}
		>
			Assign to me and begin matching
		</Button>
	</div>
</Dialog>
