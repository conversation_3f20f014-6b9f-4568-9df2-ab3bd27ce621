<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { page } from '$app/state';
	import {
		TableHeaderRow,
		TableBody,
		TableCell,
		TableHeader,
		TableRow,
	} from '$global/components/Table';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { WelcomeFreelancerPageData } from '$routes/welcome-freelancer/types';

	interface Props {
		dataCy: string;
		class?: string;
	}

	let { ...props }: Props = $props();

	const headers = [
		'Upload Name',
		'Priority',
		'Artworks Submitted',
		'Date Submitted',
	];

	const formatCompletedChecklist = (
		checklist: WelcomeFreelancerPageData['completedChecklists'][number]
	) => {
		return [
			checklist.uploadName,
			checklist.priority,
			checklist.artworksSubmitted,
			checklist.submittedDate,
		];
	};

	let dataCyPrefix = $derived(`${props.dataCy}-completed-checklists`);
	let data = $derived(getPageData<WelcomeFreelancerPageData>(page.data));
	let completedChecklists = $derived(data.completedChecklists);
</script>

<table class={twMerge('w-full table-fixed bg-white', props.class)}>
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader dataCy={dataCyPrefix} width="25%">
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each completedChecklists as completedChecklist, i}
			<TableRow index={i} dataCy={dataCyPrefix}>
				{#each formatCompletedChecklist(completedChecklist) as completedChecklistValue}
					<TableCell
						dataCy={dataCyPrefix}
						width="25%"
						content={completedChecklistValue}
					>
						{completedChecklistValue}
					</TableCell>
				{/each}
			</TableRow>
		{/each}
	</TableBody>
</table>
