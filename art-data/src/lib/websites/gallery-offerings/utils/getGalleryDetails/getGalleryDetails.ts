import { type GetArteyeGalleriesQuery } from '../../arteye-queries/__generated__/getArteyeGalleries.generated';
import { Config } from '$lib/constants/config';

export const getGalleryDetails = (
	gallery: GetArteyeGalleriesQuery['gallery'][number]
) => ({
	line1: gallery?.organisation?.entity?.name,
	line2: `${Config.ArteyeDomain}/galleries/${gallery?.id}`,
	line3: gallery?.organisation?.location?.name,
});
