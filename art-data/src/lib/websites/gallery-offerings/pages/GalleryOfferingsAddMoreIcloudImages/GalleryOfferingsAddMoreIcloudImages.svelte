<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddMoreIcloudImages } from '$lib/features/add-more-images/components/AddMoreIcloudImages';
	import type { GalleryOfferingsIdImagesAddIcloudPageData } from '$routes/gallery-offerings/[id]/images/add-icloud/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesAddIcloudPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Add More Icloud Images' },
	]);
</script>

<AddMoreIcloudImages dataCy="gallery-offerings" {crumbs} />
