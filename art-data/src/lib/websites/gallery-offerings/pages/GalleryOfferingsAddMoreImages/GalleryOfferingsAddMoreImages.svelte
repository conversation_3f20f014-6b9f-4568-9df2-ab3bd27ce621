<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddMoreImages } from '$lib/features/add-more-images/components/AddMoreImages';
	import type { GalleryOfferingsIdImagesAddPageData } from '$routes/gallery-offerings/[id]/images/add/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesAddPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let users = $derived(data.users);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Add More Images' },
	]);
</script>

<AddMoreImages
	dataCy="fairs"
	{users}
	visitId={`${galleryOffering?.visit?.id}`}
	title={`${galleryOffering?.title}`}
	{crumbs}
	backButtonLabel="Back to gallery offering page"
/>
