import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import type { FairsIdImagesAddPageServerLoadEvent } from '$routes/fairs/[id]/images/add/types';

export const galleryOfferingsAddMoreImagesServerLoad = async ({
	params,
	parent,
}: FairsIdImagesAddPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingResponse = await gqlClient.request(
		GetGalleryOfferingsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingResponse?.Ingestion_Gallery_Offering?.[0];

	if (!galleryOffering.visit) {
		error(404, 'Not found');
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{},
		getAuthorizationHeaders(data)
	);

	const users = usersResponse.users;

	if (!users) {
		error(404, 'Not found');
	}

	return { galleryOffering, users };
};
