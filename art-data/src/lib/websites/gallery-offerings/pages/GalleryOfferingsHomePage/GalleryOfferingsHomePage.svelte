<script lang="ts">
	import { onMount } from 'svelte';
	import { GalleryOfferingsTable } from './GalleryOfferingsTable';
	import {
		GalleryOfferingsFieldNames,
		GALLERY_OFFERINGS_SORT_OPTIONS,
	} from './types';
	import { getInitialVariables } from './utils/getInitialVariables/getInitialVariables';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { SearchIcon } from '$global/assets/icons/SearchIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import type { SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';
	import { EVENT_STATUS_OPTIONS } from '$lib/constants/event-status';
	import { PRIORITY_OPTIONS } from '$lib/constants/priority';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'gallery-offerings-home';

	let isReady = $state(false);

	onMount(() => {
		isReady = true;
	});

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings' },
	];

	const initialVariables = getInitialVariables(page.url.searchParams);
	let statusValue = $state(initialVariables[GalleryOfferingsFieldNames.Status]);
	let fromValue = $state(initialVariables[GalleryOfferingsFieldNames.From]);
	let toValue = $state(initialVariables[GalleryOfferingsFieldNames.To]);
	let searchValue = $state(initialVariables[GalleryOfferingsFieldNames.Search]);
	let priorityValue = $state(
		initialVariables[GalleryOfferingsFieldNames.Priority]
	);
	let sortValue = $state(initialVariables[GalleryOfferingsFieldNames.Sort]);

	let pageQueryParam = $derived(
		page.url.searchParams.get(GalleryOfferingsFieldNames.Page)
	);

	let pageNumber = $derived(pageQueryParam ? +pageQueryParam : 1);

	const getSearchParamsObj = (page?: number | null) => {
		return {
			...(page &&
				page > 1 && {
					[GalleryOfferingsFieldNames.Page]: `${page}`,
				}),
			...(searchValue && {
				[GalleryOfferingsFieldNames.Search]: searchValue,
			}),
			[GalleryOfferingsFieldNames.Status]: statusValue,
			[GalleryOfferingsFieldNames.Priority]: priorityValue,
			[GalleryOfferingsFieldNames.Sort]: sortValue,
			...(fromValue && {
				[GalleryOfferingsFieldNames.From]: fromValue,
			}),
			...(toValue && {
				[GalleryOfferingsFieldNames.To]: toValue,
			}),
		};
	};

	const handleChangeStatus = (e: { detail: { value: string } }) => {
		const searchParamsObj = {
			...getSearchParamsObj(),
			[GalleryOfferingsFieldNames.Status]: e.detail.value,
		};

		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleSubmit = (e: Event | undefined, page?: number | null) => {
		if (e) {
			e.preventDefault();
		}

		const searchParamsObj = getSearchParamsObj(page);
		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleSelectChange =
		(fieldName: GalleryOfferingsFieldNames) => (e: SelectChangeEvent) => {
			const searchParamsObj = {
				...getSearchParamsObj(),
				[fieldName]: e.detail.value,
			};

			const searchParamsString = new URLSearchParams(
				searchParamsObj
			).toString();

			goto(`?${searchParamsString}`, { noScroll: true });
		};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0 max-w-[1500px]"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-[1500px]">
		<div class="mb-6 flex items-center justify-between">
			<Txt variant="h3">Gallery Offerings</Txt>
			<LinkButton
				size="md"
				dataCy={`${dataCyPrefix}-add-offering`}
				icon
				href={Routes.GalleryOfferingsAdd}
			>
				add new offering
				{#snippet trailing()}
					<PlusIcon color="gray-0" />
				{/snippet}
			</LinkButton>
		</div>

		<form
			onsubmit={(e: Event) => handleSubmit(e, null)}
			class="mb-6 flex justify-between gap-4"
		>
			<button type="submit" class="hidden" aria-label="hidden"></button>
			<div class="flex gap-4">
				<div class="relative">
					<div class="absolute left-2 top-1/2 z-10 -translate-y-1/2">
						<SearchIcon />
					</div>
					<Input
						class="w-[21.5rem] pl-[2.5rem]"
						name={GalleryOfferingsFieldNames.Search}
						dataCy={`${dataCyPrefix}-search`}
						placeholder="Search by gallery name or offering title"
						bind:value={searchValue}
					/>
				</div>
			</div>

			<div class="flex gap-4">
				<div class="flex items-center gap-4">
					<Txt variant="label3">Received date:</Txt>
					<Input
						bind:value={fromValue}
						placeholder="from"
						name={GalleryOfferingsFieldNames.From}
						dataCy={`${dataCyPrefix}-from`}
						type="date"
						class="w-[9rem]"
					/>
				</div>
				<Input
					bind:value={toValue}
					placeholder="to"
					name={GalleryOfferingsFieldNames.To}
					dataCy={`${dataCyPrefix}-to`}
					type="date"
					class="w-[9rem]"
				/>
				<Select
					onchange={handleSelectChange(GalleryOfferingsFieldNames.Priority)}
					bind:value={priorityValue}
					ariaLabel="Filter by priority"
					name={GalleryOfferingsFieldNames.Priority}
					dataCy={`${dataCyPrefix}-priority`}
					selectedPrefix="PRIORITY:"
					options={PRIORITY_OPTIONS}
					classes={{
						placeholder: 'font-medium tracking-widest',
					}}
				/>

				<Select
					onchange={handleChangeStatus}
					bind:value={statusValue}
					ariaLabel="Filter by status"
					name="status"
					dataCy={`${dataCyPrefix}-status`}
					selectedPrefix="STATUS:"
					options={EVENT_STATUS_OPTIONS}
					classes={{
						placeholder: 'font-medium tracking-widest',
					}}
				/>

				<Select
					onchange={handleSelectChange(GalleryOfferingsFieldNames.Sort)}
					bind:value={sortValue}
					ariaLabel="Sort entries"
					name={GalleryOfferingsFieldNames.Sort}
					dataCy={`${dataCyPrefix}-sort`}
					selectedPrefix="SORT BY:"
					options={GALLERY_OFFERINGS_SORT_OPTIONS}
					classes={{
						placeholder: 'font-medium tracking-widest',
					}}
				/>
			</div>
		</form>

		{#if isReady}
			<GalleryOfferingsTable {pageNumber} onClickPage={handleSubmit} />
		{/if}
	</Container>
</PageBody>
