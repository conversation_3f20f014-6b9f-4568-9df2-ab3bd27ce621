<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { onMount } from 'svelte';
	import { GALLERY_OFFERINGS_LIMIT } from '../utils/galleryOfferingsHomePageLoad/galleryOfferingsHomePageLoad';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getInfiniteData } from '$global/utils/infinite-loading/getInfiniteData';
	import { infiniteQuery } from '$global/utils/infinite-loading/infiniteQuery';
	import { Routes } from '$lib/constants/routes';
	import { getGqlClient } from '$lib/utils/getGqlClient/getGqlClient';
	import { hasEventPriorityItems } from '$lib/utils/hasEventPriorityItems/hasEventPriorityItems';
	import type { GetGalleryOfferingsWithChecklistsQuery } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
	import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
	import type { GalleryOfferingsHomePageData } from '$routes/gallery-offerings/types';

	interface Props {
		pageNumber: number;
		onClickPage: (e: Event | undefined, page?: number | null) => void;
	}

	let { pageNumber, onClickPage }: Props = $props();

	let isReady = $state(false);

	let data = $derived(getPageData<GalleryOfferingsHomePageData>(page.data));
	// eslint-disable-next-line svelte/valid-compile
	const gqlClient = getGqlClient(data);
	let queryVariables = $derived(data.queryVariables);

	onMount(() => {
		isReady = true;
	});

	let query = $derived(
		infiniteQuery({
			gqlClient,
			variables: queryVariables,
			document: GetGalleryOfferingsWithChecklistsDocument,
			limit: GALLERY_OFFERINGS_LIMIT,
		})
	);

	let galleryOfferings = $derived(
		getInfiniteData({
			query: $query,
			transform: (data) => {
				return data.Ingestion_Gallery_Offering;
			},
		})
	);

	let count = $derived(
		$query.data?.pages[0]?.Ingestion_Gallery_Offering_aggregated?.[0]
			?.countDistinct?.id || 0
	);

	const actionCellWidth = '0rem';
	const headers = [
		'Offering title',
		'Gallery name',
		'Location',
		'Received date',
		'Has Priority Items',
	];

	const formatGalleryOffering = (
		gallery: GetGalleryOfferingsWithChecklistsQuery['Ingestion_Gallery_Offering'][number]
	) => {
		return [
			gallery.title,
			gallery.processed_gallery?.name,
			gallery.processed_gallery?.location,
			dayjs(gallery.date_created).format('DD/MM/YYYY'),
			hasEventPriorityItems(gallery.ingestion_data) ? 'Yes' : 'No',
		];
	};

	const dataCyPrefix = 'gallery-offerings-home-table';
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>

	{#if galleryOfferings}
		<TableBody dataCy={dataCyPrefix}>
			{#each galleryOfferings as galleryOffering, i}
				<TableRow
					class={classNames('cursor-pointer', {
						'bg-green-100':
							galleryOffering.event_review_status?.key === 'COMPLETED',
						'bg-red-100':
							galleryOffering.event_review_status?.key === 'REQUIRES_ATTENTION',
					})}
					index={i}
					dataCy={dataCyPrefix}
					onclick={() =>
						goto(`${Routes.GalleryOfferingsHome}/${galleryOffering.id}`)}
				>
					{@const row = formatGalleryOffering(galleryOffering)}
					{#each row as cellValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							content={cellValue}
							classes={{
								text: classNames({
									'text-red-500': j === row.length - 1 && cellValue === 'Yes',
								}),
							}}
						>
							{cellValue}
						</TableCell>
					{/each}
				</TableRow>
			{/each}

			{#if !galleryOfferings.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No gallery offerings to display</TableNoResults
				>
			{/if}
		</TableBody>
	{/if}
</table>

{#if $query.isFetching}
	<div class="mt-3 flex justify-center">
		<CircularProgress dataCy={dataCyPrefix} />
	</div>
{:else}
	{#key pageNumber}
		{#key count}
			<div class="mt-2 flex justify-end">
				<Pagination
					onClick={onClickPage}
					dataCy={dataCyPrefix}
					currentPage={pageNumber}
					limit={GALLERY_OFFERINGS_LIMIT}
					total={count}
				/>
			</div>
		{/key}
	{/key}
{/if}
