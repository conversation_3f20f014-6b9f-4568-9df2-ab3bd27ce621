import { type QueryClient } from '@tanstack/svelte-query';
import dayjs from 'dayjs';
import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { EventStatus } from '$lib/constants/event-status';
import { Priority } from '$lib/constants/priority';
import { getQuery } from '$lib/query-utils/getQuery';
import type { GetGalleryOfferingsWithChecklistsQueryVariables } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import type { GalleryOfferingsHomePageLoadEvent } from '$routes/gallery-offerings/types';

export const GALLERY_OFFERINGS_LIMIT = 20;
export const GALLERY_OFFERINGS_STALE_TIME = 1000 * 60 * 60;

export const galleryOfferingsHomePageLoad = async ({
	parent,
	url,
}: GalleryOfferingsHomePageLoadEvent) => {
	const parentData = await parent();
	const queryParamsVariables = getInitialVariables(url.searchParams);
	const queryClient: QueryClient = parentData.queryClient;

	const searchFilter: NonNullable<
		GetGalleryOfferingsWithChecklistsQueryVariables['filter']
	>['_and'] = queryParamsVariables.search
		? [
				{
					_or: [
						{ title: { _icontains: queryParamsVariables.search } },
						{
							processed_gallery: {
								name: { _icontains: queryParamsVariables.search },
							},
						},
					],
				},
			]
		: [];

	const priorityHighFilter: NonNullable<
		GetGalleryOfferingsWithChecklistsQueryVariables['filter']
	>['_and'] =
		queryParamsVariables.priority === Priority.High
			? [
					{
						_or: [
							{
								ingestion_data: {
									item__PDF: { high_priority: { _eq: true } },
								},
							},
							{
								ingestion_data: {
									item__Manual_Upload: { high_priority: { _eq: true } },
								},
							},
							{
								ingestion_data: {
									item__Artlogic_Link: { high_priority: { _eq: true } },
								},
							},
						],
					},
				]
			: [];

	const statusFilter: NonNullable<
		GetGalleryOfferingsWithChecklistsQueryVariables['filter']
	>['_and'] =
		queryParamsVariables.status &&
		queryParamsVariables.status !== EventStatus.All
			? [
					{
						event_review_status: {
							key: { _eq: queryParamsVariables.status },
						},
					},
				]
			: [];

	const datesFilter: NonNullable<
		GetGalleryOfferingsWithChecklistsQueryVariables['filter']
	>['_and'] = (() => {
		if (queryParamsVariables.from && queryParamsVariables.to) {
			return [
				{
					_and: [
						{
							date_created: {
								_gte: dayjs(queryParamsVariables.from).toISOString(),
							},
						},
						{
							date_created: {
								_lte: dayjs(queryParamsVariables.to).endOf('day').toISOString(),
							},
						},
					],
				},
			];
		}

		if (queryParamsVariables.from) {
			return [
				{
					date_created: {
						_gte: dayjs(queryParamsVariables.from).toISOString(),
					},
				},
			];
		}

		if (queryParamsVariables.to) {
			return [
				{
					date_created: {
						_lte: dayjs(queryParamsVariables.to).endOf('day').toISOString(),
					},
				},
			];
		}

		return [];
	})();

	const queryVariables: GetGalleryOfferingsWithChecklistsQueryVariables = {
		sort: [queryParamsVariables.sort],
		limit: GALLERY_OFFERINGS_LIMIT,
		...(queryParamsVariables.page && {
			offset: GALLERY_OFFERINGS_LIMIT * (queryParamsVariables.page - 1),
		}),
		filter: {
			_and: [
				...searchFilter,
				...priorityHighFilter,
				...datesFilter,
				...statusFilter,
			],
		},
	};

	queryClient.prefetchInfiniteQuery({
		...getQuery(
			GetGalleryOfferingsWithChecklistsDocument,
			queryVariables,
			getAuthorizationHeaders(parentData)
		),
		initialPageParam: 0,
		staleTime: GALLERY_OFFERINGS_STALE_TIME,
	});

	return {
		...parentData,
		queryVariables,
	};
};
