import {
	GalleryOfferingsFieldNames,
	GALLERY_OFFERINGS_SORT_OPTIONS,
} from '../../types';
import { EVENT_STATUS_OPTIONS } from '$lib/constants/event-status';
import { PRIORITY_OPTIONS } from '$lib/constants/priority';

export const getInitialVariables = (searchParams: URLSearchParams) => {
	const queryParams = Object.fromEntries(searchParams);

	return {
		[GalleryOfferingsFieldNames.Page]:
			+queryParams[GalleryOfferingsFieldNames.Page] || 1,
		[GalleryOfferingsFieldNames.Search]:
			queryParams[GalleryOfferingsFieldNames.Search] || '',
		[GalleryOfferingsFieldNames.Priority]:
			queryParams[GalleryOfferingsFieldNames.Priority] ||
			PRIORITY_OPTIONS[0].value,
		[GalleryOfferingsFieldNames.Status]:
			queryParams[GalleryOfferingsFieldNames.Status] ||
			EVENT_STATUS_OPTIONS[0].value,
		[GalleryOfferingsFieldNames.Sort]:
			queryParams[GalleryOfferingsFieldNames.Sort] ||
			GALLERY_OFFERINGS_SORT_OPTIONS[1].value,
		[GalleryOfferingsFieldNames.From]:
			queryParams[GalleryOfferingsFieldNames.From],
		[GalleryOfferingsFieldNames.To]: queryParams[GalleryOfferingsFieldNames.To],
	};
};
