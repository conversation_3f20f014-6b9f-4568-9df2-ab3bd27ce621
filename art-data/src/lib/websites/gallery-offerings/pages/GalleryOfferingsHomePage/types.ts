export enum GalleryOfferingsFieldNames {
	From = 'from',
	To = 'to',
	Status = 'status',
	Search = 'search',
	Priority = 'priority',
	Sort = 'sort',
	Page = 'page',
}

export const GALLERY_OFFERINGS_SORT_OPTIONS = [
	{ label: 'DATE RECEIVED ASC', value: 'date_created' },
	{ label: 'DATE RECEIVED DESC', value: '-date_created' },
	{ label: 'DATE UPDATED ASC', value: 'date_updated' },
	{ label: 'DATE UPDATED DESC', value: '-date_updated' },
	{ label: 'TITLE ASC', value: 'title' },
	{ label: 'TITLE DESC', value: '-title' },
	{ label: 'Gallery ASC', value: 'processed_gallery.name' },
	{ label: 'Gallery DESC', value: '-processed_gallery.name' },
];
