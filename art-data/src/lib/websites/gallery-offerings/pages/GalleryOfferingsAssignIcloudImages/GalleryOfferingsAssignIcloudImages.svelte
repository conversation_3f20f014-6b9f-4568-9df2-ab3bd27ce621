<script lang="ts">
	import { GalleryOfferingsDetails } from '../../components/GalleryOfferingsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AssignIcloudImages } from '$lib/features/add-more-images/components/AssignIcloudImages';
	import type { GalleryOfferingsIdImagesAddIcloudAssignPageData } from '$routes/gallery-offerings/[id]/images/add-icloud/assign/types';

	let pageNumber = $state(1);

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesAddIcloudAssignPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let images = $derived(data.images);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Assign Icloud Images' },
	]);
</script>

<PageBody>
	<Breadcrumbs
		dataCy={'assign-icloud-images'}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<AssignIcloudImages
		visitId={galleryOffering?.visit?.id}
		{images}
		title={`${galleryOffering?.title}`}
		bind:pageNumber
		dataCy="gallery-offerings"
	>
		{#if galleryOffering}
			<GalleryOfferingsDetails
				dataCy={'assign-icloud-images'}
				galleryOffering={{
					id: `${galleryOffering?.processed_gallery?.id}`,
					organisation: {
						entity: {
							name: `${galleryOffering?.processed_gallery?.name}`,
						},
						location: {
							name: `${galleryOffering?.processed_gallery?.location}`,
							code: `${galleryOffering?.processed_gallery?.location}`,
						},
					},
				}}
			/>
		{/if}
	</AssignIcloudImages>
</PageBody>
