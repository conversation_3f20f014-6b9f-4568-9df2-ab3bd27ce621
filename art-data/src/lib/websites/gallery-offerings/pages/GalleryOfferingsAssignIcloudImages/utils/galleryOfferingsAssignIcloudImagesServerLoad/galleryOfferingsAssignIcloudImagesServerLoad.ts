import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import type { GalleryOfferingsIdImagesAddIcloudAssignPageServerLoadEvent } from '$routes/gallery-offerings/[id]/images/add-icloud/assign/types';

export const galleryOfferingsAssignIcloudImagesServerLoad = async ({
	params,
	parent,
}: GalleryOfferingsIdImagesAddIcloudAssignPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingResponse = await gqlClient.request(
		GetGalleryOfferingsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingResponse?.Ingestion_Gallery_Offering?.[0];

	if (!galleryOffering.visit) {
		error(404, 'Not found');
	}

	return { galleryOffering };
};
