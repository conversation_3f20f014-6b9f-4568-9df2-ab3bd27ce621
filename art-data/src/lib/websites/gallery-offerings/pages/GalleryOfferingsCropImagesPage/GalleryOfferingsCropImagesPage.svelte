<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ImageCroppingPage } from '$lib/features/crop-images/ImageCroppingPage';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { GalleryOfferingsIdImagesCropPageData } from '$routes/gallery-offerings/[id]/images/crop/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesCropPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let visitImages = $derived(data.visitImages);
	let photographer = $derived(data.photographer);
	let date = $derived(data.date);

	const dataCyPrefix = 'gallery-offerings';

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${galleryOffering.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Image Cropping' },
	]);

	let buttonProps = $derived({
		label: 'back to gallery offering page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	});
</script>

<ImageCroppingPage
	{visitImages}
	{photographer}
	title={`${galleryOffering.title}`}
	{crumbs}
	dataCy={dataCyPrefix}
	{buttonProps}
/>
