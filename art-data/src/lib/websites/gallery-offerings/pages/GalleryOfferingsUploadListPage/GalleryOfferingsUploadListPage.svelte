<script lang="ts">
	import { GalleryOfferingsDetails } from '../../components/GalleryOfferingsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { UploadList } from '$lib/features/upload-list/components/UploadList';

	import {
		type UploadListArtlogicLink,
		type UploadListManualUpload,
		type UploadListPdf,
		IngestionDataTypenames,
	} from '$lib/types';

	import { extractItemsFromIngestionData } from '$lib/utils/extractItemsFromIngestionData/extractItemsFromIngestionData';
	import type { GalleryOfferingsIdPageData } from '$routes/gallery-offerings/[id]/types';

	let data = $derived(getPageData<GalleryOfferingsIdPageData>(page.data));
	let galleryOffering = $derived(data.galleryOffering);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{ label: `${data?.galleryOffering?.processed_gallery?.name}` },
	]);

	let pdfs = $derived(
		extractItemsFromIngestionData(
			data.galleryOffering.ingestion_data,
			data.galleryOffering.ready_for_review,
			data.galleryOffering.reviewed_and_submitted,
			data.galleryOffering.matches,
			data.galleryOffering.skipped,
			IngestionDataTypenames.Pdf
		) as UploadListPdf[]
	);

	let artlogicLinks = $derived(
		extractItemsFromIngestionData(
			data.galleryOffering.ingestion_data,
			data.galleryOffering.ready_for_review,
			data.galleryOffering.reviewed_and_submitted,
			data.galleryOffering.matches,
			data.galleryOffering.skipped,
			IngestionDataTypenames.ArtlogicLink
		) as UploadListArtlogicLink[]
	);

	let manualUploads = $derived(
		extractItemsFromIngestionData(
			data.galleryOffering.ingestion_data,
			data.galleryOffering.ready_for_review,
			data.galleryOffering.reviewed_and_submitted,
			data.galleryOffering.matches,
			data.galleryOffering.skipped,
			IngestionDataTypenames.ManualUpload
		) as UploadListManualUpload[]
	);
</script>

<UploadList
	{crumbs}
	{pdfs}
	visitArtworks={data.visitArtworks}
	visit={galleryOffering.visit}
	{manualUploads}
	{artlogicLinks}
	event="gallery offering"
	dataCy="gallery-offerings"
	subtitle="Gallery offering details"
	title="Gallery Offering Upload List"
	firstTabTitle="Gallery offering checklists"
	collectionName="Ingestion_Gallery_Offering"
>
	{#if galleryOffering?.processed_gallery}
		<GalleryOfferingsDetails
			dataCy="gallery-offerings-upload-list"
			galleryOffering={{
				id: galleryOffering?.processed_gallery?.id,
				organisation: {
					entity: {
						name: `${galleryOffering?.processed_gallery?.name}`,
					},
					location: {
						code: `${galleryOffering?.processed_gallery?.location}`,
						name: `${galleryOffering?.processed_gallery?.location}`,
					},
				},
			}}
		/>
	{/if}
</UploadList>
