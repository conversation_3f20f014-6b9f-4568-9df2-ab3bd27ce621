import { getQueryKeys } from '$global/query-utils/getQueryKeys';
import { GetCropImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getCropImagesVisits.generated';
import { GetToMatchImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToMatchImagesVisits.generated';
import { GetToReviewImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToReviewImagesVisits.generated';
import { GetUploadedImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getUploadedImagesVisits.generated';
import { getQuery } from '$lib/query-utils/getQuery';
import type { GalleryOfferingsIdPageLoadEvent } from '$routes/gallery-offerings/[id]/types';

export const VISITS_STALE_TIME = 1000 * 60 * 60;
export const VISITS_LIMIT = 1;

export const getVisitsBaseVariables = (id: string) => ({
	filter: {
		_and: [{ ingestion_gallery_offering: { id: { _eq: id } } }],
	},
	limit: VISITS_LIMIT,
});

export const galleryOfferingsUploadListPageLoad = async ({
	parent,
	params,
	data,
}: GalleryOfferingsIdPageLoadEvent) => {
	const parentData = await parent();
	const queryClient = parentData.queryClient;
	const visitVariables = getVisitsBaseVariables(params.id);

	queryClient.setQueryData(
		getQueryKeys(GetUploadedImagesVisitsDocument, visitVariables),
		{
			pageParams: [0],
			pages: [data.uploadedImages],
		}
	);

	queryClient.setQueryData(
		getQueryKeys(GetCropImagesVisitsDocument, visitVariables),
		{
			pageParams: [0],
			pages: [data.cropImages],
		}
	);

	queryClient.setQueryData(
		getQueryKeys(GetToMatchImagesVisitsDocument, visitVariables),
		{
			pageParams: [0],
			pages: [data.toMatchImages],
		}
	);

	queryClient.setQueryData(
		getQueryKeys(GetToReviewImagesVisitsDocument, visitVariables),
		{
			pageParams: [0],
			pages: [data.toReviewImages],
		}
	);

	queryClient.prefetchInfiniteQuery({
		...getQuery(GetUploadedImagesVisitsDocument, visitVariables),
		initialPageParam: 0,
		staleTime: VISITS_STALE_TIME,
	});

	queryClient.prefetchInfiniteQuery({
		...getQuery(GetCropImagesVisitsDocument, visitVariables),
		initialPageParam: 0,
		staleTime: VISITS_STALE_TIME,
	});

	queryClient.prefetchInfiniteQuery({
		...getQuery(GetToMatchImagesVisitsDocument, visitVariables),
		initialPageParam: 0,
		staleTime: VISITS_STALE_TIME,
	});

	queryClient.prefetchInfiniteQuery({
		...getQuery(GetToReviewImagesVisitsDocument, visitVariables),
		initialPageParam: 0,
		staleTime: VISITS_STALE_TIME,
	});

	return {
		...data,
		...parentData,
		visitVariables,
	};
};
