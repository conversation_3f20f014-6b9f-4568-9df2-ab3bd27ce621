import { error } from '@sveltejs/kit';
import { getVisitsBaseVariables } from '../galleryOfferingsUploadListPageLoad/galleryOfferingsUploadListPageLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetCropImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getCropImagesVisits.generated';
import { GetToMatchImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToMatchImagesVisits.generated';
import { GetToReviewImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getToReviewImagesVisits.generated';
import { GetUploadedImagesVisitsDocument } from '$lib/features/visit/queries/__generated__/getUploadedImagesVisits.generated';
import { gqlClient } from '$lib/gqlClient';
import { GetVisitArtworkDocument } from '$lib/queries/__generated__/getVisitArtwork.generated';
import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import type { ExhibitionsIdPageServerLoadEvent } from '$routes/exhibitions/[id]/types';

export const galleryOfferingsUploadListServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingPromise = gqlClient.request(
		GetGalleryOfferingsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const uploadedImagesPromise = gqlClient.request(
		GetUploadedImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const cropImagesPromise = gqlClient.request(
		GetCropImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const toMatchImagesPromise = gqlClient.request(
		GetToMatchImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const toReviewImagesPromise = gqlClient.request(
		GetToReviewImagesVisitsDocument,
		getVisitsBaseVariables(params.id),
		getAuthorizationHeaders(data)
	);

	const [
		galleryOfferingResponse,
		uploadedImages,
		cropImages,
		toMatchImages,
		toReviewImages,
	] = await Promise.all([
		galleryOfferingPromise,
		uploadedImagesPromise,
		cropImagesPromise,
		toMatchImagesPromise,
		toReviewImagesPromise,
	]);

	const galleryOffering =
		galleryOfferingResponse?.Ingestion_Gallery_Offering?.[0];

	if (!galleryOffering) {
		error(404, 'Not found');
	}

	const visitArtworks = await (async () => {
		const visitId = galleryOffering?.visit?.id;
		if (visitId) {
			const res = await gqlClient.request(
				GetVisitArtworkDocument,
				{ filter: { visit: { id: { _eq: visitId } } }, limit: 10000 },
				getAuthorizationHeaders(data)
			);

			return res?.Visit_Artwork;
		}

		return [];
	})();

	return {
		...data,
		visitArtworks,
		galleryOffering,
		uploadedImages,
		cropImages,
		toMatchImages,
		toReviewImages,
	};
};
