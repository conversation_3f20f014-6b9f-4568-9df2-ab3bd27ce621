<script lang="ts">
	import { GalleryOfferingsDetails } from '../../components/GalleryOfferingsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { AddTextToImages } from '$lib/features/add-text-to-images/components/AddTextToImages';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import type { GalleryOfferingsIdManualUploadManualUploadIdAddDataPageData } from '$routes/gallery-offerings/[id]/manual-upload/[manualUploadId]/add-data/types';

	const dataCyPrefix = 'gallery-offerings-add-text-to-images';

	let data = $derived(
		getPageData<GalleryOfferingsIdManualUploadManualUploadIdAddDataPageData>(
			page.data
		)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: data?.galleryOffering?.title,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Add Text To Images' },
	]);

	const buttonProps = {
		label: 'Back to Gallery Offering Page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	};

	const button2Props = {
		label: 'back to Gallery Offerings',
		href: `${Routes.GalleryOfferingsHome}`,
	};
</script>

<ArtworkTextTableContext>
	<PageBody>
		<AddTextToImages
			{crumbs}
			detailsString="Gallery offering details"
			title={`${galleryOffering.title}`}
			slotLabel="Gallery offering the information is going to be added to"
			dataCy={dataCyPrefix}
			{data}
			{buttonProps}
			{button2Props}
			{artworkSaleStatuses}
			artist={null}
		>
			{#if galleryOffering}
				<GalleryOfferingsDetails
					dataCy={dataCyPrefix}
					galleryOffering={{
						id: `${galleryOffering?.processed_gallery?.id}`,
						organisation: {
							entity: {
								name: `${galleryOffering?.processed_gallery?.name}`,
							},
							location: {
								name: `${galleryOffering?.processed_gallery?.location}`,
								code: `${galleryOffering?.processed_gallery?.location}`,
							},
						},
					}}
				/>
			{/if}
		</AddTextToImages>
	</PageBody>
</ArtworkTextTableContext>
