import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { addTextToImagesServerLoad } from '$lib/features/add-text-to-images/utils/addTextToImagesServerLoad/addTextToImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdManualUploadManualUploadIdAddDataPageServerLoadEvent } from '$routes/gallery-offerings/[id]/manual-upload/[manualUploadId]/add-data/types';

export const galleryOfferingsAddTextToImagesServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdManualUploadManualUploadIdAddDataPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];

	const addTextToImagesServerLoadResponse = await addTextToImagesServerLoad(
		params.manualUploadId,
		data
	);

	return {
		...data,
		...addTextToImagesServerLoadResponse,
		galleryOffering,
	};
};
