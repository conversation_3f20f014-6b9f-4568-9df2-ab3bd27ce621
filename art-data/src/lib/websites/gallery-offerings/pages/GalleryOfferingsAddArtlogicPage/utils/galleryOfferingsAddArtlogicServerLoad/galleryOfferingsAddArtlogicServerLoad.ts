import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import type { ArtlogicGallery } from '$lib/features/add-artlogic/types';
import { getAddArtlogicSuperform } from '$lib/features/add-artlogic/utils/getAddArtlogicSuperform/getAddArtlogicSuperform';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetArteyeGalleriesDocument } from '$lib/websites/gallery-offerings/arteye-queries/__generated__/getArteyeGalleries.generated';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdPageServerLoadEvent } from '$routes/gallery-offerings/[id]/types';

export const galleryOfferingsAddArtlogicServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const forms = await getAddArtlogicSuperform();

	const galleryRes = await gqlClientArteye.request(
		GetArteyeGalleriesDocument,
		{
			filter: { id: { _eq: galleryOffering?.processed_gallery?.id } },
		},
		getAuthorizationHeaders({ user: { access_token: data.user?.arteye_token } })
	);

	const gallery = galleryRes?.gallery?.[0] as ArtlogicGallery;

	return {
		...data,
		...forms,
		galleryOffering,
		gallery,
	};
};
