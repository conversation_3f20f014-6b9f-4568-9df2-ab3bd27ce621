<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddArtlogic } from '$lib/features/add-artlogic/components/AddArtlogic';
	import type { GalleryOfferingsIdArtlogicAddPageData } from '$routes/gallery-offerings/[id]/artlogic/add/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdArtlogicAddPageData>(page.data)
	);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Add Artlogic link' },
	]);
</script>

<AddArtlogic
	{crumbs}
	gallery={data?.gallery}
	title={`${data?.galleryOffering?.title}`}
	key="gallery-offerings"
	collection="Ingestion_Gallery_Offering"
	buttonProps={{
		label: 'Back to gallery offering page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to gallery offerings list',
		href: Routes.GalleryOfferingsHome,
	}}
/>
