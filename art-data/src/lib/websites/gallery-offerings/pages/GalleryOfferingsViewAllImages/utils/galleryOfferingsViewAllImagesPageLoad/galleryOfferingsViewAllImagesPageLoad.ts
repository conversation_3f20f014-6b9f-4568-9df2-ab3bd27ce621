import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { viewAllImagesPageLoad } from '$lib/features/view-all-images/utils/viewAllImagesPageLoad/viewAllImagesPageLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { ExhibitionsIdImagesViewPageLoadEvent } from '$routes/exhibitions/[id]/images/view/types';

export const VISIT_IMAGES_LIMIT = 20;

export const galleryOfferingsViewAllImagesPageLoad = async ({
	parent,
	url,
	params,
}: ExhibitionsIdImagesViewPageLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const visitId = galleryOffering?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const viewAllImagesResponse = await viewAllImagesPageLoad({
		url,
		data,
		visitId,
	});

	return {
		galleryOffering,
		...viewAllImagesResponse,
	};
};
