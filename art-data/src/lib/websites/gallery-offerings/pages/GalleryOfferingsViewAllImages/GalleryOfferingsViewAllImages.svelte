<script lang="ts">
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { ViewAllImages } from '$lib/features/view-all-images/components/ViewAllImages';
	import type { GalleryOfferingsIdImagesViewPageData } from '$routes/gallery-offerings/[id]/images/view/types';

	const dataCy = 'gallery-offerings-view-all-images';

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesViewPageData>(page.data)
	);
	let images = $derived(data.images);
	let users = $derived(data.users);
	let total = $derived(data.total);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data.galleryOffering.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'View all images' },
	]);
</script>

<PageBody>
	<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
	<ViewAllImages
		title={`${data.galleryOffering.title}`}
		{dataCy}
		{images}
		{users}
		{total}
		backButtonLabel="Back to gallery offering page"
		backButtonHref={`${Routes.GalleryOfferingsHome}/${page.params.id}`}
	/>
</PageBody>
