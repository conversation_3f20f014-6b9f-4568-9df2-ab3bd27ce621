<script lang="ts">
	import { GalleryOfferingsDetails } from '../../components/GalleryOfferingsDetails';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkTextTableContext } from '$lib/features/artwork-text-table/components/ArtworkTextTableContext';
	import { EditExtractedImages } from '$lib/features/edit-extracted-images/components/EditExtractedImages';
	import type { GalleryOfferingsIdPdfsPdfIdEditExtractedImagesPageData } from '$routes/gallery-offerings/[id]/pdfs/[pdfId]/edit-extracted-images/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdPdfsPdfIdEditExtractedImagesPageData>(
			page.data
		)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let pdfArtworkFormatMethod = $derived(data.pdfArtworkFormatMethod);
	let pdfFile = $derived(data.file);
	let pdfAiExtractedText = $derived(data.pdfAiExtractedText);
	let artworks = $derived(data.artworks);
	let pdfTitle = $derived(data.pdfTitle);
	let pdfArtist = $derived(data.pdfArtist);
	let pdfArtistCountry = $derived(data.pdfArtistCountry);
	let pages = $derived(data.pages);
	let discardedImages = $derived(data.discardedImages);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${galleryOffering.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Edit Extracted Images' },
	]);

	const dataCy = 'gallery-offerings';

	const buttonProps = {
		label: 'back to gallery offering uploads',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	};
</script>

<ArtworkTextTableContext>
	<EditExtractedImages
		{pdfArtist}
		{pdfTitle}
		{pdfArtistCountry}
		{pdfAiExtractedText}
		{pdfArtworkFormatMethod}
		title={`${galleryOffering?.title}`}
		{artworks}
		{pages}
		{discardedImages}
		pdfLink={pdfFile}
		slotLabel="Gallery offering the information is going to be added to"
		{dataCy}
		{crumbs}
		{buttonProps}
		{artworkSaleStatuses}
	>
		{#if galleryOffering}
			<GalleryOfferingsDetails
				dataCy={`${dataCy}-edit-extracted-images`}
				galleryOffering={{
					id: `${galleryOffering?.processed_gallery?.id}`,
					organisation: {
						entity: {
							name: `${galleryOffering?.processed_gallery?.name}`,
						},
						location: {
							name: `${galleryOffering?.processed_gallery?.location}`,
							code: `${galleryOffering?.processed_gallery?.location}`,
						},
					},
				}}
			/>
		{/if}
	</EditExtractedImages>
</ArtworkTextTableContext>
