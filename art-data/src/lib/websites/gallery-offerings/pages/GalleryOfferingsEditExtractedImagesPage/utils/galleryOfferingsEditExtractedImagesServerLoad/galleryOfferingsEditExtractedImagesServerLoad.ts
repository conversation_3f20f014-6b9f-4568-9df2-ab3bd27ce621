import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { editExtractedImagesServerLoad } from '$lib/features/edit-extracted-images/utils/editExtractedImagesServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsWithChecklistsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferingsWithChecklists.generated';
import type { ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent } from '$routes/exhibitions/[id]/pdfs/[pdfId]/edit-extracted-images/types';

export const galleryOfferingsEditExtractedImagesServerLoad = async ({
	parent,
	params,
}: ExhibitionsIdPdfsPdfIdEditExtractedImagesPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsWithChecklistsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];

	if (!galleryOffering) {
		error(404, 'Not found');
	}

	const editExtractedImagesResponse = await editExtractedImagesServerLoad({
		params,
		data,
	});

	return {
		...data,
		galleryOffering,
		...editExtractedImagesResponse,
	};
};
