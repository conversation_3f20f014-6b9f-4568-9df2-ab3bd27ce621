<script lang="ts">
	import classNames from 'classnames';
	import { GalleryOfferingsDetails } from '../../components/GalleryOfferingsDetails';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { MatchArtworksWithLabels } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels';
	import { MatchArtworksWithLabelsTableContext } from '$lib/features/match-artworks-with-labels/components/MatchArtworksWithLabels/MatchArtworksWithLabelsTableContext';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import type { GalleryOfferingsIdImagesMatchArtworksPageData } from '$routes/gallery-offerings/[id]/images/match-artworks/types';

	const dataCy = 'gallery-offerings-match-artworks-with-labels';

	let submittingDiscardedImages = $state(false);
	let fetching = $state(false);
	let submitting = $state(false);

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesMatchArtworksPageData>(page.data)
	);
	let visitId = $derived(data.visitId);
	let date = $derived(data.dateParam);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data.galleryOffering.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'Match artworks with labels' },
	]);
</script>

<MatchArtworksWithLabelsTableContext>
	<PageBody
		class={classNames({
			'pointer-events-none':
				submitting || submittingDiscardedImages || fetching,
		})}
	>
		<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />
		<MatchArtworksWithLabels
			bind:submittingDiscardedImages
			bind:fetching
			bind:submitting
			title={`${data.galleryOffering.processed_gallery?.name}`}
			slotLabel="Gallery offering details"
			{visitId}
			{dataCy}
			{date}
			photographerId={photographer.id}
		>
			{#if data.galleryOffering?.processed_gallery}
				<GalleryOfferingsDetails
					dataCy="gallery-offerings-upload-list"
					galleryOffering={{
						id: data.galleryOffering?.processed_gallery?.id,
						organisation: {
							entity: {
								name: `${data.galleryOffering?.processed_gallery?.name}`,
							},
							location: {
								code: `${data.galleryOffering?.processed_gallery?.location}`,
								name: `${data.galleryOffering?.processed_gallery?.location}`,
							},
						},
					}}
				/>
			{/if}
		</MatchArtworksWithLabels>
	</PageBody>
</MatchArtworksWithLabelsTableContext>
