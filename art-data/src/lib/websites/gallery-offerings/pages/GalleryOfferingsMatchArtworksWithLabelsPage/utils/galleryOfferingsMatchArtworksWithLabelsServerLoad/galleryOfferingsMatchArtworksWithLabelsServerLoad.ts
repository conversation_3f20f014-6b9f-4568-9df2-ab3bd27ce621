import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdImagesMatchArtworksPageServerLoadEvent } from '$routes/gallery-offerings/[id]/images/match-artworks/types';

export const galleryOfferingsMatchArtworksWithLabelsServerLoad = async ({
	parent,
	params,
	url,
}: GalleryOfferingsIdImagesMatchArtworksPageServerLoadEvent) => {
	const data = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const visitId = galleryOffering?.visit?.id;

	if (!visitId) {
		error(404, 'Not found');
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographerParam } } },
		getAuthorizationHeaders(data)
	);

	const photographer = usersResponse.users?.[0];

	return {
		galleryOffering,
		visitId,
		dateParam,
		photographer,
	};
};
