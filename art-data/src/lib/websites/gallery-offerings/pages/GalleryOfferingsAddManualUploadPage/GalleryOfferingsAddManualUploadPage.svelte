<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddManualUpload } from '$lib/features/add-manual-upload/components/AddManualUpload';
	import type { GalleryOfferingsIdManualUploadAddPageData } from '$routes/gallery-offerings/[id]/manual-upload/add/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdManualUploadAddPageData>(page.data)
	);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data.galleryOffering.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Add Manual Upload' },
	]);
</script>

<AddManualUpload
	{crumbs}
	title={`${data.galleryOffering.title}`}
	key="gallery-offerings"
	collection="Ingestion_Gallery_Offering"
	backButtonHref={`${Routes.GalleryOfferingsHome}/${page.params.id}`}
	buttonProps={{
		label: 'Back to gallery offering page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to gallery offerings list',
		href: Routes.GalleryOfferingsHome,
	}}
/>
