import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAddManualUploadSuperform } from '$lib/features/add-manual-upload/utils/getAddManualUploadSuperform/getAddManualUploadSuperform';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdPageServerLoadEvent } from '$routes/gallery-offerings/[id]/types';

export const galleryOfferingsAddManualUploadServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{
			filter: { id: { _eq: params.id } },
		},
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const forms = await getAddManualUploadSuperform();
	const dropzoneUrlForms = await getDropzoneUrlDialogSuperform();

	return {
		...data,
		...forms,
		...dropzoneUrlForms,
		galleryOffering,
	};
};
