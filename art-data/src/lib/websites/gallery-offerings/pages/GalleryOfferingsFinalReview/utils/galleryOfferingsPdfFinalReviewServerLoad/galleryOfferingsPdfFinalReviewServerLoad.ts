import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { pdfFinalReviewServerLoad } from '$lib/features/final-review/utils/pdfFinalReviewServerLoad/pdfFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdPdfsPdfIdReviewArtworksPageServerLoadEvent } from '$routes/gallery-offerings/[id]/pdfs/[pdfId]/review-artworks/types';

export const galleryOfferingsPdfFinalReviewServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdPdfsPdfIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionGalleryResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const name = `${ingestionGalleryResponse?.Ingestion_Gallery_Offering?.[0]?.processed_gallery?.name}`;

	const finalReviewResponse = await pdfFinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
