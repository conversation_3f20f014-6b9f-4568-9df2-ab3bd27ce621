import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { manualUploadfinalReviewServerLoad } from '$lib/features/final-review/utils/manualUploadFinalReviewServerLoad/manualUploadFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent } from '$routes/gallery-offerings/[id]/manual-upload/[manualUploadId]/review-artworks/types';

export const galleryOfferingsManualUploadFinalReviewServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdManualUploadManualUploadIdReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();

	const ingestionGalleryResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const name = `${ingestionGalleryResponse?.Ingestion_Gallery_Offering?.[0]?.processed_gallery?.name}`;

	const finalReviewResponse = await manualUploadfinalReviewServerLoad({
		params,
		parent,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name,
		galleryName: name,
	};
};
