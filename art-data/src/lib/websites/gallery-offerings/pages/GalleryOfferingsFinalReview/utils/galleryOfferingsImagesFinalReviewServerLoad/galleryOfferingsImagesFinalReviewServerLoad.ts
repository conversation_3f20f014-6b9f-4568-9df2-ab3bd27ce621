import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { imagesFinalReviewServerLoad } from '$lib/features/final-review/utils/imagesFinalReviewServerLoad/imagesFinalReviewServerLoad';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { ExhibitionsIdImagesReviewArtworksPageServerLoadEvent } from '$routes/exhibitions/[id]/images/review-artworks/types';

export const galleryOfferingsImagesFinalReviewServerLoad = async ({
	parent,
	params,
	url,
}: ExhibitionsIdImagesReviewArtworksPageServerLoadEvent) => {
	const parentData = await parent();
	const queryParams = url.searchParams;
	const dateParam = queryParams.get('date');
	const photographerParam = queryParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const ingestionGalleryResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const galleryOffering =
		ingestionGalleryResponse?.Ingestion_Gallery_Offering?.[0];

	const name = galleryOffering?.processed_gallery?.name;
	const visitId = galleryOffering?.visit?.id;

	if (!visitId) {
		error(404, 'No visit attached to this gallery offering');
	}

	const finalReviewResponse = await imagesFinalReviewServerLoad({
		parent,
		date: dateParam,
		photographerId: photographerParam,
		visitId,
	});

	return {
		...parentData,
		...finalReviewResponse,
		name: `${name}`,
	};
};
