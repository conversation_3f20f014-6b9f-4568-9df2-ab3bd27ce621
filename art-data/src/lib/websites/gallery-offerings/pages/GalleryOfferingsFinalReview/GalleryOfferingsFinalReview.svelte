<script lang="ts">
	import { type CreateMutationResult } from '@tanstack/svelte-query';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { FinalReview } from '$lib/features/final-review';
	import type { Artwork } from '$lib/features/final-review/types';
	import type { GalleryOfferingsIdManualUploadManualUploadIdReviewArtworksPageData } from '$routes/gallery-offerings/[id]/manual-upload/[manualUploadId]/review-artworks/types';

	interface Props {
		updateArtwork: CreateMutationResult<any, any, any>;
		updateChecklist: CreateMutationResult<any, any, any> | null;
		finalizeReview: (args: {
			data: {
				user: {
					access_token: string;
				} | null;
			};
			updateArtwork: CreateMutationResult<any, any, any>;
			updateChecklist: CreateMutationResult<any, any, any> | null;
			artworksCopy: Artwork[];
			nbArtworksRemaining: number | undefined;
			selectedImages: (string | undefined)[];
		}) => void;
	}

	let { updateArtwork, updateChecklist, finalizeReview }: Props = $props();

	let data = $derived(
		getPageData<GalleryOfferingsIdManualUploadManualUploadIdReviewArtworksPageData>(
			page.data
		)
	);
	let additionalCrumb = $derived(data.additionalCrumb);

	let buttonProps = $derived({
		label: 'back to gallery offering page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	});

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Review and submit artworks' },
	]);
</script>

<FinalReview
	{updateArtwork}
	{updateChecklist}
	{finalizeReview}
	{crumbs}
	{data}
	{buttonProps}
	dataCy="gallery-offerings"
/>
