<script lang="ts">
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';
</script>

<div class="flex items-center gap-1">
	<Txt variant="body3" class="text-gray-500">Cannot find the gallery?</Txt>
	<Txt
		variant="body3"
		component="a"
		href={`${Config.ArteyeDomain}/galleries/new`}
		rel="noopener noreferrer"
		target="_blank"
		class="text-blue-500 underline">Create new gallery on Arteye</Txt
	>
	<ExternalIcon class="h-3 w-3" color="blue-500" />
</div>
