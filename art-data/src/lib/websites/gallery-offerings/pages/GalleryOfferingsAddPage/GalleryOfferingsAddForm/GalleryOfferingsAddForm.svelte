<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import { writable } from 'svelte/store';
	import { CannotFindGallery } from './CannotFindGallery';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepDescription } from '$global/features/form/components/StepDescription';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { StepTitle } from '$global/features/form/components/StepTitle';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { RemovableSelectedLinkOption } from '$lib/components/RemovableSelectedLinkOption';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { CreateProcessedOrganisationDocument } from '$lib/queries/__generated__/createProcessedOrganisation.generated';
	import { GetProcessedOrganisationDocument } from '$lib/queries/__generated__/getProcessedOrganisation.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import {
		GetArteyeGalleriesDocument,
		type GetArteyeGalleriesQuery,
		type GetArteyeGalleriesQueryVariables,
	} from '$lib/websites/gallery-offerings/arteye-queries/__generated__/getArteyeGalleries.generated';
	import { CreateIngestionGalleryOfferingDocument } from '$lib/websites/gallery-offerings/queries/__generated__/createIngestionGalleryOffering.generated';
	import { getGalleryDetails } from '$lib/websites/gallery-offerings/utils/getGalleryDetails/getGalleryDetails';
	import type { GalleryOfferingsAddPageData } from '$routes/gallery-offerings/add/types';

	const dataCyPrefix = 'gallery-offering-add';

	let titleBlur = $state(false);
	let titleValue = $state('');
	let submitting = $state(false);
	let value = $state(writable(''));
	let selectedOption: OptionType | null = $state(null);

	const getVariables = (value: string): GetArteyeGalleriesQueryVariables => {
		return {
			limit: 10,
			filter: {
				_and: [
					...(() => {
						if (!value) {
							return [];
						}

						if (isUuidValid(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [
							{
								organisation: {
									entity: {
										name: { _icontains: value },
									},
								},
							},
							{
								organisation: {
									name: { _icontains: value },
								},
							},
						];
					})(),

					{ status: { key: { _neq: 'archived' } } },
				],
			},
		};
	};

	const createIngestionGalleryOffering = createMutation(
		getMutation(
			CreateIngestionGalleryOfferingDocument,
			getAuthorizationHeaders(page.data as GalleryOfferingsAddPageData)
		)
	);

	const createProcessedOrganisation = createMutation(
		getMutation(
			CreateProcessedOrganisationDocument,
			getAuthorizationHeaders(page.data as GalleryOfferingsAddPageData)
		)
	);

	const getOptions = (data: GetArteyeGalleriesQuery | undefined) => {
		return (data?.gallery || [])?.map(getGalleryDetails);
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		const urlParts = selectedOption?.line2?.split('/') || [];
		const id = urlParts?.[urlParts?.length - 1];

		submitting = true;

		let processedOrganisation;

		const getProcessedOrganisationResponse = await gqlClient.request(
			GetProcessedOrganisationDocument,
			{
				filter: { id: { _eq: id } },
			},
			getAuthorizationHeaders(page.data as GalleryOfferingsAddPageData)
		);

		processedOrganisation =
			getProcessedOrganisationResponse?.Processed_Organisation?.[0];

		if (!processedOrganisation) {
			const createProcessedOrganisationResponse =
				await $createProcessedOrganisation.mutateAsync({
					data: {
						id,
						name: `${selectedOption?.line1}`,
						location: selectedOption?.line3,
						type: 'Gallery',
					},
				});

			processedOrganisation =
				createProcessedOrganisationResponse?.create_Processed_Organisation_item;
		}

		if (!processedOrganisation) {
			submitting = false;
			return;
		}

		const response = await $createIngestionGalleryOffering.mutateAsync({
			input: {
				title: titleValue,
				processed_gallery: {
					id: processedOrganisation?.id,
					name: processedOrganisation?.name,
					type: 'Gallery',
				},
			},
		});

		submitting = false;

		(window as unknown as { location: string }).location =
			`${Routes.GalleryOfferingsHome}/${response?.create_Ingestion_Gallery_Offering_item?.id}`;
	};
</script>

<div>
	<StepTitle>Add data about a gallery offering</StepTitle>
	<StepDescription>
		Try searching for the gallery below. If you cannot find the gallery, click
		'create new' to add it on Arteye, then return here to select it in the
		search field below.
	</StepDescription>

	<StepContainer>
		<form onsubmit={handleSubmit} class="col-span-2 mb-[4rem] sm:mb-[7rem]">
			<StepInput
				dataCy={`${dataCyPrefix}-offering-title`}
				label="Offering Title"
				placeholder="Enter offering title"
				id="offering-title"
				name="offering-title"
				type="text"
				class="col-span-2"
				required
				stopPropagationWhenPressingEnter
				error={titleBlur && !titleValue && 'Please enter an offering title'}
				bind:value={titleValue}
				onblur={() => {
					titleBlur = true;
				}}
			/>

			<QueryAutocomplete
				emptyValueResponse={{ gallery: [] }}
				label="Search for gallery"
				name="gallery"
				dataCy={`${dataCyPrefix}-gallery`}
				placeholder="Start by typing to search by gallery name or ID"
				document={GetArteyeGalleriesDocument}
				graphQlClient={gqlClientArteye}
				{getOptions}
				{getVariables}
				required
				requestHeaders={{
					authorization: `Bearer ${page.data.user.arteye_token}`,
				}}
				class="mt-4"
				classes={{
					listWithOptions: 'pb-[3rem]',
					longList: '!max-h-[232px] !min-h-[232px]',
					option: {
						line3: 'font-[500]',
					},
				}}
				OptionComponent={LinkOption}
				SelectedOptionComponent={RemovableSelectedLinkOption}
				{value}
				bind:selectedOption
			>
				{#snippet list()}
					<div
						class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
					>
						<CannotFindGallery />
					</div>
				{/snippet}

				{#snippet noResults()}
					<div class="flex flex-col items-center">
						<NoResults
							class="mb-2"
							dataCy={`${dataCyPrefix}-gallery-offerings-autocomplete`}
							>No gallery found</NoResults
						>
						<CannotFindGallery />
					</div>
				{/snippet}
			</QueryAutocomplete>

			<StepButtons
				backButtonProps={{
					href: Routes.GalleryOfferingsHome,
				}}
				continueButtonProps={{
					disabled: !selectedOption || !titleValue,
					loading: submitting,
				}}
			>
				Continue
			</StepButtons>
		</form>
	</StepContainer>
</div>
