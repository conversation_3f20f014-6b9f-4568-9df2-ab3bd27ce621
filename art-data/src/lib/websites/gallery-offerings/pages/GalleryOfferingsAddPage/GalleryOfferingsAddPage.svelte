<script lang="ts">
	import { GalleryOfferingsAddForm } from './GalleryOfferingsAddForm';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'gallery-offering-home';

	const crumbs = [
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{ label: 'Add New Gallery Offering' },
	];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<GalleryOfferingsAddForm />
</PageBody>
