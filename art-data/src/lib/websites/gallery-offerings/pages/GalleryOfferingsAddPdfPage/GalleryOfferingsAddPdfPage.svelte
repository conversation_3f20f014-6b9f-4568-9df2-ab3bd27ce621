<script lang="ts">
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { AddPdf } from '$lib/features/add-pdf/components/AddPdf';
	import type { GalleryOfferingsIdPdfsAddPageData } from '$routes/gallery-offerings/[id]/pdfs/add/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdPdfsAddPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{ label: 'Add PDF' },
	]);
</script>

<AddPdf
	key="gallery-offerings"
	collection="Ingestion_Gallery_Offering"
	{crumbs}
	title={galleryOffering.title}
	buttonProps={{
		label: 'Back to gallery offering page',
		href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
	}}
	button2Props={{
		label: 'Back to gallery offerings list',
		href: Routes.GalleryOfferingsHome,
	}}
/>
