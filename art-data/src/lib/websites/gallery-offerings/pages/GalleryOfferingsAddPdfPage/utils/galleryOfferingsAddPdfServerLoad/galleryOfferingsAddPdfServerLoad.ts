import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAddPdfSuperform } from '$lib/features/add-pdf/utils/getAddPdfSuperform/getAddPdfSuperform';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';
import type { GalleryOfferingsIdPageServerLoadEvent } from '$routes/gallery-offerings/[id]/types';

export const galleryOfferingsAddPdfServerLoad = async ({
	parent,
	params,
}: GalleryOfferingsIdPageServerLoadEvent) => {
	const data = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(data)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const forms = await getAddPdfSuperform();

	return {
		...data,
		...forms,
		galleryOffering,
	};
};
