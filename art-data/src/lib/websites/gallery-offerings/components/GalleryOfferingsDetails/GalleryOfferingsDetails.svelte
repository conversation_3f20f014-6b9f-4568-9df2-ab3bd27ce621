<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { getGalleryDetails } from '../../utils/getGalleryDetails/getGalleryDetails';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { type OptionClasses } from '$global/components/QueryAutocomplete/types';
	import { type GetArteyeGalleriesQuery } from '$lib/websites/gallery-offerings/arteye-queries/__generated__/getArteyeGalleries.generated';

	interface Props {
		galleryOffering: GetArteyeGalleriesQuery['gallery'][number];
		dataCy: string;
		classes?: OptionClasses;
		class?: string;
	}

	let {
		galleryOffering,
		dataCy,
		classes = { line2: 'font-[500]' },
		...rest
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-details`);
	let option = $derived(getGalleryDetails(galleryOffering));
</script>

<div
	data-cy={dataCyPrefix}
	class={twMerge(
		'flex flex-col gap-2 rounded border border-gray-200 p-3',
		rest.class
	)}
>
	<LinkOption dataCy={dataCyPrefix} {option} {classes} />
</div>
