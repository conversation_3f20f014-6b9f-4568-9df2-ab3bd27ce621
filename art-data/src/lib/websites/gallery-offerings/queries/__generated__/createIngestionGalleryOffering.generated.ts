import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateIngestionGalleryOfferingMutationVariables = Types.Exact<{
	input: Types.Create_Ingestion_Gallery_Offering_Input;
}>;

export type CreateIngestionGalleryOfferingMutation = {
	__typename?: 'Mutation';
	create_Ingestion_Gallery_Offering_item?: {
		__typename?: 'Ingestion_Gallery_Offering';
		id: string;
	} | null;
};

export const CreateIngestionGalleryOfferingDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createIngestionGalleryOffering' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: {
								kind: 'Name',
								value: 'create_Ingestion_Gallery_Offering_input',
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'create_Ingestion_Gallery_Offering_item',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateIngestionGalleryOfferingMutation,
	CreateIngestionGalleryOfferingMutationVariables
>;
