import { gqlClientCustom } from '$lib/gqlClientCustom';
import { UserTokenDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/userToken.generated';

export const getAdditionalCookieData = async (accessToken: string) => {
	const arteyeUserTokenRes = await gqlClientCustom.request(
		UserTokenDocument,
		{},
		{ Authorization: `Bearer ${accessToken}` }
	);
	return { arteye_token: arteyeUserTokenRes?.userToken?.token };
};
