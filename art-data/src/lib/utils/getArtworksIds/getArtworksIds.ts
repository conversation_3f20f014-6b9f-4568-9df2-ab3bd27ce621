export const getArtworkIds = (
	artworks: {
		id?: string;
		status?:
			| {
					key?: string | null | undefined;
			  }
			| null
			| undefined;
		created_artwork_id?: string | null | undefined;
		processed_activity_id?: string | null | undefined;
	}[]
) => {
	return {
		nbArtworksPending: artworks.filter(
			(artwork) => artwork.status?.key === 'REVIEWED_AND_SUBMITTED'
		).length,
		createdArtworkIds: artworks
			.filter((artwork) => artwork.status?.key === 'COMPLETED')
			.map((artwork) =>
				artwork.created_artwork_id
					? JSON.parse(artwork.created_artwork_id)?.id
					: null
			)
			.filter(Boolean) as string[],
		processedActivityIds: artworks
			.filter((artwork) => artwork.status?.key === 'COMPLETED')
			.map((artwork) => artwork.processed_activity_id) as string[],
		failedArtworkIds: artworks
			.filter((artwork) => artwork.status?.key === 'INGESTION_FAILED')
			.map((artwork) => artwork.id) as string[],
	};
};
