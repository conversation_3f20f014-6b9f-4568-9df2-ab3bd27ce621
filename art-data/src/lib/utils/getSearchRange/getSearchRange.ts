import dayjs from 'dayjs';
import { SearchRange } from '$lib/constants/search-range-options';

const getDayjs = (date: string) => {
	const saleDateParts = date.split('/');
	return dayjs(`${saleDateParts[2]}-${saleDateParts[1]}-${saleDateParts[0]}`);
};

const formatDate = (date: string) => {
	return getDayjs(date).toISOString();
};

export const getSearchRangeFilter = ({
	value,
	range,
}: {
	value: string;
	range: SearchRange;
}) => {
	if (range === SearchRange.GreaterThan) {
		return {
			_gt: formatDate(value.trim()),
		};
	}

	if (range === SearchRange.LessThan) {
		return {
			_lt: formatDate(value.trim()),
		};
	}

	if (range === SearchRange.Between) {
		const [min, max] = value.replaceAll(' ', '').split('-');
		return {
			_between: [formatDate(min), formatDate(max)],
		};
	}

	if (range === SearchRange.EqualTo) {
		return {
			_between: [
				formatDate(value.trim()),
				getDayjs(value.trim()).endOf('day').toISOString(),
			],
		};
	}

	return {};
};
