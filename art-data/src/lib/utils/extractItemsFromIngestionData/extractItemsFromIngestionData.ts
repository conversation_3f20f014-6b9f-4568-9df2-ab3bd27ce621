import {
	type UploadListPdf,
	type UploadListManualUpload,
	type UploadListArtlogicLink,
	IngestionDataTypenames,
	type ReadyForReview,
	type ReviewedAndSubmitted,
	type Skipped,
	type Matches,
} from '$lib/types';

export type IngestionData = Array<{
	item?: UploadListArtlogicLink | UploadListManualUpload | UploadListPdf | null;
} | null> | null;

type ManualUploadArtworks = {
	__typename?: 'Manual_Upload';
	manually_added_artworks?: Array<{
		__typename?: 'Manually_Added_Artwork';
		id: string;
	} | null> | null;
};

type PDFArtworks = {
	__typename?: 'PDF';
	artworks?: Array<{
		__typename?: 'PDF_Artwork';
		id: string;
	} | null> | null;
};

export const extractItemsFromIngestionData = (
	ingestionData: IngestionData | undefined,
	readyForReview: ReadyForReview | undefined,
	reviewedAndSubmitted: ReviewedAndSubmitted | undefined,
	matches: Matches | undefined,
	skipped: Skipped | undefined,
	typename: IngestionDataTypenames
) => {
	return ingestionData
		?.map((item, i) => ({
			...item?.item,
			...(typename === IngestionDataTypenames.ManualUpload && {
				matches: (matches?.[i]?.item as ManualUploadArtworks)
					?.manually_added_artworks,
				ready_for_review: (readyForReview?.[i]?.item as ManualUploadArtworks)
					?.manually_added_artworks,
				reviewed_and_submitted: (
					reviewedAndSubmitted?.[i]?.item as ManualUploadArtworks
				)?.manually_added_artworks,
				skipped: (skipped?.[i]?.item as ManualUploadArtworks)
					?.manually_added_artworks,
			}),
			...(typename === IngestionDataTypenames.Pdf && {
				matches: (matches?.[i]?.item as PDFArtworks)?.artworks,
				ready_for_review: (readyForReview?.[i]?.item as PDFArtworks)?.artworks,
				reviewed_and_submitted: (reviewedAndSubmitted?.[i]?.item as PDFArtworks)
					?.artworks,
				skipped: (skipped?.[i]?.item as PDFArtworks)?.artworks,
			}),
		}))
		?.filter(
			(item) =>
				(item as { __typename: IngestionDataTypenames })?.__typename ===
				typename
		);
};
