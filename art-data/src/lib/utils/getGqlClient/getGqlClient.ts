import { GraphQLClient } from 'graphql-request';
import { browser } from '$app/environment';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Config } from '$lib/constants/config';
import type { RootLayoutData } from '$routes/types';

export const getGqlClient = (data: RootLayoutData) => {
	return new GraphQLClient(Config.GraphqlApiUrl, {
		...(browser && {
			fetch: window.fetch,
			...getAuthorizationHeaders(data),
		}),
	});
};
