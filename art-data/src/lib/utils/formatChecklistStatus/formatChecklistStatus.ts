import { ChecklistStatus } from '$lib/types';

export const formatChecklistStatus = (status: string | null | undefined) => {
	if (!status) {
		return '';
	}

	switch (status) {
		case ChecklistStatus.SentToIngestionSchema:
			return 'Extracted';
		case ChecklistStatus.Submitted:
			return 'Reviewed and submitted';
		default: {
			const statusWithSpaces = status.replaceAll('_', ' ');
			return (
				statusWithSpaces.charAt(0).toUpperCase() + statusWithSpaces.slice(1)
			);
		}
	}
};
