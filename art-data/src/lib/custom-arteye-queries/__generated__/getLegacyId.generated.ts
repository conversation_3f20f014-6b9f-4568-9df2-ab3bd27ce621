import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-arteye-custom';

export type GetLegacyIdQueryVariables = Types.Exact<{
	collection: Types.Scalars['String']['input'];
	id: Types.Scalars['String']['input'];
}>;

export type GetLegacyIdQuery = {
	__typename?: 'Query';
	getLegacyId?: {
		__typename?: 'LegacyIdSearchResponse';
		legacyId?: string | null;
	} | null;
};

export const GetLegacyIdDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getLegacyId' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'collection' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'getLegacyId' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'collection' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'collection' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'legacyId' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetLegacyIdQuery, GetLegacyIdQueryVariables>;
