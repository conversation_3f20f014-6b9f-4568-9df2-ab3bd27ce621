import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetIcloudVisitImagesDocument } from '$lib/queries/__generated__/getIcloudVisitImages.generated';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
import type { FairsIdImagesAddIcloudAssignPageServerLoadEvent } from '$routes/fairs/[id]/images/add-icloud/assign/types';

export const assignIcloudImagesPageLoad = async ({
	url,
	parent,
	data,
}: FairsIdImagesAddIcloudAssignPageServerLoadEvent & { data: any }) => {
	dayjs.extend(utc);
	const parentData = await parent();
	const searchParams = url.searchParams;
	const locationParam = searchParams.get('location');
	const dateParam = searchParams.get('date');
	const photographerEmailParam = searchParams.get('photographer');
	const phoneParam = searchParams.get('phone');

	if (!locationParam || !dateParam || !photographerEmailParam) {
		error(404, 'Not found');
	}

	const imagesResponse = await gqlClient.request(
		GetIcloudVisitImagesDocument,
		{
			filter: {
				_and: [
					{
						_or: [
							{ source: { _null: true } },
							{ source: { _nin: ['additional_crop', 'ios_app_duplicate'] } },
						],
					},
					{
						visit_phone_registration: {
							image_date: {
								_gte: dayjs.utc(dateParam).toISOString(),
							},
						},
					},
					{
						visit_phone_registration: {
							image_date: {
								_lt: dayjs.utc(dateParam).add(1, 'day').toISOString(),
							},
						},
					},
					{
						visit_phone_registration: {
							phone_registration: { phone: { id: { _eq: phoneParam } } },
						},
					},
					{
						visit_phone_registration: {
							phone_registration: { location: { _eq: locationParam } },
						},
					},
					{
						visit_phone_registration: {
							phone_registration: {
								photographer: { email: { _eq: photographerEmailParam } },
							},
						},
					},
				],
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const images = imagesResponse?.Visit_Image;
	const formattedImages = (images || []).map((image) => ({
		id: image.id,
		width: image?.original_uncropped_image?.width || 1,
		height: image?.original_uncropped_image?.height || 1,
		filename_disk: `${image?.original_uncropped_image?.filename_disk}`,
		url: `${getImageUrl(
			image?.original_uncropped_image?.id,
			parentData?.user?.access_token
		)}`,
		alt: image.visit?.id,
		page: 0,
	}));

	return {
		...parentData,
		...data,
		images: formattedImages,
	};
};
