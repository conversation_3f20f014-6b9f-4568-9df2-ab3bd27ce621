<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Dropzone, type DropzoneFile } from '$global/components/Dropzone';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import { Select } from '$global/components/Select';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableActionCell,
		TableCell,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { PageBody } from '$lib/components/PageBody';
	import { CreateVisitImageDocument } from '$lib/features/visit/queries/__generated__/createVisitImage.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { GetPhonesDocument } from '$lib/queries/__generated__/getPhones.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

	interface Props {
		title: string;
		dataCy: string;
		crumbs: Crumb[];
		backButtonLabel: string;
		visitId: string;
		users: {
			id?: string | null | undefined;
			first_name?: string | null;
			last_name?: string | null;
		}[];
	}

	let { title, dataCy, crumbs, backButtonLabel, visitId, users }: Props =
		$props();

	let submitting = $state(false);
	let statuses: string[] = $state([]);
	let submitted = $state(false);
	let dropzoneFiles: DropzoneFile[] = $state([]);
	let files: File[] = $state([]);

	let takenDate = $state('');
	let photographer = $state('');

	let blurTakenDate = $state(false);
	let blurPhotographer = $state(false);

	const dataCyPrefix = `${dataCy}-add-more-images`;
	const actionCellWidth = '9rem';

	const createVisitImageMutation = createMutation(
		getMutation(
			CreateVisitImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const handleSubmitFiles = (newFiles: File[]) => {
		files = [...files, ...newFiles];
		statuses = [...statuses, ...newFiles.map(() => 'Not uploaded')];
	};

	const getFileValues = (file: File) => {
		return [file.name];
	};

	const secondsToTimeString = (n: number) => {
		const hours = Math.floor(n / 3600);
		const minutes = Math.floor((n % 3600) / 60);
		const seconds = n % 60;

		// Format with leading zeros
		const hh = hours.toString().padStart(2, '0');
		const mm = minutes.toString().padStart(2, '0');
		const ss = seconds.toString().padStart(2, '0');

		return `${hh}:${mm}:${ss}`;
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		submitting = true;
		for (let index = 0; index < files.length; index++) {
			statuses[index] = 'Uploading...';

			try {
				const fileResult = await uploadFile(
					files[index],
					getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				);

				const firstPhoneRes = await gqlClient.request(
					GetPhonesDocument,
					{},
					getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				);

				const firstPhone = firstPhoneRes?.Visit_Phone?.[0];
				const phonePayload = {
					id: firstPhone?.id,
					icloud_account: firstPhone?.icloud_account,
					authentication_status: {
						key: `${firstPhone?.authentication_status?.key}`,
					},
				};

				const regDate = new Date(takenDate);
				const registrationPayload = {
					location: 'Manual',
					photographer,
					phone: phonePayload,
					start_date: regDate.toISOString(),
					end_date: regDate.toISOString(),
				};

				await $createVisitImageMutation.mutateAsync({
					data: {
						original_uncropped_image: fileResult,
						status: {
							name: 'Awaiting Coordinate Extraction',
							key: 'AWAITING_COORDINATE_EXTRACTION',
						},
						source: 'web_upload',
						visit: {
							id: visitId,
						},
						image_taken_date: dayjs(takenDate).format(
							`YYYY-MM-DDT${secondsToTimeString(index * 10)}`
						),
						visit_phone_registration: [
							{
								image_date: (() => {
									const imageDate = new Date(regDate);
									imageDate.setSeconds(imageDate.getSeconds() + index * 10);
									return imageDate.toISOString();
								})(),
								phone: phonePayload,
								phone_registration: registrationPayload,
							},
						],
						photographer: photographer as any,
					},
				});

				statuses[index] = 'Uploaded';

				if (index === files.length - 1) {
					submitted = true;
				}
			} catch {
				statuses[index] = 'Error uploading';
			}
		}
	};

	const headers = ['File name', 'Status', ''];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<form onsubmit={handleSubmit} class={'mb-[4rem] sm:mb-[7rem]'}>
		<StepContainer class="max-w-[32.5rem]">
			<Txt
				variant="body3"
				class="col-span-2 mb-[-0.5rem] uppercase tracking-[1.68px]">{title}</Txt
			>
			<Txt variant="h4" class="col-span-2 mb-2">Add More Images</Txt>

			<StepInput
				dataCy={`${dataCyPrefix}-taken-date`}
				label="Taken Date"
				class="col-span-2"
				placeholder="DD/MM/YYYY"
				id="image_taken_date"
				name="image_taken_date"
				disabled={submitting}
				type="date"
				required
				stopPropagationWhenPressingEnter
				error={blurTakenDate && !takenDate
					? 'Please provide the date this images were taken'
					: ''}
				bind:value={takenDate}
				helper="You can upload more images taken on a different date where the current images are uploaded"
				onblur={() => {
					blurTakenDate = true;
				}}
			/>

			<div class="col-span-2">
				<Select
					class="col-span-2"
					label={`Photographer`}
					placeholder="Select Photographer"
					ariaLabel="Select Photographer"
					dataCy={`${dataCyPrefix}-photographer`}
					name="photographer"
					options={users.map((user) => ({
						value: user.id,
						label: `${user.first_name}${
							user.last_name ? ` ${user.last_name}` : ''
						}`,
					}))}
					disabled={submitting}
					required
					error={blurPhotographer && !photographer
						? `Please provide a photographer`
						: ''}
					bind:value={photographer}
					onblur={() => {
						blurPhotographer = false;
					}}
				>
					<span class="font-normal">
						You can upload more images taken by another photographer where the
						current images are uploaded
					</span>
				</Select>
			</div>

			{#if files.length}
				<div class="col-span-2 mb-8 mt-4">
					<InputLabel class="mb-1" dataCy={`${dataCyPrefix}-add-more-files:`}
						>Files to upload:</InputLabel
					>
					<table class={'w-full table-fixed bg-white'}>
						<TableHeaderRow dataCy={dataCyPrefix}>
							{#each headers as header, i}
								<TableHeader
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
								>
									{header}
								</TableHeader>
							{/each}
						</TableHeaderRow>
						<TableBody dataCy={dataCyPrefix}>
							{#each files as file, i}
								{@const fileValues = getFileValues(file)}

								<TableRow index={i} dataCy={dataCyPrefix}>
									{#each fileValues as fileValue}
										<TableCell
											dataCy={dataCyPrefix}
											width={getCellWidth(i, actionCellWidth, headers)}
											content={fileValue}
										>
											{fileValue}
										</TableCell>
									{/each}

									<TableCell
										dataCy={dataCyPrefix}
										width={getCellWidth(i, actionCellWidth, headers)}
										content={statuses[i]}
										class={classNames({
											'[&_p]:text-red-500': statuses[i] === 'Error uploading',
											'[&_p]:text-green-500': statuses[i] === 'Uploaded',
											'[&_p]:text-gray-500': statuses[i] === 'Not uploaded',
											'[&_p]:text-blue-500': statuses[i] === 'Uploading...',
										})}
									>
										{statuses[i]}
									</TableCell>

									<TableActionCell
										dataCy={dataCyPrefix}
										width={actionCellWidth}
									>
										{#if !submitting}
											<Button
												dataCy={`${dataCyPrefix}-cell`}
												variant="secondary"
												icon
												type="button"
												size="sm"
												onclick={() => {
													files = [...files.slice(0, i), ...files.slice(i + 1)];
													statuses = [
														...statuses.slice(0, i),
														...statuses.slice(i + 1),
													];
												}}
												class="min-w-full [&>button]:min-w-full"
											>
												remove
											</Button>
										{/if}
									</TableActionCell>
								</TableRow>
							{/each}
						</TableBody>
					</table>
				</div>
			{/if}

			{#if !submitting}
				<div class="col-span-2">
					<InputLabel class="mb-1" dataCy={`${dataCyPrefix}-add-more-files:`}
						>Add more files to upload:</InputLabel
					>
					<Dropzone
						maxSize={100000000}
						showFiles={false}
						bind:files={dropzoneFiles}
						dataCy={dataCyPrefix}
						accept={['image/jpeg', 'image/png']}
						multiple
						onSubmitFiles={handleSubmitFiles}
					/>
				</div>
			{/if}
		</StepContainer>

		{#if !submitting}
			<StepButtons
				class="max-w-[32.5rem]"
				backButtonProps={{
					disabled: submitting,
					href: `/${page.url.pathname.split('/')[1]}/${page.params.id}`,
				}}
				continueButtonProps={{
					class: 'sm:basis-[23.75rem] sm:max-w-[23.75rem]',
					disabled: !files.length || !takenDate || !photographer,
					loading: submitting,
				}}
			>
				Submit
			</StepButtons>
		{:else if submitted}
			<div class="flex w-full justify-center">
				<div class="flex-col gap-4 sm:w-[32.5rem]">
					<Button
						fullWidth
						type="button"
						class="mb-2 inline-block w-full sm:max-w-[32.5rem]"
						dataCy={`${dataCyPrefix}-add-more-images`}
						size="md"
						onclick={() => {
							window.location.reload();
						}}
					>
						add more images
					</Button>

					<LinkButton
						fullWidth
						variant="secondary"
						class="inline-block w-full sm:max-w-[32.5rem]"
						dataCy={`${dataCyPrefix}-back`}
						size="md"
						href={`/${page.url.pathname.split('/')[1]}/${page.params.id}`}
					>
						{backButtonLabel}
					</LinkButton>
				</div>
			</div>
		{/if}
	</form>
</PageBody>
