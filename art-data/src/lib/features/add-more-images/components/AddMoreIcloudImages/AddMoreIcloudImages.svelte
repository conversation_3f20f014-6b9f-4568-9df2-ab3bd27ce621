<script lang="ts">
	import { onMount } from 'svelte';
	import {
		GetPhoneVisitImageGroupingsDocument,
		type GetPhoneVisitImageGroupingsQuery,
	} from '../../custom-queries/__generated__/getPhoneVisitImageGroupings.generated';
	import { AddMoreIcloudImagesTable } from './AddMoreIcloudImagesTable';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { PageBody } from '$lib/components/PageBody';
	import { gqlClientCustom } from '$lib/gqlClientCustom';

	interface Props {
		crumbs: Crumb[];
		dataCy: string;
	}

	let { crumbs, dataCy }: Props = $props();

	const dataCyPrefix = `${dataCy}-add-more-icloud-images`;
	let searching = $state(false);
	let data: NonNullable<
		GetPhoneVisitImageGroupingsQuery['phoneVisitImageGroupings']
	>['data'] = $state(undefined);
	let locationValue = $state('');
	let photographerValue = $state('');
	let dateCaptureValue = $state('');

	let pageNumber = $state(1);

	const handleClickSearch = async (e?: Event) => {
		e?.preventDefault();
		searching = true;
		data = undefined;
		pageNumber = 1;

		const phoneVisitImageGroupingsResponse = await gqlClientCustom.request(
			GetPhoneVisitImageGroupingsDocument,
			{
				input: {
					location: locationValue,
					imageDate: dateCaptureValue,
					photographerEmail: photographerValue,
				},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		data = phoneVisitImageGroupingsResponse?.phoneVisitImageGroupings?.data;
		searching = false;
	};

	const handleClickReset = () => {
		locationValue = '';
		photographerValue = '';
		dateCaptureValue = '';
	};

	onMount(() => {
		handleClickSearch();
	});
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<div class="mb-6 flex items-center justify-between">
			<Txt variant="h3">Add iCloud images</Txt>
		</div>

		<form
			onsubmit={(e: Event) => handleClickSearch(e)}
			class="mb-8 border border-gray-200 py-4"
		>
			<div class="border-b border-gray-200 px-4 pb-4">
				<Txt variant="h5">Search iCloud image uploads</Txt>
			</div>
			<div class="grid grid-cols-4 gap-4 px-4 py-4">
				<div class="col-span-1">
					<Input
						label="Location"
						name="location"
						dataCy={`${dataCyPrefix}-location`}
						placeholder="Location"
						bind:value={locationValue}
					/>
				</div>

				<div class="col-span-1">
					<Input
						label="Photographer"
						name="photographer"
						dataCy={`${dataCyPrefix}-photographer`}
						placeholder="Photographer"
						bind:value={photographerValue}
					/>
				</div>

				<div class="col-span-1">
					<Input
						label="Date of capture"
						name="date-capture"
						type="date"
						dataCy={`${dataCyPrefix}-date-capture`}
						placeholder="Date of capture"
						bind:value={dateCaptureValue}
					/>
				</div>
			</div>

			<div class="grid grid-cols-4 gap-4 border-t border-gray-200 px-4 pt-4">
				<Button disabled={searching} dataCy={dataCyPrefix} size="md"
					>search</Button
				>

				<Button
					variant="secondary"
					disabled={searching}
					type="button"
					dataCy={dataCyPrefix}
					onclick={handleClickReset}
					size="md">clear search fields</Button
				>
			</div>
		</form>

		{#if searching}
			<div class="flex justify-end">
				<CircularProgress dataCy={dataCyPrefix} />
			</div>
		{:else if data}
			<AddMoreIcloudImagesTable {data} bind:pageNumber />
		{/if}
	</Container>
</PageBody>
