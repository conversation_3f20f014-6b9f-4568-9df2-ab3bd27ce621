<script lang="ts">
	import dayjs from 'dayjs';
	import { page } from '$app/state';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { type GetPhoneVisitImageGroupingsQuery } from '$lib/features/add-more-images/custom-queries/__generated__/getPhoneVisitImageGroupings.generated';

	interface Props {
		pageNumber: number;
		data: NonNullable<
			NonNullable<
				GetPhoneVisitImageGroupingsQuery['phoneVisitImageGroupings']
			>['data']
		>;
	}

	let { pageNumber = $bindable(), data }: Props = $props();

	const LIMIT = 15;
	const actionCellWidth = '7rem';
	const headers = [
		'Date of Capture',
		'Location',
		'Photographer',
		'Total images',
		'',
	];

	const formatRow = (
		grouping: NonNullable<
			NonNullable<
				GetPhoneVisitImageGroupingsQuery['phoneVisitImageGroupings']
			>['data']
		>[number]
	) => [
		`${dayjs(grouping?.imageDate).format('DD/MM/YYYY')}`,
		`${grouping?.location}`,
		`${grouping?.photographerEmail}`,
		`${grouping?.numberOfImages}`,
	];

	let groupings = $derived(
		data.slice((pageNumber - 1) * LIMIT, pageNumber * LIMIT)
	);

	const dataCyPrefix = 'add-more-icloud-images-table';
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			{#if i === headers.length - 1}
				<TableHeader dataCy={dataCyPrefix} width={actionCellWidth} />
			{:else}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
				>
					{header}
				</TableHeader>
			{/if}
		{/each}
	</TableHeaderRow>

	<TableBody dataCy={dataCyPrefix}>
		{#each groupings as grouping, i}
			<TableRow index={i} dataCy={dataCyPrefix}>
				{@const formattedGrouping = formatRow(grouping)}
				{#each formattedGrouping as formattedGroupingValue, j}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						content={formattedGroupingValue}
					>
						{formattedGroupingValue}
					</TableCell>
				{/each}
				<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
					<LinkButton
						size="xs"
						dataCy={`${dataCyPrefix}-cell`}
						variant="secondary"
						href={`${page.url.pathname}/assign?date=${grouping?.imageDate}&location=${grouping?.location}&photographer=${grouping?.photographerEmail}&phone=${grouping?.phone}`}
					>
						Select Images
					</LinkButton>
				</TableActionCell>
			</TableRow>
		{/each}

		{#if !data.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No images to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>

<div class="mt-2 flex items-center justify-between">
	{#if data.length}
		<Txt variant="body3">
			Showing {(pageNumber - 1) * LIMIT + 1} - {(pageNumber - 1) * LIMIT +
				groupings.length} of {data.length}
			results
		</Txt>
	{/if}
	{#key pageNumber}
		{#key data.length}
			<Pagination
				onClick={(e: Event | undefined, newPage: number) => {
					pageNumber = newPage;
				}}
				dataCy={dataCyPrefix}
				currentPage={pageNumber}
				limit={LIMIT}
				total={data.length}
			/>
		{/key}
	{/key}
</div>
