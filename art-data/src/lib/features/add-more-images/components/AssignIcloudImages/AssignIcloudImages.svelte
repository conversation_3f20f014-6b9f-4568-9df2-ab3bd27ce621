<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel/Image';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	// import { Pagination } from '$global/components/Pagination';
	import { Radio } from '$global/components/Radio';
	import { Switch } from '$global/components/Switch';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { CreateVisitImageDocument } from '$lib/features/visit/queries/__generated__/createVisitImage.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { GetVisitImageDocument } from '$lib/queries/__generated__/getVisitImage.generated';
	import { UpdateVisitImagesDocument } from '$lib/queries/__generated__/updateVisitImages.generated';

	interface Props {
		visitId: string;
		title: string;
		dataCy: string;
		pageNumber: number;
		images: ImageCarouselImage[];
		children?: import('svelte').Snippet;
	}

	let {
		visitId,
		title,
		dataCy,
		pageNumber = $bindable(),
		images,
		children,
	}: Props = $props();

	let submitting = $state(false);
	let showAssigned = $state(false);
	let range = $state('all');
	let exclude = $state('');
	let include = $state('');
	let blurExclude = $state(false);
	let blurInclude = $state(false);

	let filteredImages = $derived(
		showAssigned ? images : images.filter((image) => !image.alt)
	);

	const VISIT_IMAGES_LIMIT = 80;

	// let pageFilteredImages = $derived(
	// 	(filteredImages || []).slice(
	// 		(pageNumber - 1) * VISIT_IMAGES_LIMIT,
	// 		pageNumber * VISIT_IMAGES_LIMIT
	// 	)
	// );

	let nbHiddenImages = $derived(images?.filter((image) => !!image.alt)?.length);

	const isValid = (value: string) => {
		const pages = value.replaceAll(' ', '').split(',');
		return pages.every(
			(page) =>
				/^\d/.test(page) &&
				/\d$/.test(page) &&
				/^([0-9]|-)+$/.test(page) &&
				page.split('-').length <= 2 &&
				!page.startsWith('-') &&
				!page.endsWith('-')
		);
	};

	const getImages = () => {
		if (range === 'all') {
			return filteredImages;
		}

		const value = range === 'exclude' ? exclude : include;
		const pages = value.replaceAll(' ', '').split(',');

		return filteredImages.filter((_, i) => {
			const normalizedIndex = i + 1;

			const checkPageFunction = (page: string) => {
				const pageParts = page.split('-');
				if (pageParts.length === 2) {
					return (
						normalizedIndex >= +pageParts[0] && normalizedIndex <= +pageParts[1]
					);
				}

				return normalizedIndex === +pageParts[0];
			};

			if (range === 'exclude') {
				return !pages.some(checkPageFunction);
			}

			return pages.some(checkPageFunction);
		});
	};

	const handleClickButton = async () => {
		submitting = true;

		await gqlClient.request(
			UpdateVisitImagesDocument,
			{
				ids: getImages()
					.filter((image) => !image.alt)
					.map((image) => `${image.id}`),
				data: {
					visit: { id: visitId },
					status: { key: 'AWAITING_COORDINATE_EXTRACTION' },
				},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		const assignedImagesPromises = getImages()
			.filter((image) => !!image.alt)
			.map(async (image) => {
				const assignedVisitImageResponse = await gqlClient.request(
					GetVisitImageDocument,
					{ filter: { id: { _eq: `${image.id}` } } },
					getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					)
				);

				const assignedVisitImage = assignedVisitImageResponse?.Visit_Image?.[0];

				return gqlClient.request(
					CreateVisitImageDocument,
					{
						data: {
							status: { key: 'AWAITING_COORDINATE_EXTRACTION' },
							image_taken_date: assignedVisitImage.image_taken_date,
							original_uncropped_image:
								assignedVisitImage.original_uncropped_image,
							photographer: assignedVisitImage.photographer?.id as any,
							visit_phone_registration: [
								{
									image_date:
										assignedVisitImage?.visit_phone_registration?.[0]
											?.image_date,
									phone:
										assignedVisitImage?.visit_phone_registration?.[0]?.phone,
									phone_registration: {
										...(assignedVisitImage?.visit_phone_registration?.[0]
											?.phone_registration as any),
										photographer: assignedVisitImage
											?.visit_phone_registration?.[0]?.phone_registration
											?.photographer?.id as string,
									},
								},
							],
							source: 'ios_app_duplicate',
							visit: {
								id: visitId,
							},
						},
					},
					getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					)
				);
			});

		await Promise.all(assignedImagesPromises);

		showToast({
			variant: 'success',
			message:
				"Visit images have been successfully assigned to this fair's visit",
		});

		goto(`/${page.url.pathname.split('/')[1]}/${page.params.id}`, {
			invalidateAll: true,
		});
	};
</script>

<Container {dataCy}>
	<div class="mb-6 flex items-end justify-between">
		<div>
			<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
				>{title}</Txt
			>
			<Txt variant="h4">Assign images to event</Txt>
		</div>
	</div>

	<div class="mb-16 flex items-start">
		<div>
			<Txt variant="h5" class="mb-2">Event Details</Txt>
			{@render children?.()}
		</div>
		<div class="ml-8">
			<Txt variant="h5" class="mb-2">Upload information</Txt>
			<div class="border border-gray-200 bg-gray-100 p-2">
				<div class="flex items-center border-b border-gray-200 pb-2">
					<Txt variant="body2" class="w-[300px] text-gray-500">Date</Txt>
					<Txt variant="body2" class="w-[300px]">
						{page.url.searchParams.get('date')}
					</Txt>
				</div>
				<div class="flex items-center border-b border-gray-200 py-2">
					<Txt variant="body2" class="w-[300px] text-gray-500">Location</Txt>
					<Txt variant="body2" class="w-[300px]">
						{page.url.searchParams.get('location')}
					</Txt>
				</div>
				<div class="flex items-center border-b border-gray-200 py-2">
					<Txt variant="body2" class="w-[300px] text-gray-500">Photographer</Txt
					>
					<Txt variant="body2" class="w-[300px]">
						{page.url.searchParams.get('photographer')}
					</Txt>
				</div>
				<div class="flex items-center pt-2">
					<Txt variant="body2" class="w-[300px] text-gray-500">Total images</Txt
					>
					<Txt variant="body2" class="w-[300px]">
						{(images || []).length}
						{nbHiddenImages ? `(${nbHiddenImages} assigned)` : ''}
					</Txt>
				</div>
			</div>
		</div>
	</div>

	{#if nbHiddenImages}
		<div class="mb-2 flex items-center justify-end gap-2">
			<Txt variant="label3">Show assigned images</Txt>
			<Switch
				dataCy="test"
				bind:checked={showAssigned}
				onChange={(value) => {
					pageNumber = 1;
					range = 'all';
					include = '';
					exclude = '';
				}}
			/>
		</div>
	{/if}

	<div class="rounded border border-gray-200 bg-gray-50 p-5 pb-1">
		<div class="grid grid-cols-8">
			{#key filteredImages}
				{#each filteredImages as image, i}
					<div>
						<a target="_blank" rel="noopener noreferrer" href={image.url}>
							<Image loading="lazy" {dataCy} {image} />
						</a>

						<Txt variant="body2" class="mt-0.5 text-gray-500">
							{(pageNumber - 1) * VISIT_IMAGES_LIMIT + (i + 1)}
						</Txt>
					</div>
				{/each}
				{#if !filteredImages?.length}
					<div class="col-span-8 flex justify-center h-[36px]">
						<Txt variant="body2" class="mb-[-16px] text-center text-gray-500"
							>No images</Txt
						>
					</div>
				{/if}
			{/key}
		</div>

		<!-- <div class="my-4 flex justify-end">
			{#key pageNumber}
				{#key filteredImages?.length}
					<Pagination
						onClick={(e: Event | undefined, page: number) => {
							pageNumber = page;
						}}
						{dataCy}
						currentPage={pageNumber}
						limit={VISIT_IMAGES_LIMIT}
						total={filteredImages?.length}
					/>
				{/key}
			{/key}
		</div> -->
	</div>

	<div class="mt-4 rounded border border-gray-200 bg-gray-50 p-5">
		<Txt variant="h6" class="mb-6">Which images would you like to assign?</Txt>

		<div class="mb-6">
			<InputLabel dataCy={`${dataCy}-include`} variant="label3"
				><Radio
					size="sm"
					bind:group={range}
					id="all"
					dataCy={`${dataCy}`}
					name="range"
					value="all"
				/>All images</InputLabel
			>
		</div>

		<div class="mb-3 flex items-center">
			<InputLabel
				classes={{ container: 'w-auto mr-2' }}
				dataCy={`${dataCy}-include`}
				variant="label3"
				><Radio
					size="sm"
					bind:group={range}
					id="specific-range"
					dataCy={`${dataCy}`}
					name="range"
					value="include"
				/>Specific range</InputLabel
			>

			<InfoTooltip
				{dataCy}
				content="You can include specific images either individually or within a range, such as: 1,2-5,4,6"
			/>

			<div class="ml-2">
				<Input
					class="min-w-[9rem] max-w-[9rem]"
					name="include"
					dataCy={`${dataCy}-include`}
					placeholder="eg. 1, 2, 24"
					bind:value={include}
					onblur={() => {
						blurInclude = true;
					}}
					classes={{ error: 'absolute' }}
					error={!isValid(include) && blurInclude && range === 'include'
						? 'Invalid range'
						: ''}
				/>
			</div>
		</div>

		<div class="mb-6 flex items-center">
			<InputLabel
				classes={{ container: 'w-auto mr-2' }}
				dataCy={`${dataCy}-exclude`}
				variant="label3"
				><Radio
					size="sm"
					bind:group={range}
					id="all-except-specific-range"
					dataCy={`${dataCy}`}
					name="range"
					value="exclude"
				/>All except specific images</InputLabel
			>

			<InfoTooltip
				{dataCy}
				content="You can exclude specific images either individually or within a range, such as: 1,2-5,4,6"
			/>

			<div class="ml-2">
				<Input
					class="min-w-[9rem] max-w-[9rem]"
					name="exclude"
					dataCy={`${dataCy}-exclude`}
					placeholder="eg. 1, 2, 24"
					bind:value={exclude}
					onblur={() => {
						blurExclude = true;
					}}
					classes={{ error: 'absolute' }}
					error={!isValid(exclude) && blurExclude && range === 'exclude'
						? 'Invalid range'
						: ''}
				/>
			</div>
		</div>

		<Button loading={submitting} onclick={handleClickButton} {dataCy} size="md"
			>assign to visit</Button
		>
	</div>
</Container>
