import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type GetPhoneVisitImageGroupingsQueryVariables = Types.Exact<{
	input?: Types.InputMaybe<Types.PhoneVisitGroupingsInput>;
}>;

export type GetPhoneVisitImageGroupingsQuery = {
	__typename?: 'Query';
	phoneVisitImageGroupings?: {
		__typename?: 'PhoneVisitGroupingsResponse';
		data?: Array<{
			__typename?: 'PhoneVisitGrouping';
			imageDate: string;
			location?: string | null;
			numberOfImages: number;
			phone: number;
			photographerEmail?: string | null;
			photographerId?: string | null;
		} | null> | null;
	} | null;
};

export const GetPhoneVisitImageGroupingsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getPhoneVisitImageGroupings' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PhoneVisitGroupingsInput' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'phoneVisitImageGroupings' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'imageDate' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'numberOfImages' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'phone' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'photographerEmail' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'photographerId' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetPhoneVisitImageGroupingsQuery,
	GetPhoneVisitImageGroupingsQueryVariables
>;
