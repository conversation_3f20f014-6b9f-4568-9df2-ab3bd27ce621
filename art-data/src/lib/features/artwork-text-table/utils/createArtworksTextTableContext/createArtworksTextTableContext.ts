import { writable, get } from 'svelte/store';
import { getImageAlt } from '../../components/ArtworksTextTable/ArtworksTextTableRow';
import type { ImageCarouselImage } from '$global/components/ImageCarousel';
import { extractNonNull } from '$global/utils/extractNonNull/extractNonNull';
import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
import {
	type EditExtractedImage,
	type FormatToggleState,
	SelectedImageStatus,
} from '$lib/features/edit-extracted-images/types';
import type { ArtworkInfo, ArtworkImages, ArtworkFile } from '$lib/types';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const createArtworksTextTableContext = () => {
	const artworkInfo = writable<ArtworkInfo[]>([]);
	const artworkImages = writable<ArtworkImages[]>([]);
	const artworkFiles = writable<ArtworkFile[]>([]);

	const getPageFromImages = (images: ArtworkImages['images']) =>
		Array.from(
			new Set(images.map((image) => image.page || 0).filter(Boolean))
		).sort();

	const getNewArtwork = () => ({
		info: {
			id: '',
			text: '',
			status: 'FOR_SALE',
		} as ArtworkInfo,
		images: { images: [], page: undefined } as ArtworkImages,
	});

	const replaceArtworkImages = (
		newArtworkImages: ArtworkImages,
		index: number
	) => {
		artworkImages.set([
			...get(artworkImages).slice(0, index),
			newArtworkImages,
			...get(artworkImages).slice(index + 1, get(artworkImages).length),
		]);
	};

	const replaceArtworkInfo = (newArtworkInfo: ArtworkInfo, index: number) => {
		artworkInfo.set([
			...get(artworkInfo).slice(0, index),
			newArtworkInfo,
			...get(artworkInfo).slice(index + 1, get(artworkInfo).length),
		]);
	};

	const setArtworkText = (text: string, index: number) => {
		const updatedArtworkInfo = {
			...get(artworkInfo)[index],
			text,
		};

		replaceArtworkInfo(updatedArtworkInfo, index);
	};

	const setArtworkStatus = (status: string, index: number) => {
		const updatedArtworkInfo = {
			...get(artworkInfo)[index],
			status,
		};

		replaceArtworkInfo(updatedArtworkInfo, index);
	};

	const addArtwork = (index: number) => {
		const newArtwork = getNewArtwork();

		artworkInfo.set([
			...get(artworkInfo).slice(0, index + 1),
			newArtwork.info,
			...get(artworkInfo).slice(index + 1),
		]);

		artworkImages.set([
			...get(artworkImages).slice(0, index + 1),
			newArtwork.images,
			...get(artworkImages).slice(index + 1),
		]);
	};

	const deleteArtwork = (index: number) => {
		handleDeleteArtworkImages(index);

		artworkInfo.set(
			get(artworkInfo).filter(
				(_, artworkInfoIndex) => artworkInfoIndex !== index
			)
		);

		artworkImages.set(
			get(artworkImages).filter(
				(_, artwornImagesIndex) => artwornImagesIndex !== index
			)
		);
	};

	const handleDragImageInTheSameList = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		listIndex: number
	) => {
		const existingArtworkImages = get(artworkImages)[listIndex];

		const newImages = [
			...existingArtworkImages.images.filter(
				(image) => image.filename_disk !== movedImage.filename_disk
			),
		];

		newImages.splice(newIndex, 0, movedImage);

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [...newImages],
		};

		replaceArtworkImages(updatedArtworkImages, listIndex);
	};

	const handleDragFromExternalSource = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		toListIndex: number
	) => {
		const toArtworkImages = get(artworkImages)[toListIndex];
		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, movedImage);
		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
			page: getPageFromImages(toArtworkImagesImages),
		};

		replaceArtworkImages(updatedToArtworkImages, toListIndex);
	};

	const handleDragImage = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		fromListIndex: number,
		toListIndex: number
	) => {
		if (fromListIndex === toListIndex) {
			handleDragImageInTheSameList(movedImage, newIndex, fromListIndex);
		} else {
			const fromArtworkImages = get(artworkImages)[fromListIndex];
			const newFromImages = [
				...fromArtworkImages.images.filter(
					(image) => image.filename_disk !== movedImage.filename_disk
				),
			];

			const updatedFromArtworkImages = {
				...fromArtworkImages,
				images: [...newFromImages],
				page: getPageFromImages(newFromImages),
			};

			const toArtworkImages = get(artworkImages)[toListIndex];
			const toArtworkImagesImages = [...toArtworkImages.images];
			toArtworkImagesImages.splice(newIndex, 0, movedImage);

			const updatedToArtworkImages = {
				...toArtworkImages,
				images: [...toArtworkImagesImages],
				page: getPageFromImages(toArtworkImagesImages),
			};

			if (fromListIndex < toListIndex) {
				artworkImages.set([
					...get(artworkImages).slice(0, fromListIndex),
					updatedFromArtworkImages,
					...get(artworkImages).slice(fromListIndex + 1, toListIndex),
					updatedToArtworkImages,
					...get(artworkImages).slice(
						toListIndex + 1,
						get(artworkImages).length
					),
				]);
			} else {
				artworkImages.set([
					...get(artworkImages).slice(0, toListIndex),
					updatedToArtworkImages,
					...get(artworkImages).slice(toListIndex + 1, fromListIndex),
					updatedFromArtworkImages,
					...get(artworkImages).slice(
						fromListIndex + 1,
						get(artworkImages).length
					),
				]);
			}
		}
	};

	const handleSubmittedFiles = async (index: number, files: File[]) => {
		const newImages = (
			await Promise.all(
				files.map(async (file: File) => imageFileToBase64(file))
			)
		).map((newImage, i) => ({
			...newImage,
			filename_disk: `${newImage.file.name}_${+new Date()}`,
			alt: getImageAlt(i),
		}));

		const existingArtworkImages = get(artworkImages)[index];
		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [
				...existingArtworkImages.images,
				...newImages,
			] as ArtworkImages['images'],
			page: getPageFromImages([
				...existingArtworkImages.images,
				...newImages,
			] as ArtworkImages['images']),
		};

		replaceArtworkImages(updatedArtworkImages, index);
		return Promise.resolve(newImages);
	};

	const handleShiftImagesDown = (index: number) => {
		const updatedArtworkImages = [...get(artworkImages)];

		updatedArtworkImages[index + 1] = {
			images: [
				...[...get(artworkImages)][index + 1].images,
				...[...get(artworkImages)][index].images,
			],
			page: getPageFromImages([
				...[...get(artworkImages)][index + 1].images,
				...[...get(artworkImages)][index].images,
			]),
		};

		for (let i = index; i > 0; i--) {
			updatedArtworkImages[i] = {
				images: [...[...get(artworkImages)][i - 1].images],
				page: getPageFromImages([...[...get(artworkImages)][i - 1].images]),
			};
		}

		updatedArtworkImages[0] = {
			images: [],
			page: getPageFromImages([]),
		};

		artworkImages.set(updatedArtworkImages);
	};

	const handleShiftImagesUp = (index: number) => {
		const updatedArtworkImages = [...get(artworkImages)];

		updatedArtworkImages[index - 1] = {
			images: [
				...updatedArtworkImages[index - 1].images,
				...updatedArtworkImages[index].images,
			],
			page: getPageFromImages([
				...updatedArtworkImages[index - 1].images,
				...updatedArtworkImages[index].images,
			]),
		};

		for (let i = index; i < updatedArtworkImages.length; i++) {
			updatedArtworkImages[i] =
				updatedArtworkImages[i + 1] || getNewArtwork().images;
		}

		artworkImages.set(updatedArtworkImages);
	};

	const handleDeleteArtworkImages = (index: number) => {
		const artworkImagesRow = get(artworkImages)[index];
		const existingArtworkImagesMaxIndex = artworkImagesRow.images.length - 1;
		for (let i = existingArtworkImagesMaxIndex; i >= 0; i--) {
			handleDeleteImage(index, artworkImagesRow.images[i]);
		}
	};

	const handleDeleteImage = (index: number, image: ImageCarouselImage) => {
		const existingArtworkImages = get(artworkImages)[index];

		const newImages = existingArtworkImages.images.filter(
			({ filename_disk }) => filename_disk !== image['filename_disk']
		);

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: newImages,
			page: getPageFromImages(newImages),
		};

		replaceArtworkImages(updatedArtworkImages, index);
	};

	const isEveryRowFilledIn = () =>
		get(artworkInfo).every((artworkInfo) => !!artworkInfo.text) &&
		get(artworkImages).every((artworkImage) => !!artworkImage.images.length);

	const isImageChecked = (imageStatus: {
		status: SelectedImageStatus;
		discardTimestamp: null | number;
	}) => imageStatus.status === SelectedImageStatus.Selected;

	const formatArtworksUsingAiText = (
		images: EditExtractedImage[],
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[],
		aiText: string
	) => {
		const aiTextParagraphs = aiText.split(/\n{2,}/);
		const filteredImages = images.filter(
			(image, i) =>
				isImageChecked(selectedImageStatuses[i]) &&
				extractNonNull(image, ['width', 'height', 'page'], {})
		);

		return aiTextParagraphs.map((aiTextParagraph, i) => ({
			images: {
				images: filteredImages[i]
					? [{ ...filteredImages[i], id: filteredImages[i].filename_disk }]
					: [],
				page: filteredImages[i] ? [filteredImages[i].page] : [],
			} as ArtworkImages,
			info: {
				status: 'FOR_SALE',
				text: aiTextParagraph,
				id: '',
			},
		}));
	};

	const formatArtworksUsingLineBreaks = (
		images: EditExtractedImage[],
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[]
	) => {
		let parsedPages: number[] = [];

		return images.reduce(
			(
				accumulator: Record<
					number,
					{ images: ArtworkImages; info: ArtworkInfo }
				>,
				image,
				index
			) => {
				if (parsedPages.includes(images[index]?.page)) {
					return accumulator;
				}

				parsedPages = [...parsedPages, images[index]?.page];

				const startOffset = Object.keys(accumulator).length;
				const paragraphs = image?.text.split(/\n{2,}/);
				const imagesOnTheSamePage = images
					?.filter(
						(potentialImageOnTheSamePage, index) =>
							potentialImageOnTheSamePage?.page === image?.page &&
							isImageChecked(selectedImageStatuses[index])
					)
					?.map((imageOnTheSamePage) => ({
						...imageOnTheSamePage,
						id: imageOnTheSamePage.filename_disk,
					}));

				const newEntries = paragraphs.reduce(
					(accumulator, paragraph, index) => {
						return {
							...accumulator,
							[startOffset + index]: {
								images: {
									images: imagesOnTheSamePage
										.slice(
											index,
											index === paragraphs.length - 1
												? imagesOnTheSamePage.length
												: index + 1
										)
										.filter((paragraphImage) =>
											extractNonNull(
												paragraphImage,
												['width', 'height', 'page'],
												{}
											)
										),
									page: [image.page],
								} as ArtworkImages,
								info: {
									status: 'FOR_SALE',
									text: paragraph,
									id: '',
								},
							},
						};
					},
					{}
				);

				return {
					...accumulator,
					...newEntries,
				};
			},
			{} as Record<number, { images: ArtworkImages; info: ArtworkInfo }>
		);
	};

	const formatArtworksByPage = (
		images: EditExtractedImage[],
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[]
	) => {
		return images.reduce(
			(
				accumulator: Record<
					number,
					{ images: ArtworkImages; info: ArtworkInfo }
				>,
				image,
				index
			) => {
				if (!isImageChecked(selectedImageStatuses[index])) {
					return accumulator;
				}

				const safeImage = extractNonNull(
					image,
					['width', 'height', 'page'],
					{}
				);

				if (!safeImage) {
					return accumulator;
				}
				return {
					...accumulator,
					[index]: {
						images: {
							images: [{ ...safeImage, id: safeImage.filename_disk }],
							page: [image.page],
						} as ArtworkImages,
						info: {
							status: 'FOR_SALE',
							text: images[index]?.text,
							id: '',
						},
					},
				};
			},
			{} as Record<number, { images: ArtworkImages; info: ArtworkInfo }>
		);
	};

	const resetArtworksTable = (
		pages: FormatToggleState['pages'],
		newArtworks: ReturnType<typeof formatArtworksByPage>
	) => {
		artworkFiles.set(
			(pages || []).flatMap((page) =>
				(page?.extracted_images || []).map(
					(
						extractedImage:
							| {
									image?: {
										__typename?: 'directus_files';
										filename_disk?: string | null;
										width?: number | null;
										height?: number | null;
										filename_download: string;
										storage: string;
										id: string;
									} | null;
							  }
							| null
							| undefined
					) => ({
						url: `${getImageUrl(extractedImage?.image?.id, '512px')}`,
						name: `${extractedImage?.image?.filename_disk}`,
						storage: `${extractedImage?.image?.storage}`,
						id: `${extractedImage?.image?.id}`,
						filename_disk: `${extractedImage?.image?.filename_disk}`,
						filename_download: `${extractedImage?.image?.filename_download}`,
					})
				)
			)
		);

		artworkImages.set(
			Object.values(newArtworks).map((artwork) => artwork.images)
		);

		artworkInfo.set(Object.values(newArtworks).map((artwork) => artwork.info));
	};

	const isTextMultipleParagraphs = (text: string) =>
		text?.split(/\n{2,}/)?.filter(Boolean)?.length > 1;

	const isArtworkInfoMultipleParagraphs = (artworkInfo: ArtworkInfo[]) => {
		return artworkInfo.some((info) => isTextMultipleParagraphs(info?.text));
	};

	return {
		isTextMultipleParagraphs,
		isArtworkInfoMultipleParagraphs,
		isImageChecked,
		resetArtworksTable,
		formatArtworksUsingAiText,
		formatArtworksByPage,
		formatArtworksUsingLineBreaks,
		artworkFiles,
		artworkInfo,
		artworkImages,
		addArtwork,
		deleteArtwork,
		handleDragImage,
		handleSubmittedFiles,
		handleDeleteImage,
		handleDeleteArtworkImages,
		handleShiftImagesUp,
		handleShiftImagesDown,
		handleDragFromExternalSource,
		setArtworkText,
		setArtworkStatus,
		isEveryRowFilledIn,
	};
};
