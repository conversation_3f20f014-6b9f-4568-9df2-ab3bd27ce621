<script lang="ts">
	import { setContext } from 'svelte';
	import { createArtworksTextTableContext } from '../../utils/createArtworksTextTableContext/createArtworksTextTableContext';
	import { Contexts } from '$lib/constants/contexts';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	setContext(Contexts.ArtworkTextTable, createArtworksTextTableContext());
</script>

{@render children?.()}
