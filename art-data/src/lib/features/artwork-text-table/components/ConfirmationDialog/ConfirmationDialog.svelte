<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import {
		ErrorSuccessPage,
		type ErrorSuccessPageProps,
	} from '$global/features/form/pages/ErrorSuccessPage';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		dataCy: string;
		errorButtonProps: ErrorSuccessPageProps['buttonProps'];
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props: ErrorSuccessPageProps['button2Props'];
		successMessage: string;
		errorMessage: string;
		slotLabel: string;
		submitSubmitting: boolean;
		onSave?: ((errorCallback: () => void) => Promise<void>) | undefined;
		children?: import('svelte').Snippet;
	}

	let {
		dialogStores,
		dataCy,
		errorButtonProps,
		buttonProps,
		button2Props,
		successMessage,
		errorMessage,
		slotLabel,
		submitSubmitting = $bindable(),
		onSave = undefined,
		children,
	}: Props = $props();

	const { artworkImages } = getArtworksTextTableContext();

	const handleClickBack = () => {
		dialogStores.states.open.set(false);
	};

	const handleClickConfirm = async () => {
		submitSubmitting = true;

		if (onSave) {
			await onSave(() => {
				submitSubmitting = false;
			});
		}

		showToast({
			variant: 'success',
			message: `Artworks have successfully been submitted.`,
		});

		step = 1;
	};

	let dataCyPrefix = $derived(`${dataCy}-confirmation`);
	let nbImages = $derived(
		$artworkImages.reduce((accumulator, artworkImage) => {
			return accumulator + artworkImage.images.length;
		}, 0)
	);

	let step = $state(0);
</script>

<Dialog
	dataCy={dataCyPrefix}
	{dialogStores}
	shouldClose={false}
	showCloseIcon={false}
	showOverlay
	class="w-[31.25rem] sm:p-8"
>
	{#if step === 0}
		<Txt variant="h4" class="mb-6">Please review before submitting</Txt>
		<Txt variant="label3" class="mb-2">You are about to process</Txt>

		<div class="mb-6 rounded border border-gray-200 p-3">
			<Txt variant="body2" class="mb-1">{$artworkImages.length} artworks</Txt>
			<Txt variant="body2" class="mb-1">{nbImages} images</Txt>
		</div>

		<Txt variant="label3" class="mb-2">{slotLabel}</Txt>

		{@render children?.()}

		<div class="mt-6 grid grid-cols-2 gap-2">
			<Button
				onclick={handleClickBack}
				size="md"
				disabled={submitSubmitting}
				dataCy={`${dataCyPrefix}-back`}
				variant="secondary">back</Button
			>
			<Button
				size="md"
				disabled={submitSubmitting}
				loading={submitSubmitting}
				onclick={handleClickConfirm}
				dataCy={`${dataCyPrefix}-back`}>confirm</Button
			>
		</div>
	{/if}

	{#if step === 1}
		<ErrorSuccessPage
			{buttonProps}
			{button2Props}
			dataCy={dataCyPrefix}
			variant="success"
			title="Uploaded and queued for extraction"
			class="min-h-fit"
			classes={{ textContainer: 'sm:mb-6', buttonContainer: 'max-w-none' }}
		>
			{successMessage}
		</ErrorSuccessPage>
	{/if}

	{#if step === -1}
		<ErrorSuccessPage
			buttonProps={errorButtonProps}
			dataCy={dataCyPrefix}
			variant="error"
			title="There was a problem"
			class="min-h-fit"
			classes={{ textContainer: 'sm:mb-6', buttonContainer: 'max-w-none' }}
		>
			{errorMessage}
		</ErrorSuccessPage>
	{/if}
</Dialog>
