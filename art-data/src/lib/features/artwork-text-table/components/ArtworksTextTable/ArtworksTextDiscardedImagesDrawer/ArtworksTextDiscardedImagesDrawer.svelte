<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	// eslint-disable-next-line import/default
	import Sortable from 'sortablejs';
	import { DragIcon } from '$global/assets/icons/DragIcon';
	import { Button } from '$global/components/Button';
	import { Image } from '$global/components/ImageCarousel/Image';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel/Image';
	import { DEFAULT_GROUP } from '$global/components/ImageCarousel/ImageCarousel.svelte';
	// import { Tabs } from '$global/components/Tabs';
	import { Txt } from '$global/components/Txt';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import { DiscardedImagesDrawer } from '$lib/features/discarded-images-drawer/components/DiscardedImagesDrawer';
	import {
		SelectedImageStatus,
		type EditExtractedImage,
	} from '$lib/features/edit-extracted-images/types';

	interface Props {
		images: EditExtractedImage[];
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];
		drawerStores: ReturnType<typeof createDialog>;
		onDrag: (image: ImageCarouselImage) => void;
	}

	let { images, selectedImageStatuses, drawerStores, onDrag }: Props = $props();

	const { handleDragFromExternalSource } = getArtworksTextTableContext();

	let activeTab = 1;

	const dataCyPrefix = 'artworks-text-discarded-images-drawer';

	// const tabs = [
	// 	{
	// 		id: SelectedImageStatus.RecentlyDiscarded,
	// 		title: 'Recently Discarded',
	// 	},
	// 	{
	// 		id: SelectedImageStatus.Discarded,
	// 		title: 'All Discarded',
	// 	},
	// ];

	let filteredImages = $derived(
		images
			.filter((_, index) => {
				if (activeTab) {
					return [
						SelectedImageStatus.Discarded,
						SelectedImageStatus.RecentlyDiscarded,
						SelectedImageStatus.ExcludedFromFormat,
					].includes(selectedImageStatuses[index].status);
				} else {
					return (
						selectedImageStatuses[index].status ===
						SelectedImageStatus.RecentlyDiscarded
					);
				}
			})
			.sort((a, b) => {
				const indexA = images.findIndex(
					(image) => image.filename_disk === a.filename_disk
				);

				const indexB = images.findIndex(
					(image) => image.filename_disk === b.filename_disk
				);

				return (
					(selectedImageStatuses[indexB].discardTimestamp || 0) -
					(selectedImageStatuses[indexA].discardTimestamp || 0)
				);
			}) as ImageCarouselImage[]
	);

	$effect(() => {
		if (!filteredImages.length) {
			drawerStores.states.open.set(false);
		}
	});

	const sortableList = (element: HTMLUListElement) => {
		Sortable.create(element, {
			animation: 150,
			group: { name: DEFAULT_GROUP },
			sort: false,
			onEnd: (event) => {
				if (event.to.id.length) {
					const toListIndex = +event.to.id.split('#')[1];
					const { oldIndex = 0, newIndex = 0 } = event;
					const movedItem = filteredImages[oldIndex];
					onDrag(movedItem);
					handleDragFromExternalSource(movedItem, newIndex, toListIndex);
				}
			},
		});
	};
</script>

<DiscardedImagesDrawer dataCy="artworks-text" {drawerStores}>
	<!-- <div class="h-[1px] w-full translate-y-[3.375rem] bg-gray-200" /> -->

	<!-- <Tabs
		dataCy="discard"
		{tabs}
		bind:activeTab
		class="h-full px-6"
		classes={{
			inactiveTabLabel: 'text-gray-700',
			container: 'mt-0 h-[calc(100%-3.5rem)] pt-4',
		}}
	>
		{#key filteredImages}
			<ul use:sortableList class="flex flex-wrap gap-2">
				{#if !filteredImages.length}
					<Txt>No image</Txt>
				{:else}
					{#each filteredImages as filteredImage}
						<li>
							<Image
								dataCy={dataCyPrefix}
								image={filteredImage}
								draggable="true"
							>
								<Button
									dataCy={`${dataCyPrefix}-image-drag`}
									class="absolute bottom-2 right-2 h-[1.5rem] w-[1.5rem] px-0"
									variant="secondary"
								>
									<DragIcon class="h-5 w-5" />
								</Button>
							</Image>
						</li>
					{/each}
				{/if}
			</ul>
		{/key}
	</Tabs> -->

	<div class="h-full px-6">
		{#key filteredImages}
			<ul use:sortableList class="flex flex-wrap gap-2 pt-4">
				{#each filteredImages as filteredImage}
					<li>
						<Image dataCy={dataCyPrefix} image={filteredImage} draggable="true">
							<Button
								size="xs"
								dataCy={`${dataCyPrefix}-image-drag`}
								class="absolute bottom-2 right-2 h-[1.5rem] w-[1.5rem] px-0"
								variant="secondary"
							>
								<DragIcon class="h-5 w-5" />
							</Button>
						</Image>
					</li>
				{/each}
				<li></li>
			</ul>
		{/key}
	</div>
</DiscardedImagesDrawer>
