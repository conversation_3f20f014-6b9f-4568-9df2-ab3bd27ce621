<script lang="ts" module>
	export const STATUS_SELECT_WIDTH = 9.25;
</script>

<script lang="ts">
	import { Select } from '$global/components/Select';
	import type { SelectChangeEvent } from '$global/components/Select';

	interface Props {
		onchange?: ((e: SelectChangeEvent) => void) | undefined;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		dataCy: string;
		value?: any;
	}

	let {
		artworkSaleStatuses,
		dataCy,
		value = $bindable(artworkSaleStatuses[0].key),
		onchange,
	}: Props = $props();
</script>

<div style:width={`${STATUS_SELECT_WIDTH}rem`}>
	<Select
		{onchange}
		bind:value
		ariaLabel="Select status to update"
		name="status"
		dataCy={`${dataCy}-status`}
		options={artworkSaleStatuses.map((artworkSaleStatus) => ({
			label: artworkSaleStatus.name,
			value: artworkSaleStatus.key,
		}))}
	/>
</div>
