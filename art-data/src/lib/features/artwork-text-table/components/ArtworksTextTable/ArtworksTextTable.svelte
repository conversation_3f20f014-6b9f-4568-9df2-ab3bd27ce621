<script lang="ts">
	import classNames from 'classnames';
	import { getArtworksTextTableContext } from '../../utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import {
		ArtworksTextTableHeader,
		EXPAND_OPTIONS,
	} from './ArtworksTextTableHeader';

	import { ArtworksTextTableRow } from './ArtworksTextTableRow';
	import { STATUS_SELECT_WIDTH } from './StatusSelect';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';

	import { Button } from '$global/components/Button';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel';
	import { type FormatToggleState } from '$lib/features/edit-extracted-images/types';

	interface Props {
		formatToggleState: FormatToggleState;
		onDeleteImage?: ((image: ImageCarouselImage) => void) | undefined;
		showPage?: boolean | undefined;
		hasSubmitted: boolean;
		dataCy: string;
		submitting: boolean;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		onSubmitFiles?:
			| undefined
			| ((
					newImages: {
						filename_disk: string;
						alt: string;
						file: File;
						url: string;
						width: number;
						height: number;
					}[]
			  ) => void);
		children?: import('svelte').Snippet;
	}

	let {
		formatToggleState,
		onDeleteImage = undefined,
		showPage = undefined,
		hasSubmitted,
		dataCy,
		submitting,
		artworkSaleStatuses,
		onSubmitFiles = undefined,
		children,
	}: Props = $props();

	const headers = [
		...(showPage ? ['page'] : []),
		'artwork',
		'text',
		'for-sale',
	];

	const { artworkImages, addArtwork } = getArtworksTextTableContext();
	const actionCellWidth = `${STATUS_SELECT_WIDTH + 2.5}rem`;
	const pageCellWidth = '5rem';

	let expandAllValue = $state(EXPAND_OPTIONS[0].value);

	// Used only to get the number of non action cells, technically the non action one is the last header
	const [_, ...nonActionHeaders] = headers;

	const nonActionCellWidth = `calc((100% - ${
		+actionCellWidth.slice(0, -3) +
		(showPage ? +pageCellWidth.slice(0, -3) : 0) +
		nonActionHeaders.length * 1.5
	}rem) / 2)`;
</script>

{@render children?.()}

<div class={classNames('mb-12', { 'pointer-events-none': submitting })}>
	<div class="relative">
		<ArtworksTextTableHeader
			pageCellWidth={showPage ? pageCellWidth : undefined}
			bind:value={expandAllValue}
			{actionCellWidth}
			{nonActionCellWidth}
			{dataCy}
		/>
		<div class="absolute bottom-[-12px] right-[12px] z-10">
			<Button
				size="sm"
				dataCy={`${dataCy}-add`}
				class="h-[1.5rem] w-[1.5rem] px-0"
				variant="secondary"
				onclick={() => {
					addArtwork(-1);
				}}
			>
				<PlusIcon class="h-3 w-3" />
			</Button>
		</div>
	</div>

	<div style:min-height={`${$artworkImages.length * 153}px`}>
		{#key $artworkImages}
			{#each $artworkImages as _, index}
				<ArtworksTextTableRow
					{index}
					{artworkSaleStatuses}
					{formatToggleState}
					{onDeleteImage}
					pageCellWidth={showPage ? pageCellWidth : undefined}
					{hasSubmitted}
					{expandAllValue}
					{actionCellWidth}
					{nonActionCellWidth}
					{dataCy}
					{onSubmitFiles}
				/>
			{/each}
		{/key}
	</div>
</div>
