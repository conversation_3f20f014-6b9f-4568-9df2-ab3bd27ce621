<script lang="ts" module>
	export const getImageAlt = (i: number) => `carousel image #${i}`;
</script>

<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	// eslint-disable-next-line import/default
	import Sortable from 'sortablejs';
	import { ExpandValue } from '../ArtworksTextTableHeader/types';
	import { STATUS_SELECT_WIDTH, StatusSelect } from '../StatusSelect';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { ChevronDownIcon } from '$global/assets/icons/ChevronDownIcon';
	import { ChevronUpIcon } from '$global/assets/icons/ChevronUpIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import {
		Dropzone,
		DropzoneContent,
		getSupportedFormats,
	} from '$global/components/Dropzone';
	import { ImageCarousel } from '$global/components/ImageCarousel';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel';
	import { DEFAULT_GROUP } from '$global/components/ImageCarousel/ImageCarousel.svelte';
	import { Input } from '$global/components/Input';
	import { Txt } from '$global/components/Txt';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import {
		FormatMethod,
		type FormatToggleState,
	} from '$lib/features/edit-extracted-images/types';

	interface Props {
		pageCellWidth?: string | undefined;
		expandAllValue: ExpandValue;
		dataCy: string;
		formatToggleState: FormatToggleState;
		nonActionCellWidth: string;
		actionCellWidth: string;
		index: number;
		hasSubmitted: boolean;
		onDeleteImage?: ((image: ImageCarouselImage) => void) | undefined;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		onSubmitFiles?:
			| undefined
			| ((
					newImages: {
						filename_disk: string;
						alt: string;
						file: File;
						url: string;
						width: number;
						height: number;
					}[]
			  ) => void);
	}

	let {
		pageCellWidth = undefined,
		expandAllValue,
		dataCy,
		formatToggleState,
		nonActionCellWidth,
		actionCellWidth,
		index,
		hasSubmitted,
		onDeleteImage = undefined,
		artworkSaleStatuses,
		onSubmitFiles = undefined,
	}: Props = $props();

	const dialogStores = createDialog();

	const {
		addArtwork,
		deleteArtwork,
		handleSubmittedFiles,
		handleDeleteImage,
		handleShiftImagesUp,
		handleShiftImagesDown,
		setArtworkText,
		setArtworkStatus,
		handleDragImage,
		artworkInfo,
		artworkImages,
		isTextMultipleParagraphs,
	} = getArtworksTextTableContext();

	const sortableList = (element: HTMLUListElement) => {
		Sortable.create(element, {
			animation: 150,
			group: { name: 'group' },
		});
	};

	let inputRef: HTMLInputElement | undefined = $state();
	let artworkImage = $derived($artworkImages[index]);
	let artworkInfoItem = $derived($artworkInfo[index]);

	const accept = ['image/png', 'image/jpeg', 'image/jpg'];
	const handleClickPlus = () => {
		const inputElement = document.querySelector(
			`#${dataCy}-${index} input`
		) as HTMLElement | null;

		if (inputElement) {
			inputElement?.click();
		}
	};

	const handleClickShiftDown = () => {
		handleShiftImagesDown(index);
	};

	const handleClickShiftUp = () => {
		handleShiftImagesUp(index);
	};

	const handleStatusChange = (e: { detail: { value: string } }) => {
		setArtworkStatus(e.detail.value, index);
	};

	const handleInputChange = (e: Event) =>
		setArtworkText(
			(e as Event & { target: { value: string } }).target?.value,
			index
		);

	const handleInput = () => {
		if (inputRef?.parentNode?.parentNode) {
			(inputRef.parentNode.parentNode as any).dataset.replicatedValue =
				artworkInfoItem.text;
		}
	};

	const handleDelete = (image: ImageCarouselImage) => {
		handleDeleteImage(index, image);
		if (onDeleteImage) {
			onDeleteImage(image);
		}
	};

	const handleDropEnd = (
		event: Sortable.SortableEvent,
		images: ImageCarouselImage[]
	) => {
		const fromListIndex = +event.from.id.split('#')[1];
		const toListIndex = +event.to.id.split('#')[1];
		const { oldIndex = 0, newIndex = 0 } = event;
		const movedItem = images[oldIndex];
		handleDragImage(movedItem, newIndex, fromListIndex, toListIndex);
	};

	const handleDropzoneSubmitFiles = async (files: File[]) => {
		const newImages = await handleSubmittedFiles(index, files);
		if (onSubmitFiles) {
			onSubmitFiles(newImages);
		}
	};

	const discardAllImages = (index: number) => {
		if (onDeleteImage) {
			const artworkImagesRow = $artworkImages[index].images;
			for (let i = 0; i < artworkImagesRow.length; i++) {
				onDeleteImage(artworkImagesRow[i]);
			}
		}
	};

	const handleClickDeleteArtwork = (index: number) => {
		discardAllImages(index);
		deleteArtwork(index);
	};

	let showMultipleParagraphError = $derived(
		formatToggleState.method === FormatMethod.LineBreak &&
			isTextMultipleParagraphs(artworkInfoItem?.text)
	);
</script>

<div
	class={classNames('flex gap-6 border-x border-b border-gray-200 px-3 py-2', {
		'bg-gray-50': !(index % 2),
		'pb-7': showMultipleParagraphError,
	})}
>
	{#if pageCellWidth}
		<div style:min-width={pageCellWidth} style:max-width={pageCellWidth}>
			{#if artworkImage?.page}
				<Txt variant="body2">
					{artworkImage.page.join(', ')}
				</Txt>
			{/if}
		</div>
	{/if}

	<div
		class={'relative'}
		style:min-width={nonActionCellWidth}
		style:max-width={nonActionCellWidth}
	>
		<Dropzone
			maxSize={100000000}
			id={`${dataCy}-${index}`}
			{accept}
			{dataCy}
			multiple
			class="h-[8.5rem]"
			showFiles={false}
			classes={{
				container: classNames(
					'!border-solid !py-0 h-full flex flex-col justify-center text-center !px-0',
					{ '!border-red-500': hasSubmitted && !artworkImage?.images?.length }
				),
			}}
			onSubmitFiles={handleDropzoneSubmitFiles}
		>
			{#snippet helper()}
				<span class="hidden"></span>
			{/snippet}

			{#snippet custom()}
				<div
					class={classNames('w-full', {
						'h-full': !artworkImage?.images?.length,
					})}
				>
					{#if artworkImage?.images?.length}
						<ImageCarousel
							{dataCy}
							group={{ name: DEFAULT_GROUP }}
							id={`image-carousel-${+new Date()}-#${index}`}
							images={artworkImage.images}
							onClickDelete={handleDelete}
							class="p-4"
							onEnd={handleDropEnd}
						/>
					{:else}
						<ul
							id={`image-carousel-${+new Date()}-#${index}`}
							class="relative h-full p-4"
							use:sortableList
						>
							<li
								class="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center"
							>
								<DropzoneContent
									supportedFormats={getSupportedFormats(accept)}
									{dataCy}
									{dialogStores}
								/>
							</li>
						</ul>
					{/if}
				</div>
			{/snippet}
		</Dropzone>

		<Button
			size="sm"
			dataCy={`${dataCy}-up`}
			class="absolute right-20 top-[6rem] h-[1.5rem] w-[1.5rem] px-0"
			variant="secondary"
			onclick={handleClickShiftUp}
		>
			<ChevronUpIcon class="h-3 w-3" />
		</Button>

		<Button
			size="sm"
			dataCy={`${dataCy}-down`}
			class="absolute right-12 top-[6rem] h-[1.5rem] w-[1.5rem] px-0"
			variant="secondary"
			onclick={handleClickShiftDown}
		>
			<ChevronDownIcon class="h-3 w-3" />
		</Button>

		<Button
			size="sm"
			dataCy={`${dataCy}-plus`}
			class="absolute right-4 top-[6rem] h-[1.5rem] w-[1.5rem] px-0"
			variant="secondary"
			onclick={handleClickPlus}
		>
			<PlusIcon class="h-3 w-3" />
		</Button>
	</div>

	<div
		style:min-width={nonActionCellWidth}
		style:max-width={nonActionCellWidth}
	>
		<div
			class={classNames(
				`grow-wrap relative grid max-h-[136px] after:invisible after:whitespace-pre-wrap after:border after:px-3 after:py-2 after:font-silka after:text-[0.875rem] after:content-[attr(data-replicated-value)]`,
				{ 'after:hidden': expandAllValue === ExpandValue.ShrinkAll }
			)}
		>
			<Input
				placeholder="Enter a text"
				rows={1}
				name={`${dataCy}-text-${index}`}
				classes={{
					wrapper: 'absolute h-full w-full [&+p]:top-[136px] [&+p]:absolute',
				}}
				{dataCy}
				value={artworkInfoItem?.text}
				oninput={handleInput}
				onkeyup={handleInputChange}
				bind:inputRef
				error={showMultipleParagraphError ? 'Line breaks are not allowed' : ''}
				class={classNames(
					`resize-none scroll-m-[7rem]`,
					{
						'border-red-500':
							(hasSubmitted && !artworkInfoItem?.text) ||
							showMultipleParagraphError,
					},
					expandAllValue === ExpandValue.ExpandAll
						? `max-h-[136px] min-h-[8.5rem]`
						: '!h-[8.5rem]'
				)}
			/>
		</div>
	</div>

	<div style:min-width={actionCellWidth} style:max-width={actionCellWidth}>
		<div class={'relative flex h-full gap-4'}>
			<div
				style:width={`${STATUS_SELECT_WIDTH}rem`}
				class="flex flex-col justify-start"
			>
				<StatusSelect
					{artworkSaleStatuses}
					value={artworkInfoItem?.status}
					{dataCy}
					onchange={handleStatusChange}
				/>
			</div>
			<div class="flex flex-col justify-center">
				<Button
					size="xs"
					dataCy={`${dataCy}-delete`}
					class="h-[1.5rem] w-[1.5rem] px-0"
					variant="secondary"
					onclick={() => {
						handleClickDeleteArtwork(index);
					}}
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>

			<div class="absolute bottom-[-1.25rem] right-0 z-10">
				<Button
					size="sm"
					dataCy={`${dataCy}-add`}
					class="h-[1.5rem] w-[1.5rem] px-0"
					variant="secondary"
					onclick={() => {
						addArtwork(index);
					}}
				>
					<PlusIcon class="h-3 w-3" />
				</Button>
			</div>
		</div>
	</div>
</div>
