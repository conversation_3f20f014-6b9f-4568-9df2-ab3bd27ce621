<script lang="ts" module>
	import { ExpandValue } from './types';

	export const EXPAND_OPTIONS = [
		{ label: 'Expand All', value: ExpandValue.ExpandAll },
		{ label: 'Shrink All', value: ExpandValue.ShrinkAll },
	];
</script>

<script lang="ts">
	import { Txt } from '$global/components/Txt';

	interface Props {
		dataCy: string;
		actionCellWidth: string;
		nonActionCellWidth: string;
		pageCellWidth?: string | undefined;
		value?: string;
	}

	let {
		dataCy,
		actionCellWidth,
		nonActionCellWidth,
		pageCellWidth = undefined,
		value = $bindable(EXPAND_OPTIONS[0].value),
	}: Props = $props();

	// eslint-disable-next-line @typescript-eslint/no-empty-function
	// const handleChangeExpandAll = () => {};
</script>

<div
	class="flex items-center gap-6 border border-gray-200 px-3 py-2"
	data-cy={`${dataCy}-table-header`}
>
	{#if pageCellWidth}
		<div style:min-width={pageCellWidth} style:max-width={pageCellWidth}>
			<Txt variant="label3">Page</Txt>
		</div>
	{/if}
	<div
		style:min-width={nonActionCellWidth}
		style:max-width={nonActionCellWidth}
	>
		<Txt variant="label3">Artwork</Txt>
	</div>
	<div
		style:min-width={nonActionCellWidth}
		style:max-width={nonActionCellWidth}
	>
		<div class="flex items-center gap-2">
			<Txt variant="label3">Text</Txt>
			<!-- <select
				bind:value
				class="border border-gray-200 p-1 text-[12px] font-[500]"
			>
				{#each EXPAND_OPTIONS as expandOption}
					<option value={expandOption.value}>
						{expandOption.label}
					</option>
				{/each}
			</select> -->
		</div>
	</div>
	<div style:min-width={actionCellWidth} style:max-width={actionCellWidth}>
		<Txt variant="label3">Is this For Sale?</Txt>
	</div>
</div>
