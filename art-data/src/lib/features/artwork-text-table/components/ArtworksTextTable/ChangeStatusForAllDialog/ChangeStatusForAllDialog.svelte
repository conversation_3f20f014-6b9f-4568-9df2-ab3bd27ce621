<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';

	interface Props {
		allStatusValue: string;
		dialogStores: ReturnType<typeof createDialog>;
	}

	let { allStatusValue, dialogStores }: Props = $props();

	const { artworkInfo } = getArtworksTextTableContext();

	const {
		states: { open },
	} = dialogStores;

	const handleClickNo = () => {
		open.set(false);
	};

	const handleClickYes = () => {
		artworkInfo.set(
			$artworkInfo.map((artworkInfo) => ({
				...artworkInfo,
				status: allStatusValue,
			}))
		);

		open.set(false);
	};

	const dataCyPrefix = 'change-status-for-all';
</script>

<Dialog
	dataCy={dataCyPrefix}
	{dialogStores}
	title={`Do you really want to set all statuses to "${allStatusValue}"`}
	shouldClose={false}
	showCloseIcon={false}
>
	<div
		class="mt-6 flex flex-col-reverse gap-2 xs:grid xs:grid-cols-2 xs:flex-row"
	>
		<Button
			dataCy={`${dataCyPrefix}-no`}
			size="lg"
			variant="secondary"
			onclick={handleClickNo}>no</Button
		>
		<Button onclick={handleClickYes} dataCy={`${dataCyPrefix}-yes`} size="lg"
			>yes</Button
		>
	</div>
</Dialog>
