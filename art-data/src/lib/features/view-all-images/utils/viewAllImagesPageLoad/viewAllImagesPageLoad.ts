import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { USER_OPTION, Fields } from '$lib/features/view-all-images/types';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import {
	GetVisitImagesDocument,
	type GetVisitImagesQueryVariables,
} from '$lib/queries/__generated__/getVisitImages.generated';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const VISIT_IMAGES_LIMIT = 50;

export const viewAllImagesPageLoad = async ({
	url,
	data,
	visitId,
}: {
	visitId: string;
	url: URL;
	data: { user: { access_token: string } | null };
}) => {
	dayjs.extend(utc);
	const queryParamsVariables = getInitialVariables(url.searchParams);
	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{},
		getAuthorizationHeaders(data)
	);

	const allUsers = usersResponse.users;

	if (!allUsers) {
		error(404, 'Not found');
	}

	const fieldFilter: NonNullable<GetVisitImagesQueryVariables['filter']>[] =
		(() => {
			switch (queryParamsVariables.field) {
				case Fields.Original:
					return [
						{
							original_uncropped_image: {
								filename_disk: { _nnull: true },
							},
						},
						{
							_or: [
								{
									source: {
										_neq: 'additional_crop',
									},
								},
								{
									source: {
										_null: true,
									},
								},
							],
						},
					];
				case Fields.RectangularCroppedImage:
					return [
						{
							rectangular_cropped_image: {
								filename_disk: { _nnull: true },
							},
						},
					];
				case Fields.PerspectiveCroppedWithDimensions:
					return [
						{
							perspective_cropped_image_with_dimensions: {
								filename_disk: { _nnull: true },
							},
						},
					];
				case Fields.PerspectiveCroppedWithoutDimensions:
					return [
						{
							perspective_cropped_image_without_dimensions: {
								filename_disk: { _nnull: true },
							},
						},
					];
				case Fields.Crops:
					return [
						{
							_or: [
								{
									rectangular_cropped_image: {
										filename_disk: { _nnull: true },
									},
								},
								{
									perspective_cropped_image_without_dimensions: {
										filename_disk: { _nnull: true },
									},
								},
								{
									perspective_cropped_image_with_dimensions: {
										filename_disk: { _nnull: true },
									},
								},
							],
						},
					];
				default:
					return [];
			}
		})();

	const imagesResponse = await gqlClient.request(
		GetVisitImagesDocument,
		{
			limit: VISIT_IMAGES_LIMIT,
			...(queryParamsVariables.page && {
				offset: VISIT_IMAGES_LIMIT * (queryParamsVariables.page - 1),
			}),
			sort: [
				'visit_phone_registration.image_date',
				'image_taken_date',
				'original_uncropped_image.id',
				'-source',
			],
			photographerFilter: { visit: { id: { _eq: `${visitId}` } } },
			filter: {
				_and: [
					{ visit: { id: { _eq: `${visitId}` } } },
					...(queryParamsVariables.from
						? [
								{
									image_taken_date: {
										_gte: `${dayjs
											.utc(queryParamsVariables.from)
											.toISOString()}`,
									},
								},
							]
						: []),
					...(queryParamsVariables.to
						? [
								{
									image_taken_date: {
										_lt: `${dayjs
											.utc(queryParamsVariables.to)
											.format('YYYY-MM-DDT23:59:59')}`,
									},
								},
							]
						: []),
					...(queryParamsVariables.user !== USER_OPTION.value
						? [
								{
									photographer: {
										id: { _eq: queryParamsVariables.user },
									},
								},
							]
						: []),
					...fieldFilter,
				],
			},
		},
		getAuthorizationHeaders(data)
	);

	const total = imagesResponse?.Visit_Image_aggregated?.[0]?.countDistinct?.id;
	const images = imagesResponse?.Visit_Image;
	const photographersIds = imagesResponse?.photographers;

	if (!photographersIds) {
		error(404, 'Not found');
	}

	const users = photographersIds?.map((photographer) =>
		allUsers?.find((user) => user?.id === photographer?.group?.photographer)
	);

	const filteredImages = (() => {
		switch (queryParamsVariables.field) {
			case Fields.Original:
				return images?.map((image) => ({
					id: image.id,
					images: [image?.original_uncropped_image],
				}));
			case Fields.Crops:
				return images?.map((image) => ({
					id: image.id,
					images: [
						image?.perspective_cropped_image_without_dimensions,
						image?.perspective_cropped_image_with_dimensions,
						image?.rectangular_cropped_image,
					],
				}));
			case Fields.RectangularCroppedImage:
				return images?.map((image) => ({
					id: image.id,
					images: [image?.rectangular_cropped_image],
				}));
			case Fields.PerspectiveCroppedWithDimensions:
				return images?.map((image) => ({
					id: image.id,
					images: [image?.perspective_cropped_image_with_dimensions],
				}));
			case Fields.PerspectiveCroppedWithoutDimensions:
				return images?.map((image) => ({
					id: image.id,
					images: [image?.perspective_cropped_image_without_dimensions],
				}));
		}
	})();

	const formattedImages = (filteredImages || []).flatMap(({ id, images }) =>
		images?.filter(Boolean)?.map((filteredImage) => ({
			id,
			width: filteredImage?.width || 1,
			height: filteredImage?.height || 1,
			filename_disk: `${filteredImage?.filename_disk}`,
			url: `${getImageUrl(filteredImage?.id, data?.user?.access_token)}`,
			alt: '',
			page: 0,
		}))
	);

	return {
		images: formattedImages,
		users,
		total,
	};
};
