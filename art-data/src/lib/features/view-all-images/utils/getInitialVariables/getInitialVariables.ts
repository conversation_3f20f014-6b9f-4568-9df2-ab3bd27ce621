import { FieldNames, USER_OPTION, Fields } from '../../types';

export const getInitialVariables = (searchParams: URLSearchParams) => {
	const queryParams = Object.fromEntries(searchParams);

	return {
		[FieldNames.User]: queryParams[FieldNames.User] || USER_OPTION.value,
		[FieldNames.From]: queryParams[FieldNames.From],
		[FieldNames.To]: queryParams[FieldNames.To],
		[FieldNames.Page]: +queryParams[FieldNames.Page] || 1,
		[FieldNames.Field]: queryParams[FieldNames.Field] || Fields.Original,
	};
};
