export enum FieldNames {
	From = 'from',
	To = 'to',
	User = 'user',
	Field = 'field',
	Page = 'page',
}

export enum Fields {
	Original = 'original',
	Crops = 'crops',
	PerspectiveCroppedWithDimensions = 'perspective_cropped_with_dimensions',
	PerspectiveCroppedWithoutDimensions = 'perspective_cropped_without_dimensions',
	RectangularCroppedImage = 'rectangular_cropped_image',
}

export const USER_OPTION = { label: 'All users', value: 'all' };
