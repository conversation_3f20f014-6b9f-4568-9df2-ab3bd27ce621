<script lang="ts">
	import { FieldNames, Fields, USER_OPTION } from '../../types';
	import { getInitialVariables } from '../../utils/getInitialVariables/getInitialVariables';
	import { VISIT_IMAGES_LIMIT } from '../../utils/viewAllImagesPageLoad/viewAllImagesPageLoad';
	import { ViewAllImagesImage } from './ViewAllImagesImage';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { UploadIcon } from '$global/assets/icons/UploadIcon';
	import { Container } from '$global/components/Container';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel/Image';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import { Select } from '$global/components/Select';
	import { Tabs } from '$global/components/Tabs';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		title: string;
		dataCy: string;
		images: ImageCarouselImage[];
		backButtonLabel: string;
		backButtonHref: string;
		total: number | null | undefined;
		users: (
			| {
					id?: string | null | undefined;
					first_name?: string | null;
					last_name?: string | null;
			  }
			| undefined
		)[];
	}

	let {
		title,
		dataCy,
		images,
		backButtonLabel,
		backButtonHref,
		total,
		users,
	}: Props = $props();

	let usersOptions = $derived([
		USER_OPTION,
		...users.map((user) => ({
			label: [user?.first_name, user?.last_name].filter(Boolean).join(' '),
			value: user?.id,
		})),
	]);

	let reactiveInitialVariables = $derived(
		getInitialVariables(page.url.searchParams)
	);
	const initialVariables = getInitialVariables(page.url.searchParams);
	let userValue = $state(initialVariables[FieldNames.User]);
	let fromValue = $state(initialVariables[FieldNames.From]);
	let toValue = $state(initialVariables[FieldNames.To]);
	let fieldValue = $derived(reactiveInitialVariables?.[FieldNames.Field]);
	let activeTab = $derived(fieldValue === Fields.Original ? 0 : 1);
	let fieldSelectValue = $derived(
		[
			Fields.RectangularCroppedImage,
			Fields.PerspectiveCroppedWithDimensions,
			Fields.PerspectiveCroppedWithoutDimensions,
		].includes(fieldValue as Fields)
			? fieldValue
			: 'all'
	);

	let pageQueryParam = $derived(page.url.searchParams.get(FieldNames.Page));
	let pageNumber = $derived(pageQueryParam ? +pageQueryParam : 1);

	const getSearchParamsObj = (page?: number) => {
		return {
			...(userValue && {
				[FieldNames.User]: userValue,
			}),
			...(fromValue && {
				[FieldNames.From]: fromValue,
			}),
			...(toValue && {
				[FieldNames.To]: toValue,
			}),
			...(page && {
				page: `${page}`,
			}),
		};
	};

	const handleChangeCropType = (e: { detail: { value: string } }) => {
		const searchParamsObj = {
			...getSearchParamsObj(),
			[FieldNames.Page]: '1',
			[FieldNames.Field]:
				e.detail.value === 'all' ? Fields.Crops : e.detail.value,
		};

		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleChangeTab = (index: number) => {
		const searchParamsObj = {
			...Object.fromEntries(Array.from(page.url.searchParams)),
			[FieldNames.Field]: index ? Fields.Crops : Fields.Original,
			[FieldNames.Page]: '1',
		};

		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleChangePage = (e: Event | undefined, pageNumber: number) => {
		const searchParamsObj = {
			...Object.fromEntries(Array.from(page.url.searchParams)),
			[FieldNames.Page]: `${pageNumber}`,
		};

		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleChangeUser = (e: { detail: { value: string } }) => {
		const searchParamsObj = {
			...getSearchParamsObj(),
			[FieldNames.User]: e.detail.value,
		};

		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const handleSubmit = (e: Event) => {
		e.preventDefault();
		const searchParamsObj = getSearchParamsObj();
		const searchParamsString = new URLSearchParams(searchParamsObj).toString();
		goto(`?${searchParamsString}`, { noScroll: true });
	};

	const tabs = [
		{
			id: '1',
			title: 'Original images',
		},
		{
			id: '2',
			title: 'Crops',
		},
	];
</script>

<Container {dataCy}>
	<div class="mb-2 flex items-start justify-between">
		<div>
			<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
				>{title}</Txt
			>
			<Txt variant="h4">View all images</Txt>
		</div>
		<div>
			<form class="flex gap-6" onsubmit={handleSubmit}>
				<div class="flex items-center gap-2">
					<Txt variant="label3">Date:</Txt>
					<Input
						bind:value={fromValue}
						placeholder="from"
						name={FieldNames.From}
						dataCy={`${dataCy}-from`}
						type="date"
						class="w-[9rem]"
					/>
					<Txt variant="label3">-</Txt>
					<Input
						bind:value={toValue}
						placeholder="to"
						name={FieldNames.To}
						dataCy={`${dataCy}-from`}
						type="date"
						class="w-[9rem]"
					/>
				</div>

				<div class="flex items-center gap-2">
					<Txt variant="label3">Filter by:</Txt>
					<Select
						onchange={handleChangeUser}
						bind:value={userValue}
						class="min-w-[15rem]"
						options={usersOptions}
						ariaLabel="select user to filter by"
						dataCy={`${dataCy}-user`}
						name="user"
					/>
				</div>

				<LinkButton
					size="md"
					buttonProps={{ type: 'button' }}
					dataCy={`${dataCy}-add-more-images`}
					href={`/${page.url.pathname.split('/')[1]}/${page.params.id}${
						Routes.AddMoreImages
					}`}
					icon
					variant="secondary"
				>
					{#snippet trailing()}
						<UploadIcon class="ml-1" />
					{/snippet}
					add more images
				</LinkButton>

				<button type="submit" class="hidden" aria-label="hidden"></button>
			</form>
			<Txt variant="body3" class="ml-[3rem] mt-0.5 max-w-[20rem] text-gray-500">
				Press enter to submit while focusing the fields to submit the date
				filters
			</Txt>
		</div>
	</div>

	<div class="h-[1px] w-full translate-y-[3.375rem] bg-gray-200"></div>

	<Tabs
		disabled
		onClickTabDisabled={handleChangeTab}
		{dataCy}
		{tabs}
		{activeTab}
		class="mb-4"
		classes={{ inactiveTabLabel: 'text-gray-700' }}
	>
		{#if activeTab}
			<div class="mb-4 flex items-center justify-end gap-2">
				<Txt variant="label3">Crop Type:</Txt>
				<Select
					ariaLabel="Select a type of crop"
					{dataCy}
					onchange={handleChangeCropType}
					value={fieldSelectValue}
					name={FieldNames.Field}
					options={Object.values([
						{ value: 'all', label: 'All' },
						{ value: Fields.RectangularCroppedImage, label: 'Rectangular' },
						{
							value: Fields.PerspectiveCroppedWithDimensions,
							label: 'Perspective With Dimensions',
						},
						{
							value: Fields.PerspectiveCroppedWithoutDimensions,
							label: 'Perspective Without Dimensions',
						},
					])}
				/>
			</div>
		{/if}
		<div class="rounded border border-gray-200 bg-gray-50 p-5 pb-1">
			<div class="grid grid-cols-8">
				{#each images as image}
					<ViewAllImagesImage {dataCy} {image} />
				{/each}
				{#if !images.length}
					<div class="col-span-8 flex justify-center">
						<Txt variant="body2" class="mb-4 text-center text-gray-500"
							>No images</Txt
						>
					</div>
				{/if}
			</div>
		</div>
	</Tabs>

	<div class="flex justify-between">
		<LinkButton size="md" dataCy={`${dataCy}-back`} href={backButtonHref}>
			{backButtonLabel}
		</LinkButton>

		{#if total}
			{#key total}
				{#key pageNumber}
					<Pagination
						onClick={handleChangePage}
						{dataCy}
						currentPage={pageNumber}
						limit={VISIT_IMAGES_LIMIT}
						{total}
					/>
				{/key}
			{/key}
		{/if}
	</div>
</Container>
