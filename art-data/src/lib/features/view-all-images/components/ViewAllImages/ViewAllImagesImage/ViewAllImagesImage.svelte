<script lang="ts">
	// import { BinIcon } from '$global/assets/icons/BinIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { EyeIcon } from '$global/assets/icons/EyeIcon';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { type ImageCarouselImage } from '$global/components/ImageCarousel/Image';
	import { Lightbox, GalleryMedia } from '$global/components/Lightbox';
	import { Txt } from '$global/components/Txt';
	import { MenuButton } from '$lib/components/MenuButton';
	import { MenuList } from '$lib/components/MenuList';
	import { MenuListItem } from '$lib/components/MenuListItem';

	interface Props {
		dataCy: string;
		image: ImageCarouselImage;
	}

	let { dataCy, image }: Props = $props();

	let open = $state(false);
	let lightboxOpen = $state(false);

	const handleClickViewImage = () => {
		lightboxOpen = true;
	};

	const handleClickCopyId = () => {
		navigator.clipboard.writeText(`${image.id}`);
		open = false;
	};

	// const handleClickViewCrop = () => {
	// 	open = false;
	// };

	// const handleClickViewImage = () => {
	// 	open = false;
	// };

	// const handleClickDelete = () => {
	// 	open = false;
	// };
</script>

<div class="relative mb-4">
	<Image dataCy={`${dataCy}-grid`} {image}>
		<MenuButton
			dataCy={`${dataCy}-grid-image-menu`}
			class="absolute bottom-2 right-2 z-20"
			onclick={() => {
				open = true;
			}}
		/>
	</Image>

	{#if open}
		<MenuList
			class="top-[6.5rem]"
			onClickOutside={() => {
				open = false;
			}}
		>
			<MenuListItem onclick={handleClickCopyId}>
				<Txt variant="body2">Copy ID</Txt>
				<CopyIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			<MenuListItem onclick={handleClickViewImage}>
				<Txt variant="body2">View Image</Txt>
				<EyeIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			<!-- <MenuListItem onclick={handleClickDelete}>
				<Txt variant="body2">Delete</Txt>
				<BinIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			

			<MenuListItem onclick={handleClickViewCrop}>
				<Txt variant="body2">View Crop</Txt>
				<EyeIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem> -->
		</MenuList>
	{/if}
</div>

{#if lightboxOpen}
	<Lightbox
		dataCy={`${dataCy}-grid-image`}
		onClose={() => {
			lightboxOpen = false;
		}}
		selectedItem={{
			type: GalleryMedia.image,
			title: '',
			media: { url: image.url, width: image.width, height: image.height },
		}}
		selectedItemIndex={0}
		itemsLength={1}
	/>
{/if}
