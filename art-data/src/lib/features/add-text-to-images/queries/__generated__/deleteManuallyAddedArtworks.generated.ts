import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type DeleteManuallyAddedArtworksMutationVariables = Types.Exact<{
	ids:
		| Array<Types.InputMaybe<Types.Scalars['ID']['input']>>
		| Types.InputMaybe<Types.Scalars['ID']['input']>;
}>;

export type DeleteManuallyAddedArtworksMutation = {
	__typename?: 'Mutation';
	delete_Manually_Added_Artwork_items?: {
		__typename?: 'delete_many';
		ids: Array<string | null>;
	} | null;
};

export const DeleteManuallyAddedArtworksDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'deleteManuallyAddedArtworks' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'delete_Manually_Added_Artwork_items',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'ids' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	DeleteManuallyAddedArtworksMutation,
	DeleteManuallyAddedArtworksMutationVariables
>;
