<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableNoResults,
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		getCellWidth,
	} from '$global/components/Table';

	interface Props {
		files: { name: string; url: string }[];
		class?: string;
	}

	let { ...props }: Props = $props();

	const headers = ['File Name', 'File Format', ''];
	const actionCellWidth = '9rem';
	const dataCyPrefix = 'exhibitions-home-table';

	const getFileValues = (file: { name: string; url: string }) => {
		const fileUrlParts = file.name.split('/');
		const lastFileUrlPart = fileUrlParts[fileUrlParts.length - 1];
		const filenameParts = lastFileUrlPart.split('.');

		const extension = filenameParts[filenameParts.length - 1].toUpperCase();

		return [file.name, extension];
	};
</script>

<table class={twMerge('w-full table-fixed bg-white', props.class)}>
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each props.files as file, i}
			{@const fileValues = getFileValues(file)}

			<TableRow index={i} dataCy={dataCyPrefix}>
				{#each fileValues as fileValue}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						content={fileValue}
					>
						{fileValue}
					</TableCell>
				{/each}

				<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
					<LinkButton
						dataCy={`${dataCyPrefix}-cell`}
						variant="secondary"
						icon
						size="sm"
						href={file.url}
						class="min-w-full [&>button]:min-w-full"
						newTab
					>
						download
						{#snippet trailing()}
							<DownloadIcon class="h-3 w-3" />
						{/snippet}
					</LinkButton>
				</TableActionCell>
			</TableRow>
		{/each}
		{#if !props.files.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No reference files to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>
