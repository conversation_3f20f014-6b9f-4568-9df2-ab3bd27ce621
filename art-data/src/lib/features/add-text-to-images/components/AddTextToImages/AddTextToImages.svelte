<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { createMutation } from '@tanstack/svelte-query';
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { ConfirmationDialog } from '../../../artwork-text-table/components/ConfirmationDialog';
	import { CreateManuallyAddedArtworkDocument } from '../../queries/__generated__/createManuallyAddedArtworks.generated';
	import type { CreateManuallyAddedArtworkMutation } from '../../queries/__generated__/createManuallyAddedArtworks.generated';
	import { DeleteManuallyAddedArtworksDocument } from '../../queries/__generated__/deleteManuallyAddedArtworks.generated';
	import type { AddTextToImagesData } from '../../types';
	import { FilesTable } from './FilesTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import type { ErrorSuccessPageProps } from '$global/features/form/pages/ErrorSuccessPage';
	import { PageBody } from '$lib/components/PageBody';
	import type { GetArteyeArtistsQuery } from '$lib/features/artist/arteye-queries/__generated__/getArteyeArtists.generated';
	import { ArtworksTextTable } from '$lib/features/artwork-text-table/components/ArtworksTextTable';
	import { getImageAlt } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ArtworksTextTableRow';
	import { ChangeStatusForAllDialog } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ChangeStatusForAllDialog';
	import { StatusSelect } from '$lib/features/artwork-text-table/components/ArtworksTextTable/StatusSelect';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import { FormatMethod } from '$lib/features/edit-extracted-images/types';
	import { UpdateManuallyAddedArtworkDocument } from '$lib/queries/__generated__/updateManuallyAddedArtwork.generated';
	import { UpdateManualUploadDocument } from '$lib/queries/__generated__/updateManualUpload.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import type { Artwork, ArtworkInfo } from '$lib/types';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

	interface Props {
		// eslint-disable-next-line svelte/valid-compile
		artist: GetArteyeArtistsQuery['artist'][number] | null;
		detailsString: string;
		title: string;
		crumbs: Crumb[];
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props: ErrorSuccessPageProps['button2Props'];
		dataCy: string;
		data: AddTextToImagesData;
		slotLabel: string;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		children?: import('svelte').Snippet;
	}

	let {
		artist,
		detailsString,
		title,
		crumbs,
		buttonProps,
		button2Props,
		dataCy,
		data,
		slotLabel,
		artworkSaleStatuses,
		children,
	}: Props = $props();

	let saveProgressSubmitting = $state(false);
	let submitSubmitting = $state(false);
	let submitting = $derived(saveProgressSubmitting || submitSubmitting);

	const dialogStores = createDialog();
	const changeStatusDialogStores = createDialog();
	const { artworkFiles, artworkInfo, artworkImages, isEveryRowFilledIn } =
		getArtworksTextTableContext();

	const errorButtonProps = {
		label: 'back to Manual Upload',
		onClick: () => {
			dialogStores.states.open.set(false);
		},
	};

	onMount(() => {
		if (!data.artworks.length) {
			artworkImages.set(
				data.files
					.map((file) => {
						return {
							images: [
								{
									url: file.url,
									page: 0,
									alt: getImageAlt(0),
									id: file.id,
									width: file.width || 1,
									height: file.height || 1,
									filename_disk: file.filename_disk,
									filename_download: file.filename_download,
								},
							],
							page: undefined,
						};
					})
					.filter(Boolean)
			);

			artworkInfo.set(
				data.files.map(() => ({
					id: '',
					text: '',
					status: 'FOR_SALE',
				}))
			);

			artworkFiles.set(
				data.files.map((file) => ({
					url: file.url,
					name: file.filename_disk,
					storage: file.storage,
					id: file.id,
					filename_disk: file.filename_disk,
					filename_download: file.filename_download,
				}))
			);

			return;
		}

		artworkInfo.set(
			data.artworks.map((artwork: Artwork) => ({
				id: artwork.id,
				text: artwork.text,
				status: artwork.status,
			}))
		);

		artworkFiles.set(
			data.artworks.flatMap((artwork: Artwork) =>
				artwork.images.map((image: Artwork['images'][number]) => ({
					url: image.url,
					name: image.filename_disk,
					storage: image.storage,
					id: image.id,
					filename_disk: image.filename_disk,
					filename_download: image.filename_download,
				}))
			)
		);

		artworkImages.set(
			data.artworks.map((artwork: Artwork) => ({
				images: artwork.images.map((image: Artwork['images'][number], i) => ({
					...image,
					id: image.filename_disk,
					alt: getImageAlt(i),
					page: 0,
				})),
				page: undefined,
			}))
		);
	});

	let hasSubmitted = $state(false);
	let allStatusValue = $state(artworkSaleStatuses[0].key);

	const deleteManuallyAddedArtworks = createMutation(
		getMutation(
			DeleteManuallyAddedArtworksDocument,
			getAuthorizationHeaders(data)
		)
	);

	const createManuallyAddedArtworkMutation = createMutation(
		getMutation(
			CreateManuallyAddedArtworkDocument,
			getAuthorizationHeaders(data)
		)
	);

	const updateManuallyAddedArtworkMutation = createMutation(
		getMutation(
			UpdateManuallyAddedArtworkDocument,
			getAuthorizationHeaders(data)
		)
	);

	const updateManualUploadMutation = createMutation(
		getMutation(UpdateManualUploadDocument, getAuthorizationHeaders(data))
	);

	const getImagesObj = async (
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number }
	) => {
		const images = $artworkImages[artworkInfo.artworkInfoIndex].images;

		const imagePromises = images.map((image) => {
			if (image.url.startsWith('/api')) {
				const file = $artworkFiles.find(
					(artworkFile) => artworkFile.filename_disk === image.filename_disk
				);

				return Promise.resolve({
					storage: file?.storage,
					id: file?.id,
					filename_download: file?.filename_download,
				});
			}

			if (image.url.startsWith('data') && image.file) {
				return uploadFile(
					image.file as File,
					getAuthorizationHeaders(
						data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				);
			}

			return null;
		});

		const imagesObj = (await Promise.all(imagePromises))
			.filter(Boolean)
			.filter(Boolean) as {
			storage: string;
			id: string;
			filename_download: string;
		}[];

		return imagesObj;
	};

	const createArtwork = async (
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number },
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER'
	) => {
		const imagesObj = await getImagesObj(artworkInfo);

		return $createManuallyAddedArtworkMutation.mutateAsync({
			input: {
				status: {
					key: status,
					name: status === 'DRAFT' ? 'Draft' : 'Awaiting Label Parser',
				},
				sort: artworkInfo.artworkInfoIndex,
				artwork_details: {
					description: artworkInfo?.text,
					sale_status: artworkInfo?.status,
					// ...(artist && {
					// 	artists: [
					// 		{
					// 			name: `${artist.person?.first_name} ${artist.person?.last_name}`,
					// 			...(artist.person?.year_birth && {
					// 				year_birth: artist.person?.year_birth,
					// 			}),
					// 			...(artist.person?.year_death && {
					// 				year_death: artist.person?.year_death,
					// 			}),
					// 			processed_artist_id: artist.id,
					// 			...(artist.person?.nationalities?.[0]?.country && {
					// 				nationality: {
					// 					code: artist.person?.nationalities?.[0].country?.code,
					// 					name: artist.person?.nationalities?.[0]?.country?.name,
					// 				},
					// 			}),
					// 		},
					// 	],
					// }),
				},
				images: imagesObj.map((imageObj) => ({
					directus_files_id: imageObj,
				})),
			},
		});
	};

	const updateArtwork = async (
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number },
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER'
	) => {
		const imagesObj = await getImagesObj(artworkInfo);

		return $updateManuallyAddedArtworkMutation.mutateAsync({
			id: artworkInfo.id,
			input: {
				status: { key: status },
				sort: artworkInfo.artworkInfoIndex,
				artwork_details: {
					id: data.artworks.find(
						(artwork: Artwork) => artwork.id === artworkInfo.id
					)?.artworkDetailsId,
					description: artworkInfo?.text,
					sale_status: artworkInfo?.status,
					// ...(artist && {
					// 	artists: [
					// 		{
					// 			name: `${artist.person?.first_name} ${artist.person?.last_name}`,
					// 			...(artist.person?.year_birth && {
					// 				year_birth: artist.person?.year_birth,
					// 			}),
					// 			...(artist.person?.year_death && {
					// 				year_death: artist.person?.year_death,
					// 			}),
					// 			processed_artist_id: artist.id,
					// 			...(artist.person?.nationalities?.[0]?.country && {
					// 				nationality: {
					// 					code: artist.person?.nationalities?.[0].country?.code,
					// 					name: artist.person?.nationalities?.[0]?.country?.name,
					// 				},
					// 			}),
					// 		},
					// 	],
					// }),
				},
				images: imagesObj.map((imageObj) => ({
					directus_files_id: imageObj,
				})),
			},
		});
	};

	const handleSave = async (
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER',
		errorCallback: () => void
	) => {
		let error = false;

		let artworkInfoCopy = $artworkInfo.map((artworkInf: ArtworkInfo) => ({
			...artworkInf,
			newId: '',
		}));

		const artworksToCreate = artworkInfoCopy?.reduce(
			(
				accumulator: (ArtworkInfo & { artworkInfoIndex: number })[],
				artworkInf: {
					newId: string;
					id: string;
					text: string;
					status: string;
				},
				i: number
			) => {
				// If no id, then it is a brand new artwork
				if (!artworkInf.id) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				return accumulator;
			},
			[]
		);

		const artworksToUpdate = artworkInfoCopy?.reduce(
			(
				accumulator: (ArtworkInfo & { artworkInfoIndex: number })[],
				artworkInf: {
					newId: string;
					id: string;
					text: string;
					status: string;
				},
				i: number
			) => {
				// If no id, then it is a brand new artwork
				if (!artworkInf.id) {
					return accumulator;
				}

				// If submitting, we need to update the artwork status
				if (status === 'AWAITING_LABEL_PARSER') {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				// We look for a corresponding artwork in the page data
				const correspondingDataArtwork = data.artworks.find(
					(artwork: Artwork) => artwork.id === artworkInf.id
				);

				// If the text or the status does not match, we recreate an artwork
				if (
					correspondingDataArtwork?.text !== artworkInf.text ||
					correspondingDataArtwork?.status !== artworkInf.status
				) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				const correspondingDataArtworkImages =
					correspondingDataArtwork?.images?.map(
						(image: { url: string }) => image?.url
					);

				// If images don't match, then we recreate an artwork
				if (
					JSON.stringify(correspondingDataArtworkImages) !==
					JSON.stringify($artworkImages[i].images.map((image) => image.url))
				) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				return accumulator;
			},
			[]
		);

		const artworksToDelete = data?.artworks?.filter((dataArtwork: Artwork) => {
			const artworkIdsToSave = artworkInfoCopy.map(
				(artworkInfo: {
					newId: string;
					id: string;
					text: string;
					status: string;
				}) => artworkInfo.id
			);

			if (!artworkIdsToSave.includes(dataArtwork.id)) {
				return true;
			}

			return false;
		});

		let artworksCreated: CreateManuallyAddedArtworkMutation[] = [];

		try {
			if (artworksToCreate?.length) {
				const artworksPromises = artworksToCreate.map(
					(
						artworkToCreate: ArtworkInfo & {
							artworkInfoIndex: number;
						}
					) => createArtwork(artworkToCreate, status)
				);

				artworksCreated = await Promise.all(artworksPromises);
				artworksToCreate.forEach(
					(
						artworkToCreate: ArtworkInfo & {
							artworkInfoIndex: number;
						},
						i: number
					) => {
						artworkInfoCopy[artworkToCreate.artworkInfoIndex].newId =
							artworksCreated[i].create_Manually_Added_Artwork_item
								?.id as string;
					}
				);
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong with the artworks creation. Please try again later or contact customer support.',
			});

			error = true;
		}

		if (error) {
			errorCallback();
			return;
		}

		try {
			if (artworksToUpdate?.length) {
				const artworksPromises = artworksToUpdate.map(
					(
						artworkToUpdate: ArtworkInfo & {
							artworkInfoIndex: number;
						}
					) => updateArtwork(artworkToUpdate, status)
				);

				await Promise.all(artworksPromises);
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong with the artworks update. Please try again later or contact customer support.',
			});

			error = true;
		}

		if (error) {
			errorCallback();
			return;
		}

		const manuallyAddedArtworks = artworkInfoCopy.map(
			(
				artworkInf: {
					newId: string;
					id: string;
					text: string;
					status: string;
				},
				i: number
			) => {
				const id = artworkInf.newId || artworkInf.id;
				return {
					id,
					sort: i + 1,
					status: {
						key: status,
					},
				};
			}
		);

		try {
			if (
				artworksToCreate.length ||
				artworksToDelete?.length ||
				artworksToUpdate?.length ||
				status !== 'DRAFT'
			) {
				await $updateManualUploadMutation.mutateAsync({
					id: data.manualUploadId,
					data: {
						manually_added_artworks: manuallyAddedArtworks,
						...(status !== 'DRAFT' && {
							submitted_for_review: true,
						}),
					},
				});
			}
		} catch {
			showToast({
				variant: 'error',
				message: `Something went wrong with the manual upload update. Please try again later or contact customer support.`,
			});

			error = true;
		}

		if (error) {
			errorCallback();
			return;
		}

		try {
			if (artworksToDelete?.length) {
				await $deleteManuallyAddedArtworks.mutateAsync({
					ids: artworksToDelete?.map(
						(artworkToDelete: { id: string }) => artworkToDelete?.id as string
					),
				});
			}
		} catch {
			showToast({
				variant: 'error',
				message: `Something went wrong with the old stock entries deletion. Please try again later or contact customer support.`,
			});

			error = true;
		}

		if (error) {
			errorCallback();
			return;
		}
	};

	const handleClickSaveProgress = async () => {
		saveProgressSubmitting = true;

		await handleSave('DRAFT', () => {
			saveProgressSubmitting = false;
		});

		showToast({
			variant: 'success',
			message: `Your progress has successfully been saved.`,
		});

		goto(`/${page.route.id?.split('/')[1]}/${page.params.id}`);
	};

	const handleSubmit = async () => {
		submitSubmitting = true;

		await handleSave('AWAITING_LABEL_PARSER', () => {
			submitSubmitting = false;
		});
	};

	const handleClickSubmit = () => {
		hasSubmitted = true;

		if (isEveryRowFilledIn()) {
			dialogStores.states.open.set(true);
		}
	};
</script>

<PageBody class={classNames({ 'pointer-events-none': submitting })}>
	<Breadcrumbs {dataCy} class="mb-11 mt-0 lg:mb-11 lg:mt-0" {crumbs} />

	<Container {dataCy}>
		<Txt variant="body3" class="col-span-2 uppercase tracking-[1.68px]"
			>{title}</Txt
		>
		<Txt variant="h4" class="mb-6">Add text to images</Txt>

		<div class="mb-6 grid grid-cols-2">
			<div>
				<Txt variant="h6" class="mb-4">{detailsString}</Txt>
				<div class="w-full">
					{@render children?.()}
				</div>
			</div>
		</div>

		<Txt variant="h6" class="mb-4">Reference Files</Txt>

		<FilesTable files={data.files} class="mb-10" />

		<div class="mb-6 flex items-center justify-end gap-1.5">
			<StatusSelect
				dataCy={`${dataCy}-all`}
				{artworkSaleStatuses}
				bind:value={allStatusValue}
			/>

			<ChangeStatusForAllDialog
				dialogStores={changeStatusDialogStores}
				{allStatusValue}
			/>

			<Button
				size="md"
				{dataCy}
				type="button"
				disabled={submitting}
				onclick={(e) => {
					changeStatusDialogStores.states.open.set(true);
				}}>Change Status for all</Button
			>
		</div>

		<ArtworksTextTable
			formatToggleState={{ method: FormatMethod.Page }}
			{artworkSaleStatuses}
			{submitting}
			{hasSubmitted}
			{dataCy}
		/>
	</Container>

	<div
		class="fixed bottom-0 z-20 w-full border-t border-gray-200 bg-gray-0 py-4"
	>
		<Container dataCy={`${dataCy}-save`} class="flex justify-end">
			<div class="flex gap-3">
				<Button
					onclick={handleClickSaveProgress}
					variant="secondary"
					loading={saveProgressSubmitting}
					disabled={submitting}
					dataCy={`${dataCy}-save-progress`}
					size="md">save progress</Button
				>
				<Button
					loading={submitSubmitting}
					disabled={submitting || !$artworkImages.length}
					dataCy={`${dataCy}-save-submit`}
					size="md"
					onclick={handleClickSubmit}>submit</Button
				>
			</div>
		</Container>
	</div>
</PageBody>

<ConfirmationDialog
	bind:submitSubmitting
	onSave={handleSubmit}
	{errorButtonProps}
	{dialogStores}
	{button2Props}
	{buttonProps}
	{dataCy}
	{slotLabel}
	successMessage="The manual upload data has been queued for extraction. Check the status on the exhibition page for updates."
	errorMessage="We weren't able to complete the manual upload, please go back and try again."
>
	{@render children?.()}
</ConfirmationDialog>
