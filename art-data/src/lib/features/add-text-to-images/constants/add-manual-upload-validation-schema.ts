import { z } from 'zod';

export enum AddManualUploadFieldNames {
	InputName = 'input_name',
	ReceivedDate = 'received_date',
	Sender = 'sender',
	Receiver = 'receiver',
}

export enum AddManualUploadErrorMessages {
	MissingInputName = 'Please enter an input name',
	InvalidReceivedDate = 'Please enter a valid received date',
	InvalidSender = 'Please enter a valid sender email address',
	InvalidReceiver = 'Please enter a valid receiver email address',
}

export const AddManualUploadValidationSchema = z.object({
	[AddManualUploadFieldNames.InputName]: z
		.string()
		.nonempty(AddManualUploadErrorMessages.MissingInputName),
	[AddManualUploadFieldNames.ReceivedDate]: z
		.string()
		.nonempty(AddManualUploadErrorMessages.InvalidReceivedDate),
	[AddManualUploadFieldNames.Sender]: z
		.string()
		.email(AddManualUploadErrorMessages.InvalidSender)
		.optional(),
	[AddManualUploadFieldNames.Receiver]: z
		.string()
		.email(AddManualUploadErrorMessages.InvalidReceiver)
		.optional(),
});
