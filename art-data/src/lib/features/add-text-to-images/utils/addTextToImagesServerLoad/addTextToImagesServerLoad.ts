import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetArtworkSaleStatusDocument } from '$lib/arteye-queries/__generated__/getArtworkSaleStatus.generated';
import { Config } from '$lib/constants/config';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetManualUploadDocument } from '$lib/queries/__generated__/getManualUpload.generated';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const addTextToImagesServerLoad = async (
	manualUploadId: string,
	data: {
		user: { access_token: string; arteye_token?: string } | null;
	}
) => {
	const manualUploadResponse = await gqlClient.request(
		GetManualUploadDocument,
		{ filter: { id: { _eq: manualUploadId } } },
		getAuthorizationHeaders(data)
	);

	const manualUpload = manualUploadResponse?.Manual_Upload?.[0];

	if (!manualUpload || manualUpload.submitted_for_review) {
		error(404, 'Not found');
	}

	const files = (manualUpload?.reference_files || [])?.map((referenceFile) => ({
		name: `${referenceFile?.directus_files_id?.title}`,
		url: `${getImageUrl(referenceFile?.directus_files_id?.id)}`,
		width: referenceFile?.directus_files_id?.width || 0,
		height: referenceFile?.directus_files_id?.height || 0,
		filename_disk: `${referenceFile?.directus_files_id?.filename_disk}`,
		filename_download: `${referenceFile?.directus_files_id?.filename_download}`,
		storage: `${referenceFile?.directus_files_id?.storage}`,
		id: `${referenceFile?.directus_files_id?.id}`,
	}));

	const artworkSaleStatusesResponse = await gqlClientArteye.request(
		GetArtworkSaleStatusDocument,
		{},
		getAuthorizationHeaders({
			user: { access_token: `${data.user?.arteye_token}` },
		})
	);

	const artworkSaleStatuses =
		artworkSaleStatusesResponse?.artwork_activity_status_type;

	const artworks = (manualUpload.manually_added_artworks || [])?.map(
		(manuallyAddedArtwork) => ({
			id: `${manuallyAddedArtwork?.id}`,
			images: (manuallyAddedArtwork?.images || [])?.map((image) => ({
				filename_disk: `${image?.directus_files_id?.filename_disk}`,
				width: image?.directus_files_id?.width || 1,
				height: image?.directus_files_id?.height || 1,
				storage: `${image?.directus_files_id?.storage}`,
				id: `${image?.directus_files_id?.id}`,
				filename_download: `${image?.directus_files_id?.filename_download}`,
				url: `${getImageUrl(
					image?.directus_files_id?.id,
					data?.user?.access_token
				)}`,
			})),
			text: manuallyAddedArtwork?.artwork_details?.description || '',
			artworkDetailsId: manuallyAddedArtwork?.artwork_details?.id,
			status:
				manuallyAddedArtwork?.artwork_details?.sale_status ||
				artworkSaleStatuses?.[0]?.key,
		})
	);

	const additionalCrumb = buildUploadCrumb(
		manualUpload?.receipt_info?.receive_date,
		manualUpload?.processed_fair_exhibitor_org?.name
	);

	return {
		additionalCrumb,
		artworkSaleStatuses,
		artworks,
		files,
		manualUploadId: manualUpload.id,
	};
};
