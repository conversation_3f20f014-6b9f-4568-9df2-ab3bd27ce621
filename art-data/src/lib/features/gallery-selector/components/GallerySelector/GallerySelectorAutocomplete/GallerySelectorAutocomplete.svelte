<script lang="ts" module>
	export type Galleries = {
		value: Writable<string>;
		selectedOption: OptionType | null;
	}[];
</script>

<script lang="ts" generics="T, V extends Variables">
	import type { TypedDocumentNode } from '@graphql-typed-document-node/core';
	import { type Variables } from 'graphql-request';
	import type { Component } from 'svelte';
	import { writable, type Writable } from 'svelte/store';
	import { page } from '$app/state';
	import { SearchIcon } from '$global/assets/icons/SearchIcon';
	import { QueryAutocomplete } from '$global/components/QueryAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import type {
		Classes,
		OptionClasses,
	} from '$global/components/QueryAutocomplete/types';
	import { Txt } from '$global/components/Txt';
	import { gqlClientArteye } from '$lib/gqlClientArteye';

	interface Props<T, V> {
		document: TypedDocumentNode<T, V>;
		getVariables: (
			value: string,
			additionalVariables: object
			// eslint-disable-next-line no-undef
		) => V | undefined;
		getOptions: (data: T | undefined) => OptionType[];
		showResultsWhenEmpty?: boolean | undefined;
		placeholder?: string;
		disabled: boolean;
		classes?: Classes | undefined;
		size?: 'sm' | 'md';
		dataCy: string;
		index: number;
		galleries: Galleries;
		noResults?: import('svelte').Snippet;
		list: import('svelte').Snippet;
		onDelete?: undefined | ((index: number, galleries: Galleries) => void);
		OptionComponent: Component<{
			classes?: OptionClasses;
			dataCy: string;
			option: OptionType;
			size?: 'sm' | 'md';
		}>;
		class?: string;
		SelectedOptionComponent?:
			| Component<{
					classes?: OptionClasses;
					dataCy: string;
					option: OptionType;
					size?: 'sm' | 'md';
					handleClick?: undefined | (() => void);
			  }>
			| undefined;
	}

	let {
		showResultsWhenEmpty = false,
		placeholder = 'Search galleries',
		disabled,
		size = 'md',
		dataCy,
		index,
		galleries = $bindable(),
		onDelete = undefined,
		list: listSnippet,
		noResults: noResultsSnippet,
		...rest
		// eslint-disable-next-line no-undef
	}: Props<T, V> = $props();

	const handleClickRemove = () => {
		if (galleries.length === 1) {
			galleries[index].selectedOption = null;
			galleries[index].value.set('');
		} else {
			galleries = galleries.filter((_, i) => i !== index);
		}

		if (onDelete) {
			onDelete(index, galleries);
		}
	};

	const handleClickAddAnotherGallery = () => {
		galleries = [
			...galleries,
			{
				value: writable(''),
				selectedOption: null,
			},
		];
	};

	// eslint-disable-next-line no-undef
	const emptyValueResponse = { gallery: [] } as T;
</script>

<div class="relative">
	<QueryAutocomplete
		{size}
		name="gallery"
		dataCy={`${dataCy}-gallery`}
		{placeholder}
		{emptyValueResponse}
		{showResultsWhenEmpty}
		graphQlClient={gqlClientArteye}
		requestHeaders={{
			authorization: `Bearer ${page.data.user.arteye_token}`,
		}}
		class="mt-4"
		classes={{
			input: size === 'sm' ? 'pl-8' : 'pl-9',
			listWithOptions: 'pb-[3rem]',
			longList: '!max-h-[232px] !min-h-[232px]',
			option: {
				line3: 'font-[500]',
			},
		}}
		value={galleries[index].value}
		bind:selectedOption={galleries[index].selectedOption}
		{...rest}
	>
		{#snippet list()}
			{@render listSnippet?.()}
		{/snippet}

		{#snippet icon()}
			<div
				class={'absolute left-2.5 top-[50%] z-10 -translate-y-1/2 text-gray-900'}
			>
				<SearchIcon
					class={size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'}
					data-cy={dataCy}
				/>
			</div>
		{/snippet}

		{#snippet noResults()}
			{@render noResultsSnippet?.()}
		{/snippet}
	</QueryAutocomplete>
	<!-- {#if galleries[index].selectedOption}
		<a
			class={'absolute right-2.5 top-[50%] z-10 -translate-y-1/2 text-gray-900'}
			href="https://google.com"
			target="_blank"
			rel="noopener noreferrer"
		>
			<ExternalIcon class="h-4 w-4" {dataCy} />
		</a>
	{/if} -->
</div>

{#if galleries[index].selectedOption && !disabled}
	<div class="my-1 flex gap-x-1">
		<button onclick={handleClickRemove}
			><Txt class="text-[0.75rem] text-blue-500 hover:underline">Remove</Txt
			></button
		>
		{#if index === galleries.length - 1}
			<Txt class="text-[0.75rem] text-blue-500">|</Txt>
			<button onclick={handleClickAddAnotherGallery}
				><Txt class="text-[0.75rem] text-blue-500 hover:underline"
					>Add another gallery</Txt
				>
			</button>
		{/if}
	</div>
{/if}
