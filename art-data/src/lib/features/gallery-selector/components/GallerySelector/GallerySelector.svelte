<script lang="ts">
	import classNames from 'classnames';
	import {
		GetFairExhibitorDocument,
		type GetFairExhibitorQuery,
	} from '../../arteye-queries/__generated__/getFairExhibitor.generated';
	import {
		GallerySelectorAutocomplete,
		type Galleries,
	} from './GallerySelectorAutocomplete';

	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Config } from '$lib/constants/config';
	import { CannotFindExhibitor } from '$lib/features/match-artworks-with-labels/components/CannotFindExhibitor';

	interface Props {
		size?: 'sm' | 'md';
		dataCy: string;
		galleries: Galleries;
		onDelete?: undefined | ((index: number, galleries: Galleries) => void);
	}

	let {
		size = 'md',
		dataCy,
		galleries = $bindable(),
		onDelete = undefined,
	}: Props = $props();

	const getVariables = (value: string) => {
		return {
			filter: {
				_and: [
					{
						fair: {
							id: { _eq: page.data?.fair?.processed_fair?.processed_fair_id },
						},
					},
					{
						status: { key: { _neq: 'archived' } },
					},
					...(value ? [{ entity: { name: { _icontains: value } } }] : []),
				],
			},
		};
	};

	const getOptions = (data: GetFairExhibitorQuery | undefined) => {
		return (data?.fair_exhibitor || [])?.map((fairExhibitor) => {
			const url = (() => {
				return `${Config.ArteyeDomain}/${
					fairExhibitor?.entity?.gallery?.id ? 'galleries' : 'organisations'
				}/${fairExhibitor?.entity?.gallery?.id || fairExhibitor?.entity?.organisation?.id}`;
			})();

			return {
				line1: `${fairExhibitor?.entity?.name}`,
				line1Suffix: fairExhibitor?.entity?.organisation?.location?.name
					? `(${fairExhibitor?.entity?.organisation?.location?.name})`
					: '',
				line2: url,
				line3: fairExhibitor?.entity?.id,
				line4:
					fairExhibitor?.entity?.organisation?.type?.[0]?.organisation_type_key
						?.name,
				line5: fairExhibitor?.entity?.organisation?.location?.name,
				line6: fairExhibitor?.entity?.organisation?.id,
			};
		});
	};
</script>

{#if galleries}
	{#each galleries as _, index}
		<div
			class={classNames('relative', {
				'flex items-center justify-between rounded border border-gray-200 bg-white p-0 pl-3':
					!!galleries[0].selectedOption,
			})}
		>
			<GallerySelectorAutocomplete
				{size}
				{getOptions}
				showResultsWhenEmpty
				placeholder="Select exhibitors"
				{getVariables}
				document={GetFairExhibitorDocument}
				class="mt-0"
				disabled={!!galleries[0].selectedOption}
				classes={{
					input: classNames(
						size === 'sm' ? 'pl-8' : 'pl-9',
						'text-[0.75rem] sm:text-[0.75rem]'
					),
					listWithOptions: 'pb-[65px]',
					longList: '!max-h-[232px] !min-h-[232px] z-40',
					selectedOption: {
						line1: 'text-[0.75rem] sm:text-[0.75rem] font-[500]',
						line1Suffix: 'text-[0.75rem] sm:text-[0.75rem]',
						line2: 'hidden',
						line3: 'hidden',
						line4: 'hidden',
						line5: 'hidden',
						line6: 'hidden',
					},
					option: {
						line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
						line1Suffix: 'text-[0.75rem] sm:text-[0.75rem]',
						line2: 'hidden',
						line3: 'hidden',
						line4: 'hidden',
						line5: 'hidden',
						line6: 'hidden',
					},
				}}
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				{dataCy}
				bind:galleries
				{index}
				{onDelete}
			>
				{#snippet list()}
					<div
						class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
					>
						<CannotFindExhibitor
							fairId={`${page.data.fair?.processed_fair?.processed_fair_id}`}
						/>
					</div>
				{/snippet}
				{#snippet noResults()}
					<div class="flex flex-col items-center">
						<NoResults class="mb-2" dataCy={`${dataCy}-exhibitor-autocomplete`}
							>No exhibitor found</NoResults
						>
						<CannotFindExhibitor
							fairId={`${page.data.fair?.processed_fair?.processed_fair_id}`}
						/>
					</div>
				{/snippet}
			</GallerySelectorAutocomplete>

			{#if !!galleries[0].selectedOption}
				<Button
					onclick={() => {
						galleries[0].selectedOption = null;
					}}
					dataCy={`${dataCy}-delete`}
					class="ml-1 h-[2rem] w-[2rem] border-none px-0 [&>span]:translate-x-[-0.25rem] [&>span]:pr-1"
					variant="secondary"
					size="sm"
				>
					<CrossIcon class="h-3 w-3" />
				</Button>
			{/if}
		</div>
	{/each}
{/if}
