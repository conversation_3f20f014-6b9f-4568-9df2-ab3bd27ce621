import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateVisitArtworkMutationVariables = Types.Exact<{
	data: Types.Create_Visit_Artwork_Input;
}>;

export type CreateVisitArtworkMutation = {
	__typename?: 'Mutation';
	create_Visit_Artwork_item?: {
		__typename?: 'Visit_Artwork';
		id: string;
		label_image?: { __typename?: 'Visit_Image'; id: string } | null;
		artwork_image?: { __typename?: 'Visit_Image'; id: string } | null;
	} | null;
};

export const CreateVisitArtworkDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createVisitArtwork' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_Visit_Artwork_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_Visit_Artwork_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'label_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateVisitArtworkMutation,
	CreateVisitArtworkMutationVariables
>;
