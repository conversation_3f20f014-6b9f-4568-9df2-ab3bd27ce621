import { gql } from 'graphql-tag';

export const GET_FAIR_EXHIBITOR = gql`
	query getFairExhibitor($filter: fair_exhibitor_filter) {
		fair_exhibitor(filter: $filter) {
			id
			fair {
				id
				title
			}
			entity {
				id
				reference_id
				type {
					key
				}
				name
				person {
					id
					year_birth
					year_death
					type {
						person_type_key {
							key
							name
						}
					}
					nationalities {
						country {
							country_nationality
						}
					}
				}
				artist {
					id
				}
				organisation {
					id
					name
					type {
						organisation_type_key {
							key
							name
						}
					}
					location {
						code
						name
						short_code
						country {
							name
							code
							short_code
						}
						country_nationality
						type {
							key
						}
					}
				}
				gallery {
					id
				}
				addresses {
					city {
						name
					}
					country {
						name
					}
				}
			}
		}
	}
`;
