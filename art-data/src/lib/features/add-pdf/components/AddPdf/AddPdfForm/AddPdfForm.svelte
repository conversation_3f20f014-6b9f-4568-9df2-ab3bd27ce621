<script lang="ts">
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';
	import { twMerge } from 'tailwind-merge';
	import {
		AddPdfFieldNames,
		AddPdfValidationSchema,
	} from '../../../constants/add-pdf-validation-schema';
	import { page } from '$app/state';
	import { Checkbox } from '$global/components/Checkbox';
	import { Dropzone, type DropzoneFile } from '$global/components/Dropzone';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { getFormBlur } from '$global/utils/getFormBlur';
	import { getFormErrorsFromSchema } from '$global/utils/getFormErrorsFromSchema';
	import { isFormValid } from '$global/utils/isFormValid';
	import { Routes } from '$lib/constants/routes';
	import {
		ArtistsAutocomplete,
		formatExhibitionArtist,
	} from '$lib/features/artist/components/ArtistsAutocomplete';
	import { FairExhibitorAutocomplete } from '$lib/features/fair-exhibitors/components/FairExhibitorAutocomplete';

	interface Props {
		artist: any;
		dataCy: string;
		title: string | null | undefined;
		files?: DropzoneFile[];
		class?: string;
		details:
			| (Record<AddPdfFieldNames, string> & {
					artist?: OptionType;
					fairExhibitor?: OptionType;
			  })
			| null;
	}

	let {
		artist,
		dataCy,
		title,
		files = $bindable([]),
		details = $bindable(),
		...rest
	}: Props = $props();

	const dataCyPrefix = `${dataCy}-add-pdf-form`;

	const value = writable('');

	let isFair = $derived(page.url.pathname.startsWith(Routes.FairsHome));
	let artistSelectedOption: OptionType | null = $state(null);
	let fairExhibitorSelectedOption: OptionType | null = $state(null);

	onMount(() => {
		if (artist) {
			artistSelectedOption = formatExhibitionArtist(artist);
		}
	});

	const handleSubmit = (e: Event) => {
		e.preventDefault();
		details = {
			[AddPdfFieldNames.InputName]: $form[AddPdfFieldNames.InputName],
			[AddPdfFieldNames.ReceivedDate]: $form[AddPdfFieldNames.ReceivedDate],
			// [AddPdfFieldNames.ForFreelancers]: $form[AddPdfFieldNames.ForFreelancers],
			[AddPdfFieldNames.HighPriority]: $form[AddPdfFieldNames.HighPriority],
			[AddPdfFieldNames.IncludesPrices]: $form[AddPdfFieldNames.IncludesPrices],
			...(artistSelectedOption && {
				artist: artistSelectedOption,
			}),
			...(fairExhibitorSelectedOption && {
				fairExhibitor: fairExhibitorSelectedOption,
			}),
			...($form[AddPdfFieldNames.Sender] && {
				[AddPdfFieldNames.Sender]: $form[AddPdfFieldNames.Sender],
			}),
			...($form[AddPdfFieldNames.Receiver] && {
				[AddPdfFieldNames.Receiver]: $form[AddPdfFieldNames.Receiver],
			}),
		} as Record<AddPdfFieldNames, string>;
	};

	const { form, constraints } = superForm(page.data.addPdfForm, {
		validators: zod(AddPdfValidationSchema),
		validationMethod: 'auto',
	});

	const handleSubmitFiles = (files: File[]) => {
		form.set({
			...$form,
			[AddPdfFieldNames.InputName]: files?.[0]?.name,
		});
	};

	const { blur, handleBlur } = getFormBlur(page.data.addPdfForm);
	const errors = getFormErrorsFromSchema(form, AddPdfValidationSchema);
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt
			variant="body3"
			class="col-span-2 mb-[-0.5rem] uppercase tracking-[1.68px]">{title}</Txt
		>
		<Txt variant="h4" class="col-span-2 mb-2">Add PDF</Txt>

		<div class="col-span-2">
			<InputLabel dataCy={`${dataCyPrefix}-pdf-label`} class="mb-2" required
				>PDF file</InputLabel
			>
			<Dropzone
				maxSize={100000000}
				bind:files
				dataCy={`${dataCyPrefix}-pdf`}
				accept={['application/pdf']}
				onSubmitFiles={handleSubmitFiles}
			/>
		</div>

		<StepInput
			dataCy={`${dataCyPrefix}-title`}
			label="Title"
			placeholder="Title"
			id={AddPdfFieldNames.InputName}
			name={AddPdfFieldNames.InputName}
			type="text"
			class="col-span-2"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddPdfFieldNames.InputName] &&
				$errors[AddPdfFieldNames.InputName]}
			bind:value={$form[AddPdfFieldNames.InputName]}
			onblur={handleBlur(AddPdfFieldNames.InputName)}
			{...$constraints[AddPdfFieldNames.InputName]}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-received-date`}
			label="Received Date"
			class="col-span-2"
			placeholder="DD/MM/YYYY"
			id={AddPdfFieldNames.ReceivedDate}
			name={AddPdfFieldNames.ReceivedDate}
			type="date"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddPdfFieldNames.ReceivedDate] &&
				$errors[AddPdfFieldNames.ReceivedDate]}
			bind:value={$form[AddPdfFieldNames.ReceivedDate]}
			onblur={handleBlur(AddPdfFieldNames.ReceivedDate)}
			{...$constraints[AddPdfFieldNames.ReceivedDate]}
		/>

		{#if isFair}
			<div class="col-span-2">
				<InputLabel
					class="mb-2"
					dataCy={`${dataCyPrefix}-fair-exhibitor`}
					required>Fair exhibitor</InputLabel
				>
				<FairExhibitorAutocomplete
					bind:selectedOption={fairExhibitorSelectedOption}
					dataCy={`${dataCyPrefix}`}
				/>
			</div>
		{/if}

		<div class="col-span-2">
			<ArtistsAutocomplete
				{value}
				bind:selectedOption={artistSelectedOption}
				dataCy={dataCyPrefix}
				name={AddPdfFieldNames.ArtistName}
			/>
		</div>

		<StepInput
			dataCy={`${dataCyPrefix}-sender`}
			label="Sender"
			placeholder="<EMAIL>"
			id={AddPdfFieldNames.Sender}
			name={AddPdfFieldNames.Sender}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddPdfFieldNames.Sender] && $errors[AddPdfFieldNames.Sender]}
			bind:value={$form[AddPdfFieldNames.Sender]}
			onblur={handleBlur(AddPdfFieldNames.Sender)}
			{...$constraints[AddPdfFieldNames.Sender]}
			required={false}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-receiver`}
			label="Receiver"
			placeholder="<EMAIL>"
			id={AddPdfFieldNames.Receiver}
			name={AddPdfFieldNames.Receiver}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddPdfFieldNames.Receiver] &&
				$errors[AddPdfFieldNames.Receiver]}
			bind:value={$form[AddPdfFieldNames.Receiver]}
			onblur={handleBlur(AddPdfFieldNames.Receiver)}
			{...$constraints[AddPdfFieldNames.Receiver]}
			required={false}
		/>

		<!-- <div class="col-span-2 mt-[0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-freelancers`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-freelancers-label`}
					name={AddPdfFieldNames.ForFreelancers}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddPdfFieldNames.ForFreelancers]}
				/>

				<div class="pl-1.5">Send this to a freelancer</div>
			</InputLabel>
		</div> -->

		<div class="col-span-2">
			<InputLabel
				dataCy={`${dataCyPrefix}-high-priority`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-high-priority-label`}
					name={AddPdfFieldNames.HighPriority}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddPdfFieldNames.HighPriority]}
				/>

				<div class="pl-1.5">High priority</div>
			</InputLabel>
		</div>

		<div class="scol-span-2 mt-[-0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-includes-prices`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-includes-prices-label`}
					name={AddPdfFieldNames.IncludesPrices}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddPdfFieldNames.IncludesPrices]}
				/>

				<div class="pl-1.5">Includes Prices</div>
			</InputLabel>
		</div>
	</StepContainer>

	<StepButtons
		backButtonProps={{
			href: `/${page.url.pathname.split('/')[1]}/${page.params.id}`,
		}}
		continueButtonProps={{
			disabled:
				!isFormValid($errors) ||
				!files.length ||
				(isFair && !fairExhibitorSelectedOption),
		}}
	>
		Submit
	</StepButtons>
</form>
