<script lang="ts">
	import classNames from 'classnames';
	import { AddPdfDetails } from './AddPdfDetails';
	import { AddPdfForm } from './AddPdfForm';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import {
		ErrorSuccessPage,
		type ErrorSuccessPageProps,
	} from '$global/features/form/pages/ErrorSuccessPage';
	import { getUTCDayDate } from '$global/utils/getUTCDayDate/getUTCDayDate';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import { AddPdfFieldNames } from '$lib/features/add-pdf/constants/add-pdf-validation-schema';
	import { GetProcessedArtistDocument } from '$lib/features/artist/queries/__generated__/getProcessedArtist.generated';
	import { createReceiptInfoMutation } from '$lib/features/receipt-info/utils/createReceiptInfoMutation/createReceiptInfoMutation';
	import { gqlClient } from '$lib/gqlClient';
	import { GetProcessedOrganisationDocument } from '$lib/queries/__generated__/getProcessedOrganisation.generated';
	import { IngestionDataTypenames } from '$lib/types';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

	interface Props {
		artist?: any;
		key: string;
		collection: string;
		crumbs: Crumb[];
		title: string | null | undefined;
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props: ErrorSuccessPageProps['button2Props'];
	}

	let {
		artist = undefined,
		key,
		collection,
		crumbs,
		title,
		buttonProps,
		button2Props,
	}: Props = $props();

	let files: DropzoneFile[] = $state([]);
	let error: string | undefined = $state();
	let success = $state(false);
	let details:
		| (Record<AddPdfFieldNames, string> & {
				artist?: OptionType;
				fairExhibitor?: OptionType;
		  })
		| null = $state(null);

	const dataCyPrefix = `${key}-add-pdf`;
	const createReceiptInfo = createReceiptInfoMutation();

	const handleSubmit = async () => {
		if (!details) {
			return;
		}

		const uplodedPdfFile = await uploadFile(
			files[0] as File,
			getAuthorizationHeaders(
				page.data as Parameters<typeof getAuthorizationHeaders>[0]
			)
		);

		const receiptInfoResponse = await $createReceiptInfo.mutateAsync({
			input: {
				...(details[AddPdfFieldNames.Sender] && {
					sender: details[AddPdfFieldNames.Sender],
				}),
				...(details[AddPdfFieldNames.Receiver] && {
					receiver: details[AddPdfFieldNames.Receiver],
				}),
				receive_date: getUTCDayDate(details[AddPdfFieldNames.ReceivedDate]),
			},
		});

		const processedArtistId = (() => {
			if (!details.artist) {
				return null;
			}

			const artistUrlBits = details.artist?.line2?.split('/');
			return artistUrlBits?.[artistUrlBits.length - 1];
		})();

		let artist;

		if (processedArtistId) {
			const artistResponse = await gqlClient.request(
				GetProcessedArtistDocument,
				{ filter: { processed_artist_id: { _eq: processedArtistId } } },
				getAuthorizationHeaders(page.data as { user: { access_token: string } })
			);

			artist = artistResponse?.Processed_Artist?.[0];
		}

		const fairExhibitorPayload = await (async () => {
			const fairExhibitor = details?.fairExhibitor;

			if (!fairExhibitor) {
				return null;
			}

			const fairExhibitorResponse = await gqlClient.request(
				GetProcessedOrganisationDocument,
				{
					filter: {
						_and: [
							{
								name: {
									_eq: `${fairExhibitor?.line1}`,
								},
								entity: { _eq: `${fairExhibitor?.line3}` },
							},
						],
					},
				},
				getAuthorizationHeaders(page.data as { user: { access_token: string } })
			);

			const processedFairExhibitor =
				fairExhibitorResponse?.Processed_Organisation?.[0];

			if (processedFairExhibitor) {
				return {
					id: processedFairExhibitor.id,
					name: processedFairExhibitor.name,
				};
			}

			return {
				id: `${fairExhibitor?.line6}`,
				entity: `${fairExhibitor?.line3}`,
				name: `${fairExhibitor?.line1}`,
				location: fairExhibitor?.line5,
				type: fairExhibitor?.line4,
			};
		})();

		const createPdfResponse = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/${collection}/${page.params.id}`,
			{
				method: 'PATCH',
				headers: {
					...getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					),
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ingestion_data: {
						create: [
							{
								collection: IngestionDataTypenames.Pdf,
								item: {
									// for_freelancers: details[AddPdfFieldNames.ForFreelancers],
									high_priority: details[AddPdfFieldNames.HighPriority],
									includes_prices: details[AddPdfFieldNames.IncludesPrices],
									status: {
										key: 'AWAITING_IMAGE_EXTRACTION',
										name: 'Awaiting Image Extraction',
									},
									pdf_file: uplodedPdfFile?.id,
									processed_fair_exhibitor_org: fairExhibitorPayload,
									...(details.artist && {
										processed_artist: artist
											? {
													processed_artist_id: artist?.processed_artist_id,
												}
											: {
													processed_artist_id: processedArtistId,
													...(details?.artist?.line1?.split('//')[0] && {
														first_name: details?.artist?.line1?.split('//')[0],
													}),
													...(details?.artist?.line1?.split('//')[1] && {
														last_name: details?.artist?.line1?.split('//')[1],
													}),
													...(details?.artist?.line3?.split(':::')[0] && {
														year_birth: details?.artist?.line3?.split(':::')[0],
													}),
													...(details?.artist?.line3?.split(':::')[1] && {
														year_death: details?.artist?.line3?.split(':::')[1],
													}),
													...(details?.artist?.line4 && {
														nationality: details?.artist?.line4,
													}),
												},
									}),
									receipt_info:
										receiptInfoResponse?.create_Receipt_Information_item?.id,
									title: details[AddPdfFieldNames.InputName],
								},
							},
						],
						delete: [],
						update: [],
					},
				}),
			}
		);

		if (!createPdfResponse.ok) {
			return Promise.reject();
		}

		success = true;
		return Promise.resolve();
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<AddPdfForm
		dataCy={dataCyPrefix}
		{title}
		{artist}
		bind:details
		bind:files
		class={classNames({ hidden: !!details })}
	/>

	<AddPdfDetails
		onSubmit={handleSubmit}
		dataCy={dataCyPrefix}
		bind:details
		bind:error
		class={classNames('mt-10', { hidden: !details || success })}
	/>

	<ErrorSuccessPage
		dataCy={dataCyPrefix}
		variant="success"
		title="PDF Uploaded"
		class={classNames('mt-10', { hidden: !details || !success })}
		{buttonProps}
		{button2Props}
	>
		{#snippet moreContent()}
			<Txt>
				Your pdf will be extracted, please wait until it is ready to review.
			</Txt>
		{/snippet}
	</ErrorSuccessPage>
</PageBody>
