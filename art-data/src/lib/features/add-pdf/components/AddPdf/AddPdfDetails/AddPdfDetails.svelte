<script lang="ts">
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepError } from '$global/features/form/components/StepError';
	import { AddPdfFieldNames } from '$lib/features/add-pdf/constants/add-pdf-validation-schema';

	interface Props {
		error: string | undefined;
		dataCy: string;
		onSubmit: () => Promise<void>;
		class?: string;
		details:
			| (Record<AddPdfFieldNames, string> & {
					artist?: OptionType;
					fairExhibitor?: OptionType;
			  })
			| null;
	}

	let {
		error: errorMessage = $bindable(),
		dataCy,
		details = $bindable(),
		onSubmit,
		...rest
	}: Props = $props();

	let submitting = $state(false);
	let dataCyPrefix = $derived(`${dataCy}-add-pdf-details`);

	const handleClickBack = () => {
		details = null;
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		errorMessage = '';
		submitting = true;
		await onSubmit();
		submitting = false;
	};
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt variant="h4" class="col-span-2 mb-2"
			>Please review before uploading</Txt
		>

		<div class="col-span-2">
			<Txt variant="label3" dataCy={`${dataCyPrefix}-details`} class="mb-2"
				>Details:</Txt
			>

			<div
				data-cy={dataCyPrefix}
				class="flex flex-col gap-1 rounded border border-gray-200 p-3"
			>
				<Txt
					dataCy={`${dataCyPrefix}-input-name`}
					class="text-left"
					variant="label3"
				>
					{details?.[AddPdfFieldNames.InputName]}
				</Txt>
				<Txt
					dataCy={`${dataCyPrefix}-received-date`}
					class={'text-left'}
					variant="body2"
				>
					Received Date: {dayjs(
						details?.[AddPdfFieldNames.ReceivedDate]
					).format('DD/MM/YYYY')}
				</Txt>
				{#if details?.artist}
					<Txt
						dataCy={`${dataCyPrefix}-artist`}
						class={'text-left'}
						variant="body2"
					>
						Artist: {details?.artist?.line1?.replaceAll('//', ' ')}
					</Txt>
				{/if}
				{#if details?.fairExhibitor}
					<Txt
						dataCy={`${dataCyPrefix}-fair-exhibitor`}
						class={'text-left'}
						variant="body2"
					>
						Fair Exhibitor: {details?.fairExhibitor?.line1}
					</Txt>
				{/if}
				<Txt
					dataCy={`${dataCyPrefix}-sender`}
					class={'text-left'}
					variant="body2"
				>
					Sender: {details?.[AddPdfFieldNames.Sender] || ''}
				</Txt>
				<Txt
					dataCy={`${dataCyPrefix}-receiver`}
					class={'text-left'}
					variant="body2"
				>
					Receiver: {details?.[AddPdfFieldNames.Receiver] || ''}
				</Txt>
			</div>
		</div>
	</StepContainer>

	<StepButtons
		onclick={handleClickBack}
		backButtonProps={{}}
		continueButtonProps={{
			loading: submitting,
		}}
	>
		Confirm
		{#snippet error()}
			<span>
				{#if errorMessage}
					<StepError class="normal-case">
						{errorMessage}
					</StepError>
				{/if}
			</span>
		{/snippet}
	</StepButtons>
</form>
