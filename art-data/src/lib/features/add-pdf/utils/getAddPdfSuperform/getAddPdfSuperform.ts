import { zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms/server';
import {
	AddPdfFieldNames,
	AddPdfValidationSchema,
} from '../../constants/add-pdf-validation-schema';

export const getAddPdfSuperform = async () => {
	const addPdfForm = await superValidate(
		zod(AddPdfValidationSchema, {
			defaults: {
				input_name: '',
				received_date: '',
				sender: '',
				receiver: '',
				[AddPdfFieldNames.IncludesPrices]: false,
				[AddPdfFieldNames.HighPriority]: false,
			},
		})
	);

	return { addPdfForm };
};
