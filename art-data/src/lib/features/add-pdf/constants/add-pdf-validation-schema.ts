import { z } from 'zod';

export enum AddPdfFieldNames {
	IncludesPrices = 'includes_prices',
	InputName = 'input_name',
	ReceivedDate = 'received_date',
	Sender = 'sender',
	Receiver = 'receiver',
	ArtistName = 'artist_name',
	ArtistId = 'artist_id',
	ForFreelancers = 'for_freelancers',
	HighPriority = 'high_priority',
}

export enum AddPdfErrorMessages {
	MissingInputName = 'Please enter a title',
	InvalidReceivedDate = 'Please enter a valid received date',
	InvalidSender = 'Please enter a valid sender email address',
	InvalidReceiver = 'Please enter a valid receiver email address',
}

export const AddPdfValidationSchema = z.object({
	[AddPdfFieldNames.InputName]: z
		.string()
		.nonempty(AddPdfErrorMessages.MissingInputName),
	[AddPdfFieldNames.ReceivedDate]: z
		.string()
		.nonempty(AddPdfErrorMessages.InvalidReceivedDate),
	[AddPdfFieldNames.Sender]: z.string().optional(),
	[AddPdfFieldNames.Receiver]: z.string().optional(),
	[AddPdfFieldNames.ForFreelancers]: z.boolean().optional(),
	[AddPdfFieldNames.HighPriority]: z.boolean().optional(),
	[AddPdfFieldNames.IncludesPrices]: z.boolean().optional(),
});
