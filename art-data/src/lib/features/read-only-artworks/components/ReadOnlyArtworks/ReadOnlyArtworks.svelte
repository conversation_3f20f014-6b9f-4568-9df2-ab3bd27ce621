<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { LIMIT } from '../../utils/readOnlyArtworksServerLoad/readOnlyArtworksServerLoad';
	import { page } from '$app/state';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { Dialog } from '$global/components/Dialog';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import ArtworksIds from '$lib/components/ArtworksIds/ArtworksIds.svelte';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import type { GetArtworkDetailsQuery } from '$lib/queries/__generated__/getArtworkDetails.generated';
	import type { getArtworkIds } from '$lib/utils/getArtworksIds/getArtworksIds';

	interface Props {
		crumbs: Crumb[];
		pageNumber: number;
		total: number;
		pdfLink: string | undefined;
		title: string;
		slotLabel: string;
		dataCy: string;
		artworks: (NonNullable<
			GetArtworkDetailsQuery['Artwork_Details'][number]
		> & {
			image: ImageCarouselImage | null;
			labelImage: ImageCarouselImage | null;
		})[];
		children?: import('svelte').Snippet;
		artworksIds: ReturnType<typeof getArtworkIds>;
	}

	let {
		artworksIds,
		crumbs,
		pageNumber,
		total,
		pdfLink,
		title,
		slotLabel,
		dataCy,
		artworks,
		children,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-read-only`);

	const onClickPage = (e: Event | undefined, pageNum: number) => {
		(window as unknown as { location: string }).location =
			`${page.url.pathname}?page=${pageNum}`;
	};

	const copyIdDialogStores = createDialog();

	const headers = [
		'Artwork',
		page.params.pdfId || page.params.manualUploadId
			? 'Artwork Description'
			: 'Label',
		'Artist',
		'Artwork Information',
		'Edition Information',
		'Sale Information',
	];
</script>

<Dialog dialogStores={copyIdDialogStores} dataCy="copy-ids" title="Copy IDs">
	<ArtworksIds {artworksIds} />
</Dialog>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<div class="mb-6 flex items-end justify-between">
			<div>
				<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
					>{title}</Txt
				>
				<Txt variant="h4">Completed Artwork Review</Txt>
			</div>

			<div class="flex gap-4">
				<Button
					size="md"
					dataCy={`${dataCyPrefix}-download-pdf`}
					variant="secondary"
					onclick={() => {
						copyIdDialogStores.states.open.set(true);
					}}
				>
					copy ids
				</Button>
				{#if pdfLink}
					<LinkButton
						size="md"
						dataCy={`${dataCyPrefix}-download-pdf`}
						href={pdfLink}
						newTab
						icon
						variant="secondary"
					>
						{#snippet leading()}
							<DownloadIcon />
						{/snippet}
						download pdf
					</LinkButton>
				{/if}
			</div>
		</div>

		<Txt variant="h6" class="mb-3">
			{slotLabel}
		</Txt>

		<div class="flex items-end justify-between">
			<div>
				{@render children?.()}
			</div>
		</div>

		<table class="mt-6 w-full table-fixed bg-white">
			<TableHeaderRow dataCy={dataCyPrefix}>
				{#each headers as header, i}
					<TableHeader
						dataCy={dataCyPrefix}
						width={`calc(100% / ${headers.length})`}
					>
						{header}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody dataCy={dataCyPrefix}>
				{#each artworks as artwork, j}
					<TableRow index={j} dataCy={dataCyPrefix}>
						{#each headers as _, i}
							{#if i === 0 && artwork.image}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div class="relative h-[100px] border border-gray-200 p-2">
											<Image
												class="max-h-[5.25rem] min-h-[5.25rem] min-w-[5.25rem] max-w-[5.25rem] [&_div]:max-h-[5.25rem] [&_div]:max-w-[5.25rem]"
												dataCy={dataCyPrefix}
												image={artwork.image as ImageCarouselImage}
											/>

											{#if artwork.processed_artwork_id}
												<Button
													size="sm"
													dataCy={`${dataCyPrefix}-copy`}
													class="absolute bottom-1.5 right-1.5 max-h-[1.5rem] max-w-[1.5rem] px-0"
													variant="secondary"
													onclick={() => {
														navigator.clipboard.writeText(
															`${artwork.processed_artwork_id}`
														);
													}}
												>
													<CopyIcon class="h-3 w-3" />
												</Button>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if i === 1}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if artwork.labelImage}
												<div
													class="relative h-[100px] border border-gray-200 p-2"
												>
													<Image
														class="max-h-[5.25rem] min-h-[5.25rem] min-w-[5.25rem] max-w-[5.25rem] [&_div]:max-h-[5.25rem]  [&_div]:max-w-[5.25rem]"
														dataCy={dataCyPrefix}
														image={artwork.labelImage}
													/>

													<Button
														size="sm"
														dataCy={`${dataCyPrefix}-copy`}
														class="absolute bottom-1.5 right-1.5 max-h-[1.5rem] max-w-[1.5rem] px-0"
														variant="secondary"
														onclick={() => {
															navigator.clipboard.writeText(
																`${Config.Domain}${artwork.labelImage?.url}`
															);
														}}
													>
														<CopyIcon class="h-3 w-3" />
													</Button>
												</div>
											{:else}
												<div
													class="relative h-[100px] border border-gray-200 p-2"
												>
													<Txt
														class="max-h-full overflow-scroll"
														variant="body3">{artwork.description}</Txt
													>
												</div>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if i === 2}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if artwork.artists?.[0]?.processed_artist_id}
												<Txt
													variant="body3"
													component="a"
													target="_blank"
													href={`${Config.ArteyeDomain}/artists/${artwork.artists?.[0]?.processed_artist_id}`}
													class="block whitespace-normal break-words text-blue-500 hover:underline"
												>
													{artwork.artists?.[0]?.name}
												</Txt>
											{:else if artwork.artists?.[0]?.name}
												<Txt variant="body3">
													{artwork.artists?.[0]?.name}
												</Txt>
											{/if}
											{#if artwork.artists?.[0]?.nationality?.name}
												<Txt variant="body3">
													{artwork.artists?.[0]?.nationality?.name}
												</Txt>
											{/if}
											{#if artwork.artists?.[0]?.year_birth}
												<Txt variant="body3">
													{artwork.artists?.[0]?.year_birth}{artwork
														.artists?.[0]?.year_death
														? ` - ${artwork.artists?.[0]?.year_death}`
														: ''}
												</Txt>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if i === 3}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											<Txt variant="body3">
												{artwork.title}
											</Txt>
											{#if artwork.crid}
												<Txt variant="body3">
													{artwork.crid}
												</Txt>
											{/if}
											{#if artwork.executed_year_start}
												<Txt variant="body3">
													{artwork.executed_year_start}{artwork.executed_year_end
														? ` - ${artwork.executed_year_end}`
														: ''}
												</Txt>
											{/if}
											{#if artwork.media}
												<Txt variant="body3">
													{artwork.media}
												</Txt>
											{/if}
											{#if artwork.artwork_type?.name}
												<Txt variant="body3">
													{artwork.artwork_type?.name}
												</Txt>
											{/if}
											{#if artwork.number_of_artworks}
												<Txt variant="body3">
													{artwork.number_of_artworks} artwork(s)
												</Txt>
											{/if}
											{#if artwork.dimensions}
												<Txt variant="body3">
													{artwork.dimensions}
												</Txt>
											{/if}
											{#if artwork.dimensions_height_cm && artwork.dimensions_width_cm}
												<Txt variant="body3">
													{artwork.dimensions_height_cm} x {artwork.dimensions_width_cm}{artwork.dimensions_depth_cm
														? ` x ${artwork.dimensions_depth_cm}`
														: ''}
												</Txt>
											{/if}
											{#if artwork.number_of_pieces}
												<Txt variant="body3">
													Number of pieces: {artwork.number_of_pieces}
												</Txt>
												{#if artwork.number_of_pieces > 1}
													<Txt variant="body3">
														{artwork.dimension_type?.name}
													</Txt>
												{/if}
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if i === 4}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if artwork.edition_description}
												<Txt variant="body3">
													{artwork.edition_description}
												</Txt>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if i === 5}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if artwork.currency?.code && artwork.price}
												<Txt variant="body3">
													{artwork.currency?.code}{' '}{artwork.price}
												</Txt>
											{/if}
											{#if artwork.edition_number_type}
												<Txt variant="body3">
													{artwork.edition_number_type.name}
												</Txt>
											{/if}
											{#if artwork.edition_number}
												<Txt variant="body3">
													{artwork.edition_number}
												</Txt>
											{/if}
											{#if artwork.sale_status}
												<Txt variant="body3">
													{artwork.sale_status}
												</Txt>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{/if}
						{/each}
					</TableRow>
				{/each}
			</TableBody>
		</table>

		<div class="mt-2 flex items-center justify-between">
			{#if total}
				<Txt variant="body3">
					Showing {(pageNumber - 1) * LIMIT + 1} - {(pageNumber - 1) * LIMIT +
						artworks.length} of {total}
					results
				</Txt>
			{/if}
			{#key pageNumber}
				{#key artworks.length}
					<Pagination
						onClick={onClickPage}
						dataCy={dataCyPrefix}
						currentPage={pageNumber}
						limit={LIMIT}
						{total}
					/>
				{/key}
			{/key}
		</div>
	</Container>
</PageBody>
