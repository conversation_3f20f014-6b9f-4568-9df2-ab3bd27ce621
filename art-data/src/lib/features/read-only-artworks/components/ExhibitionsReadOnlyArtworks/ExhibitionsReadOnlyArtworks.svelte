<script lang="ts">
	import { ReadOnlyArtworks } from '../ReadOnlyArtworks';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { ExhibitionsDetails } from '$lib/websites/exhibitions/components/ExhibitionsDetails';
	import type { ExhibitionsIdPdfsPdfIdViewCompletedPageData } from '$routes/exhibitions/[id]/pdfs/[pdfId]/view-completed/types';

	let data = $derived(
		getPageData<ExhibitionsIdPdfsPdfIdViewCompletedPageData>(page.data)
	);
	let exhibition = $derived(data.exhibition);
	let artworks = $derived(data.artworks);
	let additionalCrumb = $derived(data.additionalCrumb);
	let artworksIds = $derived(data.artworksIds);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Completed Artwork Review' },
	]);
</script>

<!-- Oddly, this hidden link seems to fix a rehydration issue when navigation from this page to the exhibition one using the breadcrumbs -->
<a
	aria-label="hidden"
	href={`${Routes.ExhibitionsHome}/${page.params.id}`}
	class="hidden"
></a>

<ReadOnlyArtworks
	{artworksIds}
	{artworks}
	{crumbs}
	pdfLink={data.pdfLink}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Exhibition details"
	title={`${exhibition.title}`}
	dataCy="exhibitions"
>
	{#if data.exhibition.processed_exhibition}
		<ExhibitionsDetails
			dataCy="exhibitions-read-only"
			exhibition={{
				title: `${exhibition?.title}`,
				id: `${exhibition?.processed_exhibition?.processed_exhibition_id}`,
				organisers: [
					{
						entity_id: {
							name: `${exhibition?.processed_exhibition?.organisers}`,
						},
					},
				],
				venue_city: {
					name: `${exhibition?.processed_exhibition?.location}`,
					code: `${exhibition?.processed_exhibition?.location}`,
				},
				local_start_date: exhibition?.processed_exhibition?.start_date,
				local_end_date: exhibition?.processed_exhibition?.end_date,
			}}
		/>
	{/if}
</ReadOnlyArtworks>
