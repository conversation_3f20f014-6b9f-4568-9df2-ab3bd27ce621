<script lang="ts">
	import { ReadOnlyArtworks } from '../ReadOnlyArtworks';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { FairsDetails } from '$lib/websites/fairs/components/FairsDetails';
	import type { FairsIdPdfsPdfIdViewCompletedPageData } from '$routes/fairs/[id]/pdfs/[pdfId]/view-completed/types';

	let data = $derived(
		getPageData<FairsIdPdfsPdfIdViewCompletedPageData>(page.data)
	);
	let fair = $derived(data.fair);
	let artworks = $derived(data.artworks);
	let additionalCrumb = $derived(data.additionalCrumb);
	let artworksIds = $derived(data.artworksIds);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Completed Artwork Review' },
	]);
</script>

<!-- Oddly, this hidden link seems to fix a rehydration issue when navigation from this page to the exhibition one using the breadcrumbs -->
<a
	aria-label="hidden"
	href={`${Routes.FairsHome}/${page.params.id}`}
	class="hidden"
></a>

<ReadOnlyArtworks
	{artworksIds}
	{artworks}
	{crumbs}
	pdfLink={data.pdfLink}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Fair details"
	title={`${fair?.processed_fair?.title}`}
	dataCy="fairs"
>
	{#if fair}
		<FairsDetails
			dataCy={`fairs-edit-extracted-images`}
			fair={{
				title: `${fair?.processed_fair?.title}`,
				id: `${data?.fair?.processed_fair?.processed_fair_id}`,
				local_start_date: fair?.processed_fair?.start_date,
				local_end_date: fair?.processed_fair?.end_date,
				venue_city: {
					code: `${fair?.processed_fair?.location}`,
					name: `${fair?.processed_fair?.location}`,
				},
			}}
		/>
	{/if}
</ReadOnlyArtworks>
