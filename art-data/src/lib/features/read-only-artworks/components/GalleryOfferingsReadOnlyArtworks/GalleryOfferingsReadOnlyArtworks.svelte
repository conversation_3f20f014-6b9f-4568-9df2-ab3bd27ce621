<script lang="ts">
	import { ReadOnlyArtworks } from '../ReadOnlyArtworks';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { GalleryOfferingsDetails } from '$lib/websites/gallery-offerings/components/GalleryOfferingsDetails';
	import type { GalleryOfferingsIdPdfsPdfIdViewCompletedPageData } from '$routes/gallery-offerings/[id]/pdfs/[pdfId]/view-completed/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdPdfsPdfIdViewCompletedPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let artworks = $derived(data.artworks);
	let additionalCrumb = $derived(data.additionalCrumb);
	let artworksIds = $derived(data.artworksIds);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'Completed Artwork Review' },
	]);
</script>

<!-- Oddly, this hidden link seems to fix a rehydration issue when navigation from this page to the exhibition one using the breadcrumbs -->
<a
	aria-label="hidden"
	href={`${Routes.GalleryOfferingsHome}/${page.params.id}`}
	class="hidden"
></a>

<ReadOnlyArtworks
	{artworksIds}
	{artworks}
	{crumbs}
	total={data.total || 1}
	pdfLink={data.pdfLink}
	pageNumber={data.page}
	slotLabel="Gallery offering details"
	title={`${galleryOffering.title}`}
	dataCy="gallery-offerings"
>
	{#if galleryOffering}
		<GalleryOfferingsDetails
			dataCy={'gallery-offerings-read-only'}
			galleryOffering={{
				id: `${galleryOffering?.processed_gallery?.id}`,
				organisation: {
					entity: {
						name: `${galleryOffering?.processed_gallery?.name}`,
					},
					location: {
						name: `${galleryOffering?.processed_gallery?.location}`,
						code: `${galleryOffering?.processed_gallery?.location}`,
					},
				},
			}}
		/>
	{/if}
</ReadOnlyArtworks>
