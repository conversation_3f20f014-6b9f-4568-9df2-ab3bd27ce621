import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import type { ImageCarouselImage } from '$global/components/ImageCarousel';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetManuallyAddedArtworksDetailsIdsDocument } from '$lib/features/final-review/queries/__generated__/getManuallyAddedArtworksDetailsIds.generated';
import {
	GetPdfArtworksDetailsIdsDocument,
	type GetPdfArtworksDetailsIdsQuery,
} from '$lib/features/final-review/queries/__generated__/getPdfArtworksDetailsIds.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetArtworkDetailsDocument } from '$lib/queries/__generated__/getArtworkDetails.generated';
import { GetVisitArtworkDocument } from '$lib/queries/__generated__/getVisitArtwork.generated';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getArtworkIds } from '$lib/utils/getArtworksIds/getArtworksIds';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const LIMIT = 20;

export const readOnlyArtworksServerLoad = async ({
	url,
	params,
	parentData,
	visitId,
}: {
	visitId: string | undefined;
	url: URL;
	params: { pdfId?: string; manualUploadId?: string };
	parentData: Parameters<typeof getAuthorizationHeaders>[0];
}) => {
	const pageParam = url.searchParams.get('page');
	const page = pageParam ? +pageParam : 1;

	let additionalCrumb = '';
	let pdfLink: string | undefined;
	let artworksDetailsIds: string[] = [];
	let total: number | undefined | null;
	let artworksIds: null | ReturnType<typeof getArtworkIds> = null;
	let artworkImages: Record<string, ImageCarouselImage | null>;

	if (params.pdfId) {
		const pdfResponse = await gqlClient.request(
			GetPdfArtworksDetailsIdsDocument,
			{
				filter: { id: { _eq: params.pdfId } },
				limit: LIMIT,
				offset: LIMIT * (page - 1),
				artworkFilter: {
					status: {
						key: {
							_in: [
								'REVIEWED_AND_SUBMITTED',
								'COMPLETED',
								'AWAITING_INGESTION',
								'INGESTION_FAILED',
							],
						},
					},
				},
			},
			getAuthorizationHeaders(parentData)
		);

		const pdf = pdfResponse?.PDF?.[0];

		if (!pdf) {
			error(404, 'This manual upload does not exist');
		}

		artworksIds = getArtworkIds(
			pdf?.artworks as Parameters<typeof getArtworkIds>[0]
		);

		artworksDetailsIds = pdf?.artworks?.map(
			(artwork) => artwork?.artwork_details?.id
		) as string[];

		artworkImages = (pdf?.artworks || [])?.reduce(
			(
				accumulator: Record<string, ImageCarouselImage | null>,
				artwork: NonNullable<
					GetPdfArtworksDetailsIdsQuery['PDF'][number]['artworks']
				>[number]
			) => {
				return {
					...accumulator,
					[`${artwork?.artwork_details?.id}`]: {
						alt: '',
						width: artwork?.images?.[0]?.directus_files_id?.width || 1,
						height: artwork?.images?.[0]?.directus_files_id?.height || 1,
						filename_disk: `${artwork?.images?.[0]?.directus_files_id?.filename_disk}`,
						url: `${getImageUrl(artwork?.images?.[0]?.directus_files_id?.id)}`,
					},
				};
			},
			{} as Record<string, ImageCarouselImage | null>
		);

		total = pdf?.artworks_total?.length;
		pdfLink = `${getImageUrl(pdf?.pdf_file?.id)}`;
		additionalCrumb = buildUploadCrumb(
			pdf?.receipt_info?.receive_date,
			pdf?.processed_fair_exhibitor_org?.name
		);

		if (!total) {
			error(404, 'There is no completed artworks for this PDF');
		}
	} else if (params.manualUploadId) {
		const manualUploadResponse = await gqlClient.request(
			GetManuallyAddedArtworksDetailsIdsDocument,
			{
				filter: { id: { _eq: params.manualUploadId } },
				limit: LIMIT,
				offset: LIMIT * (page - 1),
				artworkFilter: {
					status: {
						key: {
							_in: [
								'REVIEWED_AND_SUBMITTED',
								'COMPLETED',
								'AWAITING_INGESTION',
								'INGESTION_FAILED',
							],
						},
					},
				},
			},
			getAuthorizationHeaders(parentData)
		);

		const manualUpload = manualUploadResponse?.Manual_Upload?.[0];

		if (!manualUpload) {
			error(404, 'This manual upload does not exist');
		}

		artworksIds = getArtworkIds(
			manualUpload?.manually_added_artworks as Parameters<
				typeof getArtworkIds
			>[0]
		);

		additionalCrumb = buildUploadCrumb(
			manualUpload?.receipt_info?.receive_date,
			manualUpload?.processed_fair_exhibitor_org?.name
		);

		artworksDetailsIds = manualUpload?.manually_added_artworks?.map(
			(manuallyAddedArtwork) => manuallyAddedArtwork?.artwork_details?.id
		) as string[];

		artworkImages = (manualUpload?.manually_added_artworks || [])?.reduce(
			(accumulator: Record<string, ImageCarouselImage | null>, artwork) => {
				return {
					...accumulator,
					[`${artwork?.artwork_details?.id}`]: {
						alt: '',
						width: artwork?.images?.[0]?.directus_files_id?.width || 1,
						height: artwork?.images?.[0]?.directus_files_id?.height || 1,
						filename_disk: `${artwork?.images?.[0]?.directus_files_id?.filename_disk}`,
						url: `${getImageUrl(artwork?.images?.[0]?.directus_files_id?.id)}`,
					},
				};
			},
			{} as Record<string, ImageCarouselImage | null>
		);

		total = manualUpload?.manually_added_artworks_total?.length;
	} else if (visitId) {
		const dateParam = url.searchParams.get('date');
		const photographerParam = url.searchParams.get('photographer');

		if (!dateParam) {
			error(404, 'The date query parameter is missing');
		}

		if (!photographerParam) {
			error(404, 'The photographer query parameter is missing');
		}

		const visitArtworksResponse = await gqlClient.request(
			GetVisitArtworkDocument,
			{
				limit: LIMIT,
				offset: LIMIT * (page - 1),
				filter: {
					_and: [
						{ visit: { id: { _eq: visitId } } },
						{
							artwork_image: {
								image_taken_date: { _gte: dayjs.utc(dateParam).toISOString() },
							},
						},
						{
							artwork_image: {
								image_taken_date: {
									_lt: dayjs.utc(dateParam).add(1, 'day').toISOString(),
								},
							},
						},
						{
							artwork_image: {
								photographer: { id: { _eq: photographerParam } },
							},
						},
						{
							status: {
								key: {
									_in: [
										'REVIEWED_AND_SUBMITTED',
										'COMPLETED',
										'AWAITING_INGESTION',
										'INGESTION_FAILED',
									],
								},
							},
						},
					],
				},
			},
			getAuthorizationHeaders(parentData)
		);

		const visitArtworks = visitArtworksResponse?.Visit_Artwork;

		artworksIds = getArtworkIds(
			visitArtworks as Parameters<typeof getArtworkIds>[0]
		);

		artworksDetailsIds = visitArtworks?.map(
			(visitArtwork) => visitArtwork?.artwork_details?.id
		) as string[];

		artworkImages = (visitArtworks || [])?.reduce(
			(
				accumulator: Record<string, ImageCarouselImage | null>,
				visitArtwork
			) => {
				return {
					...accumulator,
					[`${visitArtwork?.artwork_details?.id}`]: {
						alt: '',
						width: visitArtwork?.images?.[0]?.directus_files_id?.width || 1,
						height: visitArtwork?.images?.[0]?.directus_files_id?.height || 1,
						filename_disk: `${visitArtwork?.images?.[0]?.directus_files_id?.filename_disk}`,
						url: `${getImageUrl(
							visitArtwork?.images?.[0]?.directus_files_id?.id
						)}`,
					},
					[`${visitArtwork?.artwork_details?.id}-label`]:
						visitArtwork?.label_image
							? (() => {
									const image =
										visitArtwork?.label_image?.crop_type === 'perspective_crop'
											? visitArtwork?.label_image
													?.perspective_cropped_image_without_dimensions
											: visitArtwork?.label_image?.rectangular_cropped_image;

									return {
										alt: '',
										width: image?.width || 1,
										height: image?.height || 1,
										filename_disk: `${image?.filename_disk}`,
										url: `${getImageUrl(image?.id)}`,
									};
								})()
							: null,
				};
			},
			{} as Record<string, ImageCarouselImage | null>
		);

		const usersResponse = await gqlClientSystem.request(
			GetUsersDocument,
			{ filter: { id: { _eq: photographerParam } } },
			getAuthorizationHeaders(parentData)
		);

		const photographer = usersResponse.users?.[0];
		additionalCrumb = buildUploadCrumb(
			dateParam,
			[photographer.first_name, photographer.last_name]
				.filter(Boolean)
				.join(' ')
		);

		total =
			visitArtworksResponse?.Visit_Artwork_aggregated?.[0]?.countDistinct?.id;
	}

	const artworksResponse = await gqlClient.request(
		GetArtworkDetailsDocument,
		{
			filter: { id: { _in: artworksDetailsIds } },
		},
		getAuthorizationHeaders(parentData)
	);

	const artworks = artworksResponse?.Artwork_Details?.map((artworkDetails) => {
		return {
			...artworkDetails,
			image: artworkImages[artworkDetails.id],
			labelImage: artworkImages[`${artworkDetails.id}-label`],
		};
	});

	return {
		page,
		artworks,
		total,
		pdfLink,
		additionalCrumb,
		artworksIds: artworksIds as NonNullable<typeof artworksIds>,
	};
};
