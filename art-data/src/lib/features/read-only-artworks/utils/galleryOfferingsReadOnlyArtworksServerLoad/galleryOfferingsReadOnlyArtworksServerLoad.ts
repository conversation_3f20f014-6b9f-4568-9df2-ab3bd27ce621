import { readOnlyArtworksServerLoad } from '../readOnlyArtworksServerLoad/readOnlyArtworksServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';

export const galleryOfferingsReadOnlyArtworksServerLoad = async ({
	url,
	params,
	parent,
}: {
	parent: () => Promise<{ user: { access_token: string } }>;
	url: URL;
	params: { pdfId?: string; manualUploadId?: string; id: string };
}) => {
	const parentData = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const readOnlyResponse = await readOnlyArtworksServerLoad({
		url,
		params,
		parentData,
		visitId: galleryOffering?.visit?.id,
	});

	return {
		galleryOffering,
		...readOnlyResponse,
	};
};
