import { gql } from 'graphql-tag';

// TODO art-data: adjust query variables
export const GET_TO_MATCH_IMAGES_VISITS = gql`
	query getToMatchImagesVisits(
		$filter: Visit_filter
		$sort: [String]
		$limit: Int
	) {
		Visit(filter: $filter, sort: $sort, limit: $limit) {
			id
			total_cropped: visit_images(
				filter: { status: { key: { _eq: "CROPPED" } } }
				limit: 10000
			) {
				id
			}
			images_remaining: visit_images(
				filter: {
					_and: [
						{ status: { key: { _eq: "CROPPED" } } }
						{ image_content_type: { _null: true } }
					]
				}
				limit: 10000
			) {
				id
			}
			artworks_created: visit_images(
				# filter: {
				# 	_and: [
				# 		{ status: { key: { _eq: "CROPPED" } } }
				# 		{
				# 			_or: [
				# 				{
				# 					_and: [
				# 						{ fair_artwork_image_artwork: { id: { _nnull: true } } }
				# 						{ label_image_artwork: { id: { _nnull: true } } }
				# 					]
				# 				}
				# 				{
				# 					_and: [
				# 						{ fair_artwork_image_artwork: { id: { _nnull: true } } }
				# 						{ image_content_type: { _eq: "installation_shot" } }
				# 					]
				# 				}
				# 			]
				# 		}
				# 	]
				# }
				limit: 1000
			) {
				id
			}
			visit_images {
				image_taken_date
				photographer {
					first_name
					last_name
				}
			}
		}
		Visit_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
