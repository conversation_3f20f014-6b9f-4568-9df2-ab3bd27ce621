import { gql } from 'graphql-tag';

export const GET_CROP_IMAGES_VISITS = gql`
	query getCropImagesVisits(
		$filter: Visit_filter
		$sort: [String]
		$limit: Int
	) {
		Visit(filter: $filter, sort: $sort, limit: $limit) {
			id
			images_remaining: visit_images(
				filter: { _or: [{ status: { key: { _eq: "AWAITING_REVIEW" } } }] }
				limit: 1000
			) {
				id
			}
			total_images: visit_images_func {
				count
			}
			visit_images {
				image_taken_date
				photographer {
					first_name
					last_name
				}
			}
		}
		Visit_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
