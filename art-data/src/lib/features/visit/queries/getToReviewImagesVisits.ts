import { gql } from 'graphql-tag';

// TODO art-data: adjust query variables
export const GET_TO_REVIEW_IMAGES_VISITS = gql`
	query getToReviewImagesVisits(
		$filter: Visit_filter
		$sort: [String]
		$limit: Int
	) {
		Visit(filter: $filter, sort: $sort, limit: $limit) {
			id
			artworks_created: visit_images(
				# filter: {
				# 	_and: [
				# 		{ status: { _eq: "includes" } }
				# 		{ fair_artwork_image_artwork: { id: { _nnull: true } } }
				# 		{ label_image_artwork: { id: { _nnull: true } } }
				# 	]
				# }
				limit: 1000
			) {
				id
			}
			artworks_remaining: visit_images(
				# filter: {
				# 	_and: [
				# 		{ status: { _eq: "includes" } }
				# 		{ fair_artwork_image_artwork: { id: { _nnull: true } } }
				# 		{ label_image_artwork: { id: { _nnull: true } } }
				# 	]
				# }
				limit: 1000
			) {
				id
			}
			visit_images {
				image_taken_date
				photographer {
					first_name
					last_name
				}
			}
		}
		Visit_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
