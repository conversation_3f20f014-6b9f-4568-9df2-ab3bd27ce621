import { gql } from 'graphql-tag';

export const GET_UPLOADED_IMAGES_VISITS = gql`
	query getUploadedImagesVisits(
		$filter: Visit_filter
		$sort: [String]
		$limit: Int
	) {
		Visit(filter: $filter, sort: $sort, limit: $limit) {
			id
			visit_images_func {
				count
			}
			visit_images {
				image_taken_date
				source
				photographer {
					first_name
					last_name
				}
			}
		}
		Visit_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
