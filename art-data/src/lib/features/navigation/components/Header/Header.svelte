<script lang="ts">
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import { MenuItem } from './MenuItem';
	import { navigating, page } from '$app/state';
	import { LogOutIcon } from '$global/assets/icons/LogOutIcon';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { IndeterminateProgressBar } from '$global/components/IndeterminateProgressBar';
	import { Txt } from '$global/components/Txt';
	import { logout } from '$global/features/auth/utils/logout/logout';
	import { Routes } from '$lib/constants/routes';

	const dataCyPrefix = 'header';

	dayjs.extend(utc);
</script>

<header class="relative flex h-[4rem] items-center bg-gray-900 lg:h-[5rem]">
	<Container
		dataCy={dataCyPrefix}
		class="flex h-full items-center justify-between"
	>
		<span class="flex">
			<Txt
				variant="h5"
				class="font-[400] uppercase tracking-[2.52px] text-gray-0 sm:text-[1.125rem]"
				>art market data</Txt
			>

			<Txt
				variant="h5"
				class="ml-2 font-[400] uppercase tracking-[2.52px] text-gray-600 sm:text-[1.125rem]"
				>ingestion app</Txt
			>
		</span>
		<div class="flex h-full items-center gap-8">
			<!-- {#if data.user?.role === Roles.Admin} -->
			<MenuItem href={Routes.Home} checked={page.url.pathname === Routes.Home}
				>Home</MenuItem
			>
			<MenuItem
				href={Routes.Phones}
				checked={page.url.pathname.startsWith(Routes.Phones)}>Phones</MenuItem
			>
			<MenuItem
				href={Routes.ScrapedData}
				checked={page.url.pathname.startsWith(Routes.ScrapedData)}
				>Scraped Data</MenuItem
			>
			<MenuItem
				href={Routes.ExhibitionsHome}
				checked={page.url.pathname.startsWith(Routes.ExhibitionsHome)}
				>Exhibitions</MenuItem
			>
			<MenuItem
				href={Routes.GalleryOfferingsHome}
				checked={page.url.pathname.startsWith(Routes.GalleryOfferingsHome)}
				>Gallery Offerings</MenuItem
			>
			<MenuItem
				href={Routes.FairsHome}
				checked={page.url.pathname.startsWith(Routes.FairsHome)}>Fairs</MenuItem
			>
			<!-- {/if} -->
			<div class="flex items-center gap-4">
				<!-- <div
					class="flex h-[1.75rem] items-center gap-1 rounded-full bg-gray-700 pl-3 pr-4"
				>
					<AccountIcon color="gray-0" class="h-4 w-4" />
					<Txt variant="body2" class="font-[500] text-gray-0">
						{data.user?.firstName} - {data.user?.role}
					</Txt>
				</div> -->

				<Button
					onclick={logout}
					size="sm"
					dataCy="header-log-out"
					class="border-gray-0 hover:border-gray-0"
				>
					log out
					{#snippet trailing()}
						<LogOutIcon color="gray-0" />
					{/snippet}
				</Button>
			</div>
		</div>
	</Container>

	{#if !!navigating.complete}
		<IndeterminateProgressBar
			dataCy="header"
			class="absolute bottom-[-1px] left-[-1rem] h-[3px] w-[calc(100vw+1rem)] bg-gray-500"
		/>
	{/if}
</header>
