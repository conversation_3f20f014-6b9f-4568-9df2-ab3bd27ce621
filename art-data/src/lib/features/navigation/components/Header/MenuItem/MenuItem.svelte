<script lang="ts">
	import classNames from 'classnames';
	import { Txt } from '$global/components/Txt';

	interface Props {
		checked: boolean;
		href: string;
		children?: import('svelte').Snippet;
	}

	let { checked, href, children }: Props = $props();
</script>

<a
	{href}
	data-sveltekit-reload
	class={classNames('flex h-full items-center', {
		'border-b-2 border-gray-0': checked,
	})}
>
	<Txt class="text-gray-0" variant="label3">
		{@render children?.()}
	</Txt>
</a>
