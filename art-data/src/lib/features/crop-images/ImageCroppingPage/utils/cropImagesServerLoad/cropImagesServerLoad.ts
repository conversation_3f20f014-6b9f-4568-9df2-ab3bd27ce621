import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetVisitImageDocument } from '$lib/queries/__generated__/getVisitImage.generated';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';

export const cropImagesServerLoad = async (
	{
		visitId,
		date,
		photographer,
	}: {
		visitId: string;
		date: string;
		photographer: string;
	},
	data: { user: { access_token: string } | null }
) => {
	dayjs.extend(utc);

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographer } } },
		getAuthorizationHeaders(data)
	);

	const photographerUser = usersResponse.users?.[0];

	if (!photographerUser) {
		error(404, 'Not found');
	}

	const visitImageResponse = await gqlClient.request(
		GetVisitImageDocument,
		{
			sort: [
				'visit_phone_registration.image_date',
				'image_taken_date',
				'original_uncropped_image.id',
				'-source',
			],
			limit: -1,
			filter: {
				_and: [
					{
						status: {
							key: {
								_in: ['AWAITING_REVIEW', 'COORDINATE_EXTRACTION_FAILED'],
							},
						},
					},
					{ visit: { id: { _eq: visitId } } },
					{ image_taken_date: { _gte: dayjs.utc(date).toISOString() } },
					{
						image_taken_date: {
							_lt: dayjs.utc(date).add(1, 'day').toISOString(),
						},
					},
					{ photographer: { id: { _eq: photographer } } },
				],
			},
		},
		getAuthorizationHeaders(data)
	);

	const visitImages = visitImageResponse?.Visit_Image;

	return {
		visitImages,
		photographer: photographerUser,
	};
};
