<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import classNames from 'classnames';
	import { type Point } from '../Cropper';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import type {
		Update_Directus_Users_Input,
		InputMaybe,
	} from '$gql/types-system';
	import { RefreshIcon } from '$global/assets/icons/RefreshIcon';
	import { Button } from '$global/components/Button';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { computeBoundingBox } from '$global/utils/computeBoundingBox/computeBoundingBox';
	import { CreateVisitImagesDocument } from '$lib/features/visit/queries/__generated__/createVisitImages.generated';
	import { gqlClient } from '$lib/gqlClient';
	import type { GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';
	import {
		UpdateVisitImageDocument,
		type UpdateVisitImageMutationVariables,
	} from '$lib/queries/__generated__/updateVisitImage.generated';
	import {
		UpdateVisitImageBatchDocument,
		type UpdateVisitImageBatchMutation,
		type UpdateVisitImageBatchMutationVariables,
	} from '$lib/queries/__generated__/updateVisitImageBatch.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { FairsIdImagesCropPageData } from '$routes/fairs/[id]/images/crop/types';
	import { GetPhonesDocument } from '$lib/queries/__generated__/getPhones.generated';

	interface Props {
		loadImage: undefined | (() => void);
		onRecrop: (() => void) | undefined;
		recrop: boolean | undefined;
		submitting: boolean;
		disabled: boolean;
		dataCy: string;
		getOriginalCrops: () => {
			perspectiveCrop: boolean;
			visitImage: GetVisitImageQuery['Visit_Image'][number];
			coords: Point[];
		}[];
		crops: {
			perspectiveCrop: boolean;
			visitImage: GetVisitImageQuery['Visit_Image'][number];
			coords: Point[];
		}[];
		cropIndex: number;
		photographer: {
			id?: string | null | undefined;
			first_name?: string | null;
			last_name?: string | null;
		};
	}

	let {
		loadImage,
		onRecrop,
		recrop,
		submitting = $bindable(),
		disabled,
		dataCy,
		getOriginalCrops,
		crops = $bindable(),
		cropIndex = $bindable(),
		photographer,
	}: Props = $props();

	let error = $state('');
	let data = $derived(getPageData<FairsIdImagesCropPageData>(page.data));

	let notLastCropOfImage = $derived(
		crops.findLastIndex(
			(crop) => crop.visitImage.id === crops[cropIndex].visitImage.id
		) !== cropIndex
	);

	let missingCoordsInPreviousOrCurrentCoords = $derived(
		!!crops
			.slice(0, cropIndex + 1)
			.find((crop) => (crop.coords.length || 0) < 4)
	);

	const updateVisitImageMutation = createMutation(
		getMutation(
			UpdateVisitImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const handleAddNewCropClick = () => {
		crops = [
			...crops.slice(0, cropIndex + 1),
			{
				visitImage: crops[cropIndex].visitImage,
				coords: [],
				perspectiveCrop: crops[cropIndex].perspectiveCrop,
			},
			...crops.slice(cropIndex + 1),
		];

		cropIndex = cropIndex + 1;
		loadImage?.();
	};

	const handleResetCropsClick = () => {
		const currentVisitImageId = crops?.[cropIndex]?.visitImage?.id;

		crops = crops.reduce(
			(accumulator, crop, i) => {
				if (crop?.visitImage?.id !== currentVisitImageId) {
					return [...accumulator, crop];
				}

				if (
					!crops
						?.slice(i + 1)
						?.filter((crop) => crop?.visitImage?.id === currentVisitImageId)
						?.length
				) {
					return [
						...accumulator,
						...getOriginalCrops().filter(
							(crop) => crop?.visitImage?.id === currentVisitImageId
						),
					];
				}

				return accumulator;
			},
			[] as typeof crops
		);

		cropIndex = crops.findIndex(
			(crop) => crop?.visitImage?.id === currentVisitImageId
		);

		setTimeout(() => {
			loadImage?.();
		}, 0);

		// crops = [...originalCrops];
		// if (!crops[cropIndex]) {
		// 	cropIndex = 0;
		// }
	};

	const handleClickSubmit = async () => {
		error = '';

		const cropsToSubmit = crops.slice(0, cropIndex + 1);
		const remainingCrops = crops.slice(cropIndex + 1);

		let invalidCropIndexes = cropsToSubmit?.reduce(
			(accumulator: number[], crop, currentIndex) => {
				if (!crop?.coords?.length || crop.coords?.length < 4) {
					return [...accumulator, currentIndex + 1];
				}

				return accumulator;
			},
			[]
		);

		if (invalidCropIndexes.length) {
			const invalidCropIndexesString = '#' + invalidCropIndexes.join(', #');
			error = `The crops ${invalidCropIndexesString} are incomplete. Please make sure they all have 4 points or remove them if they are unwanted.`;
			return;
		}

		submitting = true;

		const getCropType = (perspectiveCrop: boolean) =>
			perspectiveCrop ? 'perspective_crop' : 'rectangular_crop';

		const getSubmittedCoordinates = (
			perspectiveCrop: boolean,
			cropCoords: Point[]
		) =>
			perspectiveCrop
				? JSON.stringify({
						topLeft: cropCoords[0],
						bottomLeft: cropCoords[1],
						bottomRight: cropCoords[2],
						topRight: cropCoords[3],
					})
				: JSON.stringify(computeBoundingBox(cropCoords));

		if (recrop) {
			const crop = crops[0];
			await $updateVisitImageMutation.mutateAsync({
				id: crop?.visitImage?.id,
				data: {
					status: { key: 'AWAITING_CROPPING' },
					rectangular_cropped_image: null,
					perspective_cropped_image_without_dimensions: null,
					crop_type: getCropType(crop.perspectiveCrop),
					data_admin_submitted_coordinates: getSubmittedCoordinates(
						crop.perspectiveCrop,
						crop.coords
					),
				},
			});

			onRecrop?.();
			return;
		}

		// const firstPhoneRes = await gqlClient.request(
		// 	GetPhonesDocument,
		// 	{},
		// 	getAuthorizationHeaders(data)
		// );

		// const firstPhone = firstPhoneRes?.Visit_Phone?.[0];
		// const phonePayload = {
		// 	id: firstPhone?.id,
		// 	icloud_account: firstPhone?.icloud_account,
		// 	authentication_status: {
		// 		key: `${firstPhone?.authentication_status?.key}`,
		// 	},
		// };

		// const lastDateString = [...data.visitImages]
		// 	.reverse()
		// 	.find(
		// 		(visitImage) => visitImage?.visit_phone_registration?.[0]?.image_date
		// 	)?.visit_phone_registration?.[0]?.image_date;

		// const lastDate = lastDateString ? new Date(lastDateString) : new Date();

		// const getRegistrationDate = (i: number) => {
		// 	const regDate = new Date(lastDate);
		// 	regDate.setSeconds(regDate.getSeconds() + i);
		// 	return regDate.toISOString();
		// };

		// const registrationPayload = {
		// 	location: 'Manual',
		// 	photographer: data.visitImages[0].photographer
		// 		?.id as InputMaybe<Update_Directus_Users_Input>,
		// 	phone: phonePayload,
		// 	start_date: getRegistrationDate(0),
		// 	end_date: getRegistrationDate(0),
		// };

		let payload = cropsToSubmit.reduce(
			(
				accumulator: Record<
					string,
					UpdateVisitImageMutationVariables['data'] & { id?: string }
				>,
				crop,
				i
			) => {
				const cropVisitImage = crop?.visitImage;
				const cropVisitImageId = cropVisitImage?.id;
				const cropType = getCropType(crop.perspectiveCrop);
				const cropCoords = crop?.coords;
				const dataAdminSubmittedCoordinates = getSubmittedCoordinates(
					crop.perspectiveCrop,
					cropCoords
				);

				// This updates the visit image with the first crop coordinates of this image
				if (!accumulator[cropVisitImageId]) {
					return {
						...accumulator,
						[cropVisitImageId]: {
							id: cropVisitImageId,
							status: { key: 'AWAITING_CROPPING' },
							data_admin_submitted_coordinates: dataAdminSubmittedCoordinates,
							crop_type: cropType,
							// ...(!cropVisitImage?.visit_phone_registration?.[0]
							// 	?.phone_registration && {
							// 	visit_phone_registration: [
							// 		{
							// 			image_date: getRegistrationDate(i),
							// 			phone: phonePayload,
							// 			phone_registration: registrationPayload,
							// 		},
							// 	],
							// }),
						},
					} as UpdateVisitImageMutationVariables['data'] & { id?: string };
				}

				// If an image has more than one crop, we create additional visit images
				return {
					...accumulator,
					[`${i}`]: {
						data_admin_submitted_coordinates: dataAdminSubmittedCoordinates,
						crop_type: cropType,
						original_uncropped_image: {
							id: `${cropVisitImage.original_uncropped_image?.id}`,
							storage: `${cropVisitImage.original_uncropped_image?.storage}`,
							filename_download: `${cropVisitImage.original_uncropped_image?.filename_download}`,
						},
						extracted_best_guess_coordinates_from_api: JSON.stringify(
							cropVisitImage.extracted_best_guess_coordinates_from_api
						),
						source: 'additional_crop',
						image_taken_date: cropVisitImage?.image_taken_date,
						status: { key: 'AWAITING_CROPPING' },
						visit_phone_registration: (() => {
							const originalVisitPhoneRegistration =
								cropVisitImage?.visit_phone_registration?.[0];

							return [
								{
									image_date: originalVisitPhoneRegistration?.image_date,
									phone: originalVisitPhoneRegistration?.phone,
									phone_registration: {
										...originalVisitPhoneRegistration?.phone_registration,
										photographer:
											originalVisitPhoneRegistration?.phone_registration
												?.photographer?.id,
									},
								},
							];

							// if (originalVisitPhoneRegistration) {
							// 	return [
							// 		{
							// 			image_date: (() => {
							// 				const date = new Date(
							// 					originalVisitPhoneRegistration?.image_date
							// 				);
							// 				date.setSeconds(date.getSeconds() + i);
							// 				return date.toISOString();
							// 			})(),
							// 			phone: originalVisitPhoneRegistration?.phone,
							// 			phone_registration: {
							// 				...originalVisitPhoneRegistration?.phone_registration,
							// 				photographer:
							// 					originalVisitPhoneRegistration?.phone_registration
							// 						?.photographer?.id,
							// 			},
							// 		},
							// 	];
							// }

							// return [
							// 	{
							// 		image_date: getRegistrationDate(i),
							// 		phone: phonePayload,
							// 		phone_registration: registrationPayload,
							// 	},
							// ];
						})(),
						visit: {
							id: `${cropVisitImage.visit?.id}`,
						},
						photographer: photographer.id as any,
					} as UpdateVisitImageMutationVariables['data'] & { id?: string },
				};
			},
			{} as Record<
				string,
				UpdateVisitImageMutationVariables['data'] & { id?: string }
			>
		);

		getOriginalCrops().forEach((originalCrop) => {
			const originalCropVisitImageId = originalCrop?.visitImage?.id;

			const isOriginalVisitImageInRemainingCrops = !!remainingCrops.find(
				(crop) => crop.visitImage.id === originalCropVisitImageId
			);

			if (
				!payload[originalCropVisitImageId] &&
				!isOriginalVisitImageInRemainingCrops
			) {
				payload = {
					...payload,
					[originalCropVisitImageId]: {
						id: originalCropVisitImageId,
						status: {
							key: 'EXCLUDED',
						},
					},
				};
			}
		});

		const updateVisitImagesReq = gqlClient.request(
			UpdateVisitImageBatchDocument,
			{
				data: Object.values(payload).filter(
					(visitImagePayload) => !!(visitImagePayload as { id: string })?.id
				) as UpdateVisitImageBatchMutationVariables['data'],
			},
			getAuthorizationHeaders(data as { user: { access_token: string } })
		);

		const createVisitImagesReq = gqlClient.request(
			CreateVisitImagesDocument,
			{
				data: Object.values(payload).filter(
					(visitImagePayload) => !(visitImagePayload as { id: string })?.id
				) as any,
			},
			getAuthorizationHeaders(data as { user: { access_token: string } })
		);

		const [_, createVisitImagesRes] = await Promise.all([
			updateVisitImagesReq,
			createVisitImagesReq,
		]);

		if (cropIndex < crops.length - 1) {
			showToast({
				message: 'Crops have been successfully reviewed',
				variant: 'success',
			});
		}

		window.location.reload();
	};
</script>

<div
	class={classNames('flex items-center p-4 justify-end', {
		'pb-3': !error,
		'pb-2': error,
	})}
>
	<div class="flex gap-2">
		{#if !recrop}
			<Button
				{disabled}
				onclick={handleAddNewCropClick}
				size="sm"
				variant="secondary"
				dataCy="exclude"
				>Add new crop
				{#snippet trailing()}
					<PlusIcon />
				{/snippet}
			</Button>
		{/if}
		<Button
			{disabled}
			onclick={handleResetCropsClick}
			size="sm"
			variant="secondary"
			dataCy="duplicate"
		>
			Reset Crops
			{#snippet trailing()}
				<RefreshIcon />
			{/snippet}
		</Button>
		<Button
			disabled={disabled ||
				notLastCropOfImage ||
				missingCoordsInPreviousOrCurrentCoords}
			loading={submitting}
			onclick={handleClickSubmit}
			size="sm"
			variant="primary"
			dataCy="reset"
		>
			Submit {cropIndex === crops.length - 1 ? 'all crops' : 'until this crop'}
		</Button>
	</div>
</div>

{#if error}
	<Txt variant="body3" class="px-4 pb-2 text-right text-red-500">
		{error}
	</Txt>
{/if}
