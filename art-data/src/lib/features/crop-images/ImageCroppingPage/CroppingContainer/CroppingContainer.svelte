<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { Cropper, type Point } from './Cropper';
	import { OptionButtons } from './OptionButtons';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import type { GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';

	let skipping = $state(false);
	let submitting = false;
	let disabled = $derived(skipping || submitting);

	interface Props {
		onRecrop?: (() => void) | undefined;
		recrop?: boolean | undefined;
		dataCy: string;
		visitImages: GetVisitImageQuery['Visit_Image'];
		photographer: {
			id?: string | null | undefined;
			first_name?: string | null;
			last_name?: string | null;
		};
	}

	let {
		onRecrop = undefined,
		recrop = false,
		dataCy,
		visitImages,
		photographer,
	}: Props = $props();

	let cropIndex = $state(0);
	let selectedPoint = $state(0);
	let cropperRef = $state(null as null | { loadImage: () => void });

	let crops: {
		perspectiveCrop: boolean;
		visitImage: GetVisitImageQuery['Visit_Image'][number];
		coords: Point[];
	}[] = $state([]);

	let dataCyPrefix = $derived(`${dataCy}-container`);

	const getOriginalCrops = () => {
		return recrop
			? (() => {
					const crop = JSON.parse(
						`${visitImages?.[0]?.data_admin_submitted_coordinates}`
					);
					return [
						{
							visitImage: visitImages?.[0],
							perspectiveCrop:
								visitImages?.[0]?.crop_type === 'perspective_crop',
							coords: [
								crop.topLeft,
								crop.bottomLeft,
								crop.bottomRight,
								crop.topRight,
							],
						},
					];
				})()
			: visitImages?.flatMap((visitImage) =>
					(visitImage?.extracted_best_guess_coordinates_from_api?.crops?.length
						? visitImage?.extracted_best_guess_coordinates_from_api?.crops
						: [null]
					)?.map(
						(
							crop: {
								topLeft: Point;
								topRight: Point;
								bottomLeft: Point;
								bottomRight: Point;
							} | null
						) => ({
							visitImage,
							perspectiveCrop: true,
							coords:
								crop === null
									? []
									: [
											crop.topLeft,
											crop.bottomLeft,
											crop.bottomRight,
											crop.topRight,
										],
						})
					)
				);
	};

	onMount(() => {
		crops = getOriginalCrops();

		document.addEventListener('keydown', (e) => {
			if (e.key === 'ArrowLeft' && cropIndex > 0) {
				handleClickPrevCrop();
			} else if (e.key === 'ArrowRight' && cropIndex < crops.length - 1) {
				handleClickNextCrop();
			} else if (crops[cropIndex].coords.length === 4) {
				if (e.key === '1') {
					selectedPoint = 1;
				} else if (e.key === '2') {
					selectedPoint = 2;
				} else if (e.key === '3') {
					selectedPoint = 3;
				} else if (e.key === '4') {
					selectedPoint = 4;
				}
			}
		});
	});

	const handleClickPrevCrop = () => {
		selectedPoint = 0;
		cropIndex = cropIndex - 1;
		if (cropIndex < 0) {
			cropIndex = crops.length - 1;
		}
		cropperRef?.loadImage();
	};

	const handleClickNextCrop = () => {
		selectedPoint = 0;
		cropIndex = cropIndex + 1;
		if (cropIndex > crops.length - 1) {
			cropIndex = 0;
		}
		cropperRef?.loadImage();
	};
</script>

{#if crops?.length}
	<div class="flex justify-center p-4">
		<div>
			<div class="mb-10 w-full rounded-md border border-gray-200 bg-white">
				<div
					class="flex flex-1 items-center justify-between border-b border-gray-200 p-4"
				>
					<div>
						<Txt variant="body2"
							>{crops?.[cropIndex]?.visitImage?.original_uncropped_image
								?.filename_download}</Txt
						>
						<Txt variant="body2"
							>{`${[photographer.first_name, photographer.last_name].filter(Boolean).join(' ')}`}</Txt
						>
					</div>

					<InputLabel
						variant="body3"
						classes={{ container: 'justify-end max-w-[150px]' }}
						dataCy={`${dataCy}-selected`}
						><Checkbox
							size="sm"
							{disabled}
							bind:checked={crops[cropIndex].perspectiveCrop}
							id="perspective"
							dataCy={`${dataCy}-perspective`}
							name="perspective"
						/>Perspective crop</InputLabel
					>
				</div>

				<div class="flex justify-center gap-4 p-4">
					<div
						class={classNames('flex items-center', {
							invisible: crops.length === 1,
						})}
					>
						<Button
							dataCy={`${dataCyPrefix}-prev`}
							class="max-h-[2rem] max-w-[2rem]"
							variant="secondary"
							onclick={handleClickPrevCrop}
							disabled={!cropIndex}
						>
							<ChevronLeftIcon class="h-3 w-3" />
						</Button>
					</div>

					<div
						class="relative flex items-center justify-center h-[400px] w-[700px]"
					>
						<Cropper
							bind:selectedPoint
							bind:this={cropperRef}
							bind:crops
							bind:cropIndex
						/>
					</div>

					<div
						class={classNames('flex items-center', {
							invisible: crops.length === 1,
						})}
					>
						<Button
							dataCy={`${dataCyPrefix}-next`}
							class="max-h-[2rem] max-w-[2rem]"
							variant="secondary"
							onclick={handleClickNextCrop}
							disabled={cropIndex === crops.length - 1}
						>
							<ChevronRightIcon class="h-3 w-3" />
						</Button>
					</div>
				</div>

				<OptionButtons
					{recrop}
					{onRecrop}
					loadImage={cropperRef?.loadImage}
					{photographer}
					{submitting}
					{disabled}
					dataCy={dataCyPrefix}
					{getOriginalCrops}
					bind:crops
					bind:cropIndex
				/>
			</div>
		</div>
	</div>
{/if}
