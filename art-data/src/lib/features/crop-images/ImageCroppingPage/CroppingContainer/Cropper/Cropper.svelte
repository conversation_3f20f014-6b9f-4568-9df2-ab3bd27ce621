<script lang="ts" module>
	export type Point = { x: number; y: number };
</script>

<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { Txt } from '$global/components/Txt';
	import type { GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		crops: {
			visitImage: GetVisitImageQuery['Visit_Image'][number];
			coords: Point[];
		}[];
		cropIndex: number;
		selectedPoint: number;
	}

	let {
		crops = $bindable(),
		cropIndex = $bindable(),
		selectedPoint = $bindable(),
	}: Props = $props();

	let ready = $state(false);
	let canvas: HTMLCanvasElement | null = $state(null);
	let ctx: any;
	let img: any;
	let scale = 1;

	const MAX_WIDTH = 700 - 32; // Set your desired max width
	const MAX_HEIGHT = 400 - 32; // Set your desired max height
	const MAX_POINTS = 4;

	onMount(() => {
		ctx = (canvas as HTMLCanvasElement).getContext('2d');
		loadImage();
		ready = true;
	});

	export const loadImage = () => {
		img = new Image();
		img.onload = () => {
			scale = Math.min(MAX_WIDTH / img.width, MAX_HEIGHT / img.height, 1);
			(canvas as HTMLCanvasElement).width = img.width * scale;
			(canvas as HTMLCanvasElement).height = img.height * scale;
			redrawCanvas();
		};
		img.src = getImageUrl(
			crops?.[cropIndex]?.visitImage?.original_uncropped_image?.id,
			page.data.user.access_token
		);
	};

	function addPoint(event: { clientX: number; clientY: number }) {
		if (crops[cropIndex]?.coords?.length >= MAX_POINTS && !selectedPoint) {
			return;
		}

		const rect = (canvas as HTMLCanvasElement).getBoundingClientRect();
		const x = (event.clientX - rect.left) / scale;
		const y = (event.clientY - rect.top) / scale;

		if (selectedPoint) {
			const sortedPoints = sortPoints(crops[cropIndex]?.coords);
			const pointToReplace = sortedPoints[selectedPoint - 1];

			const sortedIndex = crops[cropIndex].coords.findIndex(
				(point) => point.x === pointToReplace.x && point.y === pointToReplace.y
			);

			if (sortedIndex > -1) {
				crops[cropIndex].coords[sortedIndex] = { x, y };
			}

			selectedPoint = 0;
		} else {
			crops[cropIndex].coords = [...(crops[cropIndex].coords || []), { x, y }];
		}

		redrawCanvas();
	}

	function sortPoints(points: Point[]) {
		if (points?.length !== MAX_POINTS) return points;

		points.sort((a: Point, b: Point) => a.x - b.x);
		const [left, right] = [points.slice(0, 2), points.slice(2)];

		left.sort((a: Point, b: Point) => a.y - b.y);
		right.sort((a: Point, b: Point) => a.y - b.y);
		return [left[0], right[0], right[1], left[1]];
	}

	export const redrawCanvas = () => {
		ctx.clearRect(
			0,
			0,
			(canvas as HTMLCanvasElement).width,
			(canvas as HTMLCanvasElement).height
		);

		const sortedPoints = sortPoints(crops[cropIndex]?.coords);

		if (crops[cropIndex]?.coords?.length === MAX_POINTS) {
			darkenOutside(sortedPoints);
		}

		drawRectangle(sortedPoints);
	};

	function darkenOutside(sortedPoints: { x: number; y: number }[]) {
		ctx.save();

		// Create a path for the rectangle
		ctx.beginPath();
		sortedPoints.forEach((point: { x: number; y: number }, index: number) => {
			if (index === 0) {
				ctx.moveTo(point.x * scale, point.y * scale);
			} else {
				ctx.lineTo(point.x * scale, point.y * scale);
			}
		});
		ctx.closePath();

		// Create a clipping region outside the rectangle
		ctx.rect(
			0,
			0,
			(canvas as HTMLCanvasElement).width,
			(canvas as HTMLCanvasElement).height
		);
		ctx.clip('evenodd');

		// Draw a semi-transparent black overlay
		ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
		ctx.fillRect(
			0,
			0,
			(canvas as HTMLCanvasElement).width,
			(canvas as HTMLCanvasElement).height
		);

		ctx.restore();
	}

	function drawRectangle(sortedPoints: { x: number; y: number }[]) {
		ctx.beginPath();

		// (sortedPoints || []).forEach((point, index) => {
		// 	if (index === 0) {
		// 		ctx.moveTo(point.x * scale, point.y * scale);
		// 	} else {
		// 		ctx.lineTo(point.x * scale, point.y * scale);
		// 	}
		// });
		// if (crops[cropIndex]?.coords?.length === MAX_POINTS) {
		// 	ctx.closePath();
		// }

		// ctx.strokeStyle = '#36B9F1';
		// ctx.setLineDash([10, 10]);
		// ctx.lineWidth = 1;
		// ctx.stroke();

		// Draw points
		(sortedPoints || []).forEach((point, i) => {
			if (sortedPoints.length < 4 || !i || i === selectedPoint - 1) {
				ctx.beginPath();
				ctx.arc(point.x * scale, point.y * scale, 3, 0, 2 * Math.PI);
				ctx.fillStyle = i + 1 === selectedPoint ? '#FF0000' : '#36B9F1';
				ctx.fill();
			}
		});
	}

	function removeCrop() {
		crops = [...crops.slice(0, cropIndex), ...crops.slice(cropIndex + 1)];
		if (cropIndex + 1 > crops.length) {
			cropIndex = crops.length - 1;
		}

		loadImage();
	}

	let isMouseInContainer = $state(false);
	let mouseX = $state(-1);
	let mouseY = $state(-1);
	let crosshairsRef = $state(null as null | SVGElement);

	// Function to handle mouse movement
	function handleMouseMove(event: { clientX: number; clientY: number }) {
		if (crosshairsRef) {
			// Get container's position relative to viewport
			const rect = crosshairsRef.getBoundingClientRect();

			// Calculate position relative to container
			mouseX = Math.round(event.clientX - rect.left);
			mouseY = Math.round(event.clientY - rect.top);
		}
	}

	// Function called when mouse enters container
	function handleMouseEnter() {
		if (crosshairsRef) {
			isMouseInContainer = true;
			crosshairsRef.addEventListener('mousemove', handleMouseMove);
		}
	}

	// Function called when mouse leaves container
	function handleMouseLeave() {
		if (crosshairsRef) {
			isMouseInContainer = false;
			crosshairsRef.removeEventListener('mousemove', handleMouseMove);
		}
	}
</script>

<div class="relative">
	<!-- eslint-disable-next-line svelte/valid-compile -->
	<svg
		class="z-20 absolute w-full h-full left-0 top-0 cursor-pointer"
		onclick={addPoint}
		onmouseenter={handleMouseEnter}
		onmouseleave={handleMouseLeave}
		bind:this={crosshairsRef}
	>
		{#if isMouseInContainer}
			<line
				x1="0"
				y1={mouseY}
				x2={canvas?.width}
				y2={mouseY}
				stroke="red"
				stroke-width="1"
			/>

			<line
				x1={mouseX}
				y1={0}
				x2={mouseX}
				y2={canvas?.height}
				stroke="red"
				stroke-width="1"
			/>
		{/if}
	</svg>
	<canvas class="z-10 relative" bind:this={canvas} onclick={addPoint}></canvas>
	<img
		alt="crop"
		class="w-full h-full absolute left-0 top-0"
		src={getImageUrl(
			crops?.[cropIndex]?.visitImage?.original_uncropped_image?.id,
			page.data.user.access_token
		)}
	/>
</div>

{#each Array.from(Array(10).keys()) as i}
	{@const nextImage = getImageUrl(
		crops?.[cropIndex + i + 1]?.visitImage?.original_uncropped_image?.id,
		page.data.user.access_token
	)}

	{#if nextImage}
		<img alt="preload" class="hidden" src={nextImage} />
	{/if}
{/each}

<div
	class="absolute bottom-0 left-[50%] translate-x-[-50%] translate-y-1 flex items-center justify-center"
>
	{#if ready}
		<Txt variant="body3" class="mr-4">
			Crop {cropIndex + 1} of {crops.length || 1}
		</Txt>
	{/if}
	{#if crops.length > 1}
		<button onclick={removeCrop} class="relative h-4 w-[110px]">
			<Txt
				variant="body3"
				class="absolute left-0 top-0 w-[110px] text-blue-500"
				component="span"
			>
				Remove this crop
			</Txt>
		</button>
	{/if}
</div>

<style>
	canvas {
		cursor: crosshair;
		max-width: 100%;
		height: auto;
	}
</style>
