<script lang="ts">
	import { CroppingContainer } from './CroppingContainer';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import { Txt } from '$global/components/Txt';
	import { ErrorSuccessPage } from '$global/features/form/pages/ErrorSuccessPage';
	import { PageBody } from '$lib/components/PageBody';
	import type { GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';

	interface Props {
		buttonProps: {
			label: string;
			href: string;
		};
		crumbs: Crumb[];
		dataCy: string;
		title: string;
		visitImages: GetVisitImageQuery['Visit_Image'];
		photographer: {
			id?: string | null | undefined;
			first_name?: string | null;
			last_name?: string | null;
		};
	}

	let { buttonProps, crumbs, dataCy, title, visitImages, photographer }: Props =
		$props();

	const dataCyPrefix = `${dataCy}-image-cropping`;
</script>

<PageBody class="bg-gray-100">
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<Txt variant="body2" class="mb-1 uppercase tracking-[1.68px]">{title}</Txt>
		<Txt variant="h3" class="mb-6">Choose suitable crops</Txt>

		{#if visitImages?.length}
			<Txt variant="body2">
				Please review the suggested crops below and choose one to submit. If
				none are suitable, adjust one manually by dragging the corner points. If
				the image contains multiple artworks and you wish to submit crops of
				each, select "extract multiple crops from this image."
			</Txt>
			<CroppingContainer dataCy={dataCyPrefix} {visitImages} {photographer} />
		{:else}
			<ErrorSuccessPage
				class="mx-auto flex min-h-0 max-w-[32.5rem] flex-col bg-white p-8"
				dataCy={dataCyPrefix}
				title="You have reviewed every image taken by this photographer at this date"
				variant="success"
				{buttonProps}
			/>
		{/if}
	</Container>
</PageBody>
