<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	// eslint-disable-next-line import/default
	import { twMerge } from 'tailwind-merge';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { PencilIcon } from '$global/assets/icons/PencilIcon';
	import { Button } from '$global/components/Button';
	import { Drawer } from '$global/components/Drawer';
	import { Txt } from '$global/components/Txt';

	interface Props {
		drawerStores: ReturnType<typeof createDialog>;
		dataCy: string;
		children?: import('svelte').Snippet;
	}

	let { drawerStores, dataCy, children }: Props = $props();

	const {
		elements: { close },
		states: { open },
	} = drawerStores;

	let dataCyPrefix = $derived(`${dataCy}-discarded-images`);

	const handleClickClose = () => {
		open.set(false);
	};
</script>

<Drawer
	allowScroll
	dataCy={dataCyPrefix}
	{drawerStores}
	classes={{ overlay: 'hidden' }}
	class="max-w-[100%] sm:max-w-[450px]"
>
	<div class="relative h-full overflow-hidden">
		<div
			class="border-b-1 flex h-[60px] items-center justify-between border-b border-b-gray-200 bg-gray-0 px-6 py-4"
		>
			<span class="flex items-center gap-4">
				<PencilIcon class="h-[20px] w-[20px]" />
				<Txt variant="h5">Discarded images</Txt>
			</span>
			<button data-cy={`filter-drawer-close-button`} {...$close} use:close
				><CrossIcon class="h-[20px] w-[20px]" /></button
			>
		</div>

		<div
			class={twMerge(
				'h-[calc((100%-60px)-70px-148px)] overflow-y-auto bg-gray-0'
			)}
		>
			{@render children?.()}
		</div>

		<div class="h-[148px] bg-gray-50 px-6 py-4">
			<div class="bg-gray-0 p-4">
				<Txt variant="label3" class="mb-2">Drag images to restore</Txt>
				<Txt variant="body2" class="text-gray-500">
					To restore an image, drag it back into a new or existing row in the
					table to the left. You can create a new row using the '+' button.
				</Txt>
			</div>
		</div>

		<div
			class="border-t-1 absolute bottom-[1px] left-0 right-0 h-[70px] border-t border-t-gray-200 pt-4"
		>
			<div class="px-6 pb-4">
				<Button
					fullWidth
					size="md"
					dataCy={`${dataCyPrefix}-drawer-close`}
					onclick={handleClickClose}>Close</Button
				>
			</div>
		</div>
	</div>
</Drawer>
