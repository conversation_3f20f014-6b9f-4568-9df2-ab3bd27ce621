import { addTextToImagesReadOnlyServerLoad } from '../addTextToImagesReadOnlyServerLoad/addTextToImagesReadOnlyServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';

export const galleryOfferingsAddTextToImagesServerLoad = async ({
	url,
	params,
	parent,
}: {
	parent: () => Promise<{ user: { access_token: string } }>;
	url: URL;
	params: { manualUploadId: string; id: string };
}) => {
	const parentData = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const readOnlyResponse = await addTextToImagesReadOnlyServerLoad({
		url,
		params,
		parentData,
	});

	return {
		galleryOffering,
		...readOnlyResponse,
	};
};
