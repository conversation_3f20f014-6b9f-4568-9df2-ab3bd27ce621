import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetManuallyAddedArtworksDetailsIdsDocument } from '$lib/features/final-review/queries/__generated__/getManuallyAddedArtworksDetailsIds.generated';
import { gqlClient } from '$lib/gqlClient';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';

export const LIMIT = 20;

export const addTextToImagesReadOnlyServerLoad = async ({
	url,
	params,
	parentData,
}: {
	parentData: Parameters<typeof getAuthorizationHeaders>[0];
	params: { manualUploadId: string };
	url: URL;
}) => {
	const pageParam = url.searchParams.get('page');
	const page = pageParam ? +pageParam : 1;

	const manuallyAddedArtworksResponse = await gqlClient.request(
		GetManuallyAddedArtworksDetailsIdsDocument,
		{
			limit: LIMIT,
			offset: LIMIT * (page - 1),
			filter: {
				id: { _eq: params.manualUploadId },
			},
			artworkFilter: {
				status: {
					key: {
						_nin: ['DRAFT'],
					},
				},
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const manualUpload = manuallyAddedArtworksResponse?.Manual_Upload?.[0];
	const total = manualUpload?.manually_added_artworks_total?.length;
	const artworks = manualUpload?.manually_added_artworks;
	const additionalCrumb = buildUploadCrumb(
		manualUpload?.receipt_info?.receive_date,
		manualUpload?.processed_fair_exhibitor_org?.name
	);

	return { page, artworks, total, additionalCrumb };
};
