import { error } from '@sveltejs/kit';
import dayjs from 'dayjs';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { GetVisitArtworkDocument } from '$lib/queries/__generated__/getVisitArtwork.generated';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';

export const LIMIT = 20;

export const matchArtworksWithLabelsReadOnlyServerLoad = async ({
	url,
	parentData,
	visitId,
}: {
	visitId: string | undefined;
	url: URL;
	parentData: Parameters<typeof getAuthorizationHeaders>[0];
}) => {
	const pageParam = url.searchParams.get('page');
	const page = pageParam ? +pageParam : 1;

	const dateParam = url.searchParams.get('date');
	const photographerParam = url.searchParams.get('photographer');

	if (!dateParam) {
		error(404, 'The date query parameter is missing');
	}

	if (!photographerParam) {
		error(404, 'The photographer query parameter is missing');
	}

	const visitArtworksResponse = await gqlClient.request(
		GetVisitArtworkDocument,
		{
			limit: LIMIT,
			offset: LIMIT * (page - 1),
			filter: {
				_and: [
					{ visit: { id: { _eq: visitId } } },
					{
						artwork_image: {
							image_taken_date: { _gte: dayjs.utc(dateParam).toISOString() },
						},
					},
					{
						artwork_image: {
							image_taken_date: {
								_lt: dayjs.utc(dateParam).add(1, 'day').toISOString(),
							},
						},
					},
					{
						artwork_image: {
							photographer: { id: { _eq: photographerParam } },
						},
					},
					{
						status: {
							key: {
								_neq: 'DRAFT',
							},
						},
					},
				],
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const total =
		visitArtworksResponse?.Visit_Artwork_aggregated?.[0]?.countDistinct?.id;
	const visitArtworks = visitArtworksResponse?.Visit_Artwork;

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographerParam } } },
		getAuthorizationHeaders(parentData)
	);

	const photographer = usersResponse.users?.[0];

	return { page, visitArtworks, total, date: dateParam, photographer };
};
