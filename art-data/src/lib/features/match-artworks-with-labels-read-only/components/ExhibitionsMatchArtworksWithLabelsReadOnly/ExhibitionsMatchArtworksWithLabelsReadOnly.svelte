<script lang="ts">
	import { MatchArtworksWithLabelsReadOnly } from '../MatchArtworksWithLabelsReadOnly';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import { ExhibitionsDetails } from '$lib/websites/exhibitions/components/ExhibitionsDetails';
	import type { ExhibitionsIdImagesViewMatchesPageData } from '$routes/exhibitions/[id]/images/view-matches/types';

	let data = $derived(
		getPageData<ExhibitionsIdImagesViewMatchesPageData>(page.data)
	);
	let exhibition = $derived(data.exhibition);
	let visitArtworks = $derived(data.visitArtworks);
	let date = $derived(data.date);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Exhibitions', href: Routes.ExhibitionsHome },
		{
			label: `${exhibition?.processed_exhibition?.title}`,
			href: `${Routes.ExhibitionsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'View Matches' },
	]);
</script>

<MatchArtworksWithLabelsReadOnly
	{visitArtworks}
	{crumbs}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Exhibition details"
	title={`${exhibition.title}`}
	dataCy="exhibitions"
>
	{#if data.exhibition.processed_exhibition}
		<ExhibitionsDetails
			dataCy="exhibitions-match-artworks-with-labels-read-only"
			exhibition={{
				title: `${exhibition?.title}`,
				id: `${exhibition?.processed_exhibition?.processed_exhibition_id}`,
				organisers: [
					{
						entity_id: {
							name: `${exhibition?.processed_exhibition?.organisers}`,
						},
					},
				],
				venue_city: {
					name: `${exhibition?.processed_exhibition?.location}`,
					code: `${exhibition?.processed_exhibition?.location}`,
				},
				local_start_date: exhibition?.processed_exhibition?.start_date,
				local_end_date: exhibition?.processed_exhibition?.end_date,
			}}
		/>
	{/if}
</MatchArtworksWithLabelsReadOnly>
