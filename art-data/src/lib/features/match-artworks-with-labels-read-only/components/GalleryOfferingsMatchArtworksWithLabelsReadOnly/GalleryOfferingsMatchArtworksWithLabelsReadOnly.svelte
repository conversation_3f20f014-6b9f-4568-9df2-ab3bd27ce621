<script lang="ts">
	import { MatchArtworksWithLabelsReadOnly } from '../MatchArtworksWithLabelsReadOnly';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import { GalleryOfferingsDetails } from '$lib/websites/gallery-offerings/components/GalleryOfferingsDetails';
	import type { GalleryOfferingsIdImagesViewMatchesPageData } from '$routes/gallery-offerings/[id]/images/view-matches/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdImagesViewMatchesPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let visitArtworks = $derived(data.visitArtworks);
	let date = $derived(data.date);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'View Matches' },
	]);
</script>

<MatchArtworksWithLabelsReadOnly
	{visitArtworks}
	{crumbs}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Gallery offering details"
	title={`${galleryOffering.title}`}
	dataCy="gallery-offerings"
>
	{#if galleryOffering}
		<GalleryOfferingsDetails
			dataCy={'gallery-offerings-read-only'}
			galleryOffering={{
				id: `${galleryOffering?.processed_gallery?.id}`,
				organisation: {
					entity: {
						name: `${galleryOffering?.processed_gallery?.name}`,
					},
					location: {
						name: `${galleryOffering?.processed_gallery?.location}`,
						code: `${galleryOffering?.processed_gallery?.location}`,
					},
				},
			}}
		/>
	{/if}
</MatchArtworksWithLabelsReadOnly>
