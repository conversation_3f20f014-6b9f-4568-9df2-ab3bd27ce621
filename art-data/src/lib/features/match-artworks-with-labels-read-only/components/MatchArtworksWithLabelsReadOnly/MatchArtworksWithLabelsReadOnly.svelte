<script lang="ts">
	import { LIMIT } from '../../utils/matchArtworksWithLabelsReadOnlyServerLoad/matchArtworksWithLabelsReadOnlyServerLoad';
	import { page } from '$app/state';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { TickIcon } from '$global/assets/icons/TickIcon';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { type GetVisitArtworkQuery } from '$lib/queries/__generated__/getVisitArtwork.generated';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		crumbs: Crumb[];
		pageNumber: number;
		total: number;
		title: string;
		slotLabel: string;
		dataCy: string;
		visitArtworks: NonNullable<GetVisitArtworkQuery['Visit_Artwork'][number]>[];
		children?: import('svelte').Snippet;
	}

	let {
		crumbs,
		pageNumber,
		total,
		title,
		slotLabel,
		dataCy,
		visitArtworks,
		children,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-match-artwork-with-labels-read-only`);

	const onClickPage = (e: Event | undefined, pageNum: number) => {
		(window as unknown as { location: string }).location = `${
			page.url.pathname
		}?page=${pageNum}&date=${page.url.searchParams.get(
			'date'
		)}&photographer=${page.url.searchParams.get('photographer')}`;
	};

	const headers = [
		'Artwork Image',
		'Label',
		...(page.url.pathname.startsWith(Routes.FairsHome) ? ['Exhibitor'] : []),
		'Notes',
		'Install shot',
	];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<div class="mb-6 flex items-end justify-between">
			<div>
				<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
					>{title}</Txt
				>
				<Txt variant="h4">View matches</Txt>
			</div>
		</div>

		<Txt variant="h6" class="mb-3">
			{slotLabel}
		</Txt>

		<div class="flex items-end justify-between">
			<div>
				{@render children?.()}
			</div>
		</div>

		<table class="mt-6 w-full table-fixed bg-white">
			<TableHeaderRow dataCy={dataCyPrefix}>
				{#each headers as header, i}
					<TableHeader
						dataCy={dataCyPrefix}
						width={`calc(100% / ${headers.length})`}
					>
						{header}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody dataCy={dataCyPrefix}>
				{#each visitArtworks as visitArtwork, j}
					<TableRow index={j} dataCy={dataCyPrefix}>
						{#each headers as header}
							{#if header === 'Artwork Image'}
								{@const artworkImage =
									visitArtwork.artwork_image?.crop_type === 'perspective_crop'
										? visitArtwork.artwork_image
												.perspective_cropped_image_without_dimensions
										: visitArtwork.artwork_image?.rectangular_cropped_image}

								{@const augmentedArtworkImage = {
									url: `${getImageUrl(artworkImage?.id)}`,
									width: artworkImage?.width || 1,
									height: artworkImage?.height || 1,
									filename_disk: `${artworkImage?.filename_disk}`,
									alt: '',
								}}

								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div class="relative h-[100px] border border-gray-200 p-2">
											{#if artworkImage}
												<Image
													class="max-h-[5.25rem] min-h-[5.25rem] min-w-[5.25rem] max-w-[5.25rem] [&_div]:max-h-[5.25rem]  [&_div]:max-w-[5.25rem]"
													dataCy={dataCyPrefix}
													image={augmentedArtworkImage}
												/>

												<Button
													size="sm"
													dataCy={`${dataCyPrefix}-copy`}
													class="absolute bottom-1.5 right-1.5 max-h-[1.5rem] max-w-[1.5rem] px-0"
													variant="secondary"
													onclick={() => {
														navigator.clipboard.writeText(
															`${Config.Domain}${augmentedArtworkImage?.url}`
														);
													}}
												>
													<CopyIcon class="h-3 w-3" />
												</Button>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if header === 'Label'}
								{@const labelImage =
									visitArtwork.label_image?.crop_type === 'perspective_crop'
										? visitArtwork.label_image
												.perspective_cropped_image_without_dimensions
										: visitArtwork.label_image?.rectangular_cropped_image}

								{@const augmentedLabelImage = {
									url: `${getImageUrl(labelImage?.id)}`,
									width: labelImage?.width || 1,
									height: labelImage?.height || 1,
									filename_disk: `${labelImage?.filename_disk}`,
									alt: '',
								}}

								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if visitArtwork['label_image']}
												<div
													class="relative h-[100px] border border-gray-200 p-2"
												>
													<Image
														class="max-h-[5.25rem] min-h-[5.25rem] min-w-[5.25rem] max-w-[5.25rem]"
														dataCy={dataCyPrefix}
														image={augmentedLabelImage}
													/>

													<Button
														size="sm"
														dataCy={`${dataCyPrefix}-copy`}
														class="absolute bottom-1.5 right-1.5 max-h-[1.5rem] max-w-[1.5rem] px-0"
														variant="secondary"
														onclick={() => {
															navigator.clipboard.writeText(
																`${Config.Domain}${augmentedLabelImage?.url}`
															);
														}}
													>
														<CopyIcon class="h-3 w-3" />
													</Button>
												</div>
											{:else if visitArtwork.label_text}
												<div
													class="relative h-[100px] border border-gray-200 p-2"
												>
													<Txt
														class="max-h-full overflow-scroll"
														variant="body3">{visitArtwork.label_text}</Txt
													>
												</div>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if header === 'Exhibitor'}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											<Txt
												variant="body3"
												component="a"
												target="_blank"
												href={`${Config.ArteyeDomain}/organisations/${visitArtwork?.processed_fair_exhibitor_org?.id}`}
												class="block whitespace-normal break-words text-blue-500 hover:underline"
											>
												{visitArtwork.processed_fair_exhibitor_org?.name}
											</Txt>
										</div>
									{/snippet}
								</TableCell>
							{:else if header === 'Notes'}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if visitArtwork.artwork_details?.ingestion_notes}
												<Txt variant="body3">
													{visitArtwork.artwork_details?.ingestion_notes}
												</Txt>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{:else if header === 'Install shot'}
								<TableCell
									dataCy={dataCyPrefix}
									width={`calc(100% / ${headers.length})`}
								>
									{#snippet custom()}
										<div>
											{#if visitArtwork.is_installation_shot}
												<TickIcon class="h-y w-4" color="green-500" />
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{/if}
						{/each}
					</TableRow>
				{/each}
			</TableBody>
		</table>

		<div class="mt-2 flex items-center justify-between">
			{#if total}
				<Txt variant="body3">
					Showing {(pageNumber - 1) * LIMIT + 1} - {(pageNumber - 1) * LIMIT +
						visitArtworks.length} of {total}
					results
				</Txt>
			{/if}
			{#key pageNumber}
				{#key visitArtworks.length}
					<Pagination
						onClick={onClickPage}
						dataCy={dataCyPrefix}
						currentPage={pageNumber}
						limit={LIMIT}
						{total}
					/>
				{/key}
			{/key}
		</div>
	</Container>
</PageBody>
