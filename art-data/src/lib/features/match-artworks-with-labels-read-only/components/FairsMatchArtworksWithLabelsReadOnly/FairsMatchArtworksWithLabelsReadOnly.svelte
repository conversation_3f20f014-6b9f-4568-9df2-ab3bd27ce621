<script lang="ts">
	import { MatchArtworksWithLabelsReadOnly } from '../MatchArtworksWithLabelsReadOnly';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
	import { FairsDetails } from '$lib/websites/fairs/components/FairsDetails';
	import type { FairsIdImagesViewMatchesPageData } from '$routes/fairs/[id]/images/view-matches/types';

	let data = $derived(getPageData<FairsIdImagesViewMatchesPageData>(page.data));
	let fair = $derived(data.fair);
	let visitArtworks = $derived(data.visitArtworks);
	let date = $derived(data.date);
	let photographer = $derived(data.photographer);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Fairs', href: Routes.FairsHome },
		{
			label: `${fair?.processed_fair?.title}`,
			href: `${Routes.FairsHome}/${page.params.id}`,
		},
		{
			label: buildUploadCrumb(
				date,
				[photographer.first_name, photographer.last_name]
					.filter(Boolean)
					.join(' ')
			),
		},
		{ label: 'View Matches' },
	]);
</script>

<MatchArtworksWithLabelsReadOnly
	{visitArtworks}
	{crumbs}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Fair details"
	title={`${fair?.processed_fair?.title}`}
	dataCy="fairs"
>
	{#if fair}
		<FairsDetails
			dataCy={`fairs-edit-extracted-images`}
			fair={{
				title: `${fair?.processed_fair?.title}`,
				id: `${data?.fair?.processed_fair?.processed_fair_id}`,
				local_start_date: fair?.processed_fair?.start_date,
				local_end_date: fair?.processed_fair?.end_date,
				venue_city: {
					code: `${fair?.processed_fair?.location}`,
					name: `${fair?.processed_fair?.location}`,
				},
			}}
		/>
	{/if}
</MatchArtworksWithLabelsReadOnly>
