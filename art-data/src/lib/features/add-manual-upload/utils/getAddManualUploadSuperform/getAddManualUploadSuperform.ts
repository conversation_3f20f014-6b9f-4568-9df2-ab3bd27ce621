import { zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms/server';
import {
	AddManualUploadFieldNames,
	AddManualUploadValidationSchema,
} from '../../constants/add-manual-upload-validation-schema';

export const getAddManualUploadSuperform = async () => {
	const addManualUploadForm = await superValidate(
		zod(AddManualUploadValidationSchema, {
			defaults: {
				title: '',
				received_date: '',
				sender: '',
				receiver: '',
				[AddManualUploadFieldNames.IncludesPrices]: false,
				[AddManualUploadFieldNames.HighPriority]: false,
			},
		})
	);

	return { addManualUploadForm };
};
