<script lang="ts">
	import classNames from 'classnames';
	import { AddManualUploadFieldNames } from '../../constants/add-manual-upload-validation-schema';
	import { AddManualUploadDetails } from './AddManualUploadDetails';
	import { AddManualUploadForm } from './AddManualUploadForm';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import {
		ErrorSuccessPage,
		type ErrorSuccessPageProps,
	} from '$global/features/form/pages/ErrorSuccessPage';
	import { getUTCDayDate } from '$global/utils/getUTCDayDate/getUTCDayDate';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import { createReceiptInfoMutation } from '$lib/features/receipt-info/utils/createReceiptInfoMutation/createReceiptInfoMutation';
	import { gqlClient } from '$lib/gqlClient';
	import { GetProcessedOrganisationDocument } from '$lib/queries/__generated__/getProcessedOrganisation.generated';
	import { IngestionDataTypenames } from '$lib/types';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';
	import type { HomePageData } from '$routes/types';

	interface Props {
		key: string;
		collection: string;
		crumbs: Crumb[];
		title: string;
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props: ErrorSuccessPageProps['button2Props'];
		backButtonHref: string;
	}

	let {
		key,
		collection,
		crumbs,
		title,
		buttonProps,
		button2Props,
		backButtonHref,
	}: Props = $props();

	let files: DropzoneFile[] = $state([]);
	let success = $state(false);
	let details:
		| (Record<AddManualUploadFieldNames, string> & {
				fairExhibitor?: OptionType;
		  })
		| null = $state(null);

	const dataCyPrefix = `${key}-add-manual-upload`;
	const createReceiptInfo = createReceiptInfoMutation();

	const handleSubmit = async () => {
		if (!details) {
			return;
		}

		const receiptInfoResponse = await $createReceiptInfo.mutateAsync({
			input: {
				...(details[AddManualUploadFieldNames.Sender] && {
					sender: details[AddManualUploadFieldNames.Sender],
				}),
				...(details[AddManualUploadFieldNames.Receiver] && {
					receiver: details[AddManualUploadFieldNames.Receiver],
				}),
				receive_date: getUTCDayDate(
					details[AddManualUploadFieldNames.ReceivedDate]
				),
			},
		});

		const uploadedFilePromises = files.map((file) => {
			// eslint-disable-next-line no-async-promise-executor
			return new Promise(async (resolve) => {
				const fileUrl = file.url;

				if (!fileUrl) {
					const uplodedPdfFile = await uploadFile(
						file as File,
						getAuthorizationHeaders(
							page.data as Parameters<typeof getAuthorizationHeaders>[0]
						)
					);

					resolve(uplodedPdfFile.id);
				} else {
					const uplodedPdfFileResponse = await fetch(
						'/api/upload-file-from-url',
						{
							method: 'POST',
							headers: getAuthorizationHeaders(page.data as HomePageData),
							body: JSON.stringify({ url: fileUrl }),
						}
					);

					const uploadedPdfFile = await uplodedPdfFileResponse.json();
					resolve(uploadedPdfFile.id);
				}
			});
		});

		const uploadedFileIds = await Promise.all(uploadedFilePromises);

		const fairExhibitorPayload = await (async () => {
			const fairExhibitor = details?.fairExhibitor;

			if (!fairExhibitor) {
				return null;
			}

			const fairExhibitorResponse = await gqlClient.request(
				GetProcessedOrganisationDocument,
				{
					filter: {
						_and: [
							{
								name: {
									_eq: `${fairExhibitor?.line1}`,
								},
								entity: { _eq: `${fairExhibitor?.line3}` },
							},
						],
					},
				},
				getAuthorizationHeaders(page.data as { user: { access_token: string } })
			);

			const processedFairExhibitor =
				fairExhibitorResponse?.Processed_Organisation?.[0];

			if (processedFairExhibitor) {
				return {
					id: processedFairExhibitor.id,
					name: processedFairExhibitor.name,
				};
			}

			return {
				id: `${fairExhibitor?.line6}`,
				entity: `${fairExhibitor?.line3}`,
				name: `${fairExhibitor?.line1}`,
				location: fairExhibitor?.line5,
				type: fairExhibitor?.line4,
			};
		})();

		const createManualUploadResponse = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/${collection}/${page.params.id}`,
			{
				method: 'PATCH',
				headers: {
					...getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					),
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ingestion_data: {
						create: [
							{
								collection: IngestionDataTypenames.ManualUpload,
								item: {
									status: {
										key: 'DRAFT',
									},
									processed_fair_exhibitor_org: fairExhibitorPayload,
									title: details[AddManualUploadFieldNames.Title],
									includes_prices:
										details[AddManualUploadFieldNames.IncludesPrices],
									for_freelancers:
										details[AddManualUploadFieldNames.ForFreelancers],
									high_priority:
										details[AddManualUploadFieldNames.HighPriority],
									receipt_info:
										receiptInfoResponse?.create_Receipt_Information_item?.id,
									reference_files: {
										delete: [],
										update: [],
										create: uploadedFileIds.map((uploadedFileId) => ({
											directus_files_id: {
												id: uploadedFileId,
											},
											Manual_Upload_id: '+',
										})),
									},
								},
							},
						],
						delete: [],
						update: [],
					},
				}),
			}
		);

		if (!createManualUploadResponse.ok) {
			return Promise.reject();
		}

		success = true;
		return Promise.resolve();
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<AddManualUploadForm
		{backButtonHref}
		dataCy={dataCyPrefix}
		{title}
		bind:details
		bind:files
		class={classNames({ hidden: !!details })}
	/>

	<AddManualUploadDetails
		onSubmit={handleSubmit}
		{files}
		dataCy={dataCyPrefix}
		bind:details
		class={classNames('mt-10', { hidden: !details || success })}
	/>

	<ErrorSuccessPage
		dataCy={dataCyPrefix}
		variant="success"
		title="Manual Upload Created"
		class={classNames('mt-10', { hidden: !details || !success })}
		{buttonProps}
		{button2Props}
	>
		{#snippet moreContent()}
			<Txt>
				The manual upload has been created. Click continue to add and process
				the data, or click done to go back to the exhibition page if you want to
				add the data at a later time.
			</Txt>
		{/snippet}
	</ErrorSuccessPage>
</PageBody>
