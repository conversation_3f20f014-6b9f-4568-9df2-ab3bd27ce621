<script lang="ts">
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';
	import { twMerge } from 'tailwind-merge';
	import {
		AddManualUploadFieldNames,
		AddManualUploadValidationSchema,
	} from '../../../constants/add-manual-upload-validation-schema';
	import { page } from '$app/state';
	import { Checkbox } from '$global/components/Checkbox';
	import { Dropzone, type DropzoneFile } from '$global/components/Dropzone';
	import { InputLabel } from '$global/components/InputLabel';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { getFormBlur } from '$global/utils/getFormBlur';
	import { getFormErrorsFromSchema } from '$global/utils/getFormErrorsFromSchema';
	import { getSuperForm } from '$global/utils/getSuperForm';
	import { isFormValid } from '$global/utils/isFormValid';
	import { Routes } from '$lib/constants/routes';
	import { FairExhibitorAutocomplete } from '$lib/features/fair-exhibitors/components/FairExhibitorAutocomplete';

	interface Props {
		backButtonHref: string;
		title: string;
		files: DropzoneFile[];
		dataCy: string;
		class?: string;
		details:
			| (Record<AddManualUploadFieldNames, string> & {
					fairExhibitor?: OptionType;
			  })
			| null;
	}

	let {
		backButtonHref,
		details = $bindable(),
		title,
		files = $bindable([]),
		dataCy,
		...rest
	}: Props = $props();

	let fairExhibitorSelectedOption: null | OptionType = $state(null);
	let isFair = $derived(page.url.pathname.startsWith(Routes.FairsHome));

	const dataCyPrefix = `${dataCy}-add-pdf-form`;

	const handleSubmit = (e: Event) => {
		e.preventDefault();
		details = {
			// [AddManualUploadFieldNames.ForFreelancers]:
			// 	$form[AddManualUploadFieldNames.ForFreelancers],
			[AddManualUploadFieldNames.HighPriority]:
				$form[AddManualUploadFieldNames.HighPriority],
			[AddManualUploadFieldNames.IncludesPrices]:
				$form[AddManualUploadFieldNames.IncludesPrices],
			[AddManualUploadFieldNames.Title]: $form[AddManualUploadFieldNames.Title],
			[AddManualUploadFieldNames.ReceivedDate]:
				$form[AddManualUploadFieldNames.ReceivedDate],
			...($form[AddManualUploadFieldNames.Sender] && {
				[AddManualUploadFieldNames.Sender]:
					$form[AddManualUploadFieldNames.Sender],
			}),
			...(!!fairExhibitorSelectedOption && {
				fairExhibitor: fairExhibitorSelectedOption,
			}),
			...($form[AddManualUploadFieldNames.Receiver] && {
				[AddManualUploadFieldNames.Receiver]:
					$form[AddManualUploadFieldNames.Receiver],
			}),
		} as Record<AddManualUploadFieldNames, string>;
	};

	const { form, constraints } = superForm(page.data.addManualUploadForm, {
		validators: zod(AddManualUploadValidationSchema),
		validationMethod: 'auto',
	});

	const { blur, handleBlur } = getFormBlur(page.data.addManualUploadForm);
	const errors = getFormErrorsFromSchema(form, AddManualUploadValidationSchema);
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt
			variant="body3"
			class="col-span-2 mb-[-0.5rem] uppercase tracking-[1.68px]">{title}</Txt
		>
		<Txt variant="h4" class="col-span-2 mb-2">Add Manual Upload</Txt>

		<div class="col-span-2">
			<InputLabel dataCy={`${dataCyPrefix}-pdf-label`} class="mb-2"
				>Reference files</InputLabel
			>
			<Dropzone
				maxSize={100000000}
				dropzoneUrlDialogForm={page.data.dropzoneUrlDialogForm}
				{getSuperForm}
				bind:files
				dataCy={`${dataCyPrefix}-pdf`}
				accept={['application/pdf', 'image/jpeg', 'image/png']}
				multiple
			/>
		</div>

		<StepInput
			dataCy={`${dataCyPrefix}-input-name`}
			label="Input Name"
			placeholder="Input Name"
			id={AddManualUploadFieldNames.Title}
			name={AddManualUploadFieldNames.Title}
			type="text"
			class="col-span-2"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddManualUploadFieldNames.Title] &&
				$errors[AddManualUploadFieldNames.Title]}
			bind:value={$form[AddManualUploadFieldNames.Title]}
			onblur={handleBlur(AddManualUploadFieldNames.Title)}
			{...$constraints[AddManualUploadFieldNames.Title]}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-received-date`}
			label="Received Date"
			class="col-span-2"
			placeholder="DD/MM/YYYY"
			id={AddManualUploadFieldNames.ReceivedDate}
			name={AddManualUploadFieldNames.ReceivedDate}
			type="date"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddManualUploadFieldNames.ReceivedDate] &&
				$errors[AddManualUploadFieldNames.ReceivedDate]}
			bind:value={$form[AddManualUploadFieldNames.ReceivedDate]}
			onblur={handleBlur(AddManualUploadFieldNames.ReceivedDate)}
			{...$constraints[AddManualUploadFieldNames.ReceivedDate]}
		/>

		{#if isFair}
			<div class="col-span-2">
				<InputLabel
					class="mb-2"
					required
					dataCy={`${dataCyPrefix}-fair-exhibitor`}>Fair exhibitor</InputLabel
				>
				<FairExhibitorAutocomplete
					bind:selectedOption={fairExhibitorSelectedOption}
					dataCy={`${dataCyPrefix}`}
				/>
			</div>
		{/if}

		<StepInput
			dataCy={`${dataCyPrefix}-sender`}
			label="Sender"
			placeholder="<EMAIL>"
			id={AddManualUploadFieldNames.Sender}
			name={AddManualUploadFieldNames.Sender}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddManualUploadFieldNames.Sender] &&
				$errors[AddManualUploadFieldNames.Sender]}
			bind:value={$form[AddManualUploadFieldNames.Sender]}
			onblur={handleBlur(AddManualUploadFieldNames.Sender)}
			{...$constraints[AddManualUploadFieldNames.Sender]}
			required={false}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-receiver`}
			label="Receiver"
			placeholder="<EMAIL>"
			id={AddManualUploadFieldNames.Receiver}
			name={AddManualUploadFieldNames.Receiver}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddManualUploadFieldNames.Receiver] &&
				$errors[AddManualUploadFieldNames.Receiver]}
			bind:value={$form[AddManualUploadFieldNames.Receiver]}
			onblur={handleBlur(AddManualUploadFieldNames.Receiver)}
			{...$constraints[AddManualUploadFieldNames.Receiver]}
			required={false}
		/>

		<!-- <div class="col-span-2 mt-[0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-freelancers`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-freelancers-label`}
					name={AddManualUploadFieldNames.ForFreelancers}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddManualUploadFieldNames.ForFreelancers]}
				/>

				<div class="pl-1.5">Send this to a freelancer</div>
			</InputLabel>
		</div> -->

		<div class="col-span-2 mt-[0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-high-priority`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-high-priority-label`}
					name={AddManualUploadFieldNames.HighPriority}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddManualUploadFieldNames.HighPriority]}
				/>

				<div class="pl-1.5">High priority</div>
			</InputLabel>
		</div>

		<div class="scol-span-2 mt-[-0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-includes-prices`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-includes-prices-label`}
					name={AddManualUploadFieldNames.IncludesPrices}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddManualUploadFieldNames.IncludesPrices]}
				/>

				<div class="pl-1.5">Includes Prices</div>
			</InputLabel>
		</div>
	</StepContainer>

	<StepButtons
		backButtonProps={{
			href: backButtonHref,
		}}
		continueButtonProps={{
			disabled:
				!isFormValid($errors) || (isFair && !fairExhibitorSelectedOption),
		}}
	>
		Submit
	</StepButtons>
</form>
