<script lang="ts">
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import {
		DropzoneFiles,
		type DropzoneFile,
	} from '$global/components/Dropzone';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { AddManualUploadFieldNames } from '$lib/features/add-manual-upload/constants/add-manual-upload-validation-schema';

	interface Props {
		files: DropzoneFile[];
		dataCy: string;
		onSubmit: () => Promise<void>;
		class?: string;
		details:
			| (Record<AddManualUploadFieldNames, string> & {
					fairExhibitor?: OptionType;
			  })
			| null;
	}

	let {
		files,
		dataCy,
		details = $bindable(),
		onSubmit,
		...rest
	}: Props = $props();

	let submitting = $state(false);
	let dataCyPrefix = $derived(`${dataCy}-add-pdf-details`);

	const handleClickBack = () => {
		details = null;
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		submitting = true;
		await onSubmit();
		submitting = false;
	};
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt variant="h4" class="col-span-2 mb-2"
			>Please review before uploading</Txt
		>

		<div class="col-span-2">
			<Txt
				variant="label3"
				dataCy={`${dataCyPrefix}-details`}
				class="mb-[-0.25rem]">Reference Files:</Txt
			>

			<DropzoneFiles {dataCy} class="mb-6" {files} />

			<Txt variant="label3" dataCy={`${dataCyPrefix}-details`} class="mb-2"
				>Details:</Txt
			>

			<div
				data-cy={dataCyPrefix}
				class="flex flex-col gap-1 rounded border border-gray-200 p-3"
			>
				<Txt
					dataCy={`${dataCyPrefix}-input-name`}
					class="text-left"
					variant="label3"
				>
					{details?.[AddManualUploadFieldNames.Title]}
				</Txt>
				<Txt
					dataCy={`${dataCyPrefix}-received-date`}
					class={'text-left'}
					variant="body2"
				>
					Received Date: {dayjs(
						details?.[AddManualUploadFieldNames.ReceivedDate]
					).format('DD/MM/YYYY')}
				</Txt>
				{#if details?.fairExhibitor}
					<Txt
						dataCy={`${dataCyPrefix}-fair-exhibitor`}
						class={'text-left'}
						variant="body2"
					>
						Fair Exhibitor: {details?.fairExhibitor?.line1}
					</Txt>
				{/if}
				{#if details?.[AddManualUploadFieldNames.Sender]}
					<Txt
						dataCy={`${dataCyPrefix}-sender`}
						class={'text-left'}
						variant="body2"
					>
						Sender: {details[AddManualUploadFieldNames.Sender]}
					</Txt>
				{/if}
				{#if details?.[AddManualUploadFieldNames.Receiver]}
					<Txt
						dataCy={`${dataCyPrefix}-receiver`}
						class={'text-left'}
						variant="body2"
					>
						Receiver: {details[AddManualUploadFieldNames.Receiver]}
					</Txt>
				{/if}
			</div>
		</div>
	</StepContainer>

	<StepButtons
		onclick={handleClickBack}
		backButtonProps={{}}
		continueButtonProps={{
			loading: submitting,
		}}
	>
		Confirm
	</StepButtons>
</form>
