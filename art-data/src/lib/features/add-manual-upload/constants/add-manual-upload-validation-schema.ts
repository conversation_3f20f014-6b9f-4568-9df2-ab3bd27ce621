import { z } from 'zod';

export enum AddManualUploadFieldNames {
	Title = 'title',
	ReceivedDate = 'received_date',
	Sender = 'sender',
	Receiver = 'receiver',
	ForFreelancers = 'for_freelancers',
	HighPriority = 'high_priority',
	IncludesPrices = 'includes_prices',
}

export enum AddManualUploadErrorMessages {
	MissingTitle = 'Please enter an input name',
	InvalidReceivedDate = 'Please enter a valid received date',
	InvalidSender = 'Please enter a valid sender email address',
	InvalidReceiver = 'Please enter a valid receiver email address',
}

export const AddManualUploadValidationSchema = z.object({
	[AddManualUploadFieldNames.Title]: z
		.string()
		.nonempty(AddManualUploadErrorMessages.MissingTitle),
	[AddManualUploadFieldNames.ReceivedDate]: z
		.string()
		.nonempty(AddManualUploadErrorMessages.InvalidReceivedDate),
	[AddManualUploadFieldNames.Sender]: z.string().optional(),
	[AddManualUploadFieldNames.Receiver]: z.string().optional(),
	[AddManualUploadFieldNames.ForFreelancers]: z.boolean().optional(),
	[AddManualUploadFieldNames.HighPriority]: z.boolean().optional(),
	[AddManualUploadFieldNames.IncludesPrices]: z.boolean().optional(),
});
