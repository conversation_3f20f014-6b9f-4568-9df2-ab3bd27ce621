import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetPdfArtworksDetailsIdsDocument } from '$lib/features/final-review/queries/__generated__/getPdfArtworksDetailsIds.generated';
import { gqlClient } from '$lib/gqlClient';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const LIMIT = 20;

export const editExtractedImagesReadOnlyServerLoad = async ({
	url,
	params,
	parentData,
}: {
	parentData: Parameters<typeof getAuthorizationHeaders>[0];
	params: { pdfId: string };
	url: URL;
}) => {
	const pageParam = url.searchParams.get('page');
	const page = pageParam ? +pageParam : 1;

	const pdfArtworksResponse = await gqlClient.request(
		GetPdfArtworksDetailsIdsDocument,
		{
			limit: LIMIT,
			offset: LIMIT * (page - 1),
			filter: {
				id: { _eq: params.pdfId },
			},
			artworkFilter: {
				status: {
					key: {
						_nin: ['DRAFT'],
					},
				},
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const pdf = pdfArtworksResponse?.PDF?.[0];
	const total = pdf?.artworks_total?.length;
	const artworks = pdf?.artworks;
	const pdfLink = `${getImageUrl(pdf?.pdf_file?.id)}`;
	const additionalCrumb = buildUploadCrumb(
		pdf?.receipt_info?.receive_date,
		pdf?.processed_fair_exhibitor_org?.name
	);

	return { page, artworks, total, pdfLink, additionalCrumb };
};
