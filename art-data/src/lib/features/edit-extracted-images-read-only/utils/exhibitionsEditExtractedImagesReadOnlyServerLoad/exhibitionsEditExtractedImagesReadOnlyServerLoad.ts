import { editExtractedImagesReadOnlyServerLoad } from '../editExtractedImagesReadOnlyServerLoad/editExtractedImagesReadOnlyServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsDocument } from '$lib/websites/exhibitions/queries/__generated__/getExhibitions.generated';

export const exhibitionsEditExtractedImagesReadOnlyServerLoad = async ({
	url,
	params,
	parent,
}: {
	parent: () => Promise<{ user: { access_token: string } }>;
	url: URL;
	params: { pdfId: string; id: string };
}) => {
	const parentData = await parent();

	const exhibitionsResponse = await gqlClient.request(
		GetExhibitionsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const exhibition = exhibitionsResponse?.Ingestion_Exhibition?.[0];
	const readOnlyResponse = await editExtractedImagesReadOnlyServerLoad({
		url,
		params,
		parentData,
	});

	return {
		exhibition,
		...readOnlyResponse,
	};
};
