import { editExtractedImagesReadOnlyServerLoad } from '../editExtractedImagesReadOnlyServerLoad/editExtractedImagesReadOnlyServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetGalleryOfferingsDocument } from '$lib/websites/gallery-offerings/queries/__generated__/getGalleryOfferings.generated';

export const galleryOfferingsEditExtractedImagesReadOnlyServerLoad = async ({
	url,
	params,
	parent,
}: {
	parent: () => Promise<{ user: { access_token: string } }>;
	url: URL;
	params: { pdfId: string; id: string };
}) => {
	const parentData = await parent();

	const galleryOfferingsResponse = await gqlClient.request(
		GetGalleryOfferingsDocument,
		{ filter: { id: { _eq: params.id } } },
		getAuthorizationHeaders(parentData)
	);

	const galleryOffering =
		galleryOfferingsResponse?.Ingestion_Gallery_Offering?.[0];
	const readOnlyResponse = await editExtractedImagesReadOnlyServerLoad({
		url,
		params,
		parentData,
	});

	return {
		galleryOffering,
		...readOnlyResponse,
	};
};
