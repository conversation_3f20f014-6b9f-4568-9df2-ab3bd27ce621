import { editExtractedImagesReadOnlyServerLoad } from '../editExtractedImagesReadOnlyServerLoad/editExtractedImagesReadOnlyServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { GetFairsDocument } from '$lib/websites/fairs/queries/__generated__/getFairs.generated';

export const fairsEditExtractedImagesReadOnlyServerLoad = async ({
	url,
	params,
	parent,
}: {
	parent: () => Promise<{ user: { access_token: string } }>;
	url: URL;
	params: { pdfId: string; id: string };
}) => {
	const parentData = await parent();

	const fairsResponse = await gqlClient.request(
		GetFairsDocument,
		{
			filter: { id: { _eq: params.id } },
		},
		getAuthorizationHeaders(parentData)
	);

	const fair = fairsResponse?.Ingestion_Fair?.[0];
	const readOnlyResponse = await editExtractedImagesReadOnlyServerLoad({
		url,
		params,
		parentData,
	});

	return {
		fair,
		...readOnlyResponse,
	};
};
