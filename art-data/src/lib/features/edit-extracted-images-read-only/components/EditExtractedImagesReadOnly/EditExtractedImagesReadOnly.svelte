<script lang="ts">
	import { LIMIT } from '../../utils/editExtractedImagesReadOnlyServerLoad/editExtractedImagesReadOnlyServerLoad';
	import { page } from '$app/state';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		pdfLink?: string | undefined;
		crumbs: Crumb[];
		pageNumber: number;
		total: number;
		title: string;
		slotLabel: string;
		dataCy: string;
		artworks:
			| ({
					id: string;
					images?: Array<{
						directus_files_id?: {
							id: string;
							filename_download: string;
							storage: string;
							width?: number | null;
							height?: number | null;
							filename_disk?: string | null;
						} | null;
					} | null> | null;
					artwork_details?: {
						id: string;
						sale_status?: string | null;
						description?: string | null;
					} | null;
			  } | null)[]
			| null
			| undefined;
		children?: import('svelte').Snippet;
	}

	let {
		pdfLink = undefined,
		crumbs,
		pageNumber,
		total,
		title,
		slotLabel,
		dataCy,
		artworks,
		children,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-edit-extracted-images-read-only`);

	const onClickPage = (e: Event | undefined, pageNum: number) => {
		(window as unknown as { location: string }).location =
			`${page.url.pathname}?page=${pageNum}`;
	};

	const headers = ['Artwork Image', 'Text', 'Sale Status'];
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<div class="mb-6 flex items-end justify-between">
			<div>
				<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
					>{title}</Txt
				>
				<Txt variant="h4">View matches</Txt>
			</div>

			{#if pdfLink}
				<LinkButton
					size="md"
					dataCy={`${dataCyPrefix}-download-pdf`}
					href={pdfLink}
					newTab
					icon
					variant="secondary"
				>
					{#snippet leading()}
						<DownloadIcon />
					{/snippet}
					download pdf
				</LinkButton>
			{/if}
		</div>

		<Txt variant="h6" class="mb-3">
			{slotLabel}
		</Txt>

		<div class="flex items-end justify-between">
			<div>
				{@render children?.()}
			</div>
		</div>

		<table class="mt-6 w-full table-fixed bg-white">
			<TableHeaderRow dataCy={dataCyPrefix}>
				{#each headers as header, i}
					<TableHeader
						dataCy={dataCyPrefix}
						width={`calc(100% / ${headers.length})`}
					>
						{header}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			{#if artworks}
				<TableBody dataCy={dataCyPrefix}>
					{#each artworks as pdfArtwork, j}
						<TableRow index={j} dataCy={dataCyPrefix}>
							{#each headers as header}
								{#if header === 'Artwork Image'}
									<TableCell
										dataCy={dataCyPrefix}
										width={`calc(100% / ${headers.length})`}
									>
										{#snippet custom()}
											<div
												class="relative flex h-[100px] gap-2 overflow-scroll border border-gray-200 p-2"
											>
												{#each pdfArtwork?.images || [] as image}
													{@const artworkImage = image?.directus_files_id}

													{@const augmentedArtworkImage = {
														url: `${getImageUrl(artworkImage?.id)}`,
														width: artworkImage?.width || 1,
														height: artworkImage?.height || 1,
														filename_disk: `${artworkImage?.filename_disk}`,
														alt: '',
													}}

													{#if artworkImage}
														<Image
															class="max-h-[5.25rem] min-h-[5.25rem] min-w-[5.25rem] max-w-[5.25rem] [&_div]:max-h-[5.25rem]  [&_div]:max-w-[5.25rem]"
															dataCy={dataCyPrefix}
															image={augmentedArtworkImage}
														/>
													{/if}
												{/each}
											</div>
										{/snippet}</TableCell
									>
								{:else if header === 'Text'}
									<TableCell
										dataCy={dataCyPrefix}
										width={`calc(100% / ${headers.length})`}
									>
										{#snippet custom()}
											<div>
												<div
													class="relative h-[100px] border border-gray-200 p-2"
												>
													<Txt
														class="max-h-full overflow-scroll"
														variant="body3"
														>{pdfArtwork?.artwork_details?.description}</Txt
													>
												</div>
											</div>
										{/snippet}
									</TableCell>
								{:else if header === 'Sale Status'}
									<TableCell
										dataCy={dataCyPrefix}
										width={`calc(100% / ${headers.length})`}
									>
										{#snippet custom()}
											<div>
												<Txt variant="body3">
													{pdfArtwork?.artwork_details?.sale_status}
												</Txt>
											</div>
										{/snippet}
									</TableCell>
								{/if}
							{/each}
						</TableRow>
					{/each}
				</TableBody>
			{/if}
		</table>

		{#if artworks}
			<div class="mt-2 flex items-center justify-between">
				{#if total}
					<Txt variant="body3">
						Showing {(pageNumber - 1) * LIMIT + 1} - {(pageNumber - 1) * LIMIT +
							artworks?.length} of {total}
						results
					</Txt>
				{/if}
				{#key pageNumber}
					{#key artworks?.length}
						<Pagination
							onClick={onClickPage}
							dataCy={dataCyPrefix}
							currentPage={pageNumber}
							limit={LIMIT}
							{total}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	</Container>
</PageBody>
