<script lang="ts">
	import { EditExtractedImagesReadOnly } from '../EditExtractedImagesReadOnly';
	import { page } from '$app/state';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { GalleryOfferingsDetails } from '$lib/websites/gallery-offerings/components/GalleryOfferingsDetails';
	import type { GalleryOfferingsIdPdfsPdfIdViewMatchesPageData } from '$routes/gallery-offerings/[id]/pdfs/[pdfId]/view-matches/types';

	let data = $derived(
		getPageData<GalleryOfferingsIdPdfsPdfIdViewMatchesPageData>(page.data)
	);
	let galleryOffering = $derived(data.galleryOffering);
	let artworks = $derived(data.artworks);
	let additionalCrumb = $derived(data.additionalCrumb);

	let crumbs = $derived([
		{ label: 'Home', href: Routes.Home },
		{ label: 'Gallery Offerings', href: Routes.GalleryOfferingsHome },
		{
			label: `${data?.galleryOffering?.processed_gallery?.name}`,
			href: `${Routes.GalleryOfferingsHome}/${page.params.id}`,
		},
		...(additionalCrumb ? [{ label: additionalCrumb }] : []),
		{ label: 'View Matches' },
	]);
</script>

<EditExtractedImagesReadOnly
	{artworks}
	{crumbs}
	pdfLink={data.pdfLink}
	total={data.total || 1}
	pageNumber={data.page}
	slotLabel="Gallery offering details"
	title={`${galleryOffering.title}`}
	dataCy="gallery-offerings"
>
	{#if galleryOffering}
		<GalleryOfferingsDetails
			dataCy={'gallery-offerings-read-only'}
			galleryOffering={{
				id: `${galleryOffering?.processed_gallery?.id}`,
				organisation: {
					entity: {
						name: `${galleryOffering?.processed_gallery?.name}`,
					},
					location: {
						name: `${galleryOffering?.processed_gallery?.location}`,
						code: `${galleryOffering?.processed_gallery?.location}`,
					},
				},
			}}
		/>
	{/if}
</EditExtractedImagesReadOnly>
