import { gql } from 'graphql-request';

export const GET_VISIT_DISCARDED_IMAGES = gql`
	query getVisitDiscardedImages($filter: Visit_Discard_Image_filter) {
		Visit_Discard_Image(filter: $filter, limit: 10000) {
			id
			timestamp
			visit_image {
				id
				image_taken_date
				original_uncropped_image {
					id
					filename_disk
					width
					height
				}
				perspective_cropped_image_with_dimensions {
					id
					filename_disk
					width
					height
				}
				perspective_cropped_image_without_dimensions {
					id
					filename_disk
					width
					height
				}
				rectangular_cropped_image {
					id
					filename_disk
					width
					height
				}
				visit_artwork {
					id
				}
				crop_type
			}
		}
	}
`;
