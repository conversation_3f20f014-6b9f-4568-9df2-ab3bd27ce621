import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetVisitDiscardedImagesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Visit_Discard_Image_Filter>;
}>;

export type GetVisitDiscardedImagesQuery = {
	__typename?: 'Query';
	Visit_Discard_Image: Array<{
		__typename?: 'Visit_Discard_Image';
		id: string;
		timestamp?: any | null;
		visit_image?: {
			__typename?: 'Visit_Image';
			id: string;
			image_taken_date?: any | null;
			crop_type?: string | null;
			original_uncropped_image?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			perspective_cropped_image_with_dimensions?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			perspective_cropped_image_without_dimensions?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			rectangular_cropped_image?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			visit_artwork?: { __typename?: 'Visit_Artwork'; id: string } | null;
		} | null;
	}>;
};

export const GetVisitDiscardedImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getVisitDiscardedImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Discard_Image_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Discard_Image' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '10000' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_taken_date' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'original_uncropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_with_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_without_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'rectangular_cropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'visit_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'crop_type' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetVisitDiscardedImagesQuery,
	GetVisitDiscardedImagesQueryVariables
>;
