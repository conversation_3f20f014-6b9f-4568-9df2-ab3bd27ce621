import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateVisitDiscardedImageMutationVariables = Types.Exact<{
	data: Types.Create_Visit_Discard_Image_Input;
}>;

export type CreateVisitDiscardedImageMutation = {
	__typename?: 'Mutation';
	create_Visit_Discard_Image_item?: {
		__typename?: 'Visit_Discard_Image';
		id: string;
		timestamp?: any | null;
		visit_image?: {
			__typename?: 'Visit_Image';
			id: string;
			original_uncropped_image?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			perspective_cropped_image_with_dimensions?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			perspective_cropped_image_without_dimensions?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			rectangular_cropped_image?: {
				__typename?: 'directus_files';
				id: string;
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
			} | null;
			visit_artwork?: { __typename?: 'Visit_Artwork'; id: string } | null;
		} | null;
	} | null;
};

export const CreateVisitDiscardedImageDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createVisitDiscardedImage' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_Visit_Discard_Image_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_Visit_Discard_Image_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'original_uncropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_with_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_without_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'rectangular_cropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'visit_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateVisitDiscardedImageMutation,
	CreateVisitDiscardedImageMutationVariables
>;
