import { gql } from 'graphql-request';

export const CREATE_VISIT_DISCARDED_IMAGE = gql`
	mutation createVisitDiscardedImage($data: create_Visit_Discard_Image_input!) {
		create_Visit_Discard_Image_item(data: $data) {
			id
			timestamp
			visit_image {
				id
				original_uncropped_image {
					id
					filename_disk
					width
					height
				}
				perspective_cropped_image_with_dimensions {
					id
					filename_disk
					width
					height
				}
				perspective_cropped_image_without_dimensions {
					id
					filename_disk
					width
					height
				}
				rectangular_cropped_image {
					id
					filename_disk
					width
					height
				}
				visit_artwork {
					id
				}
			}
		}
	}
`;
