<script lang="ts">
	import {
		FAIRS_CELL_WIDTH,
		NO_FAIRS_CELL_WIDTH,
		VALIDATION_CELL_WIDTH,
		INSTALL_CELL_WIDTH,
	} from '../../constants/dimensions';
	import { page } from '$app/state';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		dataCy: string;
		children?: import('svelte').Snippet;
	}

	let { dataCy, children }: Props = $props();

	let isOnFairs = $derived(page.url.pathname.startsWith(Routes.FairsHome));
</script>

<div
	class="flex items-center gap-6 border border-gray-200 px-3 py-2"
	data-cy={`${dataCy}-table-header`}
>
	<div
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<Txt variant="label3">Artwork</Txt>
	</div>

	<div
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<Txt variant="label3">Label</Txt>
	</div>

	{#if page.url.pathname.startsWith(Routes.FairsHome)}
		<div
			style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
			style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		>
			<Txt variant="label3">Exhibitor</Txt>
		</div>
	{/if}

	<div
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<Txt variant="label3">Notes</Txt>
	</div>

	<div
		style:min-width={INSTALL_CELL_WIDTH}
		style:max-width={INSTALL_CELL_WIDTH}
	>
		<Txt variant="label3">Install shot</Txt>
	</div>

	<div
		style:min-width={VALIDATION_CELL_WIDTH}
		style:max-width={VALIDATION_CELL_WIDTH}
	>
		<Txt variant="label3">{@render children?.()}</Txt>
	</div>
</div>
