<script lang="ts">
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';

	interface Props {
		fairId: string;
	}

	let { fairId }: Props = $props();
</script>

<div>
	<Txt variant="body3" class="mb-[-8px] text-gray-500"
		>Cannot find the exhibitor?</Txt
	>
	<Txt
		variant="body3"
		component="a"
		href={`${Config.ArteyeDomain}/fairs/${fairId}`}
		rel="noopener noreferrer"
		target="_blank"
		class="text-blue-500 underline">Create new exhibitor on Arteye</Txt
	>
</div>
