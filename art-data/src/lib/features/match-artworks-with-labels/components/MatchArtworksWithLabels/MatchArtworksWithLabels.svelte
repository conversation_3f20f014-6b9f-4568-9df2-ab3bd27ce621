<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import dayjs from 'dayjs';
	import { type Galleries } from '../../../gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import { getMatchArtworksWithLabelsContext } from '../../utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { ImagesForm } from './ImagesForm';
	import { MatchArtworksWithLabelsDiscardedImagesDrawer } from './ImagesForm/MatchArtworksWithLabelsDiscardedImagesDrawer';
	import { KeyboardShortcutsDialog } from './KeyboardShortcutsDialog';
	import { MatchArtworksWithLabelsFooter } from './MatchArtworksWithLabelsFooter';
	import { MatchArtworksWithLabelsTable } from './MatchArtworksWithLabelsTable';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { LinkButton } from '$global/components/LinkButton';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		dataCy: string;
		slotLabel: string;
		visitId: string;
		photographerId: string | null | undefined;
		date: string;
		title: string;
		submittingDiscardedImages: boolean;
		fetching: boolean;
		submitting: boolean;
		children?: import('svelte').Snippet;
	}

	let {
		dataCy,
		slotLabel,
		visitId,
		photographerId,
		date,
		title,
		submittingDiscardedImages = $bindable(),
		fetching = $bindable(),
		submitting = $bindable(),
		children,
	}: Props = $props();

	let importedVisitImagesId: string[] = $state([]);

	const drawerStores = createDialog({ preventScroll: false });
	const keyboardShortcutsDialogStores = createDialog();

	const handleClickViewKeyboardShortcuts = () => {
		keyboardShortcutsDialogStores.states.open.set(true);
	};

	const handleClickViewDiscardedImages = () => {
		drawerStores.states.open.set(true);
	};

	const { artworkLabelImagesTextareas } = getMatchArtworksWithLabelsContext();

	let artworkGalleries: Galleries[] = $state([]);
	let offset = $state(0);
	let total = $state(0);
</script>

<div id="match-artworks-with-labels-bin" class="hidden"></div>

<Container {dataCy}>
	<div class="mb-2 flex items-end justify-between">
		<div>
			<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
				>{title}</Txt
			>
			<Txt variant="h4">Match artworks with labels</Txt>
		</div>
		<div class="flex gap-2">
			<Button
				dataCy={`${dataCy}-view-keyboard-shortcuts`}
				icon
				variant="secondary"
				size="md"
				onclick={handleClickViewKeyboardShortcuts}
			>
				view keyboard shortcuts
			</Button>

			<LinkButton
				dataCy={`${dataCy}-view-all-images`}
				icon
				variant="secondary"
				size="md"
				newTab
				href={`/${page.url.pathname.split('/')[1]}/${page.params.id}${
					Routes.ViewImages
				}?user=${page.url.searchParams.get(
					'photographer'
				)}&field=crops&from=${page.url.searchParams.get('date')}&to=${dayjs(
					page.url.searchParams.get('date')
				)
					.add(1, 'day')
					.format('YYYY-MM-DD')}`}
			>
				view all images
				{#snippet trailing()}
					<ExternalIcon />
				{/snippet}
			</LinkButton>
		</div>
	</div>

	<Txt variant="h6" class="mb-4">
		{slotLabel}
	</Txt>
</Container>

<div
	class="sticky top-0 z-30 mb-4 gap-4 border-b border-gray-200 bg-white py-4"
>
	<Container {dataCy} class="flex items-end justify-between">
		<!-- <div class="flex-1"> -->
		<div>
			{@render children?.()}
		</div>

		<div class="ml-10 flex flex-col justify-end">
			<!-- <Txt variant="label3" class="mb-2">
				Select amount of images you want to fetch
			</Txt> -->

			<ImagesForm
				onClickViewDiscardedImages={handleClickViewDiscardedImages}
				{dataCy}
			/>
		</div>
	</Container>
</div>

<Container {dataCy}>
	<Txt variant="body3" class="mb-2 uppercase tracking-[1.68px] text-gray-500">
		{#if total && offset}
			{offset} out of {total} images
		{/if}
	</Txt>

	<div style:min-height={`${34 + $artworkLabelImagesTextareas.length * 159}px`}>
		<MatchArtworksWithLabelsTable
			bind:importedVisitImagesId
			bind:artworkGalleries
			{dataCy}
		/>
	</div>
	<MatchArtworksWithLabelsDiscardedImagesDrawer {drawerStores} />
	<MatchArtworksWithLabelsFooter
		bind:total
		bind:offset
		bind:artworkGalleries
		bind:submittingDiscardedImages
		bind:fetching
		bind:submitting
		{importedVisitImagesId}
		{photographerId}
		{date}
		{dataCy}
		{visitId}
	/>
</Container>

<KeyboardShortcutsDialog dialogStores={keyboardShortcutsDialogStores} />
