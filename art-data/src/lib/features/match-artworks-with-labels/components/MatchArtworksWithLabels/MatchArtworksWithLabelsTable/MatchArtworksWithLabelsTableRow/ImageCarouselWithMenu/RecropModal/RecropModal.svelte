<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { get } from 'svelte/store';
	import { ImageCarouselWithMenuType } from '..';
	import { page } from '$app/state';
	import { Dialog } from '$global/components/Dialog';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { CroppingContainer } from '$lib/features/crop-images/ImageCroppingPage/CroppingContainer';
	import { CropVisitImageDocument } from '$lib/features/match-artworks-with-labels/custom-queries/__generated__/cropVisitImage.generated';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { getVisitImageImage } from '$lib/features/match-artworks-with-labels/utils/getVisitImageUrl/getVisitImageUrl';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { type GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';
	import { GetVisitImagesDocument } from '$lib/queries/__generated__/getVisitImages.generated';

	interface Props {
		type: ImageCarouselWithMenuType;
		index: number;
		onClose: () => void;
		dialogStores: ReturnType<typeof createDialog>;
		visitImage: GetVisitImageQuery['Visit_Image'][number];
	}

	let { type, index, onClose, dialogStores, visitImage }: Props = $props();

	const { artworkImages, artworkLabelImages } =
		getMatchArtworksWithLabelsContext();

	const handleRecrop = async () => {
		await gqlClientCustom.request(
			CropVisitImageDocument,
			{ visitImageId: visitImage.id },
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		await new Promise((r) => setTimeout(r, 5000));

		const updatedVisitImageResponse = await gqlClient.request(
			GetVisitImagesDocument,
			{
				filter: { id: { _eq: visitImage.id } },
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		const updatedVisitImage = updatedVisitImageResponse?.Visit_Image?.[0];

		const imagesStore =
			type === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages;

		imagesStore.set(
			get(imagesStore).map((image, i) => {
				if (i === index) {
					return {
						...image,
						images: [
							{
								...image.images[0],
								...getVisitImageImage(updatedVisitImage),
							},
						],
					};
				}

				return image;
			})
		);

		showToast({
			variant: 'success',
			message: 'The image has been successfully recropped',
		});

		onClose();
	};

	const dataCyPrefix = 'recrop-modal';
</script>

<Dialog
	title="Recrop image"
	showOverlay
	{dialogStores}
	dataCy={dataCyPrefix}
	{onClose}
	class="h-auto max-w-[60rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-2 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	<Txt variant="body2" class="mb-4"
		>The image will be recropped in a background process but won't show up in
		the table.
	</Txt>
	{#if visitImage.photographer}
		<CroppingContainer
			onRecrop={handleRecrop}
			recrop
			dataCy={dataCyPrefix}
			visitImages={[visitImage]}
			photographer={visitImage.photographer}
		/>
	{/if}
</Dialog>
