<script lang="ts" module>
	export const getImageAlt = (i: number) => `carousel image #${i}`;
</script>

<script lang="ts">
	import classNames from 'classnames';
	// eslint-disable-next-line import/default
	import { writable } from 'svelte/store';
	import { GallerySelector } from '../../../../../gallery-selector/components/GallerySelector';
	import { type Galleries } from '../../../../../gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import {
		ImageCarouselWithMenu,
		ImageCarouselWithMenuType,
	} from './ImageCarouselWithMenu';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { ErrorIcon } from '$global/assets/styled-icons/ErrorIcon';
	import { TickIcon } from '$global/assets/styled-icons/TickIcon';
	import { But<PERSON> } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { Routes } from '$lib/constants/routes';
	import {
		FAIRS_CELL_WIDTH,
		NO_FAIRS_CELL_WIDTH,
		INSTALL_CELL_WIDTH,
		VALIDATION_CELL_WIDTH,
	} from '$lib/features/match-artworks-with-labels/constants/dimensions';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { isRowValid } from '$lib/features/match-artworks-with-labels/utils/isRowValid/isRowValid';

	interface Props {
		index: number;
		dataCy: string;
		artworkGalleries: Galleries[];
		importedVisitImagesId: string[];
	}

	let {
		index,
		dataCy,
		artworkGalleries = $bindable(),
		importedVisitImagesId = $bindable(),
	}: Props = $props();

	let isOnFairs = $derived(page.url.pathname.startsWith(Routes.FairsHome));

	const {
		addArtwork,
		deleteArtwork,
		setArtworkNotes,
		setArtworkInstallShot,
		setArtworkImagesEmpty,
		setArtworkLabelImagesTextarea,
		handleDeleteImage,
		artworkImages,
		artworkImagesEmpty,
		artworkLabelImages,
		artworkLabelImagesTextareas,
		artworkInfo,
		handlePostDeletion,
	} = getMatchArtworksWithLabelsContext();

	const handleClickDeleteRow = () => {
		deleteArtwork(index, true);
		artworkGalleries.splice(index, 1);
	};

	const handleClickAddRow = () => {
		addArtwork(index);
		artworkGalleries.splice(index + 1, 0, [
			{ value: writable(''), selectedOption: null },
		]);
	};

	const handleChangeInstallShot = (installShot: string | boolean) => {
		const newInstallShotValue = !!installShot;
		setArtworkInstallShot(newInstallShotValue, index);

		if (newInstallShotValue) {
			setArtworkNotes('', index);
			setArtworkImagesEmpty(false, index);
			setArtworkLabelImagesTextarea(null, index);
			if ($artworkLabelImages[index].images[0]) {
				handleDeleteImage(
					`${index}-label`,
					$artworkLabelImages[index].images[0]
				);
			}
		}
	};

	const handleNotesChange = (e: Event) =>
		setArtworkNotes(
			(e as Event & { target: { value: string } }).target?.value,
			index
		);

	let valid = $derived(
		isRowValid(
			index,
			artworkGalleries[index],
			$artworkImages,
			$artworkLabelImages,
			$artworkLabelImagesTextareas,
			$artworkInfo,
			$artworkImagesEmpty
		)
	);
</script>

<div
	class={classNames('flex gap-6 border-x border-b border-gray-200 px-3 py-2', {
		'bg-gray-50': !(index % 2),
	})}
>
	<div
		class={'relative'}
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<ImageCarouselWithMenu
			{index}
			type={ImageCarouselWithMenuType.Artwork}
			dataCy={`${dataCy}-artwork`}
			bind:importedVisitImagesId
			bind:galleries={artworkGalleries}
		/>
	</div>

	<div
		class={'relative'}
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<ImageCarouselWithMenu
			{index}
			type={ImageCarouselWithMenuType.Label}
			bind:importedVisitImagesId
			bind:galleries={artworkGalleries}
			dataCy={`${dataCy}-label`}
		/>
	</div>

	{#if isOnFairs}
		<div
			class={'relative'}
			style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
			style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		>
			<GallerySelector
				bind:galleries={artworkGalleries[index]}
				{dataCy}
				onDelete={handlePostDeletion}
			/>
		</div>
	{/if}

	<div
		class={'relative'}
		style:min-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
		style:max-width={isOnFairs ? FAIRS_CELL_WIDTH : NO_FAIRS_CELL_WIDTH}
	>
		<Input
			dataCy={`${dataCy}-notes`}
			name="notes"
			disabled={$artworkInfo[index]?.install}
			placeholder="Notes"
			oninput={handleNotesChange}
			value={$artworkInfo[index]?.notes}
			type="text"
			rows={1}
			classes={{ wrapper: 'h-full' }}
			class="h-full w-full resize-none"
		/>
	</div>

	<div
		class={'relative flex justify-center'}
		style:min-width={INSTALL_CELL_WIDTH}
		style:max-width={INSTALL_CELL_WIDTH}
	>
		<Checkbox
			dataCy={`${dataCy}-install-shot`}
			name="install-shot"
			size="sm"
			checked={$artworkInfo[index]?.install}
			onChange={handleChangeInstallShot}
		/>
	</div>

	<div
		style:min-width={VALIDATION_CELL_WIDTH}
		style:max-width={VALIDATION_CELL_WIDTH}
	>
		<div class={'relative flex h-full gap-4'}>
			<div
				style:width={'2.375rem'}
				class="flex flex-col items-center justify-start"
			>
				{#if valid}
					<TickIcon class="h-5 w-5 translate-x-[1rem] pt-[3px]" />
				{:else}
					<ErrorIcon class="h-[22px] w-[22px] translate-x-[1rem]" />
				{/if}
			</div>
			<div class="flex flex-col justify-center">
				<Button
					size="sm"
					dataCy={`${dataCy}-delete`}
					class="h-[1.5rem] w-[1.5rem] px-0"
					variant="secondary"
					onclick={handleClickDeleteRow}
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>

			<div class="absolute bottom-[-1.25rem] right-2.5 z-10">
				<Button
					size="sm"
					dataCy={`${dataCy}-delete`}
					class="h-[1.5rem] w-[1.5rem] px-0"
					variant="secondary"
					onclick={handleClickAddRow}
				>
					<PlusIcon class="h-3 w-3" />
				</Button>
			</div>
		</div>
	</div>
</div>
