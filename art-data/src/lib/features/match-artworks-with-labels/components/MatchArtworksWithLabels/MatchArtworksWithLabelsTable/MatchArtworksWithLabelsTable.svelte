<script lang="ts">
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	// import { writable } from 'svelte/store';
	import { type Galleries } from '../../../../gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import { MatchArtworksWithLabelsTableHeader } from '../../MatchArtworksWithLabelsTableHeader';
	import { MatchArtworksWithLabelsTableRow } from './MatchArtworksWithLabelsTableRow';
	import { ImageCarouselWithMenuType } from './MatchArtworksWithLabelsTableRow/ImageCarouselWithMenu';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { getCarouselImages } from '$lib/features/match-artworks-with-labels/utils/getCarouselImages/getCarouselImages';
	import { getIdSuffix } from '$lib/features/match-artworks-with-labels/utils/getIdSuffix/getIdSuffix';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { shouldShowTextarea } from '$lib/features/match-artworks-with-labels/utils/shouldShowTextarea/shouldShowTextarea';

	const {
		addArtwork,
		artworkImages,
		// artworkInfo,
		artworkLabelImages,
		artworkLabelImagesTextareas,
		moveCursorToNextCell,
		moveCursorToPreviousCell,
		moveCursorToNextRow,
		moveCursorToPreviousRow,
		cursorCoords,
		setArtworkLabelImagesTextarea,
		handleDeleteImage,
		copyCurrentCellData,
		pasteCopiedData,
		handlePostDeletion,
	} = getMatchArtworksWithLabelsContext();

	interface Props {
		dataCy: string;
		importedVisitImagesId: string[];
		artworkGalleries?: Galleries[];
	}

	let {
		dataCy,
		importedVisitImagesId = $bindable(),
		artworkGalleries = $bindable([[]]),
	}: Props = $props();

	onMount(() => {
		document.addEventListener('keydown', (e) => {
			if (e.key === 'Tab' || (e.key.includes('Arrow') && e.metaKey)) {
				e.preventDefault();
			}
			if (e.metaKey) {
				if (e.key.includes('Backspace')) {
					if (!$cursorCoords) {
						return;
					}
					const rowIndex = $cursorCoords[1];
					const type =
						$cursorCoords[0] === 0
							? ImageCarouselWithMenuType.Artwork
							: ImageCarouselWithMenuType.Label;
					const showTextarea = shouldShowTextarea(
						rowIndex,
						type,
						$artworkLabelImagesTextareas
					);
					if (showTextarea) {
						setArtworkLabelImagesTextarea(null, rowIndex);
					} else {
						const images = getCarouselImages(
							rowIndex,
							type,
							$artworkImages,
							$artworkLabelImages
						);
						handleDeleteImage(getIdSuffix(rowIndex, type), images[0]);
					}
					handlePostDeletion(rowIndex);
				}
				if (e.key.includes('Arrow')) {
					if (e.key.includes('Right')) {
						moveCursorToNextCell();
						return;
					}
					if (e.key.includes('Left')) {
						moveCursorToPreviousCell();
						return;
					}
					if (e.key.includes('Down')) {
						moveCursorToNextRow();
						return;
					}
					if (e.key.includes('Up')) {
						moveCursorToPreviousRow();
						return;
					}
				}
				if (e.key === 'c') {
					copyCurrentCellData(false);
					return;
				}
				if (e.key === 'x') {
					copyCurrentCellData(true);
					return;
				}
				if (e.key === 'v') {
					pasteCopiedData();
					return;
				}
				if (e.key === 'n') {
					e.preventDefault();
					if ($cursorCoords) {
						addArtwork($cursorCoords[1]);
						return;
					}
				}
			}
		});
	});

	const handleClickAddFirstRow = () => {
		addArtwork(-1);
		artworkGalleries.splice(0, 0, [
			{ value: writable(''), selectedOption: null },
		]);
	};
</script>

<div class="relative mb-12">
	<MatchArtworksWithLabelsTableHeader {dataCy}>
		Validation
	</MatchArtworksWithLabelsTableHeader>
	<div class="absolute right-[-0.625rem] top-[1.375rem] z-10">
		<Button
			size="sm"
			dataCy={`${dataCy}-delete`}
			class="h-[1.5rem] w-[1.5rem] px-0"
			variant="secondary"
			onclick={handleClickAddFirstRow}
		>
			<PlusIcon class="h-3 w-3" />
		</Button>
	</div>
	{#key $artworkImages}
		{#key $artworkLabelImages}
			{#each $artworkImages as _, index (index)}
				<MatchArtworksWithLabelsTableRow
					{index}
					{dataCy}
					bind:artworkGalleries
					bind:importedVisitImagesId
				/>
			{/each}
		{/key}
	{/key}
</div>
