<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	// eslint-disable-next-line import/default
	import type Sortable from 'sortablejs';
	import { twMerge } from 'tailwind-merge';
	import { AddImageByIdDialog } from './AddImageByIdDialog';
	import { AddNewImageDialog } from './AddNewImageDialog';
	import { RecropModal } from './RecropModal';
	import { ImageCarouselWithMenuType } from '.';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { PasteIcon } from '$global/assets/icons/PasteIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { ScissorsIcon } from '$global/assets/icons/ScissorsIcon';
	import { But<PERSON> } from '$global/components/Button';
	import {
		ImageCarousel,
		type ImageCarouselImage,
		DEFAULT_GROUP,
		DEFAULT_STATIC_CLASSNAME,
	} from '$global/components/ImageCarousel';
	import { Input } from '$global/components/Input';

	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { MenuButton } from '$lib/components/MenuButton';
	import { MenuList } from '$lib/components/MenuList';
	import { MenuListItem } from '$lib/components/MenuListItem';
	import { MenuListSeparator } from '$lib/components/MenuListSeparator';
	import type { Galleries } from '$lib/features/gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import { getCarouselImages } from '$lib/features/match-artworks-with-labels/utils/getCarouselImages/getCarouselImages';
	import { getIdSuffix } from '$lib/features/match-artworks-with-labels/utils/getIdSuffix/getIdSuffix';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { shouldShowTextarea } from '$lib/features/match-artworks-with-labels/utils/shouldShowTextarea/shouldShowTextarea';
	import { ID_LENGTH } from '$lib/features/view-all-images/constants';
	import { getImageId } from '$lib/features/view-all-images/utils/getInitialVariables/getImageId';
	import { gqlClient } from '$lib/gqlClient';
	import {
		GetVisitImageDocument,
		type GetVisitImageQuery,
	} from '$lib/queries/__generated__/getVisitImage.generated';

	let open = $state(false);
	let visitImage: GetVisitImageQuery['Visit_Image'][number] | null =
		$state(null);

	interface Props {
		dataCy: string;
		type: ImageCarouselWithMenuType;
		index: number;
		importedVisitImagesId: string[];
		galleries: Galleries[];
	}

	let {
		dataCy,
		type,
		index,
		importedVisitImagesId = $bindable(),
		galleries = $bindable(),
	}: Props = $props();

	const addImageByIdDialogStores = createDialog();
	const addNewImageDialogStores = createDialog();
	const recropDialogStores = createDialog();

	const {
		artworkImages,
		artworkImagesEmpty,
		artworkLabelImages,
		artworkLabelImagesTextareas,
		handlePostDeletion,
		setArtworkLabelImagesTextarea,
		setArtworkImagesEmpty,
		handleDeleteImage,
		handleDragImage,
		cursorCoords,
		setCursorCoords,
		artworkInfo,
		copiedData,
	} = getMatchArtworksWithLabelsContext();

	let idSuffix = $derived(getIdSuffix(index, type));
	let images = $derived(
		getCarouselImages(index, type, $artworkImages, $artworkLabelImages)
	);

	let showTextarea = $derived(
		shouldShowTextarea(index, type, $artworkLabelImagesTextareas)
	);

	let showEmpty = $derived(
		type === 'artwork' ? $artworkImagesEmpty[index] : false
	);

	let isCut = $derived(
		index === $copiedData?.coords?.[1] &&
			((type === ImageCarouselWithMenuType.Artwork &&
				$copiedData?.coords?.[0] === 0) ||
				(type === ImageCarouselWithMenuType.Label &&
					$copiedData?.coords?.[0] === 1))
	);

	let isHighlighted = $derived(
		index === $cursorCoords?.[1] &&
			((type === ImageCarouselWithMenuType.Artwork &&
				$cursorCoords?.[0] === 0) ||
				(type === ImageCarouselWithMenuType.Label && $cursorCoords?.[0] === 1))
	);

	const handleDropEnd = (
		event: Sortable.SortableEvent,
		images: ImageCarouselImage[]
	) => {
		if (event.to !== event.from) {
			const fromListIndex = event.from.id.split('#')[1];
			const toListIndex = event.to.id.split('#')[1];
			// const { oldIndex = 0, newIndex = 0 } = event;
			const oldIndex = 0;
			const newIndex = 0;
			const movedItem = images[oldIndex];

			handleDragImage(movedItem, newIndex, fromListIndex, toListIndex);
		}
	};

	const handleClickCopyId = () => {
		navigator.clipboard.writeText(getImageId(images[0].id));
		open = false;
	};

	const handleClickCrop = async () => {
		const visitImageResponse = await gqlClient.request(
			GetVisitImageDocument,
			{
				filter: { id: { _eq: getImageId(images[0].id) } },
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		visitImage = visitImageResponse?.Visit_Image?.[0];
		recropDialogStores.states.open.set(true);
		open = false;
	};

	const handleRecropModalClose = () => {
		recropDialogStores.states.open.set(false);
		visitImage = null;
	};

	const handleFocus = () => {
		setCursorCoords(null);
	};

	const handleClickAddByImageId = () => {
		open = false;
		addImageByIdDialogStores.states.open.set(true);
	};

	const handleClickAddNewImage = () => {
		open = false;
		addNewImageDialogStores.states.open.set(true);
	};

	const handleClickAddLabelImage = () => {
		setArtworkLabelImagesTextarea('', index);
		open = false;
	};

	const handleChangeLabelTextarea: HTMLInputElement['onchange'] = (e) => {
		setArtworkLabelImagesTextarea(
			(e.target as EventTarget & { value: string })?.value,
			index
		);
	};

	const handleClickDiscard = () => {
		open = false;
		if (showTextarea) {
			setArtworkLabelImagesTextarea(null, index);
		} else if (showEmpty) {
			setArtworkImagesEmpty(false, index);
		} else {
			handleDeleteImage(idSuffix, images[0]);
		}

		const shouldDeleteGallery = handlePostDeletion(index);
		if (shouldDeleteGallery) {
			galleries.splice(index, 1);
			galleries = galleries;
		}
	};

	const handleClickAddEmpty = () => {
		setArtworkImagesEmpty(true, index);
		open = false;
	};

	// Has to be 2 when empty so that we can insert an image on top of the placeholder
	// SortableJS considers the placeholder as an item of its own
	let handlePut: Sortable.GroupOptions['put'] = (to) =>
		to.el.children.length < (images?.length ? 1 : 2);
</script>

<div
	id={`image-carousel-${idSuffix}`}
	class={classNames('relative', { 'h-full': showTextarea })}
>
	<Input
		dataCy={`${dataCy}-label`}
		class={twMerge(
			'h-full',
			isHighlighted ? 'border-2 border-blue-700' : '',
			classNames({ hidden: !showTextarea })
		)}
		placeholder="Enter some text"
		classes={{ wrapper: 'h-full' }}
		type="text"
		name="label"
		id={`image-carousel-textarea-${idSuffix}`}
		rows={1}
		value={`${$artworkLabelImagesTextareas[index]}`}
		onkeyup={handleChangeLabelTextarea}
		onFocus={handleFocus}
		onclick={handleFocus}
	/>

	<ImageCarousel
		{dataCy}
		id={`image-carousel-${+new Date()}-#${idSuffix}`}
		{images}
		zoomEnabled
		onEnd={handleDropEnd}
		class={twMerge(
			'min-h-[8.875rem] border border-gray-200 p-1',
			isCut ? 'opacity-30' : '',
			isHighlighted ? 'border-2 border-blue-700' : '',
			classNames({
				hidden: showTextarea,
				'pointer-events-none':
					showEmpty || ($artworkInfo[index]?.install && type === 'label'),
			})
		)}
		filter={`.${DEFAULT_STATIC_CLASSNAME}`}
		classes={{
			item: 'absolute z-20 max-h-[8.25rem] min-h-[8.25rem] min-w-[8.25rem] max-w-[8.25rem] [&>div]:max-h-[8.25rem] [&>div]:min-h-[8.25rem] [&>div]:min-w-[8.25rem] [&>div]:max-w-[8.25rem] [&>div>div]:max-h-[7.5rem] [&>div>div>div]:max-h-[7.5rem]',
		}}
		group={{
			name: DEFAULT_GROUP,
			put: handlePut,
		}}
	>
		{#snippet placeholder()}
			<li
				class={classNames(
					'absolute flex max-h-[8.25rem] min-h-[8.25rem] min-w-[8.25rem] max-w-[8.25rem] items-center justify-center border border-gray-200 bg-[#efefef]',
					DEFAULT_STATIC_CLASSNAME
				)}
			>
				{#if showEmpty}
					<Txt class="px-2 text-center text-gray-500" variant="body2"
						>Empty artwork</Txt
					>
				{/if}
			</li>
		{/snippet}
	</ImageCarousel>

	<Button
		dataCy={`${dataCy}-${images.length ? 'copy' : 'paste'}`}
		size="sm"
		variant="secondary"
		class="h-[2rem] w-[2rem] absolute bottom-2 right-12 z-10"
		onclick={async () => {
			if (images.length) {
				handleClickCopyId();
			} else {
				let image: ImageCarouselImage | null | undefined;
				let fromList = '';

				const value = await navigator.clipboard.readText();

				if (!value) {
					return;
				}

				const artworkImagesMatchIndex = $artworkImages.findIndex(
					(artworkImagesItem) =>
						artworkImagesItem.images.find(
							(image) => getImageId(image.id) === value
						)
				);

				if (artworkImagesMatchIndex > -1) {
					image = $artworkImages[artworkImagesMatchIndex].images.find(
						(image) => getImageId(image.id) === value
					);
					fromList = `${artworkImagesMatchIndex}-${ImageCarouselWithMenuType.Artwork}`;
				}

				if (!fromList) {
					const artworkLabelsImageMatchIndex = $artworkLabelImages.findIndex(
						(artworkLabelsImagesItem) =>
							artworkLabelsImagesItem.images.find(
								(image) => getImageId(image.id) === value
							)
					);

					if (artworkLabelsImageMatchIndex > -1) {
						image = $artworkLabelImages[
							artworkLabelsImageMatchIndex
						].images.find((image) => getImageId(image.id) === value);
						fromList = `${artworkLabelsImageMatchIndex}-${ImageCarouselWithMenuType.Label}`;
					}
				}

				if (!image || !fromList) {
					return;
				}

				handleDragImage(image, 0, fromList, idSuffix);
			}
		}}
	>
		{#if !!images.length}
			<CopyIcon />
		{:else}
			<PasteIcon />
		{/if}
	</Button>

	<MenuButton
		dataCy={`${dataCy}-grid-image-menu`}
		class="absolute bottom-2 right-2 z-10"
		onclick={() => {
			open = true;
		}}
	/>

	{#if open}
		<MenuList
			class="left-[6.5rem] top-[8.75rem] w-[12rem]"
			onClickOutside={() => {
				open = false;
			}}
		>
			<MenuListItem disabled={!images.length} onclick={handleClickCopyId}>
				<Txt variant="body2">Copy image ID</Txt>
				<CopyIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			<MenuListItem
				disabled={!images.length ||
					(images[0].id?.length || ID_LENGTH + 1) > ID_LENGTH}
				onclick={handleClickCrop}
			>
				<Txt variant="body2">Recrop</Txt>
				<ScissorsIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			<MenuListSeparator />

			<MenuListItem
				disabled={!!images.length || showTextarea}
				onclick={handleClickAddByImageId}
			>
				<Txt variant="body2">Add by image ID</Txt>
				<PlusIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			<MenuListItem
				disabled={!!images.length || showTextarea}
				onclick={handleClickAddNewImage}
			>
				<Txt variant="body2">Add new image</Txt>
				<PlusIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>

			{#if type === 'artwork'}
				<MenuListItem
					disabled={!!images.length ||
						showTextarea ||
						$artworkInfo[index].install}
					onclick={handleClickAddEmpty}
				>
					<Txt variant="body2">Add empty</Txt>
					<PlusIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
				</MenuListItem>
			{:else if type === 'label'}
				<MenuListItem
					disabled={!!images.length || showTextarea}
					onclick={handleClickAddLabelImage}
				>
					<Txt variant="body2">Add label text</Txt>
					<PlusIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
				</MenuListItem>
			{/if}

			<MenuListSeparator />

			<MenuListItem
				disabled={!images.length && !showTextarea && !showEmpty}
				onclick={handleClickDiscard}
			>
				<Txt variant="body2">Discard</Txt>
				<BinIcon class="max-h-5 min-h-5 min-w-5 max-w-5" />
			</MenuListItem>
		</MenuList>
	{/if}
</div>

<AddImageByIdDialog
	bind:importedVisitImagesId
	dialogStores={addImageByIdDialogStores}
	toList={idSuffix}
/>
<AddNewImageDialog
	contextFn={getMatchArtworksWithLabelsContext}
	dialogStores={addNewImageDialogStores}
	toList={idSuffix}
	description="This image will be created as a new image with a new ID."
/>
{#if visitImage}
	<RecropModal
		{type}
		{index}
		{visitImage}
		onClose={handleRecropModalClose}
		dialogStores={recropDialogStores}
	/>
{/if}
