<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import Dropzone from '$global/components/Dropzone/Dropzone.svelte';
	import { checkUrl } from '$global/components/Dropzone/DropzoneUrlDialog/utils/checkUrl/checkUrl';
	import { Input } from '$global/components/Input';
	import { Txt } from '$global/components/Txt';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		toList: string;
		description?: string;
		onAddImage?: (() => void) | undefined;
		contextFn: () => {
			handleSubmittedFiles: (toList: string, files: File[]) => Promise<void>;
			handleLinkImage: (
				toList: string,
				url: string,
				filename_disk: string
			) => Promise<void>;
		};
	}

	let {
		dialogStores,
		toList,
		description,
		onAddImage = undefined,
		contextFn,
	}: Props = $props();

	let files: DropzoneFile[] = $state([]);
	let linkInputValue: string | undefined = $state();
	let linkInputBlur = $state(false);
	let checkingLink = $state(false);
	let linkInputValueValid = $state(false);
	let linkInputCheckError = $state('');

	const dataCyPrefix = 'add-new-image';
	const accept = ['image/png', 'image/jpeg', 'image/jpg'];
	const maxSize = *********;

	const { handleSubmittedFiles, handleLinkImage } = contextFn();

	const handleLinkInputBlur = async () => {
		linkInputBlur = true;

		if (linkInputValue && isValidUrl(linkInputValue)) {
			linkInputCheckError = '';
			checkingLink = true;

			try {
				await checkUrl(linkInputValue, accept, maxSize);
				linkInputValueValid = true;
			} catch (e: any) {
				linkInputCheckError = e.message;
				linkInputValueValid = false;
			} finally {
				checkingLink = false;
			}
		}
	};

	const handleClose = () => {
		linkInputBlur = false;
		dialogStores.states.open.set(false);
	};

	const handleClickAddNew = () => {
		handleClose();
		if (files.length) {
			handleSubmittedFiles(toList, files as File[]);
		} else if (linkInputValue) {
			const urlParts = linkInputValue.split('/');
			handleLinkImage(toList, linkInputValue, urlParts[urlParts.length - 1]);
		}

		if (onAddImage) {
			onAddImage();
		}
	};

	let linkInputError = $derived(
		linkInputCheckError ||
			(!linkInputValue || (linkInputBlur && !isValidUrl(linkInputValue))
				? 'This link is not valid'
				: '')
	);
</script>

<Dialog
	title="Add new image via upload or link"
	showOverlay
	{dialogStores}
	dataCy={dataCyPrefix}
	onClose={handleClose}
	class="h-auto max-w-[31.25rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-2 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	{#if description}
		<Txt variant="body2" class="mb-4"
			>This image will be created as a new image with a new ID.
		</Txt>
	{/if}

	<Dropzone
		{maxSize}
		bind:files
		dataCy={dataCyPrefix}
		class="col-span-2 mb-4"
		{accept}
	/>

	<div class="relative mb-4">
		<hr />
		<Txt
			class="absolute left-[50%] translate-x-[-50%] translate-y-[-50%] bg-white px-4 font-medium tracking-widest"
			>OR</Txt
		>
	</div>

	<div class="mb-6">
		<Input
			dataCy={`${dataCyPrefix}-id`}
			name="link"
			bind:value={linkInputValue}
			onblur={handleLinkInputBlur}
			disabled={checkingLink}
			label="Link"
			required
			placeholder="eg. https://www.example.com/image.jpeg"
			error={linkInputBlur && linkInputValue ? linkInputError : ''}
		/>
	</div>

	<div class="grid grid-cols-3 gap-2">
		<Button
			dataCy={`${dataCyPrefix}-cancel`}
			onclick={handleClose}
			class="border-0"
			variant="secondary"
			size="md"
		>
			cancel
		</Button>

		<Button
			size="md"
			class="col-span-2"
			dataCy={`${dataCyPrefix}-add-new`}
			disabled={!files.length && !linkInputValueValid}
			onclick={handleClickAddNew}
		>
			add new
		</Button>
	</div>
</Dialog>
