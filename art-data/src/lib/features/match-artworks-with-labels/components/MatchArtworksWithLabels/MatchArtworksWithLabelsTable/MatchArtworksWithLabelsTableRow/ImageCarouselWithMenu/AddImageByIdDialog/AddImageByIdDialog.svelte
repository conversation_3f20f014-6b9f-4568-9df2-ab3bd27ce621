<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { ImageCarouselWithMenuType } from '..';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { type ImageCarouselImage } from '$global/components/ImageCarousel';
	import { Input } from '$global/components/Input';
	import { Txt } from '$global/components/Txt';
	import { WhiteBorderImageContainer } from '$global/components/WhiteBorderImageContainer';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { getVisitImageImage } from '$lib/features/match-artworks-with-labels/utils/getVisitImageUrl/getVisitImageUrl';
	import { getImageId } from '$lib/features/view-all-images/utils/getInitialVariables/getImageId';
	import { gqlClient } from '$lib/gqlClient';
	import { type GetVisitImageQuery } from '$lib/queries/__generated__/getVisitImage.generated';
	import { GetVisitImagesDocument } from '$lib/queries/__generated__/getVisitImages.generated';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		toList: string;
		importedVisitImagesId: string[];
	}

	let {
		dialogStores,
		toList,
		importedVisitImagesId = $bindable(),
	}: Props = $props();

	let fetchedVisitImage:
		| null
		| undefined
		| GetVisitImageQuery['Visit_Image'][number];
	let image: ImageCarouselImage | null | undefined = $state();
	let fromList = $state('');
	let error = $state('');
	let fetching = $state(false);

	const dataCyPrefix = 'add-image-by-id';

	const {
		artworkImages,
		artworkLabelImages,
		discardedImages,
		handleDragImage,
		handleDuplicateImage,
		handleDragFromExternalSource,
		replaceArtworkImages,
		replaceArtworkLabelImages,
	} = getMatchArtworksWithLabelsContext();

	const handleClose = () => {
		dialogStores.states.open.set(false);
		fetchedVisitImage = null;
		error = '';
		fromList = '';
		image = null;
	};

	const handleClickDuplicate = () => {
		if (image && fromList) {
			handleDuplicateImage(image, 0, toList);
			handleClose();
		}
	};

	const handleClickMove = () => {
		if (image && fromList) {
			if (fromList.endsWith('discarded')) {
				handleDragFromExternalSource(image, 0, toList);
			} else {
				handleDragImage(image, 0, fromList, toList);
			}

			handleClose();
		}
	};

	const handleClickImport = () => {
		if (fetchedVisitImage && image) {
			importedVisitImagesId = [
				...importedVisitImagesId,
				getImageId(fetchedVisitImage.id),
			];

			const newArtworkImages = {
				images: [image],
				page: [0],
			};

			if (toList.endsWith('artwork')) {
				replaceArtworkImages(newArtworkImages, +toList.split('-')[0]);
			} else if (toList.endsWith('label')) {
				replaceArtworkLabelImages(newArtworkImages, +toList.split('-')[0]);
			}
		}
	};

	const handleChange = async (e: Event) => {
		image = null;
		fetchedVisitImage = null;
		fromList = '';
		error = '';

		const value = (e as Event & { target: { value: string } }).target.value;

		if (!isUuidValid(value)) {
			error = 'This ID is not valid';
			return;
		}

		const artworkImagesMatchIndex = $artworkImages.findIndex(
			(artworkImagesItem) =>
				artworkImagesItem.images.find((image) => getImageId(image.id) === value)
		);

		if (artworkImagesMatchIndex > -1) {
			image = $artworkImages[artworkImagesMatchIndex].images.find(
				(image) => getImageId(image.id) === value
			);
			fromList = `${artworkImagesMatchIndex}-${ImageCarouselWithMenuType.Artwork}`;
			return;
		}

		const artworkLabelsImageMatchIndex = $artworkLabelImages.findIndex(
			(artworkLabelsImagesItem) =>
				artworkLabelsImagesItem.images.find(
					(image) => getImageId(image.id) === value
				)
		);

		if (artworkLabelsImageMatchIndex > -1) {
			image = $artworkLabelImages[artworkLabelsImageMatchIndex].images.find(
				(image) => getImageId(image.id) === value
			);
			fromList = `${artworkLabelsImageMatchIndex}-${ImageCarouselWithMenuType.Label}`;
			return;
		}

		const discardedImagesMatchIndex = $discardedImages.findIndex(
			(discardedImage) => getImageId(discardedImage.image.id) === value
		);

		if (discardedImagesMatchIndex > -1) {
			image = $discardedImages.find(
				(discardedImage) => getImageId(discardedImage.image.id) === value
			)?.image as ImageCarouselImage;
			fromList = `${discardedImagesMatchIndex}-discarded`;
			return;
		}

		fetching = true;

		const getVisitImageResponse = await gqlClient.request(
			GetVisitImagesDocument,
			{ filter: { id: { _eq: value } } },
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		const visitImage = getVisitImageResponse?.Visit_Image?.[0];

		if (!visitImage) {
			error =
				'No visit image with this ID has been found. Please check the provided ID.';

			return;
		}

		if (
			visitImage?.photographer?.id !== page.url.searchParams.get('photographer')
		) {
			error = 'This picture has been taken by another photographer';
			return;
		}

		if (
			visitImage?.image_taken_date?.slice(0, 10) !==
			page.url.searchParams.get('date')
		) {
			error = 'This picture has been taken on another date';
			return;
		}

		if (visitImage?.visit_artwork?.id) {
			error = 'This image has already been assigned to an artwork';
			return;
		}

		if (
			!visitImage?.perspective_cropped_image_without_dimensions &&
			!visitImage?.rectangular_cropped_image
		) {
			error = 'This image has not been cropped yet';
			return;
		}

		fetchedVisitImage = visitImage as GetVisitImageQuery['Visit_Image'][number];
		fromList = 'import';
		image = {
			...getVisitImageImage(visitImage),
			alt: '',
			page: 0,
			id: visitImage.id,
		} as ImageCarouselImage;

		fetching = false;
	};
</script>

<Dialog
	title="Add by image ID"
	showOverlay
	{dialogStores}
	dataCy={dataCyPrefix}
	onClose={handleClose}
	class="h-auto max-w-[31.25rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-6 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	<div class="mb-6 flex items-center justify-between gap-6">
		<div class="flex-1">
			<Input
				dataCy={`${dataCyPrefix}-id`}
				name="id"
				disabled={fetching}
				onchange={handleChange}
				label="Image ID"
				required
				placeholder="Enter image ID"
				classes={{ helper: 'mt-1' }}
				{error}
				tooltip={`You can copy the image ID by clicking the "Copy image ID" contextual menu item`}
			>
				{#snippet helper()}
					<span>The image ID is a 32 digit number</span>
				{/snippet}
			</Input>
		</div>

		<div
			class="relative flex h-[5.5rem] w-[5.5rem] items-center justify-center bg-gray-100"
		>
			<Txt class="text-gray-500">Preview</Txt>
			{#if image}
				<WhiteBorderImageContainer
					dataCy={`${dataCyPrefix}-preview`}
					alt={image.alt}
					class="absolute max-h-[5.5rem] border-gray-100 bg-gray-100 p-2 [&>div]:max-h-[5.5rem]"
					{image}
				/>
			{/if}
		</div>
	</div>

	<Txt variant="body2" class="mb-6"
		>Please enter an image ID above then choose to move or duplicate the image.
		Moving the image removes it from it's previous cell and puts into this cell.
		Duplicating the image will create a new image and then add it to this cell.
	</Txt>

	{#if fromList === 'import'}
		<Button
			dataCy={`${dataCyPrefix}-import`}
			onclick={handleClickImport}
			fullWidth
			class="mb-2"
			size="md">import</Button
		>
	{:else if fromList}
		<Button
			dataCy={`${dataCyPrefix}-move`}
			onclick={handleClickMove}
			fullWidth
			class="mb-2"
			size="md">move</Button
		>
		<Button
			dataCy={`${dataCyPrefix}-duplicate`}
			onclick={handleClickDuplicate}
			fullWidth
			class="mb-2"
			size="md">duplicate</Button
		>
	{/if}

	<Button
		variant="secondary"
		dataCy={`${dataCyPrefix}-cancel`}
		onclick={handleClose}
		fullWidth
		size="md">cancel</Button
	>
</Dialog>
