<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
	}

	let { dialogStores }: Props = $props();

	const dataCyPrefix = 'keyboard-shortcuts';

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};

	const entries = [
		{ label: 'CMD+C', description: 'Copy artwork/label image' },
		{ label: 'CMD+X', description: 'Cut artwork/label image' },
		{ label: 'CMD+V', description: 'Paste artwork/label image' },
		{ label: 'CMD+ARROW', description: 'Move Cursor' },
		{ label: 'CMD+RETURN', description: 'Delete artwork/label image' },
	];
</script>

<Dialog
	title="Keyboard shortcuts"
	showOverlay
	{dialogStores}
	dataCy={dataCyPrefix}
	onClose={handleClose}
	class="h-auto max-w-[31.25rem] rounded !p-10"
	titleVariant="h5"
	classes={{
		title: 'mb-6 text-left pl-0',
		closeIconButton: 'right-10 top-9',
	}}
>
	<Txt variant="body2" class="mb-6">
		{#each entries as { label, description }}
			<p class="flex items-center">
				<Txt component="span" variant="label3">
					{label}&nbsp;:&nbsp;
				</Txt>
				<Txt component="span" variant="body2">
					{description}
				</Txt>
			</p>
		{/each}
	</Txt>

	<Button
		dataCy={`${dataCyPrefix}-back`}
		onclick={handleClose}
		fullWidth
		size="md">back</Button
	>
</Dialog>
