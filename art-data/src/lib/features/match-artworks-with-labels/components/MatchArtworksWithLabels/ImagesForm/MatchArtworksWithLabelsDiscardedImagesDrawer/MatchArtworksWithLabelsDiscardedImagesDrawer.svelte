<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	// eslint-disable-next-line import/default
	import Sortable from 'sortablejs';
	import { DragIcon } from '$global/assets/icons/DragIcon';
	import { Button } from '$global/components/Button';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { DiscardedImagesDrawer } from '$lib/features/discarded-images-drawer/components/DiscardedImagesDrawer';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';

	interface Props {
		drawerStores: ReturnType<typeof createDialog>;
	}

	let { drawerStores }: Props = $props();

	const { handleDragFromExternalSource, discardedImages } =
		getMatchArtworksWithLabelsContext();

	let sortedDiscardedImages = $derived(
		$discardedImages.sort(
			(discardedImageA, discardedImageB) =>
				discardedImageB.timestamp - discardedImageA.timestamp
		)
	);

	const dataCyPrefix = 'match-artworks-with-labels-discarded-images-drawer';

	const sortableList = (element: HTMLUListElement) => {
		Sortable.create(element, {
			animation: 150,
			group: { name: 'group' },
			sort: false,
			onEnd: (event) => {
				if (event.to.id.length) {
					const toList = event.to.id.split('#')[1];
					const { oldIndex = 0, newIndex = 0 } = event;
					const movedItem = sortedDiscardedImages[oldIndex];
					handleDragFromExternalSource(movedItem.image, newIndex, toList);
				}
			},
		});
	};
</script>

<DiscardedImagesDrawer
	dataCy="match-artworks-with-labels-discarded-images"
	{drawerStores}
>
	{#key sortedDiscardedImages}
		<ul use:sortableList class="flex flex-wrap gap-2 px-6 py-4">
			{#each sortedDiscardedImages as discardedImage}
				<li>
					<Image
						dataCy={dataCyPrefix}
						image={discardedImage.image}
						draggable="true"
					>
						<Button
							size="xs"
							dataCy={`${dataCyPrefix}-image-drag`}
							class="absolute bottom-2 right-2 h-[1.5rem] w-[1.5rem] px-0"
							variant="secondary"
						>
							<DragIcon class="h-5 w-5" />
						</Button>
					</Image>
				</li>
			{/each}
			<li></li>
		</ul>
	{/key}
</DiscardedImagesDrawer>
