<script lang="ts">
	import { Option } from '.';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Radio } from '$global/components/Radio';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';

	interface Props {
		dataCy: string;
		onClickViewDiscardedImages: () => void;
	}

	let { dataCy, onClickViewDiscardedImages }: Props = $props();

	let customNbImages: string;
	let selectedNbImages = Option.OneHundred;

	const { discardedImages } = getMatchArtworksWithLabelsContext();
</script>

<div class="flex items-center gap-4">
	<!-- <InputLabel dataCy={`${dataCy}-all`} classes={{ container: 'w-auto' }}
		><Radio
			bind:group={selectedNbImages}
			id={Option.OneHundred}
			dataCy={`${dataCy}-one-hundred`}
			name="option"
			value={Option.OneHundred}
		/>100 images</InputLabel
	>

	<div class="flex items-center gap-2">
		<InputLabel dataCy={`${dataCy}-include`} classes={{ container: 'w-auto' }}
			><Radio
				bind:group={selectedNbImages}
				id={Option.Custom}
				dataCy={`${dataCy}-custom`}
				name="option"
				value={Option.Custom}
			/>Custom</InputLabel
		>
	</div>

	<Input
		class="min-w-[9rem] max-w-[9rem]"
		name="custom"
		dataCy={`${dataCy}-custom`}
		placeholder="Max 200"
		type="number"
		min="1"
		bind:value={customNbImages}
		disabled={selectedNbImages !== Option.Custom}
		on:keydown={(evt) =>
			['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()}
	/>

	<Button dataCy={`${dataCy}-fetch-images`} size="md">Fetch images</Button> -->
	<Button
		class="ml-10"
		variant="secondary"
		dataCy={`${dataCy}-view-discarded-images`}
		onclick={onClickViewDiscardedImages}
		disabled={!$discardedImages.length}
		size="md">View discarded images</Button
	>
</div>
