<script lang="ts">
	import { setContext } from 'svelte';
	import { Contexts } from '$lib/constants/contexts';
	import { createMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/createMatchArtworksWithLabelsContext/createMatchArtworksWithLabelsContext';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	setContext(
		Contexts.MatchArtworksWithLabels,
		createMatchArtworksWithLabelsContext()
	);
</script>

{@render children?.()}
