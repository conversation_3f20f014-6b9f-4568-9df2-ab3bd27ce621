<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import { type Galleries } from '../../../../gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import { page } from '$app/state';
	import { PUBLIC_APP_ENV } from '$env/static/public';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { type ImageCarouselImage } from '$global/components/ImageCarousel';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { CreateVisitArtworkDocument } from '$lib/features/gallery-selector/queries/__generated__/createVisitArtwork.generated';
	import { CreateVisitDiscardedImageDocument } from '$lib/features/match-artworks-with-labels/queries/__generated__/createVisitDiscardedImage.generated';
	import { DeleteVisitDiscardedImageDocument } from '$lib/features/match-artworks-with-labels/queries/__generated__/deleteVisitDiscardedImage.generated';
	import {
		GetVisitDiscardedImagesDocument,
		type GetVisitDiscardedImagesQuery,
	} from '$lib/features/match-artworks-with-labels/queries/__generated__/getVisitDiscardedImages.generated';
	import { UpdateVisitDiscardedImageDocument } from '$lib/features/match-artworks-with-labels/queries/__generated__/updateVisitDiscardedImage.generated';
	import { getMatchArtworksWithLabelsContext } from '$lib/features/match-artworks-with-labels/utils/getMatchArtworksWithLabelsContext/getMatchArtworksWithLabelsContext';
	import { getVisitImageImage } from '$lib/features/match-artworks-with-labels/utils/getVisitImageUrl/getVisitImageUrl';
	import { isRowValid } from '$lib/features/match-artworks-with-labels/utils/isRowValid/isRowValid';
	import { getImageId } from '$lib/features/view-all-images/utils/getInitialVariables/getImageId';
	import { gqlClient } from '$lib/gqlClient';
	import { GetProcessedOrganisationDocument } from '$lib/queries/__generated__/getProcessedOrganisation.generated';
	import { GetVisitImagesDocument } from '$lib/queries/__generated__/getVisitImages.generated';
	import { UpdateVisitImageDocument } from '$lib/queries/__generated__/updateVisitImage.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import type { ArtworkImages } from '$lib/types';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

	interface Props {
		dataCy: string;
		artworkGalleries: Galleries[];
		offset: number;
		visitId: string;
		date: string;
		photographerId: string | null | undefined;
		total: number | null | undefined;
		importedVisitImagesId: string[];
		submittingDiscardedImages: boolean;
		fetching: boolean;
		submitting: boolean;
	}

	let {
		dataCy,
		artworkGalleries = $bindable(),
		offset = $bindable(),
		visitId,
		date,
		photographerId,
		total = $bindable(),
		importedVisitImagesId,
		submittingDiscardedImages = $bindable(),
		fetching = $bindable(),
		submitting = $bindable(),
	}: Props = $props();

	dayjs.extend(utc);

	let visitDiscardedImages: GetVisitDiscardedImagesQuery['Visit_Discard_Image'];

	const createVisitDiscardedImage = createMutation(
		getMutation(
			CreateVisitDiscardedImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updateVisitDiscardedImage = createMutation(
		getMutation(
			UpdateVisitDiscardedImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const deleteVisitDiscardedImage = createMutation(
		getMutation(
			DeleteVisitDiscardedImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const LIMIT = ['production', 'staging'].includes(PUBLIC_APP_ENV) ? 100 : 3;

	const {
		discardedImages,
		artworkImages,
		artworkImagesEmpty,
		artworkLabelImages,
		artworkLabelImagesTextareas,
		artworkInfo,
		deleteArtwork,
	} = getMatchArtworksWithLabelsContext();

	let isOneRowValid = $derived(
		$artworkLabelImagesTextareas.some((_, index) =>
			isRowValid(
				index,
				artworkGalleries[index],
				$artworkImages,
				$artworkLabelImages,
				$artworkLabelImagesTextareas,
				$artworkInfo,
				$artworkImagesEmpty
			)
		)
	);

	const fetchDiscardedImages = async () => {
		const discardedVisitImagesResponse = await gqlClient.request(
			GetVisitDiscardedImagesDocument,
			{
				filter: {
					_and: [
						{ visit_image: { visit: { id: { _eq: visitId } } } },
						{ visit_image: { photographer: { id: { _eq: photographerId } } } },
						{
							visit_image: {
								image_taken_date: { _gte: dayjs.utc(date).toISOString() },
							},
						},
						{
							visit_image: {
								image_taken_date: {
									_lt: dayjs.utc(date).add(1, 'day').toISOString(),
								},
							},
						},
						{
							_or: [
								{
									visit_image: {
										perspective_cropped_image_without_dimensions: {
											id: { _nnull: true },
										},
									},
								},
								{
									visit_image: {
										rectangular_cropped_image: {
											id: { _nnull: true },
										},
									},
								},
							],
						},
					],
				},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		visitDiscardedImages = discardedVisitImagesResponse?.Visit_Discard_Image;
	};

	const saveDiscardedImages = async () => {
		const createVisitDiscardedImagePromises = $discardedImages
			.map(async (discardedImage, index) => {
				const existingDiscardedImage = visitDiscardedImages.find(
					(visitDiscardedImage) => {
						return (
							getImageId(visitDiscardedImage.visit_image?.id) ===
							getImageId(discardedImage.image.id)
						);
					}
				);

				if (existingDiscardedImage) {
					return null;
				}

				const visitImagePayload = await (async () => {
					if (discardedImage.image.file) {
						const uploadFileResponse = await uploadFile(
							discardedImage.image.file,
							getAuthorizationHeaders(
								page.data as { user: { access_token: string } }
							)
						);
						return {
							original_uncropped_image: uploadFileResponse,
							rectangular_cropped_image: uploadFileResponse,
							status: { key: 'CROPPED' },
							image_taken_date: dayjs.utc(date).toISOString(),
							crop_type: 'rectangular_crop',
							visit: {
								id: visitId,
							},
							photographer: photographerId,
						};
					}

					if (!discardedImage.image.url.startsWith('/api')) {
						const uplodedImageResponse = await fetch(
							'/api/upload-file-from-url',
							{
								method: 'POST',
								headers: getAuthorizationHeaders(
									page.data as { user: { access_token: string } }
								),
								body: JSON.stringify({ url: discardedImage.image.url }),
							}
						);

						const uploadFileResponse = await uplodedImageResponse.json();
						return {
							original_uncropped_image: uploadFileResponse,
							rectangular_cropped_image: uploadFileResponse,
							status: { key: 'CROPPED' },
							image_taken_date: dayjs.utc(date).toISOString(),
							crop_type: 'rectangular_crop',
							visit: {
								id: visitId,
							},
							photographer: photographerId,
						};
					}

					return {
						id: getImageId(discardedImage.image.id),
						image_taken_date: dayjs.utc(
							(discardedImage.image as unknown as { image_taken_date: string })
								.image_taken_date
						),
					};
				})();

				const newVisitDiscardedImageResponse =
					await $createVisitDiscardedImage.mutateAsync({
						data: {
							timestamp: dayjs(discardedImage.timestamp).toISOString(),
							visit_image: visitImagePayload,
							visit: {
								id: visitId,
							},
						},
					});

				const newVisitDiscardedImage =
					newVisitDiscardedImageResponse?.create_Visit_Discard_Image_item;

				if (!visitImagePayload.id && newVisitDiscardedImage) {
					discardedImages.set(
						$discardedImages.map((oldDiscardedImage, i) => {
							if (i === index) {
								return {
									timestamp: +new Date(newVisitDiscardedImage.timestamp),
									image: {
										...getVisitImageImage(
											newVisitDiscardedImage.visit_image as Parameters<
												typeof getVisitImageImage
											>[0]
										),
										id: newVisitDiscardedImage.visit_image?.id,
										alt: '',
										page: 0,
									} as ImageCarouselImage,
								};
							}

							return oldDiscardedImage;
						})
					);
				}

				return Promise.resolve();
			})
			.filter(Boolean);

		const updateVisitDiscardedImagePromises = $discardedImages
			.map((discardedImage) => {
				const existingDiscardedImage = visitDiscardedImages.find(
					(visitDiscardedImage) => {
						return (
							getImageId(visitDiscardedImage.visit_image?.id) ===
							getImageId(discardedImage.image.id)
						);
					}
				);

				if (
					existingDiscardedImage &&
					+new Date(existingDiscardedImage.timestamp) !==
						discardedImage.timestamp
				) {
					return $updateVisitDiscardedImage.mutateAsync({
						id: existingDiscardedImage.id,
						data: {
							timestamp: dayjs.utc(discardedImage.timestamp).toISOString(),
							visit_image: {
								id: getImageId(discardedImage.image.id),
							},
							visit: {
								id: visitId,
							},
						},
					});
				}

				return null;
			})
			.filter(Boolean);

		const deleteVisitDiscardedImagePromise = visitDiscardedImages
			?.map((visitDiscardedImage) => {
				const existingDiscardedImage = $discardedImages.find(
					(discardedImage) => {
						return (
							getImageId(visitDiscardedImage.visit_image?.id) ===
							getImageId(discardedImage.image.id)
						);
					}
				);

				if (!existingDiscardedImage) {
					return $deleteVisitDiscardedImage.mutateAsync({
						id: visitDiscardedImage?.id,
					});
				}
			})
			.filter(Boolean);

		await Promise.all([
			...createVisitDiscardedImagePromises,
			...updateVisitDiscardedImagePromises,
			...deleteVisitDiscardedImagePromise,
		]);
	};

	const handleFetch = async (shouldSetTotal: boolean) => {
		const visitImagesResponse = await gqlClient.request(
			GetVisitImagesDocument,
			{
				limit: LIMIT,
				offset,
				sort: [
					'visit_phone_registration.image_date',
					'image_taken_date',
					'original_uncropped_image.id',
					'-source',
				],
				filter: {
					_and: [
						...(importedVisitImagesId.length
							? [
									{
										id: {
											_nin: importedVisitImagesId.map((id) => getImageId(id)),
										},
									},
								]
							: []),
						{ visit: { id: { _eq: visitId } } },
						{ photographer: { id: { _eq: photographerId } } },
						{ image_taken_date: { _gte: dayjs.utc(date).toISOString() } },
						{
							image_taken_date: {
								_lt: dayjs.utc(date).add(1, 'day').toISOString(),
							},
						},
						{
							_or: [
								{
									perspective_cropped_image_without_dimensions: {
										id: { _nnull: true },
									},
								},
								{
									rectangular_cropped_image: {
										id: { _nnull: true },
									},
								},
							],
						},
					],
				},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		if (shouldSetTotal) {
			total =
				visitImagesResponse?.Visit_Image_aggregated?.[0]?.countDistinct?.id;
		}

		offset = offset + LIMIT > (total || 0) ? total || 0 : offset + LIMIT;

		const artworklessVisitImages = visitImagesResponse?.Visit_Image?.filter(
			(visitImage) =>
				!visitImage?.visit_artwork?.id &&
				!visitDiscardedImages?.find(
					(visitDiscardedImage) =>
						visitDiscardedImage.visit_image?.id === visitImage.id
				)
		);

		artworkImages.set([
			...$artworkImages,
			...(artworklessVisitImages
				.map((artworklessVisitImage) => {
					const image = getVisitImageImage(artworklessVisitImage);

					if (!image) {
						return null;
					}

					return {
						images: [
							{
								...image,
								id: artworklessVisitImage.id,
								image_taken_date: artworklessVisitImage.image_taken_date,
								alt: '',
								page: 0,
							},
						],
					};
				})
				.filter(Boolean) as ArtworkImages[]),
		]);

		artworkLabelImages.set([
			...$artworkLabelImages,
			...Array(artworklessVisitImages.length).fill({
				images: [],
			}),
		]);

		artworkImagesEmpty.set([
			...$artworkImagesEmpty,
			...Array(artworklessVisitImages.length).fill(false),
		]);

		artworkInfo.set([
			...$artworkInfo,
			...Array(artworklessVisitImages.length).fill({
				notes: '',
				install: false,
			}),
		]);

		Array(artworklessVisitImages.length)
			.fill(null)
			.forEach(() => {
				artworkGalleries.push([
					{
						value: writable(''),
						selectedOption: null,
					},
				]);
			});

		artworkLabelImagesTextareas.set([
			...$artworkLabelImagesTextareas,
			...Array(artworklessVisitImages.length).fill(null),
		]);

		return artworklessVisitImages?.length;
	};

	const handleClickFetch = async (shouldSetTotal: boolean) => {
		fetching = true;
		await handleFetch(shouldSetTotal);
		if (!shouldSetTotal) {
			fetching = false;
		}
	};

	const createVisitArtworkMutation = createMutation(
		getMutation(
			CreateVisitArtworkDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updateVisitImageMutation = createMutation(
		getMutation(
			UpdateVisitImageDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const handleSubmitRow = async (rowIndex: number) => {
		let visitArtworkId: string | undefined;

		const isInstallationShot = $artworkInfo[rowIndex].install;

		const artworkImagePayload = await (async () => {
			const artworkImage = $artworkImages[rowIndex].images[0];
			if (!artworkImage) {
				return null;
			}

			if (artworkImage.file) {
				const uploadFileResponse = await uploadFile(
					artworkImage.file,
					getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					)
				);
				return {
					original_uncropped_image: uploadFileResponse,
					rectangular_cropped_image: uploadFileResponse,
					status: { key: 'CROPPED' },
					image_taken_date: dayjs.utc(date).toISOString(),
					crop_type: 'rectangular_crop',
					visit: {
						id: visitId,
					},
					photographer: photographerId,

					image_content_type: isInstallationShot
						? 'installation_shot'
						: 'artwork',
				};
			}

			if (!artworkImage.url.startsWith('/api')) {
				const uplodedImageResponse = await fetch('/api/upload-file-from-url', {
					method: 'POST',
					headers: getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					body: JSON.stringify({ url: artworkImage.url }),
				});

				const uploadFileResponse = await uplodedImageResponse.json();
				return {
					original_uncropped_image: uploadFileResponse,
					rectangular_cropped_image: uploadFileResponse,
					status: { key: 'CROPPED' },
					image_taken_date: dayjs.utc(date).toISOString(),
					crop_type: 'rectangular_crop',
					visit: {
						id: visitId,
					},
					photographer: photographerId,
					image_content_type: isInstallationShot
						? 'installation_shot'
						: 'artwork',
				};
			}

			return {
				id: getImageId(artworkImage.id),
				image_taken_date: dayjs
					.utc(
						(artworkImage as unknown as { image_taken_date: string })
							.image_taken_date
					)
					.toISOString(),
				image_content_type: isInstallationShot
					? 'installation_shot'
					: 'artwork',
			};
		})();

		const fairExhibitorPayload = await (async () => {
			const fairExhibitor = artworkGalleries[rowIndex][0].selectedOption;

			if (!fairExhibitor) {
				return null;
			}

			const fairExhibitorResponse = await gqlClient.request(
				GetProcessedOrganisationDocument,
				{
					filter: {
						_and: [
							{
								name: {
									_eq: `${fairExhibitor?.line1}`,
								},
								entity: { _eq: `${fairExhibitor?.line3}` },
							},
						],
					},
				},
				getAuthorizationHeaders(page.data as { user: { access_token: string } })
			);

			const processedFairExhibitor =
				fairExhibitorResponse?.Processed_Organisation?.[0];

			if (processedFairExhibitor) {
				return {
					id: processedFairExhibitor.id,
					name: processedFairExhibitor.name,
				};
			}

			return {
				id: `${fairExhibitor?.line6}`,
				entity: `${fairExhibitor?.line3}`,
				name: `${fairExhibitor?.line1}`,
				location: fairExhibitor?.line5,
				type: fairExhibitor?.line4,
			};
		})();

		if (!isInstallationShot) {
			const labelImagePayload = await (async () => {
				const labelImage = $artworkLabelImages[rowIndex].images[0];
				if (!labelImage) {
					return null;
				}

				if (labelImage.file) {
					const uploadFileResponse = await uploadFile(
						labelImage.file,
						getAuthorizationHeaders(
							page.data as Parameters<typeof getAuthorizationHeaders>[0]
						)
					);

					return {
						original_uncropped_image: uploadFileResponse,
						rectangular_cropped_image: uploadFileResponse,
						status: { key: 'CROPPED' },
						image_taken_date: dayjs.utc(date).toISOString(),
						crop_type: 'rectangular_crop',
						image_content_type: 'label',
						visit: {
							id: visitId,
						},
						photographer: photographerId,
					};
				}

				if (!labelImage.url.startsWith('/api')) {
					const uplodedImageResponse = await fetch(
						'/api/upload-file-from-url',
						{
							method: 'POST',
							headers: getAuthorizationHeaders(
								page.data as { user: { access_token: string } }
							),
							body: JSON.stringify({ url: labelImage.url }),
						}
					);

					const uploadFileResponse = await uplodedImageResponse.json();
					return {
						original_uncropped_image: uploadFileResponse,
						rectangular_cropped_image: uploadFileResponse,
						status: { key: 'CROPPED' },
						image_taken_date: dayjs.utc(date).toISOString(),
						crop_type: 'rectangular_crop',
						visit: {
							id: visitId,
						},
						photographer: photographerId,
						image_content_type: 'label',
					};
				}

				return {
					id: getImageId(labelImage.id),
					image_taken_date: dayjs
						.utc(
							(labelImage as unknown as { image_taken_date: string })
								.image_taken_date
						)
						.toISOString(),
					image_content_type: 'label',
				};
			})();

			// Creates the artwork
			const createVisitArtworkResponse =
				await $createVisitArtworkMutation.mutateAsync({
					data: {
						is_installation_shot: false,
						empty_artwork_image: $artworkImagesEmpty[rowIndex],
						...(artworkImagePayload && {
							artwork_image: artworkImagePayload,
						}),
						...(labelImagePayload && {
							label_image: labelImagePayload,
						}),
						...($artworkLabelImagesTextareas[rowIndex] && {
							label_text: $artworkLabelImagesTextareas[rowIndex],
						}),
						...(artworkGalleries[rowIndex][0].selectedOption && {
							processed_fair_exhibitor_org: fairExhibitorPayload,
						}),
						...(labelImagePayload
							? {
									status: {
										key: 'AWAITING_OCR_EXTRACTION',
										name: 'Awaiting OCR Extraction',
									},
								}
							: {
									status: {
										key: 'AWAITING_LABEL_PARSER',
										name: 'Awaiting Label Parser',
									},
								}),
						artwork_details: {
							...($artworkLabelImagesTextareas[rowIndex] && {
								description: $artworkLabelImagesTextareas[rowIndex],
							}),
							ingestion_notes: $artworkInfo[rowIndex].notes,
						},
						visit: {
							id: visitId,
						},
					},
				});

			const createdVisitArtwork =
				createVisitArtworkResponse?.create_Visit_Artwork_item;

			visitArtworkId = createdVisitArtwork?.id;

			// Updates the artwork visit image and the label visit image (if applicable)
			const updateVisitImagePromises = [
				createdVisitArtwork?.artwork_image?.id,
				createdVisitArtwork?.label_image?.id,
			]
				.filter(Boolean)
				.map((visitImageId) =>
					$updateVisitImageMutation.mutateAsync({
						id: getImageId(visitImageId) as string,
						data: {
							visit_artwork: {
								id: visitArtworkId,
							},
						},
					})
				);

			await Promise.all(updateVisitImagePromises);
		} else {
			// Creates the artwork
			const createVisitArtworkResponse =
				await $createVisitArtworkMutation.mutateAsync({
					data: {
						is_installation_shot: true,
						...(artworkImagePayload && {
							artwork_image: artworkImagePayload,
						}),
						...(artworkGalleries[rowIndex][0].selectedOption && {
							fair_exhibitor: fairExhibitorPayload,
						}),
						status: {
							key: 'REVIEWED_AND_SUBMITTED',
							name: 'Reviewed And Submitted',
						},
						visit: {
							id: visitId,
						},
					},
				});

			visitArtworkId =
				createVisitArtworkResponse?.create_Visit_Artwork_item?.id;

			await $updateVisitImageMutation.mutateAsync({
				id: getImageId($artworkImages[rowIndex].images[0].id),
				data: {
					visit_artwork: {
						id: visitArtworkId,
					},
				},
			});
		}

		deleteArtwork(rowIndex, false);
		artworkGalleries.splice(rowIndex, 1);
		return Promise.resolve();
	};

	const handleClickSaveDiscardedImages = async () => {
		submittingDiscardedImages = true;
		await saveDiscardedImages();
		await fetchDiscardedImages();
		submittingDiscardedImages = false;

		showToast({
			variant: 'success',
			message: 'Discarded images have been successfully saved',
		});
	};

	const handleClickSubmit = async () => {
		submitting = true;

		const validRowIndexes = $artworkLabelImagesTextareas.reduce(
			(accumulator: number[], _, index) => {
				if (
					isRowValid(
						index,
						artworkGalleries[index],
						$artworkImages,
						$artworkLabelImages,
						$artworkLabelImagesTextareas,
						$artworkInfo,
						$artworkImagesEmpty
					)
				) {
					return [...accumulator, index];
				}

				return accumulator;
			},
			[]
		);

		for (let i = validRowIndexes.length - 1; i >= 0; i--) {
			await handleSubmitRow(validRowIndexes[i]);
		}

		await saveDiscardedImages();
		await fetchDiscardedImages();

		showToast({
			variant: 'success',
			message: 'Artworks and discarded images have been successfully saved',
		});

		submitting = false;
	};

	onMount(() => {
		const initialLoad = async () => {
			fetching = true;

			await fetchDiscardedImages();
			await handleClickFetch(true);

			discardedImages.set(
				visitDiscardedImages?.map(
					(visitDiscardImage) =>
						({
							timestamp: +new Date(visitDiscardImage.timestamp),
							image: {
								...getVisitImageImage(
									visitDiscardImage.visit_image as Parameters<
										typeof getVisitImageImage
									>[0]
								),
								image_taken_date:
									visitDiscardImage.visit_image?.image_taken_date,
								id: visitDiscardImage.visit_image?.id,
								alt: '',
								page: 0,
							},
						}) as { timestamp: number; image: ImageCarouselImage }
				)
			);

			fetching = false;
		};

		initialLoad();
	});
</script>

<div
	class="fixed bottom-0 left-0 z-20 w-full border-t border-gray-200 bg-gray-0 py-4"
>
	<Container
		dataCy={`${dataCy}-save`}
		class="flex items-center justify-end gap-2"
	>
		{#if offset === total}
			<Txt class="mr-2 text-gray-500" variant="body2"
				>All images have been loaded</Txt
			>
		{/if}

		<Button
			dataCy={`${dataCy}-save-discarded-images`}
			size="md"
			variant="secondary"
			loading={submittingDiscardedImages}
			disabled={submittingDiscardedImages || submitting || fetching}
			onclick={handleClickSaveDiscardedImages}>save discarded images</Button
		>

		<Button
			dataCy={`${dataCy}-fetch`}
			size="md"
			loading={fetching}
			disabled={submittingDiscardedImages ||
				submitting ||
				fetching ||
				offset === total}
			onclick={() => handleClickFetch(false)}>fetch more images</Button
		>

		<Button
			dataCy={`${dataCy}-save-submit`}
			size="md"
			loading={submitting}
			disabled={submittingDiscardedImages ||
				submitting ||
				fetching ||
				!isOneRowValid}
			onclick={handleClickSubmit}>submit valid entries</Button
		>
	</Container>
</div>
