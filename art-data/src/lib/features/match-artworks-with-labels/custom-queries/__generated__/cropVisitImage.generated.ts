import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type CropVisitImageMutationVariables = Types.Exact<{
	visitImageId: Types.Scalars['ID']['input'];
}>;

export type CropVisitImageMutation = {
	__typename?: 'Mutation';
	cropVisitImage: boolean;
};

export const CropVisitImageDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'cropVisitImage' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'visitImageId' },
					},
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'cropVisitImage' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'visitImageId' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'visitImageId' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CropVisitImageMutation,
	CropVisitImageMutationVariables
>;
