import { type Galleries } from '../../../gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
import { page } from '$app/state';
import { Routes } from '$lib/constants/routes';
import { type ArtworkLabelInfo, type ArtworkImages } from '$lib/types';

export const isRowValid = (
	index: number,
	galleries: Galleries,
	artworkImages: ArtworkImages[],
	artworkLabelImages: ArtworkImages[],
	artworkLabelImagesTextareas: (string | null)[],
	artworkInfo: ArtworkLabelInfo[],
	artworkImagesEmpty: boolean[]
) => {
	const isOnFairs = page.url.pathname.startsWith(Routes.FairsHome);

	if (artworkInfo[index]?.install) {
		return (
			artworkImages[index]?.images?.length &&
			(isOnFairs ? galleries[0].selectedOption : true)
		);
	}

	return (
		(artworkImages[index]?.images?.length || artworkImagesEmpty[index]) &&
		(artworkLabelImagesTextareas[index] ||
			artworkLabelImages[index]?.images?.length) &&
		(isOnFairs ? galleries[0].selectedOption : true)
	);
};
