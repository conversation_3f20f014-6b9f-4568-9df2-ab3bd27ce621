import { ImageCarouselWithMenuType } from '../../components/MatchArtworksWithLabels/MatchArtworksWithLabelsTable/MatchArtworksWithLabelsTableRow/ImageCarouselWithMenu';
import { type ArtworkImages } from '$lib/types';

export const getCarouselImages = (
	index: number,
	type: ImageCarouselWithMenuType,
	artworkImages: ArtworkImages[],
	artworkLabelImages: ArtworkImages[]
) => {
	return type === ImageCarouselWithMenuType.Artwork
		? artworkImages[index]?.images
		: artworkLabelImages[index]?.images;
};
