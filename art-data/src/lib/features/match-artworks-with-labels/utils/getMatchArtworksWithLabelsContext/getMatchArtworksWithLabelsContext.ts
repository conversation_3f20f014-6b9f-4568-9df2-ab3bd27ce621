import { getContext } from 'svelte';
import { type createMatchArtworksWithLabelsContext } from '../createMatchArtworksWithLabelsContext/createMatchArtworksWithLabelsContext';
import { Contexts } from '$lib/constants/contexts';

export const getMatchArtworksWithLabelsContext = () => {
	return getContext<ReturnType<typeof createMatchArtworksWithLabelsContext>>(
		Contexts.MatchArtworksWithLabels
	);
};
