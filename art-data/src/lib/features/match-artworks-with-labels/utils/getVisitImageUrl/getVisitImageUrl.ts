import type { GetVisitImagesQuery } from '$lib/queries/__generated__/getVisitImages.generated';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const getVisitImageImage = (
	visitImage: GetVisitImagesQuery['Visit_Image'][number]
) => {
	if (
		visitImage.perspective_cropped_image_without_dimensions &&
		visitImage.crop_type === 'perspective_crop'
	) {
		return {
			height:
				visitImage.perspective_cropped_image_without_dimensions.height || 1,
			width: visitImage.perspective_cropped_image_without_dimensions.width || 1,
			url: getImageUrl(
				visitImage.perspective_cropped_image_without_dimensions.id
			) as string,
			filename_disk: visitImage.perspective_cropped_image_without_dimensions
				.filename_disk as string,
		};
	}

	if (
		visitImage.rectangular_cropped_image &&
		visitImage.crop_type === 'rectangular_crop'
	) {
		return {
			height: visitImage.rectangular_cropped_image.height || 1,
			width: visitImage.rectangular_cropped_image.width || 1,
			url: getImageUrl(visitImage.rectangular_cropped_image.id) as string,
			filename_disk: visitImage.rectangular_cropped_image
				.filename_disk as string,
		};
	}

	return null;
};
