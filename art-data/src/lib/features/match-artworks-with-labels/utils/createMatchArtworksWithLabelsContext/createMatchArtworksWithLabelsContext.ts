import { writable, get, type Writable } from 'svelte/store';
import { ImageCarouselWithMenuType } from '../../components/MatchArtworksWithLabels/MatchArtworksWithLabelsTable/MatchArtworksWithLabelsTableRow/ImageCarouselWithMenu';
import { getIdSuffix } from '../getIdSuffix/getIdSuffix';
import type { ImageCarouselImage } from '$global/components/ImageCarousel';
import { showToast } from '$global/components/Toasts';
import { getImageDimensionsFromUrl } from '$global/utils/getImageDimensionsFromUrl/getImageDimensionsFromUrl';
import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
import { getImageAlt } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ArtworksTextTableRow';
import { getImageId } from '$lib/features/view-all-images/utils/getInitialVariables/getImageId';
import type { ArtworkImages, ArtworkLabelInfo } from '$lib/types';

export const createMatchArtworksWithLabelsContext = () => {
	const artworkImagesEmpty = writable<boolean[]>([]);
	const artworkImages = writable<ArtworkImages[]>([]);
	const artworkLabelImages = writable<ArtworkImages[]>([]);
	const artworkLabelImagesTextareas = writable<(string | null)[]>([]);
	const discardedImages = writable<
		{
			timestamp: number;
			image: ImageCarouselImage;
		}[]
	>([]);
	const artworkInfo = writable<ArtworkLabelInfo[]>([]);
	const cursorCoords: Writable<null | [number, number]> = writable(null);

	const copiedData: Writable<null | {
		content: ArtworkImages | string;
		cut: boolean;
		coords: [number, number];
		fromList: string;
	}> = writable(null);

	const copyCurrentCellData = (cut: boolean) => {
		const coords = get(cursorCoords);
		if (!coords) {
			return '';
		}

		const xCoord = coords[0];
		const yCoord = coords[1];

		const textareaToCopy = get(artworkLabelImagesTextareas)[yCoord];
		const fromList = getIdSuffix(
			yCoord,
			xCoord
				? ImageCarouselWithMenuType.Label
				: ImageCarouselWithMenuType.Artwork
		);

		document.getElementById(`image-carousel-textarea-${fromList}`)?.blur();

		if (xCoord === 1 && textareaToCopy !== null) {
			copiedData.set({
				content: textareaToCopy,
				cut,
				coords: coords,
				fromList,
			});
		} else {
			copiedData.set({
				content: get(xCoord ? artworkLabelImages : artworkImages)[yCoord],
				cut,
				coords: coords,
				fromList,
			});
		}
	};

	const getIdSuffixFromCoords = (coords: [number, number]) => {
		return getIdSuffix(
			coords[1],
			coords[0]
				? ImageCarouselWithMenuType.Label
				: ImageCarouselWithMenuType.Artwork
		);
	};

	const pasteCopiedData = () => {
		const coords = get(cursorCoords);
		if (!coords) {
			return;
		}

		const xCoord = coords[0];
		const yCoord = coords[1];

		if (xCoord === 1 && get(artworkLabelImagesTextareas)[yCoord] !== null) {
			return;
		}

		if (
			get(xCoord ? artworkLabelImages : artworkImages)[yCoord].images.length
		) {
			return;
		}

		const dataToCopy = get(copiedData);
		if (!dataToCopy) {
			showToast({
				variant: 'error',
				message: 'Nothing has been copied',
			});
			return;
		}

		const contentToCopy = dataToCopy.content;
		const shouldDeleteOriginalEntry = dataToCopy.cut;

		const toList = getIdSuffixFromCoords(coords);

		if (dataToCopy.fromList === toList) {
			return;
		}

		if (typeof contentToCopy === 'string') {
			if (xCoord === 0) {
				showToast({
					variant: 'error',
					message: 'You cannot copy a manual label in the artwork image column',
				});
				return;
			}

			document
				.getElementById(`image-carousel-textarea-${dataToCopy.fromList}`)
				?.blur();

			setArtworkLabelImagesTextarea(contentToCopy, yCoord);
			if (shouldDeleteOriginalEntry) {
				setArtworkLabelImagesTextarea(null, yCoord);
			}
		} else {
			if (shouldDeleteOriginalEntry) {
				handleDragImage(
					contentToCopy.images[0],
					0,
					dataToCopy.fromList,
					toList
				);
			} else {
				handleDuplicateImage(contentToCopy.images[0], 0, toList);
			}
		}

		copiedData.set(null);
	};

	const setCursorCoords = (newCoords: null | [number, number]) => {
		if (
			!newCoords ||
			newCoords[0] >= 0 ||
			newCoords[0] <= 1 ||
			newCoords[1] >= 0 ||
			newCoords[1] < get(artworkImages).length
		) {
			cursorCoords.set(newCoords);
		}
	};

	const findNextCell = (): [number, number] => {
		const coords = get(cursorCoords);
		if (!coords) {
			return [0, 0];
		}

		const xCoord = coords[0];
		const yCoord = coords[1];

		if (xCoord === 0) {
			return [1, yCoord];
		} else {
			if (yCoord + 1 >= get(artworkImages).length) {
				return [0, 0];
			} else {
				return [0, yCoord + 1];
			}
		}
	};

	const findPreviousCell = (): [number, number] => {
		const coords = get(cursorCoords);
		if (!coords) {
			return [0, 0];
		}

		const xCoord = coords[0];
		const yCoord = coords[1];

		if (xCoord === 0) {
			if (yCoord - 1 >= 0) {
				return [1, yCoord - 1];
			} else {
				return [1, get(artworkImages).length - 1];
			}
		} else {
			return [0, yCoord];
		}
	};

	const moveCursorToNextRow = () => {
		const coords = get(cursorCoords);
		if (!coords) {
			setCursorCoords([0, 0]);
			return;
		}

		const xCoord = coords[0];
		const yCoord = coords[1];
		if (yCoord + 1 >= get(artworkImages).length) {
			setCursorCoords([xCoord, 0]);
		} else {
			setCursorCoords([xCoord, yCoord + 1]);
		}
	};

	const moveCursorToPreviousRow = () => {
		const coords = get(cursorCoords);
		if (!coords) {
			setCursorCoords([0, 0]);
			return;
		}

		const xCoord = coords[0];
		const yCoord = coords[1];
		if (yCoord - 1 < 0) {
			setCursorCoords([xCoord, get(artworkImages).length - 1]);
		} else {
			setCursorCoords([xCoord, yCoord - 1]);
		}
	};

	const moveCursorToPreviousCell = () => {
		setCursorCoords(findPreviousCell());
	};

	const moveCursorToNextCell = () => {
		setCursorCoords(findNextCell());
	};

	const getNewArtwork = () => ({
		info: {
			notes: '',
			install: false,
		} as ArtworkLabelInfo,
		images: { images: [], files: [], page: undefined } as ArtworkImages,
		imagesLabels: { images: [], files: [], page: undefined } as ArtworkImages,
		imageLabelTextarea: null,
		imagesEmpty: false,
	});

	const addArtwork = (index: number) => {
		const newArtwork = getNewArtwork();

		artworkInfo.set([
			...get(artworkInfo).slice(0, index + 1),
			newArtwork.info,
			...get(artworkInfo).slice(index + 1),
		]);

		artworkImages.set([
			...get(artworkImages).slice(0, index + 1),
			newArtwork.images,
			...get(artworkImages).slice(index + 1),
		]);

		artworkLabelImages.set([
			...get(artworkLabelImages).slice(0, index + 1),
			newArtwork.imagesLabels,
			...get(artworkLabelImages).slice(index + 1),
		]);

		artworkImagesEmpty.set([
			...get(artworkImagesEmpty).slice(0, index + 1),
			newArtwork.imagesEmpty,
			...get(artworkImagesEmpty).slice(index + 1),
		]);

		artworkLabelImagesTextareas.set([
			...get(artworkLabelImagesTextareas).slice(0, index + 1),
			newArtwork.imageLabelTextarea,
			...get(artworkLabelImagesTextareas).slice(index + 1),
		]);

		// If a cursor is shown, we need to offset the corresponding coords
		const coords = get(cursorCoords);
		if (coords) {
			if (index < coords?.[1]) {
				cursorCoords.set([coords[0], coords[1] + 1]);
			}
		}

		// If data is copied, we need to offset the corresponding coords
		const dataToCopy = get(copiedData);
		if (dataToCopy && index < dataToCopy.coords[1]) {
			const newCoords: [number, number] = [
				dataToCopy.coords[0],
				dataToCopy.coords[1] + 1,
			];

			copiedData.set({
				...dataToCopy,
				coords: newCoords,
				fromList: getIdSuffixFromCoords(newCoords),
			});
		}
	};

	const deleteArtwork = (index: number, shouldDiscardImages: boolean) => {
		// const newArtwork = getNewArtwork();

		const bin = document.getElementById('match-artworks-with-labels-bin');
		bin?.prepend(
			document.getElementById(`image-carousel-${index}-label`) as HTMLElement
		);
		bin?.prepend(
			document.getElementById(`image-carousel-${index}-artwork`) as HTMLElement
		);

		// if (get(artworkInfo).length === 1) {
		// 	artworkInfo.set([newArtwork.info]);
		// 	artworkImages.set([newArtwork.images]);
		// 	artworkLabelImages.set([newArtwork.imagesLabels]);
		// 	artworkImagesEmpty.set([newArtwork.imagesEmpty]);
		// 	artworkLabelImagesTextareas.set([newArtwork.imageLabelTextarea]);
		// 	cursorCoords.set(null);
		// 	copiedData.set(null);
		// } else {

		if (shouldDiscardImages) {
			discardedImages.set([
				...get(discardedImages),
				...get(artworkImages)[index].images.map((image) => ({
					image,
					timestamp: +new Date(),
				})),
				...get(artworkLabelImages)[index].images.map((image) => ({
					image,
					timestamp: +new Date(),
				})),
			]);
		}

		// replaceArtworkImages({ images: [], page: [0] }, index);
		// replaceArtworkLabelImages({ images: [], page: [0] }, index);

		artworkInfo.set(
			get(artworkInfo).filter(
				(_, artworkInfoIndex) => artworkInfoIndex !== index
			)
		);

		artworkImages.set(
			get(artworkImages).filter(
				(_, artwornImagesIndex) => artwornImagesIndex !== index
			)
		);

		artworkLabelImages.set(
			get(artworkLabelImages).filter(
				(_, artwornLabelsImagesIndex) => artwornLabelsImagesIndex !== index
			)
		);

		artworkImagesEmpty.set(
			get(artworkImagesEmpty).filter(
				(_, artworkImagesEmptyIndex) => artworkImagesEmptyIndex !== index
			)
		);

		artworkLabelImagesTextareas.set(
			get(artworkLabelImagesTextareas).filter(
				(_, artwornLabelsImagesIndex) => artwornLabelsImagesIndex !== index
			)
		);

		// If a cursor is shown, we need to offset the corresponding coords
		const coords = get(cursorCoords);
		if (coords) {
			if (index < coords?.[1]) {
				cursorCoords.set([coords[0], coords[1] - 1]);
			} else if (index === coords?.[1]) {
				cursorCoords.set(null);
			}
		}

		// If data is copied, we need to offset the corresponding coords
		const dataToCopy = get(copiedData);
		if (dataToCopy && index < dataToCopy.coords[1]) {
			const newCoords: [number, number] = [
				dataToCopy.coords[0],
				dataToCopy.coords[1] - 1,
			];

			copiedData.set({
				...dataToCopy,
				coords: newCoords,
				fromList: getIdSuffixFromCoords(newCoords),
			});
		} else if (dataToCopy && index === dataToCopy.coords[1]) {
			copiedData.set(null);
		}
	};

	const handlePostDeletion = (index: number) => {
		// Delete the row if artwork and label are empty
		if (
			!get(artworkImages)[index].images.length &&
			!get(artworkLabelImages)[index].images.length &&
			get(artworkLabelImagesTextareas)[index] === null &&
			!get(artworkImagesEmpty)[index]
		) {
			deleteArtwork(index, true);
			return true;
		}

		return false;
	};

	const replaceArtworkInfo = (
		newArtworkInfo: ArtworkLabelInfo,
		index: number
	) => {
		artworkInfo.set([
			...get(artworkInfo).slice(0, index),
			newArtworkInfo,
			...get(artworkInfo).slice(index + 1, get(artworkInfo).length),
		]);
	};

	const setArtworkInstallShot = (install: boolean, index: number) => {
		const updatedArtworkInfo = {
			...get(artworkInfo)[index],
			install,
		};

		replaceArtworkInfo(updatedArtworkInfo, index);
	};

	const setArtworkLabelImagesTextarea = (
		value: string | null,
		index: number
	) => {
		artworkLabelImagesTextareas.set(
			get(artworkLabelImagesTextareas).map((artworkLabelImagesTextarea, i) =>
				i === index ? value : artworkLabelImagesTextarea
			)
		);
	};

	const setArtworkImagesEmpty = (value: boolean, index: number) => {
		artworkImagesEmpty.set(
			get(artworkImagesEmpty).map((artworkImageEmpty, i) =>
				i === index ? value : artworkImageEmpty
			)
		);
	};

	const setArtworkNotes = (notes: string, index: number) => {
		const updatedArtworkInfo = {
			...get(artworkInfo)[index],
			notes,
		};

		replaceArtworkInfo(updatedArtworkInfo, index);
	};

	const replaceArtworkImages = (
		newArtworkImages: ArtworkImages,
		index: number
	) => {
		artworkImages.set([
			...get(artworkImages).slice(0, index),
			newArtworkImages,
			...get(artworkImages).slice(index + 1, get(artworkImages).length),
		]);
	};

	const replaceArtworkLabelImages = (
		newArtworkLabelsImages: ArtworkImages,
		index: number
	) => {
		artworkLabelImages.set([
			...get(artworkLabelImages).slice(0, index),
			newArtworkLabelsImages,
			...get(artworkLabelImages).slice(
				index + 1,
				get(artworkLabelImages).length
			),
		]);
	};

	const handleDragFromExternalSource = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		toList: string
	) => {
		const [toListIndex, toListType] = toList.split('-');
		const toArtworkImages = get(
			toListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+toListIndex];

		const imageToAdd = {
			...movedImage,
			id: `${getImageId(movedImage.id)}_${+new Date()}`,
		};

		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, imageToAdd);
		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
		};

		discardedImages.set(
			get(discardedImages).filter(
				(discardedImage) =>
					getImageId(discardedImage.image.id) !== getImageId(imageToAdd.id)
			)
		);

		if (toListType === ImageCarouselWithMenuType.Artwork) {
			replaceArtworkImages(updatedToArtworkImages, +toListIndex);
		} else {
			replaceArtworkLabelImages(updatedToArtworkImages, +toListIndex);
		}
	};

	// Could be simplified in order to only empty the from list and populate the destination list
	// But this implementation can support multiple images in the same list if required
	const handleDragImage = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		fromList: string,
		toList: string
	) => {
		const [fromListIndex, fromListType] = fromList.split('-');
		const fromArtworkImages = get(
			fromListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+fromListIndex];

		const newFromImages = [
			...fromArtworkImages.images.filter((image) => image.id !== movedImage.id),
		];

		const updatedFromArtworkImages = {
			...fromArtworkImages,
			images: [...newFromImages],
		};

		const [toListIndex, toListType] = toList.split('-');
		const toArtworkImages = get(
			toListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+toListIndex];

		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, movedImage);

		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
		};

		if (toListType === fromListType) {
			const storeToUpdate =
				toListType === ImageCarouselWithMenuType.Artwork
					? artworkImages
					: artworkLabelImages;

			if (+fromListIndex < +toListIndex) {
				storeToUpdate.set([
					...get(storeToUpdate).slice(0, +fromListIndex),
					updatedFromArtworkImages,
					...get(storeToUpdate).slice(+fromListIndex + 1, +toListIndex),
					updatedToArtworkImages,
					...get(storeToUpdate).slice(
						+toListIndex + 1,
						get(storeToUpdate).length
					),
				]);
			} else {
				storeToUpdate.set([
					...get(storeToUpdate).slice(0, +toListIndex),
					updatedToArtworkImages,
					...get(storeToUpdate).slice(+toListIndex + 1, +fromListIndex),
					updatedFromArtworkImages,
					...get(storeToUpdate).slice(
						+fromListIndex + 1,
						get(storeToUpdate).length
					),
				]);
			}
		} else {
			if (fromListType === ImageCarouselWithMenuType.Artwork) {
				replaceArtworkImages(updatedFromArtworkImages, +fromListIndex);
			} else {
				replaceArtworkLabelImages(updatedFromArtworkImages, +fromListIndex);
			}

			if (toListType === ImageCarouselWithMenuType.Artwork) {
				replaceArtworkImages(updatedToArtworkImages, +toListIndex);
			} else {
				replaceArtworkLabelImages(updatedToArtworkImages, +toListIndex);
			}
		}

		handlePostDeletion(+fromListIndex);
		handlePostDeletion(+toListIndex);

		// If the copied data is dragged, we need to update its coordinates in the grid
		const dataToCopy = get(copiedData);
		if (dataToCopy?.fromList === fromList) {
			copiedData.set({
				...dataToCopy,
				fromList: toList,
				coords: [
					toListType === ImageCarouselWithMenuType.Artwork ? 0 : 1,
					+toListIndex,
				],
			});
		}
	};

	const handleDuplicateImage = (
		movedImage: ImageCarouselImage,
		newIndex: number,
		toList: string
	) => {
		const [toListIndex, toListType] = toList.split('-');
		const toArtworkImages = get(
			toListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+toListIndex];

		const toArtworkImagesImages = [...toArtworkImages.images];
		toArtworkImagesImages.splice(newIndex, 0, {
			...movedImage,
			id: `${getImageId(movedImage.id)}_${+new Date()}`,
		});

		const updatedToArtworkImages = {
			...toArtworkImages,
			images: [...toArtworkImagesImages],
		};

		if (toListType === ImageCarouselWithMenuType.Artwork) {
			replaceArtworkImages(updatedToArtworkImages, +toListIndex);
		} else {
			replaceArtworkLabelImages(updatedToArtworkImages, +toListIndex);
		}
	};

	const handleDeleteImage = (fromList: string, image: ImageCarouselImage) => {
		const [fromListIndex, fromListType] = fromList.split('-');
		const existingArtworkImages = get(
			fromListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+fromListIndex];

		const newImages = existingArtworkImages.images.filter(
			({ id }) => id !== image['id']
		);

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: newImages,
		};

		const imageToDiscard = {
			...image,
			id: getImageId(image.id),
		};

		if (
			!get(discardedImages).find(
				(discardedImage) => discardedImage.image.id === imageToDiscard.id
			)
		) {
			discardedImages.set([
				...get(discardedImages),
				{ image: imageToDiscard, timestamp: +new Date() },
			]);
		}

		if (fromListType === ImageCarouselWithMenuType.Artwork) {
			replaceArtworkImages(updatedArtworkImages, +fromListIndex);
		} else {
			replaceArtworkLabelImages(updatedArtworkImages, +fromListIndex);
		}

		// If the copied data is deleted, we need to clear the copied data
		const dataToCopy = get(copiedData);
		if (dataToCopy?.fromList === fromList) {
			copiedData.set(null);
		}
	};

	const handleLinkImage = async (
		toList: string,
		url: string,
		filename_disk: string
	) => {
		const { width, height } = await getImageDimensionsFromUrl(url);

		const [toListIndex, toListType] = toList.split('-');
		const newImage = {
			id: `${crypto.randomUUID()}-0000`,
			filename_disk,
			alt: getImageAlt(0),
			url,
			width,
			height,
		};

		const existingArtworkImages = get(
			toListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+toListIndex];

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [
				...existingArtworkImages.images,
				newImage,
			] as ArtworkImages['images'],
		};

		if (toListType === ImageCarouselWithMenuType.Artwork) {
			replaceArtworkImages(updatedArtworkImages, +toListIndex);
		} else {
			replaceArtworkLabelImages(updatedArtworkImages, +toListIndex);
		}
	};

	const handleSubmittedFiles = async (toList: string, files: File[]) => {
		const [toListIndex, toListType] = toList.split('-');
		const newImages = (
			await Promise.all(
				files.map(async (file: File) => imageFileToBase64(file))
			)
		).map((newImage, i) => ({
			...newImage,
			id: `${crypto.randomUUID()}-0000`,
			filename_disk: newImage.file.name,
			alt: getImageAlt(i),
		}));

		const existingArtworkImages = get(
			toListType === ImageCarouselWithMenuType.Artwork
				? artworkImages
				: artworkLabelImages
		)[+toListIndex];

		const updatedArtworkImages = {
			...existingArtworkImages,
			images: [
				...existingArtworkImages.images,
				...newImages,
			] as ArtworkImages['images'],
		};

		if (toListType === ImageCarouselWithMenuType.Artwork) {
			replaceArtworkImages(updatedArtworkImages, +toListIndex);
		} else {
			replaceArtworkLabelImages(updatedArtworkImages, +toListIndex);
		}
	};

	return {
		moveCursorToPreviousCell,
		moveCursorToNextCell,
		cursorCoords,
		artworkImagesEmpty,
		setCursorCoords,
		handleLinkImage,
		handleDuplicateImage,
		handleSubmittedFiles,
		artworkImages,
		artworkLabelImages,
		artworkInfo,
		setArtworkNotes,
		setArtworkInstallShot,
		setArtworkLabelImagesTextarea,
		artworkLabelImagesTextareas,
		setArtworkImagesEmpty,
		handleDragFromExternalSource,
		handleDragImage,
		handleDeleteImage,
		deleteArtwork,
		addArtwork,
		discardedImages,
		moveCursorToNextRow,
		moveCursorToPreviousRow,
		copyCurrentCellData,
		pasteCopiedData,
		copiedData,
		handlePostDeletion,
		replaceArtworkImages,
		replaceArtworkLabelImages,
	};
};
