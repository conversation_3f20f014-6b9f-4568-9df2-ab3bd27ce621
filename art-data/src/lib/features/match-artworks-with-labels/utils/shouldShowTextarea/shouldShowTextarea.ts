import { ImageCarouselWithMenuType } from '../../components/MatchArtworksWithLabels/MatchArtworksWithLabelsTable/MatchArtworksWithLabelsTableRow/ImageCarouselWithMenu';

export const shouldShowTextarea = (
	index: number,
	type: ImageCarouselWithMenuType,
	artworkLabelImagesTextareas: (string | null)[]
) => {
	return (
		type === ImageCarouselWithMenuType.Label &&
		artworkLabelImagesTextareas[index] !== null
	);
};
