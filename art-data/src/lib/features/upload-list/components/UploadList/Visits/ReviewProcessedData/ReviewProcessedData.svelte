<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { Section } from '../types';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { TickIcon } from '$global/assets/icons/TickIcon';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableBody,
		TableRow,
		TableActionCell,
		TableCell,
		TableNoResults,
		TableHeaderRow,
		TableHeader,
		getCellWidth,
	} from '$global/components/Table';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import {
		type UploadListVisitImage,
		type UploadListVisitArtwork,
	} from '$lib/types';

	interface Props {
		event: string;
		dataCy: string;
		visitEntries: {
			images: UploadListVisitImage[];
			artworks: UploadListVisitArtwork[];
			nbImagesCropped: number;
			nbImagesRemaining: number;
			nbArtworksRemaining: number;
			nbArtworksCompleted: number;
			nbArtworksReviewable: number;
			nbArtworksWithError: number;
			nbArtworksIngestionFailed: number;
		}[];
	}

	let { event, dataCy, visitEntries }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-review-processed-data`);

	const actionCellWidth = '12rem';
	const headers = [
		'',
		'Date of Capture',
		'Photographer',
		'No. Artworks',
		'Artworks remaining',
		'',
	];

	const getNbInstallationShots = (visitEntry: {
		images: UploadListVisitImage[];
		artworks: UploadListVisitArtwork[];
		nbImagesCropped: number;
		nbImagesRemaining: number;
		nbArtworksRemaining: number;
		nbArtworksCompleted: number;
		nbArtworksReviewable: number;
		nbArtworksWithError: number;
		nbArtworksIngestionFailed: number;
	}) =>
		visitEntry?.artworks?.filter((artwork) => artwork?.is_installation_shot)
			?.length;

	const formatVisit = (visitEntry: {
		images: UploadListVisitImage[];
		artworks: UploadListVisitArtwork[];
		nbImagesCropped: number;
		nbImagesRemaining: number;
		nbArtworksRemaining: number;
		nbArtworksCompleted: number;
		nbArtworksReviewable: number;
		nbArtworksWithError: number;
		nbArtworksIngestionFailed: number;
	}) => {
		const nbInstallationShot = getNbInstallationShots(visitEntry);

		return [
			dayjs(visitEntry.images?.[0]?.image_taken_date).format('DD/MM/YYYY'),
			[
				visitEntry.images[0]?.photographer?.first_name,
				visitEntry.images[0]?.photographer?.last_name,
			]
				.filter(Boolean)
				.join(' '),
			`${visitEntry?.artworks?.length}${
				nbInstallationShot
					? ` (${nbInstallationShot} installation shot(s))`
					: ''
			}`,
			`${visitEntry?.nbArtworksRemaining}`,
		];
	};

	const LIMIT = 5;
	let maxRows = $state(LIMIT);

	const handleClickLoadMore = () => {
		maxRows = maxRows + LIMIT;
	};
</script>

<div>
	<div class="mb-4 flex justify-between" id={Section.MatchLabels}>
		<div>
			<Txt variant="h6" class="mb-1 text-gray-900">Review processed data</Txt>
			<Txt variant="body2" class="mb-1"
				>The table below contains all of the reviewing tasks for this {event}.</Txt
			>
		</div>
	</div>

	<table class="w-full table-fixed bg-white">
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
					class={!i ? '!w-[2.5rem]' : undefined}
				>
					{#snippet custom()}
						<div
							class={twMerge(
								'flex flex-row items-center gap-1',
								classNames({ hidden: !i })
							)}
						>
							<Txt variant="label3" class="whitespace-nowrap">{header}</Txt>
							{#if header === 'No Artworks'}
								<InfoTooltip
									dataCy={dataCyPrefix}
									content="The total number of artworks matched with a label"
								/>
							{:else if header === 'Artworks remaining'}
								<InfoTooltip
									dataCy={dataCyPrefix}
									content="The remaining number of artworks matched with a label"
								/>
							{/if}
						</div>
					{/snippet}
				</TableHeader>
			{/each}
		</TableHeaderRow>
		{#if visitEntries}
			<TableBody dataCy={dataCyPrefix}>
				{#each visitEntries
					.filter((visitEntry) => visitEntry?.artworks?.length)
					.slice(0, maxRows) as visitEntry, i}
					{@const showError = visitEntry?.nbArtworksIngestionFailed > 0}
					{#if visitEntry?.artworks?.length}
						<TableRow
							index={i}
							dataCy={dataCyPrefix}
							class={classNames({
								'bg-red-100': showError,
							})}
						>
							<TableCell dataCy={dataCyPrefix} width="2.5rem">
								{#snippet custom()}
									{@const nbInstallationShot =
										getNbInstallationShots(visitEntry)}
									{#if showError}
										<InfoTooltip
											class="h-4 w-4 mt-1.5"
											content="Ingestion failed for at least one of the reviewed artworks. Please contact customer support."
											dataCy={`${dataCyPrefix}-error`}
											classes={{
												tooltipClasses: { content: '[&>p]:whitespace-normal' },
											}}
										/>
									{:else if visitEntry.nbArtworksCompleted + nbInstallationShot === visitEntry?.artworks?.length}
										<TickIcon class="h-y w-4" color="green-500" />
									{/if}
								{/snippet}
							</TableCell>
							{@const formattedVisit = formatVisit(visitEntry)}
							{#each formattedVisit as formattedExhibitionValue, j}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
									content={formattedExhibitionValue}
								>
									{formattedExhibitionValue}
								</TableCell>
							{/each}
							<TableActionCell
								dataCy={dataCyPrefix}
								width={actionCellWidth}
								class="[&>div]:flex-col"
							>
								{#if visitEntry?.nbArtworksReviewable}
									<LinkButton
										dataCy={`${dataCyPrefix}-cell`}
										variant="primary"
										size="xs"
										class="flex justify-end"
										href={`${page.url.pathname}/images${
											Routes.ReviewArtworks
										}?photographer=${
											visitEntry?.images?.[0]?.photographer?.id
										}&date=${dayjs(
											visitEntry?.images?.[0]?.image_taken_date
										)?.format?.('YYYY-MM-DD')}`}
									>
										Review
										{#snippet trailing()}
											<ChevronRightIcon class="h-3 w-3" />
										{/snippet}
									</LinkButton>
								{/if}
								{#if visitEntry?.nbArtworksCompleted}
									<Txt
										href={`${page.url.pathname}/images${
											Routes.ViewCompleted
										}?photographer=${
											visitEntry?.images?.[0]?.photographer?.id
										}&date=${dayjs(
											visitEntry?.images?.[0]?.image_taken_date
										)?.format?.('YYYY-MM-DD')}`}
										component="a"
										variant="body3"
										class="mt-1 block text-right text-blue-500 underline"
									>
										View submitted artworks
									</Txt>
								{/if}
							</TableActionCell>
						</TableRow>
					{/if}
				{/each}

				{#if !visitEntries.length}
					<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
						>No visits to display</TableNoResults
					>
				{/if}
			</TableBody>
		{/if}
	</table>

	{#if maxRows < visitEntries.filter((visitEntry) => visitEntry?.artworks?.length).length}
		<div class="mt-6 flex w-full justify-center">
			<Button
				dataCy={`${dataCyPrefix}-load-more`}
				onclick={handleClickLoadMore}
				variant="secondary"
				size="sm">LOAD MORE</Button
			>
		</div>
	{/if}
</div>
