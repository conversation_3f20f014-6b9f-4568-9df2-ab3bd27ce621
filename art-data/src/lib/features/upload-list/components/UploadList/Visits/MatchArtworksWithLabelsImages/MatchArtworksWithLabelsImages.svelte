<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { Section } from '../types';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { TickIcon } from '$global/assets/icons/TickIcon';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableBody,
		TableRow,
		TableActionCell,
		TableCell,
		TableNoResults,
		TableHeaderRow,
		TableHeader,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import {
		type UploadListVisitImage,
		type UploadListVisitArtwork,
	} from '$lib/types';

	interface Props {
		event: string;
		dataCy: string;
		visitEntries: {
			images: UploadListVisitImage[];
			artworks: UploadListVisitArtwork[];
			nbImagesCropped: number;
			nbImagesRemaining: number;
			nbArtworksRemaining: number;
			nbArtworksWithError: number;
		}[];
	}

	let { event, dataCy, visitEntries }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-match-artworks-with-labels`);

	const actionCellWidth = '8rem';
	const headers = [
		'',
		'Date of Capture',
		'Photographer',
		'Total cropped',
		'Images remaining',
		'Artworks created',
		'',
	];

	const formatVisit = (visitEntry: {
		images: UploadListVisitImage[];
		artworks: UploadListVisitArtwork[];
		nbImagesCropped: number;
		nbImagesRemaining: number;
		nbArtworksRemaining: number;
	}) => {
		const nbInstallationShot = visitEntry?.artworks?.filter(
			(artwork) => artwork?.is_installation_shot
		)?.length;

		return [
			dayjs(visitEntry.images?.[0]?.image_taken_date).format('DD/MM/YYYY'),
			[
				visitEntry.images[0]?.photographer?.first_name,
				visitEntry.images[0]?.photographer?.last_name,
			]
				.filter(Boolean)
				.join(' '),
			`${visitEntry?.nbImagesCropped}`,
			`${visitEntry?.nbImagesRemaining}`,
			`${visitEntry?.artworks?.length}${
				nbInstallationShot
					? ` (${nbInstallationShot} installation shot(s))`
					: ''
			}`,
		];
	};

	const LIMIT = 5;
	let maxRows = $state(LIMIT);

	const handleClickLoadMore = () => {
		maxRows = maxRows + LIMIT;
	};
</script>

<div>
	<div class="mb-4 flex justify-between" id={Section.MatchLabels}>
		<div>
			<Txt variant="h6" class="mb-1 text-gray-900">Match labels to artworks</Txt
			>
			<Txt variant="body2" class="mb-1"
				>The table below contains all of the matching tasks for this {event}.
				When an image and a label are matched, they become an artwork.
			</Txt>
		</div>
	</div>

	<table class="w-full table-fixed bg-white">
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
					class={!i ? '!w-[2.5rem]' : undefined}
				>
					{#snippet custom()}
						<div
							class={twMerge(
								'flex flex-row items-center gap-1',
								classNames({ hidden: !i })
							)}
						>
							<Txt variant="label3" class="whitespace-nowrap">{header}</Txt>
							{#if header === 'Images remaining'}
								<InfoTooltip
									dataCy={dataCyPrefix}
									content="This refers to the amount of incomplete rows in the matching table."
								/>
							{:else if header === 'Artworks created'}
								<InfoTooltip
									dataCy={dataCyPrefix}
									content="This refers to the artworks matched with labels."
								/>
							{/if}
						</div>
					{/snippet}
				</TableHeader>
			{/each}
		</TableHeaderRow>
		{#if visitEntries}
			<TableBody dataCy={dataCyPrefix}>
				{#each visitEntries
					.filter((visitEntry) => visitEntry?.nbImagesCropped)
					.slice(0, maxRows) as visitEntry, i}
					{@const showError = visitEntry?.nbArtworksWithError > 1}
					{#if visitEntry?.nbImagesCropped}
						<TableRow
							index={i}
							dataCy={dataCyPrefix}
							class={classNames({
								'bg-red-100': showError,
							})}
						>
							<TableCell dataCy={dataCyPrefix} width="2.5rem">
								{#if showError}
									<InfoTooltip
										class="h-4 w-4 mt-1.5"
										content="Label matching failed for at least one of the created artworks. Please contact customer support."
										dataCy={`${dataCyPrefix}-error`}
										classes={{
											tooltipClasses: { content: '[&>p]:whitespace-normal' },
										}}
									/>
								{:else if !visitEntry?.nbImagesRemaining}
									<TickIcon class="h-y w-4" color="green-500" />
								{/if}
							</TableCell>
							{@const formattedVisit = formatVisit(visitEntry)}
							{#each formattedVisit as formattedExhibitionValue, j}
								<TableCell
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
									content={formattedExhibitionValue}
								>
									{formattedExhibitionValue}
								</TableCell>
							{/each}
							<TableActionCell
								dataCy={dataCyPrefix}
								width={actionCellWidth}
								class="[&>div]:flex-col"
							>
								{#if visitEntry?.nbImagesRemaining}
									<LinkButton
										dataCy={`${dataCyPrefix}-cell`}
										variant="primary"
										size="xs"
										class="flex justify-end"
										href={`${page.url.pathname}${
											Routes.MatchArtworks
										}?photographer=${
											visitEntry?.images?.[0]?.photographer?.id
										}&date=${dayjs(
											visitEntry?.images?.[0]?.image_taken_date
										)?.format?.('YYYY-MM-DD')}`}
									>
										Match
										{#snippet trailing()}
											<ChevronRightIcon class="h-3 w-3" />
										{/snippet}
									</LinkButton>
								{/if}
								{#if visitEntry?.artworks?.length}
									<Txt
										href={`${page.url.pathname}/images${
											Routes.ViewMatches
										}?photographer=${
											visitEntry?.images?.[0]?.photographer?.id
										}&date=${dayjs(
											visitEntry?.images?.[0]?.image_taken_date
										)?.format?.('YYYY-MM-DD')}`}
										component="a"
										variant="body3"
										class="mt-1 block text-right text-blue-500 underline"
									>
										View matches
									</Txt>
								{/if}
							</TableActionCell>
						</TableRow>
					{/if}
				{/each}

				{#if !visitEntries.length}
					<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
						>No visits to display</TableNoResults
					>
				{/if}
			</TableBody>
		{/if}
	</table>

	{#if maxRows < visitEntries.filter((visitEntry) => visitEntry?.nbImagesCropped).length}
		<div class="mt-6 flex w-full justify-center">
			<Button
				dataCy={`${dataCyPrefix}-load-more`}
				onclick={handleClickLoadMore}
				variant="secondary"
				size="sm">LOAD MORE</Button
			>
		</div>
	{/if}
</div>
