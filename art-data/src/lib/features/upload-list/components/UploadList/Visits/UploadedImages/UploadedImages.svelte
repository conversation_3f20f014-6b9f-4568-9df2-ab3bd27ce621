<script lang="ts">
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import { Section } from '../types';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableBody,
		TableRow,
		TableActionCell,
		TableCell,
		TableNoResults,
		TableHeaderRow,
		TableHeader,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import {
		type UploadListVisitArtwork,
		type UploadListVisitImage,
	} from '$lib/types';

	dayjs.extend(utc);

	interface Props {
		event: string;
		dataCy: string;
		visitEntries: {
			images: UploadListVisitImage[];
			artworks: UploadListVisitArtwork[];
		}[];
	}

	let { event, dataCy, visitEntries }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-uploaded-images`);

	const actionCellWidth = '9rem';
	const headers = [
		'',
		'Date of Capture',
		'Photographer',
		'Uploaded by',
		// 'Upload source',
		'No. Upload items',
		'',
	];

	const formatVisitEntry = (visitEntry: { images: UploadListVisitImage[] }) => {
		const userCreated = visitEntry?.images?.[0]?.user_created;

		return [
			dayjs(visitEntry?.images?.[0]?.image_taken_date).format('DD/MM/YYYY'),
			[
				visitEntry.images[0]?.photographer?.first_name,
				visitEntry.images[0]?.photographer?.last_name,
			]
				.filter(Boolean)
				.join(' '),
			[userCreated?.first_name, userCreated?.last_name]
				.filter(Boolean)
				.join(' '),
			// `${visit.visit_images?.[0]?.source}`,
			`${visitEntry?.images?.filter((image) => image?.source !== 'additional_crop')?.length}`,
		];
	};

	const LIMIT = 5;
	let maxRows = $state(LIMIT);

	const handleClickLoadMore = () => {
		maxRows = maxRows + LIMIT;
	};
</script>

<div>
	<div class="mb-4 flex justify-between" id={Section.UploadedImages}>
		<div>
			<Txt variant="h6" class="mb-1 text-gray-900">Uploaded images</Txt>
			<Txt variant="body2" class="mb-1"
				>The table below contains all of the image uploads for this {event}.</Txt
			>
		</div>
		<div class="flex flex-1 items-end justify-end gap-2">
			<LinkButton
				size="md"
				variant="secondary"
				dataCy={`${dataCy}-view-all-images`}
				href={`${page.url.pathname}${Routes.ViewImages}`}
			>
				View all images
			</LinkButton>
			<LinkButton
				size="md"
				variant="secondary"
				dataCy={`${dataCy}-add-icloud-images`}
				href={`${page.url.pathname}${Routes.AddIcloudImages}`}
			>
				Add icloud images
				{#snippet trailing()}
					<PlusIcon />
				{/snippet}
			</LinkButton>
			<LinkButton
				size="md"
				variant="secondary"
				dataCy={`${dataCy}-add-more-images`}
				href={`${page.url.pathname}${Routes.AddMoreImages}`}
			>
				Add manual images
				{#snippet trailing()}
					<PlusIcon />
				{/snippet}
			</LinkButton>
		</div>
	</div>

	<table class="w-full table-fixed bg-white">
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
					class={!i ? '!w-[2.5rem]' : undefined}
				>
					{header}
				</TableHeader>
			{/each}
		</TableHeaderRow>

		<TableBody dataCy={dataCyPrefix}>
			{#each visitEntries.slice(0, maxRows) as visitEntry, i}
				<TableRow index={i} dataCy={dataCyPrefix}>
					<TableCell dataCy={dataCyPrefix} width="2.5rem" />
					{@const formattedVisit = formatVisitEntry(visitEntry)}
					{#each formattedVisit as formattedExhibitionValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							content={formattedExhibitionValue}
						>
							{formattedExhibitionValue}
						</TableCell>
					{/each}
					<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
						<LinkButton
							dataCy={`${dataCyPrefix}-cell`}
							variant="primary"
							size="xs"
							href={`${page.url.pathname}${Routes.ViewImages}?from=${dayjs
								.utc(visitEntry?.images?.[0]?.image_taken_date)
								.format('YYYY-MM-DD')}&to=${dayjs
								.utc(visitEntry?.images?.[0]?.image_taken_date)
								.format('YYYY-MM-DD')}&user=${
								visitEntry?.images?.[0]?.photographer?.id
							}`}
						>
							View images
							{#snippet trailing()}
								<ChevronRightIcon class="h-3 w-3" />
							{/snippet}
						</LinkButton>
					</TableActionCell>
				</TableRow>
			{/each}

			{#if !visitEntries.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No visits to display</TableNoResults
				>
			{/if}
		</TableBody>
	</table>

	{#if maxRows < visitEntries.length}
		<div class="mt-6 flex w-full justify-center">
			<Button
				dataCy={`${dataCyPrefix}-load-more`}
				onclick={handleClickLoadMore}
				variant="secondary"
				size="sm">LOAD MORE</Button
			>
		</div>
	{/if}
</div>
