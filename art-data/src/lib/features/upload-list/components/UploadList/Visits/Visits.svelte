<script lang="ts">
	import { CropImages } from './CropImages';
	import { MatchArtworksWithLabelsImages } from './MatchArtworksWithLabelsImages';
	import { ReviewProcessedData } from './ReviewProcessedData';
	// import { Section } from './types';
	import { UploadedImages } from './UploadedImages';
	import { groupVisitImages } from '$lib/features/upload-list/utils/groupVisitImages/groupVisitImages';
	import {
		type UploadListVisit,
		type UploadListVisitArtworks,
	} from '$lib/types';

	interface Props {
		dataCy: string;
		event: string;
		visit: UploadListVisit;
		visitArtworks: UploadListVisitArtworks;
	}

	let { dataCy, event, visit, visitArtworks }: Props = $props();

	let visitEntries = $derived(
		groupVisitImages(visit?.visit_images, visitArtworks)
	);

	// let section: Section = Section.UploadedImages;

	// $: getSectionClass = (highlightedSection: Section) =>
	// 	classNames({ 'text-gray-500': section !== highlightedSection });
</script>

<div class="mt-10 flex gap-16">
	<!-- <div class="flex min-w-48 flex-col gap-4">
		<Txt variant="h5" class={getSectionClass(Section.UploadedImages)}
			>Uploaded Images</Txt
		>
		<Txt variant="h5" class={getSectionClass(Section.CropImage)}>Crop image</Txt
		>
		<Txt variant="h5" class={getSectionClass(Section.MatchLabels)}
			>Match labels</Txt
		>
		<Txt variant="h5" class={getSectionClass(Section.Review)}>Review</Txt>
	</div> -->
	<div class="flex-grow">
		<div class="flex flex-col gap-10">
			<UploadedImages {event} {visitEntries} {dataCy} />
			<CropImages {event} {visitEntries} {dataCy} />
			<MatchArtworksWithLabelsImages {event} {visitEntries} {dataCy} />
			<ReviewProcessedData {event} {visitEntries} {dataCy} />
		</div>
	</div>
</div>
