<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { Section } from '../types';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { TickIcon } from '$global/assets/icons/TickIcon';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { LinkButton } from '$global/components/LinkButton';
	import {
		TableBody,
		TableRow,
		TableActionCell,
		TableCell,
		TableNoResults,
		TableHeaderRow,
		TableHeader,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import {
		type UploadListVisitArtwork,
		type UploadListVisitImage,
	} from '$lib/types';

	interface Props {
		event: string;
		dataCy: string;
		visitEntries: {
			images: UploadListVisitImage[];
			artworks: UploadListVisitArtwork[];
		}[];
	}

	let { event, dataCy, visitEntries }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-crop-images`);

	const actionCellWidth = '9rem';
	const headers = [
		'',
		'Date of Capture',
		'Photographer',
		'Total images to crop',
		'Images remaining',
		'',
	];

	const getImagesToExtract = (
		image?: {
			status?: { key: string | null } | null;
			data_admin_submitted_coordinates?: string | null;
		} | null
	) => {
		return (image?.status?.key as string) === 'AWAITING_COORDINATE_EXTRACTION';
	};

	const getImagesToCrop = (
		image?: {
			status?: { key: string | null } | null;
			data_admin_submitted_coordinates?: string | null;
		} | null
	) => {
		return ['COORDINATE_EXTRACTION_FAILED', 'AWAITING_REVIEW'].includes(
			image?.status?.key as string
		);
	};

	const getCroppedImages = (
		image?: {
			status?: { key: string | null } | null;
			data_admin_submitted_coordinates?: string | null;
		} | null
	) => {
		return [
			'CROPPED',
			'EXCLUDED',
			'AWAITING_CROPPING',
			'CROPPING_FAILED',
		].includes(image?.status?.key as string);
	};

	const formatVisitEntry = (visitEntry: { images: UploadListVisitImage[] }) => {
		const totalImagesToCrop = visitEntry?.images?.reduce(
			(accumulator, image, index) => {
				const originalUncroppedImageId = image?.original_uncropped_image?.id;

				if (
					index ===
					visitEntry?.images?.findIndex(
						(iimage) =>
							iimage?.original_uncropped_image?.id === originalUncroppedImageId
					)
				) {
					return (
						accumulator +
						(image?.extracted_best_guess_coordinates_from_api?.crops?.length ||
							1)
					);
				}

				return accumulator;
			},
			0
		);

		const imagesRemaining = visitEntry?.images
			?.filter((image) =>
				['AWAITING_REVIEW', 'COORDINATE_EXTRACTION_FAILED'].includes(
					image?.status?.key as string
				)
			)
			?.reduce((accumulator, image) => {
				return (
					accumulator +
					(image?.extracted_best_guess_coordinates_from_api?.crops?.length || 1)
				);
			}, 0);

		return [
			dayjs(visitEntry.images[0]?.image_taken_date).format('DD/MM/YYYY'),
			[
				visitEntry.images[0]?.photographer?.first_name,
				visitEntry.images[0]?.photographer?.last_name,
			]
				.filter(Boolean)
				.join(' '),
			totalImagesToCrop,
			imagesRemaining,
		];
	};

	const LIMIT = 5;
	let maxRows = $state(LIMIT);

	const handleClickLoadMore = () => {
		maxRows = maxRows + LIMIT;
	};
</script>

<div>
	<div class="mb-4 flex justify-between" id={Section.CropImage}>
		<div>
			<Txt variant="h6" class="mb-1 text-gray-900">Crop Images</Txt>
			<Txt variant="body2" class="mb-1"
				>The table below contains all of the cropping tasks for this {event}.
				The total images to crop could increase if any images are duplicated.
			</Txt>
		</div>
	</div>

	<table class="w-full table-fixed bg-white">
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
					class={!i ? '!w-[2.5rem]' : undefined}
				>
					{#snippet custom()}
						<div
							class={twMerge(
								'flex flex-row items-center gap-1',
								classNames({ hidden: !i })
							)}
						>
							<Txt variant="label3" class="whitespace-nowrap">{header}</Txt>
							{#if header === 'Images remaining'}
								<InfoTooltip dataCy={dataCyPrefix} content="TODO art-data" />
							{/if}
						</div>
					{/snippet}
				</TableHeader>
			{/each}
		</TableHeaderRow>

		<TableBody dataCy={dataCyPrefix}>
			{#each visitEntries.slice(0, maxRows) as visitEntry, i}
				{@const showError = visitEntry?.images?.some(
					(image) => image?.status?.key === 'CROPPING_FAILED'
				)}
				<TableRow
					index={i}
					dataCy={dataCyPrefix}
					class={classNames({
						'bg-red-100': showError,
					})}
				>
					{@const formattedVisit = formatVisitEntry(visitEntry)}
					<TableCell dataCy={dataCyPrefix} width="2.5rem">
						{#if showError}
							<InfoTooltip
								class="h-4 w-4 mt-1.5"
								content="Cropping failed for at least one of the images. Please contact customer support."
								dataCy={`${dataCyPrefix}-error`}
								classes={{
									tooltipClasses: { content: '[&>p]:whitespace-normal' },
								}}
							/>
						{:else if visitEntry?.images?.filter(getCroppedImages)?.length === visitEntry?.images?.length}
							<TickIcon class="h-4 w-4" color="green-500" />
						{/if}
					</TableCell>
					{#each formattedVisit as formattedVisitValue}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							content={formattedVisitValue}
						>
							{formattedVisitValue}
						</TableCell>
					{/each}
					<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
						{#if visitEntry?.images?.filter(getImagesToExtract)?.length}
							<Txt variant="body3" class="text-gray-500 text-right"
								>Extraction in progress ({visitEntry?.images?.filter(
									getImagesToExtract
								)?.length} images left)</Txt
							>
						{:else if visitEntry?.images?.filter(getImagesToCrop)?.length}
							<LinkButton
								dataCy={`${dataCyPrefix}-cell`}
								variant="primary"
								size="xs"
								href={`${page.url.pathname}${Routes.CropImages}?date=${dayjs(
									visitEntry?.images?.[0]?.image_taken_date
								).format('YYYY-MM-DD')}&photographer=${
									visitEntry?.images?.[0]?.photographer?.id
								}`}
							>
								Crop images
								{#snippet trailing()}
									<ChevronRightIcon class="h-3 w-3" />
								{/snippet}
							</LinkButton>
						{/if}
					</TableActionCell>
				</TableRow>
			{/each}

			{#if !visitEntries.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No visits to display</TableNoResults
				>
			{/if}
		</TableBody>
	</table>

	{#if maxRows < visitEntries.length}
		<div class="mt-6 flex w-full justify-center">
			<Button
				dataCy={`${dataCyPrefix}-load-more`}
				onclick={handleClickLoadMore}
				variant="secondary"
				size="sm">LOAD MORE</Button
			>
		</div>
	{/if}
</div>
