<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { ActionCell } from '../ActionCell';
	import { page } from '$app/state';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import { type UploadListPdf } from '$lib/types';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		dataCy: string;
		pdfs: UploadListPdf[];
	}

	let { dataCy, pdfs }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-pdfs`);
	const actionCellWidth = '12rem';

	const headers = [
		'Upload name',
		...(page.url.pathname.startsWith(Routes.FairsHome)
			? ['Fair Exhibitor']
			: []),
		'Received date',
		'Uploaded by',
		// 'Freelancer',
		'Priority',
		'Artworks',
		'Price Included',
		'Sender',
		'Receiver',
		'Submitted for review',
		'Ready for Review',
		'Reviewed and Submitted',
		'',
	];

	const getActionButtonsProps = (pdf: UploadListPdf) => {
		if (!pdf?.submitted_for_review && pdf?.status?.key === 'IMAGES_EXTRACTED') {
			return [
				{
					label: 'Match Artworks',
					href: `${page.url.pathname}${Routes.Pdfs}/${pdf.id}${Routes.EditExtractedImages}`,
					type: 'button',
				},
			];
		} else {
			return [
				...(pdf?.ready_for_review?.length
					? [
							{
								label: 'Review Artworks',
								href: `${page.url.pathname}${Routes.Pdfs}/${pdf.id}${Routes.ReviewArtworks}`,
								type: 'button',
							},
						]
					: []),
				...(pdf?.matches?.length
					? [
							{
								label: 'View matches',
								href: `${page.url.pathname}${Routes.Pdfs}/${pdf.id}${Routes.ViewMatches}`,
								type: 'link',
							},
						]
					: []),
				...(pdf?.reviewed_and_submitted?.length
					? [
							{
								label: 'View submitted artworks',
								href: `${page.url.pathname}${Routes.Pdfs}/${pdf.id}${Routes.ViewCompleted}`,
								type: 'link',
							},
						]
					: []),
			];
		}
	};

	export const formatPdf = (pdf: UploadListPdf) => {
		return [
			pdf.title,
			...(page.url.pathname.startsWith(Routes.FairsHome)
				? [pdf?.processed_fair_exhibitor_org?.name || '']
				: []),
			dayjs(pdf.receipt_info?.receive_date).format('DD/MM/YYYY'),
			`${pdf.user_created?.first_name} ${pdf.user_created?.last_name}`,
			// pdf.for_freelancers ? 'Yes' : 'No',
			pdf.high_priority ? 'Yes' : 'No',
			pdf?.artworks?.length || 0,
			pdf.includes_prices ? 'Yes' : 'No',
			pdf.receipt_info?.sender || '',
			pdf.receipt_info?.receiver || '',
			pdf.submitted_for_review ? 'Yes' : 'No',
			pdf?.ready_for_review?.length || 0,
			`${pdf?.reviewed_and_submitted?.length || 0} / ${
				pdf?.artworks?.length || 0
			}${pdf?.skipped?.length ? ` (${pdf?.skipped?.length} skipped)` : ''}`,
		];
	};
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each pdfs as pdf, i}
			{@const actionButtonsProps = getActionButtonsProps(pdf)}
			{@const values = formatPdf(pdf)}
			{@const nbLabelParsingFailed = pdf?.artworks?.filter(
				(artwork) => artwork?.status?.key === 'LABEL_PARSER_EXTRACTION_FAILED'
			).length}
			{@const nbIngestionFailed = pdf?.artworks?.filter(
				(artwork) => artwork?.status?.key === 'INGESTION_FAILED'
			).length}
			{@const showError =
				nbLabelParsingFailed ||
				pdf?.review_status?.key === 'FAILED' ||
				pdf?.status?.key === 'IMAGE_EXTRACTION_FAILED'}

			<TableRow
				index={i}
				dataCy={dataCyPrefix}
				class={classNames({
					'bg-green-100': pdf?.review_status?.key === 'COMPLETED',
					'bg-red-100': showError,
				})}
			>
				{#each values as value, j}
					{@const isLastCell = j === values.length - 1}
					{#if !j}
						<TableCell
							dataCy={dataCyPrefix}
							class="break-all"
							width={getCellWidth(i, actionCellWidth, headers)}
							{...isLastCell ? {} : { content: value }}
						>
							{#snippet custom()}
								<a
									href={getImageUrl(pdf?.pdf_file?.filename_disk, 'original')}
									rel="noopener noreferrer"
									target="_blank"
									class={classNames('line-clamp-4', { hidden: j > 0 })}
								>
									<Txt variant="body2" class="text-blue-500">
										{value}
									</Txt>
								</a>
							{/snippet}
						</TableCell>
					{:else}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
							wrap
							class="[&_p]:break-words"
							textVariant={isLastCell ? 'body3' : 'body2'}
							{...isLastCell ? {} : { content: value }}
						>
							{value}
						</TableCell>
					{/if}
				{/each}

				<ActionCell
					{actionButtonsProps}
					dataCy={dataCyPrefix}
					{actionCellWidth}
					errorMessage={(() => {
						if (!showError) {
							return '';
						}

						if (pdf?.status?.key === 'IMAGE_EXTRACTION_FAILED') {
							return 'Image extraction failed';
						}

						if (nbLabelParsingFailed) {
							return `Label parsing failed for ${nbLabelParsingFailed} artwork(s)`;
						}

						return `Ingestion failed for ${nbIngestionFailed} artwork(s)`;
					})()}
				/>
			</TableRow>
		{/each}

		{#if !pdfs.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No PDF to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>
