<script lang="ts">
	import { ArtlogicLinksTable } from './ArtlogicLinksTable';
	import { ManualUploadsTable } from './ManualUploadsTable';
	import { PdfsTable } from './PdfsTable';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { UploadIcon } from '$global/assets/icons/UploadIcon';
	import { LinkButton } from '$global/components/LinkButton';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import type {
		UploadListArtlogicLink,
		UploadListManualUpload,
		UploadListPdf,
	} from '$lib/types';

	interface Props {
		dataCy: string;
		manualUploads: UploadListManualUpload[];
		pdfs: UploadListPdf[];
		artlogicLinks: UploadListArtlogicLink[];
	}

	let { dataCy, manualUploads, pdfs, artlogicLinks }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-checklists`);
</script>

<div class="mt-10 flex gap-4">
	<LinkButton
		dataCy={`${dataCyPrefix}-add-pdf`}
		class="min-w-[15rem] [&>button]:w-full"
		size="md"
		icon
		href={`${page.url.pathname}${Routes.PdfsAdd}`}
		variant="secondary"
		>add pdf{#snippet trailing()}
			<UploadIcon class="ml-1" />
		{/snippet}</LinkButton
	>

	<LinkButton
		dataCy={`${dataCyPrefix}-add-artlogic`}
		class="min-w-[15rem] [&>button]:w-full"
		size="md"
		href={`${page.url.pathname}${Routes.ArtlogicAdd}`}
		variant="secondary"
		>add artlogic link{#snippet trailing()}
			<PlusIcon />
		{/snippet}</LinkButton
	>

	<LinkButton
		dataCy={`${dataCyPrefix}-add-manual`}
		class="min-w-[15rem] [&>button]:w-full"
		size="md"
		href={`${page.url.pathname}${Routes.ManualUploadAdd}`}
		variant="secondary"
		>add manual upload{#snippet trailing()}
			<UploadIcon class="ml-1" />
		{/snippet}</LinkButton
	>
</div>

<Txt variant="h6" class="mb-4 mt-8">PDF</Txt>
<PdfsTable {pdfs} dataCy={dataCyPrefix} />
<Txt variant="h6" class="mb-4 mt-10">Artlogic Links</Txt>
<ArtlogicLinksTable {artlogicLinks} dataCy={dataCyPrefix} />
<Txt variant="h6" class="mb-4 mt-10">Manual</Txt>
<ManualUploadsTable {manualUploads} dataCy={dataCyPrefix} />
