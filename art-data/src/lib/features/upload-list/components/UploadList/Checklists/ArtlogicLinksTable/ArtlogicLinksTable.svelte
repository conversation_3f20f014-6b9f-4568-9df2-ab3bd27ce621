<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { ActionCell } from '../ActionCell';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { ChecklistStatus, type UploadListArtlogicLink } from '$lib/types';
	import { formatChecklistStatus } from '$lib/utils/formatChecklistStatus/formatChecklistStatus';

	interface Props {
		dataCy: string;
		artlogicLinks: UploadListArtlogicLink[];
	}

	let { dataCy, artlogicLinks }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-artlogic`);
	const actionCellWidth = '12rem';

	const headers = [
		'Upload name',
		'Gallery',
		'Received date',
		'Uploaded by',
		'Priority',
		'Artworks',
		'Price Included',
		'Sender',
		'Receiver',
		'Status',
		'',
	];

	const getActionButtonsProps = (artlogicLink: UploadListArtlogicLink) => {
		switch (artlogicLink.status?.name) {
			case ChecklistStatus.SentToIngestionSchema.toLowerCase():
				return [
					{
						label: 'View artlogic link',
						href: artlogicLink.url,
						external: true,
						type: 'link',
					},
				];
			default:
				return [];
		}
	};

	export const formatPdf = (artlogicLink: UploadListArtlogicLink) => {
		return [
			artlogicLink?.title,
			artlogicLink?.processed_gallery?.name,
			artlogicLink.receipt_info?.receive_date
				? dayjs(
						getDayFromDirectus(artlogicLink.receipt_info?.receive_date)
					).format('DD/MM/YYYY')
				: '',
			`${artlogicLink?.user_created?.first_name} ${artlogicLink?.user_created?.last_name}`,
			artlogicLink.high_priority ? 'Yes' : 'No',
			artlogicLink.art_event_feed?.artwork_feed?.length || 0,
			artlogicLink.includes_prices ? 'Yes' : 'No',
			artlogicLink.receipt_info?.sender || '',
			artlogicLink.receipt_info?.receiver || '',
			formatChecklistStatus(artlogicLink?.status?.name),
		];
	};
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each artlogicLinks as artlogicLink, i}
			{@const actionButtonsProps = getActionButtonsProps(artlogicLink)}
			{@const values = formatPdf(artlogicLink)}

			<TableRow
				index={i}
				dataCy={dataCyPrefix}
				class={classNames({
					'bg-green-100':
						artlogicLink.status?.name?.toLowerCase() ===
						ChecklistStatus.SentToIngestionSchema.toLowerCase(),
				})}
			>
				{#each values as value, j}
					{@const isLastCell = j === values.length - 1}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						wrap
						class="[&_p]:break-words"
						textVariant={isLastCell ? 'body3' : 'body2'}
						classes={{
							text: classNames({
								'text-green-700':
									artlogicLink.status?.name?.toLowerCase() ===
										ChecklistStatus.SentToIngestionSchema.toLowerCase() &&
									isLastCell,
							}),
						}}
						{...isLastCell ? {} : { content: value }}
					>
						{value}
					</TableCell>
				{/each}

				<ActionCell
					{actionButtonsProps}
					dataCy={dataCyPrefix}
					{actionCellWidth}
					errorMessage=""
				/>
			</TableRow>
		{/each}

		{#if !artlogicLinks.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No Artlogic links to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>
