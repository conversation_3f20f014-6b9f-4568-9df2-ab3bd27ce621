<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { ActionCell } from '../ActionCell';
	import { page } from '$app/state';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { Routes } from '$lib/constants/routes';
	import { type UploadListManualUpload } from '$lib/types';

	interface Props {
		dataCy: string;
		manualUploads: UploadListManualUpload[];
	}

	let { dataCy, manualUploads }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-manual-uploads`);

	const actionCellWidth = '12rem';

	const headers = [
		'Upload name',
		...(page.url.pathname.startsWith(Routes.FairsHome)
			? ['Fair Exhibitor']
			: []),
		'Received date',
		'Uploaded by',
		// 'Freelancer',
		'Priority',
		'Artworks',
		'Price Included',
		'Sender',
		'Receiver',
		'Submitted for review',
		'Ready for Review',
		'Reviewed and Submitted',
		'',
	];

	const getActionButtonsProps = (manualUpload: UploadListManualUpload) => {
		if (!manualUpload.submitted_for_review) {
			return [
				{
					label: 'Match Artworks',
					href: `${page.url.pathname}${Routes.ManualUpload}/${manualUpload.id}${Routes.AddData}`,
					type: 'button',
				},
			];
		} else {
			return [
				...(manualUpload?.ready_for_review?.length
					? [
							{
								label: 'Review Artworks',
								href: `${page.url.pathname}${Routes.ManualUpload}/${manualUpload.id}${Routes.ReviewArtworks}`,
								type: 'button',
							},
						]
					: []),
				...(manualUpload?.matches?.length
					? [
							{
								label: 'View matches',
								href: `${page.url.pathname}${Routes.ManualUpload}/${manualUpload.id}${Routes.ViewMatches}`,
								type: 'link',
							},
						]
					: []),
				...(manualUpload?.reviewed_and_submitted?.length
					? [
							{
								label: 'View submitted artworks',
								href: `${page.url.pathname}${Routes.ManualUpload}/${manualUpload.id}${Routes.ViewCompleted}`,
								type: 'link',
							},
						]
					: []),
			];
		}
	};

	export const formatManualUpload = (manualUpload: UploadListManualUpload) => {
		return [
			manualUpload?.title,
			...(page.url.pathname.startsWith(Routes.FairsHome)
				? [manualUpload?.processed_fair_exhibitor_org?.name || '']
				: []),
			manualUpload.receipt_info?.receive_date
				? dayjs(
						getDayFromDirectus(manualUpload.receipt_info?.receive_date)
					).format('DD/MM/YYYY')
				: '',
			`${manualUpload?.user_created?.first_name} ${manualUpload?.user_created?.last_name}`,
			// manualUpload.for_freelancers ? 'Yes' : 'No',
			manualUpload.high_priority ? 'Yes' : 'No',
			(manualUpload?.manually_added_artworks || []).length,
			manualUpload.includes_prices ? 'Yes' : 'No',
			manualUpload.receipt_info?.sender || '',
			manualUpload.receipt_info?.receiver || '',
			manualUpload.submitted_for_review ? 'Yes' : 'No',
			manualUpload?.ready_for_review?.length || 0,
			`${manualUpload?.reviewed_and_submitted?.length || 0} / ${
				manualUpload?.manually_added_artworks?.length || 0
			}${
				manualUpload?.skipped?.length
					? ` (${manualUpload?.skipped?.length} skipped)`
					: ''
			}`,
		];
	};
</script>

<table class="w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, actionCellWidth, headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	<TableBody dataCy={dataCyPrefix}>
		{#each manualUploads as manualUpload, i}
			{@const actionButtonsProps = getActionButtonsProps(manualUpload)}
			{@const values = formatManualUpload(manualUpload)}
			{@const nbLabelParsingFailed =
				manualUpload?.manually_added_artworks?.filter(
					(artwork) => artwork?.status?.key === 'LABEL_PARSER_EXTRACTION_FAILED'
				).length}
			{@const nbIngestionFailed = manualUpload?.manually_added_artworks?.filter(
				(artwork) => artwork?.status?.key === 'INGESTION_FAILED'
			).length}
			{@const showError =
				nbLabelParsingFailed ||
				manualUpload?.review_status?.key === 'FAILED' ||
				manualUpload?.status?.key === 'IMAGE_EXTRACTION_FAILED'}

			<TableRow
				index={i}
				dataCy={dataCyPrefix}
				class={classNames({
					'bg-green-100': manualUpload?.review_status?.key === 'COMPLETED',
					'bg-red-100': showError,
				})}
			>
				{#each values as value, j}
					{@const isLastCell = j === values.length - 1}
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						class="[&_p]:break-words"
						wrap
						textVariant={isLastCell ? 'body3' : 'body2'}
						{...isLastCell ? {} : { content: value }}
					>
						{value}
					</TableCell>
				{/each}
				<ActionCell
					{actionCellWidth}
					{actionButtonsProps}
					{dataCy}
					errorMessage={(() => {
						if (!showError) {
							return '';
						}

						if (manualUpload?.status?.key === 'IMAGE_EXTRACTION_FAILED') {
							return 'Image extraction failed';
						}

						if (nbLabelParsingFailed) {
							return `Label parsing failed for ${nbLabelParsingFailed} artwork(s)`;
						}

						return `Ingestion failed for ${nbIngestionFailed} artwork(s)`;
					})()}
				/>
			</TableRow>
		{/each}

		{#if !manualUploads.length}
			<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
				>No Manual Upload to display</TableNoResults
			>
		{/if}
	</TableBody>
</table>
