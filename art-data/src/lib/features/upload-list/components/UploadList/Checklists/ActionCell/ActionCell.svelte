<script lang="ts">
	import classNames from 'classnames';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { LinkButton } from '$global/components/LinkButton';
	import { TableActionCell } from '$global/components/Table/TableActionCell';
	import { Txt } from '$global/components/Txt';

	interface Props {
		actionCellWidth: string;
		dataCy: string;
		errorMessage: string;
		actionButtonsProps: {
			type: string;
			href: string | null | undefined;
			label: string | null | undefined;
			external?: boolean;
		}[];
	}

	let { actionCellWidth, dataCy, actionButtonsProps, errorMessage }: Props =
		$props();
</script>

<TableActionCell {dataCy} width={actionCellWidth} class="[&>div]:flex-col">
	{#each actionButtonsProps as actionButtonProps}
		{#if actionButtonProps?.type === 'button'}
			<LinkButton
				dataCy={`${dataCy}-cell`}
				size="xs"
				icon
				href={actionButtonProps.href}
				class="min-w-full [&>button]:min-w-full"
			>
				{actionButtonProps.label}
				{#snippet trailing()}
					<ChevronRightIcon class="h-3 w-3" />
				{/snippet}
			</LinkButton>
		{:else if actionButtonProps?.type === 'link'}
			<Txt
				href={actionButtonProps.href}
				component="a"
				variant="body3"
				class="mt-1 block text-right text-blue-500 underline"
			>
				{actionButtonProps.label}
			</Txt>
		{/if}
	{/each}
	{#if errorMessage}
		<Txt
			variant="body3"
			class={classNames('block text-right text-red-500', {
				'mt-1': actionButtonsProps.length,
			})}
		>
			{errorMessage}
		</Txt>
	{/if}
</TableActionCell>
