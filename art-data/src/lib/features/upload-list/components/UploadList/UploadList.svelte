<script lang="ts">
	import classNames from 'classnames';
	import { mutation } from 'gql-query-builder';
	import { Checklists } from './Checklists';
	import { Visits } from './Visits';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { Tabs } from '$global/components/Tabs';
	import { Txt } from '$global/components/Txt';

	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { PageBody } from '$lib/components/PageBody';
	import { CreateVisitDocument } from '$lib/features/visit/queries/__generated__/createVisit.generated';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		UploadListArtlogicLink,
		UploadListManualUpload,
		UploadListPdf,
		UploadListVisit,
		UploadListVisitArtworks,
	} from '$lib/types';

	interface Props {
		event: string;
		dataCy: string;
		pdfs: UploadListPdf[];
		artlogicLinks: UploadListArtlogicLink[];
		manualUploads: UploadListManualUpload[];
		visit: UploadListVisit;
		visitArtworks: UploadListVisitArtworks;
		crumbs: Crumb[];
		title: string;
		subtitle: string;
		firstTabTitle: string;
		collectionName: string;
		summary?: import('svelte').Snippet;
		children?: import('svelte').Snippet;
	}

	let {
		event,
		dataCy,
		pdfs,
		artlogicLinks,
		manualUploads,
		visit,
		visitArtworks,
		crumbs,
		title,
		subtitle,
		firstTabTitle,
		collectionName,
		summary,
		children,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-upload-list`);

	const tabs = [
		{
			id: '1',
			title: firstTabTitle,
		},
		{
			id: '2',
			title: 'Visit',
		},
	];

	let activeTab = $state(0);
	let creatingVisit = $state(false);

	const handleClickCreateVisit = async () => {
		creatingVisit = true;

		const createVisitRes = await gqlClient.request(
			CreateVisitDocument,
			{
				data: {},
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		const viistId = createVisitRes?.create_Visit_item?.id;

		const updateMutation = mutation({
			operation: {
				name: `update_${collectionName}_item`,
				alias: `update_${collectionName}_item`,
			},
			fields: ['id'],
			variables: {
				id: {
					name: 'id',
					type: `ID!`,
					value: page.params.id,
				},
				data: {
					name: 'data',
					type: `update_${collectionName}_input!`,
					value: { visit: { id: viistId } },
				},
			},
		});

		await gqlClient.request(
			updateMutation.query,
			updateMutation.variables,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		window.location.reload();
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<Txt variant="h3">{title}</Txt>
		<hr class="my-6 border-gray-200" />

		<div
			class={classNames('mb-14', summary ? 'flex gap-6' : 'grid grid-cols-2')}
		>
			<div class={classNames({ 'flex-1': summary })}>
				<Txt variant="h6" class="mb-4">{subtitle}</Txt>
				<div class="w-full">
					{@render children?.()}
				</div>
			</div>
			{@render summary?.()}
		</div>

		<div class="h-[1px] w-full translate-y-[3.375rem] bg-gray-200"></div>

		<Tabs
			dataCy={dataCyPrefix}
			{tabs}
			bind:activeTab
			classes={{ inactiveTabLabel: 'text-gray-700' }}
		>
			{#if activeTab === 0}
				<Checklists
					{pdfs}
					{artlogicLinks}
					{manualUploads}
					dataCy={dataCyPrefix}
				/>
			{/if}
			{#if activeTab === 1}
				{#if !visit}
					<Button
						size="md"
						dataCy={`${dataCyPrefix}-create-visit`}
						variant="secondary"
						loading={creatingVisit}
						onclick={handleClickCreateVisit}
						class="mt-12"
					>
						Create visit
					</Button>
				{:else}
					<Visits {event} {visitArtworks} {visit} dataCy={dataCyPrefix} />
				{/if}
			{/if}
		</Tabs>
	</Container>
</PageBody>
