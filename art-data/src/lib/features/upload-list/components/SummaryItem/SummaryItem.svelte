<script lang="ts">
	import { Txt } from '$global/components/Txt';

	interface Props {
		label: string;
		count: number;
		bgColor: string;
	}

	let { label, count, bgColor }: Props = $props();
</script>

<div
	class="w-[8.75rem] rounded px-2 py-4 shadow-[0px_1px_1px_0px_rgba(0,0,0,0.10)]"
	style:background-color={bgColor}
>
	<Txt variant="body2" class="mb-2 text-center font-[700] text-gray-0">
		{label}
	</Txt>
	<div
		class="mx-auto w-[2.625rem] justify-center rounded-full bg-[rgba(255,255,255,0.23)]"
	>
		<Txt variant="h5" class="text-center font-[700] text-gray-0">
			{count}
		</Txt>
	</div>
</div>
