import {
	type UploadListVisit,
	type UploadListVisitImage,
	type UploadListVisitArtworks,
	type UploadListVisitArtwork,
} from '$lib/types';

export const groupVisitImages = (
	visitImages: NonNullable<UploadListVisit>['visit_images'],
	visitArtworks: UploadListVisitArtworks
) => {
	const sortedVisitEntries = (visitImages || [])?.reduce(
		(
			accumulator: Record<
				string,
				Record<
					string,
					{ images: UploadListVisitImage[]; artworks: UploadListVisitArtwork[] }
				>
			>,
			visitImage
		) => {
			const imageTakenDate = `${visitImage?.image_taken_date}`.slice(0, 10);
			const photographerId = `${visitImage?.photographer?.id}`;

			const artworks = visitArtworks?.filter(
				(visitArtwork) =>
					(visitArtwork?.artwork_image?.image_taken_date.slice(0, 10) ===
						imageTakenDate &&
						visitArtwork?.artwork_image?.photographer?.id === photographerId) ||
					(visitArtwork?.label_image?.image_taken_date.slice(0, 10) ===
						imageTakenDate &&
						visitArtwork?.label_image?.photographer?.id === photographerId)
			);

			if (!accumulator[imageTakenDate]) {
				return {
					...accumulator,
					[imageTakenDate]: {
						[photographerId]: { images: [visitImage], artworks },
					},
				};
			}

			if (!accumulator[imageTakenDate][photographerId]) {
				return {
					...accumulator,
					[imageTakenDate]: {
						...accumulator[imageTakenDate],
						[photographerId]: { images: [visitImage], artworks },
					},
				};
			}

			return {
				...accumulator,
				[imageTakenDate]: {
					...accumulator[imageTakenDate],
					[photographerId]: {
						images: [
							...accumulator[imageTakenDate][photographerId].images,
							visitImage,
						],
						artworks: accumulator[imageTakenDate][photographerId].artworks,
					},
				},
			};
		},
		{} as Record<
			string,
			Record<
				string,
				{ images: UploadListVisitImage[]; artworks: UploadListVisitArtwork[] }
			>
		>
	);

	const visitEntriesArray = Object.keys(sortedVisitEntries).flatMap(
		(imagesTakenKeyEntry) =>
			Object.keys(sortedVisitEntries[imagesTakenKeyEntry]).map(
				(photographerTakenKeyEntry) =>
					sortedVisitEntries[imagesTakenKeyEntry][photographerTakenKeyEntry]
			)
	) as { images: UploadListVisitImage[]; artworks: UploadListVisitArtwork[] }[];

	return visitEntriesArray
		.map((visitEntry) => {
			const imagesCropped = visitEntry?.images?.filter(
				(image) =>
					image?.perspective_cropped_image_without_dimensions ||
					image?.rectangular_cropped_image
			);

			const imagesRemaining = imagesCropped?.filter((imageCropped) =>
				visitArtworks?.every(
					(visitArtwork) =>
						visitArtwork?.artwork_image?.id !== imageCropped?.id &&
						visitArtwork?.label_image?.id !== imageCropped?.id
				)
			);

			const nbArtworksRemaining = visitEntry?.artworks?.filter((visitArtwork) =>
				[
					'AWAITING_REVIEW',
					'LABEL_PARSER_EXTRACTION_FAILED',
					'AWAITING_LABEL_PARSER',
					'LABEL_PARSER_EXTRACTING',
				].includes(visitArtwork?.status?.key as string)
			)?.length;

			const nbArtworksReviewable = visitEntry?.artworks?.filter(
				(visitArtwork) =>
					['AWAITING_REVIEW'].includes(visitArtwork?.status?.key as string)
			)?.length;

			const nbArtworksWithError = visitEntry?.artworks?.filter((visitArtwork) =>
				['LABEL_PARSER_EXTRACTION_FAILED', 'OCR_EXTRACTION_FAILED'].includes(
					visitArtwork?.status?.key as string
				)
			)?.length;

			const nbArtworksIngestionFailed = visitEntry?.artworks?.filter(
				(visitArtwork) =>
					['INGESTION_FAILED'].includes(visitArtwork?.status?.key as string)
			)?.length;

			const nbArtworksCompleted = visitEntry?.artworks?.filter(
				(visitArtwork) =>
					[
						'REVIEWED_AND_SUBMITTED',
						'COMPLETED',
						'AWAITING_INGESTION',
						'INGESTION_FAILED',
					].includes(`${visitArtwork?.status?.key}`) &&
					!visitArtwork.is_installation_shot
			)?.length;

			return {
				...visitEntry,
				nbImagesCropped: imagesCropped?.length,
				nbImagesRemaining: imagesRemaining?.length,
				nbArtworksRemaining,
				nbArtworksCompleted,
				nbArtworksReviewable,
				nbArtworksWithError,
				nbArtworksIngestionFailed,
			};
		})
		.sort(
			(visitEntryA, visitEntryB) =>
				+new Date(visitEntryB?.images?.[0]?.image_taken_date) -
				+new Date(visitEntryA?.images?.[0]?.image_taken_date)
		);
};
