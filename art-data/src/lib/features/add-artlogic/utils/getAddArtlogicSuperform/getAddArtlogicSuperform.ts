import { zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms/server';
import {
	AddArtlogicValidationSchema,
	AddArtlogicFieldNames,
} from '../../constants/add-artlogic-validation-schema';

export const getAddArtlogicSuperform = async () => {
	const addArtlogicForm = await superValidate(
		zod(AddArtlogicValidationSchema, {
			defaults: {
				artlogic_url: '',
				input_name: '',
				received_date: '',
				sender: '',
				receiver: '',
				[AddArtlogicFieldNames.IncludesPrices]: false,
				[AddArtlogicFieldNames.HighPriority]: false,
			},
		})
	);
	return { addArtlogicForm };
};
