<script lang="ts">
	import classNames from 'classnames';
	import { AddArtlogicFieldNames } from '../../constants/add-artlogic-validation-schema';
	import type { ArtlogicGallery } from '../../types';
	import { AddArtlogicDetails } from './AddArtlogicDetails';
	import { AddArtlogicForm } from './AddArtlogicForm';
	import { page } from '$app/state';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import {
		ErrorSuccessPage,
		type ErrorSuccessPageProps,
	} from '$global/features/form/pages/ErrorSuccessPage';
	import { getUTCDayDate } from '$global/utils/getUTCDayDate/getUTCDayDate';
	import { PageBody } from '$lib/components/PageBody';
	import { Config } from '$lib/constants/config';
	import { GetProcessedArtistDocument } from '$lib/features/artist/queries/__generated__/getProcessedArtist.generated';
	import { createReceiptInfoMutation } from '$lib/features/receipt-info/utils/createReceiptInfoMutation/createReceiptInfoMutation';
	import { gqlClient } from '$lib/gqlClient';
	import { GetProcessedOrganisationDocument } from '$lib/queries/__generated__/getProcessedOrganisation.generated';
	import { IngestionDataTypenames } from '$lib/types';

	interface Props {
		artist?: any;
		gallery: ArtlogicGallery | null;
		key: string;
		collection: string;
		title: string;
		crumbs: Crumb[];
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props: ErrorSuccessPageProps['button2Props'];
	}

	let {
		artist = undefined,
		key,
		gallery,
		collection,
		title,
		crumbs,
		buttonProps,
		button2Props,
	}: Props = $props();

	const dataCyPrefix = `${key}-add-artlogic`;
	const createReceiptInfo = createReceiptInfoMutation();

	let success = $state(false);
	let details:
		| (Record<AddArtlogicFieldNames, string> & {
				gallery?: OptionType;
				artist?: OptionType;
		  })
		| null
		| undefined = $state();

	const handleSubmit = async () => {
		if (!details) {
			return;
		}

		const receiptInfoResponse = await $createReceiptInfo.mutateAsync({
			input: {
				...(details[AddArtlogicFieldNames.Sender] && {
					sender: details[AddArtlogicFieldNames.Sender],
				}),
				...(details[AddArtlogicFieldNames.Receiver] && {
					receiver: details[AddArtlogicFieldNames.Receiver],
				}),
				receive_date: getUTCDayDate(
					details[AddArtlogicFieldNames.ReceivedDate]
				),
			},
		});

		const processedArtistId = (() => {
			if (!details.artist) {
				return null;
			}

			const artistUrlBits = details.artist?.line2?.split('/');
			return artistUrlBits?.[artistUrlBits.length - 1];
		})();

		let artist;

		if (processedArtistId) {
			const artistResponse = await gqlClient.request(
				GetProcessedArtistDocument,
				{ filter: { processed_artist_id: { _eq: processedArtistId } } },
				getAuthorizationHeaders(page.data as { user: { access_token: string } })
			);

			artist = artistResponse?.Processed_Artist?.[0];
		}

		const processedGalleryId = (() => {
			if (!details.gallery) {
				return null;
			}

			const galleryUrlBits = details.gallery?.line2?.split('/');
			return galleryUrlBits?.[galleryUrlBits.length - 1];
		})();

		let gallery;

		const getGalleryResponse = await gqlClient.request(
			GetProcessedOrganisationDocument,
			{
				filter: { id: { _eq: processedGalleryId } },
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		gallery = getGalleryResponse?.Processed_Organisation?.[0];

		const createArtlogicLinkResponse = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/${collection}/${page.params.id}`,
			{
				method: 'PATCH',
				headers: {
					...getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					),
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ingestion_data: {
						create: [
							{
								collection: IngestionDataTypenames.ArtlogicLink,
								item: {
									high_priority: details?.high_priority,
									title: details?.input_name,
									includes_prices: details?.includes_prices,
									processed_gallery: gallery
										? { id: gallery?.id }
										: {
												id: processedGalleryId,
												location: details?.gallery?.line3,
												name: details?.gallery?.line1,
											},
									status: {
										key: 'AWAITING_EXTRACTION',
									},
									...(processedArtistId && {
										processed_artist: artist
											? {
													processed_artist_id: artist?.processed_artist_id,
												}
											: {
													processed_artist_id: processedArtistId,
													...(details?.artist?.line1?.split('//')[0] && {
														first_name: details?.artist?.line1?.split('//')[0],
													}),
													...(details?.artist?.line1?.split('//')[1] && {
														last_name: details?.artist?.line1?.split('//')[1],
													}),
													...(details?.artist?.line3?.split(':::')[0] && {
														year_birth: details?.artist?.line3?.split(':::')[0],
													}),
													...(details?.artist?.line3?.split(':::')[1] && {
														year_death: details?.artist?.line3?.split(':::')[1],
													}),
													...(details?.artist?.line4 && {
														nationality: details?.artist?.line4,
													}),
												},
									}),
									url: details[AddArtlogicFieldNames.ArtlogicUrl],
									receipt_info:
										receiptInfoResponse?.create_Receipt_Information_item?.id,
								},
							},
						],
						delete: [],
						update: [],
					},
				}),
			}
		);

		if (!createArtlogicLinkResponse.ok) {
			return Promise.reject();
		}

		success = true;
		return Promise.resolve();
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	<AddArtlogicForm
		{artist}
		{gallery}
		dataCy={dataCyPrefix}
		{title}
		bind:details
		class={classNames({ hidden: !!details })}
	/>

	<AddArtlogicDetails
		onSubmit={handleSubmit}
		dataCy={dataCyPrefix}
		bind:details
		class={classNames('mt-10', { hidden: !details || success })}
	/>

	<ErrorSuccessPage
		dataCy={dataCyPrefix}
		variant="success"
		title="Artlogic Link Added"
		class={classNames('mt-10', { hidden: !details || !success })}
		{buttonProps}
		{button2Props}
	>
		{#snippet moreContent()}
			<Txt>
				Your art logic link will be scraped. Please check back on the exhibition
				page to review the data before you submit it to the database.
			</Txt>
		{/snippet}
	</ErrorSuccessPage>
</PageBody>
