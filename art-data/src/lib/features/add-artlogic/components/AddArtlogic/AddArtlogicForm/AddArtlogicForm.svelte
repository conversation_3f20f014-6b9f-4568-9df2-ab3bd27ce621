<script lang="ts">
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';
	import { twMerge } from 'tailwind-merge';
	import {
		AddArtlogicFieldNames,
		AddArtlogicValidationSchema,
	} from '../../../constants/add-artlogic-validation-schema';
	import { page } from '$app/state';
	import { Checkbox } from '$global/components/Checkbox';
	import { InputLabel } from '$global/components/InputLabel';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { getFormBlur } from '$global/utils/getFormBlur';
	import { getFormErrorsFromSchema } from '$global/utils/getFormErrorsFromSchema';
	import { isFormValid } from '$global/utils/isFormValid';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { RemovableSelectedLinkOption } from '$lib/components/RemovableSelectedLinkOption';
	import type { ArtlogicGallery } from '$lib/features/add-artlogic/types';
	import {
		ArtistsAutocomplete,
		formatExhibitionArtist,
	} from '$lib/features/artist/components/ArtistsAutocomplete';
	import { GallerySelectorAutocomplete } from '$lib/features/gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';
	import {
		GetArteyeGalleriesDocument,
		type GetArteyeGalleriesQueryVariables,
		type GetArteyeGalleriesQuery,
	} from '$lib/websites/gallery-offerings/arteye-queries/__generated__/getArteyeGalleries.generated';
	import { CannotFindGallery } from '$lib/websites/gallery-offerings/pages/GalleryOfferingsAddPage/GalleryOfferingsAddForm/CannotFindGallery';
	import { getGalleryDetails } from '$lib/websites/gallery-offerings/utils/getGalleryDetails/getGalleryDetails';

	interface Props {
		artist: any;
		gallery: ArtlogicGallery | null;
		title: string;
		dataCy: string;
		class?: string;
		details:
			| (Record<AddArtlogicFieldNames, string> & {
					gallery?: OptionType;
					artist?: OptionType;
			  })
			| null
			| undefined;
	}

	let {
		artist,
		gallery,
		title,
		dataCy,
		details = $bindable(),
		...rest
	}: Props = $props();
	const artistValue = writable('');
	let artistSelectedOption: OptionType | null = $state(null);
	let galleries = $state([
		{ value: writable(''), selectedOption: null as OptionType | null },
	]);

	onMount(() => {
		if (artist) {
			artistSelectedOption = formatExhibitionArtist(artist);
		}

		if (gallery) {
			galleries[0].selectedOption = {
				line1: gallery?.organisation?.entity?.name,
				line2: `https://feather-dome.no-zero.net/galleries/${gallery?.id}`,
				line3: gallery?.organisation?.location?.name,
			};
		}
	});

	const dataCyPrefix = `${dataCy}-add-artlogic-form`;

	const handleSubmit = (e: Event) => {
		e.preventDefault();
		details = {
			[AddArtlogicFieldNames.HighPriority]:
				$form[AddArtlogicFieldNames.HighPriority],
			[AddArtlogicFieldNames.IncludesPrices]:
				$form[AddArtlogicFieldNames.IncludesPrices],
			[AddArtlogicFieldNames.ArtlogicUrl]:
				$form[AddArtlogicFieldNames.ArtlogicUrl],
			[AddArtlogicFieldNames.InputName]: $form[AddArtlogicFieldNames.InputName],
			[AddArtlogicFieldNames.ReceivedDate]:
				$form[AddArtlogicFieldNames.ReceivedDate],
			...($form[AddArtlogicFieldNames.Sender] && {
				[AddArtlogicFieldNames.Sender]: $form[AddArtlogicFieldNames.Sender],
			}),
			...($form[AddArtlogicFieldNames.Receiver] && {
				[AddArtlogicFieldNames.Receiver]: $form[AddArtlogicFieldNames.Receiver],
			}),
			gallery: galleries[0].selectedOption,
			...(artistSelectedOption && {
				artist: artistSelectedOption,
			}),
		};
	};

	const { form, constraints } = superForm(page.data.addArtlogicForm, {
		validators: zod(AddArtlogicValidationSchema),
		validationMethod: 'auto',
	});

	const { blur, handleBlur } = getFormBlur(page.data.addArtlogicForm);
	const errors = getFormErrorsFromSchema(form, AddArtlogicValidationSchema);

	const getVariables = (value: string): GetArteyeGalleriesQueryVariables => {
		return {
			limit: 20,
			filter: {
				_and: [
					...(() => {
						if (!value) {
							return [];
						}

						if (isUuidValid(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [
							{
								organisation: {
									entity: {
										name: { _icontains: value },
									},
								},
							},
						];
					})(),

					{ status: { key: { _neq: 'archived' } } },
				],
			},
		};
	};

	const getOptions = (data: GetArteyeGalleriesQuery | undefined) => {
		return (data?.gallery || [])?.map(getGalleryDetails);
	};
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt
			variant="body3"
			class="col-span-2 mb-[-0.5rem] uppercase tracking-[1.68px]">{title}</Txt
		>
		<Txt variant="h4" class="col-span-2 mb-2">Add Artlogic link</Txt>

		<StepInput
			dataCy={`${dataCyPrefix}-artlogic-url`}
			label="Artlogic URL"
			placeholder="example.com"
			id={AddArtlogicFieldNames.ArtlogicUrl}
			name={AddArtlogicFieldNames.ArtlogicUrl}
			type="text"
			class="col-span-2"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddArtlogicFieldNames.ArtlogicUrl] &&
				$errors[AddArtlogicFieldNames.ArtlogicUrl]}
			bind:value={$form[AddArtlogicFieldNames.ArtlogicUrl]}
			onblur={handleBlur(AddArtlogicFieldNames.ArtlogicUrl)}
			{...$constraints[AddArtlogicFieldNames.ArtlogicUrl]}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-input-name`}
			label="Input Name"
			placeholder="Input Name"
			id={AddArtlogicFieldNames.InputName}
			name={AddArtlogicFieldNames.InputName}
			type="text"
			class="col-span-2 mb-[-4px]"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddArtlogicFieldNames.InputName] &&
				$errors[AddArtlogicFieldNames.InputName]}
			bind:value={$form[AddArtlogicFieldNames.InputName]}
			onblur={handleBlur(AddArtlogicFieldNames.InputName)}
			{...$constraints[AddArtlogicFieldNames.InputName]}
		/>

		<div class="col-span-2 mt-2">
			<InputLabel class="mb-[-8px]" dataCy={`${dataCyPrefix}-gallery`} required
				>Gallery</InputLabel
			>
			<GallerySelectorAutocomplete
				index={0}
				OptionComponent={LinkOption}
				SelectedOptionComponent={RemovableSelectedLinkOption}
				document={GetArteyeGalleriesDocument}
				{getOptions}
				{getVariables}
				disabled={!!galleries[0].selectedOption}
				placeholder="Start typing to search for galleries"
				dataCy={dataCyPrefix}
				bind:galleries
			>
				{#snippet list()}
					<div
						class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
					>
						<CannotFindGallery />
					</div>
				{/snippet}

				{#snippet noResults()}
					<div class="flex flex-col items-center">
						<NoResults
							class="mb-2"
							dataCy={`${dataCy}-gallery-offerings-autocomplete`}
							>No gallery found</NoResults
						>
						<CannotFindGallery />
					</div>
				{/snippet}
			</GallerySelectorAutocomplete>
		</div>

		<div class="col-span-2">
			<ArtistsAutocomplete
				value={artistValue}
				bind:selectedOption={artistSelectedOption}
				dataCy={dataCyPrefix}
				name="artist"
			/>
		</div>

		<StepInput
			dataCy={`${dataCyPrefix}-received-date`}
			label="Received Date"
			class="col-span-2"
			placeholder="DD/MM/YYYY"
			id={AddArtlogicFieldNames.ReceivedDate}
			name={AddArtlogicFieldNames.ReceivedDate}
			type="date"
			required
			stopPropagationWhenPressingEnter
			error={$blur[AddArtlogicFieldNames.ReceivedDate] &&
				$errors[AddArtlogicFieldNames.ReceivedDate]}
			bind:value={$form[AddArtlogicFieldNames.ReceivedDate]}
			onblur={handleBlur(AddArtlogicFieldNames.ReceivedDate)}
			{...$constraints[AddArtlogicFieldNames.ReceivedDate]}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-sender`}
			label="Sender"
			placeholder="<EMAIL>"
			id={AddArtlogicFieldNames.Sender}
			name={AddArtlogicFieldNames.Sender}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddArtlogicFieldNames.Sender] &&
				$errors[AddArtlogicFieldNames.Sender]}
			bind:value={$form[AddArtlogicFieldNames.Sender]}
			onblur={handleBlur(AddArtlogicFieldNames.Sender)}
			{...$constraints[AddArtlogicFieldNames.Sender]}
			required={false}
		/>

		<StepInput
			dataCy={`${dataCyPrefix}-receiver`}
			label="Receiver"
			placeholder="<EMAIL>"
			id={AddArtlogicFieldNames.Receiver}
			name={AddArtlogicFieldNames.Receiver}
			class="col-span-2"
			stopPropagationWhenPressingEnter
			error={$blur[AddArtlogicFieldNames.Receiver] &&
				$errors[AddArtlogicFieldNames.Receiver]}
			bind:value={$form[AddArtlogicFieldNames.Receiver]}
			onblur={handleBlur(AddArtlogicFieldNames.Receiver)}
			{...$constraints[AddArtlogicFieldNames.Receiver]}
			required={false}
		/>

		<div class="col-span-2 mt-[0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-high-priority`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-high-priority-label`}
					name={AddArtlogicFieldNames.HighPriority}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddArtlogicFieldNames.HighPriority]}
				/>

				<div class="pl-1.5">High priority</div>
			</InputLabel>
		</div>

		<div class="scol-span-2 mt-[-0.5rem]">
			<InputLabel
				dataCy={`${dataCyPrefix}-includes-prices`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-includes-prices-label`}
					name={AddArtlogicFieldNames.IncludesPrices}
					class="mt-[-0.125rem]"
					bind:checked={$form[AddArtlogicFieldNames.IncludesPrices]}
				/>

				<div class="pl-1.5">Includes Prices</div>
			</InputLabel>
		</div>
	</StepContainer>

	<StepButtons
		backButtonProps={{
			href: `/${page.url.pathname.split('/')[1]}/${page.params.id}`,
		}}
		continueButtonProps={{
			disabled: !isFormValid($errors) || !galleries[0].selectedOption,
		}}
	>
		Submit
	</StepButtons>
</form>
