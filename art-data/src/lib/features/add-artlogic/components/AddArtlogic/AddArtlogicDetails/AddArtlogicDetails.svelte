<script lang="ts">
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { AddArtlogicFieldNames } from '$lib/features/add-artlogic/constants/add-artlogic-validation-schema';

	interface Props {
		dataCy: string;
		onSubmit: () => Promise<void>;
		class?: string;
		details:
			| (Record<AddArtlogicFieldNames, string> & {
					gallery?: OptionType;
					artist?: OptionType;
			  })
			| null
			| undefined;
	}

	let { dataCy, onSubmit, details = $bindable(), ...rest }: Props = $props();

	let submitting = $state(false);
	let dataCyPrefix = $derived(`${dataCy}-add-pdf-details`);

	const handleClickBack = () => {
		details = null;
	};

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		submitting = true;
		await onSubmit();
		submitting = false;
	};
</script>

<form
	onsubmit={handleSubmit}
	class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', rest.class)}
>
	<StepContainer>
		<Txt variant="h4" class="col-span-2 mb-2"
			>Please review before uploading</Txt
		>

		<div class="col-span-2">
			<div
				data-cy={dataCyPrefix}
				class="flex flex-col gap-1 rounded border border-gray-200 p-3"
			>
				<Txt
					variant="label3"
					dataCy={`${dataCyPrefix}-details`}
					class="text-left">Artlogic URL:</Txt
				>

				<a
					class="mb-3 flex items-center gap-1"
					href={details?.[AddArtlogicFieldNames.ArtlogicUrl]}
					target="_blank"
					rel="noopener noreferrer"
				>
					<Txt
						dataCy={`${dataCyPrefix}-received-date`}
						class={'text-left text-blue-500 underline'}
						variant="body2"
					>
						{details?.[AddArtlogicFieldNames.ArtlogicUrl]}
					</Txt>
					<ExternalIcon class="h-4 w-4" color="blue-500" />
				</a>

				<Txt
					dataCy={`${dataCyPrefix}-input-name`}
					class="text-left"
					variant="label3"
				>
					{details?.[AddArtlogicFieldNames.InputName]}
				</Txt>

				{#if details?.artist}
					<Txt
						dataCy={`${dataCyPrefix}-artist`}
						class={'text-left'}
						variant="body2"
					>
						Artist: {details?.artist?.line1?.replaceAll('//', ' ')}
					</Txt>
				{/if}

				<Txt
					dataCy={`${dataCyPrefix}-gallery`}
					class={'text-left'}
					variant="body2"
				>
					Gallery: {details?.gallery?.line1}
				</Txt>

				<Txt
					dataCy={`${dataCyPrefix}-received-date`}
					class={'text-left'}
					variant="body2"
				>
					Received Date: {dayjs(
						details?.[AddArtlogicFieldNames.ReceivedDate]
					).format('DD/MM/YYYY')}
				</Txt>

				<Txt
					dataCy={`${dataCyPrefix}-sender`}
					class={'text-left'}
					variant="body2"
				>
					Sender: {details?.[AddArtlogicFieldNames.Sender] || ''}
				</Txt>

				<Txt
					dataCy={`${dataCyPrefix}-receiver`}
					class={'text-left'}
					variant="body2"
				>
					Receiver: {details?.[AddArtlogicFieldNames.Receiver] || ''}
				</Txt>
			</div>
		</div>
	</StepContainer>

	<StepButtons
		onclick={handleClickBack}
		backButtonProps={{}}
		continueButtonProps={{
			loading: submitting,
		}}
	>
		Confirm
	</StepButtons>
</form>
