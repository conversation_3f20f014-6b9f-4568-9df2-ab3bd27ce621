import { z } from 'zod';

export enum AddArtlogicFieldNames {
	ArtlogicUrl = 'artlogic_url',
	InputName = 'input_name',
	ReceivedDate = 'received_date',
	Sender = 'sender',
	Receiver = 'receiver',
	HighPriority = 'high_priority',
	IncludesPrices = 'includes_prices',
}

export enum AddArtlogicErrorMessages {
	InvalidArtlogicUrl = 'Please enter a valid artlogic url',
	MissingInputName = 'Please enter an input name',
	InvalidReceivedDate = 'Please enter a valid received date',
	InvalidSender = 'Please enter a valid sender email address',
	InvalidReceiver = 'Please enter a valid receiver email address',
}

export const AddArtlogicValidationSchema = z.object({
	[AddArtlogicFieldNames.IncludesPrices]: z.boolean().optional(),
	[AddArtlogicFieldNames.HighPriority]: z.boolean().optional(),
	[AddArtlogicFieldNames.ArtlogicUrl]: z
		.string()
		.nonempty(AddArtlogicErrorMessages.InvalidArtlogicUrl)
		.url(AddArtlogicErrorMessages.InvalidArtlogicUrl)
		.refine(
			(val) => !val || val.includes('privateviews'),
			AddArtlogicErrorMessages.InvalidArtlogicUrl
		),
	[AddArtlogicFieldNames.InputName]: z
		.string()
		.nonempty(AddArtlogicErrorMessages.MissingInputName),
	[AddArtlogicFieldNames.ReceivedDate]: z
		.string()
		.nonempty(AddArtlogicErrorMessages.InvalidReceivedDate),
	[AddArtlogicFieldNames.Sender]: z.string().optional(),
	[AddArtlogicFieldNames.Receiver]: z.string().optional(),
});
