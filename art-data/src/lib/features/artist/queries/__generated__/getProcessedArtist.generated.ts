import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetProcessedArtistQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Processed_Artist_Filter>;
}>;

export type GetProcessedArtistQuery = {
	__typename?: 'Query';
	Processed_Artist: Array<{
		__typename?: 'Processed_Artist';
		processed_artist_id: string;
	}>;
};

export const GetProcessedArtistDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getProcessedArtist' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Processed_Artist_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Processed_Artist' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_artist_id' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetProcessedArtistQuery,
	GetProcessedArtistQueryVariables
>;
