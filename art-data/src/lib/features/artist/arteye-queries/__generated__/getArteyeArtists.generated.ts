import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-arteye';

export type GetArteyeArtistsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artist_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetArteyeArtistsQuery = {
	__typename?: 'Query';
	artist: Array<{
		__typename?: 'artist';
		id: string;
		reference_id?: number | null;
		person?: {
			__typename?: 'person';
			first_name?: string | null;
			last_name?: string | null;
			year_birth?: number | null;
			year_death?: number | null;
			nationalities?: Array<{
				__typename?: 'person_nationality';
				country?: {
					__typename?: 'location';
					name?: string | null;
					code: string;
				} | null;
			} | null> | null;
		} | null;
	}>;
};

export const GetArteyeArtistsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArteyeArtists' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artist_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artist' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'reference_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'person' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nationalities' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArteyeArtistsQuery,
	GetArteyeArtistsQueryVariables
>;
