<script lang="ts">
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import {
		type Option,
		type OptionClasses,
	} from '$global/components/QueryAutocomplete/types';

	interface Props {
		dataCy: string;
		option: Option;
		classes?: OptionClasses;
	}

	let { dataCy, option, classes = {} }: Props = $props();
</script>

<LinkOption
	{dataCy}
	{classes}
	option={{
		...option,
		line1: option.line1?.replaceAll('//', ' '),
		line3: option.line3?.replaceAll(
			':::',
			option.line3?.startsWith(':::') || option.line3?.endsWith(':::')
				? ''
				: ' - '
		),
	}}
/>
