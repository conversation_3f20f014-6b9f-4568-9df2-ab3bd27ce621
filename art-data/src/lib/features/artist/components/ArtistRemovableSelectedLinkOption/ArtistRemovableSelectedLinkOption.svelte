<script lang="ts">
	import {
		type Option,
		type OptionClasses,
	} from '$global/components/QueryAutocomplete/types';
	import { RemovableSelectedLinkOption } from '$lib/components/RemovableSelectedLinkOption';

	interface Props {
		handleClick?: undefined | (() => void);
		dataCy: string;
		option: Option;
		classes?: OptionClasses;
	}

	let {
		handleClick = undefined,
		dataCy,
		option,
		classes = {},
	}: Props = $props();
</script>

<RemovableSelectedLinkOption
	{handleClick}
	{dataCy}
	{classes}
	option={{
		...option,
		line1: option.line1?.replaceAll('//', ' '),
		line3: option.line3?.replaceAll(
			':::',
			option.line3?.startsWith(':::') || option.line3?.endsWith(':::')
				? ''
				: ' - '
		),
	}}
/>
