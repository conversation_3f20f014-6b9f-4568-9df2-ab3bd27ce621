<script lang="ts" module>
	export const formatExhibitionArtist = (artist: any) => ({
		line1: `${artist?.first_name}//${artist?.last_name}`,
		line2: `${Config.ArteyeDomain}/artists/${artist?.id}`,
		line3: [artist?.year_birth || '', artist?.year_death || ''].join(':::'),
		line4: artist?.nationality,
		line5: `Ref ID: ${artist.reference_id}`,
		...(artist.legacy_id && {
			line6: `Legacy ID: ${artist.legacy_id}`,
		}),
	});
</script>

<script lang="ts">
	import { type Writable } from 'svelte/store';
	import {
		GetArteyeArtistsDocument,
		type GetArteyeArtistsQuery,
	} from '../../arteye-queries/__generated__/getArteyeArtists.generated';
	import { ArtistOption } from '../ArtistOption';
	import { ArtistRemovableSelectedLinkOption } from '../ArtistRemovableSelectedLinkOption';
	import { CannotFindArtist } from './CannotFindArtist';
	import { page } from '$app/state';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { type Option } from '$global/components/QueryAutocomplete/types';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Config } from '$lib/constants/config';
	import { VARIOUS_ARTISTS_ID } from '$lib/constants/various_artists';
	import { GetLegacyIdDocument } from '$lib/custom-arteye-queries/__generated__/getLegacyId.generated';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import { gqlClientCustomArteye } from '$lib/gqlClientCustomArteye';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';

	interface Props {
		required?: boolean | undefined;
		name: string;
		dataCy: string;
		value: Writable<string>;
		selectedOption: Option | null | undefined;
	}

	let {
		required = undefined,
		name,
		dataCy,
		value = $bindable(),
		selectedOption = $bindable(),
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-artist`);

	const formatArtist = (
		artist: GetArteyeArtistsQuery['artist'][number],
		legacyId: string | null | undefined
	) => {
		return {
			line1: `${artist?.person?.first_name}//${artist?.person?.last_name}`,
			line2: `${Config.ArteyeDomain}/artists/${artist?.id}`,
			line3: [
				artist?.person?.year_birth || '',
				artist?.person?.year_death || '',
			].join(':::'),
			line4: artist?.person?.nationalities?.[0]?.country?.code,
			line5: `Ref ID: ${artist?.reference_id}`,
			...(legacyId && {
				line6: `Legacy ID: ${legacyId}`,
			}),
		};
	};

	const getOptions = async (value: string) => {
		const optionsRes = await gqlClientArteye.request(
			GetArteyeArtistsDocument,
			{
				limit: 5,
				filter: {
					_and: [
						...(() => {
							if (!value) {
								return [];
							}

							if (isUuidValid(value)) {
								return [{ id: { _eq: value } }];
							}

							if (!isNaN(+value)) {
								return [{ reference_id: { _eq: value } }];
							}

							return [
								{
									person: {
										entity: { name: { _icontains: value } },
									},
								},
							];
						})(),

						{ id: { _neq: VARIOUS_ARTISTS_ID } },
						{ status: { key: { _neq: 'archived' } } },
					],
				},
			},
			getAuthorizationHeaders({
				user: { access_token: page.data.user.arteye_token },
			})
		);

		if (isOnDev()) {
			return (optionsRes?.artist || [])?.map((artist, i) =>
				formatArtist(artist, '')
			);
		}

		const legacyIdPromises = (optionsRes?.artist || [])?.map((artist) =>
			gqlClientCustomArteye.request(
				GetLegacyIdDocument,
				{
					id: artist?.id,
					collection: 'artist',
				},
				getAuthorizationHeaders({
					user: { access_token: page.data.user.arteye_token },
				})
			)
		);

		const legacyIdsRes = await Promise.all(legacyIdPromises);
		return (optionsRes?.artist || [])?.map((artist, i) =>
			formatArtist(artist, legacyIdsRes[i]?.getLegacyId?.legacyId)
		);
	};
</script>

<PromiseAutocomplete
	label="Artist"
	description="If more than one artist is in this checklist, leave this field blank"
	{name}
	{required}
	dataCy={dataCyPrefix}
	placeholder="Search for artist or artist id"
	showResultsWhenEmpty
	{getOptions}
	classes={{
		listWithOptions: 'pb-[3rem]',
		longList: '!max-h-[244px] !min-h-[244px]',
		option: {},
		selectedOption: {},
	}}
	OptionComponent={ArtistOption}
	SelectedOptionComponent={ArtistRemovableSelectedLinkOption}
	{value}
	bind:selectedOption
>
	{#snippet list()}
		<div
			class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
		>
			<CannotFindArtist />
		</div>
	{/snippet}

	{#snippet noResults()}
		<div class="flex flex-col items-center">
			<NoResults
				class="mb-2"
				dataCy={`${dataCyPrefix}-exhibitions-autocomplete`}
				>No artist found</NoResults
			>
			<CannotFindArtist />
		</div>
	{/snippet}
</PromiseAutocomplete>
