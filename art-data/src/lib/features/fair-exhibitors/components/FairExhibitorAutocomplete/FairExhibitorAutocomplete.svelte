<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { QueryAutocomplete } from '$global/components/QueryAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Config } from '$lib/constants/config';
	import { CannotFindExhibitor } from '$lib/features/match-artworks-with-labels/components/CannotFindExhibitor';
	import { gqlClientArteye } from '$lib/gqlClientArteye';
	import {
		GetEntitiesDocument,
		type GetEntitiesQuery,
	} from '$lib/websites/scraped-data/arteye-queries/__generated__/getEntities.generated';

	interface Props {
		dataCy: string;
		selectedOption?: OptionType | null;
	}

	let { dataCy, selectedOption = $bindable(null) }: Props = $props();

	const value = writable('');
	const getVariables = (value: string) => {
		return {
			filter: {
				_and: [
					{
						status: { key: { _neq: 'archived' } },
					},
					{
						organisation: { id: { _nnull: true } },
					},
					...(value ? [{ name: { _icontains: value } }] : []),
				],
			},
		};
	};

	const getOptions = (data: GetEntitiesQuery | undefined) => {
		return (data?.entity || [])?.map((entity) => {
			const url = (() => {
				return `${Config.ArteyeDomain}/${
					entity?.gallery?.id ? 'galleries' : 'organisations'
				}/${entity?.gallery?.id || entity?.organisation?.id}`;
			})();

			return {
				line1: `${entity?.name}`,
				line1Suffix: entity?.organisation?.location?.name
					? `(${entity?.organisation?.location?.name})`
					: '',
				line2: url,
				line3: entity.id,
				line4: entity.organisation?.type?.[0]?.organisation_type_key?.name,
				line5: entity.organisation?.location?.name,
				line6: entity?.organisation?.id,
			};
		});
	};
</script>

<div
	class={classNames('relative', {
		'flex items-center justify-between rounded border border-gray-200 bg-white p-0 pl-3':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		{getOptions}
		showResultsWhenEmpty
		name="fair-exhibitors"
		requestHeaders={{
			authorization: `Bearer ${page.data.user.arteye_token}`,
		}}
		graphQlClient={gqlClientArteye}
		placeholder="Select exhibitors"
		{getVariables}
		document={GetEntitiesDocument}
		class="mt-0"
		classes={{
			listWithOptions: 'pb-[65px]',
			longList: '!max-h-[232px] !min-h-[232px]',
			selectedOption: {
				line1: 'font-[500]',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
				line6: 'hidden',
			},
			option: {
				line1: 'font-[500]',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
				line6: 'hidden',
			},
		}}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		dataCy={`${dataCy}-fair-exhibitor`}
		{value}
		bind:selectedOption
	>
		{#snippet list()}
			<div
				class="absolute bottom-0 left-0 w-full border-t border-gray-200 bg-white p-4"
			>
				<CannotFindExhibitor
					fairId={`${page.data.fair?.processed_fair?.processed_fair_id}`}
				/>
			</div>
		{/snippet}

		{#snippet noResults()}
			<div class="flex flex-col items-center">
				<NoResults class="mb-2" dataCy={`${dataCy}-fair-exhibitor-autocomplete`}
					>No exhibitor found</NoResults
				>
				<CannotFindExhibitor
					fairId={`${page.data.fair?.processed_fair?.processed_fair_id}`}
				/>
			</div>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<Button
			onclick={() => {
				selectedOption = null;
			}}
			dataCy={`${dataCy}-fair-exhibitor-delete`}
			class="ml-1 h-[2rem] w-[2rem] border-none px-0 [&>span]:translate-x-[-0.25rem] [&>span]:pr-1"
			variant="secondary"
		>
			<CrossIcon class="h-3 w-3" />
		</Button>
	{/if}
</div>
