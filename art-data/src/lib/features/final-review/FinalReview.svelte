<script lang="ts">
	import {
		createMutation,
		type CreateMutationResult,
	} from '@tanstack/svelte-query';
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import type { finalReviewServerLoad } from './utils/finalReviewServerLoad/finalReviewServerLoad';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Checkbox, type CheckboxValue } from '$global/components/Checkbox';
	import { Container } from '$global/components/Container';
	import { Image } from '$global/components/ImageCarousel/Image';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import { Radio } from '$global/components/Radio';
	import { Skeleton } from '$global/components/Skeleton';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { ErrorSuccessPage } from '$global/features/form/pages/ErrorSuccessPage';
	import { isIntegerOrLetterOrRomanNumberValid } from '$global/utils/isIntegerOrLetterOrRomanNumberValid/isIntegerOrLetterOrRomanNumberValid';
	import { scrollToTop } from '$global/utils/scrollToTop';
	import { ArtworksIds } from '$lib/components/ArtworksIds';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { VARIOUS_ARTISTS_ID } from '$lib/constants/various_artists';
	import { FinalReviewForm } from '$lib/features/final-review/components/FinalReviewForm';
	import { UpdateArtworkDetailsDocument } from '$lib/features/final-review/queries/__generated__/updateArtworkDetails.generated';
	import type { Artist, Artwork } from '$lib/features/final-review/types';
	import { getMutation } from '$lib/query-utils/getMutation';
	import type { getArtworkIds } from '$lib/utils/getArtworksIds/getArtworksIds';

	interface Props {
		data: Awaited<ReturnType<typeof finalReviewServerLoad>> & {
			name: string;
			galleryName?: string;
			artworksIds: ReturnType<typeof getArtworkIds>;
		};
		buttonProps: {
			label: string;
			href: string;
		};
		updateArtwork: CreateMutationResult<any, any, any>;
		updateChecklist: CreateMutationResult<any, any, any> | null;
		finalizeReview: (args: {
			data: {
				user: {
					access_token: string;
				} | null;
			};
			updateArtwork: CreateMutationResult<any, any, any>;
			updateChecklist: CreateMutationResult<any, any, any> | null;
			artworksCopy: Artwork[];
			selectedImages: (string | undefined)[];
			nbArtworksRemaining: number | undefined;
		}) => void;
		dataCy: string;
		crumbs: Crumb[];
	}

	let {
		data,
		buttonProps,
		updateArtwork,
		updateChecklist,
		finalizeReview,
		dataCy,
		crumbs,
	}: Props = $props();

	const dataCyPrefix = `${dataCy}-final-review`;

	const updateArtworkDetails = createMutation(
		getMutation(UpdateArtworkDetailsDocument, getAuthorizationHeaders(data))
	);

	let artworksMatches = $derived(data.artworksMatches);
	let artworks = $derived(data.artworks);
	let nationalities = $derived(data.nationalities);
	let artworkTypes = $derived(data.artworkTypes);
	let galleryName = $derived(data.galleryName);
	let currencies = $derived(data.currencies);
	let artworkSaleStatuses = $derived(data.artworkSaleStatuses);
	let editionNumberTypes = $derived(data.editionNumberTypes);
	let artworkTypesDimensions = $derived(data.artworkTypesDimensions);
	let nbArtworksRemaining = $derived(data.nbArtworksRemaining);
	let artworksIds = $derived(data.artworksIds);
	let loadingPerspectiveImage = $state(false);
	let pageNumber = $state(0);
	let selectedImages: (string | undefined)[] = $state([]);
	let artworksCopy: undefined | Artwork[] = $state([]);
	let collapseArtworkMatches: CheckboxValue = $state(false);
	let artworkMatchesOpen: boolean[] = $state([]);
	let submitted = $state(false);

	const getCurrentArtworks = () =>
		artworks.slice(pageNumber * 25, (pageNumber + 1) * 25);

	onMount(() => {
		artworksCopy = getCurrentArtworks();
		artworkMatchesOpen = artworksCopy.map(() => true);

		selectedImages = artworksCopy.map(
			(artwork) => artwork?.imageChoices?.[0]?.name
		);

		if (!artworksCopy.length) {
			submitted = true;
		}
	});

	let galleries = $derived(
		(artworks || []).map(() =>
			galleryName
				? [
						{
							value: writable(galleryName),
							selectedOption: {
								line1: galleryName,
							},
						},
					]
				: []
		)
	);

	let submitting = $state(false);

	let submitDisabled = $derived(
		artworksCopy?.some((artwork) => {
			if (artwork.skip) {
				return false;
			}

			if (
				artwork.edition_number &&
				!isIntegerOrLetterOrRomanNumberValid(artwork.edition_number)
			) {
				return true;
			}

			if (!artwork.various_artists) {
				return ((artwork.artists as { processed_artist: null }[]) || []).some(
					(artist) => !artist?.processed_artist
				);
			}

			return false;
		})
	);

	// const getHandleArteyeTodosChange = (i: number) => (e: Event) => {
	// 	artworksCopy[i]['arteye_todos'] = (
	// 		e.target as unknown as { value: string }
	// 	).value;
	// };

	const handleChangeCollapseArtworkMatches = (checked: CheckboxValue) => {
		if (artworksCopy) {
			artworkMatchesOpen = artworksCopy.map(() => !checked);
		}
	};

	const handleSubmit = async () => {
		submitting = true;

		try {
			const updateArtworkDetailsPromises = (artworksCopy || []).map(
				(artworkCopy) => {
					const {
						skip,
						various_artists,
						images,
						labelText,
						labelImages,
						imageChoices,
						has_multiple_pieces,
						manuallyAddedArtworkId,
						...restArtwork
					} = artworkCopy;

					const data = {
						...restArtwork,
						artists: (() => {
							if (various_artists) {
								return [{ processed_artist_id: VARIOUS_ARTISTS_ID }];
							}

							return restArtwork.artists?.map((artist) => {
								const { processed_artist, ...restArtist } = artist as Artist;

								return {
									...restArtist,
									...(processed_artist !== 'create' &&
										!!processed_artist && {
											processed_artist_id: processed_artist?.artist_id,
										}),
								};
							});
						})(),
					};

					return $updateArtworkDetails.mutateAsync({
						id: artworkCopy?.id,
						data: {
							...data,
							edition_number_type: data.edition_number_type?.key,
						},
					});
				}
			);

			await Promise.all(updateArtworkDetailsPromises);

			await finalizeReview({
				data,
				artworksCopy: artworksCopy || [],
				selectedImages,
				nbArtworksRemaining,
				updateArtwork,
				updateChecklist,
			});

			showToast({
				variant: 'success',
				message: 'Artwork details have been successfully updated and reviewed.',
			});

			pageNumber = pageNumber + 1;
			artworksCopy = getCurrentArtworks();

			if (!artworksCopy.length) {
				window.location.reload();
			} else {
				selectedImages = artworksCopy.map(
					(artwork) => artwork?.imageChoices?.[0]?.name
				);

				collapseArtworkMatches = collapseArtworkMatches === true;
				artworkMatchesOpen = artworksCopy.map(() => !collapseArtworkMatches);

				scrollToTop();
			}
		} catch {
			showToast({
				variant: 'success',
				message:
					'Something went wrong while updating the artwork details. Please contact customer support.',
			});

			submitting = false;
		}
	};
</script>

<PageBody class="bg-gray-100">
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix}>
		<div class="flex-end flex justify-between">
			<Txt variant="h4" class="mb-6">
				Review and submit artworks
				{#if nbArtworksRemaining}
					({artworks.length} out of {nbArtworksRemaining} remaining)
				{/if}
			</Txt>

			<div class="flex gap-4">
				<LinkButton
					dataCy={dataCyPrefix}
					variant="secondary"
					newTab
					size="md"
					href={page.url.href.replace(
						Routes.ReviewArtworks,
						Routes.ViewMatches
					)}
				>
					View Matches
				</LinkButton>

				{#if page.data.fileUrl}
					<LinkButton
						size="md"
						dataCy={`${dataCyPrefix}-download-pdf`}
						href={page.data.fileUrl}
						newTab
						icon
						variant="secondary"
					>
						{#snippet leading()}
							<DownloadIcon />
						{/snippet}
						download pdf
					</LinkButton>
				{/if}
			</div>
		</div>

		{#if artworksCopy}
			{#if artworksCopy.length}
				<div class="mb-6">
					<InputLabel dataCy="collapse-artwork-matches">
						<Checkbox
							dataCy="collapse-artwork-matches"
							onChange={handleChangeCollapseArtworkMatches}
							bind:checked={collapseArtworkMatches}
						/>
						Collapse artwork matches
					</InputLabel>
				</div>

				{#each artworksCopy as artwork, i}
					<FinalReviewForm
						class={i === artworksCopy.length - 1 ? 'mb-6' : 'mb-16'}
						galleries={galleries[i]}
						{dataCy}
						{artworkTypesDimensions}
						{nationalities}
						{currencies}
						{editionNumberTypes}
						{artworkTypes}
						{artworkSaleStatuses}
						galleriesDisabled
						artworkMatches={artworksMatches[i]['findArtworkMatches']}
						bind:loadingPerspectiveImage
						bind:collapseArtworkMatches
						bind:artwork={artworksCopy[i]}
						bind:open={artworkMatchesOpen[i]}
					>
						{#if artwork.images?.length}
							<Txt variant="body3" class="mb-[0.25rem] mt-2 font-[500]"
								>Images</Txt
							>
							<div class="grid grid-cols-2 gap-2">
								{#each artwork.images as image}
									{#if image?.directus_files_id}
										<Image
											image={image?.directus_files_id}
											dataCy={`${dataCyPrefix}-artwork-data-image`}
											class="max-h-[5.875rem] min-h-[5.875rem] min-w-[5.875rem] max-w-[5.875rem]"
										/>
									{/if}
								{/each}
							</div>
						{/if}

						{#if artwork.labelImages?.length}
							<Txt variant="body3" class="mb-[0.25rem] mt-2 font-[500]"
								>Label Image</Txt
							>
							<div class="grid grid-cols-2 gap-2">
								{#each artwork.labelImages as image}
									{#if image?.directus_files_id}
										<Image
											image={image?.directus_files_id}
											dataCy={`${dataCyPrefix}-artwork-data-label-image`}
											class="max-h-[5.875rem] min-h-[5.875rem] min-w-[5.875rem] max-w-[5.875rem]"
										/>
									{/if}
								{/each}
							</div>
						{/if}

						{#if artwork.imageChoices?.length}
							<Txt variant="body3" class="mb-[0.25rem] mt-3 font-[500]"
								>Artwork Image</Txt
							>
							<div class="grid grid-cols-2 gap-2">
								{#each artwork.imageChoices as imageChoice}
									{#if imageChoice?.directus_files_id}
										<div class="border border-gray-200 p-2">
											<InputLabel
												dataCy={`${dataCy}-include`}
												variant="label4"
												class="text-[10px]"
												classes={{ container: 'w-auto mb-3' }}
												><Radio
													size="sm"
													bind:group={selectedImages[i]}
													id={imageChoice.name}
													dataCy={`${dataCy}`}
													name={`image-choice-${i}`}
													value={imageChoice.name}
												/>{imageChoice.name}</InputLabel
											>

											<Image
												image={imageChoice?.directus_files_id}
												dataCy={`${dataCyPrefix}-artwork-data-image-choice`}
												class="max-h-[4.875rem] min-h-[4.875rem] min-w-[4.875rem] max-w-[4.875rem] [&>div>div]:max-h-[4.375rem] [&>div]:max-h-[4.375rem]"
											/>
										</div>
									{/if}

									{#if loadingPerspectiveImage}
										<Skeleton variant="rectangular" />
									{/if}
								{/each}
							</div>
						{/if}

						<!-- <div class="mt-3" slot="before-skip">
							<Input
								placeholder="Arteye Todos..."
								labelVariant="label4"
								classes={{ label: 'mb-[-0.25rem]', tooltip: 'translate-y-1.5' }}
								name="arteye-todos"
								rows={5}
								class="mb-2 resize-none text-[0.75rem] leading-[1rem] "
								bind:value={artworksCopy[i]['arteye_todos']}
								on:keyup={getHandleArteyeTodosChange(i)}
								dataCy={`${dataCyPrefix}-arteye-todos`}
								label="Arteye Todos"
								tooltip="If any further edits are needed in Arteye, for options that can't be edited in the ingestion app, capture them here. You'll be able to filter for these items in Arteye"
							/>
						</div> -->
					</FinalReviewForm>
				{/each}

				<div class="flex justify-end">
					<Button
						loading={submitting}
						disabled={submitDisabled}
						dataCy={`${dataCyPrefix}-submit`}
						size="md"
						onclick={handleSubmit}
					>
						submit
						{#snippet trailing()}
							<ChevronRightIcon />
						{/snippet}
					</Button>
				</div>
			{:else if submitted}
				<ErrorSuccessPage
					class="mx-auto flex min-h-0 max-w-[32.5rem] flex-col bg-white p-8"
					dataCy={dataCyPrefix}
					title="You have successfully submitted everything"
					variant="success"
					{buttonProps}
				>
					{#snippet afterLinks()}
						<ArtworksIds {artworksIds} />
					{/snippet}
				</ErrorSuccessPage>
			{/if}
		{/if}
	</Container>
</PageBody>
