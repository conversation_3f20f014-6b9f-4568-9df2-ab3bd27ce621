<script lang="ts">
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import { EditionInformationFieldNames } from '$lib/features/final-review/constants/edition-information-validation-schema';
	import type { Artwork } from '$lib/features/final-review/types';

	interface Props {
		dataCy: string;
		artwork: Artwork;
	}

	let { dataCy, artwork = $bindable() }: Props = $props();

	const handleEditionDescriptionChange = (e: {
		target: EventTarget | null;
	}) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.edition_description = newValue;
	};

	const handleRegularEditionSizeChange = (e: {
		target: EventTarget | null;
	}) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.regular_edition_size = newValue ? +newValue : null;
	};

	const handleArtistProofChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.artist_proof_size = newValue ? +newValue : null;
	};

	const handleHcChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.house_of_commerce_size = newValue ? +newValue : null;
	};

	const handleTotalEditionSizeChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.total_edition_size = newValue ? +newValue : null;
	};

	const handleSeriesSizeChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.series_size = newValue ? +newValue : null;
	};

	const handleOpenEditionChange = () => {
		artwork.open_edition = !artwork.open_edition;
	};

	let dataCyPrefix = $derived(`${dataCy}-edition-information`);
</script>

<div class="border-b border-gray-200">
	<div class="flex items-center gap-2 border-b border-gray-200 px-5">
		<div
			class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
		>
			<Txt variant="label4">4</Txt>
		</div>

		<Txt
			data-cy={`${dataCyPrefix}-title`}
			variant="h6"
			class={'py-4 text-left'}
		>
			Edition Information
		</Txt>
	</div>

	<div class="px-5 py-3">
		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-edition-description`}
			name={EditionInformationFieldNames.Description}
			label="Edition Description"
			class="mb-3"
			value={artwork?.edition_description}
			onkeyup={handleEditionDescriptionChange}
		/>

		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-regular-edition-size`}
			name={EditionInformationFieldNames.RegularSize}
			label="Regular Edition Size"
			class="mb-3"
			value={`${artwork?.regular_edition_size || ''}`}
			onkeydown={handleKeyDownNumbersOnly}
			onkeyup={handleRegularEditionSizeChange}
			onchange={handleRegularEditionSizeChange}
		/>

		<div class="mb-3 grid grid-cols-2 gap-x-10 gap-y-3">
			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-artist-proof-size`}
					name={EditionInformationFieldNames.ArtistProofSize}
					label="Artist proof size"
					value={`${artwork?.artist_proof_size || ''}`}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleArtistProofChange}
					onchange={handleArtistProofChange}
				/>
			</div>

			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-hc-size`}
					name={EditionInformationFieldNames.HCSize}
					label="HC size"
					value={`${artwork?.house_of_commerce_size || ''}`}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleHcChange}
					onchange={handleHcChange}
				/>
			</div>

			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-total-size`}
					name={EditionInformationFieldNames.TotalSize}
					label="Total edition size"
					value={`${artwork?.total_edition_size || ''}`}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleTotalEditionSizeChange}
					onchange={handleTotalEditionSizeChange}
				/>
			</div>

			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-series-size`}
					name={EditionInformationFieldNames.SeriesSize}
					label="Series size"
					value={`${artwork?.series_size || ''}`}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleSeriesSizeChange}
					onchange={handleSeriesSizeChange}
				/>
			</div>
		</div>

		<InputLabel variant="body3" dataCy={`${dataCy}-back-to-matching`}
			><Checkbox
				size="sm"
				checked={!!artwork?.open_edition}
				onChange={handleOpenEditionChange}
				dataCy={`${dataCy}-open-edition`}
				name={EditionInformationFieldNames.OpenEdition}
			/>This is an open edition</InputLabel
		>
	</div>
</div>
