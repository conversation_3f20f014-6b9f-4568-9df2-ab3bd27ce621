<script lang="ts">
	import { Checkbox } from '$global/components/Checkbox';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import type { Artwork } from '$lib/features/final-review/types';

	interface Props {
		dataCy: string;
		artwork: Artwork;
		children?: import('svelte').Snippet;
		beforeSkip?: import('svelte').Snippet;
	}

	let { dataCy, artwork, children, beforeSkip }: Props = $props();

	const toggleArtworkSkip = () => {
		artwork.skip = !artwork.skip;
	};

	const handleChangeArtworkText = (e: Event) => {
		artwork.description = (e.target as unknown as { value: string }).value;
	};

	let dataCyPrefix = $derived(`${dataCy}-artwork-data`);
</script>

<div class="px-3 py-4">
	<Txt variant="h6" class="mb-4">Artwork data</Txt>

	{@render children?.()}

	<Input
		labelVariant="label4"
		classes={{ label: 'mb-[-0.25rem] mt-4' }}
		name="description"
		rows={10}
		class="w-full resize-y text-[0.75rem] leading-[1rem] text-gray-500"
		value={artwork.description}
		onkeyup={handleChangeArtworkText}
		dataCy={`${dataCyPrefix}-description`}
		label="Description"
	/>

	{@render beforeSkip?.()}

	<div class="mb-2.5 mt-8 flex items-center">
		<InputLabel variant="body3" dataCy={`${dataCy}-skip`}
			><Checkbox
				size="sm"
				checked={artwork.skip}
				onChange={toggleArtworkSkip}
				id="skip"
				dataCy={`${dataCy}-skip`}
				name="option"
			/>Skip?</InputLabel
		>

		<InfoTooltip
			content="If there is a problem with this record and you would like to skip sending it to the processed database, click skip"
			dataCy={`${dataCy}-skip`}
		/>
	</div>
</div>
