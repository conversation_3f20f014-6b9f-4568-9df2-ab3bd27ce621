<script lang="ts">
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { isIntegerOrLetterOrRomanNumberValid } from '$global/utils/isIntegerOrLetterOrRomanNumberValid/isIntegerOrLetterOrRomanNumberValid';
	import { SaleInformationFieldNames } from '$lib/features/final-review/constants/sale-information-validation-schema';
	import type { Artwork } from '$lib/features/final-review/types';
	// import { GallerySelector } from '$lib/features/gallery-selector/components/GallerySelector';
	import type { Galleries } from '$lib/features/gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';

	interface Props {
		artwork: Artwork;
		galleries: Galleries;
		galleriesDisabled: boolean;
		dataCy: string;
		currencies: {
			code: string;
			name: string;
			symbol?: string | null;
		}[];
		editionNumberTypes: {
			key: string;
			name?: string | null;
		}[];
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
	}

	let {
		artwork = $bindable(),
		galleries = $bindable(),
		galleriesDisabled,
		dataCy,
		currencies,
		editionNumberTypes,
		artworkSaleStatuses,
	}: Props = $props();

	let editionNoBlur = $state(false);

	let dataCyPrefix = $derived(`${dataCy}-sale-information`);

	const handleChangePrice = (e: Event) => {
		const newValue = (e.target as unknown as { value: string })?.value;
		artwork.price = newValue ? +newValue : null;
	};

	const handleChangeEditionNumber = (e: Event) => {
		const newValue = (e.target as unknown as { value: string })?.value;
		artwork.edition_number = newValue;
	};

	const handleChangeStatus = (e: SelectChangeEvent) => {
		artwork.sale_status = e.detail.value;
	};

	const handleEditionNumberTypeChange = (e: SelectChangeEvent) => {
		const selectedEditionNumberType = editionNumberTypes.find(
			(editionNumberType) => editionNumberType?.key === e.detail.value
		);

		artwork.edition_number_type = { key: `${selectedEditionNumberType?.key}` };
	};

	const handleChangeCurrency = (e: SelectChangeEvent) => {
		const selectedCurrency = currencies.find(
			(currency) => currency.code === e.detail.value
		);

		artwork.currency = { code: `${selectedCurrency?.code}` };
	};

	// const toggleArtworkRemoved = () => {
	// 	artwork.artwork_removed = !artwork.artwork_removed;
	// };
</script>

<div>
	<div class="flex items-center gap-2 border-b border-gray-200 px-5">
		<div
			class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
		>
			<Txt variant="label4">5</Txt>
		</div>

		<Txt
			data-cy={`${dataCyPrefix}-title`}
			variant="h6"
			class={'py-4 text-left'}
		>
			Sale Information
		</Txt>
	</div>

	<div class="px-5 py-3">
		<!-- {#if galleries.length}
			<Txt class="mb-1" variant="body3">Gallery</Txt>
			<GallerySelector
				size="sm"
				dataCy={`${dataCyPrefix}-gallery`}
				bind:galleries
				disabled={galleriesDisabled}
			/>
		{/if} -->

		<Txt class="mb-1" variant="body3">Price</Txt>

		<div class="mb-2 flex w-full">
			<Select
				ariaLabel="Select a currency"
				options={currencies.map((currency) => ({
					value: currency.code,
					label: `${currency.code}`,
				}))}
				onchange={handleChangeCurrency}
				size="sm"
				value={artwork?.currency?.code || 'USD'}
				dataCy={`${dataCyPrefix}-currency`}
				name={SaleInformationFieldNames.Currency}
				class="min-w-[5rem] max-w-[5rem]  [&>button]:rounded-r-none"
			/>

			<Input
				onkeydown={handleKeyDownNumbersOnly}
				size="sm"
				placeholder="00"
				dataCy={`${dataCyPrefix}-price`}
				name={SaleInformationFieldNames.Price}
				classes={{
					label: 'mb-[-0.25rem]',
					wrapper: 'min-w-[calc(100%-5rem)] max-w-[calc(100%-5rem)]',
				}}
				value={`${artwork?.price || ''}`}
				class="rounded-l-none border-l-0"
				onkeyup={handleChangePrice}
				onchange={handleChangePrice}
			/>
		</div>

		<div class="mb-3 grid grid-cols-2 items-baseline gap-x-10 gap-y-3">
			<div class="relative">
				<Select
					ariaLabel="Select an edition number type"
					options={editionNumberTypes.map((editionNumberType) => ({
						value: editionNumberType?.key,
						label: `${editionNumberType?.name}`,
					}))}
					size="sm"
					classes={{ label: 'mb-1', tooltip: 'translate-y-[-2px]' }}
					dataCy={`${dataCyPrefix}-edition-number-type`}
					name={SaleInformationFieldNames.EditionNoType}
					label="Edition # type"
					labelVariant="body3"
					onchange={handleEditionNumberTypeChange}
					value={artwork.edition_number_type?.key}
					tooltip="This relates to the type of the edition number of the specific artwork (i.e. AP or Regular)"
				/>
			</div>

			<div class="max-h-[48px]">
				<Input
					error={artwork.edition_number &&
					!isIntegerOrLetterOrRomanNumberValid(artwork.edition_number) &&
					editionNoBlur
						? 'Please provide an integer or a roman number'
						: ''}
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]', tooltip: 'translate-y-1.5' }}
					dataCy={`${dataCyPrefix}-edition-number`}
					name={SaleInformationFieldNames.EditionNo}
					label="Edition no"
					value={artwork.edition_number}
					onkeyup={handleChangeEditionNumber}
					tooltip="This relates to the specific edition number of this (i.e. number 5 if it was 5/100)"
					onblur={() => {
						editionNoBlur = true;
					}}
				/>
			</div>
		</div>

		<Select
			ariaLabel="Select a status"
			options={artworkSaleStatuses.map(({ key, name }) => ({
				label: name,
				value: key,
			}))}
			value={artwork.sale_status}
			size="sm"
			onchange={handleChangeStatus}
			placeholder="- Select -"
			labelVariant="body3"
			classes={{ label: 'mb-1' }}
			dataCy={`${dataCyPrefix}-sale-status`}
			name={SaleInformationFieldNames.Status}
			label="Sale status"
			class="mb-3"
		/>

		<!-- <InputLabel variant="body3" dataCy={`${dataCy}-back-to-matching`}
			><Checkbox
				size="sm"
				checked={!!artwork.artwork_removed}
				onChange={toggleArtworkRemoved}
				dataCy={`${dataCy}-open-edition`}
				name={SaleInformationFieldNames.Removed}
			/>This was removed</InputLabel
		> -->
	</div>
</div>
