<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import type { Artist, QueryArtist } from '../../../../types';
	import { ArtistEntry } from './ArtistEntry';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Autocomplete } from '$global/components/QueryAutocomplete/Autocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { Option } from '$global/components/QueryAutocomplete/Option';
	import { SelectedOption } from '$global/components/QueryAutocomplete/SelectedOption';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { Config } from '$lib/constants/config';
	import { ArtistFieldNames } from '$lib/features/final-review/constants/artist-validation-schema';

	interface Props {
		onSelectArtist: (id: string) => void;
		artists: (Artist | null | undefined)[] | null | undefined;
		artistIndex: number;
		active: boolean;
		dataCy: string;
		artistFormMaxHeight: string;
		activeArtistIndex: number;
		nationalities: {
			__typename?: 'country';
			code: string;
			name: string;
			country_nationality?: string | null;
		}[];
		class?: string;
	}

	let {
		onSelectArtist,
		artists = $bindable(),
		artistIndex,
		active,
		dataCy,
		artistFormMaxHeight,
		activeArtistIndex = $bindable(),
		nationalities,
		...rest
	}: Props = $props();

	let name = $state(artists?.[artistIndex]?.name);
	let birth = $state(`${artists?.[artistIndex]?.year_birth || ''}`);
	let death = $state(`${artists?.[artistIndex]?.year_death || ''}`);
	let timer: ReturnType<typeof setTimeout> | undefined = $state(undefined);
	let loading = $state(false);
	let queryArtists: QueryArtist[] = $state([]);

	let params = $state({
		...(artists?.[artistIndex]?.name && {
			searchName: artists?.[artistIndex]?.name,
		}),
		...(artists?.[artistIndex]?.nationality?.name && {
			nationality: artists?.[artistIndex]?.nationality?.name,
		}),
		...(artists?.[artistIndex]?.year_birth && {
			yearOfBirth: artists?.[artistIndex]?.year_birth as number | null,
		}),
		...(artists?.[artistIndex]?.year_death && {
			yearOfDeath: artists?.[artistIndex]?.year_death as number | null,
		}),
		overallThreshold: 0.2,
		limit: 10,
	});

	$effect(() => {
		const fetchArtists = async () => {
			if (!params.searchName) {
				queryArtists = [];
				return Promise.resolve([]);
			}

			loading = true;

			try {
				const response = await fetch(
					`${Config.ArteyeGraphqlApiDomain}/advanced/artist-search`,
					{
						method: 'POST',
						body: JSON.stringify(params),
						headers: {
							'Content-Type': 'application/json',
						},
					}
				);

				const responseJson = await response.json();

				if (responseJson.errors) {
					queryArtists = [];
					return Promise.resolve([]);
				} else {
					queryArtists = responseJson;
					return Promise.resolve(responseJson);
				}
			} catch {
				queryArtists = [];
			} finally {
				loading = false;
			}
		};

		if (active) {
			fetchArtists().then((queryArtists: QueryArtist[]) => {
				const bestArtistMatch = queryArtists?.find(
					(queryArtist) => queryArtist?.overall_score >= 0.8
				);

				if (bestArtistMatch) {
					handleSelectArtist(bestArtistMatch);
				}
			});
		}
	});

	const handleDebouncedChangeNationality = (
		newNationality:
			| {
					__typename?: 'country';
					code: string;
					name: string;
					country_nationality?: string | null;
			  }
			| undefined
	) => {
		clearTimeout(timer);

		timer = setTimeout(() => {
			if (typeof newNationality !== 'undefined') {
				params.nationality = newNationality?.name;
			}
		}, 500);
	};

	const handleDebouncedChangeName = (newValue: string | undefined) => {
		clearTimeout(timer);

		timer = setTimeout(() => {
			if (typeof newValue !== 'undefined') {
				params.searchName = newValue;
			}
		}, 500);
	};

	const handleDebouncedChangeYearBirth = (newValue: number | null) => {
		clearTimeout(timer);

		timer = setTimeout(() => {
			params.yearOfBirth = newValue;
		}, 500);
	};

	const handleDebouncedChangeYearDeath = (newValue: number | null) => {
		clearTimeout(timer);

		timer = setTimeout(() => {
			params.yearOfDeath = newValue;
		}, 500);
	};

	const handleChangeName = (e: Event) => {
		if (artists?.[artistIndex]) {
			(artists[artistIndex] as { processed_artist: null }).processed_artist =
				null;
		}

		const newValue = (e.target as unknown as { value: string })
			?.value as string;

		(artists as { name: string }[])[artistIndex].name = newValue;
		handleDebouncedChangeName(newValue);
	};

	const handleChangeNationality = (e: { detail: { value: OptionType } }) => {
		const selectedNationality = nationalities.find(
			(nationality) => nationality.code === e.detail.value.line2
		);

		(artists as { nationality: { code: string } | undefined }[])[
			artistIndex
		].nationality = selectedNationality;
		handleDebouncedChangeNationality(selectedNationality);

		return Promise.resolve();
	};

	const handleUpdateBirth = (e: Event) => {
		const newValue = (e.target as unknown as { value: string })?.value;
		(artists as { year_birth: number | null }[])[artistIndex].year_birth =
			newValue ? +newValue : null;

		handleDebouncedChangeYearBirth(newValue ? +newValue : null);
	};

	const handleUpdateDeath = (e: Event) => {
		const newValue = (e.target as unknown as { value: string })?.value;
		(artists as { year_death: number | null }[])[artistIndex].year_death =
			newValue ? +newValue : null;

		handleDebouncedChangeYearDeath(newValue ? +newValue : null);
	};

	const handleClickCreateNew = () => {
		if (artists?.[artistIndex]) {
			(
				artists[artistIndex] as { processed_artist: 'create' }
			).processed_artist = 'create';

			if (!artistIndex) {
				onSelectArtist('');
			}
		}
	};

	const handleSelectArtist = (artist: QueryArtist) => {
		if (artists?.[artistIndex]) {
			(
				artists[artistIndex] as {
					processed_artist: QueryArtist;
				}
			).processed_artist = artist;

			if (!artistIndex) {
				onSelectArtist(artist.artist_id);
			}
		}
	};

	const handleRemoveArtist = () => {
		artists = [
			...(artists || []).slice(0, artistIndex),
			...(artists || []).slice(artistIndex + 1),
		];

		activeArtistIndex = 0;
	};

	let showRemoveButton = $derived((artists?.length || 0) > 1);
	let dataCyPrefix = $derived(`${dataCy}-form`);
	let createNewChecked = $derived(
		artists?.[artistIndex]?.processed_artist === 'create'
	);
	let otherArtistIds = $derived(
		(artists || [])
			?.map((artist, i) =>
				i === artistIndex
					? null
					: (artist?.processed_artist as { artist_id: string })?.artist_id
			)
			.filter(Boolean)
	);

	const value = writable('');
	let selectedOption: OptionType | null = $state(
		artists?.[artistIndex]?.nationality
			? {
					line1: artists?.[artistIndex]?.nationality?.name,
					line2: artists?.[artistIndex]?.nationality?.code,
				}
			: null
	);

	let options = $derived(
		nationalities
			.map((nationality) => ({
				line1: nationality.name,
				line2: nationality.code,
			}))
			.filter((nationality) => nationality.line1.toLowerCase().includes($value))
	);

	const handleKeyUp = (e: {
		target: (EventTarget & { value?: string | undefined }) | null;
	}) => {
		value.set(e.target?.value || '');
	};
</script>

<div
	style={artistFormMaxHeight}
	class={twMerge('relative flex h-full flex-col', rest.class)}
>
	<div class="flex-none border-b border-gray-200 px-5 pb-6">
		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-name`}
			name={ArtistFieldNames.Name}
			label="Name"
			class="mb-3"
			onkeyup={handleChangeName}
			bind:value={name}
		/>

		<Txt class="mb-1 text-[0.75rem]">Nationality</Txt>

		<Autocomplete
			size="sm"
			{value}
			bind:selectedOption
			onkeyup={handleKeyUp}
			onChange={handleChangeNationality}
			showResultsWhenEmpty
			dataCy={`${dataCyPrefix}-nationality`}
			placeholder="Select a nationality"
			name={ArtistFieldNames.Nationality}
			{options}
			classes={{
				listWithOptions: '[&>div>div]:max-h-[184px]',
				input: 'text-[0.75rem] sm:text-[0.75rem] py-1',
				option: {
					line1: classNames('font-[400] text-[0.75rem] sm:text-[0.75rem]'),
					line2: 'hidden',
				},
				selectedOption: {
					wrapper: 'py-1 bg-white [&_p]:line-clamp-1',
					line1: classNames('font-[400] text-[0.75rem] sm:text-[0.75rem]'),
					line2: '!hidden',
				},
			}}
			class="mb-3"
			OptionComponent={Option}
			SelectedOptionComponent={SelectedOption}
		>
			{#snippet noResults()}
				<NoResults
					class="text-left"
					dataCy={`${dataCy}-autocomplete-nationality`}
					>No country found.</NoResults
				>
			{/snippet}
		</Autocomplete>

		<div class="grid grid-cols-2 gap-x-2">
			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-birth`}
					name={ArtistFieldNames.Birth}
					label="Year of Birth"
					bind:value={birth}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleUpdateBirth}
					onchange={handleUpdateBirth}
				/>
			</div>

			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-death`}
					name={ArtistFieldNames.Death}
					label="Year of Death"
					bind:value={death}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleUpdateDeath}
					onchange={handleUpdateDeath}
				/>
			</div>
		</div>
	</div>

	<div class="relative flex-1 overflow-y-scroll bg-gray-100 px-5 py-4">
		<div class="mb-2 flex items-center justify-between">
			<Txt variant="label3">Potential matches</Txt>
			<InfoTooltip
				dataCy={dataCyPrefix}
				content="Please select an option to complete the artwork review"
			/>
		</div>

		{#if loading}
			<div class="flex justify-center">
				<CircularProgress dataCy={dataCyPrefix} />
			</div>
		{:else}
			{#each queryArtists as artist}
				{#if !otherArtistIds.includes(artist.artist_id)}
					<ArtistEntry
						{artists}
						{handleSelectArtist}
						{artist}
						index={artistIndex}
						dataCy={dataCyPrefix}
					/>
				{/if}
			{/each}

			<button
				type="button"
				onclick={handleClickCreateNew}
				class={classNames(
					'mb-2 flex w-full rounded border  bg-gray-0 p-2.5',
					createNewChecked ? 'border-gray-900' : 'border-gray-200',
					{
						'mb-[3.625rem]': showRemoveButton,
					}
				)}
			>
				<div
					role="button"
					tabindex={0}
					onkeyup={handleClickCreateNew}
					onclick={handleClickCreateNew}
					class="flex min-w-6 max-w-6 flex-1 flex-col justify-center self-stretch"
				>
					<Radio
						value="create"
						group={artists?.[artistIndex]?.processed_artist}
						onChange={handleClickCreateNew}
						dataCy={dataCyPrefix}
						class="max-h-[16px] max-w-[16px]"
					/>
				</div>

				<div class={classNames('flex-1 text-left')}>
					<Txt variant="label4">Create new</Txt>
				</div>
			</button>
		{/if}
	</div>

	{#if showRemoveButton}
		<div
			class="absolute bottom-0 left-0 flex w-full justify-center border-t border-gray-200 bg-white py-3"
		>
			<Button
				variant="secondary"
				onclick={handleRemoveArtist}
				dataCy={`${dataCyPrefix}-remove-artist`}
				size="sm">remove this artist</Button
			>
		</div>
	{/if}
</div>
