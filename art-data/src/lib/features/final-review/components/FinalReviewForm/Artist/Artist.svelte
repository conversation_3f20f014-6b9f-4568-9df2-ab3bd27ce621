<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import type { Artwork } from '../../../types';
	import { ArtistForm } from './ArtistForm';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { TabsHeader } from '$global/components/Tabs/TabsHeader';
	import { Txt } from '$global/components/Txt';
	import { MenuList } from '$lib/components/MenuList';
	import { MenuListItem } from '$lib/components/MenuListItem';
	import { MAX_ARTISTS } from '$lib/features/final-review/constants/artist-validation-schema';

	interface Props {
		onSelectArtist: (id: string) => void;
		activeArtistIndex: number;
		dataCy: string;
		lastColumnHeight: number | undefined;
		artwork: Artwork;
		nationalities: {
			__typename?: 'country';
			code: string;
			name: string;
			country_nationality?: string | null;
		}[];
	}

	let {
		onSelectArtist,
		activeArtistIndex = $bindable(),
		dataCy,
		lastColumnHeight,
		artwork = $bindable(),
		nationalities,
	}: Props = $props();

	let open = $state(false);

	const handleClickPlus = () => {
		open = true;
	};

	const handleClose = () => {
		open = false;
	};

	const setActiveTab = (index: number) => {
		activeArtistIndex = index;
	};

	let dataCyPrefix = $derived(`${dataCy}-artist`);
	let height = $derived(lastColumnHeight || 761);

	let tabs = $derived(
		(artwork.artists || []).map((_, index) => ({
			id: `artist-${index + 1}`,
			title: `Artist ${index + 1}`,
		}))
	);

	let artistFormMaxHeight = $derived(`max-height:calc(${height}px - 111px)`);

	let validationClasses = $derived(
		((artwork.artists as { processed_artist: null }[]) || [])
			.map((artist, i) =>
				artist?.processed_artist
					? null
					: `[&>button:nth-child(${i + 1})>p]:!text-red-500`
			)
			.filter(Boolean)
			.join(' ')
	);

	const handleSetVariousArtists = () => {
		onSelectArtist('');
		artwork.various_artists = true;

		if (!activeArtistIndex) {
			onSelectArtist('');
		}

		handleClose();
	};

	const handleClickRevert = () => {
		artwork.various_artists = false;

		const firstProcessedArtist = artwork.artists?.[0]?.processed_artist;
		if (!activeArtistIndex && firstProcessedArtist) {
			onSelectArtist(
				firstProcessedArtist === 'create'
					? 'create'
					: firstProcessedArtist?.artist_id
			);
		}
	};

	const handleAddArtist = () => {
		if (artwork.artists) {
			artwork.artists = [
				...artwork.artists,
				{ id: '', processed_artist: null },
			];
			activeArtistIndex = artwork.artists.length - 1;
			handleClose();
		}
	};

	onMount(() => {
		if (!artwork.artists?.length) {
			handleAddArtist();
		}
	});
</script>

<div style={`align-self:start; max-height: ${height}px; height: 100%`}>
	<div class="flex items-center gap-2 border-b border-gray-200 px-5">
		<div
			class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
		>
			<Txt variant="label4">2</Txt>
		</div>

		<Txt
			data-cy={`${dataCyPrefix}-title`}
			variant="h6"
			class={'py-4 text-left'}
		>
			Artist
		</Txt>
	</div>

	<div class="relative h-full">
		{#if artwork.various_artists}
			<div
				style={`max-height: ${height - 57}px;`}
				class="absolute left-0 top-0 z-20 flex h-full w-full items-center bg-gray-100 px-5"
			>
				<div class="rounded border border-gray-200 bg-gray-0 p-6">
					<Txt variant="label3" class="mb-1 text-center"
						>By "various artists" selected</Txt
					>
					<Txt variant="body3" class="mb-2 text-center">
						You have set this artwork to have been created by "various artists".
						This is when an artwork has over 5 artists.
					</Txt>
					<div class="flex justify-center">
						<Button
							dataCy={`${dataCyPrefix}-revert`}
							variant="secondary"
							onclick={handleClickRevert}
							size="sm"
						>
							revert back to 5 or less
						</Button>
					</div>
				</div>
			</div>
		{/if}

		<div class="h-[1px] w-full translate-y-[2.3125rem] bg-gray-200"></div>

		<div class="relative">
			<!-- Do not remove, here for tailwind compilation -->
			<!-- [&>button:nth-child(1)>p]:!text-red-500 -->
			<!-- [&>button:nth-child(2)>p]:!text-red-500 -->
			<!-- [&>button:nth-child(3)>p]:!text-red-500 -->
			<!-- [&>button:nth-child(4)>p]:!text-red-500 -->
			<!-- [&>button:nth-child(5)>p]:!text-red-500 -->
			<TabsHeader
				{tabs}
				variant="label4"
				{setActiveTab}
				activeTab={activeArtistIndex}
				{dataCy}
				classes={{
					inactiveTabLabel: 'text-gray-700',
					tabLabel: 'text-[10px] mb-2',
					button: 'border-b-0 z-10 pt-3',
					currentTabBorder: 'transform-none',
				}}
				class={`px-5 ${validationClasses}`}
			/>

			<Button
				{dataCy}
				size="sm"
				type="button"
				onclick={handleClickPlus}
				variant="secondary"
				class={'absolute right-3 top-1.5 max-h-[1.5rem] min-h-[1.5rem] min-w-[1.5rem] max-w-[1.5rem] px-0'}
			>
				<PlusIcon class="h-3 w-3" />
			</Button>

			{#if open}
				<MenuList class="left-[10rem] w-[12rem]" onClickOutside={handleClose}>
					{#if (artwork.artists?.length || 0) < 5}
						<MenuListItem onclick={handleAddArtist}>
							<Txt variant="body3">Add artist</Txt>
							<PlusIcon class="max-h-4 min-h-4 min-w-4 max-w-4" />
						</MenuListItem>
					{/if}

					<MenuListItem onclick={handleSetVariousArtists}>
						<div>
							<Txt variant="body3">Set as "various artists"</Txt>
							<Txt variant="body3" class="text-left text-[10px] text-gray-500"
								>When 5+ artists</Txt
							>
						</div>

						<PlusIcon class="max-h-4 min-h-4 min-w-4 max-w-4" />
					</MenuListItem>
				</MenuList>
			{/if}
		</div>

		<div class="mt-4 h-full" style={artistFormMaxHeight}>
			{#each Array(MAX_ARTISTS).fill(null) as _, i}
				<ArtistForm
					active={i === activeArtistIndex}
					{onSelectArtist}
					bind:artists={artwork.artists}
					bind:activeArtistIndex
					{nationalities}
					artistIndex={i}
					{artistFormMaxHeight}
					dataCy={dataCyPrefix}
					class={classNames({ hidden: i !== activeArtistIndex })}
				/>
			{/each}
		</div>
	</div>
</div>
