<script lang="ts">
	import classNames from 'classnames';
	import { type Artist, type QueryArtist } from '../../../../../types';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';

	interface Props {
		artist: QueryArtist;
		index: number;
		dataCy: string;
		artists: (Artist | null | undefined)[] | null | undefined;
		handleSelectArtist: (artist: QueryArtist) => void;
	}

	let { artist, index, dataCy, artists, handleSelectArtist }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-entry`);
	let currentArtistProcessedId = $derived(
		(artists?.[index]?.processed_artist as QueryArtist)?.artist_id
	);

	let checked = $derived(currentArtistProcessedId === artist?.artist_id);

	const handleChangeArtist = () => {
		handleSelectArtist(artist);
	};
</script>

<button
	type="button"
	onclick={handleChangeArtist}
	class={classNames(
		'mb-2 flex w-full rounded border  bg-gray-0 p-2.5',
		checked ? 'border-gray-900' : 'border-gray-200'
	)}
>
	<div class="flex min-w-6 max-w-6 flex-1 flex-col justify-center self-stretch">
		<Radio
			value={artist.artist_id}
			group={currentArtistProcessedId}
			onChange={handleChangeArtist}
			dataCy={dataCyPrefix}
			class="max-h-[16px] max-w-[16px]"
		/>
	</div>

	<div class="flex-1 text-left">
		<Txt variant="label4">{artist.artist_name}</Txt>
		{#if artist.year_birth}
			<Txt variant="body3"
				>{`${artist.year_birth}${
					artist?.year_death ? `- ${artist?.year_death}` : ''
				}`}</Txt
			>
		{/if}
		{#if artist.nationality}
			<Txt variant="body3">{(artist.nationality || []).join(', ')}</Txt>
		{/if}
		{#if artist.artwork_count !== undefined && artist.artwork_count !== null}
			<Txt variant="body3">{artist.artwork_count} artwork(s)</Txt>
		{/if}
	</div>

	<div
		class="flex min-w-[2.25rem] max-w-[2.25rem] flex-col justify-end self-stretch"
	>
		<div
			class="flex h-[1.5rem] w-full items-center justify-center rounded-full bg-green-500"
		>
			<Txt variant="label4" class="text-gray-0"
				>{artist.overall_score.toFixed(1)}</Txt
			>
		</div>
	</div>
</button>
