<script lang="ts">
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { ArtworkInformationFieldNames } from '$lib/features/final-review/constants/artwork-information-validation-schema';
	import type { Artwork } from '$lib/features/final-review/types';

	interface Props {
		dataCy: string;
		artwork: Artwork;
		artworkTypesDimensions: {
			description?: string | null;
			key: string;
			name?: string | null | undefined;
		}[];
	}

	let {
		dataCy,
		artwork = $bindable(),
		artworkTypesDimensions,
	}: Props = $props();

	const handleNumberOfPieces = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.number_of_pieces = newValue ? +newValue : null;
	};

	const values = ['Each artwork', 'Largest artwork', 'Overall size'];
	let blur = $state(false);

	const handleChangeDimensionsType = (newValue: string | number | boolean) => {
		artwork.dimension_type = { key: newValue as string };
	};
</script>

<div class="mt-3">
	<Input
		size="sm"
		labelVariant="body3"
		classes={{ label: 'mb-[-0.25rem]' }}
		dataCy={`${dataCy}-number-of-pieces`}
		name={ArtworkInformationFieldNames.NumberOfPieces}
		label="How many pieces are in this artwork?"
		required
		error={blur && !artwork.number_of_pieces
			? 'Please provide a number of pieces'
			: ''}
		onblur={() => {
			blur = true;
		}}
		onkeydown={handleKeyDownNumbersOnly}
		onkeyup={handleNumberOfPieces}
		onchange={handleNumberOfPieces}
		value={`${artwork.number_of_pieces || ''}`}
	/>

	<Txt variant="label4" class="mb-3 mt-4">What do the dimensions refer to?</Txt>
	<fieldset class="mb-3 flex flex-col gap-3">
		{#each artworkTypesDimensions as artworkTypesDimension}
			<InputLabel variant="body3" dataCy="test"
				><Radio
					group={artwork.dimension_type?.key}
					onChange={handleChangeDimensionsType}
					id="dimensions_type"
					dataCy={`${dataCy}-dimensions_type`}
					name="dimensions_type"
					value={artworkTypesDimension.key}
				/>{artworkTypesDimension.name}</InputLabel
			>
		{/each}
	</fieldset>
</div>
