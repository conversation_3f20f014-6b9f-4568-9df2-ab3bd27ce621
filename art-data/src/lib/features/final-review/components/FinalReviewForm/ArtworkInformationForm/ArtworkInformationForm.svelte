<script lang="ts">
	import { MultiplePieces } from './MultiplePieces';
	import { page } from '$app/state';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { ArtworkInformationFieldNames } from '$lib/features/final-review/constants/artwork-information-validation-schema';
	import { PerspectiveCropVisitImageWithDimensionsDocument } from '$lib/features/final-review/custom-queries/__generated__/perspectiveCropVisitImageWithDimensions.generated';
	import type { Artwork } from '$lib/features/final-review/types';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		onChangeTitle: (newValue: string) => void;
		dataCy: string;
		artwork: Artwork;
		loadingPerspectiveImage: boolean;
		artworkTypes: {
			key: string;
			name: string;
		}[];
		artworkTypesDimensions: {
			description?: string | null;
			key: string;
			name?: string | null | undefined;
		}[];
	}

	let {
		onChangeTitle,
		dataCy,
		artwork = $bindable(),
		loadingPerspectiveImage = $bindable(),
		artworkTypes,
		artworkTypesDimensions,
	}: Props = $props();

	let titleInputBlurred = $state(false);

	let params = $state({
		width: artwork.dimensions_width_cm,
		height: artwork.dimensions_height_cm,
	});

	const visitImageId = artwork.imageChoices?.[0]?.visitImageId;

	const updateCorrectedImage = (
		image: NonNullable<typeof artwork.imageChoices>[number]
	) => {
		if (artwork.imageChoices) {
			artwork.imageChoices[1] = image;
		}
	};

	$effect(() => {
		const fetchPerspectiveCorrectedImage = async () => {
			if (params.width && params.height && visitImageId) {
				loadingPerspectiveImage = true;
				try {
					const perspectiveImageResponse = await gqlClientCustom.request(
						PerspectiveCropVisitImageWithDimensionsDocument,
						{
							widthCm: Math.round(params.width),
							heightCm: Math.round(params.height),
							visitImageId,
						},
						getAuthorizationHeaders(
							page.data as { user: { access_token: string } }
						)
					);

					const perspectiveImage =
						perspectiveImageResponse?.perspectiveCropVisitImageWithDimensions;

					updateCorrectedImage({
						name: 'Corrected',
						id: perspectiveImage?.id,
						visitImageId: `${visitImageId}`,
						filename_download: `${perspectiveImage?.filename_download}`,
						storage: `${perspectiveImage?.storage}`,
						directus_files_id: {
							width: perspectiveImage?.width || 1,
							height: perspectiveImage?.height || 1,
							filename_disk: `${perspectiveImage?.filename_disk}`,
							alt: '',
							url: `${getImageUrl(
								perspectiveImage?.id,
								page.data.user.access_token
							)}`,
						},
					});
					// eslint-disable-next-line no-empty
				} catch {
				} finally {
					loadingPerspectiveImage = false;
				}
			}
		};

		fetchPerspectiveCorrectedImage();
	});

	const handleTitleChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.title = newValue;
		onChangeTitle(`${newValue}`);
	};

	const handleCridChange = (e: { target: EventTarget | null }) => {
		artwork.crid = (
			e.target as (EventTarget & { value: string }) | null
		)?.value;
	};

	const handleYearFromChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.executed_year_start = newValue ? +newValue : null;
	};

	const handleYearToChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.executed_year_end = newValue ? +newValue : null;
	};

	const handleMediaChange = (e: { target: EventTarget | null }) => {
		artwork.media = (
			e.target as (EventTarget & { value: string }) | null
		)?.value;
	};

	const handleArtworkTypeChange = (e: SelectChangeEvent) => {
		const artworkType = artworkTypes.find(
			(type) => type.key === e.detail.value
		);
		artwork.artwork_type = artworkType;
	};

	const handleDimensionsStringChange = (e: { target: EventTarget | null }) => {
		artwork.dimensions = (
			e.target as (EventTarget & { value: string }) | null
		)?.value;
	};

	const handleWidthChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.dimensions_width_cm = newValue ? +newValue : null;
		params.width = newValue ? +newValue : null;
	};

	const handleHeightChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.dimensions_height_cm = newValue ? +newValue : null;
		params.height = newValue ? +newValue : null;
	};

	const handleDepthChange = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.dimensions_depth_cm = newValue ? +newValue : null;
	};

	const handleNumberOfArtworks = (e: { target: EventTarget | null }) => {
		const newValue = (e.target as (EventTarget & { value: string }) | null)
			?.value;

		artwork.number_of_artworks = newValue ? +newValue : null;
	};

	const toggleMultiplePieces = () => {
		artwork.has_multiple_pieces = !artwork.has_multiple_pieces;
	};

	let dataCyPrefix = $derived(`${dataCy}-artwork-information`);
</script>

<div class="border-l border-gray-200">
	<div class="flex items-center gap-2 border-b border-gray-200 px-5">
		<div
			class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
		>
			<Txt variant="label4">3</Txt>
		</div>

		<Txt
			data-cy={`${dataCyPrefix}-title`}
			variant="h6"
			class={'py-4 text-left'}
		>
			Artwork Information
		</Txt>
	</div>

	<div class="px-5 py-3">
		<div class="mb-3">
			<Input
				size="sm"
				labelVariant="body3"
				classes={{ label: 'mb-[-0.25rem]' }}
				dataCy={`${dataCyPrefix}-title`}
				name={ArtworkInformationFieldNames.Title}
				label="Title"
				onblur={() => {
					titleInputBlurred = true;
				}}
				onkeyup={handleTitleChange}
				error={titleInputBlurred && !artwork.title
					? 'Please provide an artwork title'
					: ''}
				value={artwork.title}
			/>
		</div>

		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-title`}
			name={ArtworkInformationFieldNames.Crid}
			label="CRID"
			class="mb-3"
			onkeyup={handleCridChange}
			value={artwork.crid}
		/>

		<Txt variant="body3" class="mb-1">Year Executed</Txt>
		<div class="mb-3 grid grid-cols-2 items-center gap-2">
			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-year-from`}
					name={ArtworkInformationFieldNames.YearFrom}
					placeholder="From"
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleYearFromChange}
					onchange={handleYearFromChange}
					value={`${artwork.executed_year_start || ''}`}
				/>
			</div>

			<div>
				<Input
					size="sm"
					labelVariant="body3"
					classes={{ label: 'mb-[-0.25rem]' }}
					dataCy={`${dataCyPrefix}-year-to`}
					name={ArtworkInformationFieldNames.YearTo}
					placeholder="To"
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleYearToChange}
					onchange={handleYearToChange}
					value={`${artwork.executed_year_end || ''}`}
				/>
			</div>
		</div>

		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-media`}
			name={ArtworkInformationFieldNames.Media}
			label="Media"
			class="mb-3"
			onkeyup={handleMediaChange}
			value={artwork.media}
		/>

		<Select
			placeholder="Select an artwork type"
			ariaLabel="Select an artwork type"
			options={artworkTypes.map((artworkType) => ({
				value: artworkType.key,
				label: artworkType.name,
			}))}
			size="sm"
			classes={{ label: 'mb-1' }}
			dataCy={`${dataCyPrefix}-artwork-type`}
			name={ArtworkInformationFieldNames.ArtworkType}
			class="mb-3"
			label="Artwork type"
			labelVariant="body3"
			onchange={handleArtworkTypeChange}
			value={artwork.artwork_type?.key}
		/>

		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]', tooltip: 'h-4 w-4 translate-y-1' }}
			dataCy={`${dataCy}-number-of-artworks`}
			name={ArtworkInformationFieldNames.NumberOfArtworks}
			label="Number of artworks"
			class="mb-3"
			onkeydown={handleKeyDownNumbersOnly}
			onkeyup={handleNumberOfArtworks}
			onchange={handleNumberOfArtworks}
			value={`${artwork.number_of_artworks || ''}`}
			tooltip="When the artwork represents a bundle or full set of disparate artworks (i.e. not diptcyhs or triptychs), this is the number of artworks within the bundle or set."
		/>

		<Input
			size="sm"
			labelVariant="body3"
			classes={{ label: 'mb-[-0.25rem]' }}
			dataCy={`${dataCyPrefix}-dimensions-string`}
			name={ArtworkInformationFieldNames.DimensionsString}
			label="Dimensions String"
			class="mb-3"
			onkeyup={handleDimensionsStringChange}
			value={artwork.dimensions}
		/>

		<Txt variant="body3" class="mb-1">Dimensions</Txt>
		<div class="mb-3 grid grid-cols-3 gap-x-2">
			<div class="relative">
				<Input
					size="sm"
					labelVariant="body3"
					dataCy={`${dataCyPrefix}-dimensions-height`}
					name={ArtworkInformationFieldNames.DimensionsHeight}
					onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
					classes={{ helper: 'mt-1' }}
					disabled={loadingPerspectiveImage}
					onchange={handleHeightChange}
					value={`${artwork.dimensions_height_cm || ''}`}
				>
					{#snippet helper()}
						<span>Height</span>
					{/snippet}
				</Input>

				<Txt
					class="absolute right-2.5 top-[0.3125rem] text-[0.75rem] text-gray-500 sm:text-[0.75rem]"
					>cm</Txt
				>
			</div>

			<div class="relative">
				<Input
					size="sm"
					labelVariant="body3"
					dataCy={`${dataCyPrefix}-dimensions-width`}
					name={ArtworkInformationFieldNames.DimensionsWidth}
					onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
					disabled={loadingPerspectiveImage}
					classes={{ helper: 'mt-1' }}
					onchange={handleWidthChange}
					value={`${artwork.dimensions_width_cm || ''}`}
				>
					{#snippet helper()}
						<span>Width</span>
					{/snippet}
				</Input>

				<Txt
					class="absolute right-2.5 top-[0.3125rem] text-[0.75rem] text-gray-500 sm:text-[0.75rem]"
					>cm</Txt
				>
			</div>

			<div class="relative">
				<Input
					size="sm"
					labelVariant="body3"
					dataCy={`${dataCyPrefix}-dimensions-depth`}
					name={ArtworkInformationFieldNames.DimensionsDepth}
					onkeydown={(e) => handleKeyDownNumbersOnly(e, true)}
					classes={{ helper: 'mt-1' }}
					onchange={handleDepthChange}
					value={`${artwork.dimensions_depth_cm || ''}`}
				>
					{#snippet helper()}
						<span>Depth</span>
					{/snippet}
				</Input>

				<Txt
					class="absolute right-2.5 top-[0.3125rem] text-[0.75rem] text-gray-500 sm:text-[0.75rem]"
					>cm</Txt
				>
			</div>
		</div>

		<InputLabel variant="body3" dataCy={`${dataCy}-multiple-pieces`}
			><Checkbox
				size="sm"
				onChange={toggleMultiplePieces}
				checked={artwork.has_multiple_pieces}
				dataCy={`${dataCy}-multiple-pieces`}
				name={ArtworkInformationFieldNames.MultiplePieces}
			/>Artwork contains multiple pieces eg. diptych</InputLabel
		>

		{#if artwork.has_multiple_pieces}
			<MultiplePieces
				{artworkTypesDimensions}
				dataCy={dataCyPrefix}
				bind:artwork
			/>
		{/if}
	</div>
</div>
