<script lang="ts">
	import classNames from 'classnames';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Accordion, AccordionItem } from '$global/components/Accordion';
	import { type CheckboxValue } from '$global/components/Checkbox';
	import { Txt } from '$global/components/Txt';
	import { WhiteBorderImageContainer } from '$global/components/WhiteBorderImageContainer';
	import { Config } from '$lib/constants/config';
	import { type FindArtworkMatchesQuery } from '$lib/features/final-review/custom-queries/__generated__/findArtworkMatches.generated';
	import type { Artwork } from '$lib/features/final-review/types';
	import { getArteyeImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		artwork: Artwork;
		collapseArtworkMatches: CheckboxValue;
		artworkMatches: FindArtworkMatchesQuery['findArtworkMatches'];
		dataCy: string;
		open: boolean;
	}

	let {
		artwork = $bindable(),
		artworkMatches,
		collapseArtworkMatches = $bindable(false),
		dataCy,
		open = $bindable(true),
	}: Props = $props();

	const handleClickArtworkMatch = (
		artworkMatch: NonNullable<
			FindArtworkMatchesQuery['findArtworkMatches']
		>[number]
	) => {
		artwork.processed_artwork_id = artworkMatch?.id;
	};

	let dataCyPrefix = $derived(`${dataCy}-artwork-matches`);
</script>

{#key open}
	<Accordion>
		<AccordionItem
			dataCy={dataCyPrefix}
			defaultOpen={open}
			onclick={() => {
				open = !open;
				collapseArtworkMatches = 'indeterminate';
			}}
			classes={{ titleButton: 'px-5 border-b border-gray-200' }}
		>
			{#snippet titleSlot()}
				<div class="flex items-center gap-2">
					<div
						class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100"
					>
						<Txt variant="label4">1</Txt>
					</div>

					<Txt
						data-cy={`${dataCyPrefix}-title`}
						variant="h6"
						class={'py-4 text-left'}
					>
						Artwork matches
					</Txt>
				</div>
			{/snippet}

			<div
				class="flex h-[19.25rem] gap-10 overflow-hidden overflow-x-scroll px-5 py-4"
			>
				{#if artworkMatches}
					{#if !artworkMatches.length}
						<div class="flex w-full items-center justify-center">
							<Txt variant="label2" class="text-center">
								No artwork matches found for this artist and artwork title
							</Txt>
						</div>
					{:else}
						{#each artworkMatches.slice(0, 5) as artworkMatch}
							<div class="w-[20%]">
								<button
									type="button"
									onclick={() => handleClickArtworkMatch(artworkMatch)}
									class={classNames('w-full outline-none')}
								>
									<WhiteBorderImageContainer
										class={classNames(
											'h-[7.5rem] rounded-t border border-b-0 border-gray-200 p-[0.5rem] [&>div]:max-h-[7.5rem]',
											{
												'border-gray-700':
													artwork.processed_artwork_id === artworkMatch?.id,
											}
										)}
										image={{
											width: artworkMatch?.primary_image?.width || 1,
											height: artworkMatch?.primary_image?.height || 1,
											url: `${getArteyeImageUrl(
												artworkMatch?.primary_image?.id,
												page.data.user.arteye_token
											)}`,
										}}
										alt={''}
										whiteBorderFix
										dataCy={dataCyPrefix}
									/>

									<div
										class={classNames(
											'border-x-200 rounded-b border-x border-b p-2.5',
											{
												'border-gray-700':
													artwork.processed_artwork_id === artworkMatch?.id,
											}
										)}
									>
										<Txt variant="body3" class="line-clamp-1 text-gray-500">
											{artworkMatch?.artists
												?.map(
													(artist) =>
														`${artist?.person?.first_name}${
															artist?.person?.last_name
																? ` ${artist?.person?.last_name}`
																: ''
														}`
												)
												.join(', ')}
										</Txt>
										<Txt variant="body3" class="line-clamp-1 text-gray-500">
											{artworkMatch?.title}
										</Txt>
										{#if artworkMatch?.execution_start_year}
											<Txt variant="body3" class="line-clamp-1 text-gray-500">
												{artworkMatch?.execution_start_year}{artworkMatch?.execution_end_year
													? `- ${artworkMatch?.execution_end_year}`
													: ''}
											</Txt>
										{/if}
										{#if artworkMatch?.dimensions_height_cm || artworkMatch?.dimensions_width_cm}
											<Txt variant="body3" class="line-clamp-1 text-gray-500">
												{artworkMatch?.dimensions_height_cm} x {artworkMatch?.dimensions_width_cm}{artworkMatch?.dimensions_depth_cm
													? ` x ${artworkMatch?.dimensions_depth_cm}`
													: ''} cm
											</Txt>
										{/if}
										{#if artwork?.media}
											<Txt
												variant="body3"
												class="mb-1 line-clamp-2 min-h-[32px] text-gray-500"
											>
												{artworkMatch?.media}
											</Txt>
										{/if}

										<div class="flex justify-end">
											<div
												class="flex h-[1.5rem] w-[2.25rem] items-center justify-center rounded-full bg-green-500"
											>
												<Txt variant="label4" class="text-gray-0"
													>{artworkMatch.score.toFixed(1)}</Txt
												>
											</div>
										</div>
									</div>
								</button>
								<div class="mt-1 flex justify-end">
									<a
										target="_blank"
										rel="noopener noreferrer"
										href={`${Config.ArteyeDomain}/artworks-and-activities/artwork/${artworkMatch?.id}`}
									>
										<ExternalIcon class="h-4 w-4" />
									</a>
								</div>
							</div>
						{/each}
					{/if}
				{/if}
			</div>
		</AccordionItem>
	</Accordion>
{/key}
