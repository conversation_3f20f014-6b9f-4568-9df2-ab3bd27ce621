<script lang="ts">
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import type { FindArtworkMatchesQuery } from '../../custom-queries/__generated__/findArtworkMatches.generated';
	import type { Artwork } from '../../types';
	import { Artist } from './Artist';
	import { ArtworkData } from './ArtworkData';
	import { ArtworkInformationForm } from './ArtworkInformationForm';
	import { ArtworkMatches } from './ArtworkMatches';
	import { EditionInformationForm } from './EditionInformationForm';
	import { SaleInformationForm } from './SaleInformationForm';
	import { type CheckboxValue } from '$global/components/Checkbox';
	import type { Galleries } from '$lib/features/gallery-selector/components/GallerySelector/GallerySelectorAutocomplete';

	interface Props {
		galleriesDisabled: boolean;
		galleries: Galleries;
		dataCy: string;
		artwork: Artwork;
		artworkMatches: FindArtworkMatchesQuery['findArtworkMatches'];
		loadingPerspectiveImage: boolean;
		artworkTypesDimensions: {
			description?: string | null;
			key: string;
			name?: string | null | undefined;
		}[];
		artworkSaleStatuses: {
			__typename?: 'artwork_activity_status_type';
			key: string;
			name: string;
		}[];
		nationalities: {
			__typename?: 'country';
			code: string;
			name: string;
			country_nationality?: string | null;
		}[];
		artworkTypes: {
			key: string;
			name: string;
		}[];
		currencies: {
			code: string;
			name: string;
			symbol?: string | null;
		}[];
		editionNumberTypes: {
			key: string;
			name?: string | null;
		}[];
		class?: string;
		open: boolean;
		collapseArtworkMatches: CheckboxValue;
		children: import('svelte').Snippet;
		beforeSkip?: import('svelte').Snippet;
	}

	let {
		artworkSaleStatuses,
		nationalities,
		artworkTypes,
		currencies,
		editionNumberTypes,
		galleriesDisabled,
		collapseArtworkMatches = $bindable(false),
		open = $bindable(false),
		galleries = $bindable(),
		dataCy,
		artwork = $bindable(),
		artworkMatches,
		loadingPerspectiveImage = $bindable(),
		artworkTypesDimensions,
		children,
		beforeSkip: beforeSkipSnippet,
		...rest
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-form`);

	let activeArtistIndex = $state(0);
	let lastColumnHeight: number | undefined = $state(undefined);

	const activeProcessedArtistId = writable<string>('');
	const artworkTitle = writable<string>(artwork.title || '');

	const onChangeTitle = (newValue: string) => artworkTitle.set(newValue);
	const onSelectArtist = (id: string) => activeProcessedArtistId.set(id);
</script>

<div
	class={twMerge(
		'grid grid-cols-5 border border-gray-200 bg-[white] shadow-md',
		rest.class
	)}
>
	<div class="col-span-1 border-r border-gray-200 shadow-lg">
		<ArtworkData dataCy={dataCyPrefix} {artwork}>
			{@render children?.()}
			{#snippet beforeSkip()}
				{@render beforeSkipSnippet?.()}
			{/snippet}
		</ArtworkData>
	</div>
	<div class="col-span-4">
		<ArtworkMatches
			bind:collapseArtworkMatches
			{artworkMatches}
			dataCy={dataCyPrefix}
			bind:artwork
			bind:open
		/>

		<div class="grid grid-cols-3 border-t border-gray-200">
			<Artist
				{activeArtistIndex}
				{lastColumnHeight}
				{nationalities}
				bind:artwork
				dataCy={dataCyPrefix}
				{onSelectArtist}
			/>
			<ArtworkInformationForm
				{artworkTypes}
				{artworkTypesDimensions}
				bind:artwork
				bind:loadingPerspectiveImage
				{onChangeTitle}
				dataCy={dataCyPrefix}
			/>
			<div
				class="border-l border-gray-200"
				bind:clientHeight={lastColumnHeight}
			>
				<EditionInformationForm dataCy={dataCyPrefix} bind:artwork />
				<SaleInformationForm
					dataCy={dataCyPrefix}
					bind:galleries
					bind:artwork
					{currencies}
					{editionNumberTypes}
					{artworkSaleStatuses}
					{galleriesDisabled}
				/>
			</div>
		</div>
	</div>
</div>
