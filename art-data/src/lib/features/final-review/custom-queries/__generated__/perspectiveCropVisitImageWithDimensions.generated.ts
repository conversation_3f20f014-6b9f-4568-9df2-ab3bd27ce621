import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type PerspectiveCropVisitImageWithDimensionsMutationVariables =
	Types.Exact<{
		heightCm: Types.Scalars['Int']['input'];
		visitImageId: Types.Scalars['ID']['input'];
		widthCm: Types.Scalars['Int']['input'];
	}>;

export type PerspectiveCropVisitImageWithDimensionsMutation = {
	__typename?: 'Mutation';
	perspectiveCropVisitImageWithDimensions: {
		__typename?: 'directus_files';
		description?: string | null;
		filename_disk?: string | null;
		filename_download?: string | null;
		height?: number | null;
		id: string;
		storage?: string | null;
		title?: string | null;
		type?: string | null;
		width?: number | null;
	};
};

export const PerspectiveCropVisitImageWithDimensionsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'perspectiveCropVisitImageWithDimensions' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'heightCm' },
					},
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'visitImageId' },
					},
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'widthCm' },
					},
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'perspectiveCropVisitImageWithDimensions',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'heightCm' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'heightCm' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'visitImageId' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'visitImageId' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'widthCm' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'widthCm' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'filename_disk' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'filename_download' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'height' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'storage' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	PerspectiveCropVisitImageWithDimensionsMutation,
	PerspectiveCropVisitImageWithDimensionsMutationVariables
>;
