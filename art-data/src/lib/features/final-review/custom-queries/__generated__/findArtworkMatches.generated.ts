import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-custom';

export type FindArtworkMatchesQueryVariables = Types.Exact<{
	imageId: Types.Scalars['ID']['input'];
}>;

export type FindArtworkMatchesQuery = {
	__typename?: 'Query';
	findArtworkMatches: Array<{
		__typename?: 'ArtworkMatch';
		dimensions_depth_cm?: number | null;
		dimensions_height_cm?: number | null;
		dimensions_width_cm?: number | null;
		id: string;
		score: number;
		title?: string | null;
		media?: string | null;
		execution_end_year?: number | null;
		execution_start_year?: number | null;
		primary_image?: {
			__typename?: 'directus_files';
			description?: string | null;
			filename_disk?: string | null;
			filename_download?: string | null;
			height?: number | null;
			id: string;
			storage?: string | null;
			title?: string | null;
			type?: string | null;
			width?: number | null;
		} | null;
		artists?: Array<{
			__typename?: 'Artist';
			id: string;
			person?: {
				__typename?: 'Person';
				first_name?: string | null;
				id: string;
				last_name?: string | null;
				year_birth?: number | null;
				year_death?: number | null;
				nationalities?: Array<{
					__typename?: 'Location';
					code: string;
					country_nationality?: string | null;
					name: string;
				} | null> | null;
			} | null;
		} | null> | null;
	}>;
};

export const FindArtworkMatchesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'findArtworkMatches' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'imageId' },
					},
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'findArtworkMatches' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'imageId' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'imageId' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_depth_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_height_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_width_cm' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'score' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'media' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'primary_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'execution_end_year' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'execution_start_year' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationalities' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	FindArtworkMatchesQuery,
	FindArtworkMatchesQueryVariables
>;
