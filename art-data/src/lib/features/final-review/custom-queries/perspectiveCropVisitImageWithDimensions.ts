import { gql } from 'graphql-tag';

export const MUTATION = gql`
	mutation perspectiveCropVisitImageWithDimensions(
		$heightCm: Int!
		$visitImageId: ID!
		$widthCm: Int!
	) {
		perspectiveCropVisitImageWithDimensions(
			heightCm: $heightCm
			visitImageId: $visitImageId
			widthCm: $widthCm
		) {
			description
			filename_disk
			filename_download
			height
			id
			storage
			title
			type
			width
		}
	}
`;
