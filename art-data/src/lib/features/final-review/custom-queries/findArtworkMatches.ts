import { gql } from 'graphql-tag';

export const QUERY = gql`
	query findArtworkMatches($imageId: ID!) {
		findArtworkMatches(imageId: $imageId) {
			dimensions_depth_cm
			dimensions_height_cm
			dimensions_width_cm
			id
			score
			title
			media
			primary_image {
				description
				filename_disk
				filename_download
				height
				id
				storage
				title
				type
				width
			}
			execution_end_year
			execution_start_year
			artists {
				id
				person {
					first_name
					id
					last_name
					year_birth
					year_death
					nationalities {
						code
						country_nationality
						name
					}
				}
			}
		}
	}
`;
