import type { WhiteBorderImage } from '$global/components/WhiteBorderImageContainer';
import type { GetArtworkDetailsQuery } from '$lib/queries/__generated__/getArtworkDetails.generated';

export type QueryArtist = {
	artwork_count: string;
	artist_id: string;
	artist_name: string;
	overall_score: number;
	name_similarity: number;
	nationality: string[];
	year_birth: number;
	year_death: number;
	nationality_match: boolean;
	year_of_birth_match: boolean;
	year_of_death_match: boolean;
};

export type Artist = NonNullable<
	GetArtworkDetailsQuery['Artwork_Details'][number]['artists']
>[number] & {
	processed_artist: QueryArtist | null | 'create';
};

export type Artwork = GetArtworkDetailsQuery['Artwork_Details'][number] & {
	skip: boolean;
	id: string;
	various_artists: boolean;
	manuallyAddedArtworkId: string | null | undefined;
	description: string | null | undefined;
	has_multiple_pieces: boolean;
	artists?: (Artist | null | undefined)[] | undefined | null;
	labelText?: string | null;
	labelImages?:
		| ({
				directus_files_id: WhiteBorderImage & {
					alt: string;
					filename_disk: string;
				};
		  } | null)[]
		| null
		| undefined;
	images?:
		| ({
				id: string;
				filename_download: string;
				storage: string;
				directus_files_id: WhiteBorderImage & {
					alt: string;
					filename_disk: string;
				};
		  } | null)[]
		| null
		| undefined;
	imageChoices?:
		| ({
				id: string;
				filename_download: string;
				name: string;
				visitImageId: string;
				storage: string;
				directus_files_id: WhiteBorderImage & {
					alt: string;
					filename_disk: string;
				};
		  } | null)[]
		| null
		| undefined;
};
