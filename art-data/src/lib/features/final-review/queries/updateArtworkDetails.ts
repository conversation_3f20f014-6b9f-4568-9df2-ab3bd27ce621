import { gql } from 'graphql-request';

export const UPDATE_ARTWORK_DETAILS = gql`
	mutation updateArtworkDetails(
		$id: ID!
		$data: update_Artwork_Details_input!
	) {
		update_Artwork_Details_item(id: $id, data: $data) {
			id
			date_created
			artists {
				name
			}
			edition_number_legacy
			artist_text
			description
			title
			media
			crid
			executed_year_start
			executed_year_end
			series_size
			dimensions
			dimensions_width_cm
			dimensions_depth_cm
			dimensions_height_cm
			dimension_type {
				key
				name
			}
			number_of_pieces
			number_of_artworks
			edition_description
			edition_number
			edition_is_unknown
			regular_edition_size
			house_of_commerce_size
			artist_proof_size
			total_edition_size
			general_proof_size
			open_edition
			edition_is_numbered
			estimate_low
			estimate_high
			price
			sale_status
			sale_date
			sale_date_tz
			edition_number_type {
				key
				name
			}
			currency {
				code
				name
			}
			price_includes_premium
			is_full_set
			is_bundle
			open_edition
			is_bundle
			lot_number
			lot_attributes {
				artwork_lot_symbol_lookup_key {
					key
				}
			}
		}
	}
`;
