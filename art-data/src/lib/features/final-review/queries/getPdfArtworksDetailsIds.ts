import { gql } from 'graphql-tag';

export const GET_PDF_ARTWORKS_DETAILS_IDS = gql`
	query getPdfArtworksDetailsIds(
		$filter: PDF_filter
		$limit: Int
		$offset: Int
		$artworkFilter: PDF_Artwork_filter
	) {
		PDF(filter: $filter) {
			title
			submitted_for_review
			pdf_file {
				id
			}
			receipt_info {
				receive_date
			}
			processed_fair_exhibitor_org {
				entity
				id
				location
				name
				type
			}
			artworks(limit: $limit, filter: $artworkFilter, offset: $offset) {
				id
				images {
					directus_files_id {
						id
						filename_download
						storage
						width
						height
						filename_disk
					}
				}
				artwork_details {
					id
					sale_status
					description
				}
				status {
					key
				}
				created_artwork_id
				processed_activity_id
			}
			processed_artist {
				processed_artist_id
			}
			artworks_total: artworks(limit: 100000, filter: $artworkFilter) {
				id
			}
		}
	}
`;
