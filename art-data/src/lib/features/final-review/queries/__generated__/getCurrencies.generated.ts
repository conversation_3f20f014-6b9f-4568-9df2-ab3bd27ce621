import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetCurrenciesQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetCurrenciesQuery = {
	__typename?: 'Query';
	currency: Array<{
		__typename?: 'currency';
		code: string;
		name: string;
		symbol?: string | null;
	}>;
};

export const GetCurrenciesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getCurrencies' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'currency' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '2000' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'symbol' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetCurrenciesQuery, GetCurrenciesQueryVariables>;
