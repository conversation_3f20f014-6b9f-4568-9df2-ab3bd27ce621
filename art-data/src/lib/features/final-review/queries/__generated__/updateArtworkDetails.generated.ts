import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type UpdateArtworkDetailsMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Artwork_Details_Input;
}>;

export type UpdateArtworkDetailsMutation = {
	__typename?: 'Mutation';
	update_Artwork_Details_item?: {
		__typename?: 'Artwork_Details';
		id: string;
		date_created?: any | null;
		edition_number_legacy?: string | null;
		artist_text?: string | null;
		description?: string | null;
		title?: string | null;
		media?: string | null;
		crid?: string | null;
		executed_year_start?: number | null;
		executed_year_end?: number | null;
		series_size?: number | null;
		dimensions?: string | null;
		dimensions_width_cm?: number | null;
		dimensions_depth_cm?: number | null;
		dimensions_height_cm?: number | null;
		number_of_pieces?: number | null;
		number_of_artworks?: number | null;
		edition_description?: string | null;
		edition_number?: string | null;
		edition_is_unknown?: boolean | null;
		regular_edition_size?: number | null;
		house_of_commerce_size?: number | null;
		artist_proof_size?: number | null;
		total_edition_size?: number | null;
		general_proof_size?: number | null;
		open_edition?: boolean | null;
		edition_is_numbered?: boolean | null;
		estimate_low?: number | null;
		estimate_high?: number | null;
		price?: number | null;
		sale_status?: string | null;
		sale_date?: any | null;
		sale_date_tz?: string | null;
		price_includes_premium?: boolean | null;
		is_full_set?: boolean | null;
		is_bundle?: boolean | null;
		lot_number?: string | null;
		artists?: Array<{
			__typename?: 'Artist_Details';
			name?: string | null;
		} | null> | null;
		dimension_type?: {
			__typename?: 'Artwork_Dimension_Type';
			key: string;
			name?: string | null;
		} | null;
		edition_number_type?: {
			__typename?: 'Edition_Number_Type';
			key: string;
			name?: string | null;
		} | null;
		currency?: { __typename?: 'currency'; code: string; name: string } | null;
		lot_attributes?: Array<{
			__typename?: 'Artwork_Details_artwork_lot_symbol_lookup';
			artwork_lot_symbol_lookup_key?: {
				__typename?: 'artwork_lot_symbol_lookup';
				key: string;
			} | null;
		} | null> | null;
	} | null;
};

export const UpdateArtworkDetailsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateArtworkDetails' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_Artwork_Details_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_Artwork_Details_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number_legacy' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'artist_text' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'media' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'executed_year_start' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'executed_year_end' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'series_size' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'dimensions' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_width_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_depth_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_height_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimension_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_pieces' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_description' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_is_unknown' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'regular_edition_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'house_of_commerce_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_proof_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'total_edition_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'general_proof_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'open_edition' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_is_numbered' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'estimate_low' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'estimate_high' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'price' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_date' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_date_tz' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_includes_premium' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'is_full_set' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_bundle' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'open_edition' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'is_bundle' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_number' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lot_attributes' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'artwork_lot_symbol_lookup_key',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateArtworkDetailsMutation,
	UpdateArtworkDetailsMutationVariables
>;
