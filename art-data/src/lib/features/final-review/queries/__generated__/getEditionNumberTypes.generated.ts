import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetEditionNumberTypesQueryVariables = Types.Exact<{
	[key: string]: never;
}>;

export type GetEditionNumberTypesQuery = {
	__typename?: 'Query';
	Edition_Number_Type: Array<{
		__typename?: 'Edition_Number_Type';
		key: string;
		name?: string | null;
	}>;
};

export const GetEditionNumberTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getEditionNumberTypes' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Edition_Number_Type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetEditionNumberTypesQuery,
	GetEditionNumberTypesQueryVariables
>;
