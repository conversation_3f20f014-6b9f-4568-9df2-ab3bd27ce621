import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetLocationsQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetLocationsQuery = {
	__typename?: 'Query';
	country: Array<{
		__typename?: 'country';
		code: string;
		name: string;
		country_nationality?: string | null;
	}>;
};

export const GetLocationsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getLocations' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'country' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '2000' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'country_nationality' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetLocationsQuery, GetLocationsQueryVariables>;
