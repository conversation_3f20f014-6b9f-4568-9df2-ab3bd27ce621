import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetArtworkDimensionTypesQueryVariables = Types.Exact<{
	[key: string]: never;
}>;

export type GetArtworkDimensionTypesQuery = {
	__typename?: 'Query';
	Artwork_Dimension_Type: Array<{
		__typename?: 'Artwork_Dimension_Type';
		key: string;
		name?: string | null;
	}>;
};

export const GetArtworkDimensionTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkDimensionTypes' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Artwork_Dimension_Type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkDimensionTypesQuery,
	GetArtworkDimensionTypesQueryVariables
>;
