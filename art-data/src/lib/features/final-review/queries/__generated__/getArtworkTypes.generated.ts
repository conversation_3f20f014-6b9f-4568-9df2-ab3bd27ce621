import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetArtworkTypesQueryVariables = Types.Exact<{
	[key: string]: never;
}>;

export type GetArtworkTypesQuery = {
	__typename?: 'Query';
	Artwork_Type: Array<{
		__typename?: 'Artwork_Type';
		key: string;
		name: string;
	}>;
};

export const GetArtworkTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkTypes' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Artwork_Type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkTypesQuery,
	GetArtworkTypesQueryVariables
>;
