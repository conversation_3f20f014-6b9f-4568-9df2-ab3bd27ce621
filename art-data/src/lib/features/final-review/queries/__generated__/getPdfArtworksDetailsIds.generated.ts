import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetPdfArtworksDetailsIdsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Pdf_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	artworkFilter?: Types.InputMaybe<Types.Pdf_Artwork_Filter>;
}>;

export type GetPdfArtworksDetailsIdsQuery = {
	__typename?: 'Query';
	PDF: Array<{
		__typename?: 'PDF';
		title: string;
		submitted_for_review?: boolean | null;
		pdf_file?: { __typename?: 'directus_files'; id: string } | null;
		receipt_info?: {
			__typename?: 'Receipt_Information';
			receive_date?: any | null;
		} | null;
		processed_fair_exhibitor_org?: {
			__typename?: 'Processed_Organisation';
			entity?: string | null;
			id: string;
			location?: string | null;
			name: string;
			type?: string | null;
		} | null;
		artworks?: Array<{
			__typename?: 'PDF_Artwork';
			id: string;
			created_artwork_id?: string | null;
			processed_activity_id?: string | null;
			images?: Array<{
				__typename?: 'PDF_Artwork_files';
				directus_files_id?: {
					__typename?: 'directus_files';
					id: string;
					filename_download: string;
					storage: string;
					width?: number | null;
					height?: number | null;
					filename_disk?: string | null;
				} | null;
			} | null> | null;
			artwork_details?: {
				__typename?: 'Artwork_Details';
				id: string;
				sale_status?: string | null;
				description?: string | null;
			} | null;
			status?: { __typename?: 'Artwork_Status_Type'; key: string } | null;
		} | null> | null;
		processed_artist?: {
			__typename?: 'Processed_Artist';
			processed_artist_id: string;
		} | null;
		artworks_total?: Array<{
			__typename?: 'PDF_Artwork';
			id: string;
		} | null> | null;
	}>;
};

export const GetPdfArtworksDetailsIdsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getPdfArtworksDetailsIds' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PDF_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'artworkFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PDF_Artwork_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'PDF' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'submitted_for_review' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'pdf_file' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'receipt_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'receive_date' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_exhibitor_org' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'limit' },
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'artworkFilter' },
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'offset' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'offset' },
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'directus_files_id',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_details' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_status' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'created_artwork_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_activity_id' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_artist' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_artist_id' },
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'artworks_total' },
									name: { kind: 'Name', value: 'artworks' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '100000' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'artworkFilter' },
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetPdfArtworksDetailsIdsQuery,
	GetPdfArtworksDetailsIdsQueryVariables
>;
