import { gql } from 'graphql-tag';

export const GET_MANUALLY_ADDED_ARTWORKS_DETAILS_IDS = gql`
	query getManuallyAddedArtworksDetailsIds(
		$filter: Manual_Upload_filter
		$artworkFilter: Manually_Added_Artwork_filter
		$limit: Int
		$offset: Int
	) {
		Manual_Upload(filter: $filter) {
			title
			receipt_info {
				receive_date
			}
			processed_fair_exhibitor_org {
				entity
				id
				location
				name
				type
			}
			manually_added_artworks(
				filter: $artworkFilter
				limit: $limit
				offset: $offset
			) {
				id
				images {
					directus_files_id {
						id
						storage
						filename_download
						width
						height
						filename_disk
					}
				}
				artwork_details {
					id
					description
					sale_status
				}
				status {
					key
				}
				created_artwork_id
				processed_activity_id
			}
			manually_added_artworks_total: manually_added_artworks(
				filter: $artworkFilter
				limit: 100000
			) {
				id
			}
		}
	}
`;
