import { z } from 'zod';

export enum SaleInformationFieldNames {
	Price = 'price',
	EditionNo = 'editionNo',
	EditionNoType = 'editionNoType',
	Status = 'status',
	Removed = 'removed',
	Currency = 'currency',
}

export enum SaleInformationErrorMessages {
	Gallery = 'Please specify a gallery',
	Price = 'Please provide a price',
	EditionNo = 'Please provide an edition number',
	Status = 'Please provide a valid status',
}

export const SaleInformationValidationSchema = z.object({
	[SaleInformationFieldNames.Price]: z
		.number()
		.min(1, SaleInformationErrorMessages.Price),
	[SaleInformationFieldNames.EditionNo]: z
		.number()
		.min(1, SaleInformationErrorMessages.EditionNo),
	[SaleInformationFieldNames.Currency]: z.string(),
	[SaleInformationFieldNames.Status]: z.string().nonempty(),
	[SaleInformationFieldNames.Removed]: z.boolean().optional(),
});
