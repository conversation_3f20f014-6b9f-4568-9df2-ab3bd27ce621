import { z } from 'zod';

export enum EditionInformationFieldNames {
	Description = 'description',
	RegularSize = 'regularSize',
	ArtistProofSize = 'artistProofSize',
	HCSize = 'hcsize',
	TotalSize = 'totalSize',
	SeriesSize = 'seriesSize',
	OpenEdition = 'openEdition',
}

export enum EditionInformationErrorMessages {
	Description = 'Please provide a description',
	RegularSize = 'Please provide a regular edition size',
	ArtistProofSize = 'Please provide an artist proof size',
	HCSize = 'Please provide an HC size',
	TotalSize = 'Please provide a total size',
	SeriesSize = 'Please provide a series size',
}

export const EditionInformationValidationSchema = z.object({
	[EditionInformationFieldNames.Description]: z
		.string()
		.nonempty(EditionInformationErrorMessages.Description),
	[EditionInformationFieldNames.RegularSize]: z
		.string()
		.nonempty(EditionInformationErrorMessages.RegularSize),
	[EditionInformationFieldNames.ArtistProofSize]: z
		.string()
		.nonempty(EditionInformationErrorMessages.ArtistProofSize),
	[EditionInformationFieldNames.HCSize]: z
		.string()
		.nonempty(EditionInformationErrorMessages.HCSize),
	[EditionInformationFieldNames.SeriesSize]: z
		.string()
		.nonempty(EditionInformationErrorMessages.SeriesSize),
});
