import { z } from 'zod';

export enum ArtworkInformationFieldNames {
	Title = 'title',
	YearFrom = 'yearFrom',
	YearTo = 'yearTo',
	Media = 'media',
	Crid = 'crid',
	ArtworkType = 'artworkType',
	DimensionsString = 'dimensionsString',
	DimensionsHeight = 'dimensionsHeight',
	DimensionsWidth = 'dimensionsWidth',
	DimensionsDepth = 'dimensionsDepth',
	MultiplePieces = 'multiplePieces',
	NumberOfPieces = 'numberOfPieces',
	NumberOfArtworks = 'numberOfArtworks',
}

export enum ArtworkInformationErrorMessages {
	Title = 'Please provide an artwork title',
	YearFrom = 'Please provide a valid year of execution',
	Media = 'Please provide a valid media',
	ArtworkType = 'Please provide a valid artwork type',
	DimensionsString = 'Please provide a valid dimensions string',
	DimensionsHeight = 'Please provide a valid dimensions height',
	DimensionsWidth = 'Please provide a valid dimensions width',
}

export const ArtworkInformationValidationSchema = z.object({
	[ArtworkInformationFieldNames.Title]: z
		.string()
		.nonempty(ArtworkInformationErrorMessages.Title),
	[ArtworkInformationFieldNames.YearFrom]: z
		.string()
		.regex(/^[0-9]{4}$/, ArtworkInformationErrorMessages.YearFrom)
		.nonempty(ArtworkInformationErrorMessages.YearFrom),
	[ArtworkInformationFieldNames.YearTo]: z
		.string()
		.regex(/^[0-9]{4}$/, ArtworkInformationErrorMessages.YearFrom)
		.optional(),
	[ArtworkInformationFieldNames.Media]: z
		.string()
		.nonempty(ArtworkInformationErrorMessages.Media),
	[ArtworkInformationFieldNames.ArtworkType]: z
		.string()
		.nonempty(ArtworkInformationErrorMessages.ArtworkType),
	[ArtworkInformationFieldNames.DimensionsString]: z
		.string()
		.nonempty(ArtworkInformationErrorMessages.DimensionsString),
	[ArtworkInformationFieldNames.DimensionsHeight]: z
		.number()
		.min(1, ArtworkInformationErrorMessages.DimensionsHeight),
	[ArtworkInformationFieldNames.DimensionsWidth]: z
		.number()
		.min(1, ArtworkInformationErrorMessages.DimensionsWidth),
	[ArtworkInformationFieldNames.DimensionsDepth]: z.number().optional(),
	[ArtworkInformationFieldNames.MultiplePieces]: z.boolean(),
});
