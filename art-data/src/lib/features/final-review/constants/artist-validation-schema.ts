import { z } from 'zod';

export const MAX_ARTISTS = 5;

export enum ArtistFieldNames {
	Name = 'name',
	Nationality = 'nationality',
	Birth = 'birth',
	Death = 'death',
}

export enum ArtistErrorMessages {
	Name = 'Please provide an artist name',
	Nationality = 'Please provide a valid nationality',
	Birth = 'Please provide a valid year of birth',
	Death = 'Please provide an valid year of death',
}

export const ArtistValidationSchema = z.object({
	[ArtistFieldNames.Name]: z.string().nonempty(ArtistErrorMessages.Name),
	[ArtistFieldNames.Nationality]: z
		.string()
		.nonempty(ArtistErrorMessages.Nationality),
	[ArtistFieldNames.Birth]: z
		.string()
		.regex(/^[0-9]{4}$/, ArtistErrorMessages.Birth)
		.nonempty(ArtistErrorMessages.Birth),
	[ArtistFieldNames.Death]: z
		.string()
		.regex(/^[0-9]{4}$/, ArtistErrorMessages.Death)
		.optional(),
});
