import { error } from '@sveltejs/kit';
import { GetManuallyAddedArtworksDetailsIdsDocument } from '../../queries/__generated__/getManuallyAddedArtworksDetailsIds.generated';
import { finalReviewServerLoad } from '../finalReviewServerLoad/finalReviewServerLoad';
import type { WhiteBorderImage } from '$global/components/WhiteBorderImageContainer';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import type { Artwork } from '$lib/features/final-review/types';
import { gqlClient } from '$lib/gqlClient';
import {
	GetArtworkDetailsDocument,
	type GetArtworkDetailsQuery,
} from '$lib/queries/__generated__/getArtworkDetails.generated';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getArtworkIds } from '$lib/utils/getArtworksIds/getArtworksIds';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const manualUploadfinalReviewServerLoad = async <
	T extends () => Promise<{
		user: { access_token: string; arteye_token?: string } | null;
	}>,
>({
	parent,
	params,
}: {
	parent: T;
	params: {
		id: string;
		manualUploadId: string;
	};
}) => {
	const parentData = await parent();

	const manualUploadResponse = await gqlClient.request(
		GetManuallyAddedArtworksDetailsIdsDocument,
		{
			filter: { id: { _eq: params.manualUploadId } },
			limit: -1,
			artworkFilter: {
				status: {
					key: {
						_in: [
							'AWAITING_REVIEW',
							'REVIEWED_AND_SUBMITTED',
							'INGESTION_FAILED',
							'COMPLETED',
						],
					},
				},
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const manualUpload = manualUploadResponse?.Manual_Upload?.[0];

	if (!manualUpload) {
		error(404, 'This manual upload does not exist');
	}

	// TODO art-data status: check the checklist is available for this feature
	// if (
	// 	![
	// 		ChecklistStatus.AwaitingReview.toLowerCase(),
	// 		ChecklistStatus.ReviewedAndSubmitted.toLowerCase(),
	// 	].includes(manualUpload?.status?.name?.toLowerCase() as string)
	// ) {
	// 	error(404, 'This manual upload is not awaiting review');
	// }

	const manuallyAddedArtworks = manualUpload?.manually_added_artworks
		?.filter((artwork) => artwork?.status?.key === 'AWAITING_REVIEW')
		?.map((manuallyAddedArtwork) => ({
			...manuallyAddedArtwork,
			images: manuallyAddedArtwork?.images?.map((image) => ({
				...image,
				id: `${image?.directus_files_id?.id}`,
				storage: `${image?.directus_files_id?.storage}`,
				filename_download: `${image?.directus_files_id?.filename_download}`,
				directus_files_id: {
					...(image?.['directus_files_id'] || {}),
					alt: '',
					url: getImageUrl(image?.directus_files_id?.id),
				} as WhiteBorderImage & {
					alt: string;
					filename_disk: string;
				},
			})),
		}));

	const artworksDetailsIds = manuallyAddedArtworks?.map(
		(manuallyAddedArtwork) => manuallyAddedArtwork?.artwork_details?.id
	) as string[];

	let artworks: Artwork[] = [];

	if (artworksDetailsIds.length) {
		const artworksResponse = await gqlClient.request(
			GetArtworkDetailsDocument,
			{
				filter: { id: { _in: artworksDetailsIds } },
			},
			getAuthorizationHeaders(parentData)
		);

		artworks = (manuallyAddedArtworks || []).map((manuallyAddedArtwork) => {
			const correspondingArtworkDetails =
				artworksResponse?.Artwork_Details?.find(
					(artworkDetails) =>
						manuallyAddedArtwork?.artwork_details?.id === artworkDetails?.id
				);

			return {
				...(correspondingArtworkDetails as NonNullable<
					typeof correspondingArtworkDetails
				>),
				skip: false,
				various_artists: false,
				manuallyAddedArtworkId: manuallyAddedArtwork?.id,
				images: manuallyAddedArtwork?.images,
				description: `${correspondingArtworkDetails?.description || ''}`,
				has_multiple_pieces:
					(correspondingArtworkDetails?.number_of_pieces || 0) > 1,
				artists: (correspondingArtworkDetails?.artists || []).map(
					(
						artistDetail: NonNullable<
							GetArtworkDetailsQuery['Artwork_Details'][number]['artists']
						>[number]
					) => ({
						...artistDetail,
						id: artistDetail?.id || '',
						processed_artist: null,
					})
				),
			};
		});
	}

	const additionalCrumb = buildUploadCrumb(
		manualUpload?.receipt_info?.receive_date,
		manualUpload?.processed_fair_exhibitor_org?.name
	);

	return {
		artworksIds: getArtworkIds(
			manualUpload?.manually_added_artworks as Parameters<
				typeof getArtworkIds
			>[0]
		),
		...(await finalReviewServerLoad({
			additionalCrumb,
			fileUrl: null,
			parentData,
			artworks,
			nbArtworksRemaining: manualUpload?.manually_added_artworks?.filter(
				(artwork) => artwork?.status?.key === 'AWAITING_REVIEW'
			)?.length,
		})),
	};
};
