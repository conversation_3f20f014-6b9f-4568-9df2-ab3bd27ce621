import { FindArtworkMatchesDocument } from '../../custom-queries/__generated__/findArtworkMatches.generated';
import { GetArtworkDimensionTypesDocument } from '../../queries/__generated__/getArtworkDimensionTypes.generated';
import { GetArtworkTypesDocument } from '../../queries/__generated__/getArtworkTypes.generated';
import { GetCurrenciesDocument } from '../../queries/__generated__/getCurrencies.generated';
import { GetEditionNumberTypesDocument } from '../../queries/__generated__/getEditionNumberTypes.generated';
import { GetLocationsDocument } from '../../queries/__generated__/getLocations.generated';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetArtworkSaleStatusDocument } from '$lib/arteye-queries/__generated__/getArtworkSaleStatus.generated';
import type { Artwork } from '$lib/features/final-review/types';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { gqlClientCustom } from '$lib/gqlClientCustom';

export const finalReviewServerLoad = async <
	T extends { user: { access_token: string; arteye_token?: string } | null },
>({
	additionalCrumb,
	fileUrl,
	parentData,
	artworks,
	nbArtworksRemaining,
}: {
	additionalCrumb: string;
	fileUrl: string | null | undefined;
	nbArtworksRemaining: number | undefined;
	parentData: T;
	artworks: Artwork[];
}) => {
	const artworksMatchesPromises = artworks.map((artwork) => {
		const artworkImageId = (artwork?.imageChoices || artwork?.images)?.[0]?.id;
		return gqlClientCustom.request(
			FindArtworkMatchesDocument,
			{ imageId: artworkImageId },
			getAuthorizationHeaders(parentData)
		);
	});

	const nationalitiesPromise = gqlClient.request(
		GetLocationsDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const currencyPromise = gqlClient.request(
		GetCurrenciesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const artworkTypesPromise = gqlClient.request(
		GetArtworkTypesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const artworkSaleStatusesPromise = gqlClientArteye.request(
		GetArtworkSaleStatusDocument,
		{},
		getAuthorizationHeaders({
			user: { access_token: `${parentData.user?.arteye_token}` },
		})
	);

	const editionNumberTypesPromise = gqlClient.request(
		GetEditionNumberTypesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const artworkTypesDimensionsPromise = gqlClient.request(
		GetArtworkDimensionTypesDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const [
		nationalitiesResponse,
		currencyResponse,
		artworkTypesResponse,
		artworkSaleStatusesResponse,
		editionNumberTypesResponse,
		artworkTypesDimensionsResponse,
		...artworksMatches
	] = await Promise.all([
		nationalitiesPromise,
		currencyPromise,
		artworkTypesPromise,
		artworkSaleStatusesPromise,
		editionNumberTypesPromise,
		artworkTypesDimensionsPromise,
		...artworksMatchesPromises,
	]);

	const nationalities = nationalitiesResponse?.country.sort((a, b) => {
		if (a.name < b.name) {
			return -1;
		}
		if (a.name > b.name) {
			return 1;
		}
		return 0;
	});

	const currencies = currencyResponse?.currency;
	const artworkTypes = artworkTypesResponse?.Artwork_Type;
	const artworkSaleStatuses =
		artworkSaleStatusesResponse?.artwork_activity_status_type;

	const editionNumberTypes = editionNumberTypesResponse?.Edition_Number_Type;

	const artworkTypesDimensions =
		artworkTypesDimensionsResponse?.Artwork_Dimension_Type;

	return {
		...parentData,
		fileUrl,
		additionalCrumb,
		nbArtworksRemaining,
		artworkTypesDimensions,
		currencies,
		artworkSaleStatuses,
		nationalities,
		artworkTypes,
		artworks,
		editionNumberTypes,
		artworksMatches,
	};
};
