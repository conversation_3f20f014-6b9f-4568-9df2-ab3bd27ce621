import { type CreateMutationResult } from '@tanstack/svelte-query';
import { get } from 'svelte/store';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { ArtworkSourceType } from '$gql/types-custom';
import type { Artwork } from '$lib/features/final-review/types';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { IngestArtworksDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/ingestArtworks.generated';

export const imagesFinalizeReview = async ({
	data,
	artworksCopy,
	updateArtwork,
	selectedImages,
}: {
	data: {
		user: {
			access_token: string;
		} | null;
	};
	selectedImages: (string | undefined)[];
	updateArtwork: CreateMutationResult<any, any, any>;
	updateChecklist: CreateMutationResult<any, any, any> | null;
	artworksCopy: Artwork[];
	nbArtworksRemaining: number | undefined;
}) => {
	const updateManuallyAddedArtworkPromises = artworksCopy.map(
		(artworkCop, i) => {
			const { manuallyAddedArtworkId } = artworkCop;
			const selectedImageName = selectedImages[i];

			const artworkImageChoice = selectedImageName
				? artworkCop.imageChoices?.find(
						(imageChoice) => imageChoice?.name === selectedImageName
					)
				: artworkCop.images?.[0];

			return get(updateArtwork).mutateAsync({
				id: `${manuallyAddedArtworkId}`,
				input: {
					status: {
						key: artworkCop.skip ? 'SKIPPED' : 'REVIEWED_AND_SUBMITTED',
					},
					images: [
						{
							directus_files_id: {
								id: artworkImageChoice?.id,
								filename_download: artworkImageChoice?.filename_download,
								storage: artworkImageChoice?.storage,
							},
						},
					],
				},
			});
		}
	);

	await Promise.all(updateManuallyAddedArtworkPromises);

	await gqlClientCustom.request(
		IngestArtworksDocument,
		{
			input: {
				source_type: ArtworkSourceType.Visit,
				artwork_ids: artworksCopy.map(
					(artworkCopy) => artworkCopy.manuallyAddedArtworkId
				),
			},
		},
		getAuthorizationHeaders(data)
	);

	return Promise.resolve();
};
