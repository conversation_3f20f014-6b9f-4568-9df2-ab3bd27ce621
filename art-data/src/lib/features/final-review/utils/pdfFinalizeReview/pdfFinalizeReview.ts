import { type CreateMutationResult } from '@tanstack/svelte-query';
import { get } from 'svelte/store';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { ArtworkSourceType } from '$gql/types-custom';
import type { Artwork } from '$lib/features/final-review/types';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { IngestArtworksDocument } from '$lib/websites/scraped-data/custom-queries/__generated__/ingestArtworks.generated';

export const pdfFinalizeReview = async ({
	artworksCopy,
	updateArtwork,
	data,
}: {
	data: {
		user: {
			access_token: string;
		} | null;
	};
	selectedImages: (string | undefined)[];
	updateArtwork: CreateMutationResult<any, any, any>;
	updateChecklist: CreateMutationResult<any, any, any> | null;
	artworksCopy: Artwork[];
	nbArtworksRemaining: number | undefined;
}) => {
	const updateManuallyAddedArtworkPromises = artworksCopy.map((artworkCopy) => {
		const { manuallyAddedArtworkId } = artworkCopy;

		return get(updateArtwork).mutateAsync({
			id: `${manuallyAddedArtworkId}`,
			data: {
				status: {
					key: artworkCopy.skip ? 'SKIPPED' : 'REVIEWED_AND_SUBMITTED',
				},
			},
		});
	});

	await Promise.all(updateManuallyAddedArtworkPromises);

	await gqlClientCustom.request(
		IngestArtworksDocument,
		{
			input: {
				source_type: ArtworkSourceType.Pdf,
				artwork_ids: artworksCopy.map(
					(artworkCopy) => artworkCopy.manuallyAddedArtworkId
				),
			},
		},
		getAuthorizationHeaders(data)
	);

	// if (nbArtworksRemaining === artworksCopy.length && updateChecklist) {
	// 	await get(updateChecklist).mutateAsync({
	// 		id: get(page).params.pdfId,
	// 		data: {
	// 			status: {
	// 				key: 'REVIEWED_AND_SUBMITTED',
	// 			},
	// 		},
	// 	});
	// }

	return Promise.resolve();
};
