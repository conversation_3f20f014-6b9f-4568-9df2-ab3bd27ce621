import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { finalReviewServerLoad } from '../finalReviewServerLoad/finalReviewServerLoad';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import type { Artwork } from '$lib/features/final-review/types';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import {
	GetArtworkDetailsDocument,
	type GetArtworkDetailsQuery,
} from '$lib/queries/__generated__/getArtworkDetails.generated';
import { GetVisitArtworkDocument } from '$lib/queries/__generated__/getVisitArtwork.generated';
import { GetUsersDocument } from '$lib/system-queries/__generated__/getUsers.generated';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getArtworkIds } from '$lib/utils/getArtworksIds/getArtworksIds';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const imagesFinalReviewServerLoad = async <
	T extends () => Promise<{
		user: { access_token: string; arteye_token?: string } | null;
	}>,
>({
	parent,
	date,
	photographerId,
	visitId,
}: {
	parent: T;
	date: string;
	photographerId: string;
	visitId: string;
}) => {
	dayjs.extend(utc);
	const parentData = await parent();
	const accessToken = parentData?.user?.access_token;

	const visitArtworksResponse = await gqlClient.request(
		GetVisitArtworkDocument,
		{
			limit: -1,
			filter: {
				_and: [
					{ visit: { id: { _eq: visitId } } },
					{
						artwork_image: {
							image_taken_date: { _gte: dayjs.utc(date).toISOString() },
						},
					},
					{
						artwork_image: {
							image_taken_date: {
								_lt: dayjs.utc(date).add(1, 'day').toISOString(),
							},
						},
					},
					{ artwork_image: { photographer: { id: { _eq: photographerId } } } },
					{
						status: {
							key: {
								_in: [
									'AWAITING_REVIEW',
									'REVIEWED_AND_SUBMITTED',
									'INGESTION_FAILED',
									'COMPLETED',
								],
							},
						},
					},
				],
			},
		},
		getAuthorizationHeaders(parentData)
	);

	const visitArtworks = visitArtworksResponse?.Visit_Artwork?.filter(
		(artwork) => artwork?.status?.key === 'AWAITING_REVIEW'
	);

	const artworksDetailsIds = visitArtworks?.map(
		(visitArtwork) => visitArtwork?.artwork_details?.id
	) as string[];

	let artworks: Artwork[] = [];

	if (artworksDetailsIds.length) {
		const artworksResponse = await gqlClient.request(
			GetArtworkDetailsDocument,
			{
				filter: { id: { _in: artworksDetailsIds } },
			},
			getAuthorizationHeaders(parentData)
		);

		artworks = artworksResponse?.Artwork_Details?.map((artworkDetails) => {
			const correspondingVisitArtwork = visitArtworks?.find(
				(visitArtwork) =>
					visitArtwork?.artwork_details?.id === artworkDetails?.id
			);

			const artworkImage =
				correspondingVisitArtwork?.artwork_image?.crop_type ===
				'perspective_crop'
					? correspondingVisitArtwork?.artwork_image
							?.perspective_cropped_image_without_dimensions
					: correspondingVisitArtwork?.artwork_image?.rectangular_cropped_image;

			const labelImage =
				correspondingVisitArtwork?.label_image?.crop_type === 'perspective_crop'
					? correspondingVisitArtwork?.label_image
							?.perspective_cropped_image_without_dimensions
					: correspondingVisitArtwork?.label_image?.rectangular_cropped_image;

			return {
				...artworkDetails,
				skip: false,
				various_artists: false,
				manuallyAddedArtworkId: correspondingVisitArtwork?.id,
				labelImages: [
					...(labelImage
						? [
								{
									directus_files_id: {
										width: labelImage?.width || 1,
										height: labelImage?.height || 1,
										filename_disk: `${labelImage?.filename_disk}`,
										alt: '',
										url: `${getImageUrl(labelImage?.id, accessToken)}`,
									},
								},
							]
						: []),
				],
				labelText: correspondingVisitArtwork?.label_text,
				...(correspondingVisitArtwork?.artwork_image?.crop_type ===
				'rectangular_crop'
					? {
							images: [
								{
									id: `${artworkImage?.id}`,
									filename_download: `${artworkImage?.filename_download}`,
									storage: `${artworkImage?.storage}`,
									directus_files_id: {
										width: artworkImage?.width || 1,
										height: artworkImage?.height || 1,
										filename_disk: `${artworkImage?.filename_disk}`,
										alt: '',
										url: `${getImageUrl(artworkImage?.id, accessToken)}`,
									},
								},
							],
						}
					: {
							imageChoices: [
								{
									name: 'Cropped',
									visitImageId: `${correspondingVisitArtwork?.artwork_image?.id}`,
									id: `${artworkImage?.id}`,
									filename_download: `${artworkImage?.filename_download}`,
									storage: `${artworkImage?.storage}`,
									directus_files_id: {
										width: artworkImage?.width || 1,
										height: artworkImage?.height || 1,
										filename_disk: `${artworkImage?.filename_disk}`,
										alt: '',
										url: `${getImageUrl(artworkImage?.id, accessToken)}`,
									},
								},
							],
						}),
				description: `${artworkDetails?.description || ''}`,
				has_multiple_pieces: (artworkDetails?.number_of_pieces || 0) > 1,
				artists: (artworkDetails.artists || []).map(
					(
						artistDetail: NonNullable<
							GetArtworkDetailsQuery['Artwork_Details'][number]['artists']
						>[number]
					) => ({
						...artistDetail,
						id: artistDetail?.id || '',
						processed_artist: null,
					})
				),
			};
		});
	}

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{ filter: { id: { _eq: photographerId } } },
		getAuthorizationHeaders(parentData)
	);

	const photographer = usersResponse.users?.[0];
	const additionalCrumb = buildUploadCrumb(
		date,
		[photographer.first_name, photographer.last_name].filter(Boolean).join(' ')
	);

	return {
		artworksIds: getArtworkIds(visitArtworksResponse?.Visit_Artwork),
		...(await finalReviewServerLoad({
			additionalCrumb,
			fileUrl: null,
			parentData,
			artworks,
			nbArtworksRemaining: visitArtworks.length,
		})),
	};
};
