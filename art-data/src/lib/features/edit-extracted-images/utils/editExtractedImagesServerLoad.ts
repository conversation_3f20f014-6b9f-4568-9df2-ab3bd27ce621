import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetArtworkSaleStatusDocument } from '$lib/arteye-queries/__generated__/getArtworkSaleStatus.generated';
import { Config } from '$lib/constants/config';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientArteye } from '$lib/gqlClientArteye';
import { GetCountriesDocument } from '$lib/queries/__generated__/getCountries.generated';
import { GetPdfDocument } from '$lib/queries/__generated__/getPdf.generated';
import { buildUploadCrumb } from '$lib/utils/buildUploadCrumb/buildUploadCrumb';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

export const editExtractedImagesServerLoad = async ({
	params,
	data,
}: {
	params: { pdfId: string };
	data: { user: { access_token: string; arteye_token?: string } | null };
}) => {
	const pdfResponse = await gqlClient.request(
		GetPdfDocument,
		{
			filter: { id: { _eq: params.pdfId } },
		},
		getAuthorizationHeaders(data)
	);

	const pdf = pdfResponse?.PDF?.[0];

	if (!pdf || pdf?.submitted_for_review) {
		error(404, 'Not found');
	}

	const artworkSaleStatusesResponse = await gqlClientArteye.request(
		GetArtworkSaleStatusDocument,
		{},
		getAuthorizationHeaders({
			user: { access_token: `${data.user?.arteye_token}` },
		})
	);

	const artworkSaleStatuses =
		artworkSaleStatusesResponse?.artwork_activity_status_type;

	const additionalCrumb = buildUploadCrumb(
		pdf?.receipt_info?.receive_date,
		pdf?.processed_fair_exhibitor_org?.name
	);

	const pdfArtist = pdf?.processed_artist;

	let pdfArtistCountry;

	if (pdfArtist?.nationality) {
		const pdfArtistCountryRes = await gqlClient.request(
			GetCountriesDocument,
			{
				filter: { code: { _eq: pdfArtist.nationality } },
			},
			getAuthorizationHeaders(data)
		);

		pdfArtistCountry = pdfArtistCountryRes?.country?.[0];
	}

	return {
		additionalCrumb,
		artworks: pdf?.artworks,
		pages: pdf?.pages,
		pdfTitle: pdf?.title,
		pdfArtist,
		pdfArtistCountry,
		pdfAiExtractedText: pdf?.ai_extracted_text,
		pdfArtworkFormatMethod: pdf?.pdf_artwork_format_method,
		discardedImages: pdf?.discarded_images,
		file: `${getImageUrl(pdf?.pdf_file?.id)}`,
		artworkSaleStatuses,
	};
};
