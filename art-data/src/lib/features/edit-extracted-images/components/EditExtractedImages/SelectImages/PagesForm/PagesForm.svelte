<script lang="ts">
	import { Option } from '.';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Radio } from '$global/components/Radio';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import {
		GetPdfImagesDocument,
		type GetPdfImagesQueryVariables,
	} from '$lib/features/edit-extracted-images/queries/__generated__/getPdfImages.generated';
	import {
		type EditExtractedImage,
		SelectedImageStatus,
	} from '$lib/features/edit-extracted-images/types';
	import { gqlClient } from '$lib/gqlClient';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	let selectedPages: Option = $state(Option.All) as Option;
	let include = $state('');
	let exclude = $state('');
	let blurInclude = $state(false);
	let blurExclude = $state(false);
	let fetchingImages = $state(false);

	const isValid = (value: string) => {
		const pages = value.replaceAll(' ', '').split(',');
		return pages.every(
			(page) =>
				/^\d/.test(page) &&
				/\d$/.test(page) &&
				/^([0-9]|-)+$/.test(page) &&
				page.split('-').length <= 2 &&
				!page.startsWith('-') &&
				!page.endsWith('-')
		);
	};

	const formatIncludePagesFilter = (
		value: string
	): GetPdfImagesQueryVariables['pagesFilter'] => {
		const pages = value.replaceAll(' ', '').split(',');

		return {
			_or: pages.map((pdf) => {
				const pdfParts = pdf.split('-');
				if (pdfParts.length === 2) {
					return {
						_and: [
							{ page_number: { _gte: +pdfParts[0] } },
							{ page_number: { _lte: +pdfParts[1] } },
						],
					};
				}

				return { page_number: { _eq: +pdfParts[0] } };
			}),
		};
	};

	const formatExcludePagesFilter = (
		value: string
	): GetPdfImagesQueryVariables['pagesFilter'] => {
		const pages = value.replaceAll(' ', '').split(',');

		return {
			_and: pages.map((pdf) => {
				const pdfParts = pdf.split('-');
				if (pdfParts.length === 2) {
					return {
						page_number: { _nbetween: [+pdfParts[0], +pdfParts[1]] },
					};
				}

				return { page_number: { _neq: +pdfParts[0] } };
			}),
		};
	};

	interface Props {
		dataCy: string;
		images: EditExtractedImage[];
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];
	}

	let {
		dataCy,
		images = $bindable(),
		selectedImageStatuses = $bindable(),
	}: Props = $props();

	const handleClickFetchImages = async () => {
		fetchingImages = true;

		const pagesFilter = (() => {
			if (selectedPages === Option.Include) {
				return formatIncludePagesFilter(include);
			}
			if (selectedPages === Option.Exclude) {
				return formatExcludePagesFilter(exclude);
			}
			return {};
		})();

		const pdfImagesResponse = await gqlClient.request(
			GetPdfImagesDocument,
			{
				filter: { id: { _eq: page.params.pdfId } },
				pagesFilter,
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		selectedImageStatuses = pdfImagesResponse?.PDF?.[0]?.pages?.flatMap(
			(page) =>
				page?.extracted_images?.flatMap((extractedImage) => {
					const oldImageIndex = images?.findIndex(
						(image) =>
							image?.filename_download ===
							extractedImage?.image?.filename_download
					);

					if (oldImageIndex === -1) {
						return {
							status: SelectedImageStatus.Selected,
							discardTimestamp: null,
						};
					}

					return selectedImageStatuses[oldImageIndex];
				})
		) as {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];

		images = pdfImagesResponse?.PDF?.[0]?.pages?.flatMap((page) =>
			page?.extracted_images?.flatMap((extractedImage) => ({
				pdf_discard_image_id: '',
				file: null,
				storage: `${extractedImage?.image?.storage}`,
				page: page?.page_number,
				filename_disk: extractedImage?.image?.filename_disk,
				filename_download: extractedImage?.image?.filename_download,
				url: `${getImageUrl(extractedImage?.image?.id)}`,
				width: extractedImage?.image?.width,
				height: extractedImage?.image?.height,
				alt: '',
				id: `${extractedImage?.image?.id}`,
				text: page?.text,
			}))
		) as EditExtractedImage[];

		fetchingImages = false;
	};

	let fetchImagesButtonDisabled = $derived(
		(() => {
			if (fetchingImages) {
				return true;
			}

			if (selectedPages === Option.Include) {
				return !isValid(include);
			}

			if (selectedPages === Option.Exclude) {
				return !isValid(exclude);
			}

			return false;
		})()
	);
</script>

<div class="flex items-center gap-4">
	<InputLabel dataCy={`${dataCy}-all`} classes={{ container: 'w-auto' }}
		><Radio
			bind:group={selectedPages}
			id={Option.All}
			dataCy={`${dataCy}-all`}
			name="option"
			value={Option.All}
		/>All</InputLabel
	>

	<div class="flex items-center gap-2">
		<InputLabel dataCy={`${dataCy}-include`} classes={{ container: 'w-auto' }}
			><Radio
				bind:group={selectedPages}
				id={Option.Include}
				dataCy={`${dataCy}-include`}
				name="option"
				value={Option.Include}
			/>Include Pages</InputLabel
		>

		<InfoTooltip
			dataCy={`${dataCy}-include`}
			content="You can include specific pages either individually or within a range, such as: 1,2-5,4,6"
		/>
	</div>

	<div>
		<Input
			class="min-w-[9rem] max-w-[9rem]"
			name="include"
			dataCy={`${dataCy}-include`}
			placeholder="eg. 1, 2, 24"
			bind:value={include}
			onblur={() => {
				blurInclude = true;
			}}
			classes={{ error: 'absolute' }}
			error={!isValid(include) &&
			blurInclude &&
			include &&
			selectedPages === Option.Include
				? 'Invalid range'
				: ''}
		/>
	</div>

	<div class="flex items-center gap-2">
		<InputLabel dataCy={`${dataCy}-exclude`} classes={{ container: 'w-auto' }}
			><Radio
				bind:group={selectedPages}
				id={Option.Exclude}
				dataCy={`${dataCy}-exclude`}
				name="option"
				value={Option.Exclude}
			/>Exclude Pages</InputLabel
		>
		<InfoTooltip
			dataCy={`${dataCy}-exclude`}
			content="You can exclude specific pages either individually or within a range, such as: 1,2-5,4,6"
		/>
	</div>

	<div>
		<Input
			class="min-w-[9rem] max-w-[9rem]"
			name="exclude"
			dataCy={`${dataCy}-exclude`}
			placeholder="eg. 1, 2, 24"
			bind:value={exclude}
			onblur={() => {
				blurExclude = true;
			}}
			classes={{ error: 'absolute' }}
			error={!isValid(exclude) &&
			blurExclude &&
			selectedPages === Option.Exclude &&
			exclude
				? 'Invalid range'
				: ''}
		/>
	</div>

	<Button
		onclick={handleClickFetchImages}
		size="md"
		loading={fetchingImages}
		dataCy={`${dataCy}-fetch-images`}
		disabled={fetchImagesButtonDisabled}
	>
		fetch images
	</Button>
</div>
