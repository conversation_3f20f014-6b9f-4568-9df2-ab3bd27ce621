<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { Button } from '$global/components/Button';
	import { Checkbox, type CheckboxValue } from '$global/components/Checkbox';
	import { Container } from '$global/components/Container';
	import {
		Image,
		type ImageCarouselImage,
	} from '$global/components/ImageCarousel/Image';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import {
		FormatMethod,
		SelectedImageStatus,
	} from '$lib/features/edit-extracted-images/types';
	import type {
		EditExtractedImage,
		FormatToggleState,
	} from '$lib/features/edit-extracted-images/types';
	import type { GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';
	import type { ArtworkImages } from '$lib/types';

	interface Props {
		pages: GetPdfQuery['PDF'][number]['pages'];
		images: EditExtractedImage[];
		pdfAiExtractedText: string | null | undefined;
		formatToggleState: FormatToggleState;
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];
		showTable: boolean;
		dataCy: string;
	}

	let {
		pages,
		images,
		pdfAiExtractedText,
		formatToggleState = $bindable(),
		selectedImageStatuses = $bindable(),
		showTable = $bindable(),
		dataCy,
	}: Props = $props();

	let initialFormatToggleState: FormatToggleState;

	onMount(() => {
		initialFormatToggleState = JSON.parse(JSON.stringify(formatToggleState));
	});

	const {
		artworkImages,
		artworkInfo,
		resetArtworksTable,
		formatArtworksUsingLineBreaks,
		formatArtworksUsingAiText,
		isImageChecked,
	} = getArtworksTextTableContext();

	let safeImages = $derived(
		images as (ImageCarouselImage & {
			page?: number;
		})[]
	);

	let allSelectedImages = $state(true) as CheckboxValue;

	$effect(() => {
		allSelectedImages = (() => {
			if (selectedImageStatuses.every(isImageChecked)) {
				return true;
			}

			return selectedImageStatuses.every(
				(selectedImageStatus) => !isImageChecked(selectedImageStatus)
			)
				? false
				: 'indeterminate';
		})() as CheckboxValue;
	});

	let nbSelectedImages = $derived(
		selectedImageStatuses?.filter(
			(selectedImageStatus) =>
				selectedImageStatus.status === SelectedImageStatus.Selected
		)?.length
	);

	const handleChangeImageCheckbox = (index: number, checked: boolean) => {
		selectedImageStatuses[index].status = checked
			? SelectedImageStatus.Selected
			: SelectedImageStatus.Discarded;
	};

	const handleChangeAllSelected = (newAllSelectedValue: boolean | string) => {
		selectedImageStatuses = images.map(() => ({
			status: newAllSelectedValue
				? SelectedImageStatus.Selected
				: SelectedImageStatus.Discarded,
			discardTimestamp: null,
		}));
	};

	const handleClickSubmit = () => {
		// If the table is generated for the first time
		if (!formatToggleState.images) {
			let newArtworks: Parameters<typeof resetArtworksTable>[1];

			if (pdfAiExtractedText) {
				newArtworks = formatArtworksUsingAiText(
					images,
					selectedImageStatuses,
					pdfAiExtractedText
				);
			} else {
				newArtworks = formatArtworksUsingLineBreaks(
					images,
					selectedImageStatuses
				);
			}

			selectedImageStatuses = selectedImageStatuses.map((status, i) => {
				if (
					Object.values(newArtworks).find((artwork) =>
						artwork?.images?.images?.find(
							(image) => image.id === images[i]?.filename_disk
						)
					) ||
					status.status !== SelectedImageStatus.Selected
				) {
					return status;
				}

				return {
					status: SelectedImageStatus.ExcludedFromFormat,
					discardTimestamp: +new Date(),
				};
			});

			resetArtworksTable(pages, newArtworks);
			// If the table has been generated at least once before
			// We just need to filter out or add back the images that have been (un)selected
			// If there are images to add back, we add them in a single line at the end of the table with an empty text
		} else {
			const imagesToRestore = formatToggleState.images
				.map((image, i) => {
					return selectedImageStatuses[i].status ===
						SelectedImageStatus.Selected &&
						[
							SelectedImageStatus.Discarded,
							SelectedImageStatus.RecentlyDiscarded,
						].includes(
							initialFormatToggleState?.selectedImageStatuses?.[i]
								?.status as SelectedImageStatus
						)
						? image
						: null;
				})
				.filter(Boolean);

			artworkImages.set([
				...$artworkImages.map((artworkImage) => {
					const filteredImages = artworkImage.images.filter((image) => {
						const imageIndex = (formatToggleState.images || [])?.findIndex(
							(toggleImage) => toggleImage.filename_disk === image.filename_disk
						);

						if (imageIndex === -1) {
							return false;
						}

						return (
							selectedImageStatuses[imageIndex].status ===
							SelectedImageStatus.Selected
						);
					});

					return {
						images: filteredImages,
						page: Array.from(
							new Set(filteredImages?.map((image) => image.page))
						) as number[],
					};
				}),
				...(imagesToRestore?.length
					? [
							{
								images: imagesToRestore.map((image) => ({
									...image,
									id: image?.filename_disk,
								})),
								page: Array.from(
									new Set(imagesToRestore?.map((image) => image?.page))
								) as number[],
							} as ArtworkImages,
						]
					: []),
			]);

			if (imagesToRestore?.length) {
				artworkInfo.set([
					...$artworkInfo,
					{
						status: 'NOT_FOR_SALE',
						text: '',
						id: '',
					},
				]);
			}
		}

		showTable = true;

		// If the table is generated for the first time
		if (!formatToggleState.images) {
			formatToggleState = {
				images: [...images],
				selectedImageStatuses: [...selectedImageStatuses],
				pages,
				method: FormatMethod.LineBreak,
			};
			// If the table has been generated at least once before
		} else {
			formatToggleState = {
				...formatToggleState,
				selectedImageStatuses: [...selectedImageStatuses],
			};
		}
	};
</script>

<div
	class={classNames('mb-12 rounded border border-gray-200 bg-gray-50 p-5 pb-1')}
>
	<InputLabel dataCy={`${dataCy}-selected`}
		><Checkbox
			bind:checked={allSelectedImages}
			id="selected"
			dataCy={`${dataCy}-selected`}
			name="option"
			onChange={handleChangeAllSelected}
		/>{nbSelectedImages} selected</InputLabel
	>

	<div class="mt-4 grid grid-cols-8">
		{#each safeImages as image, i}
			{@const checked = isImageChecked(selectedImageStatuses[i])}
			<div class="mb-4">
				<Image
					{image}
					{dataCy}
					class={classNames({
						'border-green-700': checked,
						'border-red-500': !checked,
					})}
				>
					<Checkbox
						dataCy={`${dataCy}-image-checkbox`}
						{checked}
						class="absolute bottom-2 right-2 z-20"
						classes={{
							icon: classNames({
								'bg-green-700 border-green-700': checked,
							}),
						}}
						onChange={(checked) => handleChangeImageCheckbox(i, !!checked)}
					/>

					{#if !checked}
						<div class="absolute z-10 h-full w-full bg-gray-0 opacity-80"></div>
					{/if}
				</Image>

				<Txt
					variant="body3"
					class="col-span-2 mb-2 mt-1 uppercase tracking-[1.68px] text-gray-500"
					>PAGE {image.page}</Txt
				>
			</div>
		{/each}
	</div>
</div>

<div
	class="fixed bottom-0 left-0 z-20 w-full border-t border-gray-200 bg-gray-0 py-4"
>
	<Container dataCy={`${dataCy}-save`} class="flex justify-end">
		<Button
			dataCy={`${dataCy}-save-submit`}
			size="md"
			disabled={!nbSelectedImages}
			onclick={handleClickSubmit}
			>{formatToggleState.images
				? 'back to artworks table'
				: 'continue with selected images'}</Button
		>
	</Container>
</div>
