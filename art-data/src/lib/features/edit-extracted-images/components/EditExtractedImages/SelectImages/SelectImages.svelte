<script lang="ts">
	import classNames from 'classnames';
	import { ImageSelector } from './ImageSelector';
	import { PagesForm } from './PagesForm';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Container } from '$global/components/Container';
	import { LinkButton } from '$global/components/LinkButton';
	import { Txt } from '$global/components/Txt';
	import { formatRange } from '$global/utils/formatRange/formatRange';
	import type {
		EditExtractedImage,
		FormatToggleState,
		SelectedImageStatus,
	} from '$lib/features/edit-extracted-images/types';
	import type { GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';

	interface Props {
		images: EditExtractedImage[];
		pages: GetPdfQuery['PDF'][number]['pages'];
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];
		pdfLink: string;
		pdfAiExtractedText: string | null | undefined;
		dataCy: string;
		showTable: boolean;
		slotLabel: string;
		title: string;
		formatToggleState: FormatToggleState;
		children?: import('svelte').Snippet;
	}

	let {
		images = $bindable(),
		pages,
		selectedImageStatuses = $bindable(),
		pdfLink,
		pdfAiExtractedText,
		dataCy,
		showTable = $bindable(),
		slotLabel,
		title,
		formatToggleState = $bindable(),
		children,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-select-images`);
	let pagesRange = $derived(
		formatRange((images || []).map((image) => image?.page).join(','))
	);
</script>

<Container dataCy={dataCyPrefix}>
	<div class="mb-6 flex items-center justify-between">
		<div>
			<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
				>{title}</Txt
			>
			<Txt variant="h4">Edit extracted images</Txt>
		</div>
		<LinkButton
			size="md"
			dataCy={`${dataCyPrefix}-download-pdf`}
			href={pdfLink}
			newTab
			icon
			variant="secondary"
		>
			{#snippet leading()}
				<DownloadIcon />
			{/snippet}
			download pdf
		</LinkButton>
	</div>

	<Txt variant="h6" class="mb-4">{slotLabel}</Txt>

	<div class="mb-4 flex items-end justify-between gap-4">
		<div class="flex-1">
			{@render children?.()}
		</div>

		<div class={classNames('flex flex-col justify-end')}>
			<Txt variant="body2" class="mb-2">
				Specify pages to move onto next step
			</Txt>

			<PagesForm bind:images bind:selectedImageStatuses dataCy={dataCyPrefix} />
		</div>
	</div>

	<Txt
		variant="body3"
		class="col-span-2 mb-4 uppercase tracking-[1.68px] text-gray-500"
		>{pagesRange ? `${pagesRange} pages` : ''}</Txt
	>

	<ImageSelector
		{pages}
		{dataCy}
		{images}
		{pdfAiExtractedText}
		bind:formatToggleState
		bind:selectedImageStatuses
		bind:showTable
	/>
</Container>
