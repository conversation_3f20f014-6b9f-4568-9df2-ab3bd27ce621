<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { createMutation } from '@tanstack/svelte-query';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { BackDialog } from './BackDialog';
	import { EditDescriptionsDialog } from './EditDescriptionsDialog';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import type { ImageCarouselImage } from '$global/components/ImageCarousel';
	import { LinkButton } from '$global/components/LinkButton';
	import { Switch } from '$global/components/Switch';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { type ErrorSuccessPageProps } from '$global/features/form/pages/ErrorSuccessPage';
	import { Config } from '$lib/constants/config';
	import { ArtworksTextTable } from '$lib/features/artwork-text-table/components/ArtworksTextTable';
	import { ArtworksTextDiscardedImagesDrawer } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ArtworksTextDiscardedImagesDrawer';
	import { ChangeStatusForAllDialog } from '$lib/features/artwork-text-table/components/ArtworksTextTable/ChangeStatusForAllDialog';
	import { StatusSelect } from '$lib/features/artwork-text-table/components/ArtworksTextTable/StatusSelect';
	import { ConfirmationDialog } from '$lib/features/artwork-text-table/components/ConfirmationDialog';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import { UpdatePdfDiscardImagesDocument } from '$lib/features/edit-extracted-images/queries/__generated__/__generated__/udpatePdfDiscardImages.generated';
	import {
		CreatePdfArtworksDocument,
		type CreatePdfArtworksMutation,
	} from '$lib/features/edit-extracted-images/queries/__generated__/createPdfArtworks.generated';
	import {
		CreatePdfDiscardImagesDocument,
		type CreatePdfDiscardImagesMutation,
	} from '$lib/features/edit-extracted-images/queries/__generated__/createPdfDiscardImages.generated';
	import { DeletePdfArtworksDocument } from '$lib/features/edit-extracted-images/queries/__generated__/deletePdfArtworks.generated';
	import { DeletePdfDiscardImagesDocument } from '$lib/features/edit-extracted-images/queries/__generated__/deletePdfDiscardImages.generated';
	import { UpdatePdfArtworksDocument } from '$lib/features/edit-extracted-images/queries/__generated__/updatePdfArtworks.generated';
	import {
		FormatMethod,
		SelectedImageStatus,
	} from '$lib/features/edit-extracted-images/types';
	import type {
		EditExtractedImage,
		FormatToggleState,
	} from '$lib/features/edit-extracted-images/types';
	import type { GetCountriesQuery } from '$lib/queries/__generated__/getCountries.generated';
	import type { GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';
	import { UpdatePdfDocument } from '$lib/queries/__generated__/updatePdf.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import type { ArtworkInfo } from '$lib/types';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

	let hasSubmitted = $state(false);
	let saveProgressSubmitting = $state(false);
	let submitSubmitting = $state(false);

	$effect(() => {
		submitting = saveProgressSubmitting || submitSubmitting;
	});

	const editDescriptionsDialogStores = createDialog();
	const drawerStores = createDialog({
		preventScroll: false,
	});

	const backDialogStores = createDialog();
	const changeStatusDialogStores = createDialog();

	const {
		isArtworkInfoMultipleParagraphs,
		isEveryRowFilledIn,
		artworkImages,
		artworkInfo,
		artworkFiles,
		resetArtworksTable,
		formatArtworksByPage,
		formatArtworksUsingLineBreaks,
		formatArtworksUsingAiText,
	} = getArtworksTextTableContext();

	const handleClickViewDiscardedImages = () => {
		drawerStores.states.open.set(true);
	};

	const areAllImagesSelected = (
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[]
	) => {
		return selectedImageStatuses.every(
			(selectedImageStatus) =>
				selectedImageStatus.status === SelectedImageStatus.Selected
		);
	};

	const handleDeleteImageInTable = (imageToDelete: ImageCarouselImage) => {
		const imageIndex = images.findIndex(
			(image) => image.url === imageToDelete.url
		);

		selectedImageStatuses[imageIndex] = {
			status: SelectedImageStatus.RecentlyDiscarded,
			discardTimestamp: +new Date(),
		};

		formatToggleState = {
			...formatToggleState,
			selectedImageStatuses: JSON.parse(JSON.stringify(selectedImageStatuses)),
		};
	};

	const handleSelectImage = (imageToSelect: ImageCarouselImage) => {
		const imageIndex = images.findIndex(
			(image) => image.url === imageToSelect.url
		);

		selectedImageStatuses[imageIndex] = {
			status: SelectedImageStatus.Selected,
			discardTimestamp: null,
		};
	};

	const handleClickSubmit = () => {
		hasSubmitted = true;

		if (isEveryRowFilledIn()) {
			dialogStores.states.open.set(true);
		} else {
			const firstLineWithoutImage = $artworkImages.findIndex(
				(artworkImage) => !artworkImage.images.length
			);

			const firstLineWithoutText = $artworkInfo.findIndex(
				(artworkInfo) => !artworkInfo.text
			);

			const firstErroneousLineIndex = (() => {
				if (firstLineWithoutImage > -1 && firstLineWithoutText > 1) {
					return firstLineWithoutImage < firstLineWithoutText
						? firstLineWithoutImage
						: firstLineWithoutText;
				}

				return firstLineWithoutImage === -1
					? firstLineWithoutText
					: firstLineWithoutImage;
			})();

			const firstLineElt = document.querySelector(
				`#${dataCyPrefix}-text-${firstErroneousLineIndex}`
			);

			if (firstLineElt) {
				const firstLineEltTop = firstLineElt.getBoundingClientRect().top;
				window.scrollTo({
					left: 0,
					top:
						firstLineEltTop - document.body.getBoundingClientRect().top - 100,
					behavior: 'smooth',
				});
			}
		}
	};

	interface Props {
		pdfArtist: NonNullable<GetPdfQuery['PDF'][number]>['processed_artist'];
		pdfAiExtractedText: string | null | undefined;
		pdfLink: string;
		formatToggleState: FormatToggleState;
		pdfTitle: string;
		pages: GetPdfQuery['PDF'][number]['pages'];
		slotLabel: string;
		errorButtonProps: ErrorSuccessPageProps['buttonProps'];
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props?: ErrorSuccessPageProps['button2Props'] | undefined;
		pdfArtistCountry: GetCountriesQuery['country'][number] | undefined | null;
		showTable: boolean;
		discardedImages: GetPdfQuery['PDF'][number]['discarded_images'];
		dialogStores: ReturnType<typeof createDialog>;
		submitting: boolean;
		artworks: GetPdfQuery['PDF'][number]['artworks'];
		images: EditExtractedImage[];
		selectedImageStatuses: {
			status: SelectedImageStatus;
			discardTimestamp: null | number;
		}[];
		dataCy: string;
		title: string;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		children?: import('svelte').Snippet;
	}

	let {
		pdfArtist,
		pdfAiExtractedText,
		pdfLink,
		formatToggleState = $bindable(),
		pdfTitle,
		pages,
		slotLabel,
		errorButtonProps,
		buttonProps,
		button2Props = undefined,
		pdfArtistCountry,
		showTable = $bindable(),
		discardedImages,
		dialogStores,
		submitting = $bindable(),
		artworks,
		images = $bindable(),
		selectedImageStatuses = $bindable(),
		dataCy,
		title,
		artworkSaleStatuses,
		children,
	}: Props = $props();

	let allStatusValue = $state(artworkSaleStatuses[0].key);

	const onSubmitFiles = (
		newImages: {
			filename_disk: string;
			alt: string;
			file: File;
			url: string;
			width: number;
			height: number;
		}[]
	) => {
		images = [
			...images,
			...newImages.map((newImage, i) => {
				return {
					file: newImage.file,
					url: newImage.url,
					width: newImage.width,
					height: newImage.height,
					filename_disk: newImage.filename_disk,
					filename_download: `${newImage.file?.name}`,
					alt: '',
					page: 0,
					text: '',
					id: '',
					storage: '',
					pdf_discard_image_id: '',
				};
			}),
		];

		selectedImageStatuses = [
			...selectedImageStatuses,
			...newImages.map(() => ({
				status: SelectedImageStatus.Selected,
				discardTimestamp: null,
			})),
		];
	};

	const createPdfDiscardImagesMutation = createMutation(
		getMutation(
			CreatePdfDiscardImagesDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updatePdfDiscardImagesMutation = createMutation(
		getMutation(
			UpdatePdfDiscardImagesDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const deletePdfDiscardImagesMutation = createMutation(
		getMutation(
			DeletePdfDiscardImagesDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const createPdfArtworks = createMutation(
		getMutation(
			CreatePdfArtworksDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updatePdfArtworks = createMutation(
		getMutation(
			UpdatePdfArtworksDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const deletePdfArtworks = createMutation(
		getMutation(
			DeletePdfArtworksDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const updatePdf = createMutation(
		getMutation(
			UpdatePdfDocument,
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		)
	);

	const getImagesObj = async (
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number }
	) => {
		const images = $artworkImages[artworkInfo.artworkInfoIndex].images;

		const imagePromises = images.map((image) => {
			if (image.url.startsWith('/api')) {
				const file = $artworkFiles.find(
					(artworkFile) => artworkFile.filename_disk === image.filename_disk
				);

				return Promise.resolve({
					storage: file?.storage,
					id: file?.id,
					filename_download: file?.filename_download,
				});
			}

			if (image.url.startsWith('data') && image.file) {
				return uploadFile(
					image.file as File,
					getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				);
			}

			return null;
		});

		const imagesObj = (await Promise.all(imagePromises))
			.filter(Boolean)
			.filter(Boolean) as {
			storage: string;
			id: string;
			filename_download: string;
		}[];

		return imagesObj;
	};

	const createPdfDiscardImage = async (
		discardedImage: EditExtractedImage & { discardTimestamp: number }
	) => {
		let imageId = discardedImage.id;

		if (discardedImage.url.startsWith('data') && discardedImage.file) {
			imageId = (
				await uploadFile(
					discardedImage.file as File,
					getAuthorizationHeaders(
						page.data as Parameters<typeof getAuthorizationHeaders>[0]
					)
				)
			)?.id;
		}

		const payload = {
			timestamp: dayjs(discardedImage.discardTimestamp).format(
				'YYYY-MM-DDThh:mm:ss'
			),
			image: discardedImage.id,
			pdf: +page.params.pdfId,
		};

		const res = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/PDF_Discard_Image`,
			{
				method: 'POST',
				body: JSON.stringify(payload),
				headers: {
					...getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					'Content-Type': 'application/json',
				},
			}
		);

		return res.json();
	};

	const updatePdfDiscardImage = async (
		discardedImage: EditExtractedImage & { discardTimestamp: number }
	) => {
		const payload = {
			timestamp: dayjs(discardedImage.discardTimestamp).format(
				'YYYY-MM-DDThh:mm:ss'
			),
			image: discardedImage.id,
			pdf: +page.params.pdfId,
		};

		const res = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/PDF_Discard_Image/${discardedImage.pdf_discard_image_id}`,
			{
				method: 'PATCH',
				body: JSON.stringify(payload),
				headers: {
					...getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					'Content-Type': 'application/json',
				},
			}
		);

		return res.json();
	};

	const createArtwork = async (
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER',
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number }
	) => {
		const imagesObj = await getImagesObj(artworkInfo);

		const payload = {
			artwork_details: {
				description: artworkInfo?.text,
				sale_status: artworkInfo?.status,
				...(pdfArtist && {
					artists: [
						{
							name: `${pdfArtist.first_name} ${pdfArtist.last_name}`,
							...(pdfArtist.year_birth && {
								year_birth: pdfArtist.year_birth,
							}),
							...(pdfArtist.year_death && {
								year_death: pdfArtist.year_death,
							}),
							processed_artist_id: pdfArtist.processed_artist_id,
							...(pdfArtistCountry && {
								nationality: {
									code: pdfArtistCountry.code,
									name: pdfArtistCountry.name,
								},
							}),
						},
					],
				}),
			},
			sort: artworkInfo.artworkInfoIndex,
			images: imagesObj.map((imageObj) => ({
				directus_files_id: imageObj.id,
			})),
			status,
			pdf: +page.params.pdfId,
		};

		const res = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/PDF_Artwork`,
			{
				method: 'POST',
				body: JSON.stringify(payload),
				headers: {
					...getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					'Content-Type': 'application/json',
				},
			}
		);

		return res.json();
	};

	const updateArtwork = async (
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER',
		artworkInfo: ArtworkInfo & { artworkInfoIndex: number }
	) => {
		const imagesObj = await getImagesObj(artworkInfo);

		const payload = {
			status,
			artwork_details: {
				description: artworkInfo?.text,
				sale_status: artworkInfo?.status,
				...(pdfArtist && {
					artists: [
						{
							name: `${pdfArtist.first_name} ${pdfArtist.last_name}`,
							...(pdfArtist.year_birth && {
								year_birth: pdfArtist.year_birth,
							}),
							...(pdfArtist.year_death && {
								year_death: pdfArtist.year_death,
							}),
							processed_artist_id: pdfArtist.processed_artist_id,
							...(pdfArtistCountry && {
								nationality: {
									code: pdfArtistCountry.code,
									name: pdfArtistCountry.name,
								},
							}),
						},
					],
				}),
			},
			sort: artworkInfo.artworkInfoIndex,
			images: imagesObj.map((imageObj) => ({
				directus_files_id: imageObj.id,
			})),
			pdf: +page.params.pdfId,
		};

		const res = await fetch(
			`${Config.ClientGraphqlApiDomain}/items/PDF_Artwork/${artworkInfo.id}`,
			{
				method: 'PATCH',
				body: JSON.stringify(payload),
				headers: {
					...getAuthorizationHeaders(
						page.data as { user: { access_token: string } }
					),
					'Content-Type': 'application/json',
				},
			}
		);

		return res.json();
	};

	const handleSave = async (
		status: 'DRAFT' | 'AWAITING_LABEL_PARSER',
		errorCallback: () => void
	) => {
		let error = false;

		let artworkInfoCopy = $artworkInfo.map((artworkInf: ArtworkInfo) => ({
			...artworkInf,
			newId: '',
		}));

		const artworksToCreate = artworkInfoCopy?.reduce(
			(
				accumulator: (ArtworkInfo & { artworkInfoIndex: number })[],
				artworkInf: {
					newId: string;
					id: string;
					text: string;
					status: string;
				},
				i: number
			) => {
				// If no id, then it is a brand new artwork
				if (!artworkInf.id) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				return accumulator;
			},
			[]
		);

		const artworksToUpdate = artworkInfoCopy?.reduce(
			(
				accumulator: (ArtworkInfo & { artworkInfoIndex: number })[],
				artworkInf: {
					newId: string;
					id: string;
					text: string;
					status: string;
				},
				i: number
			) => {
				// If no id, then it is a brand new artwork
				if (!artworkInf.id) {
					return accumulator;
				}

				// We look for a corresponding artwork in the page data
				const correspondingDataArtwork = (artworks || []).find(
					(
						artwork: NonNullable<GetPdfQuery['PDF'][number]['artworks']>[number]
					) => artwork?.id === artworkInf.id
				);

				// If submitting, we need to update the artwork status
				if (status === 'AWAITING_LABEL_PARSER') {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				// If the text or the status does not match, we recreate an artwork
				if (
					correspondingDataArtwork?.artwork_details?.description !==
						artworkInf.text ||
					correspondingDataArtwork?.artwork_details?.sale_status !==
						artworkInf.status
				) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				const correspondingDataArtworkImages =
					correspondingDataArtwork?.images?.map(
						(image) => image?.directus_files_id?.filename_disk
					);

				// If images don't match, then we recreate an artwork
				if (
					JSON.stringify(correspondingDataArtworkImages) !==
					JSON.stringify(
						$artworkImages[i].images.map((image) => image.filename_disk)
					)
				) {
					return [...accumulator, { ...artworkInf, artworkInfoIndex: i }];
				}

				return accumulator;
			},
			[]
		);

		const artworksToDelete = artworks?.filter(
			(
				dataArtwork: NonNullable<GetPdfQuery['PDF'][number]['artworks']>[number]
			) => {
				const artworkIdsToSave = artworkInfoCopy.map(
					(artworkInfo: {
						newId: string;
						id: string;
						text: string;
						status: string;
					}) => artworkInfo.id
				);

				if (!dataArtwork?.id || !artworkIdsToSave.includes(dataArtwork?.id)) {
					return true;
				}

				return false;
			}
		);

		const currentDiscardedImages = [
			...(images || [])
				.map((image, i) =>
					[
						SelectedImageStatus.RecentlyDiscarded,
						SelectedImageStatus.Discarded,
						SelectedImageStatus.ExcludedFromFormat,
					].includes(selectedImageStatuses[i].status)
						? {
								...image,
								discardTimestamp: selectedImageStatuses[i].discardTimestamp,
							}
						: null
				)
				.filter(Boolean),
		] as (EditExtractedImage & {
			discardTimestamp: number;
			new_pdf_discard_image_id?: string;
		})[];

		const pdfDiscardImagesToCreate = currentDiscardedImages
			?.map((currentDiscardedImage, i) => {
				const existingPdfDiscardImage = discardedImages?.find(
					(discardedImage) =>
						discardedImage?.image?.filename_disk ===
						currentDiscardedImage?.filename_disk
				);

				if (!existingPdfDiscardImage) {
					return { ...currentDiscardedImage, imagesIndex: i };
				}

				return null;
			})
			.filter(Boolean) as (EditExtractedImage & {
			discardTimestamp: number;
			imagesIndex: number;
		})[];

		const pdfDiscardImagesToUpdate = currentDiscardedImages
			?.map((currentDiscardedImage, i) => {
				const existingPdfDiscardImage = discardedImages?.find(
					(discardedImage) =>
						discardedImage?.image?.filename_disk ===
						currentDiscardedImage?.filename_disk
				);

				if (
					!!existingPdfDiscardImage &&
					+new Date(existingPdfDiscardImage.timestamp) !==
						currentDiscardedImage?.discardTimestamp
				) {
					return {
						...currentDiscardedImage,
						imagesIndex: i,
					};
				}

				return null;
			})
			.filter(Boolean) as (EditExtractedImage & {
			discardTimestamp: number;
			imagesIndex: number;
		})[];

		const pdfDiscardImagesToDelete = discardedImages?.filter(
			(discardedImage) =>
				!currentDiscardedImages?.find(
					(currentDiscardedImage) =>
						currentDiscardedImage?.filename_disk ===
						discardedImage?.image?.filename_disk
				)
		);

		const promises: Promise<any>[] = [];

		if (pdfDiscardImagesToCreate?.length) {
			promises.push(
				...pdfDiscardImagesToCreate.map((pdfDiscardImageToCreate) => {
					return new Promise((r) => {
						createPdfDiscardImage(pdfDiscardImageToCreate).then(
							(pdfDiscardImageCreated) => {
								currentDiscardedImages[
									pdfDiscardImageToCreate.imagesIndex
								].new_pdf_discard_image_id = pdfDiscardImageCreated?.data
									?.id as string;

								r(1);
							}
						);
					});
				})
			);
		}

		if (pdfDiscardImagesToUpdate?.length) {
			promises.push(
				...pdfDiscardImagesToUpdate.map((pdfDiscardImageToUpdate) => {
					return updatePdfDiscardImage(pdfDiscardImageToUpdate);
				})
			);
		}

		if (pdfDiscardImagesToDelete?.length) {
			promises.push(
				$deletePdfDiscardImagesMutation.mutateAsync({
					ids: pdfDiscardImagesToDelete?.map(
						(pdfDiscardImageToDelete) => pdfDiscardImageToDelete?.id as string
					),
				})
			);
		}

		if (artworksToCreate?.length) {
			promises.push(
				...artworksToCreate.map((artworkToCreate) => {
					return new Promise((r) => {
						createArtwork(status, artworkToCreate).then((artworkCreated) => {
							artworkInfoCopy[artworkToCreate.artworkInfoIndex].newId =
								artworkCreated.data?.id as string;

							r(1);
						});
					});
				})
			);
		}

		if (artworksToUpdate?.length) {
			promises.push(
				...artworksToUpdate.map((artworkToUpdate) => {
					return updateArtwork(status, artworkToUpdate);
				})
			);
		}

		if (artworksToDelete?.length) {
			promises.push(
				$deletePdfArtworks.mutateAsync({
					ids: artworksToDelete?.map(
						(artworkToDelete) => artworkToDelete?.id as string
					),
				})
			);
		}

		promises.push(
			$updatePdf.mutateAsync({
				id: page.params.pdfId,
				data: {
					...(status !== 'DRAFT' && {
						submitted_for_review: true,
					}),
					...(formatToggleState.images && {
						pdf_artwork_format_method: formatToggleState.method,
					}),
				},
			})
		);

		try {
			await Promise.all(promises);
		} catch {
			showToast({
				variant: 'error',
				message: `Something went wrong. Please contact support.`,
			});

			error = true;
		}

		if (error) {
			errorCallback();
			return;
		}
	};

	const handleClickSaveProgress = async () => {
		saveProgressSubmitting = true;

		await handleSave('DRAFT', () => {
			saveProgressSubmitting = false;
		});

		showToast({
			variant: 'success',
			message: `Your progress has successfully been saved.`,
		});

		goto(`/${page.route.id?.split('/')[1]}/${page.params.id}`);
	};

	const handleChangeFormat = async () => {
		let newArtworks: ReturnType<typeof formatArtworksByPage>;

		const formatToggleStateImages = formatToggleState.images;
		let formatSelectedImageStatuses = formatToggleState.selectedImageStatuses;

		if (formatToggleStateImages && formatSelectedImageStatuses) {
			if (formatToggleState.method === FormatMethod.LineBreak) {
				formatToggleState = {
					...formatToggleState,
					method: FormatMethod.Page,
				};

				formatSelectedImageStatuses = formatSelectedImageStatuses.map(
					(status) => {
						if (status.status === SelectedImageStatus.ExcludedFromFormat) {
							return {
								status: SelectedImageStatus.Selected,
								discardTimestamp: null,
							};
						}

						return status;
					}
				);

				newArtworks = formatArtworksByPage(
					formatToggleStateImages,
					formatSelectedImageStatuses
				);
			} else {
				formatToggleState = {
					...formatToggleState,
					method: FormatMethod.LineBreak,
				};

				if (pdfAiExtractedText) {
					newArtworks = formatArtworksUsingAiText(
						images,
						selectedImageStatuses,
						pdfAiExtractedText
					);
				} else {
					newArtworks = formatArtworksUsingLineBreaks(
						images,
						selectedImageStatuses
					);
				}

				formatSelectedImageStatuses = formatSelectedImageStatuses.map(
					(status, i) => {
						if (
							Object.values(newArtworks).find((artwork) =>
								artwork?.images?.images?.find(
									(image) => image.id === images[i]?.filename_disk
								)
							) ||
							status.status !== SelectedImageStatus.Selected
						) {
							return status;
						}

						return {
							status: SelectedImageStatus.ExcludedFromFormat,
							discardTimestamp: +new Date(),
						};
					}
				);
			}

			images = [...formatToggleStateImages];
			selectedImageStatuses = [...formatSelectedImageStatuses];
			resetArtworksTable(formatToggleState.pages, newArtworks);
		}
	};

	const handleClickEdit = () => {
		editDescriptionsDialogStores.states.open.set(true);
	};

	const onSubmit = async () => {
		submitSubmitting = true;

		await handleSave('AWAITING_LABEL_PARSER', () => {
			submitSubmitting = false;
		});
	};

	let dataCyPrefix = $derived(`${dataCy}-attach-text-artworks`);
</script>

<Container
	dataCy={dataCyPrefix}
	class={classNames({ 'pointer-events-none': submitting })}
>
	<div class="mb-4 mt-[-1rem] flex justify-between">
		<Button
			dataCy={`${dataCyPrefix}-back`}
			variant="secondary"
			size="md"
			onclick={() => {
				if (artworks?.length || discardedImages?.length) {
					backDialogStores.states.open.set(true);
				} else {
					showTable = false;
				}
			}}
		>
			{#snippet leading()}
				<ChevronLeftIcon />
			{/snippet}
			Back to image selector
		</Button>

		<LinkButton
			size="md"
			dataCy={`${dataCyPrefix}-download-pdf`}
			href={pdfLink}
			newTab
			icon
			variant="secondary"
		>
			{#snippet leading()}
				<DownloadIcon />
			{/snippet}
			download pdf
		</LinkButton>
	</div>
</Container>

<div
	class={classNames(
		'sticky top-0 z-20 mb-6 border-b border-gray-200 bg-white pt-4',
		{ 'pointer-events-none': submitting }
	)}
>
	<Container dataCy={dataCyPrefix} class="mb-3 flex items-end justify-between">
		<div>
			<Txt variant="body3" class="col-span-2 mb-2 uppercase tracking-[1.68px]"
				>{title}</Txt
			>
			<Txt variant="h4">Edit extracted images</Txt>
		</div>

		{#if pdfAiExtractedText}
			<div class="flex items-center gap-2">
				<Txt variant="label3">Page</Txt>
				<Switch
					dataCy={`${dataCyPrefix}-format`}
					checked={formatToggleState.method === FormatMethod.LineBreak}
					onChange={handleChangeFormat}
				/>
				<Txt variant="label3">Line Break</Txt>
			</div>
		{/if}

		<div class="flex items-center justify-end gap-6">
			<div class="flex items-center justify-end gap-1.5">
				<StatusSelect
					dataCy={`${dataCy}-all`}
					{artworkSaleStatuses}
					bind:value={allStatusValue}
				/>

				<ChangeStatusForAllDialog
					dialogStores={changeStatusDialogStores}
					{allStatusValue}
				/>

				<Button
					size="md"
					{dataCy}
					type="button"
					disabled={submitting}
					onclick={(e) => {
						changeStatusDialogStores.states.open.set(true);
					}}>Change Status for all</Button
				>
			</div>

			<ArtworksTextDiscardedImagesDrawer
				{drawerStores}
				{images}
				{selectedImageStatuses}
				onDrag={handleSelectImage}
			/>

			{#if formatToggleState?.method === FormatMethod.LineBreak && pdfAiExtractedText}
				<Button
					onclick={handleClickEdit}
					size="md"
					variant="secondary"
					disabled={isArtworkInfoMultipleParagraphs($artworkInfo)}
					{dataCy}>Edit</Button
				>
			{/if}

			<Button
				onclick={handleClickViewDiscardedImages}
				size="md"
				variant="secondary"
				disabled={areAllImagesSelected(selectedImageStatuses)}
				{dataCy}>View discarded images</Button
			>
		</div>
	</Container>
</div>

<Container
	dataCy={dataCyPrefix}
	class={classNames({ 'pointer-events-none': submitting })}
>
	<ArtworksTextTable
		{formatToggleState}
		{artworkSaleStatuses}
		onDeleteImage={handleDeleteImageInTable}
		dataCy={dataCyPrefix}
		{hasSubmitted}
		{submitting}
		{onSubmitFiles}
		showPage
	/>
</Container>

<div class="fixed bottom-0 z-20 w-full border-t border-gray-200 bg-gray-0 py-4">
	<Container dataCy={`${dataCy}-save`} class="flex justify-end">
		<div class="flex gap-3">
			<Button
				onclick={handleClickSaveProgress}
				variant="secondary"
				loading={saveProgressSubmitting}
				disabled={submitting}
				dataCy={`${dataCy}-save-progress`}
				size="md">save progress</Button
			>
			<Button
				loading={submitSubmitting}
				disabled={submitting ||
					(isArtworkInfoMultipleParagraphs($artworkInfo) &&
						formatToggleState.method === FormatMethod.LineBreak)}
				dataCy={`${dataCy}-save-submit`}
				size="md"
				onclick={handleClickSubmit}>submit</Button
			>
		</div>
	</Container>
</div>

<BackDialog
	{artworks}
	{discardedImages}
	dataCy={dataCyPrefix}
	dialogStores={backDialogStores}
/>

<ConfirmationDialog
	onSave={onSubmit}
	{submitSubmitting}
	{errorButtonProps}
	{dialogStores}
	{button2Props}
	{buttonProps}
	{dataCy}
	{slotLabel}
	successMessage="Artworks from pdfs have been submitted successfully"
	errorMessage="We weren't able to extract your PDF, please try again"
>
	{@render children?.()}
</ConfirmationDialog>

<EditDescriptionsDialog
	{pages}
	{pdfAiExtractedText}
	dialogStores={editDescriptionsDialogStores}
	dataCy={dataCyPrefix}
/>
