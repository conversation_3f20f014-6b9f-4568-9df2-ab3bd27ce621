<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { DeletePdfArtworksDocument } from '$lib/features/edit-extracted-images/queries/__generated__/deletePdfArtworks.generated';
	import { DeletePdfDiscardImagesDocument } from '$lib/features/edit-extracted-images/queries/__generated__/deletePdfDiscardImages.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { type GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		dataCy: string;
		discardedImages: GetPdfQuery['PDF'][number]['discarded_images'];
		artworks: GetPdfQuery['PDF'][number]['artworks'];
	}

	let { dialogStores, dataCy, discardedImages, artworks }: Props = $props();

	let submitting = $state(false);

	const handleClickBack = () => {
		dialogStores.states.open.set(false);
	};

	const handleClickConfirm = async () => {
		submitting = true;

		const artworksIds = artworks?.map((artwork) => artwork?.id) as string[];
		const discardedImagesIds = discardedImages?.map(
			(discardedImage) => discardedImage?.id
		) as string[];

		await gqlClient.request(
			DeletePdfArtworksDocument,
			{
				ids: artworksIds,
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		await gqlClient.request(
			DeletePdfDiscardImagesDocument,
			{
				ids: discardedImagesIds,
			},
			getAuthorizationHeaders(page.data as { user: { access_token: string } })
		);

		window.location.reload();
	};

	let dataCyPrefix = $derived(`${dataCy}-back`);
</script>

<Dialog
	dataCy={dataCyPrefix}
	{dialogStores}
	shouldClose={false}
	showCloseIcon={false}
	showOverlay
	class="w-[31.25rem] sm:p-8"
>
	<Txt variant="h4" class="mb-6 text-center"
		>Going back to the image selector will discard the current progress. Are you
		sure you would like to continue ?</Txt
	>

	<div class="mt-6 grid grid-cols-2 gap-2">
		<Button
			onclick={handleClickBack}
			size="md"
			disabled={submitting}
			dataCy={`${dataCyPrefix}-back`}
			variant="secondary">back</Button
		>
		<Button
			size="md"
			onclick={handleClickConfirm}
			loading={submitting}
			dataCy={`${dataCyPrefix}-back`}>confirm</Button
		>
	</div>
</Dialog>
