<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Input } from '$global/components/Input';
	import { Txt } from '$global/components/Txt';
	import { findIndexes } from '$global/utils/findIndexes/findIndexes';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import { type GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';

	interface Props {
		pdfAiExtractedText: string | undefined | null;
		dialogStores: ReturnType<typeof createDialog>;
		dataCy: string;
		pages: GetPdfQuery['PDF'][number]['pages'];
	}

	let { pdfAiExtractedText, dialogStores, dataCy, pages }: Props = $props();

	const { artworkInfo, artworkImages } = getArtworksTextTableContext();

	let text = $state('');

	let dialogOpen = $derived(dialogStores.states.open);

	$effect(() => {
		if ($dialogOpen && !text) {
			text = $artworkInfo
				.map((info) => {
					return info.text;
				})
				.join('\n\n');
		}
	});

	let paragraphs = $derived(text.split(/\n{2,}/).filter(Boolean));

	const handleClose = () => {
		dialogStores.states.open.set(false);
		text = '';
	};

	const handleClickParse = () => {
		const newArtworkInfo = (() => {
			const formatParagraph = (paragraph: string, index: number) => ({
				text: paragraph,
				status: $artworkInfo[index]?.status || 'FOR_SALE',
				id: $artworkInfo[index]?.id || '',
			});

			if (paragraphs.length > $artworkInfo.length) {
				return paragraphs.map(formatParagraph);
			} else {
				return $artworkInfo.map((_, index) => {
					if (index > paragraphs.length - 1) {
						return {
							...formatParagraph('', index),
							text: '',
						};
					} else {
						return formatParagraph(paragraphs[index], index);
					}
				});
			}
		})();

		artworkInfo.set(newArtworkInfo);

		if ($artworkInfo.length > $artworkImages.length) {
			const newArtworkImages = [];
			const diff = $artworkInfo.length - $artworkImages.length;
			for (let i = 0; i < diff; i++) {
				newArtworkImages.push({ images: [], page: undefined });
			}

			artworkImages.set([...$artworkImages, ...newArtworkImages]);
		}

		const emptyArtworksIndexes = findIndexes(
			$artworkInfo,
			(info, i) => !info.text && !$artworkImages[i].images.length
		);

		artworkImages.set(
			$artworkImages.filter((_, i) => !emptyArtworksIndexes.includes(i))
		);

		artworkInfo.set(
			$artworkInfo.filter((_, i) => !emptyArtworksIndexes.includes(i))
		);

		dialogStores.states.open.set(false);
		text = '';
	};

	const handleResetOriginalText = () => {
		text = (pages || [])
			.map((page) => page?.text)
			.filter(Boolean)
			.join('\n\n');
	};

	const handleResetAiText = () => {
		if (pdfAiExtractedText) {
			text = pdfAiExtractedText;
		}
	};

	let dataCyPrefix = $derived(`${dataCy}-edit-descriptions`);
</script>

<Dialog
	dataCy={dataCyPrefix}
	{dialogStores}
	showOverlay
	class="h-[98vh] max-h-[98vh] w-[98vw] max-w-[98vw] sm:p-8"
>
	<div class="flex h-full flex-col gap-4">
		<div>
			<Txt variant="h4" class="mb-1">Edit extracted text</Txt>
			<Txt variant="body2" class="mb-2"
				>Please ensure each artwork description is separated by a line break.</Txt
			>
		</div>
		<div class="flex-grow">
			<Input
				name="text"
				dataCy={`${dataCyPrefix}-text`}
				rows={5}
				bind:value={text}
				classes={{ wrapper: 'h-[calc(100%-24px)] w-full' }}
			/>
			<Txt variant="body2" class="mt-1"
				>{paragraphs.length} artworks detected</Txt
			>
		</div>

		<div class="flex justify-between">
			<div class="flex gap-2">
				<Button
					dataCy={`${dataCyPrefix}-cancel`}
					variant="secondary"
					size="md"
					onclick={handleClose}
				>
					cancel
				</Button>

				{#if (pages || []).map((page) => page?.text).filter(Boolean)}
					<Button
						dataCy={`${dataCyPrefix}-reset-to-original-text`}
						variant="secondary"
						size="md"
						onclick={handleResetOriginalText}
					>
						reset to original text{pdfAiExtractedText ? '' : ' (default)'}
					</Button>
				{/if}

				{#if pdfAiExtractedText}
					<Button
						dataCy={`${dataCyPrefix}-reset-to-original-text`}
						variant="secondary"
						size="md"
						onclick={handleResetAiText}
					>
						reset to AI text (default)
					</Button>
				{/if}
			</div>

			<Button
				dataCy={`${dataCyPrefix}-parse`}
				size="md"
				onclick={handleClickParse}
			>
				parse edited text
			</Button>
		</div>
	</div>
</Dialog>
