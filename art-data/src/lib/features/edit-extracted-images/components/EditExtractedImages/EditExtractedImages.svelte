<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { onMount } from 'svelte';
	import { SelectedImageStatus } from '../../types';
	import type {
		FormatMethod,
		FormatToggleState,
		EditExtractedImage,
	} from '../../types';
	import { AttachTextToArtworks } from './AttachTextToArtworks';
	import { SelectImages } from './SelectImages';
	import { Breadcrumbs, type Crumb } from '$global/components/Breadcrumbs';
	import type { ErrorSuccessPageProps } from '$global/features/form/pages/ErrorSuccessPage';
	import { PageBody } from '$lib/components/PageBody';
	import { getArtworksTextTableContext } from '$lib/features/artwork-text-table/utils/getArtworksTextTableContext/getArtworksTextTableContext';
	import type { GetCountriesQuery } from '$lib/queries/__generated__/getCountries.generated';
	import type { GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';
	import type { ArtworkImages, ArtworkFile } from '$lib/types';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';

	interface Props {
		crumbs: Crumb[];
		buttonProps: ErrorSuccessPageProps['buttonProps'];
		button2Props?: ErrorSuccessPageProps['button2Props'] | undefined;
		pdfArtist: NonNullable<GetPdfQuery['PDF'][number]>['processed_artist'];
		pdfArtworkFormatMethod: string | null | undefined;
		pdfTitle: string;
		pdfArtistCountry: GetCountriesQuery['country'][number] | undefined | null;
		pdfLink: string;
		pdfAiExtractedText: string | null | undefined;
		dataCy: string;
		slotLabel: string;
		artworks: GetPdfQuery['PDF'][number]['artworks'];
		pages: GetPdfQuery['PDF'][number]['pages'];
		discardedImages: GetPdfQuery['PDF'][number]['discarded_images'];
		title: string;
		artworkSaleStatuses: {
			key: string;
			name: string;
		}[];
		children?: import('svelte').Snippet;
	}

	let {
		crumbs,
		buttonProps,
		button2Props = undefined,
		pdfArtist,
		pdfArtworkFormatMethod,
		pdfTitle,
		pdfArtistCountry,
		pdfLink,
		pdfAiExtractedText,
		dataCy,
		slotLabel,
		artworks,
		pages,
		discardedImages,
		title,
		artworkSaleStatuses,
		children,
	}: Props = $props();

	const { artworkImages, artworkInfo, artworkFiles } =
		getArtworksTextTableContext();

	onMount(() => {
		let files: ArtworkFile[] = [];
		let i = 0;
		let paginatedSelectedImageStatuses =
			[] as ((typeof selectedImageStatuses)[number] & { page: number })[];

		if (artworks?.length) {
			const newArtworks = artworks.map((artwork) => {
				const images = (artwork?.images || []).map((image) => ({
					...image?.directus_files_id,
					url: `${getImageUrl(image?.directus_files_id?.id)}`,
					id: image?.directus_files_id?.filename_disk,
					page: (pages || []).find((page) =>
						(page?.extracted_images || []).some(
							(extractedImage) =>
								extractedImage?.image?.id === image?.directus_files_id?.id
						)
					)?.page_number,
				}));

				const pageNumbers = (artwork?.images || [])
					.map(
						(artworkImage) =>
							(pages || []).find((page) =>
								(page?.extracted_images || []).some(
									(extractedImage) =>
										extractedImage?.image?.id ===
										artworkImage?.directus_files_id?.id
								)
							)?.page_number
					)
					.filter(Boolean) as number[];

				return {
					images: {
						images,
						page: Array.from(new Set(pageNumbers)),
					} as ArtworkImages,
					info: {
						status: `${artwork?.artwork_details?.sale_status}`,
						text: `${artwork?.artwork_details?.description}`,
						id: `${artwork?.id}`,
					},
				};
			});

			files = (artworks || []).flatMap((artwork) =>
				(artwork?.images || [])?.map((artworkImage) => ({
					url: `${getImageUrl(artworkImage?.directus_files_id?.id)}`,
					name: `${artworkImage?.directus_files_id?.filename_disk}`,
					storage: `${artworkImage?.directus_files_id?.storage}`,
					id: `${artworkImage?.directus_files_id?.id}`,
					filename_disk: `${artworkImage?.directus_files_id?.filename_disk}`,
					filename_download: `${artworkImage?.directus_files_id?.filename_download}`,
				}))
			);

			images = (artworks || []).flatMap((artwork) =>
				(artwork?.images || [])?.map((artworkImage) => {
					const correspondingPage = pages?.find((page) =>
						page?.extracted_images?.find(
							(extractedImage) =>
								extractedImage?.image?.filename_disk ===
								artworkImage?.directus_files_id?.filename_disk
						)
					);

					return {
						pdf_discard_image_id: '',
						storage: `${artworkImage?.directus_files_id?.storage}`,
						page: correspondingPage?.page_number || 0,
						filename_disk: `${artworkImage?.directus_files_id?.filename_disk}`,
						filename_download: `${artworkImage?.directus_files_id?.filename_download}`,
						url: `${getImageUrl(`${artworkImage?.directus_files_id?.id}`)}`,
						width: artworkImage?.directus_files_id?.width || 1,
						height: artworkImage?.directus_files_id?.height || 1,
						alt: '',
						file: null,
						id: `${artworkImage?.directus_files_id?.id}`,
						text: correspondingPage?.text || '',
					};
				})
			);

			(artworks || []).forEach((artwork) => {
				(artwork?.images || [])?.forEach(() => {
					paginatedSelectedImageStatuses = [
						...paginatedSelectedImageStatuses,
						{
							discardTimestamp: null,
							status: SelectedImageStatus.Selected,
							page: images[i].page,
						},
					];

					i++;
				});
			});

			artworkImages.set(
				Object.values(newArtworks).map((artwork) => artwork.images)
			);

			artworkInfo.set(
				Object.values(newArtworks).map((artwork) => artwork.info)
			);
		}

		if (discardedImages?.length) {
			images = [
				...images,
				...(discardedImages || []).map((discardedImage) => {
					const correspondingPage = pages?.find((page) =>
						page?.extracted_images?.find(
							(extractedImage) =>
								extractedImage?.image?.filename_disk ===
								discardedImage?.image?.filename_disk
						)
					);

					return {
						file: null,
						pdf_discard_image_id: discardedImage?.id,
						storage: discardedImage?.image?.storage,
						page: correspondingPage?.page_number,
						filename_disk: discardedImage?.image?.filename_disk,
						filename_download: discardedImage?.image?.filename_download,
						url: `${getImageUrl(discardedImage?.image?.id)}`,
						width: discardedImage?.image?.width,
						height: discardedImage?.image?.height,
						alt: '',
						id: discardedImage?.image?.id,
						text: correspondingPage?.text || '',
					};
				}),
			] as EditExtractedImage[];

			(discardedImages || []).forEach((discardedImage) => {
				paginatedSelectedImageStatuses = [
					...paginatedSelectedImageStatuses,
					{
						status: SelectedImageStatus.RecentlyDiscarded,
						discardTimestamp: +new Date(discardedImage?.timestamp),
						page: images[i].page,
					},
				];

				i++;
			});

			files = [
				...files,
				...(discardedImages || []).map((discardedImage) => ({
					name: `${discardedImage?.image?.filename_disk}`,
					storage: `${discardedImage?.image?.storage}`,
					filename_disk: `${discardedImage?.image?.filename_disk}`,
					filename_download: `${discardedImage?.image?.filename_download}`,
					url: `${getImageUrl(discardedImage?.image?.id)}`,
					id: `${discardedImage?.image?.id}`,
				})),
			];
		}

		if (artworks?.length || discardedImages?.length) {
			paginatedSelectedImageStatuses
				.sort(
					(imageA, imageB) =>
						(imageA as unknown as { page: number }).page -
						(imageB as unknown as { page: number }).page
				)
				.map(({ page, ...restImage }: { page: number }) => restImage);
			images.sort((imageA, imageB) => imageA.page - imageB.page);
			selectedImageStatuses = [...paginatedSelectedImageStatuses];

			formatToggleState.images = [...images];
			formatToggleState.selectedImageStatuses = [
				...paginatedSelectedImageStatuses,
			];

			artworkFiles.set(files);
			showTable = true;
		}

		ready = true;
	});

	let ready = $state(false);
	let images: EditExtractedImage[] = $state([]);
	let showTable = $state(false);
	let submitSubmitting = false;
	let submitting = $derived(submitSubmitting);

	const dialogStores = createDialog();
	let dataCyPrefix = $derived(`${dataCy}-edit-extracted-images`);

	const errorButtonProps = {
		label: 'back to pdf extraction',
		onClick: () => {
			dialogStores.states.open.set(false);
		},
	};

	let selectedImageStatuses: {
		status: SelectedImageStatus;
		discardTimestamp: null | number;
	}[] = $state([]);

	// State for the toggle
	// Available only when the user has selected the images but has not saved their progress yet
	let formatToggleState: FormatToggleState = $state({
		method: pdfArtworkFormatMethod as FormatMethod,
	});
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 lg:mb-11 lg:mt-0"
		{crumbs}
	/>

	{#if ready}
		{#if !showTable}
			<SelectImages
				{title}
				{pdfLink}
				{pages}
				{pdfAiExtractedText}
				bind:formatToggleState
				bind:images
				{slotLabel}
				dataCy={dataCyPrefix}
				bind:selectedImageStatuses
				bind:showTable
			>
				{@render children?.()}
			</SelectImages>
		{:else}
			<AttachTextToArtworks
				{pages}
				{pdfArtist}
				{pdfArtistCountry}
				{pdfAiExtractedText}
				{slotLabel}
				{pdfLink}
				{errorButtonProps}
				{title}
				{pdfTitle}
				{discardedImages}
				{artworkSaleStatuses}
				{submitting}
				{artworks}
				{dialogStores}
				bind:formatToggleState
				bind:images
				dataCy={dataCyPrefix}
				bind:selectedImageStatuses
				bind:showTable
				{buttonProps}
				{button2Props}
			>
				{@render children?.()}
			</AttachTextToArtworks>
		{/if}
	{/if}
</PageBody>
