import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type GetPdfImagesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Pdf_Filter>;
	pagesFilter?: Types.InputMaybe<Types.Pdf_Page_Filter>;
}>;

export type GetPdfImagesQuery = {
	__typename?: 'Query';
	PDF: Array<{
		__typename?: 'PDF';
		pages?: Array<{
			__typename?: 'PDF_Page';
			id: string;
			page_number?: number | null;
			status?: string | null;
			text?: string | null;
			extracted_images?: Array<{
				__typename?: 'PDF_Image';
				image?: {
					__typename?: 'directus_files';
					filename_disk?: string | null;
					width?: number | null;
					height?: number | null;
					filename_download: string;
					storage: string;
					id: string;
				} | null;
			} | null> | null;
		} | null> | null;
	}>;
};

export const GetPdfImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getPdfImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PDF_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'pagesFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PDF_Page_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'PDF' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'pages' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'pagesFilter' },
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'page_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'text' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'extracted_images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetPdfImagesQuery, GetPdfImagesQueryVariables>;
