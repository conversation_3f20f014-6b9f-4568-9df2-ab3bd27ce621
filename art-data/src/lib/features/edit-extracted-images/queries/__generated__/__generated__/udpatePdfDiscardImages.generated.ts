import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../../gql/types';

export type UpdatePdfDiscardImagesMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Update_Pdf_Discard_Image_Input>
		| Types.Update_Pdf_Discard_Image_Input
	>;
}>;

export type UpdatePdfDiscardImagesMutation = {
	__typename?: 'Mutation';
	update_PDF_Discard_Image_batch: Array<{
		__typename?: 'PDF_Discard_Image';
		id: string;
	}>;
};

export const UpdatePdfDiscardImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updatePdfDiscardImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'update_PDF_Discard_Image_input' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_PDF_Discard_Image_batch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdatePdfDiscardImagesMutation,
	UpdatePdfDiscardImagesMutationVariables
>;
