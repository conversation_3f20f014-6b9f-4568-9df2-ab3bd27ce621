import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreatePdfArtworksMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		Array<Types.Create_Pdf_Artwork_Input> | Types.Create_Pdf_Artwork_Input
	>;
}>;

export type CreatePdfArtworksMutation = {
	__typename?: 'Mutation';
	create_PDF_Artwork_items: Array<{
		__typename?: 'PDF_Artwork';
		id: string;
		images?: Array<{
			__typename?: 'PDF_Artwork_files';
			directus_files_id?: {
				__typename?: 'directus_files';
				filename_disk?: string | null;
				height?: number | null;
				width?: number | null;
			} | null;
		} | null> | null;
	}>;
};

export const CreatePdfArtworksDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createPdfArtworks' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'create_PDF_Artwork_input' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_PDF_Artwork_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreatePdfArtworksMutation,
	CreatePdfArtworksMutationVariables
>;
