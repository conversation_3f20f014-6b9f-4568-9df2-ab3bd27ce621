import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreatePdfDiscardImagesMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Create_Pdf_Discard_Image_Input>
		| Types.Create_Pdf_Discard_Image_Input
	>;
}>;

export type CreatePdfDiscardImagesMutation = {
	__typename?: 'Mutation';
	create_PDF_Discard_Image_items: Array<{
		__typename?: 'PDF_Discard_Image';
		id: string;
		image?: {
			__typename?: 'directus_files';
			filename_disk?: string | null;
			height?: number | null;
			width?: number | null;
		} | null;
	}>;
};

export const CreatePdfDiscardImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createPdfDiscardImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'create_PDF_Discard_Image_input' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_PDF_Discard_Image_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreatePdfDiscardImagesMutation,
	CreatePdfDiscardImagesMutationVariables
>;
