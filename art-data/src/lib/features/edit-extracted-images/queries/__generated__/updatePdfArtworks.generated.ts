import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type UpdatePdfArtworksMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		Array<Types.Update_Pdf_Artwork_Input> | Types.Update_Pdf_Artwork_Input
	>;
}>;

export type UpdatePdfArtworksMutation = {
	__typename?: 'Mutation';
	update_PDF_Artwork_batch: Array<{ __typename?: 'PDF_Artwork'; id: string }>;
};

export const UpdatePdfArtworksDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updatePdfArtworks' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'update_PDF_Artwork_input' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_PDF_Artwork_batch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdatePdfArtworksMutation,
	UpdatePdfArtworksMutationVariables
>;
