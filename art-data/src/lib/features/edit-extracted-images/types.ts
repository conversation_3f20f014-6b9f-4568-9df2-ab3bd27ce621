import type { GetPdfQuery } from '$lib/queries/__generated__/getPdf.generated';

export type EditExtractedImage = {
	url: string;
	width: number;
	height: number;
	filename_disk: string;
	filename_download: string;
	alt: string;
	page: number;
	text: string;
	id: string;
	storage: string;
	file: File | null;
	pdf_discard_image_id: string;
};

export enum SelectedImageStatus {
	Selected = 'selected',
	ExcludedFromFormat = 'excluded-from-format',
	RecentlyDiscarded = 'recently-discarded',
	Discarded = 'discarded',
}

export enum FormatMethod {
	Page = 'page',
	LineBreak = 'line break',
}

export type FormatToggleState = {
	images?: EditExtractedImage[];
	pages?: GetPdfQuery['PDF'][number]['pages'];
	selectedImageStatuses?: {
		status: SelectedImageStatus;
		discardTimestamp: null | number;
	}[];
	method: FormatMethod;
};
