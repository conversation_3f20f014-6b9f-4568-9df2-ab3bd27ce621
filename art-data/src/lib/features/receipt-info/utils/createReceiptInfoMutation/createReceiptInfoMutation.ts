import { createMutation } from '@tanstack/svelte-query';
import { CreateReceiptInfoDocument } from '../../queries/__generated__/createReceiptInfo.generated';
import { page } from '$app/state';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getMutation } from '$lib/query-utils/getMutation';

export const createReceiptInfoMutation = () => {
	return createMutation(
		getMutation(
			CreateReceiptInfoDocument,
			getAuthorizationHeaders(
				page.data as Parameters<typeof getAuthorizationHeaders>[0]
			)
		)
	);
};
