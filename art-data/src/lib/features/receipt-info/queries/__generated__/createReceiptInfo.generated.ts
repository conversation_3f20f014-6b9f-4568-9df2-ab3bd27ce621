import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types';

export type CreateReceiptInfoMutationVariables = Types.Exact<{
	input: Types.Create_Receipt_Information_Input;
}>;

export type CreateReceiptInfoMutation = {
	__typename?: 'Mutation';
	create_Receipt_Information_item?: {
		__typename?: 'Receipt_Information';
		id: string;
	} | null;
};

export const CreateReceiptInfoDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createReceiptInfo' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_Receipt_Information_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_Receipt_Information_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateReceiptInfoMutation,
	CreateReceiptInfoMutationVariables
>;
