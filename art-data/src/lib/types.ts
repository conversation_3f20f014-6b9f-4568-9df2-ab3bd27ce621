import type { GetExhibitionsQuery } from './websites/exhibitions/queries/__generated__/getExhibitions.generated';
import { type ImageCarouselImage } from '$global/components/ImageCarousel';

export type Exhibition = GetExhibitionsQuery['Ingestion_Exhibition'][number];

export enum IngestionDataTypenames {
	ArtlogicLink = 'Artlogic_Link',
	Pdf = 'PDF',
	ManualUpload = 'Manual_Upload',
}

export enum ChecklistStatus {
	Draft = 'Draft',
	AwaitingExtraction = 'Awaiting Extraction',
	Extracting = 'extracting',
	Extracted = 'extracted',
	ExtractionFailed = 'Extraction Failed',
	AwaitingReview = 'Awaiting Review',
	ReviewedAndSubmitted = 'Reviewed and submitted',
	Submitted = 'submitted',
	Processed = 'processed',
	SentToIngestionSchema = 'sent_to_ingestion_schema',
}

export type Artwork = {
	id: string;
	images: {
		url: string;
		width: number;
		height: number;
		filename_disk: string;
		filename_download: string;
		storage: string;
		id: string;
	}[];
	text: string;
	artworkDetailsId: string | undefined;
	status: string;
};

export type ArtworkLabelInfo = {
	notes: string;
	install: boolean;
};

export type ArtworkFile = {
	filename_download: string;
	filename_disk: string;
	name: string;
	url: string;
	storage: string;
	id: string;
};

export type ArtworkInfo = {
	id: string;
	text: string;
	status: string;
};

export type ArtworkImages = {
	images: ImageCarouselImage[];
	page?: number[];
};

export type ReadyForReview = Array<{
	item?:
		| { __typename?: 'Artlogic_Link' }
		| {
				__typename?: 'Manual_Upload';
				manually_added_artworks?: Array<{
					__typename?: 'Manually_Added_Artwork';
					id: string;
				} | null> | null;
		  }
		| {
				__typename?: 'PDF';
				artworks?: Array<{
					__typename?: 'PDF_Artwork';
					id: string;
				} | null> | null;
		  }
		| null;
} | null> | null;

export type Skipped = Array<{
	item?:
		| { __typename?: 'Artlogic_Link' }
		| {
				__typename?: 'Manual_Upload';
				manually_added_artworks?: Array<{
					__typename?: 'Manually_Added_Artwork';
					id: string;
				} | null> | null;
		  }
		| {
				__typename?: 'PDF';
				artworks?: Array<{
					__typename?: 'PDF_Artwork';
					id: string;
				} | null> | null;
		  }
		| null;
} | null> | null;

export type Matches = Array<{
	item?:
		| { __typename?: 'Artlogic_Link' }
		| {
				__typename?: 'Manual_Upload';
				manually_added_artworks?: Array<{
					__typename?: 'Manually_Added_Artwork';
					id: string;
				} | null> | null;
		  }
		| {
				__typename?: 'PDF';
				artworks?: Array<{
					__typename?: 'PDF_Artwork';
					id: string;
				} | null> | null;
		  }
		| null;
} | null> | null;

export type ReviewedAndSubmitted = Array<{
	item?:
		| { __typename?: 'Artlogic_Link' }
		| {
				__typename?: 'Manual_Upload';
				manually_added_artworks?: Array<{
					__typename?: 'Manually_Added_Artwork';
					id: string;
				} | null> | null;
		  }
		| {
				__typename?: 'PDF';
				artworks?: Array<{
					__typename?: 'PDF_Artwork';
					id: string;
				} | null> | null;
		  }
		| null;
} | null> | null;

export type UploadListArtlogicLink = {
	__typename?: 'Artlogic_Link';
	id: string;
	high_priority?: boolean | null;
	includes_prices?: boolean | null;
	review_status?: {
		key?: string | null | undefined;
		code?: string | null | undefined;
	} | null;
	processed_gallery?:
		| {
				name?: string | null | undefined;
		  }
		| null
		| undefined;
	url?: string | null;
	title?: string | null;
	user_created?: {
		last_name?: string | null | undefined;
		first_name?: string | null | undefined;
	} | null;
	art_event_feed?:
		| {
				__typename?: 'art_event_feed' | undefined;
				artwork_feed?:
					| ({ __typename?: 'artwork_feed' | undefined; id: string } | null)[]
					| null
					| undefined;
		  }
		| undefined
		| null;
	status?: {
		__typename?: 'Artlogic_Link_Status';
		name?: string | null;
		key: string;
	} | null;
	receipt_info?: {
		__typename?: 'Receipt_Information';
		id: string;
		receive_date?: any | null;
		receiver?: string | null;
		sender?: string | null;
	} | null;
};

export type UploadListManualUpload = {
	__typename: 'Manual_Upload';
	id: string;
	status?: {
		key: string;
	} | null;
	for_freelancers?: boolean | null;
	high_priority?: boolean | null;
	includes_prices?: boolean | null;
	submitted_for_review?: boolean | null;
	// status?: {
	// 	__typename?: 'Upload_Status_Type';
	// 	name?: string | null;
	// 	key: string;
	// } | null;
	title?: string | null;
	user_created?: {
		last_name?: string | null | undefined;
		first_name?: string | null | undefined;
	} | null;
	review_status?: {
		key?: string | null | undefined;
		code?: string | null | undefined;
	} | null;
	ready_for_review?: Array<{
		id: string;
	} | null> | null;
	reviewed_and_submitted?: Array<{
		id: string;
	} | null> | null;
	matches?: Array<{
		id: string;
	} | null> | null;
	skipped?: Array<{
		id: string;
	} | null> | null;
	processed_fair_exhibitor_org?:
		| {
				name?: string | null | undefined;
		  }
		| null
		| undefined;
	manually_added_artworks?:
		| (
				| {
						id?: string | null | undefined;
						status?:
							| {
									key: string;
							  }
							| null
							| undefined;
				  }
				| null
				| undefined
		  )[]
		| null;
	receipt_info?: {
		__typename?: 'Receipt_Information';
		receive_date?: any | null;
		receiver?: string | null;
		sender?: string | null;
	} | null;
};

export type UploadListVisit =
	| {
			__typename?: 'Visit';
			id?: string | null | undefined;
			review_status?: {
				key?: string | null | undefined;
				code?: string | null | undefined;
			} | null;
			visit_images?: Array<{
				__typename?: 'Visit_Image';
				id: string;
				image_taken_date?: any | null;
				source?: string | null | undefined;
				status?: {
					__typename?: 'Visit_Image_Status';
					key: string;
					name?: string | null;
				} | null;
				photographer?: {
					__typename?: 'directus_users';
					id?: string | null | undefined;
					last_name?: string | null;
					first_name?: string | null;
				} | null;
				user_created?: {
					__typename?: 'directus_users';
					id?: string | null | undefined;
					last_name?: string | null;
					first_name?: string | null;
				} | null;
				extracted_best_guess_coordinates_from_api?: any | null;
				perspective_cropped_image_without_dimensions?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
				rectangular_cropped_image?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
				original_uncropped_image?: {
					__typename?: 'directus_files';
					id: string;
				} | null;
			} | null> | null;
	  }
	| null
	| undefined;

export type UploadListVisitImage = NonNullable<
	NonNullable<UploadListVisit>['visit_images']
>[number];

export type UploadListVisitArtwork = {
	__typename?: 'Visit_Artwork';
	is_installation_shot?: boolean | null | undefined;
	artwork_image?: {
		__typename?: 'Visit_Image';
		id: string;
		image_taken_date?: any | null;
		photographer?: {
			__typename?: 'directus_users';
			id?: string | null | undefined;
		} | null;
	} | null;
	label_image?: {
		__typename?: 'Visit_Image';
		id: string;
		image_taken_date?: any | null;
		photographer?: {
			__typename?: 'directus_users';
			id?: string | null | undefined;
		} | null;
	} | null;
	status?: {
		__typename?: 'Artwork_Status_Type';
		key: string;
	} | null;
};

export type UploadListVisitArtworks = UploadListVisitArtwork[];

export type UploadListPdf = {
	__typename?: 'PDF';
	includes_prices?: boolean | null;
	for_freelancers?: boolean | null;
	high_priority?: boolean | null;
	id: string;
	pdf_file?: {
		filename_disk?: string | null | undefined;
	} | null;
	status?: {
		key: string;
	} | null;
	title?: string | null;
	submitted_for_review?: boolean | null;
	user_created?: {
		last_name?: string | null | undefined;
		first_name?: string | null | undefined;
	} | null;
	processed_fair_exhibitor_org?:
		| {
				name?: string | null | undefined;
		  }
		| null
		| undefined;
	ready_for_review?: Array<{
		id: string;
	} | null> | null;
	skipped?: Array<{
		id: string;
	} | null> | null;
	matches?: Array<{
		id: string;
	} | null> | null;
	review_status?: {
		key?: string | null | undefined;
		code?: string | null | undefined;
	} | null;
	reviewed_and_submitted?: Array<{
		id: string;
	} | null> | null;
	artworks?:
		| (
				| {
						id?: string | null | undefined;
						status?:
							| {
									key: string;
							  }
							| null
							| undefined;
				  }
				| null
				| undefined
		  )[]
		| null;
	receipt_info?: {
		__typename?: 'Receipt_Information';
		receive_date?: any | null;
		sender?: string | null;
		receiver?: string | null;
	} | null;
};

export type ManualUpload = {
	id: string;
	name: string;
	date: string;
	uploadedBy: string;
	freelancer: string;
	priority: string;
	artworks: number;
	priceIncluded: string;
	sentBy: string;
	receivedBy: string;
	status: ChecklistStatus;
};

export type Image = {
	filename_disk: string | null | undefined;
	width: number | null | undefined;
	height: number | null | undefined;
	url: string;
	alt: string;
	page?: number;
};
