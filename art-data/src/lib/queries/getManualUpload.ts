import { gql } from 'graphql-tag';

export const GET_MANUAL_UPLOAD = gql`
	query getManualUpload($filter: Manual_Upload_filter) {
		Manual_Upload(filter: $filter) {
			id
			reference_files {
				directus_files_id {
					id
					title
					width
					height
					filename_disk
					filename_download
					storage
					id
				}
			}
			receipt_info {
				receive_date
			}
			processed_fair_exhibitor_org {
				entity
				id
				location
				name
				type
			}
			submitted_for_review
			manually_added_artworks {
				id
				images {
					directus_files_id {
						storage
						id
						filename_download
						filename_disk
						height
						width
					}
				}
				artwork_details {
					id
					description
					sale_status
				}
			}
		}
	}
`;
