import { gql } from 'graphql-tag';

export const GET_PHONE_REGISTRATION = gql`
	query getPhoneRegistration(
		$filter: Visit_Phone_Registration_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		Visit_Phone_Registration(
			filter: $filter
			sort: $sort
			limit: $limit
			offset: $offset
		) {
			id
			phone {
				id
				icloud_account
			}
			date_created
			start_date
			end_date
			location
			photographer {
				id
				first_name
				last_name
			}
		}
		Visit_Phone_Registration_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
