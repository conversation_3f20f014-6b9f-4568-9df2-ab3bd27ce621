import { gql } from 'graphql-tag';

export const GET_ARTWORK_DETAILS = gql`
	query getArtworkDetails(
		$filter: Artwork_Details_filter
		$limit: Int
		$offset: Int
	) {
		Artwork_Details(filter: $filter, limit: $limit, offset: $offset) {
			id
			artists {
				id
				name
				nationality {
					code
					name
				}
				year_birth
				year_death
				processed_artist_id
			}
			description
			crid
			processed_artwork_id
			# arteye_todos
			title
			executed_year_end
			executed_year_start
			media
			artwork_type {
				key
				name
			}
			dimensions
			dimension_type {
				key
				name
			}
			dimensions_width_cm
			dimensions_depth_cm
			dimensions_height_cm
			number_of_pieces
			number_of_artworks
			edition_description
			regular_edition_size
			artist_proof_size
			house_of_commerce_size
			total_edition_size
			series_size
			open_edition
			edition_number
			edition_number_type {
				key
				name
			}
			price
			artwork_removed
			currency {
				code
			}
			sale_status
		}
		Artwork_Details_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
