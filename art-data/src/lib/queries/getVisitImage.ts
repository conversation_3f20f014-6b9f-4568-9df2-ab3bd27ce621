import { gql } from 'graphql-request';

export const GET_VISIT_IMAGE = gql`
	query getVisitImage(
		$filter: Visit_Image_filter
		$offset: Int
		$limit: Int
		$sort: [String]
	) {
		Visit_Image(filter: $filter, limit: $limit, offset: $offset, sort: $sort) {
			id
			image_taken_date
			source
			visit {
				id
			}
			visit_phone_registration {
				id
				image_date
				phone {
					id
					icloud_account
				}
				phone_registration {
					end_date
					location
					photographer {
						id
					}
					start_date
					phone {
						id
						icloud_account
					}
				}
			}
			original_uncropped_image {
				id
				storage
				filename_disk
				filename_download
				width
				height
			}
			perspective_cropped_image_with_dimensions {
				id
				filename_disk
				width
				height
			}
			perspective_cropped_image_without_dimensions {
				id
				filename_disk
				width
				height
			}
			rectangular_cropped_image {
				id
				filename_disk
				width
				height
			}
			photographer {
				first_name
				last_name
				id
			}
			crop_type
			data_admin_submitted_coordinates
			extracted_best_guess_coordinates_from_api
		}
	}
`;
