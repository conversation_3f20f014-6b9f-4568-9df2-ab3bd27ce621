import { gql } from 'graphql-tag';

export const GET_PDF = gql`
	query getPdf($filter: PDF_filter) {
		PDF(filter: $filter) {
			id
			title
			receipt_info {
				receive_date
			}
			processed_fair_exhibitor_org {
				entity
				id
				location
				name
				type
			}
			pdf_file {
				id
				filename_disk
			}
			pdf_artwork_format_method
			processed_artist {
				first_name
				last_name
				year_birth
				year_death
				processed_artist_id
				nationality
			}
			artworks {
				id
				images {
					directus_files_id {
						id
						filename_disk
						filename_download
						storage
						width
						height
					}
				}
				artwork_details {
					description
					id
					sale_status
				}
				status {
					key
					name
				}
			}
			discarded_images {
				id
				timestamp
				image {
					filename_disk
					width
					height
					filename_download
					storage
					id
				}
			}
			ai_extracted_text
			pages {
				id
				page_number
				status
				text
				extracted_images {
					image {
						filename_disk
						width
						height
						filename_download
						storage
						id
					}
				}
			}
			submitted_for_review
		}
	}
`;
