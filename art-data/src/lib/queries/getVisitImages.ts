import { gql } from 'graphql-request';

export const GET_VISIT_IMAGES = gql`
	query getVisitImages(
		$filter: Visit_Image_filter
		$photographerFilter: Visit_Image_filter
		$sort: [String]
		$offset: Int
		$limit: Int
	) {
		Visit_Image(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			id
			image_taken_date
			crop_type
			original_uncropped_image {
				id
				filename_disk
				width
				height
			}
			visit_phone_registration {
				id
				image_date
			}
			photographer {
				id
			}
			perspective_cropped_image_with_dimensions {
				id
				filename_disk
				width
				height
			}
			perspective_cropped_image_without_dimensions {
				id
				filename_disk
				width
				height
			}
			rectangular_cropped_image {
				id
				filename_disk
				width
				height
			}
			visit_artwork {
				id
			}
		}
		Visit_Image_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
		photographers: Visit_Image_aggregated(
			filter: $photographerFilter
			groupBy: "photographer"
		) {
			group
		}
	}
`;
