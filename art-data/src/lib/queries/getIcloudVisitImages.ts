import { gql } from 'graphql-request';

export const GET_ICLOUD_VISIT_IMAGES = gql`
	query getIcloudVisitImages($filter: Visit_Image_filter) {
		Visit_Image(
			filter: $filter
			sort: [
				"visit_phone_registration.image_date"
				"image_taken_date"
				"original_uncropped_image.id"
				"-source"
			]
			limit: 100000
		) {
			id
			image_taken_date
			original_uncropped_image {
				id
				filename_disk
				width
				height
			}
			visit_phone_registration {
				id
				image_date
				phone {
					id
					icloud_account
				}
				phone_registration {
					end_date
					location
					photographer {
						id
					}
					start_date
					phone {
						id
						icloud_account
					}
				}
			}
			visit {
				id
			}
		}
	}
`;
