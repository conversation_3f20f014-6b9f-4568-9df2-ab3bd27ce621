import { gql } from 'graphql-request';

export const GET_VISIT_ARTWORK = gql`
	query getVisitArtwork(
		$filter: Visit_Artwork_filter
		$limit: Int
		$offset: Int
	) {
		Visit_Artwork(limit: $limit, filter: $filter, offset: $offset) {
			id
			images {
				directus_files_id {
					id
					storage
					filename_download
					width
					height
					filename_disk
				}
			}
			artwork_image {
				id
				image_taken_date
				crop_type
				photographer {
					id
				}
				rectangular_cropped_image {
					width
					height
					filename_disk
					filename_download
					storage
					id
				}
				perspective_cropped_image_without_dimensions {
					width
					height
					filename_disk
					filename_download
					storage
					id
				}
			}
			label_text
			label_image {
				id
				image_taken_date
				crop_type
				photographer {
					id
				}
				rectangular_cropped_image {
					id
					width
					height
					filename_disk
				}
				perspective_cropped_image_without_dimensions {
					id
					width
					height
					filename_disk
				}
			}
			status {
				key
			}
			is_installation_shot
			artwork_details {
				id
				ingestion_notes
				artists {
					id
					name
					nationality {
						code
						name
						country_nationality
					}
					year_birth
					year_death
					processed_artist_id
				}
			}
			processed_fair_exhibitor_org {
				entity
				id
				location
				name
				type
			}
			status {
				key
			}
			created_artwork_id
			processed_activity_id
		}
		Visit_Artwork_aggregated(filter: $filter) {
			countDistinct {
				id
			}
		}
	}
`;
