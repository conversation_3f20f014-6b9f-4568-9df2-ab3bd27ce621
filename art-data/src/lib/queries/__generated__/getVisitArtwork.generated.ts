import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetVisitArtworkQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Visit_Artwork_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetVisitArtworkQuery = {
	__typename?: 'Query';
	Visit_Artwork: Array<{
		__typename?: 'Visit_Artwork';
		id: string;
		label_text?: string | null;
		is_installation_shot: boolean;
		created_artwork_id?: string | null;
		processed_activity_id?: string | null;
		images?: Array<{
			__typename?: 'Visit_Artwork_files';
			directus_files_id?: {
				__typename?: 'directus_files';
				id: string;
				storage: string;
				filename_download: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
			} | null;
		} | null> | null;
		artwork_image?: {
			__typename?: 'Visit_Image';
			id: string;
			image_taken_date?: any | null;
			crop_type?: string | null;
			photographer?: {
				__typename?: 'directus_users';
				id?: string | null;
			} | null;
			rectangular_cropped_image?: {
				__typename?: 'directus_files';
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
				storage: string;
				id: string;
			} | null;
			perspective_cropped_image_without_dimensions?: {
				__typename?: 'directus_files';
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
				storage: string;
				id: string;
			} | null;
		} | null;
		label_image?: {
			__typename?: 'Visit_Image';
			id: string;
			image_taken_date?: any | null;
			crop_type?: string | null;
			photographer?: {
				__typename?: 'directus_users';
				id?: string | null;
			} | null;
			rectangular_cropped_image?: {
				__typename?: 'directus_files';
				id: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
			} | null;
			perspective_cropped_image_without_dimensions?: {
				__typename?: 'directus_files';
				id: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
			} | null;
		} | null;
		status?: { __typename?: 'Artwork_Status_Type'; key: string } | null;
		artwork_details?: {
			__typename?: 'Artwork_Details';
			id: string;
			ingestion_notes?: string | null;
			artists?: Array<{
				__typename?: 'Artist_Details';
				id: string;
				name?: string | null;
				year_birth?: number | null;
				year_death?: number | null;
				processed_artist_id?: string | null;
				nationality?: {
					__typename?: 'country';
					code: string;
					name: string;
					country_nationality?: string | null;
				} | null;
			} | null> | null;
		} | null;
		processed_fair_exhibitor_org?: {
			__typename?: 'Processed_Organisation';
			entity?: string | null;
			id: string;
			location?: string | null;
			name: string;
			type?: string | null;
		} | null;
	}>;
	Visit_Artwork_aggregated: Array<{
		__typename?: 'Visit_Artwork_aggregated';
		countDistinct?: {
			__typename?: 'Visit_Artwork_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetVisitArtworkDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getVisitArtwork' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Artwork_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_taken_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'crop_type' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'photographer' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'rectangular_cropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_without_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'label_text' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'label_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_taken_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'crop_type' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'photographer' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'rectangular_cropped_image',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'perspective_cropped_image_without_dimensions',
												},
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'is_installation_shot' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_details' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'ingestion_notes' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationality' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'processed_artist_id',
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_exhibitor_org' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'created_artwork_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_activity_id' },
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Artwork_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetVisitArtworkQuery,
	GetVisitArtworkQueryVariables
>;
