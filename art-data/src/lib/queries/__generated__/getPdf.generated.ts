import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetPdfQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Pdf_Filter>;
}>;

export type GetPdfQuery = {
	__typename?: 'Query';
	PDF: Array<{
		__typename?: 'PDF';
		id: string;
		title: string;
		pdf_artwork_format_method?: string | null;
		ai_extracted_text?: string | null;
		submitted_for_review?: boolean | null;
		receipt_info?: {
			__typename?: 'Receipt_Information';
			receive_date?: any | null;
		} | null;
		processed_fair_exhibitor_org?: {
			__typename?: 'Processed_Organisation';
			entity?: string | null;
			id: string;
			location?: string | null;
			name: string;
			type?: string | null;
		} | null;
		pdf_file?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
		} | null;
		processed_artist?: {
			__typename?: 'Processed_Artist';
			first_name?: string | null;
			last_name?: string | null;
			year_birth?: number | null;
			year_death?: number | null;
			processed_artist_id: string;
			nationality?: string | null;
		} | null;
		artworks?: Array<{
			__typename?: 'PDF_Artwork';
			id: string;
			images?: Array<{
				__typename?: 'PDF_Artwork_files';
				directus_files_id?: {
					__typename?: 'directus_files';
					id: string;
					filename_disk?: string | null;
					filename_download: string;
					storage: string;
					width?: number | null;
					height?: number | null;
				} | null;
			} | null> | null;
			artwork_details?: {
				__typename?: 'Artwork_Details';
				description?: string | null;
				id: string;
				sale_status?: string | null;
			} | null;
			status?: {
				__typename?: 'Artwork_Status_Type';
				key: string;
				name: string;
			} | null;
		} | null> | null;
		discarded_images?: Array<{
			__typename?: 'PDF_Discard_Image';
			id: string;
			timestamp?: any | null;
			image?: {
				__typename?: 'directus_files';
				filename_disk?: string | null;
				width?: number | null;
				height?: number | null;
				filename_download: string;
				storage: string;
				id: string;
			} | null;
		} | null> | null;
		pages?: Array<{
			__typename?: 'PDF_Page';
			id: string;
			page_number?: number | null;
			status?: string | null;
			text?: string | null;
			extracted_images?: Array<{
				__typename?: 'PDF_Image';
				image?: {
					__typename?: 'directus_files';
					filename_disk?: string | null;
					width?: number | null;
					height?: number | null;
					filename_download: string;
					storage: string;
					id: string;
				} | null;
			} | null> | null;
		} | null> | null;
	}>;
};

export const GetPdfDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getPdf' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'PDF_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'PDF' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'receipt_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'receive_date' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_exhibitor_org' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'pdf_file' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'pdf_artwork_format_method' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_artist' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_artist_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nationality' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'directus_files_id',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_details' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_status' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'discarded_images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ai_extracted_text' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'pages' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'page_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'text' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'extracted_images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'submitted_for_review' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetPdfQuery, GetPdfQueryVariables>;
