import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetCountriesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Country_Filter>;
}>;

export type GetCountriesQuery = {
	__typename?: 'Query';
	country: Array<{
		__typename?: 'country';
		nationality?: string | null;
		code: string;
		country_nationality?: string | null;
		name: string;
	}>;
};

export const GetCountriesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getCountries' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'country_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'country' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '-1' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'nationality' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'country_nationality' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetCountriesQuery, GetCountriesQueryVariables>;
