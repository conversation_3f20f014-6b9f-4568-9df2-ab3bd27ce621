import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateVisitImagesMutationVariables = Types.Exact<{
	ids: Array<Types.Scalars['ID']['input']> | Types.Scalars['ID']['input'];
	data: Types.Update_Visit_Image_Input;
}>;

export type UpdateVisitImagesMutation = {
	__typename?: 'Mutation';
	update_Visit_Image_items: Array<{ __typename?: 'Visit_Image'; id: string }>;
};

export const UpdateVisitImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateVisitImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: {
								kind: 'NonNullType',
								type: {
									kind: 'NamedType',
									name: { kind: 'Name', value: 'ID' },
								},
							},
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_Visit_Image_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_Visit_Image_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateVisitImagesMutation,
	UpdateVisitImagesMutationVariables
>;
