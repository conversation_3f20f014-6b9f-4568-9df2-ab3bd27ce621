import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetPhonesQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetPhonesQuery = {
	__typename?: 'Query';
	Visit_Phone: Array<{
		__typename?: 'Visit_Phone';
		id: string;
		icloud_account: string;
		authentication_status?: {
			__typename?: 'Visit_Phone_Status';
			key: string;
		} | null;
	}>;
};

export const GetPhonesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getPhones' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Phone' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'icloud_account' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'authentication_status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetPhonesQuery, GetPhonesQueryVariables>;
