import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetIcloudVisitImagesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Visit_Image_Filter>;
}>;

export type GetIcloudVisitImagesQuery = {
	__typename?: 'Query';
	Visit_Image: Array<{
		__typename?: 'Visit_Image';
		id: string;
		image_taken_date?: any | null;
		original_uncropped_image?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		visit_phone_registration?: Array<{
			__typename?: 'Visit_Phone_Registration_Visit_Image';
			id: string;
			image_date: any;
			phone?: {
				__typename?: 'Visit_Phone';
				id: string;
				icloud_account: string;
			} | null;
			phone_registration?: {
				__typename?: 'Visit_Phone_Registration';
				end_date: any;
				location: string;
				start_date: any;
				photographer?: {
					__typename?: 'directus_users';
					id?: string | null;
				} | null;
				phone?: {
					__typename?: 'Visit_Phone';
					id: string;
					icloud_account: string;
				} | null;
			} | null;
		} | null> | null;
		visit?: { __typename?: 'Visit'; id: string } | null;
	}>;
};

export const GetIcloudVisitImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getIcloudVisitImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Image_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Image' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'ListValue',
									values: [
										{
											kind: 'StringValue',
											value: 'visit_phone_registration.image_date',
											block: false,
										},
										{
											kind: 'StringValue',
											value: 'image_taken_date',
											block: false,
										},
										{
											kind: 'StringValue',
											value: 'original_uncropped_image.id',
											block: false,
										},
										{ kind: 'StringValue', value: '-source', block: false },
									],
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '100000' },
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'image_taken_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'original_uncropped_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_phone_registration' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'phone' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'icloud_account' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'phone_registration' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'end_date' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'photographer' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'start_date' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'phone' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'icloud_account',
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetIcloudVisitImagesQuery,
	GetIcloudVisitImagesQueryVariables
>;
