import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateScrapedEntityMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Scraped_Entity_Input;
}>;

export type UpdateScrapedEntityMutation = {
	__typename?: 'Mutation';
	update_Scraped_Entity_item?: {
		__typename?: 'Scraped_Entity';
		id: string;
		processed_entity_id?: string | null;
		processed_reference_id?: string | null;
		associated_entity_feed?: {
			__typename?: 'associated_entity_feed';
			name?: string | null;
		} | null;
	} | null;
};

export const UpdateScrapedEntityDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateScrapedEntity' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_Scraped_Entity_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_Scraped_Entity_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associated_entity_feed' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_entity_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_reference_id' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateScrapedEntityMutation,
	UpdateScrapedEntityMutationVariables
>;
