import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetVisitImageQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Visit_Image_Filter>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetVisitImageQuery = {
	__typename?: 'Query';
	Visit_Image: Array<{
		__typename?: 'Visit_Image';
		id: string;
		image_taken_date?: any | null;
		source?: string | null;
		crop_type?: string | null;
		data_admin_submitted_coordinates?: string | null;
		extracted_best_guess_coordinates_from_api?: any | null;
		visit?: { __typename?: 'Visit'; id: string } | null;
		visit_phone_registration?: Array<{
			__typename?: 'Visit_Phone_Registration_Visit_Image';
			id: string;
			image_date: any;
			phone?: {
				__typename?: 'Visit_Phone';
				id: string;
				icloud_account: string;
			} | null;
			phone_registration?: {
				__typename?: 'Visit_Phone_Registration';
				end_date: any;
				location: string;
				start_date: any;
				photographer?: {
					__typename?: 'directus_users';
					id?: string | null;
				} | null;
				phone?: {
					__typename?: 'Visit_Phone';
					id: string;
					icloud_account: string;
				} | null;
			} | null;
		} | null> | null;
		original_uncropped_image?: {
			__typename?: 'directus_files';
			id: string;
			storage: string;
			filename_disk?: string | null;
			filename_download: string;
			width?: number | null;
			height?: number | null;
		} | null;
		perspective_cropped_image_with_dimensions?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		perspective_cropped_image_without_dimensions?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		rectangular_cropped_image?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		photographer?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
			id?: string | null;
		} | null;
	}>;
};

export const GetVisitImageDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getVisitImage' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Image_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Image' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'image_taken_date' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'source' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_phone_registration' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'phone' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'icloud_account' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'phone_registration' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'end_date' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'photographer' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'start_date' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'phone' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'icloud_account',
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'original_uncropped_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'perspective_cropped_image_with_dimensions',
									},
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'perspective_cropped_image_without_dimensions',
									},
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'rectangular_cropped_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'photographer' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crop_type' } },
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'data_admin_submitted_coordinates',
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'extracted_best_guess_coordinates_from_api',
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetVisitImageQuery, GetVisitImageQueryVariables>;
