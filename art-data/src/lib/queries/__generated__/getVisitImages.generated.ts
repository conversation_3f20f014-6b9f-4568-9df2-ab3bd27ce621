import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetVisitImagesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Visit_Image_Filter>;
	photographerFilter?: Types.InputMaybe<Types.Visit_Image_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetVisitImagesQuery = {
	__typename?: 'Query';
	Visit_Image: Array<{
		__typename?: 'Visit_Image';
		id: string;
		image_taken_date?: any | null;
		crop_type?: string | null;
		original_uncropped_image?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		visit_phone_registration?: Array<{
			__typename?: 'Visit_Phone_Registration_Visit_Image';
			id: string;
			image_date: any;
		} | null> | null;
		photographer?: { __typename?: 'directus_users'; id?: string | null } | null;
		perspective_cropped_image_with_dimensions?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		perspective_cropped_image_without_dimensions?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		rectangular_cropped_image?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			width?: number | null;
			height?: number | null;
		} | null;
		visit_artwork?: { __typename?: 'Visit_Artwork'; id: string } | null;
	}>;
	Visit_Image_aggregated: Array<{
		__typename?: 'Visit_Image_aggregated';
		countDistinct?: {
			__typename?: 'Visit_Image_aggregated_count';
			id?: number | null;
		} | null;
	}>;
	photographers: Array<{
		__typename?: 'Visit_Image_aggregated';
		group?: any | null;
	}>;
};

export const GetVisitImagesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getVisitImages' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Image_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'photographerFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Visit_Image_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Image' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'image_taken_date' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crop_type' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'original_uncropped_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_phone_registration' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image_date' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'photographer' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'perspective_cropped_image_with_dimensions',
									},
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: {
										kind: 'Name',
										value: 'perspective_cropped_image_without_dimensions',
									},
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'rectangular_cropped_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'visit_artwork' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Visit_Image_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						alias: { kind: 'Name', value: 'photographers' },
						name: { kind: 'Name', value: 'Visit_Image_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'photographerFilter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'groupBy' },
								value: {
									kind: 'StringValue',
									value: 'photographer',
									block: false,
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'group' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetVisitImagesQuery, GetVisitImagesQueryVariables>;
