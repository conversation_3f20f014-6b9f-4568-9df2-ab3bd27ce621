import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtworkDetailsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Details_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetArtworkDetailsQuery = {
	__typename?: 'Query';
	Artwork_Details: Array<{
		__typename?: 'Artwork_Details';
		id: string;
		description?: string | null;
		crid?: string | null;
		processed_artwork_id?: string | null;
		title?: string | null;
		executed_year_end?: number | null;
		executed_year_start?: number | null;
		media?: string | null;
		dimensions?: string | null;
		dimensions_width_cm?: number | null;
		dimensions_depth_cm?: number | null;
		dimensions_height_cm?: number | null;
		number_of_pieces?: number | null;
		number_of_artworks?: number | null;
		edition_description?: string | null;
		regular_edition_size?: number | null;
		artist_proof_size?: number | null;
		house_of_commerce_size?: number | null;
		total_edition_size?: number | null;
		series_size?: number | null;
		open_edition?: boolean | null;
		edition_number?: string | null;
		price?: number | null;
		artwork_removed?: boolean | null;
		sale_status?: string | null;
		artists?: Array<{
			__typename?: 'Artist_Details';
			id: string;
			name?: string | null;
			year_birth?: number | null;
			year_death?: number | null;
			processed_artist_id?: string | null;
			nationality?: {
				__typename?: 'country';
				code: string;
				name: string;
			} | null;
		} | null> | null;
		artwork_type?: {
			__typename?: 'Artwork_Type';
			key: string;
			name: string;
		} | null;
		dimension_type?: {
			__typename?: 'Artwork_Dimension_Type';
			key: string;
			name?: string | null;
		} | null;
		edition_number_type?: {
			__typename?: 'Edition_Number_Type';
			key: string;
			name?: string | null;
		} | null;
		currency?: { __typename?: 'currency'; code: string } | null;
	}>;
	Artwork_Details_aggregated: Array<{
		__typename?: 'Artwork_Details_aggregated';
		countDistinct?: {
			__typename?: 'Artwork_Details_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetArtworkDetailsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkDetails' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Artwork_Details_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Artwork_Details' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nationality' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processed_artist_id' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_artwork_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'executed_year_end' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'executed_year_start' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'media' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'dimensions' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimension_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_width_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_depth_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_height_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_pieces' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_description' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'regular_edition_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist_proof_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'house_of_commerce_size' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'total_edition_size' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'series_size' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'open_edition' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_number_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'price' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_removed' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_status' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Artwork_Details_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkDetailsQuery,
	GetArtworkDetailsQueryVariables
>;
