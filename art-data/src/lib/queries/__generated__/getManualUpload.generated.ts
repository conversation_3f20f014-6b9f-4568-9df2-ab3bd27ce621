import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetManualUploadQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Manual_Upload_Filter>;
}>;

export type GetManualUploadQuery = {
	__typename?: 'Query';
	Manual_Upload: Array<{
		__typename?: 'Manual_Upload';
		id: string;
		submitted_for_review?: boolean | null;
		reference_files?: Array<{
			__typename?: 'Manual_Upload_files';
			directus_files_id?: {
				__typename?: 'directus_files';
				id: string;
				title?: string | null;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
				storage: string;
			} | null;
		} | null> | null;
		receipt_info?: {
			__typename?: 'Receipt_Information';
			receive_date?: any | null;
		} | null;
		processed_fair_exhibitor_org?: {
			__typename?: 'Processed_Organisation';
			entity?: string | null;
			id: string;
			location?: string | null;
			name: string;
			type?: string | null;
		} | null;
		manually_added_artworks?: Array<{
			__typename?: 'Manually_Added_Artwork';
			id: string;
			images?: Array<{
				__typename?: 'Manually_Added_Artwork_files';
				directus_files_id?: {
					__typename?: 'directus_files';
					storage: string;
					id: string;
					filename_download: string;
					filename_disk?: string | null;
					height?: number | null;
					width?: number | null;
				} | null;
			} | null> | null;
			artwork_details?: {
				__typename?: 'Artwork_Details';
				id: string;
				description?: string | null;
				sale_status?: string | null;
			} | null;
		} | null> | null;
	}>;
};

export const GetManualUploadDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getManualUpload' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'Manual_Upload_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'Manual_Upload' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'reference_files' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'receipt_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'receive_date' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'processed_fair_exhibitor_org' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'submitted_for_review' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'manually_added_artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'directus_files_id',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_details' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_status' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetManualUploadQuery,
	GetManualUploadQueryVariables
>;
