import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreatePhoneRegistrationMutationVariables = Types.Exact<{
	data: Types.Create_Visit_Phone_Registration_Input;
}>;

export type CreatePhoneRegistrationMutation = {
	__typename?: 'Mutation';
	create_Visit_Phone_Registration_item?: {
		__typename?: 'Visit_Phone_Registration';
		id: string;
	} | null;
};

export const CreatePhoneRegistrationDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createPhoneRegistration' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: {
								kind: 'Name',
								value: 'create_Visit_Phone_Registration_input',
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'create_Visit_Phone_Registration_item',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreatePhoneRegistrationMutation,
	CreatePhoneRegistrationMutationVariables
>;
