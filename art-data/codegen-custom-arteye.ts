/* eslint-disable consistent-default-export-name/default-export-match-filename */
import type { CodegenConfig } from '@graphql-codegen/cli';
import { BasicDevConfig } from './src/lib/constants/basic-dev-config';

const codegenCustomArteye: CodegenConfig = {
	hooks: { afterAllFileWrite: ['prettier --write', 'eslint --fix'] },
	schema: [
		{
			[BasicDevConfig.CustomArteyeGraphqlApiUrl]: {
				headers: {
					authorization: `Bearer ${BasicDevConfig.ArteyeGraphqlApiKey}`,
				},
			},
		},
	],
	documents: 'src/**/custom-arteye-queries/**/!(*.generated).{ts,tsx}',
	generates: {
		'./src/gql/types-arteye-custom.ts': {
			plugins: ['typescript'],
		},
		'./src/': {
			preset: 'near-operation-file',
			presetConfig: {
				extension: '.generated.ts',
				baseTypesPath: './gql/types-arteye-custom.ts',
				folder: '__generated__',
			},
			plugins: ['typescript-operations', 'typed-document-node'],
		},
	},
};
export default codegenCustomArteye;
