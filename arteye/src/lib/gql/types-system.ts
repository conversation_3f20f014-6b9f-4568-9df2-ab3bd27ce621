export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	GraphQLBigInt: { input: any; output: any };
	GraphQLStringOrFloat: { input: any; output: any };
	Hash: { input: any; output: any };
	JSON: { input: any; output: any };
	Void: { input: any; output: any };
};

export enum EventEnum {
	Create = 'create',
	Delete = 'delete',
	Update = 'update',
}

export type Mutation = {
	__typename?: 'Mutation';
	auth_login?: Maybe<Auth_Tokens>;
	auth_logout?: Maybe<Scalars['Boolean']['output']>;
	auth_password_request?: Maybe<Scalars['Boolean']['output']>;
	auth_password_reset?: Maybe<Scalars['Boolean']['output']>;
	auth_refresh?: Maybe<Auth_Tokens>;
	create_collections_item?: Maybe<Directus_Collections>;
	create_comment?: Maybe<Directus_Activity>;
	create_dashboards_item?: Maybe<Directus_Dashboards>;
	create_dashboards_items: Array<Directus_Dashboards>;
	create_fields_item?: Maybe<Directus_Fields>;
	create_files_item?: Maybe<Directus_Files>;
	create_files_items: Array<Directus_Files>;
	create_flows_item?: Maybe<Directus_Flows>;
	create_flows_items: Array<Directus_Flows>;
	create_folders_item?: Maybe<Directus_Folders>;
	create_folders_items: Array<Directus_Folders>;
	create_notifications_item?: Maybe<Directus_Notifications>;
	create_notifications_items: Array<Directus_Notifications>;
	create_operations_item?: Maybe<Directus_Operations>;
	create_operations_items: Array<Directus_Operations>;
	create_panels_item?: Maybe<Directus_Panels>;
	create_panels_items: Array<Directus_Panels>;
	create_permissions_item?: Maybe<Directus_Permissions>;
	create_permissions_items: Array<Directus_Permissions>;
	create_presets_item?: Maybe<Directus_Presets>;
	create_presets_items: Array<Directus_Presets>;
	create_relations_item?: Maybe<Directus_Relations>;
	create_roles_item?: Maybe<Directus_Roles>;
	create_roles_items: Array<Directus_Roles>;
	create_shares_item?: Maybe<Directus_Shares>;
	create_shares_items: Array<Directus_Shares>;
	create_translations_item?: Maybe<Directus_Translations>;
	create_translations_items: Array<Directus_Translations>;
	create_users_item?: Maybe<Directus_Users>;
	create_users_items: Array<Directus_Users>;
	create_versions_item?: Maybe<Directus_Versions>;
	create_versions_items: Array<Directus_Versions>;
	create_webhooks_item?: Maybe<Directus_Webhooks>;
	create_webhooks_items: Array<Directus_Webhooks>;
	delete_collections_item?: Maybe<Delete_Collection>;
	delete_comment?: Maybe<Delete_One>;
	delete_dashboards_item?: Maybe<Delete_One>;
	delete_dashboards_items?: Maybe<Delete_Many>;
	delete_fields_item?: Maybe<Delete_Field>;
	delete_files_item?: Maybe<Delete_One>;
	delete_files_items?: Maybe<Delete_Many>;
	delete_flows_item?: Maybe<Delete_One>;
	delete_flows_items?: Maybe<Delete_Many>;
	delete_folders_item?: Maybe<Delete_One>;
	delete_folders_items?: Maybe<Delete_Many>;
	delete_notifications_item?: Maybe<Delete_One>;
	delete_notifications_items?: Maybe<Delete_Many>;
	delete_operations_item?: Maybe<Delete_One>;
	delete_operations_items?: Maybe<Delete_Many>;
	delete_panels_item?: Maybe<Delete_One>;
	delete_panels_items?: Maybe<Delete_Many>;
	delete_permissions_item?: Maybe<Delete_One>;
	delete_permissions_items?: Maybe<Delete_Many>;
	delete_presets_item?: Maybe<Delete_One>;
	delete_presets_items?: Maybe<Delete_Many>;
	delete_relations_item?: Maybe<Delete_Relation>;
	delete_roles_item?: Maybe<Delete_One>;
	delete_roles_items?: Maybe<Delete_Many>;
	delete_shares_item?: Maybe<Delete_One>;
	delete_shares_items?: Maybe<Delete_Many>;
	delete_translations_item?: Maybe<Delete_One>;
	delete_translations_items?: Maybe<Delete_Many>;
	delete_users_item?: Maybe<Delete_One>;
	delete_users_items?: Maybe<Delete_Many>;
	delete_versions_item?: Maybe<Delete_One>;
	delete_versions_items?: Maybe<Delete_Many>;
	delete_webhooks_item?: Maybe<Delete_One>;
	delete_webhooks_items?: Maybe<Delete_Many>;
	import_file?: Maybe<Directus_Files>;
	update_collections_item?: Maybe<Directus_Collections>;
	update_comment?: Maybe<Directus_Activity>;
	update_dashboards_batch: Array<Directus_Dashboards>;
	update_dashboards_item?: Maybe<Directus_Dashboards>;
	update_dashboards_items: Array<Directus_Dashboards>;
	update_extensions_item?: Maybe<Directus_Extensions>;
	update_fields_item?: Maybe<Directus_Fields>;
	update_files_batch: Array<Directus_Files>;
	update_files_item?: Maybe<Directus_Files>;
	update_files_items: Array<Directus_Files>;
	update_flows_batch: Array<Directus_Flows>;
	update_flows_item?: Maybe<Directus_Flows>;
	update_flows_items: Array<Directus_Flows>;
	update_folders_batch: Array<Directus_Folders>;
	update_folders_item?: Maybe<Directus_Folders>;
	update_folders_items: Array<Directus_Folders>;
	update_notifications_batch: Array<Directus_Notifications>;
	update_notifications_item?: Maybe<Directus_Notifications>;
	update_notifications_items: Array<Directus_Notifications>;
	update_operations_batch: Array<Directus_Operations>;
	update_operations_item?: Maybe<Directus_Operations>;
	update_operations_items: Array<Directus_Operations>;
	update_panels_batch: Array<Directus_Panels>;
	update_panels_item?: Maybe<Directus_Panels>;
	update_panels_items: Array<Directus_Panels>;
	update_permissions_batch: Array<Directus_Permissions>;
	update_permissions_item?: Maybe<Directus_Permissions>;
	update_permissions_items: Array<Directus_Permissions>;
	update_presets_batch: Array<Directus_Presets>;
	update_presets_item?: Maybe<Directus_Presets>;
	update_presets_items: Array<Directus_Presets>;
	update_relations_item?: Maybe<Directus_Relations>;
	update_roles_batch: Array<Directus_Roles>;
	update_roles_item?: Maybe<Directus_Roles>;
	update_roles_items: Array<Directus_Roles>;
	update_settings?: Maybe<Directus_Settings>;
	update_shares_batch: Array<Directus_Shares>;
	update_shares_item?: Maybe<Directus_Shares>;
	update_shares_items: Array<Directus_Shares>;
	update_translations_batch: Array<Directus_Translations>;
	update_translations_item?: Maybe<Directus_Translations>;
	update_translations_items: Array<Directus_Translations>;
	update_users_batch: Array<Directus_Users>;
	update_users_item?: Maybe<Directus_Users>;
	update_users_items: Array<Directus_Users>;
	update_users_me?: Maybe<Directus_Users>;
	update_versions_batch: Array<Directus_Versions>;
	update_versions_item?: Maybe<Directus_Versions>;
	update_versions_items: Array<Directus_Versions>;
	update_webhooks_batch: Array<Directus_Webhooks>;
	update_webhooks_item?: Maybe<Directus_Webhooks>;
	update_webhooks_items: Array<Directus_Webhooks>;
	users_invite?: Maybe<Scalars['Boolean']['output']>;
	users_invite_accept?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_disable?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_enable?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_generate?: Maybe<Users_Me_Tfa_Generate_Data>;
	users_register?: Maybe<Scalars['Boolean']['output']>;
	users_register_verify?: Maybe<Scalars['Boolean']['output']>;
	utils_cache_clear?: Maybe<Scalars['Void']['output']>;
	utils_hash_generate?: Maybe<Scalars['String']['output']>;
	utils_hash_verify?: Maybe<Scalars['Boolean']['output']>;
	utils_random_string?: Maybe<Scalars['String']['output']>;
	utils_revert?: Maybe<Scalars['Boolean']['output']>;
	utils_sort?: Maybe<Scalars['Boolean']['output']>;
};

export type MutationAuth_LoginArgs = {
	email: Scalars['String']['input'];
	mode?: InputMaybe<Auth_Mode>;
	otp?: InputMaybe<Scalars['String']['input']>;
	password: Scalars['String']['input'];
};

export type MutationAuth_LogoutArgs = {
	mode?: InputMaybe<Auth_Mode>;
	refresh_token?: InputMaybe<Scalars['String']['input']>;
};

export type MutationAuth_Password_RequestArgs = {
	email: Scalars['String']['input'];
	reset_url?: InputMaybe<Scalars['String']['input']>;
};

export type MutationAuth_Password_ResetArgs = {
	password: Scalars['String']['input'];
	token: Scalars['String']['input'];
};

export type MutationAuth_RefreshArgs = {
	mode?: InputMaybe<Auth_Mode>;
	refresh_token?: InputMaybe<Scalars['String']['input']>;
};

export type MutationCreate_Collections_ItemArgs = {
	data: Create_Directus_Collections_Input;
};

export type MutationCreate_CommentArgs = {
	collection: Scalars['String']['input'];
	comment: Scalars['String']['input'];
	item: Scalars['ID']['input'];
};

export type MutationCreate_Dashboards_ItemArgs = {
	data: Create_Directus_Dashboards_Input;
};

export type MutationCreate_Dashboards_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Dashboards_Input>>;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Fields_ItemArgs = {
	collection: Scalars['String']['input'];
	data: Create_Directus_Fields_Input;
};

export type MutationCreate_Files_ItemArgs = {
	data: Create_Directus_Files_Input;
};

export type MutationCreate_Files_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Files_Input>>;
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Flows_ItemArgs = {
	data: Create_Directus_Flows_Input;
};

export type MutationCreate_Flows_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Flows_Input>>;
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Folders_ItemArgs = {
	data: Create_Directus_Folders_Input;
};

export type MutationCreate_Folders_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Folders_Input>>;
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Notifications_ItemArgs = {
	data: Create_Directus_Notifications_Input;
};

export type MutationCreate_Notifications_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Notifications_Input>>;
	filter?: InputMaybe<Directus_Notifications_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Operations_ItemArgs = {
	data: Create_Directus_Operations_Input;
};

export type MutationCreate_Operations_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Operations_Input>>;
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Panels_ItemArgs = {
	data: Create_Directus_Panels_Input;
};

export type MutationCreate_Panels_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Panels_Input>>;
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Permissions_ItemArgs = {
	data: Create_Directus_Permissions_Input;
};

export type MutationCreate_Permissions_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Permissions_Input>>;
	filter?: InputMaybe<Directus_Permissions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Presets_ItemArgs = {
	data: Create_Directus_Presets_Input;
};

export type MutationCreate_Presets_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Presets_Input>>;
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Relations_ItemArgs = {
	data: Create_Directus_Relations_Input;
};

export type MutationCreate_Roles_ItemArgs = {
	data: Create_Directus_Roles_Input;
};

export type MutationCreate_Roles_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Roles_Input>>;
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Shares_ItemArgs = {
	data: Create_Directus_Shares_Input;
};

export type MutationCreate_Shares_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Shares_Input>>;
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Translations_ItemArgs = {
	data: Create_Directus_Translations_Input;
};

export type MutationCreate_Translations_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Translations_Input>>;
	filter?: InputMaybe<Directus_Translations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Users_ItemArgs = {
	data: Create_Directus_Users_Input;
};

export type MutationCreate_Users_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Users_Input>>;
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Versions_ItemArgs = {
	data: Create_Directus_Versions_Input;
};

export type MutationCreate_Versions_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Versions_Input>>;
	filter?: InputMaybe<Directus_Versions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Webhooks_ItemArgs = {
	data: Create_Directus_Webhooks_Input;
};

export type MutationCreate_Webhooks_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Webhooks_Input>>;
	filter?: InputMaybe<Directus_Webhooks_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationDelete_Collections_ItemArgs = {
	collection: Scalars['String']['input'];
};

export type MutationDelete_CommentArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Dashboards_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Dashboards_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Fields_ItemArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type MutationDelete_Files_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Files_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Flows_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Flows_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Folders_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Folders_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Notifications_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Notifications_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Operations_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Operations_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Panels_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Panels_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Permissions_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Permissions_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Presets_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Presets_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Relations_ItemArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type MutationDelete_Roles_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Roles_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Shares_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Shares_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Translations_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Translations_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Users_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Users_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Versions_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Versions_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Webhooks_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Webhooks_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationImport_FileArgs = {
	data?: InputMaybe<Create_Directus_Files_Input>;
	url: Scalars['String']['input'];
};

export type MutationUpdate_Collections_ItemArgs = {
	collection: Scalars['String']['input'];
	data: Update_Directus_Collections_Input;
};

export type MutationUpdate_CommentArgs = {
	comment: Scalars['String']['input'];
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Dashboards_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Dashboards_Input>>;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Dashboards_ItemArgs = {
	data: Update_Directus_Dashboards_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Dashboards_ItemsArgs = {
	data: Update_Directus_Dashboards_Input;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Extensions_ItemArgs = {
	data?: InputMaybe<Update_Directus_Extensions_InputInput>;
	id?: InputMaybe<Scalars['ID']['input']>;
};

export type MutationUpdate_Fields_ItemArgs = {
	collection: Scalars['String']['input'];
	data: Update_Directus_Fields_Input;
	field: Scalars['String']['input'];
};

export type MutationUpdate_Files_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Files_Input>>;
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Files_ItemArgs = {
	data: Update_Directus_Files_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Files_ItemsArgs = {
	data: Update_Directus_Files_Input;
	filter?: InputMaybe<Directus_Files_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Flows_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Flows_Input>>;
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Flows_ItemArgs = {
	data: Update_Directus_Flows_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Flows_ItemsArgs = {
	data: Update_Directus_Flows_Input;
	filter?: InputMaybe<Directus_Flows_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Folders_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Folders_Input>>;
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Folders_ItemArgs = {
	data: Update_Directus_Folders_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Folders_ItemsArgs = {
	data: Update_Directus_Folders_Input;
	filter?: InputMaybe<Directus_Folders_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Notifications_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Notifications_Input>>;
	filter?: InputMaybe<Directus_Notifications_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Notifications_ItemArgs = {
	data: Update_Directus_Notifications_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Notifications_ItemsArgs = {
	data: Update_Directus_Notifications_Input;
	filter?: InputMaybe<Directus_Notifications_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Operations_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Operations_Input>>;
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Operations_ItemArgs = {
	data: Update_Directus_Operations_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Operations_ItemsArgs = {
	data: Update_Directus_Operations_Input;
	filter?: InputMaybe<Directus_Operations_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Panels_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Panels_Input>>;
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Panels_ItemArgs = {
	data: Update_Directus_Panels_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Panels_ItemsArgs = {
	data: Update_Directus_Panels_Input;
	filter?: InputMaybe<Directus_Panels_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Permissions_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Permissions_Input>>;
	filter?: InputMaybe<Directus_Permissions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Permissions_ItemArgs = {
	data: Update_Directus_Permissions_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Permissions_ItemsArgs = {
	data: Update_Directus_Permissions_Input;
	filter?: InputMaybe<Directus_Permissions_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Presets_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Presets_Input>>;
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Presets_ItemArgs = {
	data: Update_Directus_Presets_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Presets_ItemsArgs = {
	data: Update_Directus_Presets_Input;
	filter?: InputMaybe<Directus_Presets_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Relations_ItemArgs = {
	collection: Scalars['String']['input'];
	data: Update_Directus_Relations_Input;
	field: Scalars['String']['input'];
};

export type MutationUpdate_Roles_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Roles_Input>>;
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Roles_ItemArgs = {
	data: Update_Directus_Roles_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Roles_ItemsArgs = {
	data: Update_Directus_Roles_Input;
	filter?: InputMaybe<Directus_Roles_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_SettingsArgs = {
	data: Update_Directus_Settings_Input;
};

export type MutationUpdate_Shares_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Shares_Input>>;
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Shares_ItemArgs = {
	data: Update_Directus_Shares_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Shares_ItemsArgs = {
	data: Update_Directus_Shares_Input;
	filter?: InputMaybe<Directus_Shares_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Translations_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Translations_Input>>;
	filter?: InputMaybe<Directus_Translations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Translations_ItemArgs = {
	data: Update_Directus_Translations_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Translations_ItemsArgs = {
	data: Update_Directus_Translations_Input;
	filter?: InputMaybe<Directus_Translations_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Users_Input>>;
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_ItemArgs = {
	data: Update_Directus_Users_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Users_ItemsArgs = {
	data: Update_Directus_Users_Input;
	filter?: InputMaybe<Directus_Users_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_MeArgs = {
	data?: InputMaybe<Update_Directus_Users_Input>;
};

export type MutationUpdate_Versions_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Versions_Input>>;
	filter?: InputMaybe<Directus_Versions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Versions_ItemArgs = {
	data: Update_Directus_Versions_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Versions_ItemsArgs = {
	data: Update_Directus_Versions_Input;
	filter?: InputMaybe<Directus_Versions_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Webhooks_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Webhooks_Input>>;
	filter?: InputMaybe<Directus_Webhooks_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Webhooks_ItemArgs = {
	data: Update_Directus_Webhooks_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Webhooks_ItemsArgs = {
	data: Update_Directus_Webhooks_Input;
	filter?: InputMaybe<Directus_Webhooks_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUsers_InviteArgs = {
	email: Scalars['String']['input'];
	invite_url?: InputMaybe<Scalars['String']['input']>;
	role: Scalars['String']['input'];
};

export type MutationUsers_Invite_AcceptArgs = {
	password: Scalars['String']['input'];
	token: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_DisableArgs = {
	otp: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_EnableArgs = {
	otp: Scalars['String']['input'];
	secret: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_GenerateArgs = {
	password: Scalars['String']['input'];
};

export type MutationUsers_RegisterArgs = {
	email: Scalars['String']['input'];
	first_name?: InputMaybe<Scalars['String']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	password: Scalars['String']['input'];
	verification_url?: InputMaybe<Scalars['String']['input']>;
};

export type MutationUsers_Register_VerifyArgs = {
	token: Scalars['String']['input'];
};

export type MutationUtils_Hash_GenerateArgs = {
	string: Scalars['String']['input'];
};

export type MutationUtils_Hash_VerifyArgs = {
	hash: Scalars['String']['input'];
	string: Scalars['String']['input'];
};

export type MutationUtils_Random_StringArgs = {
	length?: InputMaybe<Scalars['Int']['input']>;
};

export type MutationUtils_RevertArgs = {
	revision: Scalars['ID']['input'];
};

export type MutationUtils_SortArgs = {
	collection: Scalars['String']['input'];
	item: Scalars['ID']['input'];
	to: Scalars['ID']['input'];
};

export type Pdf = {
	__typename?: 'PDF';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	pdf_file?: Maybe<Directus_Files>;
	status?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type PdfPdf_FileArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Mutated = {
	__typename?: 'PDF_mutated';
	data?: Maybe<Pdf>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Query = {
	__typename?: 'Query';
	activity: Array<Directus_Activity>;
	activity_aggregated: Array<Directus_Activity_Aggregated>;
	activity_by_id?: Maybe<Directus_Activity>;
	collections: Array<Directus_Collections>;
	collections_by_name?: Maybe<Directus_Collections>;
	dashboards: Array<Directus_Dashboards>;
	dashboards_aggregated: Array<Directus_Dashboards_Aggregated>;
	dashboards_by_id?: Maybe<Directus_Dashboards>;
	extensions: Array<Directus_Extensions>;
	fields: Array<Directus_Fields>;
	fields_by_name?: Maybe<Directus_Fields>;
	fields_in_collection: Array<Directus_Fields>;
	files: Array<Directus_Files>;
	files_aggregated: Array<Directus_Files_Aggregated>;
	files_by_id?: Maybe<Directus_Files>;
	flows: Array<Directus_Flows>;
	flows_aggregated: Array<Directus_Flows_Aggregated>;
	flows_by_id?: Maybe<Directus_Flows>;
	folders: Array<Directus_Folders>;
	folders_aggregated: Array<Directus_Folders_Aggregated>;
	folders_by_id?: Maybe<Directus_Folders>;
	notifications: Array<Directus_Notifications>;
	notifications_aggregated: Array<Directus_Notifications_Aggregated>;
	notifications_by_id?: Maybe<Directus_Notifications>;
	operations: Array<Directus_Operations>;
	operations_aggregated: Array<Directus_Operations_Aggregated>;
	operations_by_id?: Maybe<Directus_Operations>;
	panels: Array<Directus_Panels>;
	panels_aggregated: Array<Directus_Panels_Aggregated>;
	panels_by_id?: Maybe<Directus_Panels>;
	permissions: Array<Directus_Permissions>;
	permissions_aggregated: Array<Directus_Permissions_Aggregated>;
	permissions_by_id?: Maybe<Directus_Permissions>;
	presets: Array<Directus_Presets>;
	presets_aggregated: Array<Directus_Presets_Aggregated>;
	presets_by_id?: Maybe<Directus_Presets>;
	relations: Array<Directus_Relations>;
	relations_by_name?: Maybe<Directus_Relations>;
	relations_in_collection: Array<Directus_Relations>;
	revisions: Array<Directus_Revisions>;
	revisions_aggregated: Array<Directus_Revisions_Aggregated>;
	revisions_by_id?: Maybe<Directus_Revisions>;
	roles: Array<Directus_Roles>;
	roles_aggregated: Array<Directus_Roles_Aggregated>;
	roles_by_id?: Maybe<Directus_Roles>;
	server_health?: Maybe<Scalars['JSON']['output']>;
	server_info?: Maybe<Server_Info>;
	server_ping?: Maybe<Scalars['String']['output']>;
	server_specs_graphql?: Maybe<Scalars['String']['output']>;
	server_specs_oas?: Maybe<Scalars['JSON']['output']>;
	settings?: Maybe<Directus_Settings>;
	shares: Array<Directus_Shares>;
	shares_aggregated: Array<Directus_Shares_Aggregated>;
	shares_by_id?: Maybe<Directus_Shares>;
	translations: Array<Directus_Translations>;
	translations_aggregated: Array<Directus_Translations_Aggregated>;
	translations_by_id?: Maybe<Directus_Translations>;
	users: Array<Directus_Users>;
	users_aggregated: Array<Directus_Users_Aggregated>;
	users_by_id?: Maybe<Directus_Users>;
	users_me?: Maybe<Directus_Users>;
	versions: Array<Directus_Versions>;
	versions_aggregated: Array<Directus_Versions_Aggregated>;
	versions_by_id?: Maybe<Directus_Versions>;
	webhooks: Array<Directus_Webhooks>;
	webhooks_aggregated: Array<Directus_Webhooks_Aggregated>;
	webhooks_by_id?: Maybe<Directus_Webhooks>;
};

export type QueryActivityArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryActivity_AggregatedArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryActivity_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryCollections_By_NameArgs = {
	name: Scalars['String']['input'];
};

export type QueryDashboardsArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryDashboards_AggregatedArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryDashboards_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFields_By_NameArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type QueryFields_In_CollectionArgs = {
	collection: Scalars['String']['input'];
};

export type QueryFilesArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFiles_AggregatedArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFiles_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFlowsArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFlows_AggregatedArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFlows_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFoldersArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFolders_AggregatedArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFolders_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryNotificationsArgs = {
	filter?: InputMaybe<Directus_Notifications_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryNotifications_AggregatedArgs = {
	filter?: InputMaybe<Directus_Notifications_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryNotifications_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryOperationsArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryOperations_AggregatedArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryOperations_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPanelsArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPanels_AggregatedArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPanels_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPermissionsArgs = {
	filter?: InputMaybe<Directus_Permissions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPermissions_AggregatedArgs = {
	filter?: InputMaybe<Directus_Permissions_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPermissions_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPresetsArgs = {
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPresets_AggregatedArgs = {
	filter?: InputMaybe<Directus_Presets_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPresets_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryRelations_By_NameArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type QueryRelations_In_CollectionArgs = {
	collection: Scalars['String']['input'];
};

export type QueryRevisionsArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRevisions_AggregatedArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRevisions_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryRolesArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRoles_AggregatedArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRoles_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryServer_Specs_GraphqlArgs = {
	scope?: InputMaybe<Graphql_Sdl_Scope>;
};

export type QuerySettingsArgs = {
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QuerySharesArgs = {
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryShares_AggregatedArgs = {
	filter?: InputMaybe<Directus_Shares_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryShares_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryTranslationsArgs = {
	filter?: InputMaybe<Directus_Translations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryTranslations_AggregatedArgs = {
	filter?: InputMaybe<Directus_Translations_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryTranslations_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryUsersArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryUsers_AggregatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryUsers_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryVersionsArgs = {
	filter?: InputMaybe<Directus_Versions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryVersions_AggregatedArgs = {
	filter?: InputMaybe<Directus_Versions_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryVersions_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryWebhooksArgs = {
	filter?: InputMaybe<Directus_Webhooks_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryWebhooks_AggregatedArgs = {
	filter?: InputMaybe<Directus_Webhooks_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryWebhooks_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type Subscription = {
	__typename?: 'Subscription';
	PDF_mutated?: Maybe<Pdf_Mutated>;
	artist_award_mutated?: Maybe<Artist_Award_Mutated>;
	artist_mutated?: Maybe<Artist_Mutated>;
	artist_raw_info_mutated?: Maybe<Artist_Raw_Info_Mutated>;
	artist_raw_info_type_mutated?: Maybe<Artist_Raw_Info_Type_Mutated>;
	artwork_activity_artwork_info_mutated?: Maybe<Artwork_Activity_Artwork_Info_Mutated>;
	artwork_activity_artwork_mutated?: Maybe<Artwork_Activity_Artwork_Mutated>;
	artwork_activity_artwork_raw_info_mutated?: Maybe<Artwork_Activity_Artwork_Raw_Info_Mutated>;
	artwork_activity_association_mutated?: Maybe<Artwork_Activity_Association_Mutated>;
	artwork_activity_association_type_artwork_activity_type_mutated?: Maybe<Artwork_Activity_Association_Type_Artwork_Activity_Type_Mutated>;
	artwork_activity_association_type_mutated?: Maybe<Artwork_Activity_Association_Type_Mutated>;
	artwork_activity_image_mutated?: Maybe<Artwork_Activity_Image_Mutated>;
	artwork_activity_image_type_mutated?: Maybe<Artwork_Activity_Image_Type_Mutated>;
	artwork_activity_mutated?: Maybe<Artwork_Activity_Mutated>;
	artwork_activity_status_mutated?: Maybe<Artwork_Activity_Status_Mutated>;
	artwork_activity_status_type_artwork_activity_type_mutated?: Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Mutated>;
	artwork_activity_status_type_mutated?: Maybe<Artwork_Activity_Status_Type_Mutated>;
	artwork_activity_type_mutated?: Maybe<Artwork_Activity_Type_Mutated>;
	artwork_artist_mutated?: Maybe<Artwork_Artist_Mutated>;
	artwork_dimension_type_mutated?: Maybe<Artwork_Dimension_Type_Mutated>;
	artwork_listing_mutated?: Maybe<Artwork_Listing_Mutated>;
	artwork_listing_super_type_mutated?: Maybe<Artwork_Listing_Super_Type_Mutated>;
	artwork_listing_type_mutated?: Maybe<Artwork_Listing_Type_Mutated>;
	artwork_mutated?: Maybe<Artwork_Mutated>;
	artwork_organisation_mutated?: Maybe<Artwork_Organisation_Mutated>;
	artwork_raw_info_mutated?: Maybe<Artwork_Raw_Info_Mutated>;
	artwork_raw_info_type_mutated?: Maybe<Artwork_Raw_Info_Type_Mutated>;
	artwork_series_mutated?: Maybe<Artwork_Series_Mutated>;
	artwork_series_tag_mutated?: Maybe<Artwork_Series_Tag_Mutated>;
	artwork_tag_mutated?: Maybe<Artwork_Tag_Mutated>;
	artwork_type_mutated?: Maybe<Artwork_Type_Mutated>;
	auction_auction_type_mutated?: Maybe<Auction_Auction_Type_Mutated>;
	auction_bid_mutated?: Maybe<Auction_Bid_Mutated>;
	auction_bidder_type_mutated?: Maybe<Auction_Bidder_Type_Mutated>;
	auction_client_mutated?: Maybe<Auction_Client_Mutated>;
	auction_group_auction_mutated?: Maybe<Auction_Group_Auction_Mutated>;
	auction_group_mutated?: Maybe<Auction_Group_Mutated>;
	auction_house_buyers_premium_mutated?: Maybe<Auction_House_Buyers_Premium_Mutated>;
	auction_house_mutated?: Maybe<Auction_House_Mutated>;
	auction_house_premium_mutated?: Maybe<Auction_House_Premium_Mutated>;
	auction_lot_attribute_mutated?: Maybe<Auction_Lot_Attribute_Mutated>;
	auction_lot_attribute_type_mutated?: Maybe<Auction_Lot_Attribute_Type_Mutated>;
	auction_lot_bidder_mutated?: Maybe<Auction_Lot_Bidder_Mutated>;
	auction_lot_mutated?: Maybe<Auction_Lot_Mutated>;
	auction_mutated?: Maybe<Auction_Mutated>;
	auction_type_mutated?: Maybe<Auction_Type_Mutated>;
	author_mutated?: Maybe<Author_Mutated>;
	award_mutated?: Maybe<Award_Mutated>;
	collection_note_mutated?: Maybe<Collection_Note_Mutated>;
	collection_note_type_mutated?: Maybe<Collection_Note_Type_Mutated>;
	currency_amount_mutated?: Maybe<Currency_Amount_Mutated>;
	currency_mutated?: Maybe<Currency_Mutated>;
	currency_rate_mutated?: Maybe<Currency_Rate_Mutated>;
	directus_activity_mutated?: Maybe<Directus_Activity_Mutated>;
	directus_dashboards_mutated?: Maybe<Directus_Dashboards_Mutated>;
	directus_files_mutated?: Maybe<Directus_Files_Mutated>;
	directus_flows_mutated?: Maybe<Directus_Flows_Mutated>;
	directus_folders_mutated?: Maybe<Directus_Folders_Mutated>;
	directus_notifications_mutated?: Maybe<Directus_Notifications_Mutated>;
	directus_operations_mutated?: Maybe<Directus_Operations_Mutated>;
	directus_panels_mutated?: Maybe<Directus_Panels_Mutated>;
	directus_permissions_mutated?: Maybe<Directus_Permissions_Mutated>;
	directus_presets_mutated?: Maybe<Directus_Presets_Mutated>;
	directus_revisions_mutated?: Maybe<Directus_Revisions_Mutated>;
	directus_roles_mutated?: Maybe<Directus_Roles_Mutated>;
	directus_settings_mutated?: Maybe<Directus_Settings_Mutated>;
	directus_shares_mutated?: Maybe<Directus_Shares_Mutated>;
	directus_translations_mutated?: Maybe<Directus_Translations_Mutated>;
	directus_users_mutated?: Maybe<Directus_Users_Mutated>;
	directus_versions_mutated?: Maybe<Directus_Versions_Mutated>;
	directus_webhooks_mutated?: Maybe<Directus_Webhooks_Mutated>;
	edition_info_mutated?: Maybe<Edition_Info_Mutated>;
	edition_number_type_mutated?: Maybe<Edition_Number_Type_Mutated>;
	entity_address_mutated?: Maybe<Entity_Address_Mutated>;
	entity_attribute_mutated?: Maybe<Entity_Attribute_Mutated>;
	entity_attribute_type_mutated?: Maybe<Entity_Attribute_Type_Mutated>;
	entity_contact_detail_mutated?: Maybe<Entity_Contact_Detail_Mutated>;
	entity_contact_detail_type_mutated?: Maybe<Entity_Contact_Detail_Type_Mutated>;
	entity_images_mutated?: Maybe<Entity_Images_Mutated>;
	entity_mutated?: Maybe<Entity_Mutated>;
	entity_note_mutated?: Maybe<Entity_Note_Mutated>;
	entity_note_type_mutated?: Maybe<Entity_Note_Type_Mutated>;
	entity_type_mutated?: Maybe<Entity_Type_Mutated>;
	exhibition_artist_mutated?: Maybe<Exhibition_Artist_Mutated>;
	exhibition_artwork_listing_mutated?: Maybe<Exhibition_Artwork_Listing_Mutated>;
	exhibition_artwork_mutated?: Maybe<Exhibition_Artwork_Mutated>;
	exhibition_attribute_mutated?: Maybe<Exhibition_Attribute_Mutated>;
	exhibition_attribute_type_mutated?: Maybe<Exhibition_Attribute_Type_Mutated>;
	exhibition_mutated?: Maybe<Exhibition_Mutated>;
	exhibition_organisers_mutated?: Maybe<Exhibition_Organisers_Mutated>;
	fair_artwork_listing_mutated?: Maybe<Fair_Artwork_Listing_Mutated>;
	fair_exhibitor_mutated?: Maybe<Fair_Exhibitor_Mutated>;
	fair_mutated?: Maybe<Fair_Mutated>;
	fair_organisation_mutated?: Maybe<Fair_Organisation_Mutated>;
	favourite_artist_mutated?: Maybe<Favourite_Artist_Mutated>;
	gallery_artwork_listing_mutated?: Maybe<Gallery_Artwork_Listing_Mutated>;
	gallery_mutated?: Maybe<Gallery_Mutated>;
	gallery_representation_artist_mutated?: Maybe<Gallery_Representation_Artist_Mutated>;
	gallery_representation_mutated?: Maybe<Gallery_Representation_Mutated>;
	gender_mutated?: Maybe<Gender_Mutated>;
	heni_artwork_type_mutated?: Maybe<Heni_Artwork_Type_Mutated>;
	interim_linked_artist_mutated?: Maybe<Interim_Linked_Artist_Mutated>;
	interim_unlinked_artist_mutated?: Maybe<Interim_Unlinked_Artist_Mutated>;
	location_mutated?: Maybe<Location_Mutated>;
	location_timezone_mutated?: Maybe<Location_Timezone_Mutated>;
	location_type_mutated?: Maybe<Location_Type_Mutated>;
	organisation_mutated?: Maybe<Organisation_Mutated>;
	organisation_organisation_type_mutated?: Maybe<Organisation_Organisation_Type_Mutated>;
	organisation_type_mutated?: Maybe<Organisation_Type_Mutated>;
	person_mutated?: Maybe<Person_Mutated>;
	person_nationality_mutated?: Maybe<Person_Nationality_Mutated>;
	person_person_type_mutated?: Maybe<Person_Person_Type_Mutated>;
	person_type_mutated?: Maybe<Person_Type_Mutated>;
	pipeline_info_item_mutated?: Maybe<Pipeline_Info_Item_Mutated>;
	pipeline_info_mutated?: Maybe<Pipeline_Info_Mutated>;
	pipeline_info_type_mutated?: Maybe<Pipeline_Info_Type_Mutated>;
	pipeline_info_unprocessed_mutated?: Maybe<Pipeline_Info_Unprocessed_Mutated>;
	pipeline_source_mutated?: Maybe<Pipeline_Source_Mutated>;
	pipeline_source_processed_mutated?: Maybe<Pipeline_Source_Processed_Mutated>;
	pipeline_source_type_mutated?: Maybe<Pipeline_Source_Type_Mutated>;
	pipeline_source_unprocessed_mutated?: Maybe<Pipeline_Source_Unprocessed_Mutated>;
	pipeline_step_mutated?: Maybe<Pipeline_Step_Mutated>;
	relationship_entity_type_mutated?: Maybe<Relationship_Entity_Type_Mutated>;
	relationship_mutated?: Maybe<Relationship_Mutated>;
	relationship_type_mutated?: Maybe<Relationship_Type_Mutated>;
	report_config_mutated?: Maybe<Report_Config_Mutated>;
	report_config_report_param_mutated?: Maybe<Report_Config_Report_Param_Mutated>;
	report_mutated?: Maybe<Report_Mutated>;
	report_param_mutated?: Maybe<Report_Param_Mutated>;
	status_mutated?: Maybe<Status_Mutated>;
	tag_mutated?: Maybe<Tag_Mutated>;
	tag_type_mutated?: Maybe<Tag_Type_Mutated>;
	validation_error_event_mutated?: Maybe<Validation_Error_Event_Mutated>;
	validation_error_mutated?: Maybe<Validation_Error_Mutated>;
	validation_status_mutated?: Maybe<Validation_Status_Mutated>;
	win_type_mutated?: Maybe<Win_Type_Mutated>;
};

export type SubscriptionPdf_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtist_Award_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtist_Raw_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtist_Raw_Info_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Artwork_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Artwork_Raw_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Association_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Association_Type_Artwork_Activity_Type_MutatedArgs =
	{
		event?: InputMaybe<EventEnum>;
	};

export type SubscriptionArtwork_Activity_Association_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Image_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Status_Type_Artwork_Activity_Type_MutatedArgs =
	{
		event?: InputMaybe<EventEnum>;
	};

export type SubscriptionArtwork_Activity_Status_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Activity_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Dimension_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Listing_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Listing_Super_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Listing_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Organisation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Raw_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Raw_Info_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Series_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Series_Tag_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Tag_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Auction_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Bid_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Bidder_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Client_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Group_Auction_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Group_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_House_Buyers_Premium_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_House_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_House_Premium_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Lot_Attribute_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Lot_Attribute_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Lot_Bidder_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Lot_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuction_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAuthor_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAward_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCollection_Note_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCollection_Note_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCurrency_Amount_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCurrency_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCurrency_Rate_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Activity_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Dashboards_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Flows_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Folders_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Notifications_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Operations_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Panels_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Permissions_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Presets_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Revisions_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Roles_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Settings_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Shares_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Translations_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Users_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Versions_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Webhooks_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEdition_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEdition_Number_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Address_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Attribute_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Attribute_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Contact_Detail_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Contact_Detail_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Images_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Note_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Note_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEntity_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Artwork_Listing_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Attribute_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Attribute_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionExhibition_Organisers_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFair_Artwork_Listing_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFair_Exhibitor_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFair_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFair_Organisation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFavourite_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionGallery_Artwork_Listing_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionGallery_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionGallery_Representation_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionGallery_Representation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionGender_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionHeni_Artwork_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionInterim_Linked_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionInterim_Unlinked_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionLocation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionLocation_Timezone_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionLocation_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionOrganisation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionOrganisation_Organisation_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionOrganisation_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPerson_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPerson_Nationality_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPerson_Person_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPerson_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Info_Item_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Info_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Info_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Info_Unprocessed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Source_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Source_Processed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Source_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Source_Unprocessed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPipeline_Step_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionRelationship_Entity_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionRelationship_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionRelationship_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionReport_Config_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionReport_Config_Report_Param_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionReport_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionReport_Param_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionStatus_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionTag_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionTag_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionValidation_Error_Event_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionValidation_Error_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionValidation_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionWin_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type Artist = {
	__typename?: 'artist';
	artworks?: Maybe<Array<Maybe<Artwork_Artist>>>;
	artworks_func?: Maybe<Count_Functions>;
	awards?: Maybe<Array<Maybe<Artist_Award>>>;
	awards_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	gallery_representation?: Maybe<Array<Maybe<Gallery_Representation_Artist>>>;
	gallery_representation_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	is_favourite?: Maybe<Array<Maybe<Favourite_Artist>>>;
	is_favourite_func?: Maybe<Count_Functions>;
	person?: Maybe<Person>;
	raw_info?: Maybe<Array<Maybe<Artist_Raw_Info>>>;
	raw_info_func?: Maybe<Count_Functions>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	/** The year that an artist was active from. */
	year_active_from?: Maybe<Scalars['Int']['output']>;
	/** The year that an artist was active to. */
	year_active_to?: Maybe<Scalars['Int']['output']>;
};

export type ArtistArtworksArgs = {
	filter?: InputMaybe<Artwork_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistAwardsArgs = {
	filter?: InputMaybe<Artist_Award_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistGallery_RepresentationArgs = {
	filter?: InputMaybe<Gallery_Representation_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistIs_FavouriteArgs = {
	filter?: InputMaybe<Favourite_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistPersonArgs = {
	filter?: InputMaybe<Person_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistRaw_InfoArgs = {
	filter?: InputMaybe<Artist_Raw_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtistUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_Award = {
	__typename?: 'artist_award';
	artist?: Maybe<Artist>;
	award?: Maybe<Award>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	win_type?: Maybe<Win_Type>;
	year: Scalars['Int']['output'];
};

export type Artist_AwardArtistArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_AwardAwardArgs = {
	filter?: InputMaybe<Award_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_AwardStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_AwardUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_AwardUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_AwardWin_TypeArgs = {
	filter?: InputMaybe<Win_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_Award_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Award_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Award_Filter>>>;
	artist?: InputMaybe<Artist_Filter>;
	award?: InputMaybe<Award_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	win_type?: InputMaybe<Win_Type_Filter>;
	year?: InputMaybe<Number_Filter_Operators>;
};

export type Artist_Award_Mutated = {
	__typename?: 'artist_award_mutated';
	data?: Maybe<Artist_Award>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Filter>>>;
	artworks?: InputMaybe<Artwork_Artist_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	awards?: InputMaybe<Artist_Award_Filter>;
	awards_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	gallery_representation?: InputMaybe<Gallery_Representation_Artist_Filter>;
	gallery_representation_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	is_favourite?: InputMaybe<Favourite_Artist_Filter>;
	is_favourite_func?: InputMaybe<Count_Function_Filter_Operators>;
	person?: InputMaybe<Person_Filter>;
	raw_info?: InputMaybe<Artist_Raw_Info_Filter>;
	raw_info_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_active_from?: InputMaybe<Number_Filter_Operators>;
	year_active_to?: InputMaybe<Number_Filter_Operators>;
};

export type Artist_Mutated = {
	__typename?: 'artist_mutated';
	data?: Maybe<Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artist_Raw_Info = {
	__typename?: 'artist_raw_info';
	artist?: Maybe<Artist>;
	id: Scalars['ID']['output'];
	key?: Maybe<Artist_Raw_Info_Type>;
	value?: Maybe<Scalars['String']['output']>;
};

export type Artist_Raw_InfoArtistArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_Raw_InfoKeyArgs = {
	filter?: InputMaybe<Artist_Raw_Info_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_Raw_Info_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Raw_Info_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Raw_Info_Filter>>>;
	artist?: InputMaybe<Artist_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<Artist_Raw_Info_Type_Filter>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Artist_Raw_Info_Mutated = {
	__typename?: 'artist_raw_info_mutated';
	data?: Maybe<Artist_Raw_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artist_Raw_Info_Type = {
	__typename?: 'artist_raw_info_type';
	key: Scalars['ID']['output'];
};

export type Artist_Raw_Info_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Raw_Info_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Raw_Info_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Artist_Raw_Info_Type_Mutated = {
	__typename?: 'artist_raw_info_type_mutated';
	data?: Maybe<Artist_Raw_Info_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork = {
	__typename?: 'artwork';
	activity?: Maybe<Array<Maybe<Artwork_Activity_Artwork>>>;
	activity_func?: Maybe<Count_Functions>;
	additional_info?: Maybe<Scalars['String']['output']>;
	artists?: Maybe<Array<Maybe<Artwork_Artist>>>;
	artists_func?: Maybe<Count_Functions>;
	artwork_type?: Maybe<Artwork_Type>;
	collaborator_organisations?: Maybe<Array<Maybe<Artwork_Organisation>>>;
	collaborator_organisations_func?: Maybe<Count_Functions>;
	crid?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** The scraped text of an artwork before it get's parsed into specific fields. This can be edited after the raw info is scraped. */
	description?: Maybe<Scalars['String']['output']>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_type?: Maybe<Artwork_Dimension_Type>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	edition_info?: Maybe<Edition_Info>;
	execution_end_year?: Maybe<Scalars['Int']['output']>;
	execution_start_year?: Maybe<Scalars['Int']['output']>;
	heni_artwork_type?: Maybe<Heni_Artwork_Type>;
	id: Scalars['ID']['output'];
	/** Indicates that this artwork represents a bundle of artworks. This is different to a full set that contains all the artworks for a particular series. The number of artworks in the bundle is indicated by "Number of Artworks". */
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	/** Indicates that this artwork represents a full set of artworks for a series. The number of artworks in the set is indicated by "Number of Artworks". */
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	media?: Maybe<Scalars['String']['output']>;
	/** When the artwork represents a bundle or full set of artworks, this is the number of artworks within the bundle or set. */
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	/** This identifies how many pieces the artwork consists of. Typically this will be 1 and does not apply to sets. Number of pieces should only apply to artwork pieces that are always sold together. If the number of pieces is greater than 1 then this artwork consists of multiple pieces. */
	number_of_pieces?: Maybe<Scalars['Int']['output']>;
	primary_image?: Maybe<Directus_Files>;
	raw_info?: Maybe<Array<Maybe<Artwork_Raw_Info>>>;
	raw_info_func?: Maybe<Count_Functions>;
	series?: Maybe<Artwork_Series>;
	status?: Maybe<Status>;
	tags?: Maybe<Array<Maybe<Artwork_Tag>>>;
	tags_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type ArtworkActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkArtistsArgs = {
	filter?: InputMaybe<Artwork_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkArtwork_TypeArgs = {
	filter?: InputMaybe<Artwork_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkCollaborator_OrganisationsArgs = {
	filter?: InputMaybe<Artwork_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkDimensions_TypeArgs = {
	filter?: InputMaybe<Artwork_Dimension_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkEdition_InfoArgs = {
	filter?: InputMaybe<Edition_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkHeni_Artwork_TypeArgs = {
	filter?: InputMaybe<Heni_Artwork_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkPrimary_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkRaw_InfoArgs = {
	filter?: InputMaybe<Artwork_Raw_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkSeriesArgs = {
	filter?: InputMaybe<Artwork_Series_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkTagsArgs = {
	filter?: InputMaybe<Artwork_Tag_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity = {
	__typename?: 'artwork_activity';
	activity_artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	activity_status?: Maybe<Array<Maybe<Artwork_Activity_Status>>>;
	activity_status_func?: Maybe<Count_Functions>;
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artwork_listing_func?: Maybe<Count_Functions>;
	artworks?: Maybe<Array<Maybe<Artwork_Activity_Artwork>>>;
	artworks_func?: Maybe<Count_Functions>;
	associations?: Maybe<Array<Maybe<Artwork_Activity_Association>>>;
	associations_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	/** Url of the specific page where this listing is found. This could be the page of a lot on the auction house site (e.g. Christies) or page of the artwork for sale on a the Gallery site. */
	source_page_url?: Maybe<Scalars['String']['output']>;
	/** Url of the site where this listing is found. This could be the auction house site (e.g. Christies) or the Gallery site. */
	source_site_url?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	/** The date and time that the activity took place. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	type?: Maybe<Artwork_Activity_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_ActivityActivity_Artwork_InfoArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityActivity_StatusArgs = {
	filter?: InputMaybe<Artwork_Activity_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityArtwork_ListingArgs = {
	filter?: InputMaybe<Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityArtworksArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityAssociationsArgs = {
	filter?: InputMaybe<Artwork_Activity_Association_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityTypeArgs = {
	filter?: InputMaybe<Artwork_Activity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ActivityUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork = {
	__typename?: 'artwork_activity_artwork';
	artwork?: Maybe<Artwork>;
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** Only integers or roman numerals are accepted. */
	edition_number?: Maybe<Scalars['String']['output']>;
	/** This stores a legacy edition_number as defined in Arteye. This edition number is defined in various formats such as '5/20', '1,2,3,4,5' or affixed with 'AP' to designate that it's an artist proof. This is not necessarily the same as the raw edition number as these could be manually captured. The raw/scraped edition number would be available in the artwork info. */
	edition_number_legacy?: Maybe<Scalars['String']['output']>;
	edition_number_type?: Maybe<Edition_Number_Type>;
	id: Scalars['ID']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_ArtworkArtworkArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ArtworkArtwork_ActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ArtworkEdition_Number_TypeArgs = {
	filter?: InputMaybe<Edition_Number_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Artwork_Filter>>>;
	artwork?: InputMaybe<Artwork_Filter>;
	artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	edition_number?: InputMaybe<String_Filter_Operators>;
	edition_number_legacy?: InputMaybe<String_Filter_Operators>;
	edition_number_type?: InputMaybe<Edition_Number_Type_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Artwork_Info = {
	__typename?: 'artwork_activity_artwork_info';
	additional_images?: Maybe<Array<Maybe<Artwork_Activity_Image>>>;
	additional_images_func?: Maybe<Count_Functions>;
	artwork_activity?: Maybe<Array<Maybe<Artwork_Activity>>>;
	artwork_activity_func?: Maybe<Count_Functions>;
	condition?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	exhibition?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	/** Indicates that this is a bundle of artworks. This is different to a full set that contains all the artworks for a particular series. The number of artworks in the bundle is indicated by "Number of Artworks". */
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	/** Indicates that this artwork represents a full set of artworks for a series. The number of artworks in the set is indicated by "Number of Artworks". */
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	lead?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	primary_image?: Maybe<Directus_Files>;
	provenance?: Maybe<Scalars['String']['output']>;
	raw_artwork_description?: Maybe<Scalars['String']['output']>;
	raw_info?: Maybe<Array<Maybe<Artwork_Activity_Artwork_Raw_Info>>>;
	raw_info_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_Artwork_InfoAdditional_ImagesArgs = {
	filter?: InputMaybe<Artwork_Activity_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_InfoArtwork_ActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_InfoPrimary_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_InfoRaw_InfoArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Raw_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_InfoUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_InfoUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_Info_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Artwork_Info_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Artwork_Info_Filter>>>;
	additional_images?: InputMaybe<Artwork_Activity_Image_Filter>;
	additional_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	artwork_activity_func?: InputMaybe<Count_Function_Filter_Operators>;
	condition?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibition?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	ingestion_notes?: InputMaybe<String_Filter_Operators>;
	is_bundle?: InputMaybe<Boolean_Filter_Operators>;
	is_full_set?: InputMaybe<Boolean_Filter_Operators>;
	lead?: InputMaybe<String_Filter_Operators>;
	literature?: InputMaybe<String_Filter_Operators>;
	notes?: InputMaybe<String_Filter_Operators>;
	number_of_artworks?: InputMaybe<Number_Filter_Operators>;
	primary_image?: InputMaybe<Directus_Files_Filter>;
	provenance?: InputMaybe<String_Filter_Operators>;
	raw_artwork_description?: InputMaybe<String_Filter_Operators>;
	raw_info?: InputMaybe<Artwork_Activity_Artwork_Raw_Info_Filter>;
	raw_info_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Artwork_Info_Mutated = {
	__typename?: 'artwork_activity_artwork_info_mutated';
	data?: Maybe<Artwork_Activity_Artwork_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Artwork_Mutated = {
	__typename?: 'artwork_activity_artwork_mutated';
	data?: Maybe<Artwork_Activity_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Artwork_Raw_Info = {
	__typename?: 'artwork_activity_artwork_raw_info';
	artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	id: Scalars['ID']['output'];
	key?: Maybe<Artwork_Raw_Info_Type>;
	value: Scalars['String']['output'];
};

export type Artwork_Activity_Artwork_Raw_InfoArtwork_InfoArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_Raw_InfoKeyArgs = {
	filter?: InputMaybe<Artwork_Raw_Info_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Artwork_Raw_Info_Filter = {
	_and?: InputMaybe<
		Array<InputMaybe<Artwork_Activity_Artwork_Raw_Info_Filter>>
	>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Artwork_Raw_Info_Filter>>>;
	artwork_info?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<Artwork_Raw_Info_Type_Filter>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Activity_Artwork_Raw_Info_Mutated = {
	__typename?: 'artwork_activity_artwork_raw_info_mutated';
	data?: Maybe<Artwork_Activity_Artwork_Raw_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Association = {
	__typename?: 'artwork_activity_association';
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	type?: Maybe<Artwork_Activity_Association_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_AssociationArtwork_ActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_AssociationEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_AssociationTypeArgs = {
	filter?: InputMaybe<Artwork_Activity_Association_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_AssociationUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_AssociationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Association_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Association_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Association_Filter>>>;
	artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<Artwork_Activity_Association_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Association_Mutated = {
	__typename?: 'artwork_activity_association_mutated';
	data?: Maybe<Artwork_Activity_Association>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Association_Type = {
	__typename?: 'artwork_activity_association_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	supported_activity_types?: Maybe<
		Array<Maybe<Artwork_Activity_Association_Type_Artwork_Activity_Type>>
	>;
	supported_activity_types_func?: Maybe<Count_Functions>;
};

export type Artwork_Activity_Association_TypeSupported_Activity_TypesArgs = {
	filter?: InputMaybe<Artwork_Activity_Association_Type_Artwork_Activity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Association_Type_Artwork_Activity_Type = {
	__typename?: 'artwork_activity_association_type_artwork_activity_type';
	artwork_activity_association_type_key?: Maybe<Artwork_Activity_Association_Type>;
	artwork_activity_type_key?: Maybe<Artwork_Activity_Type>;
	id: Scalars['ID']['output'];
};

export type Artwork_Activity_Association_Type_Artwork_Activity_TypeArtwork_Activity_Association_Type_KeyArgs =
	{
		filter?: InputMaybe<Artwork_Activity_Association_Type_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Artwork_Activity_Association_Type_Artwork_Activity_TypeArtwork_Activity_Type_KeyArgs =
	{
		filter?: InputMaybe<Artwork_Activity_Type_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Artwork_Activity_Association_Type_Artwork_Activity_Type_Filter = {
	_and?: InputMaybe<
		Array<
			InputMaybe<Artwork_Activity_Association_Type_Artwork_Activity_Type_Filter>
		>
	>;
	_or?: InputMaybe<
		Array<
			InputMaybe<Artwork_Activity_Association_Type_Artwork_Activity_Type_Filter>
		>
	>;
	artwork_activity_association_type_key?: InputMaybe<Artwork_Activity_Association_Type_Filter>;
	artwork_activity_type_key?: InputMaybe<Artwork_Activity_Type_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Activity_Association_Type_Artwork_Activity_Type_Mutated = {
	__typename?: 'artwork_activity_association_type_artwork_activity_type_mutated';
	data?: Maybe<Artwork_Activity_Association_Type_Artwork_Activity_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Association_Type_Filter = {
	_and?: InputMaybe<
		Array<InputMaybe<Artwork_Activity_Association_Type_Filter>>
	>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Association_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	supported_activity_types?: InputMaybe<Artwork_Activity_Association_Type_Artwork_Activity_Type_Filter>;
	supported_activity_types_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Artwork_Activity_Association_Type_Mutated = {
	__typename?: 'artwork_activity_association_type_mutated';
	data?: Maybe<Artwork_Activity_Association_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Filter>>>;
	activity_artwork_info?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	activity_status?: InputMaybe<Artwork_Activity_Status_Filter>;
	activity_status_func?: InputMaybe<Count_Function_Filter_Operators>;
	artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	artwork_listing_func?: InputMaybe<Count_Function_Filter_Operators>;
	artworks?: InputMaybe<Artwork_Activity_Artwork_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	associations?: InputMaybe<Artwork_Activity_Association_Filter>;
	associations_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	ingestion_notes?: InputMaybe<String_Filter_Operators>;
	notes?: InputMaybe<String_Filter_Operators>;
	source_page_url?: InputMaybe<String_Filter_Operators>;
	source_site_url?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	type?: InputMaybe<Artwork_Activity_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Image = {
	__typename?: 'artwork_activity_image';
	artwork_info?: Maybe<Artwork_Activity_Artwork_Info>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	status?: Maybe<Status>;
	type?: Maybe<Artwork_Activity_Image_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_ImageArtwork_InfoArgs = {
	filter?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ImageImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ImageStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ImageTypeArgs = {
	filter?: InputMaybe<Artwork_Activity_Image_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ImageUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_ImageUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Image_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Image_Filter>>>;
	artwork_info?: InputMaybe<Artwork_Activity_Artwork_Info_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Artwork_Activity_Image_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Image_Mutated = {
	__typename?: 'artwork_activity_image_mutated';
	data?: Maybe<Artwork_Activity_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Image_Type = {
	__typename?: 'artwork_activity_image_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Activity_Image_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Image_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Image_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Activity_Image_Type_Mutated = {
	__typename?: 'artwork_activity_image_type_mutated';
	data?: Maybe<Artwork_Activity_Image_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Mutated = {
	__typename?: 'artwork_activity_mutated';
	data?: Maybe<Artwork_Activity>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Status = {
	__typename?: 'artwork_activity_status';
	artwork_activity?: Maybe<Artwork_Activity>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	/** The timestamp of when the status change took place. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	type?: Maybe<Artwork_Activity_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Activity_StatusArtwork_ActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_StatusStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_StatusTypeArgs = {
	filter?: InputMaybe<Artwork_Activity_Status_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_StatusUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_StatusUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Status_Filter>>>;
	artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	type?: InputMaybe<Artwork_Activity_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Activity_Status_Mutated = {
	__typename?: 'artwork_activity_status_mutated';
	data?: Maybe<Artwork_Activity_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Status_Type = {
	__typename?: 'artwork_activity_status_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	supported_activity_types?: Maybe<
		Array<Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type>>
	>;
	supported_activity_types_func?: Maybe<Count_Functions>;
};

export type Artwork_Activity_Status_TypeSupported_Activity_TypesArgs = {
	filter?: InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Status_Type_Artwork_Activity_Type = {
	__typename?: 'artwork_activity_status_type_artwork_activity_type';
	artwork_activity_status_type_key?: Maybe<Artwork_Activity_Status_Type>;
	artwork_activity_type_key?: Maybe<Artwork_Activity_Type>;
	id: Scalars['ID']['output'];
};

export type Artwork_Activity_Status_Type_Artwork_Activity_TypeArtwork_Activity_Status_Type_KeyArgs =
	{
		filter?: InputMaybe<Artwork_Activity_Status_Type_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Artwork_Activity_Status_Type_Artwork_Activity_TypeArtwork_Activity_Type_KeyArgs =
	{
		filter?: InputMaybe<Artwork_Activity_Type_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter = {
	_and?: InputMaybe<
		Array<InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>>
	>;
	_or?: InputMaybe<
		Array<InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>>
	>;
	artwork_activity_status_type_key?: InputMaybe<Artwork_Activity_Status_Type_Filter>;
	artwork_activity_type_key?: InputMaybe<Artwork_Activity_Type_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Activity_Status_Type_Artwork_Activity_Type_Mutated = {
	__typename?: 'artwork_activity_status_type_artwork_activity_type_mutated';
	data?: Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Status_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Status_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Status_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	supported_activity_types?: InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>;
	supported_activity_types_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Artwork_Activity_Status_Type_Mutated = {
	__typename?: 'artwork_activity_status_type_mutated';
	data?: Maybe<Artwork_Activity_Status_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Activity_Type = {
	__typename?: 'artwork_activity_type';
	artwork_activity_status_types?: Maybe<
		Array<Maybe<Artwork_Activity_Status_Type_Artwork_Activity_Type>>
	>;
	artwork_activity_status_types_func?: Maybe<Count_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Activity_TypeArtwork_Activity_Status_TypesArgs = {
	filter?: InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Activity_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Activity_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Activity_Type_Filter>>>;
	artwork_activity_status_types?: InputMaybe<Artwork_Activity_Status_Type_Artwork_Activity_Type_Filter>;
	artwork_activity_status_types_func?: InputMaybe<Count_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Activity_Type_Mutated = {
	__typename?: 'artwork_activity_type_mutated';
	data?: Maybe<Artwork_Activity_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Artist = {
	__typename?: 'artwork_artist';
	artist_id?: Maybe<Artist>;
	artwork_id?: Maybe<Artwork>;
	id: Scalars['ID']['output'];
};

export type Artwork_ArtistArtist_IdArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ArtistArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Artist_Filter>>>;
	artist_id?: InputMaybe<Artist_Filter>;
	artwork_id?: InputMaybe<Artwork_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Artist_Mutated = {
	__typename?: 'artwork_artist_mutated';
	data?: Maybe<Artwork_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Dimension_Type = {
	__typename?: 'artwork_dimension_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Dimension_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Dimension_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Dimension_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Dimension_Type_Mutated = {
	__typename?: 'artwork_dimension_type_mutated';
	data?: Maybe<Artwork_Dimension_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Filter>>>;
	activity?: InputMaybe<Artwork_Activity_Artwork_Filter>;
	activity_func?: InputMaybe<Count_Function_Filter_Operators>;
	additional_info?: InputMaybe<String_Filter_Operators>;
	artists?: InputMaybe<Artwork_Artist_Filter>;
	artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	artwork_type?: InputMaybe<Artwork_Type_Filter>;
	collaborator_organisations?: InputMaybe<Artwork_Organisation_Filter>;
	collaborator_organisations_func?: InputMaybe<Count_Function_Filter_Operators>;
	crid?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	dimensions_depth_cm?: InputMaybe<Number_Filter_Operators>;
	dimensions_height_cm?: InputMaybe<Number_Filter_Operators>;
	dimensions_type?: InputMaybe<Artwork_Dimension_Type_Filter>;
	dimensions_width_cm?: InputMaybe<Number_Filter_Operators>;
	edition_info?: InputMaybe<Edition_Info_Filter>;
	execution_end_year?: InputMaybe<Number_Filter_Operators>;
	execution_start_year?: InputMaybe<Number_Filter_Operators>;
	heni_artwork_type?: InputMaybe<Heni_Artwork_Type_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	is_bundle?: InputMaybe<Boolean_Filter_Operators>;
	is_full_set?: InputMaybe<Boolean_Filter_Operators>;
	media?: InputMaybe<String_Filter_Operators>;
	number_of_artworks?: InputMaybe<Number_Filter_Operators>;
	number_of_pieces?: InputMaybe<Number_Filter_Operators>;
	primary_image?: InputMaybe<Directus_Files_Filter>;
	raw_info?: InputMaybe<Artwork_Raw_Info_Filter>;
	raw_info_func?: InputMaybe<Count_Function_Filter_Operators>;
	series?: InputMaybe<Artwork_Series_Filter>;
	status?: InputMaybe<Status_Filter>;
	tags?: InputMaybe<Artwork_Tag_Filter>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Listing = {
	__typename?: 'artwork_listing';
	artwork_activity?: Maybe<Artwork_Activity>;
	auction_lot?: Maybe<Auction_Lot>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	exhibition_listing?: Maybe<Exhibition_Artwork_Listing>;
	fair_listing?: Maybe<Fair_Artwork_Listing>;
	gallery_listing?: Maybe<Gallery_Artwork_Listing>;
	id: Scalars['ID']['output'];
	known_price?: Maybe<Currency_Amount>;
	listing_type?: Maybe<Artwork_Listing_Type>;
	price_high_estimate?: Maybe<Currency_Amount>;
	price_low_estimate?: Maybe<Currency_Amount>;
	sale_amount?: Maybe<Currency_Amount>;
	shipping?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_ListingArtwork_ActivityArgs = {
	filter?: InputMaybe<Artwork_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingAuction_LotArgs = {
	filter?: InputMaybe<Auction_Lot_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingExhibition_ListingArgs = {
	filter?: InputMaybe<Exhibition_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingFair_ListingArgs = {
	filter?: InputMaybe<Fair_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingGallery_ListingArgs = {
	filter?: InputMaybe<Gallery_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingKnown_PriceArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingListing_TypeArgs = {
	filter?: InputMaybe<Artwork_Listing_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingPrice_High_EstimateArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingPrice_Low_EstimateArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingSale_AmountArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_ListingUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Listing_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Listing_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Listing_Filter>>>;
	artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	auction_lot?: InputMaybe<Auction_Lot_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibition_listing?: InputMaybe<Exhibition_Artwork_Listing_Filter>;
	fair_listing?: InputMaybe<Fair_Artwork_Listing_Filter>;
	gallery_listing?: InputMaybe<Gallery_Artwork_Listing_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	known_price?: InputMaybe<Currency_Amount_Filter>;
	listing_type?: InputMaybe<Artwork_Listing_Type_Filter>;
	price_high_estimate?: InputMaybe<Currency_Amount_Filter>;
	price_low_estimate?: InputMaybe<Currency_Amount_Filter>;
	sale_amount?: InputMaybe<Currency_Amount_Filter>;
	shipping?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Listing_Mutated = {
	__typename?: 'artwork_listing_mutated';
	data?: Maybe<Artwork_Listing>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Listing_Super_Type = {
	__typename?: 'artwork_listing_super_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Listing_Super_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Listing_Super_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Listing_Super_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Listing_Super_Type_Mutated = {
	__typename?: 'artwork_listing_super_type_mutated';
	data?: Maybe<Artwork_Listing_Super_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Listing_Type = {
	__typename?: 'artwork_listing_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	super_type?: Maybe<Artwork_Listing_Super_Type>;
};

export type Artwork_Listing_TypeSuper_TypeArgs = {
	filter?: InputMaybe<Artwork_Listing_Super_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Listing_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Listing_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Listing_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	super_type?: InputMaybe<Artwork_Listing_Super_Type_Filter>;
};

export type Artwork_Listing_Type_Mutated = {
	__typename?: 'artwork_listing_type_mutated';
	data?: Maybe<Artwork_Listing_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Mutated = {
	__typename?: 'artwork_mutated';
	data?: Maybe<Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Organisation = {
	__typename?: 'artwork_organisation';
	artwork_id?: Maybe<Artwork>;
	id: Scalars['ID']['output'];
	organisation_id?: Maybe<Organisation>;
};

export type Artwork_OrganisationArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_OrganisationOrganisation_IdArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Organisation_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Organisation_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Organisation_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	organisation_id?: InputMaybe<Organisation_Filter>;
};

export type Artwork_Organisation_Mutated = {
	__typename?: 'artwork_organisation_mutated';
	data?: Maybe<Artwork_Organisation>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Raw_Info = {
	__typename?: 'artwork_raw_info';
	artwork?: Maybe<Artwork>;
	id: Scalars['ID']['output'];
	key?: Maybe<Artwork_Raw_Info_Type>;
	value: Scalars['String']['output'];
};

export type Artwork_Raw_InfoArtworkArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Raw_InfoKeyArgs = {
	filter?: InputMaybe<Artwork_Raw_Info_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Raw_Info_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Raw_Info_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Raw_Info_Filter>>>;
	artwork?: InputMaybe<Artwork_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<Artwork_Raw_Info_Type_Filter>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Raw_Info_Mutated = {
	__typename?: 'artwork_raw_info_mutated';
	data?: Maybe<Artwork_Raw_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Raw_Info_Type = {
	__typename?: 'artwork_raw_info_type';
	key: Scalars['ID']['output'];
};

export type Artwork_Raw_Info_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Raw_Info_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Raw_Info_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Raw_Info_Type_Mutated = {
	__typename?: 'artwork_raw_info_type_mutated';
	data?: Maybe<Artwork_Raw_Info_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Series = {
	__typename?: 'artwork_series';
	artworks?: Maybe<Array<Maybe<Artwork>>>;
	artworks_func?: Maybe<Count_Functions>;
	crid?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	is_heni_series?: Maybe<Scalars['Boolean']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	parent_series?: Maybe<Artwork_Series>;
	status?: Maybe<Status>;
	sub_series?: Maybe<Array<Maybe<Artwork_Series>>>;
	sub_series_func?: Maybe<Count_Functions>;
	tags?: Maybe<Array<Maybe<Artwork_Series_Tag>>>;
	tags_func?: Maybe<Count_Functions>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_SeriesArtworksArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesParent_SeriesArgs = {
	filter?: InputMaybe<Artwork_Series_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesSub_SeriesArgs = {
	filter?: InputMaybe<Artwork_Series_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesTagsArgs = {
	filter?: InputMaybe<Artwork_Series_Tag_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_SeriesUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Series_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Series_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Series_Filter>>>;
	artworks?: InputMaybe<Artwork_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	crid?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	is_heni_series?: InputMaybe<Boolean_Filter_Operators>;
	number_of_artworks?: InputMaybe<Number_Filter_Operators>;
	parent_series?: InputMaybe<Artwork_Series_Filter>;
	status?: InputMaybe<Status_Filter>;
	sub_series?: InputMaybe<Artwork_Series_Filter>;
	sub_series_func?: InputMaybe<Count_Function_Filter_Operators>;
	tags?: InputMaybe<Artwork_Series_Tag_Filter>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Series_Mutated = {
	__typename?: 'artwork_series_mutated';
	data?: Maybe<Artwork_Series>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Series_Tag = {
	__typename?: 'artwork_series_tag';
	artwork_series_id?: Maybe<Artwork_Series>;
	id: Scalars['ID']['output'];
	tag_tag?: Maybe<Tag>;
};

export type Artwork_Series_TagArtwork_Series_IdArgs = {
	filter?: InputMaybe<Artwork_Series_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Series_TagTag_TagArgs = {
	filter?: InputMaybe<Tag_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Series_Tag_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Series_Tag_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Series_Tag_Filter>>>;
	artwork_series_id?: InputMaybe<Artwork_Series_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	tag_tag?: InputMaybe<Tag_Filter>;
};

export type Artwork_Series_Tag_Mutated = {
	__typename?: 'artwork_series_tag_mutated';
	data?: Maybe<Artwork_Series_Tag>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Tag = {
	__typename?: 'artwork_tag';
	artwork_id?: Maybe<Artwork>;
	id: Scalars['ID']['output'];
	tag_tag?: Maybe<Tag>;
};

export type Artwork_TagArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_TagTag_TagArgs = {
	filter?: InputMaybe<Tag_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Tag_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Tag_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Tag_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	tag_tag?: InputMaybe<Tag_Filter>;
};

export type Artwork_Tag_Mutated = {
	__typename?: 'artwork_tag_mutated';
	data?: Maybe<Artwork_Tag>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Type = {
	__typename?: 'artwork_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Type_Mutated = {
	__typename?: 'artwork_type_mutated';
	data?: Maybe<Artwork_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction = {
	__typename?: 'auction';
	auction_end_date?: Maybe<Scalars['Date']['output']>;
	auction_end_date_func?: Maybe<Datetime_Functions>;
	auction_groups?: Maybe<Array<Maybe<Auction_Group_Auction>>>;
	auction_groups_func?: Maybe<Count_Functions>;
	auction_house?: Maybe<Auction_House>;
	auction_start_date?: Maybe<Scalars['Date']['output']>;
	auction_start_date_func?: Maybe<Datetime_Functions>;
	auction_types?: Maybe<Array<Maybe<Auction_Auction_Type>>>;
	auction_types_func?: Maybe<Count_Functions>;
	clients?: Maybe<Array<Maybe<Auction_Client>>>;
	clients_func?: Maybe<Count_Functions>;
	cover_page_image?: Maybe<Directus_Files>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	lots?: Maybe<Array<Maybe<Auction_Lot>>>;
	lots_func?: Maybe<Count_Functions>;
	/** This is the title or name of the auction. Commonly referred to as the sale name. */
	sale_name?: Maybe<Scalars['String']['output']>;
	/** This is the unique number of the auction. Commonly known as the sale number. */
	sale_number?: Maybe<Scalars['String']['output']>;
	/** A link to the auction page */
	sale_url?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type AuctionAuction_GroupsArgs = {
	filter?: InputMaybe<Auction_Group_Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionAuction_HouseArgs = {
	filter?: InputMaybe<Auction_House_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionAuction_TypesArgs = {
	filter?: InputMaybe<Auction_Auction_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionClientsArgs = {
	filter?: InputMaybe<Auction_Client_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionCover_Page_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionCurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionLotsArgs = {
	filter?: InputMaybe<Auction_Lot_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuctionUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Auction_Type = {
	__typename?: 'auction_auction_type';
	auction_id?: Maybe<Auction>;
	auction_type_key?: Maybe<Auction_Type>;
	id: Scalars['ID']['output'];
};

export type Auction_Auction_TypeAuction_IdArgs = {
	filter?: InputMaybe<Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Auction_TypeAuction_Type_KeyArgs = {
	filter?: InputMaybe<Auction_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Auction_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Auction_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Auction_Type_Filter>>>;
	auction_id?: InputMaybe<Auction_Filter>;
	auction_type_key?: InputMaybe<Auction_Type_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Auction_Auction_Type_Mutated = {
	__typename?: 'auction_auction_type_mutated';
	data?: Maybe<Auction_Auction_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Bid = {
	__typename?: 'auction_bid';
	amount?: Maybe<Currency_Amount>;
	auction_lot?: Maybe<Auction_Lot>;
	bidder?: Maybe<Auction_Lot_Bidder>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	/** This is the timestamp that is captured in bidding chart app, relative to each other timestamp in the bid and not indicative of the actual time of the bid. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_BidAmountArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_BidAuction_LotArgs = {
	filter?: InputMaybe<Auction_Lot_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_BidBidderArgs = {
	filter?: InputMaybe<Auction_Lot_Bidder_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_BidStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_BidUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_BidUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Bid_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Bid_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Bid_Filter>>>;
	amount?: InputMaybe<Currency_Amount_Filter>;
	auction_lot?: InputMaybe<Auction_Lot_Filter>;
	bidder?: InputMaybe<Auction_Lot_Bidder_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Bid_Mutated = {
	__typename?: 'auction_bid_mutated';
	data?: Maybe<Auction_Bid>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Bidder_Type = {
	__typename?: 'auction_bidder_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	label: Scalars['String']['output'];
	name: Scalars['String']['output'];
};

export type Auction_Bidder_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Bidder_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Bidder_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	label?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Auction_Bidder_Type_Mutated = {
	__typename?: 'auction_bidder_type_mutated';
	data?: Maybe<Auction_Bidder_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Client = {
	__typename?: 'auction_client';
	auction?: Maybe<Auction>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	paddle_number?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_ClientAuctionArgs = {
	filter?: InputMaybe<Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_ClientEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_ClientUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_ClientUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Client_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Client_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Client_Filter>>>;
	auction?: InputMaybe<Auction_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	paddle_number?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Client_Mutated = {
	__typename?: 'auction_client_mutated';
	data?: Maybe<Auction_Client>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Filter>>>;
	auction_end_date?: InputMaybe<Date_Filter_Operators>;
	auction_end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	auction_groups?: InputMaybe<Auction_Group_Auction_Filter>;
	auction_groups_func?: InputMaybe<Count_Function_Filter_Operators>;
	auction_house?: InputMaybe<Auction_House_Filter>;
	auction_start_date?: InputMaybe<Date_Filter_Operators>;
	auction_start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	auction_types?: InputMaybe<Auction_Auction_Type_Filter>;
	auction_types_func?: InputMaybe<Count_Function_Filter_Operators>;
	clients?: InputMaybe<Auction_Client_Filter>;
	clients_func?: InputMaybe<Count_Function_Filter_Operators>;
	cover_page_image?: InputMaybe<Directus_Files_Filter>;
	currency?: InputMaybe<Currency_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	lots?: InputMaybe<Auction_Lot_Filter>;
	lots_func?: InputMaybe<Count_Function_Filter_Operators>;
	sale_name?: InputMaybe<String_Filter_Operators>;
	sale_number?: InputMaybe<String_Filter_Operators>;
	sale_url?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Group = {
	__typename?: 'auction_group';
	auctions?: Maybe<Array<Maybe<Auction_Group_Auction>>>;
	auctions_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_GroupAuctionsArgs = {
	filter?: InputMaybe<Auction_Group_Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_GroupUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_GroupUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Group_Auction = {
	__typename?: 'auction_group_auction';
	auction_group_id?: Maybe<Auction_Group>;
	auction_id?: Maybe<Auction>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Auction_Group_AuctionAuction_Group_IdArgs = {
	filter?: InputMaybe<Auction_Group_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Group_AuctionAuction_IdArgs = {
	filter?: InputMaybe<Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Group_Auction_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Group_Auction_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Group_Auction_Filter>>>;
	auction_group_id?: InputMaybe<Auction_Group_Filter>;
	auction_id?: InputMaybe<Auction_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Auction_Group_Auction_Mutated = {
	__typename?: 'auction_group_auction_mutated';
	data?: Maybe<Auction_Group_Auction>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Group_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Group_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Group_Filter>>>;
	auctions?: InputMaybe<Auction_Group_Auction_Filter>;
	auctions_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Group_Mutated = {
	__typename?: 'auction_group_mutated';
	data?: Maybe<Auction_Group>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_House = {
	__typename?: 'auction_house';
	auctions?: Maybe<Array<Maybe<Auction>>>;
	auctions_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	organisation?: Maybe<Organisation>;
	premiums?: Maybe<Array<Maybe<Auction_House_Premium>>>;
	premiums_func?: Maybe<Count_Functions>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_HouseAuctionsArgs = {
	filter?: InputMaybe<Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_HouseOrganisationArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_HousePremiumsArgs = {
	filter?: InputMaybe<Auction_House_Premium_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_HouseStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_HouseUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_HouseUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_Premium = {
	__typename?: 'auction_house_buyers_premium';
	auction_house_premium?: Maybe<Auction_House_Premium>;
	band_high_amount?: Maybe<Currency_Amount>;
	band_low_amount?: Maybe<Currency_Amount>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	premium_rate: Scalars['Float']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_House_Buyers_PremiumAuction_House_PremiumArgs = {
	filter?: InputMaybe<Auction_House_Premium_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_PremiumBand_High_AmountArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_PremiumBand_Low_AmountArgs = {
	filter?: InputMaybe<Currency_Amount_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_PremiumStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_PremiumUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_PremiumUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Buyers_Premium_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_House_Buyers_Premium_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_House_Buyers_Premium_Filter>>>;
	auction_house_premium?: InputMaybe<Auction_House_Premium_Filter>;
	band_high_amount?: InputMaybe<Currency_Amount_Filter>;
	band_low_amount?: InputMaybe<Currency_Amount_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	premium_rate?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_House_Buyers_Premium_Mutated = {
	__typename?: 'auction_house_buyers_premium_mutated';
	data?: Maybe<Auction_House_Buyers_Premium>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_House_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_House_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_House_Filter>>>;
	auctions?: InputMaybe<Auction_Filter>;
	auctions_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	organisation?: InputMaybe<Organisation_Filter>;
	premiums?: InputMaybe<Auction_House_Premium_Filter>;
	premiums_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_House_Mutated = {
	__typename?: 'auction_house_mutated';
	data?: Maybe<Auction_House>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_House_Premium = {
	__typename?: 'auction_house_premium';
	auction_house?: Maybe<Auction_House>;
	buyers_premiums?: Maybe<Array<Maybe<Auction_House_Buyers_Premium>>>;
	buyers_premiums_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	overhead_premium_rate?: Maybe<Scalars['Float']['output']>;
	sellers_premium_rate?: Maybe<Scalars['Float']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year: Scalars['Int']['output'];
};

export type Auction_House_PremiumAuction_HouseArgs = {
	filter?: InputMaybe<Auction_House_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_PremiumBuyers_PremiumsArgs = {
	filter?: InputMaybe<Auction_House_Buyers_Premium_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_PremiumStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_PremiumUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_PremiumUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_House_Premium_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_House_Premium_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_House_Premium_Filter>>>;
	auction_house?: InputMaybe<Auction_House_Filter>;
	buyers_premiums?: InputMaybe<Auction_House_Buyers_Premium_Filter>;
	buyers_premiums_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	overhead_premium_rate?: InputMaybe<Number_Filter_Operators>;
	sellers_premium_rate?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year?: InputMaybe<Number_Filter_Operators>;
};

export type Auction_House_Premium_Mutated = {
	__typename?: 'auction_house_premium_mutated';
	data?: Maybe<Auction_House_Premium>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Lot = {
	__typename?: 'auction_lot';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artwork_listing_func?: Maybe<Count_Functions>;
	attributes?: Maybe<Array<Maybe<Auction_Lot_Attribute>>>;
	attributes_func?: Maybe<Count_Functions>;
	auction?: Maybe<Auction>;
	auctioneer?: Maybe<Person>;
	bidders?: Maybe<Array<Maybe<Auction_Lot_Bidder>>>;
	bidders_func?: Maybe<Count_Functions>;
	bids?: Maybe<Array<Maybe<Auction_Bid>>>;
	bids_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	hammer_timestamp?: Maybe<Scalars['Date']['output']>;
	hammer_timestamp_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	lot_notes?: Maybe<Scalars['String']['output']>;
	lot_number?: Maybe<Scalars['String']['output']>;
	/** Indicates that the sale amount on the artwork listing includes the auction house premium or not. */
	sale_amount_includes_premium?: Maybe<Scalars['Boolean']['output']>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	/** The starting bid amount. Different to the low and high estimate for the auction lot. */
	starting_bid_amount?: Maybe<Scalars['Float']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	video_file_name?: Maybe<Scalars['String']['output']>;
	video_hammer_time_seconds?: Maybe<Scalars['Date']['output']>;
	video_hammer_time_seconds_func?: Maybe<Time_Functions>;
	winning_bid?: Maybe<Auction_Bid>;
};

export type Auction_LotArtwork_ListingArgs = {
	filter?: InputMaybe<Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotAttributesArgs = {
	filter?: InputMaybe<Auction_Lot_Attribute_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotAuctionArgs = {
	filter?: InputMaybe<Auction_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotAuctioneerArgs = {
	filter?: InputMaybe<Person_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotBiddersArgs = {
	filter?: InputMaybe<Auction_Lot_Bidder_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotBidsArgs = {
	filter?: InputMaybe<Auction_Bid_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_LotWinning_BidArgs = {
	filter?: InputMaybe<Auction_Bid_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_Attribute = {
	__typename?: 'auction_lot_attribute';
	/** A generic amount field for a guaranteed amount or irrevocable bid amount. */
	amount?: Maybe<Scalars['Float']['output']>;
	auction_lot?: Maybe<Auction_Lot>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	type?: Maybe<Auction_Lot_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Lot_AttributeAuction_LotArgs = {
	filter?: InputMaybe<Auction_Lot_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_AttributeTypeArgs = {
	filter?: InputMaybe<Auction_Lot_Attribute_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_AttributeUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_AttributeUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_Attribute_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Lot_Attribute_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Lot_Attribute_Filter>>>;
	amount?: InputMaybe<Number_Filter_Operators>;
	auction_lot?: InputMaybe<Auction_Lot_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<Auction_Lot_Attribute_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Lot_Attribute_Mutated = {
	__typename?: 'auction_lot_attribute_mutated';
	data?: Maybe<Auction_Lot_Attribute>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Lot_Attribute_Type = {
	__typename?: 'auction_lot_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Auction_Lot_Attribute_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Lot_Attribute_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Lot_Attribute_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Auction_Lot_Attribute_Type_Mutated = {
	__typename?: 'auction_lot_attribute_type_mutated';
	data?: Maybe<Auction_Lot_Attribute_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Lot_Bidder = {
	__typename?: 'auction_lot_bidder';
	bidder?: Maybe<Entity>;
	bidder_type?: Maybe<Auction_Bidder_Type>;
	bids?: Maybe<Array<Maybe<Auction_Bid>>>;
	bids_func?: Maybe<Count_Functions>;
	client?: Maybe<Auction_Client>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	/** The number identifying where the bidder is located. E.g. If P1 the bidder type would be Phone and the location number would be 1. */
	location_number?: Maybe<Scalars['Int']['output']>;
	lot?: Maybe<Auction_Lot>;
	notes?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Auction_Lot_BidderBidderArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderBidder_TypeArgs = {
	filter?: InputMaybe<Auction_Bidder_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderBidsArgs = {
	filter?: InputMaybe<Auction_Bid_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderClientArgs = {
	filter?: InputMaybe<Auction_Client_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderLotArgs = {
	filter?: InputMaybe<Auction_Lot_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_BidderUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Auction_Lot_Bidder_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Lot_Bidder_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Lot_Bidder_Filter>>>;
	bidder?: InputMaybe<Entity_Filter>;
	bidder_type?: InputMaybe<Auction_Bidder_Type_Filter>;
	bids?: InputMaybe<Auction_Bid_Filter>;
	bids_func?: InputMaybe<Count_Function_Filter_Operators>;
	client?: InputMaybe<Auction_Client_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	location_number?: InputMaybe<Number_Filter_Operators>;
	lot?: InputMaybe<Auction_Lot_Filter>;
	notes?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Auction_Lot_Bidder_Mutated = {
	__typename?: 'auction_lot_bidder_mutated';
	data?: Maybe<Auction_Lot_Bidder>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Lot_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Lot_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Lot_Filter>>>;
	artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	artwork_listing_func?: InputMaybe<Count_Function_Filter_Operators>;
	attributes?: InputMaybe<Auction_Lot_Attribute_Filter>;
	attributes_func?: InputMaybe<Count_Function_Filter_Operators>;
	auction?: InputMaybe<Auction_Filter>;
	auctioneer?: InputMaybe<Person_Filter>;
	bidders?: InputMaybe<Auction_Lot_Bidder_Filter>;
	bidders_func?: InputMaybe<Count_Function_Filter_Operators>;
	bids?: InputMaybe<Auction_Bid_Filter>;
	bids_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	hammer_timestamp?: InputMaybe<Date_Filter_Operators>;
	hammer_timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	lot_notes?: InputMaybe<String_Filter_Operators>;
	lot_number?: InputMaybe<String_Filter_Operators>;
	sale_amount_includes_premium?: InputMaybe<Boolean_Filter_Operators>;
	saleroom_notice?: InputMaybe<String_Filter_Operators>;
	starting_bid_amount?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	video_file_name?: InputMaybe<String_Filter_Operators>;
	video_hammer_time_seconds?: InputMaybe<Date_Filter_Operators>;
	video_hammer_time_seconds_func?: InputMaybe<Time_Function_Filter_Operators>;
	winning_bid?: InputMaybe<Auction_Bid_Filter>;
};

export type Auction_Lot_Mutated = {
	__typename?: 'auction_lot_mutated';
	data?: Maybe<Auction_Lot>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Mutated = {
	__typename?: 'auction_mutated';
	data?: Maybe<Auction>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Auction_Type = {
	__typename?: 'auction_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Auction_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Auction_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Auction_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Auction_Type_Mutated = {
	__typename?: 'auction_type_mutated';
	data?: Maybe<Auction_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export enum Auth_Mode {
	Cookie = 'cookie',
	Json = 'json',
	Session = 'session',
}

export type Auth_Tokens = {
	__typename?: 'auth_tokens';
	access_token?: Maybe<Scalars['String']['output']>;
	expires?: Maybe<Scalars['GraphQLBigInt']['output']>;
	refresh_token?: Maybe<Scalars['String']['output']>;
};

export type Author = {
	__typename?: 'author';
	artwork_id?: Maybe<Artwork>;
	entity_id?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	is_duplicate: Scalars['Boolean']['output'];
};

export type AuthorArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AuthorEntity_IdArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Author_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Author_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Author_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Filter>;
	entity_id?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	is_duplicate?: InputMaybe<Boolean_Filter_Operators>;
};

export type Author_Mutated = {
	__typename?: 'author_mutated';
	data?: Maybe<Author>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Award = {
	__typename?: 'award';
	artists?: Maybe<Array<Maybe<Artist_Award>>>;
	artists_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	institution?: Maybe<Organisation>;
	name: Scalars['String']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_award_created?: Maybe<Scalars['Int']['output']>;
};

export type AwardArtistsArgs = {
	filter?: InputMaybe<Artist_Award_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AwardInstitutionArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AwardStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AwardUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type AwardUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Award_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Award_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Award_Filter>>>;
	artists?: InputMaybe<Artist_Award_Filter>;
	artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	institution?: InputMaybe<Organisation_Filter>;
	name?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_award_created?: InputMaybe<Number_Filter_Operators>;
};

export type Award_Mutated = {
	__typename?: 'award_mutated';
	data?: Maybe<Award>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Big_Int_Filter_Operators = {
	_between?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_eq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nbetween?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_neq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Boolean_Filter_Operators = {
	_eq?: InputMaybe<Scalars['Boolean']['input']>;
	_neq?: InputMaybe<Scalars['Boolean']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Collection_Note = {
	__typename?: 'collection_note';
	artist?: Maybe<Artist>;
	artwork_series?: Maybe<Artwork_Series>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	/** Additional details about what the entity is interested in or owns. */
	note?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	/** The date when the collection information was received or when the info was first known. This could be different to the date created and date updated. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	type?: Maybe<Collection_Note_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Collection_NoteArtistArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteArtwork_SeriesArgs = {
	filter?: InputMaybe<Artwork_Series_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteTypeArgs = {
	filter?: InputMaybe<Collection_Note_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_NoteUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Collection_Note_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Collection_Note_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Collection_Note_Filter>>>;
	artist?: InputMaybe<Artist_Filter>;
	artwork_series?: InputMaybe<Artwork_Series_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	type?: InputMaybe<Collection_Note_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Collection_Note_Mutated = {
	__typename?: 'collection_note_mutated';
	data?: Maybe<Collection_Note>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Collection_Note_Type = {
	__typename?: 'collection_note_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Collection_Note_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Collection_Note_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Collection_Note_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Collection_Note_Type_Mutated = {
	__typename?: 'collection_note_type_mutated';
	data?: Maybe<Collection_Note_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Count_Function_Filter_Operators = {
	count?: InputMaybe<Number_Filter_Operators>;
};

export type Count_Functions = {
	__typename?: 'count_functions';
	count?: Maybe<Scalars['Int']['output']>;
};

export type Create_Directus_Collections_Fields_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	field?: InputMaybe<Scalars['String']['input']>;
	meta?: InputMaybe<Directus_Fields_Meta_Input>;
	schema?: InputMaybe<Directus_Fields_Schema_Input>;
	type?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Directus_Collections_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	fields?: InputMaybe<Array<Create_Directus_Collections_Fields_Input>>;
	meta?: InputMaybe<Directus_Collections_Meta_Input>;
	schema?: InputMaybe<Directus_Collections_Schema_Input>;
};

export type Create_Directus_Dashboards_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name: Scalars['String']['input'];
	note?: InputMaybe<Scalars['String']['input']>;
	panels?: InputMaybe<Array<InputMaybe<Create_Directus_Panels_Input>>>;
	user_created?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Fields_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	field?: InputMaybe<Scalars['String']['input']>;
	meta?: InputMaybe<Directus_Fields_Meta_Input>;
	schema?: InputMaybe<Directus_Fields_Schema_Input>;
	type?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Directus_Files_Input = {
	charset?: InputMaybe<Scalars['String']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	duration?: InputMaybe<Scalars['Int']['input']>;
	embed?: InputMaybe<Scalars['String']['input']>;
	filename_disk?: InputMaybe<Scalars['String']['input']>;
	filename_download: Scalars['String']['input'];
	filesize?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	focal_point_x?: InputMaybe<Scalars['Int']['input']>;
	focal_point_y?: InputMaybe<Scalars['Int']['input']>;
	folder?: InputMaybe<Create_Directus_Folders_Input>;
	height?: InputMaybe<Scalars['Int']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	metadata?: InputMaybe<Scalars['JSON']['input']>;
	modified_by?: InputMaybe<Create_Directus_Users_Input>;
	modified_on?: InputMaybe<Scalars['Date']['input']>;
	storage: Scalars['String']['input'];
	tags?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	tus_data?: InputMaybe<Scalars['JSON']['input']>;
	tus_id?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	uploaded_by?: InputMaybe<Create_Directus_Users_Input>;
	uploaded_on?: InputMaybe<Scalars['Date']['input']>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Create_Directus_Flows_Input = {
	accountability?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name: Scalars['String']['input'];
	operation?: InputMaybe<Create_Directus_Operations_Input>;
	operations?: InputMaybe<Array<InputMaybe<Create_Directus_Operations_Input>>>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	trigger?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Folders_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	name: Scalars['String']['input'];
	parent?: InputMaybe<Create_Directus_Folders_Input>;
};

export type Create_Directus_Notifications_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item?: InputMaybe<Scalars['String']['input']>;
	message?: InputMaybe<Scalars['String']['input']>;
	recipient?: InputMaybe<Create_Directus_Users_Input>;
	sender?: InputMaybe<Create_Directus_Users_Input>;
	status?: InputMaybe<Scalars['String']['input']>;
	subject: Scalars['String']['input'];
	timestamp?: InputMaybe<Scalars['Date']['input']>;
};

export type Create_Directus_Operations_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	flow?: InputMaybe<Create_Directus_Flows_Input>;
	id?: InputMaybe<Scalars['ID']['input']>;
	key: Scalars['String']['input'];
	name?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x: Scalars['Int']['input'];
	position_y: Scalars['Int']['input'];
	reject?: InputMaybe<Create_Directus_Operations_Input>;
	resolve?: InputMaybe<Create_Directus_Operations_Input>;
	type: Scalars['String']['input'];
	user_created?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Panels_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	dashboard?: InputMaybe<Create_Directus_Dashboards_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	height: Scalars['Int']['input'];
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x: Scalars['Int']['input'];
	position_y: Scalars['Int']['input'];
	show_header: Scalars['Boolean']['input'];
	type: Scalars['String']['input'];
	user_created?: InputMaybe<Create_Directus_Users_Input>;
	width: Scalars['Int']['input'];
};

export type Create_Directus_Permissions_Input = {
	action: Scalars['String']['input'];
	collection: Scalars['String']['input'];
	fields?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	id?: InputMaybe<Scalars['ID']['input']>;
	permissions?: InputMaybe<Scalars['JSON']['input']>;
	presets?: InputMaybe<Scalars['JSON']['input']>;
	role?: InputMaybe<Create_Directus_Roles_Input>;
	validation?: InputMaybe<Scalars['JSON']['input']>;
};

export type Create_Directus_Presets_Input = {
	bookmark?: InputMaybe<Scalars['String']['input']>;
	collection?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	filter?: InputMaybe<Scalars['JSON']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	layout?: InputMaybe<Scalars['String']['input']>;
	layout_options?: InputMaybe<Scalars['JSON']['input']>;
	layout_query?: InputMaybe<Scalars['JSON']['input']>;
	refresh_interval?: InputMaybe<Scalars['Int']['input']>;
	role?: InputMaybe<Create_Directus_Roles_Input>;
	search?: InputMaybe<Scalars['String']['input']>;
	user?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Relations_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	field?: InputMaybe<Scalars['String']['input']>;
	meta?: InputMaybe<Directus_Relations_Meta_Input>;
	related_collection?: InputMaybe<Scalars['String']['input']>;
	schema?: InputMaybe<Directus_Relations_Schema_Input>;
};

export type Create_Directus_Roles_Input = {
	admin_access: Scalars['Boolean']['input'];
	app_access?: InputMaybe<Scalars['Boolean']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	enforce_tfa: Scalars['Boolean']['input'];
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	ip_access?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	name: Scalars['String']['input'];
	users?: InputMaybe<Array<InputMaybe<Create_Directus_Users_Input>>>;
};

export type Create_Directus_Shares_Input = {
	collection: Scalars['String']['input'];
	date_created?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item: Scalars['String']['input'];
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: InputMaybe<Scalars['Int']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: InputMaybe<Scalars['Hash']['input']>;
	role?: InputMaybe<Create_Directus_Roles_Input>;
	times_used?: InputMaybe<Scalars['Int']['input']>;
	user_created?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Translations_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	key: Scalars['String']['input'];
	language: Scalars['String']['input'];
	value: Scalars['String']['input'];
};

export type Create_Directus_Users_Input = {
	appearance?: InputMaybe<Scalars['String']['input']>;
	auth_data?: InputMaybe<Scalars['JSON']['input']>;
	avatar?: InputMaybe<Create_Directus_Files_Input>;
	description?: InputMaybe<Scalars['String']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	email_notifications?: InputMaybe<Scalars['Boolean']['input']>;
	external_identifier?: InputMaybe<Scalars['String']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	language?: InputMaybe<Scalars['String']['input']>;
	last_access?: InputMaybe<Scalars['Date']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	last_page?: InputMaybe<Scalars['String']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	password?: InputMaybe<Scalars['Hash']['input']>;
	provider?: InputMaybe<Scalars['String']['input']>;
	role?: InputMaybe<Create_Directus_Roles_Input>;
	status?: InputMaybe<Scalars['String']['input']>;
	tags?: InputMaybe<Scalars['JSON']['input']>;
	tfa_secret?: InputMaybe<Scalars['Hash']['input']>;
	theme_dark?: InputMaybe<Scalars['String']['input']>;
	theme_dark_overrides?: InputMaybe<Scalars['JSON']['input']>;
	theme_light?: InputMaybe<Scalars['String']['input']>;
	theme_light_overrides?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	token?: InputMaybe<Scalars['Hash']['input']>;
};

export type Create_Directus_Versions_Input = {
	collection: Scalars['String']['input'];
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	hash?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item: Scalars['String']['input'];
	key: Scalars['String']['input'];
	name?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Create_Directus_Users_Input>;
	user_updated?: InputMaybe<Create_Directus_Users_Input>;
};

export type Create_Directus_Webhooks_Input = {
	actions: Array<InputMaybe<Scalars['String']['input']>>;
	collections: Array<InputMaybe<Scalars['String']['input']>>;
	data?: InputMaybe<Scalars['Boolean']['input']>;
	headers?: InputMaybe<Scalars['JSON']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	method?: InputMaybe<Scalars['String']['input']>;
	migrated_flow?: InputMaybe<Create_Directus_Flows_Input>;
	name: Scalars['String']['input'];
	status?: InputMaybe<Scalars['String']['input']>;
	url: Scalars['String']['input'];
	was_active_before_deprecation: Scalars['Boolean']['input'];
};

export type Currency = {
	__typename?: 'currency';
	code: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	symbol?: Maybe<Scalars['String']['output']>;
};

export type Currency_Amount = {
	__typename?: 'currency_amount';
	amount?: Maybe<Scalars['Float']['output']>;
	/** The timestamp when the local amount, in local currency, was converted to USD. */
	conversion_timestamp: Scalars['Date']['output'];
	conversion_timestamp_func?: Maybe<Datetime_Functions>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	/** The amount converted from the specified currency into USD. */
	usd_amount: Scalars['Float']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Currency_AmountCurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Currency_AmountUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Currency_AmountUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Currency_Amount_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Currency_Amount_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Currency_Amount_Filter>>>;
	amount?: InputMaybe<Number_Filter_Operators>;
	conversion_timestamp?: InputMaybe<Date_Filter_Operators>;
	conversion_timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	currency?: InputMaybe<Currency_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	usd_amount?: InputMaybe<Number_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Currency_Amount_Mutated = {
	__typename?: 'currency_amount_mutated';
	data?: Maybe<Currency_Amount>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Currency_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Currency_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Currency_Filter>>>;
	code?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	symbol?: InputMaybe<String_Filter_Operators>;
};

export type Currency_Mutated = {
	__typename?: 'currency_mutated';
	data?: Maybe<Currency>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Currency_Rate = {
	__typename?: 'currency_rate';
	currency?: Maybe<Currency>;
	id: Scalars['ID']['output'];
	rate_usd: Scalars['Float']['output'];
	timestamp: Scalars['Date']['output'];
	timestamp_func?: Maybe<Datetime_Functions>;
};

export type Currency_RateCurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Currency_Rate_Mutated = {
	__typename?: 'currency_rate_mutated';
	data?: Maybe<Currency_Rate>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Date_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_gt?: InputMaybe<Scalars['String']['input']>;
	_gte?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_lt?: InputMaybe<Scalars['String']['input']>;
	_lte?: InputMaybe<Scalars['String']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Datetime_Function_Filter_Operators = {
	day?: InputMaybe<Number_Filter_Operators>;
	hour?: InputMaybe<Number_Filter_Operators>;
	minute?: InputMaybe<Number_Filter_Operators>;
	month?: InputMaybe<Number_Filter_Operators>;
	second?: InputMaybe<Number_Filter_Operators>;
	week?: InputMaybe<Number_Filter_Operators>;
	weekday?: InputMaybe<Number_Filter_Operators>;
	year?: InputMaybe<Number_Filter_Operators>;
};

export type Datetime_Functions = {
	__typename?: 'datetime_functions';
	day?: Maybe<Scalars['Int']['output']>;
	hour?: Maybe<Scalars['Int']['output']>;
	minute?: Maybe<Scalars['Int']['output']>;
	month?: Maybe<Scalars['Int']['output']>;
	second?: Maybe<Scalars['Int']['output']>;
	week?: Maybe<Scalars['Int']['output']>;
	weekday?: Maybe<Scalars['Int']['output']>;
	year?: Maybe<Scalars['Int']['output']>;
};

export type Delete_Collection = {
	__typename?: 'delete_collection';
	collection?: Maybe<Scalars['String']['output']>;
};

export type Delete_Field = {
	__typename?: 'delete_field';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
};

export type Delete_Many = {
	__typename?: 'delete_many';
	ids: Array<Maybe<Scalars['ID']['output']>>;
};

export type Delete_One = {
	__typename?: 'delete_one';
	id: Scalars['ID']['output'];
};

export type Delete_Relation = {
	__typename?: 'delete_relation';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
};

export type Directus_Activity = {
	__typename?: 'directus_activity';
	action: Scalars['String']['output'];
	collection: Scalars['String']['output'];
	comment?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	ip?: Maybe<Scalars['String']['output']>;
	item: Scalars['String']['output'];
	origin?: Maybe<Scalars['String']['output']>;
	revisions?: Maybe<Array<Maybe<Directus_Revisions>>>;
	revisions_func?: Maybe<Count_Functions>;
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user?: Maybe<Directus_Users>;
	user_agent?: Maybe<Scalars['String']['output']>;
};

export type Directus_ActivityRevisionsArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_ActivityUserArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Activity_Aggregated = {
	__typename?: 'directus_activity_aggregated';
	avg?: Maybe<Directus_Activity_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Activity_Aggregated_Fields>;
	count?: Maybe<Directus_Activity_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Activity_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Activity_Aggregated_Fields>;
	min?: Maybe<Directus_Activity_Aggregated_Fields>;
	sum?: Maybe<Directus_Activity_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Activity_Aggregated_Fields>;
};

export type Directus_Activity_Aggregated_Count = {
	__typename?: 'directus_activity_aggregated_count';
	action?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	comment?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	ip?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	origin?: Maybe<Scalars['Int']['output']>;
	revisions?: Maybe<Scalars['Int']['output']>;
	timestamp?: Maybe<Scalars['Int']['output']>;
	user?: Maybe<Scalars['Int']['output']>;
	user_agent?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Activity_Aggregated_Fields = {
	__typename?: 'directus_activity_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Activity_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Activity_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Activity_Filter>>>;
	action?: InputMaybe<String_Filter_Operators>;
	collection?: InputMaybe<String_Filter_Operators>;
	comment?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	ip?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	origin?: InputMaybe<String_Filter_Operators>;
	revisions?: InputMaybe<Directus_Revisions_Filter>;
	revisions_func?: InputMaybe<Count_Function_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user?: InputMaybe<Directus_Users_Filter>;
	user_agent?: InputMaybe<String_Filter_Operators>;
};

export type Directus_Activity_Mutated = {
	__typename?: 'directus_activity_mutated';
	data?: Maybe<Directus_Activity>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Collections = {
	__typename?: 'directus_collections';
	collection?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Collections_Meta>;
	schema?: Maybe<Directus_Collections_Schema>;
};

export type Directus_Collections_Meta = {
	__typename?: 'directus_collections_meta';
	accountability?: Maybe<Scalars['String']['output']>;
	archive_app_filter: Scalars['Boolean']['output'];
	archive_field?: Maybe<Scalars['String']['output']>;
	archive_value?: Maybe<Scalars['String']['output']>;
	collapse: Scalars['String']['output'];
	collection: Scalars['String']['output'];
	color?: Maybe<Scalars['String']['output']>;
	display_template?: Maybe<Scalars['String']['output']>;
	group?: Maybe<Scalars['String']['output']>;
	hidden: Scalars['Boolean']['output'];
	icon?: Maybe<Scalars['String']['output']>;
	item_duplication_fields?: Maybe<Scalars['JSON']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	preview_url?: Maybe<Scalars['String']['output']>;
	singleton: Scalars['Boolean']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	sort_field?: Maybe<Scalars['String']['output']>;
	translations?: Maybe<Scalars['JSON']['output']>;
	unarchive_value?: Maybe<Scalars['String']['output']>;
	versioning: Scalars['Boolean']['output'];
};

export type Directus_Collections_Meta_Input = {
	accountability?: InputMaybe<Scalars['String']['input']>;
	archive_app_filter: Scalars['Boolean']['input'];
	archive_field?: InputMaybe<Scalars['String']['input']>;
	archive_value?: InputMaybe<Scalars['String']['input']>;
	collapse: Scalars['String']['input'];
	collection: Scalars['String']['input'];
	color?: InputMaybe<Scalars['String']['input']>;
	display_template?: InputMaybe<Scalars['String']['input']>;
	group?: InputMaybe<Scalars['String']['input']>;
	hidden: Scalars['Boolean']['input'];
	icon?: InputMaybe<Scalars['String']['input']>;
	item_duplication_fields?: InputMaybe<Scalars['JSON']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	preview_url?: InputMaybe<Scalars['String']['input']>;
	singleton: Scalars['Boolean']['input'];
	sort?: InputMaybe<Scalars['Int']['input']>;
	sort_field?: InputMaybe<Scalars['String']['input']>;
	translations?: InputMaybe<Scalars['JSON']['input']>;
	unarchive_value?: InputMaybe<Scalars['String']['input']>;
	versioning: Scalars['Boolean']['input'];
};

export type Directus_Collections_Schema = {
	__typename?: 'directus_collections_schema';
	comment?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
};

export type Directus_Collections_Schema_Input = {
	comment?: InputMaybe<Scalars['String']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
};

export type Directus_Dashboards = {
	__typename?: 'directus_dashboards';
	color?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	note?: Maybe<Scalars['String']['output']>;
	panels?: Maybe<Array<Maybe<Directus_Panels>>>;
	panels_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
};

export type Directus_DashboardsPanelsArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_DashboardsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Dashboards_Aggregated = {
	__typename?: 'directus_dashboards_aggregated';
	count?: Maybe<Directus_Dashboards_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Dashboards_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Dashboards_Aggregated_Count = {
	__typename?: 'directus_dashboards_aggregated_count';
	color?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	note?: Maybe<Scalars['Int']['output']>;
	panels?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Dashboards_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Dashboards_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Dashboards_Filter>>>;
	color?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	panels?: InputMaybe<Directus_Panels_Filter>;
	panels_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Dashboards_Mutated = {
	__typename?: 'directus_dashboards_mutated';
	data?: Maybe<Directus_Dashboards>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Extensions = {
	__typename?: 'directus_extensions';
	bundle?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Extensions_Meta>;
	name: Scalars['String']['output'];
	schema?: Maybe<Directus_Extensions_Schema>;
};

export type Directus_Extensions_Meta = {
	__typename?: 'directus_extensions_meta';
	enabled?: Maybe<Scalars['Boolean']['output']>;
};

export type Directus_Extensions_Schema = {
	__typename?: 'directus_extensions_schema';
	local?: Maybe<Scalars['Boolean']['output']>;
	type?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields = {
	__typename?: 'directus_fields';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Fields_Meta>;
	schema?: Maybe<Directus_Fields_Schema>;
	type?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields_Meta = {
	__typename?: 'directus_fields_meta';
	collection: Scalars['String']['output'];
	conditions?: Maybe<Scalars['JSON']['output']>;
	display?: Maybe<Scalars['String']['output']>;
	display_options?: Maybe<Scalars['JSON']['output']>;
	field: Scalars['String']['output'];
	group?: Maybe<Scalars['String']['output']>;
	hidden: Scalars['Boolean']['output'];
	id: Scalars['Int']['output'];
	interface?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	options?: Maybe<Scalars['JSON']['output']>;
	readonly: Scalars['Boolean']['output'];
	required?: Maybe<Scalars['Boolean']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	special?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	translations?: Maybe<Scalars['JSON']['output']>;
	validation?: Maybe<Scalars['JSON']['output']>;
	validation_message?: Maybe<Scalars['String']['output']>;
	width?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields_Meta_Input = {
	collection: Scalars['String']['input'];
	conditions?: InputMaybe<Scalars['JSON']['input']>;
	display?: InputMaybe<Scalars['String']['input']>;
	display_options?: InputMaybe<Scalars['JSON']['input']>;
	field: Scalars['String']['input'];
	group?: InputMaybe<Scalars['String']['input']>;
	hidden: Scalars['Boolean']['input'];
	id: Scalars['Int']['input'];
	interface?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	readonly: Scalars['Boolean']['input'];
	required?: InputMaybe<Scalars['Boolean']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	special?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	translations?: InputMaybe<Scalars['JSON']['input']>;
	validation?: InputMaybe<Scalars['JSON']['input']>;
	validation_message?: InputMaybe<Scalars['String']['input']>;
	width?: InputMaybe<Scalars['String']['input']>;
};

export type Directus_Fields_Schema = {
	__typename?: 'directus_fields_schema';
	comment?: Maybe<Scalars['String']['output']>;
	data_type?: Maybe<Scalars['String']['output']>;
	default_value?: Maybe<Scalars['String']['output']>;
	foreign_key_column?: Maybe<Scalars['String']['output']>;
	foreign_key_table?: Maybe<Scalars['String']['output']>;
	has_auto_increment?: Maybe<Scalars['Boolean']['output']>;
	is_nullable?: Maybe<Scalars['Boolean']['output']>;
	is_primary_key?: Maybe<Scalars['Boolean']['output']>;
	is_unique?: Maybe<Scalars['Boolean']['output']>;
	max_length?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	numeric_precision?: Maybe<Scalars['Int']['output']>;
	numeric_scale?: Maybe<Scalars['Int']['output']>;
	table?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields_Schema_Input = {
	comment?: InputMaybe<Scalars['String']['input']>;
	data_type?: InputMaybe<Scalars['String']['input']>;
	default_value?: InputMaybe<Scalars['String']['input']>;
	foreign_key_column?: InputMaybe<Scalars['String']['input']>;
	foreign_key_table?: InputMaybe<Scalars['String']['input']>;
	has_auto_increment?: InputMaybe<Scalars['Boolean']['input']>;
	is_nullable?: InputMaybe<Scalars['Boolean']['input']>;
	is_primary_key?: InputMaybe<Scalars['Boolean']['input']>;
	is_unique?: InputMaybe<Scalars['Boolean']['input']>;
	max_length?: InputMaybe<Scalars['Int']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	numeric_precision?: InputMaybe<Scalars['Int']['input']>;
	numeric_scale?: InputMaybe<Scalars['Int']['input']>;
	table?: InputMaybe<Scalars['String']['input']>;
};

export type Directus_Files = {
	__typename?: 'directus_files';
	charset?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	duration?: Maybe<Scalars['Int']['output']>;
	embed?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	filename_download: Scalars['String']['output'];
	filesize?: Maybe<Scalars['GraphQLBigInt']['output']>;
	focal_point_x?: Maybe<Scalars['Int']['output']>;
	focal_point_y?: Maybe<Scalars['Int']['output']>;
	folder?: Maybe<Directus_Folders>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	metadata?: Maybe<Scalars['JSON']['output']>;
	metadata_func?: Maybe<Count_Functions>;
	modified_by?: Maybe<Directus_Users>;
	modified_on?: Maybe<Scalars['Date']['output']>;
	modified_on_func?: Maybe<Datetime_Functions>;
	storage: Scalars['String']['output'];
	tags?: Maybe<Scalars['JSON']['output']>;
	tags_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	tus_data?: Maybe<Scalars['JSON']['output']>;
	tus_data_func?: Maybe<Count_Functions>;
	tus_id?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	uploaded_by?: Maybe<Directus_Users>;
	uploaded_on?: Maybe<Scalars['Date']['output']>;
	uploaded_on_func?: Maybe<Datetime_Functions>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_FilesFolderArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FilesModified_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FilesUploaded_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Files_Aggregated = {
	__typename?: 'directus_files_aggregated';
	avg?: Maybe<Directus_Files_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Files_Aggregated_Fields>;
	count?: Maybe<Directus_Files_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Files_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Files_Aggregated_Fields>;
	min?: Maybe<Directus_Files_Aggregated_Fields>;
	sum?: Maybe<Directus_Files_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Files_Aggregated_Fields>;
};

export type Directus_Files_Aggregated_Count = {
	__typename?: 'directus_files_aggregated_count';
	charset?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	duration?: Maybe<Scalars['Int']['output']>;
	embed?: Maybe<Scalars['Int']['output']>;
	filename_disk?: Maybe<Scalars['Int']['output']>;
	filename_download?: Maybe<Scalars['Int']['output']>;
	filesize?: Maybe<Scalars['Int']['output']>;
	focal_point_x?: Maybe<Scalars['Int']['output']>;
	focal_point_y?: Maybe<Scalars['Int']['output']>;
	folder?: Maybe<Scalars['Int']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	location?: Maybe<Scalars['Int']['output']>;
	metadata?: Maybe<Scalars['Int']['output']>;
	modified_by?: Maybe<Scalars['Int']['output']>;
	modified_on?: Maybe<Scalars['Int']['output']>;
	storage?: Maybe<Scalars['Int']['output']>;
	tags?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	tus_data?: Maybe<Scalars['Int']['output']>;
	tus_id?: Maybe<Scalars['Int']['output']>;
	type?: Maybe<Scalars['Int']['output']>;
	uploaded_by?: Maybe<Scalars['Int']['output']>;
	uploaded_on?: Maybe<Scalars['Int']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Files_Aggregated_Fields = {
	__typename?: 'directus_files_aggregated_fields';
	duration?: Maybe<Scalars['Float']['output']>;
	filesize?: Maybe<Scalars['Float']['output']>;
	focal_point_x?: Maybe<Scalars['Float']['output']>;
	focal_point_y?: Maybe<Scalars['Float']['output']>;
	height?: Maybe<Scalars['Float']['output']>;
	width?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Files_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	charset?: InputMaybe<String_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	duration?: InputMaybe<Number_Filter_Operators>;
	embed?: InputMaybe<String_Filter_Operators>;
	filename_disk?: InputMaybe<String_Filter_Operators>;
	filename_download?: InputMaybe<String_Filter_Operators>;
	filesize?: InputMaybe<Big_Int_Filter_Operators>;
	focal_point_x?: InputMaybe<Number_Filter_Operators>;
	focal_point_y?: InputMaybe<Number_Filter_Operators>;
	folder?: InputMaybe<Directus_Folders_Filter>;
	height?: InputMaybe<Number_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	metadata?: InputMaybe<String_Filter_Operators>;
	metadata_func?: InputMaybe<Count_Function_Filter_Operators>;
	modified_by?: InputMaybe<Directus_Users_Filter>;
	modified_on?: InputMaybe<Date_Filter_Operators>;
	modified_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	storage?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	tus_data?: InputMaybe<String_Filter_Operators>;
	tus_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	tus_id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	uploaded_by?: InputMaybe<Directus_Users_Filter>;
	uploaded_on?: InputMaybe<Date_Filter_Operators>;
	uploaded_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Files_Mutated = {
	__typename?: 'directus_files_mutated';
	data?: Maybe<Directus_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Flows = {
	__typename?: 'directus_flows';
	accountability?: Maybe<Scalars['String']['output']>;
	color?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	operation?: Maybe<Directus_Operations>;
	operations?: Maybe<Array<Maybe<Directus_Operations>>>;
	operations_func?: Maybe<Count_Functions>;
	options?: Maybe<Scalars['JSON']['output']>;
	options_func?: Maybe<Count_Functions>;
	status?: Maybe<Scalars['String']['output']>;
	trigger?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
};

export type Directus_FlowsOperationArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FlowsOperationsArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FlowsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Flows_Aggregated = {
	__typename?: 'directus_flows_aggregated';
	count?: Maybe<Directus_Flows_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Flows_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Flows_Aggregated_Count = {
	__typename?: 'directus_flows_aggregated_count';
	accountability?: Maybe<Scalars['Int']['output']>;
	color?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	operation?: Maybe<Scalars['Int']['output']>;
	operations?: Maybe<Scalars['Int']['output']>;
	options?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	trigger?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Flows_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Flows_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Flows_Filter>>>;
	accountability?: InputMaybe<String_Filter_Operators>;
	color?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	operation?: InputMaybe<Directus_Operations_Filter>;
	operations?: InputMaybe<Directus_Operations_Filter>;
	operations_func?: InputMaybe<Count_Function_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	trigger?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Flows_Mutated = {
	__typename?: 'directus_flows_mutated';
	data?: Maybe<Directus_Flows>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Folders = {
	__typename?: 'directus_folders';
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	parent?: Maybe<Directus_Folders>;
};

export type Directus_FoldersParentArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Folders_Aggregated = {
	__typename?: 'directus_folders_aggregated';
	count?: Maybe<Directus_Folders_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Folders_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Folders_Aggregated_Count = {
	__typename?: 'directus_folders_aggregated_count';
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	parent?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Folders_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Folders_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Folders_Filter>>>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Folders_Filter>;
};

export type Directus_Folders_Mutated = {
	__typename?: 'directus_folders_mutated';
	data?: Maybe<Directus_Folders>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Notifications = {
	__typename?: 'directus_notifications';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Scalars['String']['output']>;
	message?: Maybe<Scalars['String']['output']>;
	recipient?: Maybe<Directus_Users>;
	sender?: Maybe<Directus_Users>;
	status?: Maybe<Scalars['String']['output']>;
	subject: Scalars['String']['output'];
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
};

export type Directus_NotificationsRecipientArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_NotificationsSenderArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Notifications_Aggregated = {
	__typename?: 'directus_notifications_aggregated';
	avg?: Maybe<Directus_Notifications_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Notifications_Aggregated_Fields>;
	count?: Maybe<Directus_Notifications_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Notifications_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Notifications_Aggregated_Fields>;
	min?: Maybe<Directus_Notifications_Aggregated_Fields>;
	sum?: Maybe<Directus_Notifications_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Notifications_Aggregated_Fields>;
};

export type Directus_Notifications_Aggregated_Count = {
	__typename?: 'directus_notifications_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	message?: Maybe<Scalars['Int']['output']>;
	recipient?: Maybe<Scalars['Int']['output']>;
	sender?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	subject?: Maybe<Scalars['Int']['output']>;
	timestamp?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Notifications_Aggregated_Fields = {
	__typename?: 'directus_notifications_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Notifications_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Notifications_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Notifications_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	message?: InputMaybe<String_Filter_Operators>;
	recipient?: InputMaybe<Directus_Users_Filter>;
	sender?: InputMaybe<Directus_Users_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	subject?: InputMaybe<String_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
};

export type Directus_Notifications_Mutated = {
	__typename?: 'directus_notifications_mutated';
	data?: Maybe<Directus_Notifications>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Operations = {
	__typename?: 'directus_operations';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	flow?: Maybe<Directus_Flows>;
	id: Scalars['ID']['output'];
	key: Scalars['String']['output'];
	name?: Maybe<Scalars['String']['output']>;
	options?: Maybe<Scalars['JSON']['output']>;
	options_func?: Maybe<Count_Functions>;
	position_x: Scalars['Int']['output'];
	position_y: Scalars['Int']['output'];
	reject?: Maybe<Directus_Operations>;
	resolve?: Maybe<Directus_Operations>;
	type: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
};

export type Directus_OperationsFlowArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_OperationsRejectArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_OperationsResolveArgs = {
	filter?: InputMaybe<Directus_Operations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_OperationsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Operations_Aggregated = {
	__typename?: 'directus_operations_aggregated';
	avg?: Maybe<Directus_Operations_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Operations_Aggregated_Fields>;
	count?: Maybe<Directus_Operations_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Operations_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Operations_Aggregated_Fields>;
	min?: Maybe<Directus_Operations_Aggregated_Fields>;
	sum?: Maybe<Directus_Operations_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Operations_Aggregated_Fields>;
};

export type Directus_Operations_Aggregated_Count = {
	__typename?: 'directus_operations_aggregated_count';
	date_created?: Maybe<Scalars['Int']['output']>;
	flow?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	key?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	options?: Maybe<Scalars['Int']['output']>;
	position_x?: Maybe<Scalars['Int']['output']>;
	position_y?: Maybe<Scalars['Int']['output']>;
	reject?: Maybe<Scalars['Int']['output']>;
	resolve?: Maybe<Scalars['Int']['output']>;
	type?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Operations_Aggregated_Fields = {
	__typename?: 'directus_operations_aggregated_fields';
	position_x?: Maybe<Scalars['Float']['output']>;
	position_y?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Operations_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Operations_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Operations_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	flow?: InputMaybe<Directus_Flows_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	position_x?: InputMaybe<Number_Filter_Operators>;
	position_y?: InputMaybe<Number_Filter_Operators>;
	reject?: InputMaybe<Directus_Operations_Filter>;
	resolve?: InputMaybe<Directus_Operations_Filter>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Operations_Mutated = {
	__typename?: 'directus_operations_mutated';
	data?: Maybe<Directus_Operations>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Panels = {
	__typename?: 'directus_panels';
	color?: Maybe<Scalars['String']['output']>;
	dashboard?: Maybe<Directus_Dashboards>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	height: Scalars['Int']['output'];
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	options?: Maybe<Scalars['JSON']['output']>;
	options_func?: Maybe<Count_Functions>;
	position_x: Scalars['Int']['output'];
	position_y: Scalars['Int']['output'];
	show_header: Scalars['Boolean']['output'];
	type: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	width: Scalars['Int']['output'];
};

export type Directus_PanelsDashboardArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_PanelsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Panels_Aggregated = {
	__typename?: 'directus_panels_aggregated';
	avg?: Maybe<Directus_Panels_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Panels_Aggregated_Fields>;
	count?: Maybe<Directus_Panels_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Panels_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Panels_Aggregated_Fields>;
	min?: Maybe<Directus_Panels_Aggregated_Fields>;
	sum?: Maybe<Directus_Panels_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Panels_Aggregated_Fields>;
};

export type Directus_Panels_Aggregated_Count = {
	__typename?: 'directus_panels_aggregated_count';
	color?: Maybe<Scalars['Int']['output']>;
	dashboard?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	note?: Maybe<Scalars['Int']['output']>;
	options?: Maybe<Scalars['Int']['output']>;
	position_x?: Maybe<Scalars['Int']['output']>;
	position_y?: Maybe<Scalars['Int']['output']>;
	show_header?: Maybe<Scalars['Int']['output']>;
	type?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Panels_Aggregated_Fields = {
	__typename?: 'directus_panels_aggregated_fields';
	height?: Maybe<Scalars['Float']['output']>;
	position_x?: Maybe<Scalars['Float']['output']>;
	position_y?: Maybe<Scalars['Float']['output']>;
	width?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Panels_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	color?: InputMaybe<String_Filter_Operators>;
	dashboard?: InputMaybe<Directus_Dashboards_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	height?: InputMaybe<Number_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	position_x?: InputMaybe<Number_Filter_Operators>;
	position_y?: InputMaybe<Number_Filter_Operators>;
	show_header?: InputMaybe<Boolean_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Panels_Mutated = {
	__typename?: 'directus_panels_mutated';
	data?: Maybe<Directus_Panels>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Permissions = {
	__typename?: 'directus_permissions';
	action: Scalars['String']['output'];
	collection: Scalars['String']['output'];
	fields?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	id?: Maybe<Scalars['ID']['output']>;
	permissions?: Maybe<Scalars['JSON']['output']>;
	permissions_func?: Maybe<Count_Functions>;
	presets?: Maybe<Scalars['JSON']['output']>;
	presets_func?: Maybe<Count_Functions>;
	role?: Maybe<Directus_Roles>;
	validation?: Maybe<Scalars['JSON']['output']>;
	validation_func?: Maybe<Count_Functions>;
};

export type Directus_PermissionsRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Permissions_Aggregated = {
	__typename?: 'directus_permissions_aggregated';
	avg?: Maybe<Directus_Permissions_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Permissions_Aggregated_Fields>;
	count?: Maybe<Directus_Permissions_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Permissions_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Permissions_Aggregated_Fields>;
	min?: Maybe<Directus_Permissions_Aggregated_Fields>;
	sum?: Maybe<Directus_Permissions_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Permissions_Aggregated_Fields>;
};

export type Directus_Permissions_Aggregated_Count = {
	__typename?: 'directus_permissions_aggregated_count';
	action?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	fields?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	permissions?: Maybe<Scalars['Int']['output']>;
	presets?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	validation?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Permissions_Aggregated_Fields = {
	__typename?: 'directus_permissions_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Permissions_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Permissions_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Permissions_Filter>>>;
	action?: InputMaybe<String_Filter_Operators>;
	collection?: InputMaybe<String_Filter_Operators>;
	fields?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	permissions?: InputMaybe<String_Filter_Operators>;
	permissions_func?: InputMaybe<Count_Function_Filter_Operators>;
	presets?: InputMaybe<String_Filter_Operators>;
	presets_func?: InputMaybe<Count_Function_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	validation?: InputMaybe<String_Filter_Operators>;
	validation_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Directus_Permissions_Mutated = {
	__typename?: 'directus_permissions_mutated';
	data?: Maybe<Directus_Permissions>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Presets = {
	__typename?: 'directus_presets';
	bookmark?: Maybe<Scalars['String']['output']>;
	collection?: Maybe<Scalars['String']['output']>;
	color?: Maybe<Scalars['String']['output']>;
	filter?: Maybe<Scalars['JSON']['output']>;
	filter_func?: Maybe<Count_Functions>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	layout?: Maybe<Scalars['String']['output']>;
	layout_options?: Maybe<Scalars['JSON']['output']>;
	layout_options_func?: Maybe<Count_Functions>;
	layout_query?: Maybe<Scalars['JSON']['output']>;
	layout_query_func?: Maybe<Count_Functions>;
	refresh_interval?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Directus_Roles>;
	search?: Maybe<Scalars['String']['output']>;
	user?: Maybe<Directus_Users>;
};

export type Directus_PresetsRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_PresetsUserArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Presets_Aggregated = {
	__typename?: 'directus_presets_aggregated';
	avg?: Maybe<Directus_Presets_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Presets_Aggregated_Fields>;
	count?: Maybe<Directus_Presets_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Presets_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Presets_Aggregated_Fields>;
	min?: Maybe<Directus_Presets_Aggregated_Fields>;
	sum?: Maybe<Directus_Presets_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Presets_Aggregated_Fields>;
};

export type Directus_Presets_Aggregated_Count = {
	__typename?: 'directus_presets_aggregated_count';
	bookmark?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	color?: Maybe<Scalars['Int']['output']>;
	filter?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	layout?: Maybe<Scalars['Int']['output']>;
	layout_options?: Maybe<Scalars['Int']['output']>;
	layout_query?: Maybe<Scalars['Int']['output']>;
	refresh_interval?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	search?: Maybe<Scalars['Int']['output']>;
	user?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Presets_Aggregated_Fields = {
	__typename?: 'directus_presets_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
	refresh_interval?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Presets_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Presets_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Presets_Filter>>>;
	bookmark?: InputMaybe<String_Filter_Operators>;
	collection?: InputMaybe<String_Filter_Operators>;
	color?: InputMaybe<String_Filter_Operators>;
	filter?: InputMaybe<String_Filter_Operators>;
	filter_func?: InputMaybe<Count_Function_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	layout?: InputMaybe<String_Filter_Operators>;
	layout_options?: InputMaybe<String_Filter_Operators>;
	layout_options_func?: InputMaybe<Count_Function_Filter_Operators>;
	layout_query?: InputMaybe<String_Filter_Operators>;
	layout_query_func?: InputMaybe<Count_Function_Filter_Operators>;
	refresh_interval?: InputMaybe<Number_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	search?: InputMaybe<String_Filter_Operators>;
	user?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Presets_Mutated = {
	__typename?: 'directus_presets_mutated';
	data?: Maybe<Directus_Presets>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Relations = {
	__typename?: 'directus_relations';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Relations_Meta>;
	related_collection?: Maybe<Scalars['String']['output']>;
	schema?: Maybe<Directus_Relations_Schema>;
};

export type Directus_Relations_Meta = {
	__typename?: 'directus_relations_meta';
	id?: Maybe<Scalars['Int']['output']>;
	junction_field?: Maybe<Scalars['String']['output']>;
	many_collection?: Maybe<Scalars['String']['output']>;
	many_field?: Maybe<Scalars['String']['output']>;
	one_allowed_collections?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	one_collection?: Maybe<Scalars['String']['output']>;
	one_collection_field?: Maybe<Scalars['String']['output']>;
	one_deselect_action?: Maybe<Scalars['String']['output']>;
	one_field?: Maybe<Scalars['String']['output']>;
	sort_field?: Maybe<Scalars['String']['output']>;
};

export type Directus_Relations_Meta_Input = {
	id?: InputMaybe<Scalars['Int']['input']>;
	junction_field?: InputMaybe<Scalars['String']['input']>;
	many_collection?: InputMaybe<Scalars['String']['input']>;
	many_field?: InputMaybe<Scalars['String']['input']>;
	one_allowed_collections?: InputMaybe<
		Array<InputMaybe<Scalars['String']['input']>>
	>;
	one_collection?: InputMaybe<Scalars['String']['input']>;
	one_collection_field?: InputMaybe<Scalars['String']['input']>;
	one_deselect_action?: InputMaybe<Scalars['String']['input']>;
	one_field?: InputMaybe<Scalars['String']['input']>;
	sort_field?: InputMaybe<Scalars['String']['input']>;
};

export type Directus_Relations_Schema = {
	__typename?: 'directus_relations_schema';
	column: Scalars['String']['output'];
	constraint_name?: Maybe<Scalars['String']['output']>;
	foreign_key_column: Scalars['String']['output'];
	foreign_key_table: Scalars['String']['output'];
	on_delete: Scalars['String']['output'];
	on_update: Scalars['String']['output'];
	table: Scalars['String']['output'];
};

export type Directus_Relations_Schema_Input = {
	column: Scalars['String']['input'];
	constraint_name?: InputMaybe<Scalars['String']['input']>;
	foreign_key_column: Scalars['String']['input'];
	foreign_key_table: Scalars['String']['input'];
	on_delete: Scalars['String']['input'];
	on_update: Scalars['String']['input'];
	table: Scalars['String']['input'];
};

export type Directus_Revisions = {
	__typename?: 'directus_revisions';
	activity?: Maybe<Directus_Activity>;
	collection: Scalars['String']['output'];
	data?: Maybe<Scalars['JSON']['output']>;
	data_func?: Maybe<Count_Functions>;
	delta?: Maybe<Scalars['JSON']['output']>;
	delta_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	parent?: Maybe<Directus_Revisions>;
	version?: Maybe<Directus_Versions>;
};

export type Directus_RevisionsActivityArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_RevisionsParentArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_RevisionsVersionArgs = {
	filter?: InputMaybe<Directus_Versions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Revisions_Aggregated = {
	__typename?: 'directus_revisions_aggregated';
	avg?: Maybe<Directus_Revisions_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Revisions_Aggregated_Fields>;
	count?: Maybe<Directus_Revisions_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Revisions_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Revisions_Aggregated_Fields>;
	min?: Maybe<Directus_Revisions_Aggregated_Fields>;
	sum?: Maybe<Directus_Revisions_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Revisions_Aggregated_Fields>;
};

export type Directus_Revisions_Aggregated_Count = {
	__typename?: 'directus_revisions_aggregated_count';
	activity?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	data?: Maybe<Scalars['Int']['output']>;
	delta?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	parent?: Maybe<Scalars['Int']['output']>;
	version?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Revisions_Aggregated_Fields = {
	__typename?: 'directus_revisions_aggregated_fields';
	activity?: Maybe<Scalars['Float']['output']>;
	id?: Maybe<Scalars['Float']['output']>;
	parent?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Revisions_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	activity?: InputMaybe<Directus_Activity_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	data?: InputMaybe<String_Filter_Operators>;
	data_func?: InputMaybe<Count_Function_Filter_Operators>;
	delta?: InputMaybe<String_Filter_Operators>;
	delta_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Revisions_Filter>;
	version?: InputMaybe<Directus_Versions_Filter>;
};

export type Directus_Revisions_Mutated = {
	__typename?: 'directus_revisions_mutated';
	data?: Maybe<Directus_Revisions>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Roles = {
	__typename?: 'directus_roles';
	admin_access: Scalars['Boolean']['output'];
	app_access?: Maybe<Scalars['Boolean']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	enforce_tfa: Scalars['Boolean']['output'];
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	ip_access?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	name: Scalars['String']['output'];
	users?: Maybe<Array<Maybe<Directus_Users>>>;
	users_func?: Maybe<Count_Functions>;
};

export type Directus_RolesUsersArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Roles_Aggregated = {
	__typename?: 'directus_roles_aggregated';
	count?: Maybe<Directus_Roles_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Roles_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Roles_Aggregated_Count = {
	__typename?: 'directus_roles_aggregated_count';
	admin_access?: Maybe<Scalars['Int']['output']>;
	app_access?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	enforce_tfa?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	ip_access?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	users?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Roles_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	admin_access?: InputMaybe<Boolean_Filter_Operators>;
	app_access?: InputMaybe<Boolean_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	enforce_tfa?: InputMaybe<Boolean_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	ip_access?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	users?: InputMaybe<Directus_Users_Filter>;
	users_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Directus_Roles_Mutated = {
	__typename?: 'directus_roles_mutated';
	data?: Maybe<Directus_Roles>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Settings = {
	__typename?: 'directus_settings';
	auth_login_attempts?: Maybe<Scalars['Int']['output']>;
	auth_password_policy?: Maybe<Scalars['String']['output']>;
	basemaps?: Maybe<Scalars['JSON']['output']>;
	basemaps_func?: Maybe<Count_Functions>;
	custom_aspect_ratios?: Maybe<Scalars['JSON']['output']>;
	custom_aspect_ratios_func?: Maybe<Count_Functions>;
	custom_css?: Maybe<Scalars['String']['output']>;
	default_appearance?: Maybe<Scalars['String']['output']>;
	default_language?: Maybe<Scalars['String']['output']>;
	default_theme_dark?: Maybe<Scalars['String']['output']>;
	default_theme_light?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	mapbox_key?: Maybe<Scalars['String']['output']>;
	module_bar?: Maybe<Scalars['JSON']['output']>;
	module_bar_func?: Maybe<Count_Functions>;
	/** $t:field_options.directus_settings.project_color_note */
	project_color?: Maybe<Scalars['String']['output']>;
	project_descriptor?: Maybe<Scalars['String']['output']>;
	project_logo?: Maybe<Directus_Files>;
	project_name?: Maybe<Scalars['String']['output']>;
	project_url?: Maybe<Scalars['String']['output']>;
	public_background?: Maybe<Directus_Files>;
	public_favicon?: Maybe<Directus_Files>;
	public_foreground?: Maybe<Directus_Files>;
	public_note?: Maybe<Scalars['String']['output']>;
	/** $t:fields.directus_settings.public_registration_note */
	public_registration: Scalars['Boolean']['output'];
	/** $t:fields.directus_settings.public_registration_email_filter_note */
	public_registration_email_filter?: Maybe<Scalars['JSON']['output']>;
	public_registration_email_filter_func?: Maybe<Count_Functions>;
	public_registration_role?: Maybe<Directus_Roles>;
	/** $t:fields.directus_settings.public_registration_verify_email_note */
	public_registration_verify_email?: Maybe<Scalars['Boolean']['output']>;
	report_bug_url?: Maybe<Scalars['String']['output']>;
	report_error_url?: Maybe<Scalars['String']['output']>;
	report_feature_url?: Maybe<Scalars['String']['output']>;
	storage_asset_presets?: Maybe<Scalars['JSON']['output']>;
	storage_asset_presets_func?: Maybe<Count_Functions>;
	storage_asset_transform?: Maybe<Scalars['String']['output']>;
	storage_default_folder?: Maybe<Directus_Folders>;
	theme_dark_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_dark_overrides_func?: Maybe<Count_Functions>;
	theme_light_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_light_overrides_func?: Maybe<Count_Functions>;
};

export type Directus_SettingsProject_LogoArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_BackgroundArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_FaviconArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_ForegroundArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_Registration_RoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsStorage_Default_FolderArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Settings_Mutated = {
	__typename?: 'directus_settings_mutated';
	data?: Maybe<Directus_Settings>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Shares = {
	__typename?: 'directus_shares';
	collection: Scalars['String']['output'];
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: Maybe<Scalars['Date']['output']>;
	date_end_func?: Maybe<Datetime_Functions>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: Maybe<Scalars['Date']['output']>;
	date_start_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: Maybe<Scalars['Hash']['output']>;
	role?: Maybe<Directus_Roles>;
	times_used?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Directus_Users>;
};

export type Directus_SharesRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SharesUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Shares_Aggregated = {
	__typename?: 'directus_shares_aggregated';
	avg?: Maybe<Directus_Shares_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Shares_Aggregated_Fields>;
	count?: Maybe<Directus_Shares_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Shares_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Shares_Aggregated_Fields>;
	min?: Maybe<Directus_Shares_Aggregated_Fields>;
	sum?: Maybe<Directus_Shares_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Shares_Aggregated_Fields>;
};

export type Directus_Shares_Aggregated_Count = {
	__typename?: 'directus_shares_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	times_used?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Shares_Aggregated_Fields = {
	__typename?: 'directus_shares_aggregated_fields';
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Float']['output']>;
	times_used?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Shares_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Shares_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Shares_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_end?: InputMaybe<Date_Filter_Operators>;
	date_end_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_start?: InputMaybe<Date_Filter_Operators>;
	date_start_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	max_uses?: InputMaybe<Number_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	password?: InputMaybe<Hash_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	times_used?: InputMaybe<Number_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Shares_Mutated = {
	__typename?: 'directus_shares_mutated';
	data?: Maybe<Directus_Shares>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Translations = {
	__typename?: 'directus_translations';
	id: Scalars['ID']['output'];
	key: Scalars['String']['output'];
	language: Scalars['String']['output'];
	value: Scalars['String']['output'];
};

export type Directus_Translations_Aggregated = {
	__typename?: 'directus_translations_aggregated';
	count?: Maybe<Directus_Translations_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Translations_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Translations_Aggregated_Count = {
	__typename?: 'directus_translations_aggregated_count';
	id?: Maybe<Scalars['Int']['output']>;
	key?: Maybe<Scalars['Int']['output']>;
	language?: Maybe<Scalars['Int']['output']>;
	value?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Translations_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Translations_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Translations_Filter>>>;
	id?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Directus_Translations_Mutated = {
	__typename?: 'directus_translations_mutated';
	data?: Maybe<Directus_Translations>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Users = {
	__typename?: 'directus_users';
	appearance?: Maybe<Scalars['String']['output']>;
	auth_data?: Maybe<Scalars['JSON']['output']>;
	auth_data_func?: Maybe<Count_Functions>;
	avatar?: Maybe<Directus_Files>;
	description?: Maybe<Scalars['String']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	email_notifications?: Maybe<Scalars['Boolean']['output']>;
	external_identifier?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	language?: Maybe<Scalars['String']['output']>;
	last_access?: Maybe<Scalars['Date']['output']>;
	last_access_func?: Maybe<Datetime_Functions>;
	last_name?: Maybe<Scalars['String']['output']>;
	last_page?: Maybe<Scalars['String']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	password?: Maybe<Scalars['Hash']['output']>;
	provider?: Maybe<Scalars['String']['output']>;
	role?: Maybe<Directus_Roles>;
	status?: Maybe<Scalars['String']['output']>;
	tags?: Maybe<Scalars['JSON']['output']>;
	tags_func?: Maybe<Count_Functions>;
	tfa_secret?: Maybe<Scalars['Hash']['output']>;
	theme_dark?: Maybe<Scalars['String']['output']>;
	theme_dark_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_dark_overrides_func?: Maybe<Count_Functions>;
	theme_light?: Maybe<Scalars['String']['output']>;
	theme_light_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_light_overrides_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	token?: Maybe<Scalars['Hash']['output']>;
};

export type Directus_UsersAvatarArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_UsersRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Users_Aggregated = {
	__typename?: 'directus_users_aggregated';
	count?: Maybe<Directus_Users_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Users_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Users_Aggregated_Count = {
	__typename?: 'directus_users_aggregated_count';
	appearance?: Maybe<Scalars['Int']['output']>;
	auth_data?: Maybe<Scalars['Int']['output']>;
	avatar?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	email?: Maybe<Scalars['Int']['output']>;
	email_notifications?: Maybe<Scalars['Int']['output']>;
	external_identifier?: Maybe<Scalars['Int']['output']>;
	first_name?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	language?: Maybe<Scalars['Int']['output']>;
	last_access?: Maybe<Scalars['Int']['output']>;
	last_name?: Maybe<Scalars['Int']['output']>;
	last_page?: Maybe<Scalars['Int']['output']>;
	location?: Maybe<Scalars['Int']['output']>;
	password?: Maybe<Scalars['Int']['output']>;
	provider?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	tags?: Maybe<Scalars['Int']['output']>;
	tfa_secret?: Maybe<Scalars['Int']['output']>;
	theme_dark?: Maybe<Scalars['Int']['output']>;
	theme_dark_overrides?: Maybe<Scalars['Int']['output']>;
	theme_light?: Maybe<Scalars['Int']['output']>;
	theme_light_overrides?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	token?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Users_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	appearance?: InputMaybe<String_Filter_Operators>;
	auth_data?: InputMaybe<String_Filter_Operators>;
	auth_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	avatar?: InputMaybe<Directus_Files_Filter>;
	description?: InputMaybe<String_Filter_Operators>;
	email?: InputMaybe<String_Filter_Operators>;
	email_notifications?: InputMaybe<Boolean_Filter_Operators>;
	external_identifier?: InputMaybe<String_Filter_Operators>;
	first_name?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	last_access?: InputMaybe<Date_Filter_Operators>;
	last_access_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	last_name?: InputMaybe<String_Filter_Operators>;
	last_page?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	password?: InputMaybe<Hash_Filter_Operators>;
	provider?: InputMaybe<String_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	tfa_secret?: InputMaybe<Hash_Filter_Operators>;
	theme_dark?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	theme_light?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	token?: InputMaybe<Hash_Filter_Operators>;
};

export type Directus_Users_Mutated = {
	__typename?: 'directus_users_mutated';
	data?: Maybe<Directus_Users>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Versions = {
	__typename?: 'directus_versions';
	collection: Scalars['String']['output'];
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	hash?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	key: Scalars['String']['output'];
	name?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Directus_VersionsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_VersionsUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Versions_Aggregated = {
	__typename?: 'directus_versions_aggregated';
	count?: Maybe<Directus_Versions_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Versions_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Versions_Aggregated_Count = {
	__typename?: 'directus_versions_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	hash?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	key?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Versions_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Versions_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Versions_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	hash?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Versions_Mutated = {
	__typename?: 'directus_versions_mutated';
	data?: Maybe<Directus_Versions>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Webhooks = {
	__typename?: 'directus_webhooks';
	actions: Array<Maybe<Scalars['String']['output']>>;
	collections: Array<Maybe<Scalars['String']['output']>>;
	data?: Maybe<Scalars['Boolean']['output']>;
	headers?: Maybe<Scalars['JSON']['output']>;
	headers_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	method?: Maybe<Scalars['String']['output']>;
	migrated_flow?: Maybe<Directus_Flows>;
	name: Scalars['String']['output'];
	status?: Maybe<Scalars['String']['output']>;
	url: Scalars['String']['output'];
	was_active_before_deprecation: Scalars['Boolean']['output'];
};

export type Directus_WebhooksMigrated_FlowArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Webhooks_Aggregated = {
	__typename?: 'directus_webhooks_aggregated';
	avg?: Maybe<Directus_Webhooks_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Webhooks_Aggregated_Fields>;
	count?: Maybe<Directus_Webhooks_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Webhooks_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Webhooks_Aggregated_Fields>;
	min?: Maybe<Directus_Webhooks_Aggregated_Fields>;
	sum?: Maybe<Directus_Webhooks_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Webhooks_Aggregated_Fields>;
};

export type Directus_Webhooks_Aggregated_Count = {
	__typename?: 'directus_webhooks_aggregated_count';
	actions?: Maybe<Scalars['Int']['output']>;
	collections?: Maybe<Scalars['Int']['output']>;
	data?: Maybe<Scalars['Int']['output']>;
	headers?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	method?: Maybe<Scalars['Int']['output']>;
	migrated_flow?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	url?: Maybe<Scalars['Int']['output']>;
	was_active_before_deprecation?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Webhooks_Aggregated_Fields = {
	__typename?: 'directus_webhooks_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Webhooks_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Webhooks_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Webhooks_Filter>>>;
	actions?: InputMaybe<String_Filter_Operators>;
	collections?: InputMaybe<String_Filter_Operators>;
	data?: InputMaybe<Boolean_Filter_Operators>;
	headers?: InputMaybe<String_Filter_Operators>;
	headers_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	method?: InputMaybe<String_Filter_Operators>;
	migrated_flow?: InputMaybe<Directus_Flows_Filter>;
	name?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	url?: InputMaybe<String_Filter_Operators>;
	was_active_before_deprecation?: InputMaybe<Boolean_Filter_Operators>;
};

export type Directus_Webhooks_Mutated = {
	__typename?: 'directus_webhooks_mutated';
	data?: Maybe<Directus_Webhooks>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Edition_Info = {
	__typename?: 'edition_info';
	artists_proof_size?: Maybe<Scalars['Int']['output']>;
	edition_size_total?: Maybe<Scalars['Int']['output']>;
	/** This omits artist proof but includes studio proofs, colour proofs, etc. */
	general_proof_size?: Maybe<Scalars['Int']['output']>;
	hors_de_commerce_size?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	is_numbered?: Maybe<Scalars['Boolean']['output']>;
	is_unlimited?: Maybe<Scalars['Boolean']['output']>;
	regular_edition_size?: Maybe<Scalars['Int']['output']>;
};

export type Edition_Info_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Edition_Info_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Edition_Info_Filter>>>;
	artists_proof_size?: InputMaybe<Number_Filter_Operators>;
	edition_size_total?: InputMaybe<Number_Filter_Operators>;
	general_proof_size?: InputMaybe<Number_Filter_Operators>;
	hors_de_commerce_size?: InputMaybe<Number_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	is_numbered?: InputMaybe<Boolean_Filter_Operators>;
	is_unlimited?: InputMaybe<Boolean_Filter_Operators>;
	regular_edition_size?: InputMaybe<Number_Filter_Operators>;
};

export type Edition_Info_Mutated = {
	__typename?: 'edition_info_mutated';
	data?: Maybe<Edition_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Edition_Number_Type = {
	__typename?: 'edition_number_type';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Edition_Number_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Edition_Number_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Edition_Number_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Edition_Number_Type_Mutated = {
	__typename?: 'edition_number_type_mutated';
	data?: Maybe<Edition_Number_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity = {
	__typename?: 'entity';
	activities?: Maybe<Array<Maybe<Artwork_Activity_Association>>>;
	activities_func?: Maybe<Count_Functions>;
	additional_images?: Maybe<Array<Maybe<Entity_Images>>>;
	additional_images_func?: Maybe<Count_Functions>;
	addresses?: Maybe<Array<Maybe<Entity_Address>>>;
	addresses_func?: Maybe<Count_Functions>;
	artist?: Maybe<Artist>;
	attributes?: Maybe<Array<Maybe<Entity_Attribute>>>;
	attributes_func?: Maybe<Count_Functions>;
	collection_notes?: Maybe<Array<Maybe<Collection_Note>>>;
	collection_notes_func?: Maybe<Count_Functions>;
	contact_details?: Maybe<Array<Maybe<Entity_Contact_Detail>>>;
	contact_details_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	/** This will be updated automatically by a hook. If the entity is a person, this will be the first name + last name. If the entity is an organisation, it will be the organisation name. */
	name: Scalars['String']['output'];
	notes?: Maybe<Array<Maybe<Entity_Note>>>;
	notes_func?: Maybe<Count_Functions>;
	organisation?: Maybe<Organisation>;
	person?: Maybe<Person>;
	profile_image?: Maybe<Directus_Files>;
	status?: Maybe<Status>;
	type?: Maybe<Entity_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type EntityActivitiesArgs = {
	filter?: InputMaybe<Artwork_Activity_Association_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityAdditional_ImagesArgs = {
	filter?: InputMaybe<Entity_Images_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityAddressesArgs = {
	filter?: InputMaybe<Entity_Address_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityArtistArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityAttributesArgs = {
	filter?: InputMaybe<Entity_Attribute_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityCollection_NotesArgs = {
	filter?: InputMaybe<Collection_Note_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityContact_DetailsArgs = {
	filter?: InputMaybe<Entity_Contact_Detail_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityNotesArgs = {
	filter?: InputMaybe<Entity_Note_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityOrganisationArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityPersonArgs = {
	filter?: InputMaybe<Person_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityProfile_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityTypeArgs = {
	filter?: InputMaybe<Entity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type EntityUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Address = {
	__typename?: 'entity_address';
	city?: Maybe<Location>;
	country?: Maybe<Location>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	line_1?: Maybe<Scalars['String']['output']>;
	line_2?: Maybe<Scalars['String']['output']>;
	line_3?: Maybe<Scalars['String']['output']>;
	post_code?: Maybe<Scalars['String']['output']>;
	/** The date when the address is known to be the address of the entity. This could be different to the date created but will be populated manually on creation if left empty. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_AddressCityArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AddressCountryArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AddressEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AddressUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AddressUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Address_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Address_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Address_Filter>>>;
	city?: InputMaybe<Location_Filter>;
	country?: InputMaybe<Location_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	line_1?: InputMaybe<String_Filter_Operators>;
	line_2?: InputMaybe<String_Filter_Operators>;
	line_3?: InputMaybe<String_Filter_Operators>;
	post_code?: InputMaybe<String_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Entity_Address_Mutated = {
	__typename?: 'entity_address_mutated';
	data?: Maybe<Entity_Address>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Attribute = {
	__typename?: 'entity_attribute';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	type?: Maybe<Entity_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_AttributeEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AttributeStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AttributeTypeArgs = {
	filter?: InputMaybe<Entity_Attribute_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AttributeUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_AttributeUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Attribute_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Attribute_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Attribute_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Entity_Attribute_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Entity_Attribute_Mutated = {
	__typename?: 'entity_attribute_mutated';
	data?: Maybe<Entity_Attribute>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Attribute_Type = {
	__typename?: 'entity_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Entity_Attribute_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Attribute_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Attribute_Type_Mutated = {
	__typename?: 'entity_attribute_type_mutated';
	data?: Maybe<Entity_Attribute_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Contact_Detail = {
	__typename?: 'entity_contact_detail';
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	type?: Maybe<Entity_Contact_Detail_Type>;
	/** The value of the contact detail such as the email address, phone number or social handle. */
	value: Scalars['String']['output'];
};

export type Entity_Contact_DetailEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Contact_DetailTypeArgs = {
	filter?: InputMaybe<Entity_Contact_Detail_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Contact_Detail_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Contact_Detail_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Contact_Detail_Filter>>>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<Entity_Contact_Detail_Type_Filter>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Contact_Detail_Mutated = {
	__typename?: 'entity_contact_detail_mutated';
	data?: Maybe<Entity_Contact_Detail>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Contact_Detail_Type = {
	__typename?: 'entity_contact_detail_type';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Entity_Contact_Detail_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Contact_Detail_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Contact_Detail_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Contact_Detail_Type_Mutated = {
	__typename?: 'entity_contact_detail_type_mutated';
	data?: Maybe<Entity_Contact_Detail_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Filter>>>;
	activities?: InputMaybe<Artwork_Activity_Association_Filter>;
	activities_func?: InputMaybe<Count_Function_Filter_Operators>;
	additional_images?: InputMaybe<Entity_Images_Filter>;
	additional_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	addresses?: InputMaybe<Entity_Address_Filter>;
	addresses_func?: InputMaybe<Count_Function_Filter_Operators>;
	artist?: InputMaybe<Artist_Filter>;
	attributes?: InputMaybe<Entity_Attribute_Filter>;
	attributes_func?: InputMaybe<Count_Function_Filter_Operators>;
	collection_notes?: InputMaybe<Collection_Note_Filter>;
	collection_notes_func?: InputMaybe<Count_Function_Filter_Operators>;
	contact_details?: InputMaybe<Entity_Contact_Detail_Filter>;
	contact_details_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	notes?: InputMaybe<Entity_Note_Filter>;
	notes_func?: InputMaybe<Count_Function_Filter_Operators>;
	organisation?: InputMaybe<Organisation_Filter>;
	person?: InputMaybe<Person_Filter>;
	profile_image?: InputMaybe<Directus_Files_Filter>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Entity_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Entity_Images = {
	__typename?: 'entity_images';
	directus_files_id?: Maybe<Directus_Files>;
	entity_id?: Maybe<Entity>;
	id: Scalars['ID']['output'];
};

export type Entity_ImagesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_ImagesEntity_IdArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Images_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Images_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Images_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	entity_id?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Images_Mutated = {
	__typename?: 'entity_images_mutated';
	data?: Maybe<Entity_Images>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Mutated = {
	__typename?: 'entity_mutated';
	data?: Maybe<Entity>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Note = {
	__typename?: 'entity_note';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	note: Scalars['String']['output'];
	status?: Maybe<Status>;
	/** If the contents of the note are relevant to specific point in time, this relates to that. The date created and date updated will be populated automatically. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	type?: Maybe<Entity_Note_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Entity_NoteEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_NoteStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_NoteTypeArgs = {
	filter?: InputMaybe<Entity_Note_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_NoteUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_NoteUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Entity_Note_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Note_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Note_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	type?: InputMaybe<Entity_Note_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Entity_Note_Mutated = {
	__typename?: 'entity_note_mutated';
	data?: Maybe<Entity_Note>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Note_Type = {
	__typename?: 'entity_note_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Entity_Note_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Note_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Note_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Note_Type_Mutated = {
	__typename?: 'entity_note_type_mutated';
	data?: Maybe<Entity_Note_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Entity_Type = {
	__typename?: 'entity_type';
	key: Scalars['ID']['output'];
};

export type Entity_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Entity_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Entity_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Entity_Type_Mutated = {
	__typename?: 'entity_type_mutated';
	data?: Maybe<Entity_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition = {
	__typename?: 'exhibition';
	artwork_listings?: Maybe<Array<Maybe<Exhibition_Artwork_Listing>>>;
	artwork_listings_func?: Maybe<Count_Functions>;
	attributes?: Maybe<Array<Maybe<Exhibition_Attribute>>>;
	attributes_func?: Maybe<Count_Functions>;
	cover_image?: Maybe<Directus_Files>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	end_date?: Maybe<Scalars['Date']['output']>;
	end_date_func?: Maybe<Datetime_Functions>;
	/** The page url of the exhibition. */
	exhibition_url?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	organisers?: Maybe<Array<Maybe<Exhibition_Organisers>>>;
	organisers_func?: Maybe<Count_Functions>;
	start_date?: Maybe<Scalars['Date']['output']>;
	start_date_func?: Maybe<Datetime_Functions>;
	status?: Maybe<Status>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	venue_address_1?: Maybe<Scalars['String']['output']>;
	venue_address_2?: Maybe<Scalars['String']['output']>;
	venue_address_3?: Maybe<Scalars['String']['output']>;
	venue_city?: Maybe<Location>;
	venue_country?: Maybe<Location>;
	venue_post_code?: Maybe<Scalars['String']['output']>;
};

export type ExhibitionArtwork_ListingsArgs = {
	filter?: InputMaybe<Exhibition_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionAttributesArgs = {
	filter?: InputMaybe<Exhibition_Attribute_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionCover_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionOrganisersArgs = {
	filter?: InputMaybe<Exhibition_Organisers_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionVenue_CityArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ExhibitionVenue_CountryArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artist = {
	__typename?: 'exhibition_artist';
	artist_id?: Maybe<Artist>;
	exhibition_id?: Maybe<Exhibition>;
	id: Scalars['ID']['output'];
};

export type Exhibition_ArtistArtist_IdArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_ArtistExhibition_IdArgs = {
	filter?: InputMaybe<Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Artist_Filter>>>;
	artist_id?: InputMaybe<Artist_Filter>;
	exhibition_id?: InputMaybe<Exhibition_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Exhibition_Artist_Mutated = {
	__typename?: 'exhibition_artist_mutated';
	data?: Maybe<Exhibition_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Artwork = {
	__typename?: 'exhibition_artwork';
	artwork_id?: Maybe<Artwork>;
	exhibition_id?: Maybe<Exhibition>;
	id: Scalars['ID']['output'];
};

export type Exhibition_ArtworkArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_ArtworkExhibition_IdArgs = {
	filter?: InputMaybe<Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Artwork_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Filter>;
	exhibition_id?: InputMaybe<Exhibition_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Exhibition_Artwork_Listing = {
	__typename?: 'exhibition_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artwork_listing_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	exhibition?: Maybe<Exhibition>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Exhibition_Artwork_ListingArtwork_ListingArgs = {
	filter?: InputMaybe<Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_ListingExhibitionArgs = {
	filter?: InputMaybe<Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_ListingStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_ListingUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_ListingUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Artwork_Listing_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Artwork_Listing_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Artwork_Listing_Filter>>>;
	artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	artwork_listing_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibition?: InputMaybe<Exhibition_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Exhibition_Artwork_Listing_Mutated = {
	__typename?: 'exhibition_artwork_listing_mutated';
	data?: Maybe<Exhibition_Artwork_Listing>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Artwork_Mutated = {
	__typename?: 'exhibition_artwork_mutated';
	data?: Maybe<Exhibition_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Attribute = {
	__typename?: 'exhibition_attribute';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	exhibition?: Maybe<Exhibition>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	type?: Maybe<Exhibition_Attribute_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Exhibition_AttributeExhibitionArgs = {
	filter?: InputMaybe<Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_AttributeStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_AttributeTypeArgs = {
	filter?: InputMaybe<Exhibition_Attribute_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_AttributeUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_AttributeUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Attribute_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Attribute_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Attribute_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibition?: InputMaybe<Exhibition_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Exhibition_Attribute_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Exhibition_Attribute_Mutated = {
	__typename?: 'exhibition_attribute_mutated';
	data?: Maybe<Exhibition_Attribute>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Attribute_Type = {
	__typename?: 'exhibition_attribute_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Exhibition_Attribute_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Attribute_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Attribute_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Exhibition_Attribute_Type_Mutated = {
	__typename?: 'exhibition_attribute_type_mutated';
	data?: Maybe<Exhibition_Attribute_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Filter>>>;
	artwork_listings?: InputMaybe<Exhibition_Artwork_Listing_Filter>;
	artwork_listings_func?: InputMaybe<Count_Function_Filter_Operators>;
	attributes?: InputMaybe<Exhibition_Attribute_Filter>;
	attributes_func?: InputMaybe<Count_Function_Filter_Operators>;
	cover_image?: InputMaybe<Directus_Files_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibition_url?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	organisers?: InputMaybe<Exhibition_Organisers_Filter>;
	organisers_func?: InputMaybe<Count_Function_Filter_Operators>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	venue_address_1?: InputMaybe<String_Filter_Operators>;
	venue_address_2?: InputMaybe<String_Filter_Operators>;
	venue_address_3?: InputMaybe<String_Filter_Operators>;
	venue_city?: InputMaybe<Location_Filter>;
	venue_country?: InputMaybe<Location_Filter>;
	venue_post_code?: InputMaybe<String_Filter_Operators>;
};

export type Exhibition_Mutated = {
	__typename?: 'exhibition_mutated';
	data?: Maybe<Exhibition>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Exhibition_Organisers = {
	__typename?: 'exhibition_organisers';
	entity_id?: Maybe<Entity>;
	exhibition_id?: Maybe<Exhibition>;
	id: Scalars['ID']['output'];
};

export type Exhibition_OrganisersEntity_IdArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_OrganisersExhibition_IdArgs = {
	filter?: InputMaybe<Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Exhibition_Organisers_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Exhibition_Organisers_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Exhibition_Organisers_Filter>>>;
	entity_id?: InputMaybe<Entity_Filter>;
	exhibition_id?: InputMaybe<Exhibition_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
};

export type Exhibition_Organisers_Mutated = {
	__typename?: 'exhibition_organisers_mutated';
	data?: Maybe<Exhibition_Organisers>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Fair = {
	__typename?: 'fair';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	end_date?: Maybe<Scalars['Date']['output']>;
	end_date_func?: Maybe<Datetime_Functions>;
	exhibitors?: Maybe<Array<Maybe<Fair_Exhibitor>>>;
	exhibitors_func?: Maybe<Count_Functions>;
	fair_organisation?: Maybe<Fair_Organisation>;
	/** The page url of the fair. */
	fair_url?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	start_date?: Maybe<Scalars['Date']['output']>;
	start_date_func?: Maybe<Datetime_Functions>;
	status?: Maybe<Status>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	venue_address_1?: Maybe<Scalars['String']['output']>;
	venue_address_2?: Maybe<Scalars['String']['output']>;
	venue_address_3?: Maybe<Scalars['String']['output']>;
	venue_city?: Maybe<Location>;
	venue_post_code?: Maybe<Scalars['String']['output']>;
};

export type FairExhibitorsArgs = {
	filter?: InputMaybe<Fair_Exhibitor_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FairFair_OrganisationArgs = {
	filter?: InputMaybe<Fair_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FairStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FairUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FairUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FairVenue_CityArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_Listing = {
	__typename?: 'fair_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artwork_listing_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	fair_exhibitor?: Maybe<Fair_Exhibitor>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Fair_Artwork_ListingArtwork_ListingArgs = {
	filter?: InputMaybe<Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_ListingFair_ExhibitorArgs = {
	filter?: InputMaybe<Fair_Exhibitor_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_ListingStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_ListingUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_ListingUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Artwork_Listing_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Fair_Artwork_Listing_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Fair_Artwork_Listing_Filter>>>;
	artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	artwork_listing_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	fair_exhibitor?: InputMaybe<Fair_Exhibitor_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Fair_Artwork_Listing_Mutated = {
	__typename?: 'fair_artwork_listing_mutated';
	data?: Maybe<Fair_Artwork_Listing>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Fair_Exhibitor = {
	__typename?: 'fair_exhibitor';
	artwork_listings?: Maybe<Array<Maybe<Fair_Artwork_Listing>>>;
	artwork_listings_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	entity?: Maybe<Entity>;
	fair?: Maybe<Fair>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Fair_ExhibitorArtwork_ListingsArgs = {
	filter?: InputMaybe<Fair_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_ExhibitorEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_ExhibitorFairArgs = {
	filter?: InputMaybe<Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_ExhibitorStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_ExhibitorUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_ExhibitorUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Exhibitor_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Fair_Exhibitor_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Fair_Exhibitor_Filter>>>;
	artwork_listings?: InputMaybe<Fair_Artwork_Listing_Filter>;
	artwork_listings_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	fair?: InputMaybe<Fair_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Fair_Exhibitor_Mutated = {
	__typename?: 'fair_exhibitor_mutated';
	data?: Maybe<Fair_Exhibitor>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Fair_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Fair_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Fair_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	exhibitors?: InputMaybe<Fair_Exhibitor_Filter>;
	exhibitors_func?: InputMaybe<Count_Function_Filter_Operators>;
	fair_organisation?: InputMaybe<Fair_Organisation_Filter>;
	fair_url?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	venue_address_1?: InputMaybe<String_Filter_Operators>;
	venue_address_2?: InputMaybe<String_Filter_Operators>;
	venue_address_3?: InputMaybe<String_Filter_Operators>;
	venue_city?: InputMaybe<Location_Filter>;
	venue_post_code?: InputMaybe<String_Filter_Operators>;
};

export type Fair_Mutated = {
	__typename?: 'fair_mutated';
	data?: Maybe<Fair>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Fair_Organisation = {
	__typename?: 'fair_organisation';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	fairs?: Maybe<Array<Maybe<Fair>>>;
	fairs_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	organisation?: Maybe<Organisation>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Fair_OrganisationFairsArgs = {
	filter?: InputMaybe<Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_OrganisationOrganisationArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_OrganisationStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_OrganisationUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_OrganisationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Fair_Organisation_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Fair_Organisation_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Fair_Organisation_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	fairs?: InputMaybe<Fair_Filter>;
	fairs_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	organisation?: InputMaybe<Organisation_Filter>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Fair_Organisation_Mutated = {
	__typename?: 'fair_organisation_mutated';
	data?: Maybe<Fair_Organisation>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Favourite_Artist = {
	__typename?: 'favourite_artist';
	artist?: Maybe<Artist>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Favourite_ArtistArtistArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Favourite_ArtistStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Favourite_ArtistUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Favourite_ArtistUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Favourite_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Favourite_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Favourite_Artist_Filter>>>;
	artist?: InputMaybe<Artist_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Favourite_Artist_Mutated = {
	__typename?: 'favourite_artist_mutated';
	data?: Maybe<Favourite_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Gallery = {
	__typename?: 'gallery';
	artist_representation?: Maybe<Array<Maybe<Gallery_Representation>>>;
	artist_representation_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	gallery_artwork_listings?: Maybe<Array<Maybe<Gallery_Artwork_Listing>>>;
	gallery_artwork_listings_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	organisation?: Maybe<Organisation>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type GalleryArtist_RepresentationArgs = {
	filter?: InputMaybe<Gallery_Representation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GalleryGallery_Artwork_ListingsArgs = {
	filter?: InputMaybe<Gallery_Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GalleryOrganisationArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GalleryStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GalleryUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GalleryUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_Listing = {
	__typename?: 'gallery_artwork_listing';
	artwork_listing?: Maybe<Array<Maybe<Artwork_Listing>>>;
	artwork_listing_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	gallery?: Maybe<Gallery>;
	id: Scalars['ID']['output'];
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gallery_Artwork_ListingArtwork_ListingArgs = {
	filter?: InputMaybe<Artwork_Listing_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_ListingGalleryArgs = {
	filter?: InputMaybe<Gallery_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_ListingStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_ListingUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_ListingUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Artwork_Listing_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Gallery_Artwork_Listing_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Gallery_Artwork_Listing_Filter>>>;
	artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	artwork_listing_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	gallery?: InputMaybe<Gallery_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Gallery_Artwork_Listing_Mutated = {
	__typename?: 'gallery_artwork_listing_mutated';
	data?: Maybe<Gallery_Artwork_Listing>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Gallery_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Gallery_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Gallery_Filter>>>;
	artist_representation?: InputMaybe<Gallery_Representation_Filter>;
	artist_representation_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	gallery_artwork_listings?: InputMaybe<Gallery_Artwork_Listing_Filter>;
	gallery_artwork_listings_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	organisation?: InputMaybe<Organisation_Filter>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Gallery_Mutated = {
	__typename?: 'gallery_mutated';
	data?: Maybe<Gallery>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Gallery_Representation = {
	__typename?: 'gallery_representation';
	artists?: Maybe<Array<Maybe<Gallery_Representation_Artist>>>;
	artists_func?: Maybe<Count_Functions>;
	cover_image?: Maybe<Directus_Files>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	email_date?: Maybe<Scalars['Date']['output']>;
	email_date_func?: Maybe<Datetime_Functions>;
	email_subject?: Maybe<Scalars['String']['output']>;
	gallery?: Maybe<Gallery>;
	id: Scalars['ID']['output'];
	receiver?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Gallery_RepresentationArtistsArgs = {
	filter?: InputMaybe<Gallery_Representation_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_RepresentationCover_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_RepresentationGalleryArgs = {
	filter?: InputMaybe<Gallery_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_RepresentationStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_RepresentationUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_RepresentationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Representation_Artist = {
	__typename?: 'gallery_representation_artist';
	artist_id?: Maybe<Artist>;
	gallery_representation_id?: Maybe<Gallery_Representation>;
	id: Scalars['ID']['output'];
};

export type Gallery_Representation_ArtistArtist_IdArgs = {
	filter?: InputMaybe<Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Representation_ArtistGallery_Representation_IdArgs = {
	filter?: InputMaybe<Gallery_Representation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Gallery_Representation_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Gallery_Representation_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Gallery_Representation_Artist_Filter>>>;
	artist_id?: InputMaybe<Artist_Filter>;
	gallery_representation_id?: InputMaybe<Gallery_Representation_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Gallery_Representation_Artist_Mutated = {
	__typename?: 'gallery_representation_artist_mutated';
	data?: Maybe<Gallery_Representation_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Gallery_Representation_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Gallery_Representation_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Gallery_Representation_Filter>>>;
	artists?: InputMaybe<Gallery_Representation_Artist_Filter>;
	artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	cover_image?: InputMaybe<Directus_Files_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	email_date?: InputMaybe<Date_Filter_Operators>;
	email_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	email_subject?: InputMaybe<String_Filter_Operators>;
	gallery?: InputMaybe<Gallery_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	receiver?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Gallery_Representation_Mutated = {
	__typename?: 'gallery_representation_mutated';
	data?: Maybe<Gallery_Representation>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Gender = {
	__typename?: 'gender';
	/** This is the primary key. */
	key: Scalars['ID']['output'];
	/** This is used in the interface for display purposes. */
	name: Scalars['String']['output'];
};

export type Gender_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Gender_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Gender_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Gender_Mutated = {
	__typename?: 'gender_mutated';
	data?: Maybe<Gender>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export enum Graphql_Sdl_Scope {
	Items = 'items',
	System = 'system',
}

export type Hash_Filter_Operators = {
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Heni_Artwork_Type = {
	__typename?: 'heni_artwork_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Heni_Artwork_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Heni_Artwork_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Heni_Artwork_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Heni_Artwork_Type_Mutated = {
	__typename?: 'heni_artwork_type_mutated';
	data?: Maybe<Heni_Artwork_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Interim_Linked_Artist = {
	__typename?: 'interim_linked_artist';
	about?: Maybe<Scalars['String']['output']>;
	archived?: Maybe<Scalars['Boolean']['output']>;
	artist?: Maybe<Scalars['String']['output']>;
	artist_id?: Maybe<Scalars['GraphQLBigInt']['output']>;
	artistimageurl?: Maybe<Scalars['String']['output']>;
	data_source?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	isliked?: Maybe<Scalars['Boolean']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
	record_id?: Maybe<Scalars['GraphQLBigInt']['output']>;
	robo_check?: Maybe<Scalars['Boolean']['output']>;
	year_born?: Maybe<Scalars['Int']['output']>;
};

export type Interim_Linked_Artist_Mutated = {
	__typename?: 'interim_linked_artist_mutated';
	data?: Maybe<Interim_Linked_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Interim_Unlinked_Artist = {
	__typename?: 'interim_unlinked_artist';
	about?: Maybe<Scalars['String']['output']>;
	archived?: Maybe<Scalars['Boolean']['output']>;
	artist?: Maybe<Scalars['String']['output']>;
	artist_id?: Maybe<Scalars['GraphQLBigInt']['output']>;
	artistimageurl?: Maybe<Scalars['String']['output']>;
	data_source?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	importance_index?: Maybe<Scalars['GraphQLBigInt']['output']>;
	importance_score?: Maybe<Scalars['Float']['output']>;
	isliked?: Maybe<Scalars['Boolean']['output']>;
	linked?: Maybe<Scalars['Boolean']['output']>;
	linked_at?: Maybe<Scalars['Date']['output']>;
	linked_at_func?: Maybe<Datetime_Functions>;
	nationality?: Maybe<Scalars['String']['output']>;
	record_id?: Maybe<Scalars['GraphQLBigInt']['output']>;
	robo_check?: Maybe<Scalars['Boolean']['output']>;
	user_id?: Maybe<Scalars['String']['output']>;
	year_born?: Maybe<Scalars['Int']['output']>;
};

export type Interim_Unlinked_Artist_Mutated = {
	__typename?: 'interim_unlinked_artist_mutated';
	data?: Maybe<Interim_Unlinked_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Location = {
	__typename?: 'location';
	code: Scalars['ID']['output'];
	country: Scalars['String']['output'];
	/** If this is a country then the nationality will be populated here. E.g. for GB the nationality will be British. */
	country_nationality?: Maybe<Scalars['String']['output']>;
	name: Scalars['String']['output'];
	parent?: Maybe<Location>;
	short_code: Scalars['String']['output'];
	subdivisions?: Maybe<Array<Maybe<Location>>>;
	subdivisions_func?: Maybe<Count_Functions>;
	timezone?: Maybe<Location_Timezone>;
	type?: Maybe<Location_Type>;
};

export type LocationParentArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type LocationSubdivisionsArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type LocationTimezoneArgs = {
	filter?: InputMaybe<Location_Timezone_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type LocationTypeArgs = {
	filter?: InputMaybe<Location_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Location_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Location_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Location_Filter>>>;
	code?: InputMaybe<String_Filter_Operators>;
	country?: InputMaybe<String_Filter_Operators>;
	country_nationality?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Location_Filter>;
	short_code?: InputMaybe<String_Filter_Operators>;
	subdivisions?: InputMaybe<Location_Filter>;
	subdivisions_func?: InputMaybe<Count_Function_Filter_Operators>;
	timezone?: InputMaybe<Location_Timezone_Filter>;
	type?: InputMaybe<Location_Type_Filter>;
};

export type Location_Mutated = {
	__typename?: 'location_mutated';
	data?: Maybe<Location>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Location_Timezone = {
	__typename?: 'location_timezone';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Location_Timezone_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Location_Timezone_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Location_Timezone_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Location_Timezone_Mutated = {
	__typename?: 'location_timezone_mutated';
	data?: Maybe<Location_Timezone>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Location_Type = {
	__typename?: 'location_type';
	key: Scalars['ID']['output'];
};

export type Location_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Location_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Location_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Location_Type_Mutated = {
	__typename?: 'location_type_mutated';
	data?: Maybe<Location_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Number_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nin?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Organisation = {
	__typename?: 'organisation';
	artworks?: Maybe<Array<Maybe<Artwork_Organisation>>>;
	artworks_func?: Maybe<Count_Functions>;
	children?: Maybe<Array<Maybe<Organisation>>>;
	children_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	location?: Maybe<Location>;
	name: Scalars['String']['output'];
	parent?: Maybe<Organisation>;
	status?: Maybe<Status>;
	type?: Maybe<Array<Maybe<Organisation_Organisation_Type>>>;
	type_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_dissolved?: Maybe<Scalars['Int']['output']>;
	year_founded?: Maybe<Scalars['Int']['output']>;
};

export type OrganisationArtworksArgs = {
	filter?: InputMaybe<Artwork_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationChildrenArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationLocationArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationParentArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationTypeArgs = {
	filter?: InputMaybe<Organisation_Organisation_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type OrganisationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Organisation_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Organisation_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Organisation_Filter>>>;
	artworks?: InputMaybe<Artwork_Organisation_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	children?: InputMaybe<Organisation_Filter>;
	children_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<Location_Filter>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Organisation_Filter>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Organisation_Organisation_Type_Filter>;
	type_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_dissolved?: InputMaybe<Number_Filter_Operators>;
	year_founded?: InputMaybe<Number_Filter_Operators>;
};

export type Organisation_Mutated = {
	__typename?: 'organisation_mutated';
	data?: Maybe<Organisation>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Organisation_Organisation_Type = {
	__typename?: 'organisation_organisation_type';
	id: Scalars['ID']['output'];
	organisation_id?: Maybe<Organisation>;
	organisation_type_key?: Maybe<Organisation_Type>;
};

export type Organisation_Organisation_TypeOrganisation_IdArgs = {
	filter?: InputMaybe<Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Organisation_Organisation_TypeOrganisation_Type_KeyArgs = {
	filter?: InputMaybe<Organisation_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Organisation_Organisation_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Organisation_Organisation_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Organisation_Organisation_Type_Filter>>>;
	id?: InputMaybe<String_Filter_Operators>;
	organisation_id?: InputMaybe<Organisation_Filter>;
	organisation_type_key?: InputMaybe<Organisation_Type_Filter>;
};

export type Organisation_Organisation_Type_Mutated = {
	__typename?: 'organisation_organisation_type_mutated';
	data?: Maybe<Organisation_Organisation_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Organisation_Type = {
	__typename?: 'organisation_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Organisation_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Organisation_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Organisation_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Organisation_Type_Mutated = {
	__typename?: 'organisation_type_mutated';
	data?: Maybe<Organisation_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Person = {
	__typename?: 'person';
	biography?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	entity?: Maybe<Entity>;
	first_name?: Maybe<Scalars['String']['output']>;
	gender?: Maybe<Gender>;
	id: Scalars['ID']['output'];
	industry?: Maybe<Scalars['String']['output']>;
	job_title?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	middle_name?: Maybe<Scalars['String']['output']>;
	nationalities?: Maybe<Array<Maybe<Person_Nationality>>>;
	nationalities_func?: Maybe<Count_Functions>;
	net_worth_usd?: Maybe<Scalars['Float']['output']>;
	preferred_name?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Status>;
	type?: Maybe<Array<Maybe<Person_Person_Type>>>;
	type_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type PersonEntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonGenderArgs = {
	filter?: InputMaybe<Gender_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonNationalitiesArgs = {
	filter?: InputMaybe<Person_Nationality_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonTypeArgs = {
	filter?: InputMaybe<Person_Person_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PersonUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Person_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Person_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Person_Filter>>>;
	biography?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	entity?: InputMaybe<Entity_Filter>;
	first_name?: InputMaybe<String_Filter_Operators>;
	gender?: InputMaybe<Gender_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	industry?: InputMaybe<String_Filter_Operators>;
	job_title?: InputMaybe<String_Filter_Operators>;
	last_name?: InputMaybe<String_Filter_Operators>;
	middle_name?: InputMaybe<String_Filter_Operators>;
	nationalities?: InputMaybe<Person_Nationality_Filter>;
	nationalities_func?: InputMaybe<Count_Function_Filter_Operators>;
	net_worth_usd?: InputMaybe<Number_Filter_Operators>;
	preferred_name?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	type?: InputMaybe<Person_Person_Type_Filter>;
	type_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_birth?: InputMaybe<Number_Filter_Operators>;
	year_death?: InputMaybe<Number_Filter_Operators>;
};

export type Person_Mutated = {
	__typename?: 'person_mutated';
	data?: Maybe<Person>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Person_Nationality = {
	__typename?: 'person_nationality';
	country?: Maybe<Location>;
	id: Scalars['ID']['output'];
	person?: Maybe<Person>;
};

export type Person_NationalityCountryArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Person_NationalityPersonArgs = {
	filter?: InputMaybe<Person_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Person_Nationality_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Person_Nationality_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Person_Nationality_Filter>>>;
	country?: InputMaybe<Location_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	person?: InputMaybe<Person_Filter>;
};

export type Person_Nationality_Mutated = {
	__typename?: 'person_nationality_mutated';
	data?: Maybe<Person_Nationality>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Person_Person_Type = {
	__typename?: 'person_person_type';
	id: Scalars['ID']['output'];
	person_id?: Maybe<Person>;
	person_type_key?: Maybe<Person_Type>;
};

export type Person_Person_TypePerson_IdArgs = {
	filter?: InputMaybe<Person_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Person_Person_TypePerson_Type_KeyArgs = {
	filter?: InputMaybe<Person_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Person_Person_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Person_Person_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Person_Person_Type_Filter>>>;
	id?: InputMaybe<String_Filter_Operators>;
	person_id?: InputMaybe<Person_Filter>;
	person_type_key?: InputMaybe<Person_Type_Filter>;
};

export type Person_Person_Type_Mutated = {
	__typename?: 'person_person_type_mutated';
	data?: Maybe<Person_Person_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Person_Type = {
	__typename?: 'person_type';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Person_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Person_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Person_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Person_Type_Mutated = {
	__typename?: 'person_type_mutated';
	data?: Maybe<Person_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Info = {
	__typename?: 'pipeline_info';
	description?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Array<Maybe<Pipeline_Info_Item>>>;
	item_func?: Maybe<Count_Functions>;
	meta?: Maybe<Scalars['JSON']['output']>;
	meta_func?: Maybe<Count_Functions>;
	step?: Maybe<Pipeline_Step>;
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	type?: Maybe<Pipeline_Info_Type>;
	unprocessed_source_data?: Maybe<Array<Maybe<Pipeline_Info_Unprocessed>>>;
	unprocessed_source_data_func?: Maybe<Count_Functions>;
};

export type Pipeline_InfoItemArgs = {
	filter?: InputMaybe<Pipeline_Info_Item_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_InfoStepArgs = {
	filter?: InputMaybe<Pipeline_Step_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_InfoTypeArgs = {
	filter?: InputMaybe<Pipeline_Info_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_InfoUnprocessed_Source_DataArgs = {
	filter?: InputMaybe<Pipeline_Info_Unprocessed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Info_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Info_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Info_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<Pipeline_Info_Item_Filter>;
	item_func?: InputMaybe<Count_Function_Filter_Operators>;
	meta?: InputMaybe<String_Filter_Operators>;
	meta_func?: InputMaybe<Count_Function_Filter_Operators>;
	step?: InputMaybe<Pipeline_Step_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	type?: InputMaybe<Pipeline_Info_Type_Filter>;
	unprocessed_source_data?: InputMaybe<Pipeline_Info_Unprocessed_Filter>;
	unprocessed_source_data_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Pipeline_Info_Item = {
	__typename?: 'pipeline_info_item';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Pipeline_Info_Item_Item_Union>;
	pipeline_info_id?: Maybe<Pipeline_Info>;
};

export type Pipeline_Info_ItemPipeline_Info_IdArgs = {
	filter?: InputMaybe<Pipeline_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Info_Item_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Info_Item_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Info_Item_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item__artist?: InputMaybe<Artist_Filter>;
	item__artist_award?: InputMaybe<Artist_Award_Filter>;
	item__artwork?: InputMaybe<Artwork_Filter>;
	item__artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	item__auction?: InputMaybe<Auction_Filter>;
	item__auction_bid?: InputMaybe<Auction_Bid_Filter>;
	item__auction_house?: InputMaybe<Auction_House_Filter>;
	item__auction_house_buyers_premium?: InputMaybe<Auction_House_Buyers_Premium_Filter>;
	item__auction_house_premium?: InputMaybe<Auction_House_Premium_Filter>;
	item__auction_lot?: InputMaybe<Auction_Lot_Filter>;
	item__author?: InputMaybe<Author_Filter>;
	item__award?: InputMaybe<Award_Filter>;
	item__entity?: InputMaybe<Entity_Filter>;
	item__entity_note?: InputMaybe<Entity_Note_Filter>;
	item__exhibition?: InputMaybe<Exhibition_Filter>;
	item__exhibition_artist?: InputMaybe<Exhibition_Artist_Filter>;
	item__exhibition_artwork?: InputMaybe<Exhibition_Artwork_Filter>;
	item__fair?: InputMaybe<Fair_Filter>;
	item__fair_artwork_listing?: InputMaybe<Fair_Artwork_Listing_Filter>;
	item__fair_exhibitor?: InputMaybe<Fair_Exhibitor_Filter>;
	item__favourite_artist?: InputMaybe<Favourite_Artist_Filter>;
	item__gallery?: InputMaybe<Gallery_Filter>;
	item__gender?: InputMaybe<Gender_Filter>;
	item__organisation?: InputMaybe<Organisation_Filter>;
	item__person?: InputMaybe<Person_Filter>;
	item__person_nationality?: InputMaybe<Person_Nationality_Filter>;
	pipeline_info_id?: InputMaybe<Pipeline_Info_Filter>;
};

export type Pipeline_Info_Item_Item_Union =
	| Artist
	| Artist_Award
	| Artwork
	| Artwork_Listing
	| Auction
	| Auction_Bid
	| Auction_House
	| Auction_House_Buyers_Premium
	| Auction_House_Premium
	| Auction_Lot
	| Author
	| Award
	| Entity
	| Entity_Note
	| Exhibition
	| Exhibition_Artist
	| Exhibition_Artwork
	| Fair
	| Fair_Artwork_Listing
	| Fair_Exhibitor
	| Favourite_Artist
	| Gallery
	| Gender
	| Organisation
	| Person
	| Person_Nationality;

export type Pipeline_Info_Item_Mutated = {
	__typename?: 'pipeline_info_item_mutated';
	data?: Maybe<Pipeline_Info_Item>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Info_Mutated = {
	__typename?: 'pipeline_info_mutated';
	data?: Maybe<Pipeline_Info>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Info_Type = {
	__typename?: 'pipeline_info_type';
	description: Scalars['String']['output'];
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Pipeline_Info_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Info_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Info_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Pipeline_Info_Type_Mutated = {
	__typename?: 'pipeline_info_type_mutated';
	data?: Maybe<Pipeline_Info_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Info_Unprocessed = {
	__typename?: 'pipeline_info_unprocessed';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Scalars['String']['output']>;
	pipeline_info_id?: Maybe<Pipeline_Info>;
};

export type Pipeline_Info_UnprocessedPipeline_Info_IdArgs = {
	filter?: InputMaybe<Pipeline_Info_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Info_Unprocessed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Info_Unprocessed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Info_Unprocessed_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	pipeline_info_id?: InputMaybe<Pipeline_Info_Filter>;
};

export type Pipeline_Info_Unprocessed_Mutated = {
	__typename?: 'pipeline_info_unprocessed_mutated';
	data?: Maybe<Pipeline_Info_Unprocessed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Source = {
	__typename?: 'pipeline_source';
	/** This describes where data came from. It could be the name of a gallery or auction house for example and should describe the data source external to HENI. */
	data_source?: Maybe<Scalars['String']['output']>;
	/** This is the external id of the data as it is defined in the external data source. This could be the sale number for an auction lot or an item id in the case of an ebay auction lot. */
	data_source_id?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	/** The id of the main record in the ingestion DB. */
	ingestion_main_record_id?: Maybe<Scalars['String']['output']>;
	/** This describes the internal HENI service responsible for extracting the data from the data source and producing entries into the ingestion DB. This would describe the spider/crawler process for scraping or the manual ingestion source from the manual ingestion app. */
	ingestion_source?: Maybe<Scalars['String']['output']>;
	/** This is the id of the data as it is defined in the internal ingestion source DB. This could be an id of the scrape record in the scrape id or the id of a record in the ingestion app DB. */
	ingestion_source_id?: Maybe<Scalars['String']['output']>;
	last_updated: Scalars['Date']['output'];
	last_updated_func?: Maybe<Datetime_Functions>;
	processed_item?: Maybe<Array<Maybe<Pipeline_Source_Processed>>>;
	processed_item_func?: Maybe<Count_Functions>;
	type?: Maybe<Pipeline_Source_Type>;
	unprocessed_source_data?: Maybe<Array<Maybe<Pipeline_Source_Unprocessed>>>;
	unprocessed_source_data_func?: Maybe<Count_Functions>;
};

export type Pipeline_SourceProcessed_ItemArgs = {
	filter?: InputMaybe<Pipeline_Source_Processed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_SourceTypeArgs = {
	filter?: InputMaybe<Pipeline_Source_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_SourceUnprocessed_Source_DataArgs = {
	filter?: InputMaybe<Pipeline_Source_Unprocessed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Source_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Source_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Source_Filter>>>;
	data_source?: InputMaybe<String_Filter_Operators>;
	data_source_id?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	ingestion_main_record_id?: InputMaybe<String_Filter_Operators>;
	ingestion_source?: InputMaybe<String_Filter_Operators>;
	ingestion_source_id?: InputMaybe<String_Filter_Operators>;
	last_updated?: InputMaybe<Date_Filter_Operators>;
	last_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	processed_item?: InputMaybe<Pipeline_Source_Processed_Filter>;
	processed_item_func?: InputMaybe<Count_Function_Filter_Operators>;
	type?: InputMaybe<Pipeline_Source_Type_Filter>;
	unprocessed_source_data?: InputMaybe<Pipeline_Source_Unprocessed_Filter>;
	unprocessed_source_data_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Pipeline_Source_Mutated = {
	__typename?: 'pipeline_source_mutated';
	data?: Maybe<Pipeline_Source>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Source_Processed = {
	__typename?: 'pipeline_source_processed';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Pipeline_Source_Processed_Item_Union>;
	pipeline_source_id?: Maybe<Pipeline_Source>;
};

export type Pipeline_Source_ProcessedPipeline_Source_IdArgs = {
	filter?: InputMaybe<Pipeline_Source_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Source_Processed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Source_Processed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Source_Processed_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item__artist?: InputMaybe<Artist_Filter>;
	item__artist_award?: InputMaybe<Artist_Award_Filter>;
	item__artwork?: InputMaybe<Artwork_Filter>;
	item__artwork_activity?: InputMaybe<Artwork_Activity_Filter>;
	item__artwork_listing?: InputMaybe<Artwork_Listing_Filter>;
	item__artwork_series?: InputMaybe<Artwork_Series_Filter>;
	item__auction?: InputMaybe<Auction_Filter>;
	item__auction_house?: InputMaybe<Auction_House_Filter>;
	item__award?: InputMaybe<Award_Filter>;
	item__entity?: InputMaybe<Entity_Filter>;
	item__exhibition?: InputMaybe<Exhibition_Filter>;
	item__fair?: InputMaybe<Fair_Filter>;
	item__fair_exhibitor?: InputMaybe<Fair_Exhibitor_Filter>;
	item__fair_organisation?: InputMaybe<Fair_Organisation_Filter>;
	item__gallery?: InputMaybe<Gallery_Filter>;
	item__organisation?: InputMaybe<Organisation_Filter>;
	item__person?: InputMaybe<Person_Filter>;
	item__relationship?: InputMaybe<Relationship_Filter>;
	pipeline_source_id?: InputMaybe<Pipeline_Source_Filter>;
};

export type Pipeline_Source_Processed_Item_Union =
	| Artist
	| Artist_Award
	| Artwork
	| Artwork_Activity
	| Artwork_Listing
	| Artwork_Series
	| Auction
	| Auction_House
	| Award
	| Entity
	| Exhibition
	| Fair
	| Fair_Exhibitor
	| Fair_Organisation
	| Gallery
	| Organisation
	| Person
	| Relationship;

export type Pipeline_Source_Processed_Mutated = {
	__typename?: 'pipeline_source_processed_mutated';
	data?: Maybe<Pipeline_Source_Processed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Source_Type = {
	__typename?: 'pipeline_source_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Pipeline_Source_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Source_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Source_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Pipeline_Source_Type_Mutated = {
	__typename?: 'pipeline_source_type_mutated';
	data?: Maybe<Pipeline_Source_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Source_Unprocessed = {
	__typename?: 'pipeline_source_unprocessed';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Scalars['String']['output']>;
	pipeline_source_id?: Maybe<Pipeline_Source>;
};

export type Pipeline_Source_UnprocessedPipeline_Source_IdArgs = {
	filter?: InputMaybe<Pipeline_Source_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pipeline_Source_Unprocessed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Source_Unprocessed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Source_Unprocessed_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	pipeline_source_id?: InputMaybe<Pipeline_Source_Filter>;
};

export type Pipeline_Source_Unprocessed_Mutated = {
	__typename?: 'pipeline_source_unprocessed_mutated';
	data?: Maybe<Pipeline_Source_Unprocessed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pipeline_Step = {
	__typename?: 'pipeline_step';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Pipeline_Step_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pipeline_Step_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pipeline_Step_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Pipeline_Step_Mutated = {
	__typename?: 'pipeline_step_mutated';
	data?: Maybe<Pipeline_Step>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Relationship = {
	__typename?: 'relationship';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** When the relationship ended. */
	end_date?: Maybe<Scalars['Date']['output']>;
	end_date_func?: Maybe<Datetime_Functions>;
	from_entity?: Maybe<Entity>;
	id: Scalars['ID']['output'];
	notes?: Maybe<Scalars['String']['output']>;
	/** When the relationship started. */
	start_date?: Maybe<Scalars['Date']['output']>;
	start_date_func?: Maybe<Datetime_Functions>;
	status?: Maybe<Status>;
	to_entity?: Maybe<Entity>;
	type?: Maybe<Relationship_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type RelationshipFrom_EntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RelationshipStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RelationshipTo_EntityArgs = {
	filter?: InputMaybe<Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RelationshipTypeArgs = {
	filter?: InputMaybe<Relationship_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RelationshipUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type RelationshipUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Relationship_Entity_Type = {
	__typename?: 'relationship_entity_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Relationship_Entity_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Relationship_Entity_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Relationship_Entity_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Relationship_Entity_Type_Mutated = {
	__typename?: 'relationship_entity_type_mutated';
	data?: Maybe<Relationship_Entity_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Relationship_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Relationship_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Relationship_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	from_entity?: InputMaybe<Entity_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	notes?: InputMaybe<String_Filter_Operators>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	status?: InputMaybe<Status_Filter>;
	to_entity?: InputMaybe<Entity_Filter>;
	type?: InputMaybe<Relationship_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Relationship_Mutated = {
	__typename?: 'relationship_mutated';
	data?: Maybe<Relationship>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Relationship_Type = {
	__typename?: 'relationship_type';
	from_entity_type?: Maybe<Relationship_Entity_Type>;
	/** This should be in the format "{from_entity_type.key}_{to_entity_type.key}". This will be populated by a hook automatically. The historical data pipeline should specify this in the same format. */
	key: Scalars['ID']['output'];
	to_entity_type?: Maybe<Relationship_Entity_Type>;
};

export type Relationship_TypeFrom_Entity_TypeArgs = {
	filter?: InputMaybe<Relationship_Entity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Relationship_TypeTo_Entity_TypeArgs = {
	filter?: InputMaybe<Relationship_Entity_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Relationship_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Relationship_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Relationship_Type_Filter>>>;
	from_entity_type?: InputMaybe<Relationship_Entity_Type_Filter>;
	key?: InputMaybe<String_Filter_Operators>;
	to_entity_type?: InputMaybe<Relationship_Entity_Type_Filter>;
};

export type Relationship_Type_Mutated = {
	__typename?: 'relationship_type_mutated';
	data?: Maybe<Relationship_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Report = {
	__typename?: 'report';
	config?: Maybe<Report_Config>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	error_message?: Maybe<Scalars['String']['output']>;
	html_file?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	params?: Maybe<Scalars['JSON']['output']>;
	params_func?: Maybe<Count_Functions>;
	pdf_file?: Maybe<Directus_Files>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type ReportConfigArgs = {
	filter?: InputMaybe<Report_Config_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ReportHtml_FileArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ReportPdf_FileArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ReportUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type ReportUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_Config = {
	__typename?: 'report_config';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	folder_for_uploads?: Maybe<Directus_Folders>;
	id: Scalars['ID']['output'];
	params?: Maybe<Array<Maybe<Report_Config_Report_Param>>>;
	params_func?: Maybe<Count_Functions>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	/** The type of the report (i.e. artist, artwork etc.). Should match the filename of the jupyter notebook uysed to generate the report. */
	type: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Report_ConfigFolder_For_UploadsArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_ConfigParamsArgs = {
	filter?: InputMaybe<Report_Config_Report_Param_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_ConfigUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_ConfigUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_Config_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Report_Config_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Report_Config_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	folder_for_uploads?: InputMaybe<Directus_Folders_Filter>;
	id?: InputMaybe<String_Filter_Operators>;
	params?: InputMaybe<Report_Config_Report_Param_Filter>;
	params_func?: InputMaybe<Count_Function_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Report_Config_Mutated = {
	__typename?: 'report_config_mutated';
	data?: Maybe<Report_Config>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Report_Config_Report_Param = {
	__typename?: 'report_config_report_param';
	id: Scalars['ID']['output'];
	report_config_id?: Maybe<Report_Config>;
	report_param_id?: Maybe<Report_Param>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Report_Config_Report_ParamReport_Config_IdArgs = {
	filter?: InputMaybe<Report_Config_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_Config_Report_ParamReport_Param_IdArgs = {
	filter?: InputMaybe<Report_Param_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_Config_Report_Param_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Report_Config_Report_Param_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Report_Config_Report_Param_Filter>>>;
	id?: InputMaybe<Number_Filter_Operators>;
	report_config_id?: InputMaybe<Report_Config_Filter>;
	report_param_id?: InputMaybe<Report_Param_Filter>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Report_Config_Report_Param_Mutated = {
	__typename?: 'report_config_report_param_mutated';
	data?: Maybe<Report_Config_Report_Param>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Report_Mutated = {
	__typename?: 'report_mutated';
	data?: Maybe<Report>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Report_Param = {
	__typename?: 'report_param';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** A description of what the parameter does, which can be used for reference and on the web interface where the parameters are entered. */
	description?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	/** What to show on the front end for this param. */
	label?: Maybe<Scalars['String']['output']>;
	param_name: Scalars['String']['output'];
	/** This controls whether the param is required, which is used by the FE when showing the form to submit the report type */
	required: Scalars['Boolean']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Report_ParamUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_ParamUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Report_Param_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Report_Param_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Report_Param_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	label?: InputMaybe<String_Filter_Operators>;
	param_name?: InputMaybe<String_Filter_Operators>;
	required?: InputMaybe<Boolean_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Report_Param_Mutated = {
	__typename?: 'report_param_mutated';
	data?: Maybe<Report_Param>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Server_Info = {
	__typename?: 'server_info';
	project?: Maybe<Server_Info_Project>;
	queryLimit?: Maybe<Server_Info_Query_Limit>;
	rateLimit?: Maybe<Scalars['Boolean']['output']>;
	rateLimitGlobal?: Maybe<Scalars['Boolean']['output']>;
	websocket?: Maybe<Scalars['Boolean']['output']>;
};

export type Server_Info_Project = {
	__typename?: 'server_info_project';
	custom_css?: Maybe<Scalars['String']['output']>;
	default_language?: Maybe<Scalars['String']['output']>;
	project_color?: Maybe<Scalars['String']['output']>;
	project_descriptor?: Maybe<Scalars['String']['output']>;
	project_logo?: Maybe<Scalars['String']['output']>;
	project_name?: Maybe<Scalars['String']['output']>;
	public_background?: Maybe<Scalars['String']['output']>;
	public_foreground?: Maybe<Scalars['String']['output']>;
	public_note?: Maybe<Scalars['String']['output']>;
	public_registration?: Maybe<Scalars['Boolean']['output']>;
	public_registration_verify_email?: Maybe<Scalars['Boolean']['output']>;
};

export type Server_Info_Query_Limit = {
	__typename?: 'server_info_query_limit';
	default?: Maybe<Scalars['Int']['output']>;
	max?: Maybe<Scalars['Int']['output']>;
};

export type Status = {
	__typename?: 'status';
	/** The value of this is what is used in the Archived and Unarchived values on the settings for a collection since this is the primary key. Everything should be set to Published by default. The use case is that if there is an artwork or an artist you want to delete, you can set it to Archived instead of deleting it. */
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Status_Mutated = {
	__typename?: 'status_mutated';
	data?: Maybe<Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type String_Filter_Operators = {
	_contains?: InputMaybe<Scalars['String']['input']>;
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_ends_with?: InputMaybe<Scalars['String']['input']>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_icontains?: InputMaybe<Scalars['String']['input']>;
	_iends_with?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_istarts_with?: InputMaybe<Scalars['String']['input']>;
	_ncontains?: InputMaybe<Scalars['String']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nends_with?: InputMaybe<Scalars['String']['input']>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_niends_with?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nistarts_with?: InputMaybe<Scalars['String']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_nstarts_with?: InputMaybe<Scalars['String']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
	_starts_with?: InputMaybe<Scalars['String']['input']>;
};

export type Tag = {
	__typename?: 'tag';
	status?: Maybe<Status>;
	tag: Scalars['ID']['output'];
	type?: Maybe<Tag_Type>;
};

export type TagStatusArgs = {
	filter?: InputMaybe<Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type TagTypeArgs = {
	filter?: InputMaybe<Tag_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Tag_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Tag_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Tag_Filter>>>;
	status?: InputMaybe<Status_Filter>;
	tag?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<Tag_Type_Filter>;
};

export type Tag_Mutated = {
	__typename?: 'tag_mutated';
	data?: Maybe<Tag>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Tag_Type = {
	__typename?: 'tag_type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Tag_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Tag_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Tag_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Tag_Type_Mutated = {
	__typename?: 'tag_type_mutated';
	data?: Maybe<Tag_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Time_Function_Filter_Operators = {
	hour?: InputMaybe<Number_Filter_Operators>;
	minute?: InputMaybe<Number_Filter_Operators>;
	second?: InputMaybe<Number_Filter_Operators>;
};

export type Time_Functions = {
	__typename?: 'time_functions';
	hour?: Maybe<Scalars['Int']['output']>;
	minute?: Maybe<Scalars['Int']['output']>;
	second?: Maybe<Scalars['Int']['output']>;
};

export type Update_Directus_Collections_Input = {
	meta?: InputMaybe<Directus_Collections_Meta_Input>;
};

export type Update_Directus_Dashboards_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	panels?: InputMaybe<Array<InputMaybe<Update_Directus_Panels_Input>>>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Extensions_InputInput = {
	meta?: InputMaybe<Update_Directus_Extensions_Input_MetaInput>;
};

export type Update_Directus_Extensions_Input_MetaInput = {
	enabled?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Update_Directus_Fields_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	field?: InputMaybe<Scalars['String']['input']>;
	meta?: InputMaybe<Directus_Fields_Meta_Input>;
	schema?: InputMaybe<Directus_Fields_Schema_Input>;
	type?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Directus_Files_Input = {
	charset?: InputMaybe<Scalars['String']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	duration?: InputMaybe<Scalars['Int']['input']>;
	embed?: InputMaybe<Scalars['String']['input']>;
	filename_disk?: InputMaybe<Scalars['String']['input']>;
	filename_download?: InputMaybe<Scalars['String']['input']>;
	filesize?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	focal_point_x?: InputMaybe<Scalars['Int']['input']>;
	focal_point_y?: InputMaybe<Scalars['Int']['input']>;
	folder?: InputMaybe<Update_Directus_Folders_Input>;
	height?: InputMaybe<Scalars['Int']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	metadata?: InputMaybe<Scalars['JSON']['input']>;
	modified_by?: InputMaybe<Update_Directus_Users_Input>;
	modified_on?: InputMaybe<Scalars['Date']['input']>;
	storage?: InputMaybe<Scalars['String']['input']>;
	tags?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	tus_data?: InputMaybe<Scalars['JSON']['input']>;
	tus_id?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	uploaded_by?: InputMaybe<Update_Directus_Users_Input>;
	uploaded_on?: InputMaybe<Scalars['Date']['input']>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Update_Directus_Flows_Input = {
	accountability?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	operation?: InputMaybe<Update_Directus_Operations_Input>;
	operations?: InputMaybe<Array<InputMaybe<Update_Directus_Operations_Input>>>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	trigger?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Folders_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	parent?: InputMaybe<Update_Directus_Folders_Input>;
};

export type Update_Directus_Notifications_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item?: InputMaybe<Scalars['String']['input']>;
	message?: InputMaybe<Scalars['String']['input']>;
	recipient?: InputMaybe<Update_Directus_Users_Input>;
	sender?: InputMaybe<Update_Directus_Users_Input>;
	status?: InputMaybe<Scalars['String']['input']>;
	subject?: InputMaybe<Scalars['String']['input']>;
	timestamp?: InputMaybe<Scalars['Date']['input']>;
};

export type Update_Directus_Operations_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	flow?: InputMaybe<Update_Directus_Flows_Input>;
	id?: InputMaybe<Scalars['ID']['input']>;
	key?: InputMaybe<Scalars['String']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x?: InputMaybe<Scalars['Int']['input']>;
	position_y?: InputMaybe<Scalars['Int']['input']>;
	reject?: InputMaybe<Update_Directus_Operations_Input>;
	resolve?: InputMaybe<Update_Directus_Operations_Input>;
	type?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Panels_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	dashboard?: InputMaybe<Update_Directus_Dashboards_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	height?: InputMaybe<Scalars['Int']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x?: InputMaybe<Scalars['Int']['input']>;
	position_y?: InputMaybe<Scalars['Int']['input']>;
	show_header?: InputMaybe<Scalars['Boolean']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Update_Directus_Permissions_Input = {
	action?: InputMaybe<Scalars['String']['input']>;
	collection?: InputMaybe<Scalars['String']['input']>;
	fields?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	id?: InputMaybe<Scalars['ID']['input']>;
	permissions?: InputMaybe<Scalars['JSON']['input']>;
	presets?: InputMaybe<Scalars['JSON']['input']>;
	role?: InputMaybe<Update_Directus_Roles_Input>;
	validation?: InputMaybe<Scalars['JSON']['input']>;
};

export type Update_Directus_Presets_Input = {
	bookmark?: InputMaybe<Scalars['String']['input']>;
	collection?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	filter?: InputMaybe<Scalars['JSON']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	layout?: InputMaybe<Scalars['String']['input']>;
	layout_options?: InputMaybe<Scalars['JSON']['input']>;
	layout_query?: InputMaybe<Scalars['JSON']['input']>;
	refresh_interval?: InputMaybe<Scalars['Int']['input']>;
	role?: InputMaybe<Update_Directus_Roles_Input>;
	search?: InputMaybe<Scalars['String']['input']>;
	user?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Relations_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	field?: InputMaybe<Scalars['String']['input']>;
	meta?: InputMaybe<Directus_Relations_Meta_Input>;
	related_collection?: InputMaybe<Scalars['String']['input']>;
	schema?: InputMaybe<Directus_Relations_Schema_Input>;
};

export type Update_Directus_Roles_Input = {
	admin_access?: InputMaybe<Scalars['Boolean']['input']>;
	app_access?: InputMaybe<Scalars['Boolean']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	enforce_tfa?: InputMaybe<Scalars['Boolean']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	ip_access?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	name?: InputMaybe<Scalars['String']['input']>;
	users?: InputMaybe<Array<InputMaybe<Update_Directus_Users_Input>>>;
};

export type Update_Directus_Settings_Input = {
	auth_login_attempts?: InputMaybe<Scalars['Int']['input']>;
	auth_password_policy?: InputMaybe<Scalars['String']['input']>;
	basemaps?: InputMaybe<Scalars['JSON']['input']>;
	custom_aspect_ratios?: InputMaybe<Scalars['JSON']['input']>;
	custom_css?: InputMaybe<Scalars['String']['input']>;
	default_appearance?: InputMaybe<Scalars['String']['input']>;
	default_language?: InputMaybe<Scalars['String']['input']>;
	default_theme_dark?: InputMaybe<Scalars['String']['input']>;
	default_theme_light?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	mapbox_key?: InputMaybe<Scalars['String']['input']>;
	module_bar?: InputMaybe<Scalars['JSON']['input']>;
	/** $t:field_options.directus_settings.project_color_note */
	project_color?: InputMaybe<Scalars['String']['input']>;
	project_descriptor?: InputMaybe<Scalars['String']['input']>;
	project_logo?: InputMaybe<Update_Directus_Files_Input>;
	project_name?: InputMaybe<Scalars['String']['input']>;
	project_url?: InputMaybe<Scalars['String']['input']>;
	public_background?: InputMaybe<Update_Directus_Files_Input>;
	public_favicon?: InputMaybe<Update_Directus_Files_Input>;
	public_foreground?: InputMaybe<Update_Directus_Files_Input>;
	public_note?: InputMaybe<Scalars['String']['input']>;
	/** $t:fields.directus_settings.public_registration_note */
	public_registration?: InputMaybe<Scalars['Boolean']['input']>;
	/** $t:fields.directus_settings.public_registration_email_filter_note */
	public_registration_email_filter?: InputMaybe<Scalars['JSON']['input']>;
	public_registration_role?: InputMaybe<Update_Directus_Roles_Input>;
	/** $t:fields.directus_settings.public_registration_verify_email_note */
	public_registration_verify_email?: InputMaybe<Scalars['Boolean']['input']>;
	report_bug_url?: InputMaybe<Scalars['String']['input']>;
	report_error_url?: InputMaybe<Scalars['String']['input']>;
	report_feature_url?: InputMaybe<Scalars['String']['input']>;
	storage_asset_presets?: InputMaybe<Scalars['JSON']['input']>;
	storage_asset_transform?: InputMaybe<Scalars['String']['input']>;
	storage_default_folder?: InputMaybe<Update_Directus_Folders_Input>;
	theme_dark_overrides?: InputMaybe<Scalars['JSON']['input']>;
	theme_light_overrides?: InputMaybe<Scalars['JSON']['input']>;
};

export type Update_Directus_Shares_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: InputMaybe<Scalars['Int']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: InputMaybe<Scalars['Hash']['input']>;
	role?: InputMaybe<Update_Directus_Roles_Input>;
	times_used?: InputMaybe<Scalars['Int']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Translations_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	key?: InputMaybe<Scalars['String']['input']>;
	language?: InputMaybe<Scalars['String']['input']>;
	value?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Directus_Users_Input = {
	appearance?: InputMaybe<Scalars['String']['input']>;
	auth_data?: InputMaybe<Scalars['JSON']['input']>;
	avatar?: InputMaybe<Update_Directus_Files_Input>;
	description?: InputMaybe<Scalars['String']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	email_notifications?: InputMaybe<Scalars['Boolean']['input']>;
	external_identifier?: InputMaybe<Scalars['String']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	language?: InputMaybe<Scalars['String']['input']>;
	last_access?: InputMaybe<Scalars['Date']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	last_page?: InputMaybe<Scalars['String']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	password?: InputMaybe<Scalars['Hash']['input']>;
	provider?: InputMaybe<Scalars['String']['input']>;
	role?: InputMaybe<Update_Directus_Roles_Input>;
	status?: InputMaybe<Scalars['String']['input']>;
	tags?: InputMaybe<Scalars['JSON']['input']>;
	tfa_secret?: InputMaybe<Scalars['Hash']['input']>;
	theme_dark?: InputMaybe<Scalars['String']['input']>;
	theme_dark_overrides?: InputMaybe<Scalars['JSON']['input']>;
	theme_light?: InputMaybe<Scalars['String']['input']>;
	theme_light_overrides?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	token?: InputMaybe<Scalars['Hash']['input']>;
};

export type Update_Directus_Versions_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	hash?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item?: InputMaybe<Scalars['String']['input']>;
	key?: InputMaybe<Scalars['String']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
	user_updated?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Webhooks_Input = {
	actions?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	collections?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	data?: InputMaybe<Scalars['Boolean']['input']>;
	headers?: InputMaybe<Scalars['JSON']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	method?: InputMaybe<Scalars['String']['input']>;
	migrated_flow?: InputMaybe<Update_Directus_Flows_Input>;
	name?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	url?: InputMaybe<Scalars['String']['input']>;
	was_active_before_deprecation?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Users_Me_Tfa_Generate_Data = {
	__typename?: 'users_me_tfa_generate_data';
	otpauth_url?: Maybe<Scalars['String']['output']>;
	secret?: Maybe<Scalars['String']['output']>;
};

export type Validation_Error = {
	__typename?: 'validation_error';
	column?: Maybe<Scalars['String']['output']>;
	error_description?: Maybe<Scalars['String']['output']>;
	error_id: Scalars['ID']['output'];
	table?: Maybe<Scalars['String']['output']>;
	validation_error_event?: Maybe<Array<Maybe<Validation_Error_Event>>>;
	validation_error_event_func?: Maybe<Count_Functions>;
};

export type Validation_ErrorValidation_Error_EventArgs = {
	filter?: InputMaybe<Validation_Error_Event_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Validation_Error_Event = {
	__typename?: 'validation_error_event';
	error_id?: Maybe<Validation_Error>;
	event_id: Scalars['ID']['output'];
	master_record_id?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Validation_Status>;
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
};

export type Validation_Error_EventError_IdArgs = {
	filter?: InputMaybe<Validation_Error_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Validation_Error_EventStatusArgs = {
	filter?: InputMaybe<Validation_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Validation_Error_Event_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Validation_Error_Event_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Validation_Error_Event_Filter>>>;
	error_id?: InputMaybe<Validation_Error_Filter>;
	event_id?: InputMaybe<String_Filter_Operators>;
	master_record_id?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Validation_Status_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
};

export type Validation_Error_Event_Mutated = {
	__typename?: 'validation_error_event_mutated';
	data?: Maybe<Validation_Error_Event>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Validation_Error_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Validation_Error_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Validation_Error_Filter>>>;
	column?: InputMaybe<String_Filter_Operators>;
	error_description?: InputMaybe<String_Filter_Operators>;
	error_id?: InputMaybe<String_Filter_Operators>;
	table?: InputMaybe<String_Filter_Operators>;
	validation_error_event?: InputMaybe<Validation_Error_Event_Filter>;
	validation_error_event_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Validation_Error_Mutated = {
	__typename?: 'validation_error_mutated';
	data?: Maybe<Validation_Error>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Validation_Status = {
	__typename?: 'validation_status';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
};

export type Validation_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Validation_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Validation_Status_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Validation_Status_Mutated = {
	__typename?: 'validation_status_mutated';
	data?: Maybe<Validation_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Win_Type = {
	__typename?: 'win_type';
	description?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Win_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Win_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Win_Type_Filter>>>;
	description?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Win_Type_Mutated = {
	__typename?: 'win_type_mutated';
	data?: Maybe<Win_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};
