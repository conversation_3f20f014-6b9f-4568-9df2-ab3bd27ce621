import { gql } from 'graphql-tag';

export const MUTATION = gql`
	mutation updateFairItem($id: ID!, $data: update_fair_input!) {
		update_fair_item(id: $id, data: $data) {
			id
			title
			fair_url
			start_date
			end_date
			venue_address_1
			venue_address_2
			venue_address_3
			venue_post_code
			venue_country {
				short_code
				code
				name
			}
			venue_city {
				name
				code
				short_code
				country {
					code
					name
					short_code
				}
			}
			exhibitors {
				id
				entity {
					id
					name
					organisation {
						name
						type {
							organisation_type_key {
								key
								name
							}
						}
						location {
							code
							name
							short_code
							country {
								name
								code
								short_code
							}
							country_nationality
							type {
								key
							}
						}
					}
				}
				artwork_listings {
					id
				}
			}
			fair_organisation {
				id
				organisation {
					id
					name
					location {
						code
						name
						short_code
						country {
							name
							code
							short_code
						}
						country_nationality
						type {
							key
						}
					}
					type {
						organisation_type_key {
							key
							name
						}
					}
				}
			}
		}
	}
`;
