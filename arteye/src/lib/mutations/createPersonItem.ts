import { gql } from 'graphql-tag';

export const MUTATION = gql`
	mutation createPersonItem($data: create_person_input!) {
		create_person_item(data: $data) {
			id
			first_name
			last_name
			middle_name
			preferred_name
			year_birth
			year_death
			biography
			net_worth_usd
			industry
			job_title
			gender {
				name
				key
			}

			type {
				id
				person_type_key {
					key
					name
				}
			}

			nationalities {
				country {
					code
					name
					short_code
					country_nationality
				}
			}
			entity {
				id
				name
				type {
					key
				}
				addresses {
					id
					date_created
					date_updated
					timestamp
					line_1
					line_2
					line_3
					post_code
					city {
						code
						name
						short_code
						country_nationality
					}
					country {
						code
						name
						short_code
						country_nationality
					}
				}

				contact_details {
					id
					value
					type {
						name
						key
					}
				}
			}
		}
	}
`;
