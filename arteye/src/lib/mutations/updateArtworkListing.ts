import { gql } from 'graphql-tag';

export const MUTATION = gql`
	mutation updateArtworkListing(
		$id: ID!
		$data: update_artwork_listing_input!
	) {
		update_artwork_listing_item(id: $id, data: $data) {
			id

			price_high_estimate {
				id
				usd_amount
				conversion_timestamp
				amount
			}
			price_low_estimate {
				id
				usd_amount
				conversion_timestamp
				amount
			}
			known_price {
				id
				usd_amount
				conversion_timestamp
				amount
			}
			sale_amount {
				id
				usd_amount
				conversion_timestamp
				amount
			}
		}
	}
`;
