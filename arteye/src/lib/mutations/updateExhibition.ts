import { gql } from 'graphql-tag';

export const MUTATION = gql`
	mutation updateExhibitionItem($id: ID!, $data: update_exhibition_input!) {
		update_exhibition_item(id: $id, data: $data) {
			id
			start_date
			end_date
			exhibition_url
			title
			description
			venue_address_1
			venue_address_2
			venue_address_3
			venue_post_code
			venue_country {
				short_code
				code
				name
			}
			venue_city {
				name
				code
				short_code
				country {
					code
					name
					short_code
				}
			}
			organisers {
				id
				entity_id {
					id
					name
					type {
						key
					}
					artist {
						id
					}
					organisation {
						id
					}
					person {
						id
					}
					addresses {
						city {
							name
						}
						country {
							name
						}
					}
				}
			}

			attributes {
				id
				type {
					key
					name
				}
				exhibition {
					id
				}
			}
		}
	}
`;
