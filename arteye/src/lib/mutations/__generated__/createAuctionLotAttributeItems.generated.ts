import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateAuctionLotAttributeItemsMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Create_Auction_Lot_Attribute_Input>
		| Types.Create_Auction_Lot_Attribute_Input
	>;
}>;

export type CreateAuctionLotAttributeItemsMutation = {
	__typename?: 'Mutation';
	create_auction_lot_attribute_items: Array<{
		__typename?: 'auction_lot_attribute';
		id: string;
	}>;
};

export const CreateAuctionLotAttributeItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createAuctionLotAttributeItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'create_auction_lot_attribute_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_auction_lot_attribute_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateAuctionLotAttributeItemsMutation,
	CreateAuctionLotAttributeItemsMutationVariables
>;
