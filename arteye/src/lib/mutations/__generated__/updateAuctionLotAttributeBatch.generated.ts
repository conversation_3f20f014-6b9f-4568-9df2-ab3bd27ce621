import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateAuctionLotAttributeBatchMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Update_Auction_Lot_Attribute_Input>
		| Types.Update_Auction_Lot_Attribute_Input
	>;
}>;

export type UpdateAuctionLotAttributeBatchMutation = {
	__typename?: 'Mutation';
	update_auction_lot_attribute_batch: Array<{
		__typename?: 'auction_lot_attribute';
		id: string;
	}>;
};

export const UpdateAuctionLotAttributeBatchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateAuctionLotAttributeBatch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'update_auction_lot_attribute_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_auction_lot_attribute_batch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateAuctionLotAttributeBatchMutation,
	UpdateAuctionLotAttributeBatchMutationVariables
>;
