import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdatePersonItemMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Person_Input;
}>;

export type UpdatePersonItemMutation = {
	__typename?: 'Mutation';
	update_person_item?: {
		__typename?: 'person';
		id: string;
		first_name?: string | null;
		last_name?: string | null;
		middle_name?: string | null;
		preferred_name?: string | null;
		year_birth?: number | null;
		year_death?: number | null;
		biography?: string | null;
		net_worth_usd?: number | null;
		industry?: string | null;
		job_title?: string | null;
		gender?: { __typename?: 'gender'; name: string; key: string } | null;
		type?: Array<{
			__typename?: 'person_person_type';
			id: string;
			person_type_key?: {
				__typename?: 'person_type';
				key: string;
				name?: string | null;
			} | null;
		} | null> | null;
		nationalities?: Array<{
			__typename?: 'person_nationality';
			country?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
				country_nationality?: string | null;
			} | null;
		} | null> | null;
		entity?: {
			__typename?: 'entity';
			id: string;
			name: string;
			type?: { __typename?: 'entity_type'; key: string } | null;
			addresses?: Array<{
				__typename?: 'entity_address';
				id: string;
				date_created?: any | null;
				date_updated?: any | null;
				timestamp?: any | null;
				line_1?: string | null;
				line_2?: string | null;
				line_3?: string | null;
				post_code?: string | null;
				city?: {
					__typename?: 'location';
					code: string;
					name?: string | null;
					short_code?: string | null;
					country_nationality?: string | null;
				} | null;
				country?: {
					__typename?: 'location';
					code: string;
					name?: string | null;
					short_code?: string | null;
					country_nationality?: string | null;
				} | null;
			} | null> | null;
			contact_details?: Array<{
				__typename?: 'entity_contact_detail';
				id: string;
				value: string;
				type?: {
					__typename?: 'entity_contact_detail_type';
					name?: string | null;
					key: string;
				} | null;
			} | null> | null;
		} | null;
	} | null;
};

export const UpdatePersonItemDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updatePersonItem' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_person_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_person_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'middle_name' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'preferred_name' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'year_birth' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'year_death' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'biography' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'net_worth_usd' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'industry' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'job_title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'gender' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person_type_key' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'nationalities' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'entity' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'addresses' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'date_created' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'date_updated' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'timestamp' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'line_1' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'line_2' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'line_3' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'post_code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'city' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'contact_details' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'value' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdatePersonItemMutation,
	UpdatePersonItemMutationVariables
>;
