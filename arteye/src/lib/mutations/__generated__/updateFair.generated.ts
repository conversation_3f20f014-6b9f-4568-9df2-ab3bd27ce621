import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateFairItemMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Fair_Input;
}>;

export type UpdateFairItemMutation = {
	__typename?: 'Mutation';
	update_fair_item?: {
		__typename?: 'fair';
		id: string;
		title: string;
		fair_url?: string | null;
		start_date?: any | null;
		end_date?: any | null;
		venue_address_1?: string | null;
		venue_address_2?: string | null;
		venue_address_3?: string | null;
		venue_post_code?: string | null;
		venue_country?: {
			__typename?: 'location';
			short_code?: string | null;
			code: string;
			name?: string | null;
		} | null;
		venue_city?: {
			__typename?: 'location';
			name?: string | null;
			code: string;
			short_code?: string | null;
			country?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
			} | null;
		} | null;
		exhibitors?: Array<{
			__typename?: 'fair_exhibitor';
			id: string;
			entity?: {
				__typename?: 'entity';
				id: string;
				name: string;
				organisation?: {
					__typename?: 'organisation';
					name: string;
					type?: Array<{
						__typename?: 'organisation_organisation_type';
						organisation_type_key?: {
							__typename?: 'organisation_type';
							key: string;
							name: string;
						} | null;
					} | null> | null;
					location?: {
						__typename?: 'location';
						code: string;
						name?: string | null;
						short_code?: string | null;
						country_nationality?: string | null;
						country?: {
							__typename?: 'location';
							name?: string | null;
							code: string;
							short_code?: string | null;
						} | null;
						type?: { __typename?: 'location_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			artwork_listings?: Array<{
				__typename?: 'fair_artwork_listing';
				id: string;
			} | null> | null;
		} | null> | null;
		fair_organisation?: {
			__typename?: 'fair_organisation';
			id: string;
			organisation?: {
				__typename?: 'organisation';
				id: string;
				name: string;
				location?: {
					__typename?: 'location';
					code: string;
					name?: string | null;
					short_code?: string | null;
					country_nationality?: string | null;
					country?: {
						__typename?: 'location';
						name?: string | null;
						code: string;
						short_code?: string | null;
					} | null;
					type?: { __typename?: 'location_type'; key: string } | null;
				} | null;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						key: string;
						name: string;
					} | null;
				} | null> | null;
			} | null;
		} | null;
	} | null;
};

export const UpdateFairItemDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateFairItem' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_fair_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_fair_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'fair_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'start_date' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'end_date' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_1' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_2' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_3' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_post_code' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_country' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_city' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'exhibitors' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation_type_key',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'short_code',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_listings' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'fair_organisation' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateFairItemMutation,
	UpdateFairItemMutationVariables
>;
