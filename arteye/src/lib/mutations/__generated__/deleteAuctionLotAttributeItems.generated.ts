import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type DeleteAuctionLotAttributeItemsMutationVariables = Types.Exact<{
	ids:
		| Array<Types.InputMaybe<Types.Scalars['ID']['input']>>
		| Types.InputMaybe<Types.Scalars['ID']['input']>;
}>;

export type DeleteAuctionLotAttributeItemsMutation = {
	__typename?: 'Mutation';
	delete_auction_lot_attribute_items?: {
		__typename?: 'delete_many';
		ids: Array<string | null>;
	} | null;
};

export const DeleteAuctionLotAttributeItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'deleteAuctionLotAttributeItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'delete_auction_lot_attribute_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'ids' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	DeleteAuctionLotAttributeItemsMutation,
	DeleteAuctionLotAttributeItemsMutationVariables
>;
