import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateArtworkListingMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Artwork_Listing_Input;
}>;

export type UpdateArtworkListingMutation = {
	__typename?: 'Mutation';
	update_artwork_listing_item?: {
		__typename?: 'artwork_listing';
		id: string;
		price_high_estimate?: {
			__typename?: 'currency_amount';
			id: string;
			usd_amount: number;
			conversion_timestamp?: any | null;
			amount: number;
		} | null;
		price_low_estimate?: {
			__typename?: 'currency_amount';
			id: string;
			usd_amount: number;
			conversion_timestamp?: any | null;
			amount: number;
		} | null;
		known_price?: {
			__typename?: 'currency_amount';
			id: string;
			usd_amount: number;
			conversion_timestamp?: any | null;
			amount: number;
		} | null;
		sale_amount?: {
			__typename?: 'currency_amount';
			id: string;
			usd_amount: number;
			conversion_timestamp?: any | null;
			amount: number;
		} | null;
	} | null;
};

export const UpdateArtworkListingDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateArtworkListing' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_artwork_listing_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_artwork_listing_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_high_estimate' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'conversion_timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_low_estimate' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'conversion_timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'known_price' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'conversion_timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_amount' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'conversion_timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateArtworkListingMutation,
	UpdateArtworkListingMutationVariables
>;
