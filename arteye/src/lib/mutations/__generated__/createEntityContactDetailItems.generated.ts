import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateEntityContactDetailItemsMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Create_Entity_Contact_Detail_Input>
		| Types.Create_Entity_Contact_Detail_Input
	>;
}>;

export type CreateEntityContactDetailItemsMutation = {
	__typename?: 'Mutation';
	create_entity_contact_detail_items: Array<{
		__typename?: 'entity_contact_detail';
		id: string;
		value: string;
		type?: {
			__typename?: 'entity_contact_detail_type';
			key: string;
			name?: string | null;
		} | null;
	}>;
};

export const CreateEntityContactDetailItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createEntityContactDetailItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'create_entity_contact_detail_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_entity_contact_detail_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'value' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateEntityContactDetailItemsMutation,
	CreateEntityContactDetailItemsMutationVariables
>;
