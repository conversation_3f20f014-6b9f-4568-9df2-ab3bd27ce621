import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateArtworkActivityMutationVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	data: Types.Update_Artwork_Activity_Input;
}>;

export type UpdateArtworkActivityMutation = {
	__typename?: 'Mutation';
	update_artwork_activity_item?: {
		__typename?: 'artwork_activity';
		id: string;
		artwork_listing?: Array<{
			__typename?: 'artwork_listing';
			auction_lot?: {
				__typename?: 'auction_lot';
				id: string;
				hammer_timestamp?: any | null;
				bids?: Array<{
					__typename?: 'auction_bid';
					id: string;
					notes?: string | null;
					date_created?: any | null;
					date_updated?: any | null;
					timestamp: any;
					user_created?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					user_updated?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					bidder?: {
						__typename?: 'auction_lot_bidder';
						id: string;
						notes?: string | null;
						location_number?: number | null;
						bidder?: {
							__typename?: 'entity';
							id: string;
							name: string;
							person?: {
								__typename?: 'person';
								id: string;
								first_name?: string | null;
								last_name?: string | null;
							} | null;
						} | null;
						bidder_type?: {
							__typename?: 'auction_bidder_type';
							name: string;
							key: string;
						} | null;
						client?: {
							__typename?: 'auction_client';
							id: string;
							paddle_number?: string | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						conversion_timestamp?: any | null;
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null> | null;
				archived_bids?: Array<{
					__typename?: 'auction_bid';
					id: string;
					notes?: string | null;
					date_created?: any | null;
					date_updated?: any | null;
					timestamp: any;
					user_created?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					user_updated?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					bidder?: {
						__typename?: 'auction_lot_bidder';
						id: string;
						notes?: string | null;
						location_number?: number | null;
						bidder?: {
							__typename?: 'entity';
							id: string;
							name: string;
							person?: {
								__typename?: 'person';
								id: string;
								first_name?: string | null;
								last_name?: string | null;
							} | null;
						} | null;
						bidder_type?: {
							__typename?: 'auction_bidder_type';
							name: string;
							key: string;
						} | null;
						client?: {
							__typename?: 'auction_client';
							id: string;
							paddle_number?: string | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						conversion_timestamp?: any | null;
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
		} | null> | null;
		artworks?: Array<{
			__typename?: 'artwork_activity_artwork';
			id: string;
			edition_number?: string | null;
			edition_number_legacy?: string | null;
			edition_number_type?: {
				__typename?: 'edition_number_type';
				key: string;
				name?: string | null;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			artwork?: {
				__typename?: 'artwork';
				crid?: string | null;
				title?: string | null;
				id: string;
				activities_transferred_to?: Array<{
					__typename?: 'artwork_activity_transfer';
					from_artwork?: { __typename?: 'artwork'; id: string } | null;
				} | null> | null;
				artists?: Array<{
					__typename?: 'artwork_artist';
					artist_id?: {
						__typename?: 'artist';
						id: string;
						person?: {
							__typename?: 'person';
							year_birth?: number | null;
							year_death?: number | null;
							entity?: { __typename?: 'entity'; name: string } | null;
							nationalities?: Array<{
								__typename?: 'person_nationality';
								country?: {
									__typename?: 'location';
									name?: string | null;
									country_nationality?: string | null;
								} | null;
							} | null> | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
		} | null> | null;
		associations?: Array<{
			__typename?: 'artwork_activity_association';
			id: string;
			entity?: {
				__typename?: 'entity';
				id: string;
				name: string;
				type?: { __typename?: 'entity_type'; key: string } | null;
				person?: { __typename?: 'person'; id: string } | null;
				artist?: { __typename?: 'artist'; id: string } | null;
				organisation?: {
					__typename?: 'organisation';
					id: string;
					name: string;
				} | null;
				addresses?: Array<{
					__typename?: 'entity_address';
					city?: { __typename?: 'location'; name?: string | null } | null;
					country?: { __typename?: 'location'; name?: string | null } | null;
				} | null> | null;
			} | null;
			type?: {
				__typename?: 'artwork_activity_association_type';
				key: string;
				name: string;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
		} | null> | null;
	} | null;
};

export const UpdateArtworkActivityDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateArtworkActivity' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'update_artwork_activity_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_artwork_activity_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_lot' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'hammer_timestamp' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'bids' },
															arguments: [
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'sort' },
																	value: {
																		kind: 'ListValue',
																		values: [
																			{
																				kind: 'StringValue',
																				value: 'timestamp',
																				block: false,
																			},
																		],
																	},
																},
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'filter' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'status' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'key',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: '_neq',
																										},
																										value: {
																											kind: 'StringValue',
																											value: 'archived',
																											block: false,
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'FragmentSpread',
																		name: {
																			kind: 'Name',
																			value: 'BidFragment',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															alias: { kind: 'Name', value: 'archived_bids' },
															name: { kind: 'Name', value: 'bids' },
															arguments: [
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'sort' },
																	value: {
																		kind: 'ListValue',
																		values: [
																			{
																				kind: 'StringValue',
																				value: 'timestamp',
																				block: false,
																			},
																		],
																	},
																},
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'filter' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'status' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'key',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: '_eq',
																										},
																										value: {
																											kind: 'StringValue',
																											value: 'archived',
																											block: false,
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'FragmentSpread',
																		name: {
																			kind: 'Name',
																			value: 'BidFragment',
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: {
													kind: 'Name',
													value: 'ArtworkActivityArtworkFragment',
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'addresses' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'city' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'BidFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'auction_bid' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'bidder' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'client' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'paddle_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'amount' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'conversion_timestamp' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'amount' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'usd_amount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'full_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivityArtworkFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity_artwork' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'edition_number' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_legacy' },
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'status' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activities_transferred_to' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-date_created',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '1' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'from_artwork' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'status' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'key' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: '_neq',
																							},
																							value: {
																								kind: 'StringValue',
																								value: 'archived',
																								block: false,
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateArtworkActivityMutation,
	UpdateArtworkActivityMutationVariables
>;
