import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateSearchPresetMutationVariables = Types.Exact<{
	data: Types.Create_Search_Preset_Input;
}>;

export type CreateSearchPresetMutation = {
	__typename?: 'Mutation';
	create_search_preset_item?: {
		__typename?: 'search_preset';
		id: string;
	} | null;
};

export const CreateSearchPresetDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createSearchPreset' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_search_preset_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_search_preset_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateSearchPresetMutation,
	CreateSearchPresetMutationVariables
>;
