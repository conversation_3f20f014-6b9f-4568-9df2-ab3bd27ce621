import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateArtworkActivityAssociationItemsMutationVariables =
	Types.Exact<{
		data?: Types.InputMaybe<
			| Array<Types.Create_Artwork_Activity_Association_Input>
			| Types.Create_Artwork_Activity_Association_Input
		>;
	}>;

export type CreateArtworkActivityAssociationItemsMutation = {
	__typename?: 'Mutation';
	create_artwork_activity_association_items: Array<{
		__typename?: 'artwork_activity_association';
		id: string;
	}>;
};

export const CreateArtworkActivityAssociationItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createArtworkActivityAssociationItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'create_artwork_activity_association_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'create_artwork_activity_association_items',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateArtworkActivityAssociationItemsMutation,
	CreateArtworkActivityAssociationItemsMutationVariables
>;
