import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateCurrencyAmountBatchMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Update_Currency_Amount_Input>
		| Types.Update_Currency_Amount_Input
	>;
}>;

export type UpdateCurrencyAmountBatchMutation = {
	__typename?: 'Mutation';
	update_currency_amount_batch: Array<{
		__typename?: 'currency_amount';
		id: string;
	}>;
};

export const UpdateCurrencyAmountBatchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateCurrencyAmountBatch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'update_currency_amount_input' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_currency_amount_batch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateCurrencyAmountBatchMutation,
	UpdateCurrencyAmountBatchMutationVariables
>;
