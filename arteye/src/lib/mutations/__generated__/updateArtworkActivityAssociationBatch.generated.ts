import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateArtworkActivityAssociationBatchMutationVariables =
	Types.Exact<{
		data?: Types.InputMaybe<
			| Array<Types.Update_Artwork_Activity_Association_Input>
			| Types.Update_Artwork_Activity_Association_Input
		>;
	}>;

export type UpdateArtworkActivityAssociationBatchMutation = {
	__typename?: 'Mutation';
	update_artwork_activity_association_batch: Array<{
		__typename?: 'artwork_activity_association';
		id: string;
	}>;
};

export const UpdateArtworkActivityAssociationBatchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateArtworkActivityAssociationBatch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'update_artwork_activity_association_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'update_artwork_activity_association_batch',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateArtworkActivityAssociationBatchMutation,
	UpdateArtworkActivityAssociationBatchMutationVariables
>;
