import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateEntityContactDetailItemsMutationVariables = Types.Exact<{
	data?: Types.InputMaybe<
		| Array<Types.Update_Entity_Contact_Detail_Input>
		| Types.Update_Entity_Contact_Detail_Input
	>;
}>;

export type UpdateEntityContactDetailItemsMutation = {
	__typename?: 'Mutation';
	update_entity_contact_detail_batch: Array<{
		__typename?: 'entity_contact_detail';
		id: string;
		value: string;
		type?: {
			__typename?: 'entity_contact_detail_type';
			key: string;
			name?: string | null;
		} | null;
	}>;
};

export const UpdateEntityContactDetailItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateEntityContactDetailItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NonNullType',
							type: {
								kind: 'NamedType',
								name: {
									kind: 'Name',
									value: 'update_entity_contact_detail_input',
								},
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_entity_contact_detail_batch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'value' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateEntityContactDetailItemsMutation,
	UpdateEntityContactDetailItemsMutationVariables
>;
