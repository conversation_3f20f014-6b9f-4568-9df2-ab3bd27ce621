import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type DeleteExhibitionOrganisersItemsMutationVariables = Types.Exact<{
	ids:
		| Array<Types.InputMaybe<Types.Scalars['ID']['input']>>
		| Types.InputMaybe<Types.Scalars['ID']['input']>;
}>;

export type DeleteExhibitionOrganisersItemsMutation = {
	__typename?: 'Mutation';
	delete_exhibition_organisers_items?: {
		__typename?: 'delete_many';
		ids: Array<string | null>;
	} | null;
};

export const DeleteExhibitionOrganisersItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'deleteExhibitionOrganisersItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'delete_exhibition_organisers_items' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'ids' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	DeleteExhibitionOrganisersItemsMutation,
	DeleteExhibitionOrganisersItemsMutationVariables
>;
