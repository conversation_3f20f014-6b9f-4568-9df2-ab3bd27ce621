import { gql } from 'graphql-tag';
import { ArtworkActivityArtworkFragment } from '$lib/fragments/ArtworkActivityArtworkFragment';
import { BidFragment } from '$lib/fragments/BidFragment';

export const MUTATION = gql`
	mutation updateArtworkActivity(
		$id: ID!
		$data: update_artwork_activity_input!
	) {
		update_artwork_activity_item(id: $id, data: $data) {
			id
			artwork_listing {
				auction_lot {
					id
					hammer_timestamp
					bids(
						sort: ["timestamp"]
						filter: { status: { key: { _neq: "archived" } } }
					) {
						...BidFragment
					}
					archived_bids: bids(
						sort: ["timestamp"]
						filter: { status: { key: { _eq: "archived" } } }
					) {
						...BidFragment
					}
				}
			}
			artworks {
				...ArtworkActivityArtworkFragment
			}
			associations {
				id
				entity {
					id
					type {
						key
					}
					name
					person {
						id
					}
					artist {
						id
					}
					organisation {
						id
						name
					}
					addresses {
						city {
							name
						}
						country {
							name
						}
					}
				}
				type {
					key
					name
				}
				status {
					key
					name
				}
			}
		}
	}
	${BidFragment}
	${ArtworkActivityArtworkFragment}
`;
