import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getGalleries(
		$filter: gallery_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		gallery(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			id
			reference_id
			organisation {
				name
				year_founded
				year_dissolved

				location {
					code
					name
					short_code
					country {
						name
						code
					}
					country_nationality
					type {
						key
					}
				}

				entity {
					aggregations {
						activity_count
					}

					attributes {
						type {
							key
							name
						}
					}
				}
			}

			artist_representation {
				artist {
					person {
						entity {
							name
						}
					}
				}
			}
		}

		gallery_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
