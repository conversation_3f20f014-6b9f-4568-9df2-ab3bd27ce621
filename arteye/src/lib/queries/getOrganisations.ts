import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getOrganisations(
		$filter: organisation_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		organisation(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			id
			reference_id
			name
			type {
				organisation_type_key {
					key
					name
				}
			}

			location {
				code
				name
				short_code
				country {
					name
					code
				}
				country_nationality
				type {
					key
				}
			}

			entity {
				name

				aggregations {
					activity_count
				}
			}
		}
		organisation_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
