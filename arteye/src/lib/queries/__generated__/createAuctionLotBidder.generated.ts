import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateAuctionLotBidderMutationVariables = Types.Exact<{
	data: Types.Create_Auction_Lot_Bidder_Input;
}>;

export type CreateAuctionLotBidderMutation = {
	__typename?: 'Mutation';
	create_auction_lot_bidder_item?: {
		__typename?: 'auction_lot_bidder';
		id: string;
		location_number?: number | null;
		bidder_type?: {
			__typename?: 'auction_bidder_type';
			name: string;
			key: string;
			label: string;
		} | null;
	} | null;
};

export const CreateAuctionLotBidderDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createAuctionLotBidder' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_auction_lot_bidder_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_auction_lot_bidder_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'label' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateAuctionLotBidderMutation,
	CreateAuctionLotBidderMutationVariables
>;
