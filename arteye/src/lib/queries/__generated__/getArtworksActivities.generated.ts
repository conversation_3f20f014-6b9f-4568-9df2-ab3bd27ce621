import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtworksActivitiesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	activityStatus?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetArtworksActivitiesQuery = {
	__typename?: 'Query';
	artwork_activity: Array<{
		__typename?: 'artwork_activity';
		id: string;
		source_page_url?: string | null;
		timestamp: any;
		type?: {
			__typename?: 'artwork_activity_type';
			name: string;
			key: string;
		} | null;
		activity_status?: Array<{
			__typename?: 'artwork_activity_status';
			timestamp: any;
			artwork_activity?: {
				__typename?: 'artwork_activity';
				timestamp: any;
				type?: { __typename?: 'artwork_activity_type'; name: string } | null;
				activity_status?: Array<{
					__typename?: 'artwork_activity_status';
					type?: {
						__typename?: 'artwork_activity_status_type';
						name: string;
					} | null;
				} | null> | null;
				associations?: Array<{
					__typename?: 'artwork_activity_association';
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						organisation?: {
							__typename?: 'organisation';
							location?: {
								__typename?: 'location';
								name?: string | null;
							} | null;
						} | null;
					} | null;
				} | null> | null;
				artwork_listing?: Array<{
					__typename?: 'artwork_listing';
					auction_lot?: {
						__typename?: 'auction_lot';
						sale_amount_includes_premium?: boolean | null;
						lot_number?: string | null;
						attributes?: Array<{
							__typename?: 'auction_lot_attribute';
							type?: {
								__typename?: 'auction_lot_attribute_type';
								name: string;
								description?: string | null;
							} | null;
						} | null> | null;
						auction?: {
							__typename?: 'auction';
							sale_name?: string | null;
							auction_house?: {
								__typename?: 'auction_house';
								organisation?: {
									__typename?: 'organisation';
									name: string;
									location?: {
										__typename?: 'location';
										name?: string | null;
										country?: {
											__typename?: 'location';
											name?: string | null;
										} | null;
									} | null;
								} | null;
							} | null;
						} | null;
					} | null;
					exhibition_listing?: {
						__typename?: 'exhibition_artwork_listing';
						exhibition?: { __typename?: 'exhibition'; title: string } | null;
					} | null;
					gallery_listing?: {
						__typename?: 'gallery_artwork_listing';
						gallery?: {
							__typename?: 'gallery';
							organisation?: {
								__typename?: 'organisation';
								name: string;
							} | null;
						} | null;
					} | null;
					fair_listing?: {
						__typename?: 'fair_artwork_listing';
						fair_exhibitor?: {
							__typename?: 'fair_exhibitor';
							fair?: { __typename?: 'fair'; title: string } | null;
						} | null;
					} | null;
					listing_type?: {
						__typename?: 'artwork_listing_type';
						key: string;
						name: string;
					} | null;
					sale_amount?: {
						__typename?: 'currency_amount';
						usd_amount: number;
						amount: number;
						currency?: {
							__typename?: 'currency';
							code: string;
							symbol?: string | null;
						} | null;
					} | null;
					price_low_estimate?: {
						__typename?: 'currency_amount';
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							code: string;
							symbol?: string | null;
						} | null;
					} | null;
					price_high_estimate?: {
						__typename?: 'currency_amount';
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							code: string;
							symbol?: string | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
			status?: { __typename?: 'status'; name: string } | null;
			type?: {
				__typename?: 'artwork_activity_status_type';
				name: string;
			} | null;
		} | null> | null;
		associations?: Array<{
			__typename?: 'artwork_activity_association';
			entity?: {
				__typename?: 'entity';
				name: string;
				organisation?: {
					__typename?: 'organisation';
					location?: { __typename?: 'location'; name?: string | null } | null;
				} | null;
			} | null;
			type?: {
				__typename?: 'artwork_activity_association_type';
				name: string;
			} | null;
		} | null> | null;
		artwork_listing?: Array<{
			__typename?: 'artwork_listing';
			known_price?: {
				__typename?: 'currency_amount';
				usd_amount: number;
				amount: number;
			} | null;
			auction_lot?: {
				__typename?: 'auction_lot';
				sale_amount_includes_premium?: boolean | null;
				lot_number?: string | null;
				attributes?: Array<{
					__typename?: 'auction_lot_attribute';
					type?: {
						__typename?: 'auction_lot_attribute_type';
						name: string;
						description?: string | null;
					} | null;
				} | null> | null;
				auction?: {
					__typename?: 'auction';
					sale_name?: string | null;
					auction_house?: {
						__typename?: 'auction_house';
						organisation?: {
							__typename?: 'organisation';
							name: string;
							location?: {
								__typename?: 'location';
								name?: string | null;
								country?: {
									__typename?: 'location';
									name?: string | null;
								} | null;
							} | null;
						} | null;
					} | null;
				} | null;
			} | null;
			exhibition_listing?: {
				__typename?: 'exhibition_artwork_listing';
				exhibition?: { __typename?: 'exhibition'; title: string } | null;
			} | null;
			gallery_listing?: {
				__typename?: 'gallery_artwork_listing';
				gallery?: {
					__typename?: 'gallery';
					organisation?: { __typename?: 'organisation'; name: string } | null;
				} | null;
			} | null;
			fair_listing?: {
				__typename?: 'fair_artwork_listing';
				fair_exhibitor?: {
					__typename?: 'fair_exhibitor';
					fair?: { __typename?: 'fair'; title: string } | null;
				} | null;
			} | null;
			listing_type?: {
				__typename?: 'artwork_listing_type';
				key: string;
				name: string;
			} | null;
			sale_amount?: {
				__typename?: 'currency_amount';
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
			price_low_estimate?: {
				__typename?: 'currency_amount';
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
			price_high_estimate?: {
				__typename?: 'currency_amount';
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
		} | null> | null;
		artworks?: Array<{
			__typename?: 'artwork_activity_artwork';
			edition_number?: string | null;
			edition_number_type?: {
				__typename?: 'edition_number_type';
				name?: string | null;
			} | null;
			artwork?: {
				__typename?: 'artwork';
				id: string;
				title?: string | null;
				number_of_pieces?: number | null;
				number_of_artworks?: number | null;
				dimensions_width_cm?: number | null;
				dimensions_depth_cm?: number | null;
				dimensions_height_cm?: number | null;
				media?: string | null;
				crid?: string | null;
				execution_start_year?: number | null;
				execution_end_year?: number | null;
				tags?: Array<{
					__typename?: 'artwork_tag';
					tag_tag?: {
						__typename?: 'tag';
						tag: string;
						type?: {
							__typename?: 'tag_type';
							key: string;
							name: string;
						} | null;
					} | null;
				} | null> | null;
				edition_info?: {
					__typename?: 'edition_info';
					is_unlimited?: boolean | null;
					edition_size_total?: number | null;
					general_proof_size?: number | null;
					artists_proof_size?: number | null;
					regular_edition_size?: number | null;
					hors_de_commerce_size?: number | null;
				} | null;
				primary_image?: {
					__typename?: 'directus_files';
					id: string;
					filename_disk?: string | null;
					width?: number | null;
					height?: number | null;
				} | null;
				artists?: Array<{
					__typename?: 'artwork_artist';
					artist_id?: {
						__typename?: 'artist';
						person?: {
							__typename?: 'person';
							year_birth?: number | null;
							year_death?: number | null;
							first_name?: string | null;
							last_name?: string | null;
							nationalities?: Array<{
								__typename?: 'person_nationality';
								country?: {
									__typename?: 'location';
									name?: string | null;
								} | null;
							} | null> | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
		} | null> | null;
	}>;
	artwork_activity_aggregated: Array<{
		__typename?: 'artwork_activity_aggregated';
		count?: {
			__typename?: 'artwork_activity_aggregated_count';
			id?: number | null;
		} | null;
	}>;
	artwork_activity_ids: Array<{
		__typename?: 'artwork_activity';
		id: string;
		artworks?: Array<{
			__typename?: 'artwork_activity_artwork';
			artwork?: { __typename?: 'artwork'; id: string } | null;
		} | null> | null;
	}>;
};

export const GetArtworksActivitiesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworksActivities' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'activityStatus' },
					},
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'source_page_url' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activity_status' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'activityStatus' },
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_activity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'timestamp' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'activity_status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'associations' },
															arguments: [
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'filter' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'status' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'key',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: '_neq',
																										},
																										value: {
																											kind: 'StringValue',
																											value: 'archived',
																											block: false,
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'location',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artwork_listing' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_lot',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value:
																							'sale_amount_includes_premium',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'lot_number',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'attributes',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'type',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'description',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'auction',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'sale_name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'auction_house',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'organisation',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'location',
																														},
																														selectionSet: {
																															kind: 'SelectionSet',
																															selections: [
																																{
																																	kind: 'Field',
																																	name: {
																																		kind: 'Name',
																																		value:
																																			'name',
																																	},
																																},
																																{
																																	kind: 'Field',
																																	name: {
																																		kind: 'Name',
																																		value:
																																			'country',
																																	},
																																	selectionSet:
																																		{
																																			kind: 'SelectionSet',
																																			selections:
																																				[
																																					{
																																						kind: 'Field',
																																						name: {
																																							kind: 'Name',
																																							value:
																																								'name',
																																						},
																																					},
																																				],
																																		},
																																},
																															],
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'exhibition_listing',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'exhibition',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'title',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'gallery_listing',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'gallery',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'organisation',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'fair_listing',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'fair_exhibitor',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'fair',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'title',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'listing_type',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'sale_amount',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'currency',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'symbol',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'usd_amount',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'amount',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'price_low_estimate',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'amount',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'currency',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'symbol',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'usd_amount',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'price_high_estimate',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'amount',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'currency',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'symbol',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'usd_amount',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associations' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'known_price' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_lot' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'sale_amount_includes_premium',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'lot_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'attributes' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'description',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'sale_name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_house',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'location',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'country',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'exhibition' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'gallery_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'gallery' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fair_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'fair_exhibitor' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'fair' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'title',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'listing_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_amount' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_low_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_high_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_number_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'tags' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'tag_tag' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'tag' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'number_of_pieces' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'number_of_artworks',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'edition_info' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'is_unlimited',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'edition_size_total',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'general_proof_size',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'artists_proof_size',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'regular_edition_size',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'hors_de_commerce_size',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_width_cm',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_depth_cm',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_height_cm',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'media' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'primary_image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'crid' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'execution_start_year',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'execution_end_year',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artists' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artist_id' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'year_birth',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'year_death',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'first_name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'last_name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'nationalities',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'country',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'count' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						alias: { kind: 'Name', value: 'artwork_activity_ids' },
						name: { kind: 'Name', value: 'artwork_activity' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworksActivitiesQuery,
	GetArtworksActivitiesQueryVariables
>;
