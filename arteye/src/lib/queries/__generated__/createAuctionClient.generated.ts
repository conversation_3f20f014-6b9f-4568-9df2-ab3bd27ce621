import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type CreateAuctionClientItemMutationVariables = Types.Exact<{
	data: Types.Create_Auction_Client_Input;
}>;

export type CreateAuctionClientItemMutation = {
	__typename?: 'Mutation';
	create_auction_client_item?: {
		__typename?: 'auction_client';
		id: string;
		paddle_number?: string | null;
		auction?: { __typename?: 'auction'; id: string } | null;
		entity?: {
			__typename?: 'entity';
			id: string;
			name: string;
			type?: { __typename?: 'entity_type'; key: string } | null;
			person?: {
				__typename?: 'person';
				id: string;
				year_birth?: number | null;
				year_death?: number | null;
				type?: Array<{
					__typename?: 'person_person_type';
					person_type_key?: {
						__typename?: 'person_type';
						key: string;
						name?: string | null;
					} | null;
				} | null> | null;
				nationalities?: Array<{
					__typename?: 'person_nationality';
					country?: {
						__typename?: 'location';
						country_nationality?: string | null;
					} | null;
				} | null> | null;
			} | null;
			artist?: { __typename?: 'artist'; id: string } | null;
			organisation?: {
				__typename?: 'organisation';
				id: string;
				name: string;
			} | null;
		} | null;
	} | null;
};

export const CreateAuctionClientItemDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'createAuctionClientItem' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'create_auction_client_input' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'create_auction_client_item' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'paddle_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'entity' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'person_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationalities' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	CreateAuctionClientItemMutation,
	CreateAuctionClientItemMutationVariables
>;
