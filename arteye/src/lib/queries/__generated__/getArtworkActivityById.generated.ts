import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtworkActivityByIdQueryVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
}>;

export type GetArtworkActivityByIdQuery = {
	__typename?: 'Query';
	artwork_activity_by_id?: {
		__typename?: 'artwork_activity';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		notes?: string | null;
		timestamp: any;
		source_page_url?: string | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		type?: {
			__typename?: 'artwork_activity_type';
			key: string;
			name: string;
		} | null;
		activity_status?: Array<{
			__typename?: 'artwork_activity_status';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			timestamp: any;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			type?: {
				__typename?: 'artwork_activity_status_type';
				key: string;
				name: string;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
		} | null> | null;
		artworks?: Array<{
			__typename?: 'artwork_activity_artwork';
			id: string;
			edition_number?: string | null;
			edition_number_legacy?: string | null;
			edition_number_type?: {
				__typename?: 'edition_number_type';
				key: string;
				name?: string | null;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			artwork?: {
				__typename?: 'artwork';
				crid?: string | null;
				title?: string | null;
				id: string;
				activities_transferred_to?: Array<{
					__typename?: 'artwork_activity_transfer';
					from_artwork?: { __typename?: 'artwork'; id: string } | null;
				} | null> | null;
				artists?: Array<{
					__typename?: 'artwork_artist';
					artist_id?: {
						__typename?: 'artist';
						id: string;
						person?: {
							__typename?: 'person';
							year_birth?: number | null;
							year_death?: number | null;
							entity?: { __typename?: 'entity'; name: string } | null;
							nationalities?: Array<{
								__typename?: 'person_nationality';
								country?: {
									__typename?: 'location';
									name?: string | null;
									country_nationality?: string | null;
								} | null;
							} | null> | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
		} | null> | null;
		artwork_listing?: Array<{
			__typename?: 'artwork_listing';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			shipping?: string | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			auction_lot?: {
				__typename?: 'auction_lot';
				id: string;
				date_updated?: any | null;
				date_created?: any | null;
				video_file_name?: string | null;
				hammer_timestamp?: any | null;
				video_hammer_time_seconds?: any | null;
				starting_bid_amount?: number | null;
				sale_amount_includes_premium?: boolean | null;
				lot_number?: string | null;
				lot_notes?: string | null;
				saleroom_notice?: string | null;
				user_updated?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
				user_created?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
				auction?: {
					__typename?: 'auction';
					id: string;
					sale_name?: string | null;
					local_auction_start_date?: any | null;
					local_auction_end_date?: any | null;
					auction_start_date?: any | null;
					auction_end_date?: any | null;
					sale_number?: string | null;
					currency?: {
						__typename?: 'currency';
						code: string;
						name: string;
						symbol?: string | null;
						full_name?: string | null;
					} | null;
					auction_house?: {
						__typename?: 'auction_house';
						organisation?: { __typename?: 'organisation'; name: string } | null;
					} | null;
				} | null;
				auctioneer?: {
					__typename?: 'person';
					entity?: {
						__typename?: 'entity';
						id: string;
						person?: {
							__typename?: 'person';
							id: string;
							entity?: {
								__typename?: 'entity';
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
				} | null;
				bids?: Array<{
					__typename?: 'auction_bid';
					id: string;
					notes?: string | null;
					date_created?: any | null;
					date_updated?: any | null;
					timestamp: any;
					user_created?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					user_updated?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					bidder?: {
						__typename?: 'auction_lot_bidder';
						id: string;
						notes?: string | null;
						location_number?: number | null;
						bidder?: {
							__typename?: 'entity';
							id: string;
							name: string;
							person?: {
								__typename?: 'person';
								id: string;
								first_name?: string | null;
								last_name?: string | null;
							} | null;
						} | null;
						bidder_type?: {
							__typename?: 'auction_bidder_type';
							name: string;
							key: string;
						} | null;
						client?: {
							__typename?: 'auction_client';
							id: string;
							paddle_number?: string | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						conversion_timestamp?: any | null;
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null> | null;
				archived_bids?: Array<{
					__typename?: 'auction_bid';
					id: string;
					notes?: string | null;
					date_created?: any | null;
					date_updated?: any | null;
					timestamp: any;
					user_created?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					user_updated?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					bidder?: {
						__typename?: 'auction_lot_bidder';
						id: string;
						notes?: string | null;
						location_number?: number | null;
						bidder?: {
							__typename?: 'entity';
							id: string;
							name: string;
							person?: {
								__typename?: 'person';
								id: string;
								first_name?: string | null;
								last_name?: string | null;
							} | null;
						} | null;
						bidder_type?: {
							__typename?: 'auction_bidder_type';
							name: string;
							key: string;
						} | null;
						client?: {
							__typename?: 'auction_client';
							id: string;
							paddle_number?: string | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						conversion_timestamp?: any | null;
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null> | null;
				winning_bid?: {
					__typename?: 'auction_bid';
					id: string;
					notes?: string | null;
					date_created?: any | null;
					date_updated?: any | null;
					timestamp: any;
					user_created?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					user_updated?: {
						__typename?: 'directus_users';
						id?: string | null;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
					bidder?: {
						__typename?: 'auction_lot_bidder';
						id: string;
						notes?: string | null;
						location_number?: number | null;
						bidder?: {
							__typename?: 'entity';
							id: string;
							name: string;
							person?: {
								__typename?: 'person';
								id: string;
								first_name?: string | null;
								last_name?: string | null;
							} | null;
						} | null;
						bidder_type?: {
							__typename?: 'auction_bidder_type';
							name: string;
							key: string;
						} | null;
						client?: {
							__typename?: 'auction_client';
							id: string;
							paddle_number?: string | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
								type?: { __typename?: 'entity_type'; key: string } | null;
							} | null;
						} | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						conversion_timestamp?: any | null;
						amount: number;
						usd_amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null;
				attributes?: Array<{
					__typename?: 'auction_lot_attribute';
					id: string;
					status?: { __typename?: 'status'; key: string; name: string } | null;
					type?: {
						__typename?: 'auction_lot_attribute_type';
						key: string;
						name: string;
						description?: string | null;
					} | null;
					amount?: {
						__typename?: 'currency_amount';
						id: string;
						usd_amount: number;
						conversion_timestamp?: any | null;
						amount: number;
						currency?: {
							__typename?: 'currency';
							symbol?: string | null;
							name: string;
							code: string;
							full_name?: string | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
			exhibition_listing?: {
				__typename?: 'exhibition_artwork_listing';
				id: string;
				exhibition?: {
					__typename?: 'exhibition';
					id: string;
					title: string;
					venue_country?: {
						__typename?: 'location';
						name?: string | null;
					} | null;
					venue_city?: { __typename?: 'location'; name?: string | null } | null;
				} | null;
			} | null;
			gallery_listing?: {
				__typename?: 'gallery_artwork_listing';
				id: string;
				gallery?: {
					__typename?: 'gallery';
					id: string;
					organisation?: {
						__typename?: 'organisation';
						name: string;
						location?: { __typename?: 'location'; name?: string | null } | null;
					} | null;
				} | null;
			} | null;
			fair_listing?: {
				__typename?: 'fair_artwork_listing';
				id: string;
				fair_exhibitor?: {
					__typename?: 'fair_exhibitor';
					id: string;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						organisation?: {
							__typename?: 'organisation';
							location?: {
								__typename?: 'location';
								name?: string | null;
								country?: {
									__typename?: 'location';
									name?: string | null;
								} | null;
							} | null;
						} | null;
					} | null;
					fair?: {
						__typename?: 'fair';
						id: string;
						title: string;
						venue_country?: {
							__typename?: 'location';
							name?: string | null;
						} | null;
						venue_city?: {
							__typename?: 'location';
							name?: string | null;
						} | null;
					} | null;
				} | null;
			} | null;
			listing_type?: {
				__typename?: 'artwork_listing_type';
				name: string;
				key: string;
			} | null;
			known_price?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					full_name?: string | null;
					symbol?: string | null;
					name: string;
					code: string;
				} | null;
			} | null;
			sale_amount?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					full_name?: string | null;
					symbol?: string | null;
					name: string;
					code: string;
				} | null;
			} | null;
			price_high_estimate?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					full_name?: string | null;
					symbol?: string | null;
					name: string;
					code: string;
				} | null;
			} | null;
			price_low_estimate?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					full_name?: string | null;
					symbol?: string | null;
					name: string;
					code: string;
				} | null;
			} | null;
		} | null> | null;
		associations?: Array<{
			__typename?: 'artwork_activity_association';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			read_only?: boolean | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			entity?: {
				__typename?: 'entity';
				id: string;
				name: string;
				type?: { __typename?: 'entity_type'; key: string } | null;
				person?: {
					__typename?: 'person';
					id: string;
					year_birth?: number | null;
					year_death?: number | null;
					type?: Array<{
						__typename?: 'person_person_type';
						person_type_key?: {
							__typename?: 'person_type';
							key: string;
							name?: string | null;
						} | null;
					} | null> | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						country?: {
							__typename?: 'location';
							country_nationality?: string | null;
						} | null;
					} | null> | null;
				} | null;
				artist?: { __typename?: 'artist'; id: string } | null;
				organisation?: {
					__typename?: 'organisation';
					id: string;
					name: string;
					type?: Array<{
						__typename?: 'organisation_organisation_type';
						organisation_type_key?: {
							__typename?: 'organisation_type';
							key: string;
							name: string;
						} | null;
					} | null> | null;
					location?: { __typename?: 'location'; name?: string | null } | null;
				} | null;
				addresses?: Array<{
					__typename?: 'entity_address';
					city?: { __typename?: 'location'; name?: string | null } | null;
					country?: { __typename?: 'location'; name?: string | null } | null;
				} | null> | null;
			} | null;
			type?: {
				__typename?: 'artwork_activity_association_type';
				key: string;
				name: string;
			} | null;
		} | null> | null;
		activity_artwork_info?: {
			__typename?: 'artwork_activity_artwork_info';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			condition?: string | null;
			provenance?: string | null;
			exhibition?: string | null;
			literature?: string | null;
			ingestion_notes?: string | null;
			is_bundle?: boolean | null;
			is_full_set?: boolean | null;
			number_of_artworks?: number | null;
			raw_artwork_description?: string | null;
			series_size?: number | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			primary_image?: {
				__typename?: 'directus_files';
				id: string;
				storage: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
			} | null;
			additional_images?: Array<{
				__typename?: 'artwork_activity_image';
				id: string;
				status?: { __typename?: 'status'; key: string; name: string } | null;
				image?: {
					__typename?: 'directus_files';
					id: string;
					storage: string;
					width?: number | null;
					height?: number | null;
					filename_disk?: string | null;
					filename_download: string;
				} | null;
			} | null> | null;
			reference_files?: Array<{
				__typename?: 'artwork_activity_artwork_info_files';
				id: string;
				status?: { __typename?: 'status'; key: string; name: string } | null;
				directus_files_id?: {
					__typename?: 'directus_files';
					filename_disk?: string | null;
					filename_download: string;
					id: string;
					storage: string;
					type?: string | null;
					title?: string | null;
				} | null;
			} | null> | null;
		} | null;
	} | null;
};

export type GetArtworksActivityQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Artwork_Filter>;
}>;

export type GetArtworksActivityQuery = {
	__typename?: 'Query';
	artwork_activity_artwork: Array<{
		__typename?: 'artwork_activity_artwork';
		id: string;
		edition_number?: string | null;
		edition_number_legacy?: string | null;
		edition_number_type?: {
			__typename?: 'edition_number_type';
			key: string;
			name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		artwork?: {
			__typename?: 'artwork';
			crid?: string | null;
			title?: string | null;
			id: string;
			activities_transferred_to?: Array<{
				__typename?: 'artwork_activity_transfer';
				from_artwork?: { __typename?: 'artwork'; id: string } | null;
			} | null> | null;
			artists?: Array<{
				__typename?: 'artwork_artist';
				artist_id?: {
					__typename?: 'artist';
					id: string;
					person?: {
						__typename?: 'person';
						year_birth?: number | null;
						year_death?: number | null;
						entity?: { __typename?: 'entity'; name: string } | null;
						nationalities?: Array<{
							__typename?: 'person_nationality';
							country?: {
								__typename?: 'location';
								name?: string | null;
								country_nationality?: string | null;
							} | null;
						} | null> | null;
					} | null;
				} | null;
			} | null> | null;
		} | null;
	}>;
};

export type GetAuctionLotQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Auction_Lot_Filter>;
}>;

export type GetAuctionLotQuery = {
	__typename?: 'Query';
	auction_lot: Array<{
		__typename?: 'auction_lot';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		video_file_name?: string | null;
		hammer_timestamp?: any | null;
		video_hammer_time_seconds?: any | null;
		starting_bid_amount?: number | null;
		sale_amount_includes_premium?: boolean | null;
		lot_number?: string | null;
		lot_notes?: string | null;
		saleroom_notice?: string | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		auction?: {
			__typename?: 'auction';
			id: string;
			sale_name?: string | null;
			local_auction_end_date?: any | null;
			local_auction_start_date?: any | null;
			auction_start_date?: any | null;
			auction_end_date?: any | null;
			sale_number?: string | null;
			currency?: {
				__typename?: 'currency';
				code: string;
				name: string;
				symbol?: string | null;
				full_name?: string | null;
			} | null;
			auction_house?: {
				__typename?: 'auction_house';
				organisation?: { __typename?: 'organisation'; name: string } | null;
			} | null;
		} | null;
		auctioneer?: {
			__typename?: 'person';
			entity?: {
				__typename?: 'entity';
				id: string;
				person?: {
					__typename?: 'person';
					id: string;
					entity?: {
						__typename?: 'entity';
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
		} | null;
		bids?: Array<{
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
		archived_bids?: Array<{
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
		winning_bid?: {
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null;
		attributes?: Array<{
			__typename?: 'auction_lot_attribute';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			type?: {
				__typename?: 'auction_lot_attribute_type';
				key: string;
				name: string;
				description?: string | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				conversion_timestamp?: any | null;
				amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
	}>;
};

export type GetAssociationsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Association_Filter>;
}>;

export type GetAssociationsQuery = {
	__typename?: 'Query';
	artwork_activity_association: Array<{
		__typename?: 'artwork_activity_association';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		read_only?: boolean | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		entity?: {
			__typename?: 'entity';
			id: string;
			name: string;
			type?: { __typename?: 'entity_type'; key: string } | null;
			person?: {
				__typename?: 'person';
				id: string;
				year_birth?: number | null;
				year_death?: number | null;
				type?: Array<{
					__typename?: 'person_person_type';
					person_type_key?: {
						__typename?: 'person_type';
						key: string;
						name?: string | null;
					} | null;
				} | null> | null;
				nationalities?: Array<{
					__typename?: 'person_nationality';
					country?: {
						__typename?: 'location';
						country_nationality?: string | null;
					} | null;
				} | null> | null;
			} | null;
			artist?: { __typename?: 'artist'; id: string } | null;
			organisation?: {
				__typename?: 'organisation';
				id: string;
				name: string;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						key: string;
						name: string;
					} | null;
				} | null> | null;
				location?: { __typename?: 'location'; name?: string | null } | null;
			} | null;
			addresses?: Array<{
				__typename?: 'entity_address';
				city?: { __typename?: 'location'; name?: string | null } | null;
				country?: { __typename?: 'location'; name?: string | null } | null;
			} | null> | null;
		} | null;
		type?: {
			__typename?: 'artwork_activity_association_type';
			key: string;
			name: string;
		} | null;
	}>;
};

export type GetArtworkActivityInfoQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Artwork_Info_Filter>;
}>;

export type GetArtworkActivityInfoQuery = {
	__typename?: 'Query';
	artwork_activity_artwork_info: Array<{
		__typename?: 'artwork_activity_artwork_info';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		condition?: string | null;
		provenance?: string | null;
		exhibition?: string | null;
		literature?: string | null;
		ingestion_notes?: string | null;
		is_bundle?: boolean | null;
		is_full_set?: boolean | null;
		number_of_artworks?: number | null;
		raw_artwork_description?: string | null;
		series_size?: number | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		primary_image?: {
			__typename?: 'directus_files';
			id: string;
			storage: string;
			width?: number | null;
			height?: number | null;
			filename_disk?: string | null;
			filename_download: string;
		} | null;
		additional_images?: Array<{
			__typename?: 'artwork_activity_image';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			image?: {
				__typename?: 'directus_files';
				id: string;
				storage: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
			} | null;
		} | null> | null;
		reference_files?: Array<{
			__typename?: 'artwork_activity_artwork_info_files';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			directus_files_id?: {
				__typename?: 'directus_files';
				filename_disk?: string | null;
				filename_download: string;
				id: string;
				storage: string;
				type?: string | null;
				title?: string | null;
			} | null;
		} | null> | null;
	}>;
};

export type GetArtworkStatusQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Status_Filter>;
}>;

export type GetArtworkStatusQuery = {
	__typename?: 'Query';
	artwork_activity_status: Array<{
		__typename?: 'artwork_activity_status';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		timestamp: any;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		type?: {
			__typename?: 'artwork_activity_status_type';
			key: string;
			name: string;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
	}>;
};

export type GetArtworkActivityByIdPiecesQueryVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
	artworkFilter?: Types.InputMaybe<Types.Artwork_Activity_Artwork_Filter>;
	associationFilter?: Types.InputMaybe<Types.Artwork_Activity_Association_Filter>;
	infoFilter?: Types.InputMaybe<Types.Artwork_Activity_Artwork_Info_Filter>;
	statusFilter?: Types.InputMaybe<Types.Artwork_Activity_Status_Filter>;
	auctionFilter?: Types.InputMaybe<Types.Auction_Lot_Filter>;
}>;

export type GetArtworkActivityByIdPiecesQuery = {
	__typename?: 'Query';
	artwork_activity_by_id?: {
		__typename?: 'artwork_activity';
		notes?: string | null;
		source_page_url?: string | null;
	} | null;
	artwork_activity_artwork: Array<{
		__typename?: 'artwork_activity_artwork';
		id: string;
		edition_number?: string | null;
		edition_number_legacy?: string | null;
		edition_number_type?: {
			__typename?: 'edition_number_type';
			key: string;
			name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		artwork?: {
			__typename?: 'artwork';
			crid?: string | null;
			title?: string | null;
			id: string;
			activities_transferred_to?: Array<{
				__typename?: 'artwork_activity_transfer';
				from_artwork?: { __typename?: 'artwork'; id: string } | null;
			} | null> | null;
			artists?: Array<{
				__typename?: 'artwork_artist';
				artist_id?: {
					__typename?: 'artist';
					id: string;
					person?: {
						__typename?: 'person';
						year_birth?: number | null;
						year_death?: number | null;
						entity?: { __typename?: 'entity'; name: string } | null;
						nationalities?: Array<{
							__typename?: 'person_nationality';
							country?: {
								__typename?: 'location';
								name?: string | null;
								country_nationality?: string | null;
							} | null;
						} | null> | null;
					} | null;
				} | null;
			} | null> | null;
		} | null;
	}>;
	artwork_activity_association: Array<{
		__typename?: 'artwork_activity_association';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		read_only?: boolean | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		entity?: {
			__typename?: 'entity';
			id: string;
			name: string;
			type?: { __typename?: 'entity_type'; key: string } | null;
			person?: {
				__typename?: 'person';
				id: string;
				year_birth?: number | null;
				year_death?: number | null;
				type?: Array<{
					__typename?: 'person_person_type';
					person_type_key?: {
						__typename?: 'person_type';
						key: string;
						name?: string | null;
					} | null;
				} | null> | null;
				nationalities?: Array<{
					__typename?: 'person_nationality';
					country?: {
						__typename?: 'location';
						country_nationality?: string | null;
					} | null;
				} | null> | null;
			} | null;
			artist?: { __typename?: 'artist'; id: string } | null;
			organisation?: {
				__typename?: 'organisation';
				id: string;
				name: string;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						key: string;
						name: string;
					} | null;
				} | null> | null;
				location?: { __typename?: 'location'; name?: string | null } | null;
			} | null;
			addresses?: Array<{
				__typename?: 'entity_address';
				city?: { __typename?: 'location'; name?: string | null } | null;
				country?: { __typename?: 'location'; name?: string | null } | null;
			} | null> | null;
		} | null;
		type?: {
			__typename?: 'artwork_activity_association_type';
			key: string;
			name: string;
		} | null;
	}>;
	artwork_activity_artwork_info: Array<{
		__typename?: 'artwork_activity_artwork_info';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		condition?: string | null;
		provenance?: string | null;
		exhibition?: string | null;
		literature?: string | null;
		ingestion_notes?: string | null;
		is_bundle?: boolean | null;
		is_full_set?: boolean | null;
		number_of_artworks?: number | null;
		raw_artwork_description?: string | null;
		series_size?: number | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		primary_image?: {
			__typename?: 'directus_files';
			id: string;
			storage: string;
			width?: number | null;
			height?: number | null;
			filename_disk?: string | null;
			filename_download: string;
		} | null;
		additional_images?: Array<{
			__typename?: 'artwork_activity_image';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			image?: {
				__typename?: 'directus_files';
				id: string;
				storage: string;
				width?: number | null;
				height?: number | null;
				filename_disk?: string | null;
				filename_download: string;
			} | null;
		} | null> | null;
		reference_files?: Array<{
			__typename?: 'artwork_activity_artwork_info_files';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			directus_files_id?: {
				__typename?: 'directus_files';
				filename_disk?: string | null;
				filename_download: string;
				id: string;
				storage: string;
				type?: string | null;
				title?: string | null;
			} | null;
		} | null> | null;
	}>;
	artwork_activity_status: Array<{
		__typename?: 'artwork_activity_status';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		timestamp: any;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		type?: {
			__typename?: 'artwork_activity_status_type';
			key: string;
			name: string;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
	}>;
	auction_lot: Array<{
		__typename?: 'auction_lot';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		video_file_name?: string | null;
		hammer_timestamp?: any | null;
		video_hammer_time_seconds?: any | null;
		starting_bid_amount?: number | null;
		sale_amount_includes_premium?: boolean | null;
		lot_number?: string | null;
		lot_notes?: string | null;
		saleroom_notice?: string | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		auction?: {
			__typename?: 'auction';
			id: string;
			sale_name?: string | null;
			local_auction_end_date?: any | null;
			local_auction_start_date?: any | null;
			auction_start_date?: any | null;
			auction_end_date?: any | null;
			sale_number?: string | null;
			currency?: {
				__typename?: 'currency';
				code: string;
				name: string;
				symbol?: string | null;
				full_name?: string | null;
			} | null;
			auction_house?: {
				__typename?: 'auction_house';
				organisation?: { __typename?: 'organisation'; name: string } | null;
			} | null;
		} | null;
		auctioneer?: {
			__typename?: 'person';
			entity?: {
				__typename?: 'entity';
				id: string;
				person?: {
					__typename?: 'person';
					id: string;
					entity?: {
						__typename?: 'entity';
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
		} | null;
		bids?: Array<{
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
		archived_bids?: Array<{
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
		winning_bid?: {
			__typename?: 'auction_bid';
			id: string;
			notes?: string | null;
			date_created?: any | null;
			date_updated?: any | null;
			timestamp: any;
			user_created?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_updated?: {
				__typename?: 'directus_users';
				id?: string | null;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			bidder?: {
				__typename?: 'auction_lot_bidder';
				id: string;
				notes?: string | null;
				location_number?: number | null;
				bidder?: {
					__typename?: 'entity';
					id: string;
					name: string;
					person?: {
						__typename?: 'person';
						id: string;
						first_name?: string | null;
						last_name?: string | null;
					} | null;
				} | null;
				bidder_type?: {
					__typename?: 'auction_bidder_type';
					name: string;
					key: string;
				} | null;
				client?: {
					__typename?: 'auction_client';
					id: string;
					paddle_number?: string | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				conversion_timestamp?: any | null;
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null;
		attributes?: Array<{
			__typename?: 'auction_lot_attribute';
			id: string;
			status?: { __typename?: 'status'; key: string; name: string } | null;
			type?: {
				__typename?: 'auction_lot_attribute_type';
				key: string;
				name: string;
				description?: string | null;
			} | null;
			amount?: {
				__typename?: 'currency_amount';
				id: string;
				usd_amount: number;
				conversion_timestamp?: any | null;
				amount: number;
				currency?: {
					__typename?: 'currency';
					symbol?: string | null;
					name: string;
					code: string;
					full_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
	}>;
};

export type GetArtworkActivityByIdBaseQueryVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
}>;

export type GetArtworkActivityByIdBaseQuery = {
	__typename?: 'Query';
	artwork_activity_by_id?: {
		__typename?: 'artwork_activity';
		notes?: string | null;
		source_page_url?: string | null;
	} | null;
};

export const GetArtworkActivityByIdDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkActivityById' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_by_id' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'source_page_url' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activity_status' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-timestamp',
														block: false,
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: {
													kind: 'Name',
													value: 'ArtworkActivityArtworkFragment',
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_lot' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'date_updated' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'date_created' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'user_updated' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'user_created' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'video_file_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'hammer_timestamp' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'video_hammer_time_seconds',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'sale_name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'local_auction_start_date',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'local_auction_end_date',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_start_date',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_end_date',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'sale_number',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'currency' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'symbol',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'full_name',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_house',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auctioneer' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'id',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'entity',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'type',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'key',
																														},
																													},
																												],
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'bids' },
															arguments: [
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'sort' },
																	value: {
																		kind: 'ListValue',
																		values: [
																			{
																				kind: 'StringValue',
																				value: 'timestamp',
																				block: false,
																			},
																		],
																	},
																},
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'filter' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'status' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'key',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: '_neq',
																										},
																										value: {
																											kind: 'StringValue',
																											value: 'archived',
																											block: false,
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'FragmentSpread',
																		name: {
																			kind: 'Name',
																			value: 'BidFragment',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															alias: { kind: 'Name', value: 'archived_bids' },
															name: { kind: 'Name', value: 'bids' },
															arguments: [
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'sort' },
																	value: {
																		kind: 'ListValue',
																		values: [
																			{
																				kind: 'StringValue',
																				value: 'timestamp',
																				block: false,
																			},
																		],
																	},
																},
																{
																	kind: 'Argument',
																	name: { kind: 'Name', value: 'filter' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'status' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: 'key',
																							},
																							value: {
																								kind: 'ObjectValue',
																								fields: [
																									{
																										kind: 'ObjectField',
																										name: {
																											kind: 'Name',
																											value: '_eq',
																										},
																										value: {
																											kind: 'StringValue',
																											value: 'archived',
																											block: false,
																										},
																									},
																								],
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'FragmentSpread',
																		name: {
																			kind: 'Name',
																			value: 'BidFragment',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'winning_bid' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'FragmentSpread',
																		name: {
																			kind: 'Name',
																			value: 'BidFragment',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'starting_bid_amount',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'sale_amount_includes_premium',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'lot_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'lot_notes' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'saleroom_notice' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'attributes' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'status' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'description',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'amount' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'usd_amount',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'conversion_timestamp',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'amount',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'currency',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'symbol',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'full_name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'exhibition' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'venue_country',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'venue_city' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'gallery_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'gallery' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fair_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'fair_exhibitor' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'location',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'country',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'fair' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'title',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'venue_country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'venue_city',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'listing_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'known_price' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_amount' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_high_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_low_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'shipping' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'read_only' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person_type_key',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation_type_key',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'addresses' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'city' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activity_artwork_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'condition' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'provenance' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'literature' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'ingestion_notes' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_bundle' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_full_set' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'number_of_artworks' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'raw_artwork_description',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'series_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'primary_image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'additional_images' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reference_files' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'directus_files_id',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_disk',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'filename_download',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'storage' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivityArtworkFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity_artwork' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'edition_number' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_legacy' },
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'status' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activities_transferred_to' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-date_created',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '1' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'from_artwork' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'status' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'key' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: '_neq',
																							},
																							value: {
																								kind: 'StringValue',
																								value: 'archived',
																								block: false,
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'BidFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'auction_bid' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'bidder' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'client' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'paddle_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'amount' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'conversion_timestamp' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'amount' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'usd_amount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'full_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkActivityByIdQuery,
	GetArtworkActivityByIdQueryVariables
>;
export const GetArtworksActivityDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworksActivity' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_artwork_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'FragmentSpread',
									name: {
										kind: 'Name',
										value: 'ArtworkActivityArtworkFragment',
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivityArtworkFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity_artwork' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'edition_number' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_legacy' },
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'status' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activities_transferred_to' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-date_created',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '1' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'from_artwork' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'status' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'key' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: '_neq',
																							},
																							value: {
																								kind: 'StringValue',
																								value: 'archived',
																								block: false,
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworksActivityQuery,
	GetArtworksActivityQueryVariables
>;
export const GetAuctionLotDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAuctionLot' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'auction_lot_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_lot' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'video_file_name' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'hammer_timestamp' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'video_hammer_time_seconds' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_auction_end_date' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'local_auction_start_date',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'symbol' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'full_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_house' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auctioneer' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bids' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: 'timestamp',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'archived_bids' },
									name: { kind: 'Name', value: 'bids' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: 'timestamp',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_eq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'winning_bid' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'starting_bid_amount' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_amount_includes_premium' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_number' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'saleroom_notice' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'attributes' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'conversion_timestamp',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'BidFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'auction_bid' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'bidder' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'client' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'paddle_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'amount' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'conversion_timestamp' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'amount' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'usd_amount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'full_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetAuctionLotQuery, GetAuctionLotQueryVariables>;
export const GetAssociationsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAssociations' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: {
							kind: 'Name',
							value: 'artwork_activity_association_filter',
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_association' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'read_only' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'entity' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'person_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationalities' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'addresses' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'city' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetAssociationsQuery,
	GetAssociationsQueryVariables
>;
export const GetArtworkActivityInfoDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkActivityInfo' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: {
							kind: 'Name',
							value: 'artwork_activity_artwork_info_filter',
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_artwork_info' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'condition' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'provenance' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'exhibition' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'literature' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_notes' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'is_bundle' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_full_set' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'raw_artwork_description' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'series_size' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'primary_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'additional_images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'reference_files' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkActivityInfoQuery,
	GetArtworkActivityInfoQueryVariables
>;
export const GetArtworkStatusDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkStatus' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_status_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_status' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'ListValue',
									values: [
										{ kind: 'StringValue', value: '-timestamp', block: false },
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkStatusQuery,
	GetArtworkStatusQueryVariables
>;
export const GetArtworkActivityByIdPiecesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkActivityByIdPieces' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'artworkFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_artwork_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'associationFilter' },
					},
					type: {
						kind: 'NamedType',
						name: {
							kind: 'Name',
							value: 'artwork_activity_association_filter',
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'infoFilter' },
					},
					type: {
						kind: 'NamedType',
						name: {
							kind: 'Name',
							value: 'artwork_activity_artwork_info_filter',
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'statusFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_status_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'auctionFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'auction_lot_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_by_id' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'source_page_url' },
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'artworkFilter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'FragmentSpread',
									name: {
										kind: 'Name',
										value: 'ArtworkActivityArtworkFragment',
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_association' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'associationFilter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'read_only' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'entity' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'person_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationalities' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'addresses' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'city' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_artwork_info' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'infoFilter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'condition' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'provenance' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'exhibition' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'literature' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_notes' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'is_bundle' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_full_set' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'raw_artwork_description' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'series_size' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'primary_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'width' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'height' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'additional_images' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'image' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'width' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'height' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'reference_files' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'directus_files_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'filename_disk' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'filename_download',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'storage' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_status' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'statusFilter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'ListValue',
									values: [
										{ kind: 'StringValue', value: '-timestamp', block: false },
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_lot' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'auctionFilter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'video_file_name' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'hammer_timestamp' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'video_hammer_time_seconds' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_auction_end_date' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'local_auction_start_date',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'symbol' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'full_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_house' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auctioneer' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bids' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: 'timestamp',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									alias: { kind: 'Name', value: 'archived_bids' },
									name: { kind: 'Name', value: 'bids' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: 'timestamp',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_eq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'winning_bid' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'FragmentSpread',
												name: { kind: 'Name', value: 'BidFragment' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'starting_bid_amount' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_amount_includes_premium' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_number' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'lot_notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'saleroom_notice' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'attributes' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'conversion_timestamp',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'full_name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivityArtworkFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity_artwork' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'edition_number' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_legacy' },
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'status' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activities_transferred_to' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-date_created',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '1' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'from_artwork' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'status' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'key' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: '_neq',
																							},
																							value: {
																								kind: 'StringValue',
																								value: 'archived',
																								block: false,
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'BidFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'auction_bid' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'bidder' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'client' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'paddle_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'amount' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'conversion_timestamp' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'amount' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'usd_amount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'full_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkActivityByIdPiecesQuery,
	GetArtworkActivityByIdPiecesQueryVariables
>;
export const GetArtworkActivityByIdBaseDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkActivityByIdBase' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_by_id' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'source_page_url' },
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkActivityByIdBaseQuery,
	GetArtworkActivityByIdBaseQueryVariables
>;
