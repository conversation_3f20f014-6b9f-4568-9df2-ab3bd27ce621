import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type UpdateArtworkActivityAssociationItemsMutationVariables =
	Types.Exact<{
		ids:
			| Array<Types.InputMaybe<Types.Scalars['ID']['input']>>
			| Types.InputMaybe<Types.Scalars['ID']['input']>;
		data: Types.Update_Artwork_Activity_Association_Input;
	}>;

export type UpdateArtworkActivityAssociationItemsMutation = {
	__typename?: 'Mutation';
	update_artwork_activity_association_items: Array<{
		__typename?: 'artwork_activity_association';
		id: string;
	}>;
};

export const UpdateArtworkActivityAssociationItemsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateArtworkActivityAssociationItems' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'ids' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'data' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: {
								kind: 'Name',
								value: 'update_artwork_activity_association_input',
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: {
							kind: 'Name',
							value: 'update_artwork_activity_association_items',
						},
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'ids' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'ids' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'data' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'data' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateArtworkActivityAssociationItemsMutation,
	UpdateArtworkActivityAssociationItemsMutationVariables
>;
