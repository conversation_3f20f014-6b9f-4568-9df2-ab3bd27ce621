import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import * as Types from '../../../gql/types';

export type OrganisationFragmentFragment = {
	__typename?: 'organisation';
	id: string;
	reference_id?: number | null;
	date_updated?: any | null;
	date_created?: any | null;
	name: string;
	year_founded?: number | null;
	year_dissolved?: number | null;
	description?: string | null;
	user_updated?: {
		__typename?: 'directus_users';
		first_name?: string | null;
		last_name?: string | null;
	} | null;
	user_created?: {
		__typename?: 'directus_users';
		first_name?: string | null;
		last_name?: string | null;
	} | null;
	type?: Array<{
		__typename?: 'organisation_organisation_type';
		id: string;
		organisation_type_key?: {
			__typename?: 'organisation_type';
			key: string;
			name: string;
		} | null;
	} | null> | null;
	children?: Array<{
		__typename?: 'organisation';
		id: string;
		name: string;
		type?: Array<{
			__typename?: 'organisation_organisation_type';
			organisation_type_key?: {
				__typename?: 'organisation_type';
				key: string;
				name: string;
			} | null;
		} | null> | null;
	} | null> | null;
	location?: {
		__typename?: 'location';
		name?: string | null;
		code: string;
		short_code?: string | null;
		type?: { __typename?: 'location_type'; key: string } | null;
		country?: {
			__typename?: 'location';
			name?: string | null;
			code: string;
			short_code?: string | null;
			type?: { __typename?: 'location_type'; key: string } | null;
		} | null;
	} | null;
	parent?: {
		__typename?: 'organisation';
		id: string;
		name: string;
		type?: Array<{
			__typename?: 'organisation_organisation_type';
			organisation_type_key?: {
				__typename?: 'organisation_type';
				key: string;
				name: string;
			} | null;
		} | null> | null;
		location?: {
			__typename?: 'location';
			code: string;
			country_nationality?: string | null;
			name?: string | null;
			short_code?: string | null;
			country?: {
				__typename?: 'location';
				code: string;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
			} | null;
		} | null;
	} | null;
	entity?: {
		__typename?: 'entity';
		id: string;
		reference_id?: number | null;
		name: string;
		type?: { __typename?: 'entity_type'; key: string } | null;
		profile_image?: {
			__typename?: 'directus_files';
			modified_on?: any | null;
			created_on?: any | null;
			filename_disk?: string | null;
			filename_download: string;
			id: string;
			storage: string;
			modified_by?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			uploaded_by?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
		} | null;
		additional_images?: Array<{
			__typename?: 'entity_images';
			directus_files_id?: {
				__typename?: 'directus_files';
				modified_on?: any | null;
				created_on?: any | null;
				filename_disk?: string | null;
				filename_download: string;
				id: string;
				storage: string;
				modified_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
				uploaded_by?: {
					__typename?: 'directus_users';
					first_name?: string | null;
					last_name?: string | null;
				} | null;
			} | null;
		} | null> | null;
		attributes?: Array<{
			__typename?: 'entity_attribute';
			id: string;
			type?: {
				__typename?: 'entity_attribute_type';
				key: string;
				name: string;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
		} | null> | null;
		addresses?: Array<{
			__typename?: 'entity_address';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			timestamp?: any | null;
			line_1?: string | null;
			line_2?: string | null;
			line_3?: string | null;
			post_code?: string | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			city?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
				country_nationality?: string | null;
				parent?: { __typename?: 'location'; code: string } | null;
			} | null;
			region?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
				country_nationality?: string | null;
				parent?: { __typename?: 'location'; code: string } | null;
			} | null;
			country?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
				country_nationality?: string | null;
				parent?: { __typename?: 'location'; code: string } | null;
			} | null;
		} | null> | null;
		notes?: Array<{
			__typename?: 'entity_note';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			timestamp?: any | null;
			note: string;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			type?: {
				__typename?: 'entity_note_type';
				key: string;
				name: string;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
		} | null> | null;
		collection_notes?: Array<{
			__typename?: 'collection_note';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			timestamp?: any | null;
			note?: string | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			type?: {
				__typename?: 'collection_note_type';
				key: string;
				name: string;
			} | null;
			artwork_series?: {
				__typename?: 'artwork_series';
				id: string;
				title: string;
				is_heni_series?: boolean | null;
				number_of_artworks?: number | null;
				crid?: string | null;
				artists?: Array<{
					__typename?: 'artwork_series_artist';
					artist_id?: {
						__typename?: 'artist';
						id: string;
						person?: {
							__typename?: 'person';
							id: string;
							year_birth?: number | null;
							year_death?: number | null;
							nationalities?: Array<{
								__typename?: 'person_nationality';
								country?: {
									__typename?: 'location';
									code: string;
									country_nationality?: string | null;
								} | null;
							} | null> | null;
							entity?: {
								__typename?: 'entity';
								id: string;
								name: string;
							} | null;
						} | null;
					} | null;
				} | null> | null;
			} | null;
			artist?: {
				__typename?: 'artist';
				id: string;
				reference_id?: number | null;
				person?: {
					__typename?: 'person';
					id: string;
					year_birth?: number | null;
					year_death?: number | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						country?: {
							__typename?: 'location';
							code: string;
							country_nationality?: string | null;
						} | null;
					} | null> | null;
					entity?: { __typename?: 'entity'; id: string; name: string } | null;
				} | null;
			} | null;
			status?: { __typename?: 'status'; key: string; name: string } | null;
		} | null> | null;
		contact_details?: Array<{
			__typename?: 'entity_contact_detail';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			value: string;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			type?: {
				__typename?: 'entity_contact_detail_type';
				name?: string | null;
				key: string;
			} | null;
		} | null> | null;
	} | null;
};

export const OrganisationFragmentFragmentDoc = {
	kind: 'Document',
	definitions: [
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'OrganisationFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'organisation' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'reference_id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'organisation_type_key' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'children' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'status' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'key' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: '_neq' },
																	value: {
																		kind: 'StringValue',
																		value: 'archived',
																		block: false,
																	},
																},
															],
														},
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation_type_key' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'location' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'short_code' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'country' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'year_founded' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'year_dissolved' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'parent' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation_type_key' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country_nationality' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'entity' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'FragmentSpread',
									name: { kind: 'Name', value: 'EntityFragment' },
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'EntityFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'entity' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'reference_id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'profile_image' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'modified_on' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_on' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'modified_by' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'uploaded_by' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'filename_disk' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'filename_download' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'storage' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'additional_images' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'directus_files_id' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'modified_on' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'created_on' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'modified_by' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'uploaded_by' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'attributes' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'addresses' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'status' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'key' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: '_neq' },
																	value: {
																		kind: 'StringValue',
																		value: 'archived',
																		block: false,
																	},
																},
															],
														},
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'line_1' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'line_2' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'line_3' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'post_code' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'city' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country_nationality' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'parent' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'region' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country_nationality' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'parent' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'country' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country_nationality' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'parent' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'notes' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'status' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'key' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: '_neq' },
																	value: {
																		kind: 'StringValue',
																		value: 'archived',
																		block: false,
																	},
																},
															],
														},
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'note' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'collection_notes' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'status' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'key' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: '_neq' },
																	value: {
																		kind: 'StringValue',
																		value: 'archived',
																		block: false,
																	},
																},
															],
														},
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'note' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_series' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_heni_series' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'number_of_artworks' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist_id' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'person' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_birth',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_death',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'nationalities',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'code',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value:
																													'country_nationality',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'id',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artist' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reference_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_birth' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_death' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'nationalities' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'contact_details' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'status' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'key' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: '_neq' },
																	value: {
																		kind: 'StringValue',
																		value: 'archived',
																		block: false,
																	},
																},
															],
														},
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'value' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<OrganisationFragmentFragment, unknown>;
