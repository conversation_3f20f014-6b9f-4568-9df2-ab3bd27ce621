import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtworkQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetArtworkQuery = {
	__typename?: 'Query';
	artwork: Array<{
		__typename?: 'artwork';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		description?: string | null;
		title?: string | null;
		crid?: string | null;
		media?: string | null;
		execution_end_year?: number | null;
		execution_start_year?: number | null;
		dimensions_height_cm?: number | null;
		dimensions_depth_cm?: number | null;
		dimensions_width_cm?: number | null;
		number_of_pieces?: number | null;
		number_of_artworks?: number | null;
		is_bundle?: boolean | null;
		is_full_set?: boolean | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		status?: { __typename?: 'status'; key: string; name: string } | null;
		primary_image?: {
			__typename?: 'directus_files';
			id: string;
			filename_disk?: string | null;
			description?: string | null;
			filename_download: string;
			storage: string;
			type?: string | null;
		} | null;
		artists?: Array<{
			__typename?: 'artwork_artist';
			id: string;
			artist_id?: {
				__typename?: 'artist';
				id: string;
				reference_id?: number | null;
				aggregations?: Array<{
					__typename?: 'artist_aggregation';
					artwork_count: number;
				} | null> | null;
				person?: {
					__typename?: 'person';
					id: string;
					first_name?: string | null;
					last_name?: string | null;
					middle_name?: string | null;
					preferred_name?: string | null;
					year_birth?: number | null;
					year_death?: number | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						id: string;
						country?: {
							__typename?: 'location';
							short_code?: string | null;
							code: string;
							country_nationality?: string | null;
							name?: string | null;
						} | null;
					} | null> | null;
					entity?: {
						__typename?: 'entity';
						id: string;
						name: string;
						type?: { __typename?: 'entity_type'; key: string } | null;
					} | null;
				} | null;
			} | null;
		} | null> | null;
		series?: {
			__typename?: 'artwork_series';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			title: string;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			artists?: Array<{
				__typename?: 'artwork_series_artist';
				artist_id?: {
					__typename?: 'artist';
					person?: {
						__typename?: 'person';
						first_name?: string | null;
						last_name?: string | null;
						middle_name?: string | null;
						preferred_name?: string | null;
						year_death?: number | null;
						year_birth?: number | null;
						nationalities?: Array<{
							__typename?: 'person_nationality';
							country?: {
								__typename?: 'location';
								country_nationality?: string | null;
								code: string;
								name?: string | null;
								short_code?: string | null;
							} | null;
						} | null> | null;
					} | null;
				} | null;
			} | null> | null;
		} | null;
		tags?: Array<{
			__typename?: 'artwork_tag';
			id: string;
			tag_tag?: {
				__typename?: 'tag';
				tag: string;
				type?: { __typename?: 'tag_type'; name: string; key: string } | null;
				status?: { __typename?: 'status'; name: string; key: string } | null;
			} | null;
		} | null> | null;
		dimensions_type?: {
			__typename?: 'artwork_dimension_type';
			description?: string | null;
			key: string;
			name: string;
		} | null;
		heni_artwork_type?: {
			__typename?: 'heni_artwork_type';
			name: string;
			key: string;
		} | null;
		collaborator_organisations?: Array<{
			__typename?: 'artwork_organisation';
			id: string;
			organisation_id?: {
				__typename?: 'organisation';
				id: string;
				name: string;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						key: string;
						name: string;
					} | null;
				} | null> | null;
				location?: {
					__typename?: 'location';
					code: string;
					name?: string | null;
					short_code?: string | null;
					country_nationality?: string | null;
					country?: {
						__typename?: 'location';
						name?: string | null;
						code: string;
					} | null;
					type?: { __typename?: 'location_type'; key: string } | null;
				} | null;
				entity?: { __typename?: 'entity'; name: string } | null;
			} | null;
		} | null> | null;
		edition_info?: {
			__typename?: 'edition_info';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			is_unlimited?: boolean | null;
			edition_size_total?: number | null;
			general_proof_size?: number | null;
			artists_proof_size?: number | null;
			regular_edition_size?: number | null;
			hors_de_commerce_size?: number | null;
			is_numbered?: boolean | null;
			is_unknown?: boolean | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
		} | null;
		artwork_type?: {
			__typename?: 'artwork_type';
			name: string;
			key: string;
		} | null;
	}>;
};

export const GetArtworkDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtwork' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'status' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'primary_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'aggregations' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'artwork_count',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'reference_id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'first_name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'last_name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'middle_name',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'preferred_name',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'short_code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'series' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist_id' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'person' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'first_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'last_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'middle_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'preferred_name',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_death',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_birth',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'nationalities',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value:
																													'country_nationality',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'code',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'short_code',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'tags' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'tag_tag' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'tag' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'media' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'execution_end_year' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'execution_start_year' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_height_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_depth_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_width_cm' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_pieces' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'is_bundle' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_full_set' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'dimensions_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'description' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'heni_artwork_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'collaborator_organisations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'edition_info' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_unlimited' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'edition_size_total' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'general_proof_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artists_proof_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'regular_edition_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'hors_de_commerce_size' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_numbered' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'is_unknown' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetArtworkQuery, GetArtworkQueryVariables>;
