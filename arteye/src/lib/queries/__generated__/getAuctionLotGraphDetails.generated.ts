import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetAuctionLotGraphDetailsQueryVariables = Types.Exact<{
	id: Types.Scalars['ID']['input'];
}>;

export type GetAuctionLotGraphDetailsQuery = {
	__typename?: 'Query';
	auction_by_id?: {
		__typename?: 'auction';
		id: string;
		sale_name?: string | null;
		auction_end_date?: any | null;
		auction_start_date?: any | null;
		lots?: Array<{
			__typename?: 'auction_lot';
			id: string;
			lot_number?: string | null;
			sale_amount_includes_premium?: boolean | null;
			bids?: Array<{
				__typename?: 'auction_bid';
				id: string;
				timestamp: any;
				amount?: { __typename?: 'currency_amount'; amount: number } | null;
				bidder?: {
					__typename?: 'auction_lot_bidder';
					bidder?: { __typename?: 'entity'; name: string; id: string } | null;
					client?: {
						__typename?: 'auction_client';
						id: string;
						entity?: { __typename?: 'entity'; name: string } | null;
					} | null;
				} | null;
			} | null> | null;
			winning_bid?: {
				__typename?: 'auction_bid';
				bidder?: {
					__typename?: 'auction_lot_bidder';
					bidder?: { __typename?: 'entity'; id: string; name: string } | null;
					client?: {
						__typename?: 'auction_client';
						id: string;
						paddle_number?: string | null;
						entity?: { __typename?: 'entity'; name: string } | null;
					} | null;
				} | null;
			} | null;
			artwork_listing?: Array<{
				__typename?: 'artwork_listing';
				price_high_estimate?: {
					__typename?: 'currency_amount';
					usd_amount: number;
				} | null;
				price_low_estimate?: {
					__typename?: 'currency_amount';
					usd_amount: number;
				} | null;
				sale_amount?: {
					__typename?: 'currency_amount';
					usd_amount: number;
				} | null;
				artwork_activity?: {
					__typename?: 'artwork_activity';
					id: string;
					artworks?: Array<{
						__typename?: 'artwork_activity_artwork';
						artwork?: {
							__typename?: 'artwork';
							title?: string | null;
							crid?: string | null;
							dimensions_depth_cm?: number | null;
							dimensions_height_cm?: number | null;
							dimensions_width_cm?: number | null;
							execution_end_year?: number | null;
							execution_start_year?: number | null;
							media?: string | null;
							artists?: Array<{
								__typename?: 'artwork_artist';
								artist_id?: {
									__typename?: 'artist';
									person?: {
										__typename?: 'person';
										entity?: { __typename?: 'entity'; name: string } | null;
									} | null;
								} | null;
							} | null> | null;
							primary_image?: {
								__typename?: 'directus_files';
								filename_disk?: string | null;
								filename_download: string;
								id: string;
							} | null;
							edition_info?: {
								__typename?: 'edition_info';
								regular_edition_size?: number | null;
								is_unlimited?: boolean | null;
							} | null;
						} | null;
					} | null> | null;
					activity_status?: Array<{
						__typename?: 'artwork_activity_status';
						id: string;
						timestamp: any;
						type?: {
							__typename?: 'artwork_activity_status_type';
							name: string;
						} | null;
					} | null> | null;
					activity_artwork_info?: {
						__typename?: 'artwork_activity_artwork_info';
						primary_image?: {
							__typename?: 'directus_files';
							filename_disk?: string | null;
							filename_download: string;
							id: string;
						} | null;
					} | null;
				} | null;
			} | null> | null;
		} | null> | null;
		auction_house?: {
			__typename?: 'auction_house';
			organisation?: {
				__typename?: 'organisation';
				entity?: { __typename?: 'entity'; id: string; name: string } | null;
			} | null;
		} | null;
	} | null;
};

export const GetAuctionLotGraphDetailsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAuctionLotGraphDetails' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_by_id' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_name' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_end_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_start_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'lots' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_number' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'sale_amount_includes_premium',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'bids' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'timestamp' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'amount' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'bidder' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'bidder' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'client' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'winning_bid' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'bidder' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'bidder' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'client' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'paddle_number',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'price_high_estimate',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'usd_amount' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'price_low_estimate',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'usd_amount' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_amount' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'usd_amount' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artwork_activity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artworks' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'artwork',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'title',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'artists',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'artist_id',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'person',
																														},
																														selectionSet: {
																															kind: 'SelectionSet',
																															selections: [
																																{
																																	kind: 'Field',
																																	name: {
																																		kind: 'Name',
																																		value:
																																			'entity',
																																	},
																																	selectionSet:
																																		{
																																			kind: 'SelectionSet',
																																			selections:
																																				[
																																					{
																																						kind: 'Field',
																																						name: {
																																							kind: 'Name',
																																							value:
																																								'name',
																																						},
																																					},
																																				],
																																		},
																																},
																															],
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'crid',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'dimensions_depth_cm',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'dimensions_height_cm',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'dimensions_width_cm',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'execution_end_year',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'execution_start_year',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'media',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'primary_image',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'filename_disk',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value:
																													'filename_download',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'id',
																											},
																										},
																									],
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'edition_info',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value:
																													'regular_edition_size',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'is_unlimited',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'activity_status',
																		},
																		arguments: [
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'sort' },
																				value: {
																					kind: 'StringValue',
																					value: '-timestamp',
																					block: false,
																				},
																			},
																			{
																				kind: 'Argument',
																				name: { kind: 'Name', value: 'limit' },
																				value: { kind: 'IntValue', value: '1' },
																			},
																		],
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'timestamp',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'activity_artwork_info',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'primary_image',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'filename_disk',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'filename_download',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'id',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_house' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetAuctionLotGraphDetailsQuery,
	GetAuctionLotGraphDetailsQueryVariables
>;
