import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetFairsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Fair_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetFairsQuery = {
	__typename?: 'Query';
	fair: Array<{
		__typename?: 'fair';
		local_start_date?: any | null;
		local_end_date?: any | null;
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		title: string;
		fair_url?: string | null;
		start_date?: any | null;
		end_date?: any | null;
		venue_address_1?: string | null;
		venue_address_2?: string | null;
		venue_address_3?: string | null;
		venue_post_code?: string | null;
		timezone?: {
			__typename?: 'timezone';
			timezone: string;
			offset: number;
			offset_dst: number;
		} | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		venue_country?: {
			__typename?: 'location';
			short_code?: string | null;
			code: string;
			name?: string | null;
		} | null;
		venue_city?: {
			__typename?: 'location';
			name?: string | null;
			code: string;
			short_code?: string | null;
			country?: {
				__typename?: 'location';
				code: string;
				name?: string | null;
				short_code?: string | null;
			} | null;
		} | null;
		aggregations?: Array<{
			__typename?: 'fair_aggregation';
			id: string;
			exhibitor_count: number;
		} | null> | null;
		exhibitors?: Array<{
			__typename?: 'fair_exhibitor';
			id: string;
			date_updated?: any | null;
			date_created?: any | null;
			user_updated?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			status?: { __typename?: 'status'; key: string } | null;
			user_created?: {
				__typename?: 'directus_users';
				first_name?: string | null;
				last_name?: string | null;
			} | null;
			entity?: {
				__typename?: 'entity';
				id: string;
				name: string;
				type?: { __typename?: 'entity_type'; key: string } | null;
				person?: {
					__typename?: 'person';
					id: string;
					year_birth?: number | null;
					year_death?: number | null;
					type?: Array<{
						__typename?: 'person_person_type';
						person_type_key?: {
							__typename?: 'person_type';
							key: string;
							name?: string | null;
						} | null;
					} | null> | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						country?: {
							__typename?: 'location';
							country_nationality?: string | null;
						} | null;
					} | null> | null;
				} | null;
				artist?: { __typename?: 'artist'; id: string } | null;
				organisation?: {
					__typename?: 'organisation';
					id: string;
					name: string;
					type?: Array<{
						__typename?: 'organisation_organisation_type';
						organisation_type_key?: {
							__typename?: 'organisation_type';
							key: string;
							name: string;
						} | null;
					} | null> | null;
					location?: {
						__typename?: 'location';
						code: string;
						name?: string | null;
						short_code?: string | null;
						country_nationality?: string | null;
						country?: {
							__typename?: 'location';
							name?: string | null;
							code: string;
							short_code?: string | null;
						} | null;
						type?: { __typename?: 'location_type'; key: string } | null;
					} | null;
				} | null;
				addresses?: Array<{
					__typename?: 'entity_address';
					city?: { __typename?: 'location'; name?: string | null } | null;
					country?: { __typename?: 'location'; name?: string | null } | null;
				} | null> | null;
			} | null;
			artwork_listings?: Array<{
				__typename?: 'fair_artwork_listing';
				id: string;
			} | null> | null;
		} | null> | null;
		fair_organisation?: {
			__typename?: 'fair_organisation';
			id: string;
			organisation?: {
				__typename?: 'organisation';
				name: string;
				id: string;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						name: string;
						key: string;
					} | null;
				} | null> | null;
				location?: {
					__typename?: 'location';
					code: string;
					country_nationality?: string | null;
					name?: string | null;
					short_code?: string | null;
					country?: {
						__typename?: 'location';
						code: string;
						country_nationality?: string | null;
						name?: string | null;
						short_code?: string | null;
					} | null;
				} | null;
				entity?: {
					__typename?: 'entity';
					name: string;
					aggregations?: Array<{
						__typename?: 'entity_aggregation';
						activity_count: number;
					} | null> | null;
				} | null;
			} | null;
		} | null;
	}>;
	fair_aggregated: Array<{
		__typename?: 'fair_aggregated';
		count?: { __typename?: 'fair_aggregated_count'; id?: number | null } | null;
	}>;
};

export const GetFairsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getFairs' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'fair_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'fair' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'timezone' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timezone' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'offset' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'offset_dst' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'local_start_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'local_end_date' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'fair_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'start_date' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'end_date' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_1' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_2' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_address_3' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_post_code' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_country' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'venue_city' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'short_code' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'aggregations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibitor_count' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'exhibitors' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '-1' },
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_updated' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'date_created' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person_type_key',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artist' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation_type_key',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'code',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'short_code',
																								},
																							},
																						],
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'type' },
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'key',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'addresses' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'city' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork_listings' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'fair_organisation' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'aggregations',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'activity_count',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'fair_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'count' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetFairsQuery, GetFairsQueryVariables>;
