import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetTimezoneQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Timezone_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetTimezoneQuery = {
	__typename?: 'Query';
	timezone: Array<{
		__typename?: 'timezone';
		timezone: string;
		offset: number;
		offset_dst: number;
	}>;
};

export const GetTimezoneDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getTimezone' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'timezone_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'timezone' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'timezone' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'offset' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'offset_dst' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetTimezoneQuery, GetTimezoneQueryVariables>;
