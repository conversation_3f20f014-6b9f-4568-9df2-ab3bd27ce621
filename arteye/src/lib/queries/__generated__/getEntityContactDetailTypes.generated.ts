import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetEntityContactDetailTypesQueryVariables = Types.Exact<{
	[key: string]: never;
}>;

export type GetEntityContactDetailTypesQuery = {
	__typename?: 'Query';
	entity_contact_detail_type: Array<{
		__typename?: 'entity_contact_detail_type';
		key: string;
		name?: string | null;
	}>;
};

export const GetEntityContactDetailTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getEntityContactDetailTypes' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'entity_contact_detail_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetEntityContactDetailTypesQuery,
	GetEntityContactDetailTypesQueryVariables
>;
