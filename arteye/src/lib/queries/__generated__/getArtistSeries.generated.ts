import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtistSeriesQueryVariables = Types.Exact<{
	artistFilter?: Types.InputMaybe<Types.Artist_Filter>;
	seriesFilter?: Types.InputMaybe<Types.Artwork_Series_Filter>;
}>;

export type GetArtistSeriesQuery = {
	__typename?: 'Query';
	artist: Array<{
		__typename?: 'artist';
		id: string;
		reference_id?: number | null;
		person?: {
			__typename?: 'person';
			id: string;
			year_birth?: number | null;
			year_death?: number | null;
			nationalities?: Array<{
				__typename?: 'person_nationality';
				country?: {
					__typename?: 'location';
					country_nationality?: string | null;
				} | null;
			} | null> | null;
			entity?: { __typename?: 'entity'; id: string; name: string } | null;
		} | null;
	}>;
	artwork_series: Array<{
		__typename?: 'artwork_series';
		id: string;
		title: string;
		is_heni_series?: boolean | null;
		number_of_artworks?: number | null;
		crid?: string | null;
		artists?: Array<{
			__typename?: 'artwork_series_artist';
			artist_id?: {
				__typename?: 'artist';
				id: string;
				person?: {
					__typename?: 'person';
					id: string;
					year_birth?: number | null;
					year_death?: number | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						country?: {
							__typename?: 'location';
							country_nationality?: string | null;
						} | null;
					} | null> | null;
					entity?: { __typename?: 'entity'; id: string; name: string } | null;
				} | null;
			} | null;
		} | null> | null;
	}>;
};

export const GetArtistSeriesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtistSeries' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'artistFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artist_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'seriesFilter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_series_filter' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artist' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '20' },
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'artistFilter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'ListValue',
									values: [
										{
											kind: 'StringValue',
											value: '-aggregations.artwork_count',
											block: false,
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'reference_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'person' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nationalities' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_series' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'IntValue', value: '20' },
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'seriesFilter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'is_heni_series' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'number_of_artworks' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'id' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtistSeriesQuery,
	GetArtistSeriesQueryVariables
>;
