import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetEntityAttributeTypesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Entity_Attribute_Type_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetEntityAttributeTypesQuery = {
	__typename?: 'Query';
	entity_attribute_type: Array<{
		__typename?: 'entity_attribute_type';
		key: string;
		name: string;
		description?: string | null;
	}>;
};

export const GetEntityAttributeTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getEntityAttributeTypes' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'entity_attribute_type_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'entity_attribute_type' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetEntityAttributeTypesQuery,
	GetEntityAttributeTypesQueryVariables
>;
