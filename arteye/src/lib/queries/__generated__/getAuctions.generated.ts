import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetAuctionsQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Auction_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetAuctionsQuery = {
	__typename?: 'Query';
	auction: Array<{
		__typename?: 'auction';
		id: string;
		date_updated?: any | null;
		date_created?: any | null;
		sale_name?: string | null;
		sale_number?: string | null;
		sale_url?: string | null;
		local_auction_start_date?: any | null;
		local_auction_end_date?: any | null;
		auction_start_date?: any | null;
		auction_end_date?: any | null;
		user_updated?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		aggregations?: Array<{
			__typename?: 'auction_aggregation';
			lot_count: number;
		} | null> | null;
		auction_house?: {
			__typename?: 'auction_house';
			organisation?: {
				__typename?: 'organisation';
				id: string;
				year_founded?: number | null;
				year_dissolved?: number | null;
				name: string;
				type?: Array<{
					__typename?: 'organisation_organisation_type';
					organisation_type_key?: {
						__typename?: 'organisation_type';
						key: string;
						name: string;
					} | null;
				} | null> | null;
				location?: {
					__typename?: 'location';
					code: string;
					name?: string | null;
					short_code?: string | null;
					country_nationality?: string | null;
					country?: {
						__typename?: 'location';
						name?: string | null;
						code: string;
						short_code?: string | null;
					} | null;
					type?: { __typename?: 'location_type'; key: string } | null;
				} | null;
			} | null;
		} | null;
		auction_types?: Array<{
			__typename?: 'auction_auction_type';
			auction_type_key?: {
				__typename?: 'auction_type';
				name: string;
				key: string;
			} | null;
		} | null> | null;
		currency?: {
			__typename?: 'currency';
			name: string;
			code: string;
			symbol?: string | null;
		} | null;
		auction_timezone?: {
			__typename?: 'timezone';
			timezone: string;
			offset: number;
			offset_dst: number;
		} | null;
		cover_page_image?: {
			__typename?: 'directus_files';
			id: string;
			title?: string | null;
			type?: string | null;
			storage: string;
			filename_download: string;
			filename_disk?: string | null;
		} | null;
	}>;
	auction_aggregated: Array<{
		__typename?: 'auction_aggregated';
		count?: {
			__typename?: 'auction_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetAuctionsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAuctions' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'auction_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_number' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sale_url' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'local_auction_start_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'local_auction_end_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_start_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_end_date' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'aggregations' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_count' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_house' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_founded' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'year_dissolved' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_types' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_type_key' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_timezone' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timezone' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'offset' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'offset_dst' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'cover_page_image' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'storage' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_download' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'filename_disk' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'count' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetAuctionsQuery, GetAuctionsQueryVariables>;
