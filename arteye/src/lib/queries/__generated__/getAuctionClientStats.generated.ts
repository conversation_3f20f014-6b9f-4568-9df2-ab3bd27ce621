import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetAuctionClientStatsQueryVariables = Types.Exact<{
	id?: Types.InputMaybe<Types.Scalars['ID']['input']>;
}>;

export type GetAuctionClientStatsQuery = {
	__typename?: 'Query';
	auction_bid_aggregated: Array<{
		__typename?: 'auction_bid_aggregated';
		countDistinct?: {
			__typename?: 'auction_bid_aggregated_count';
			id?: number | null;
		} | null;
	}>;
	auction_bid: Array<{
		__typename?: 'auction_bid';
		auction_lot?: {
			__typename?: 'auction_lot';
			lot_number?: string | null;
		} | null;
	}>;
	auction_lot_aggregated: Array<{
		__typename?: 'auction_lot_aggregated';
		countDistinct?: {
			__typename?: 'auction_lot_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetAuctionClientStatsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getAuctionClientStats' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'ID' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_bid_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: '_and' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'bidder' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'client' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: { kind: 'Name', value: 'id' },
																						value: {
																							kind: 'ObjectValue',
																							fields: [
																								{
																									kind: 'ObjectField',
																									name: {
																										kind: 'Name',
																										value: '_eq',
																									},
																									value: {
																										kind: 'Variable',
																										name: {
																											kind: 'Name',
																											value: 'id',
																										},
																									},
																								},
																							],
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'status' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'key' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: {
																							kind: 'Name',
																							value: '_neq',
																						},
																						value: {
																							kind: 'StringValue',
																							value: 'archived',
																							block: false,
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_bid' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: '_and' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'bidder' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'client' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: { kind: 'Name', value: 'id' },
																						value: {
																							kind: 'ObjectValue',
																							fields: [
																								{
																									kind: 'ObjectField',
																									name: {
																										kind: 'Name',
																										value: '_eq',
																									},
																									value: {
																										kind: 'Variable',
																										name: {
																											kind: 'Name',
																											value: 'id',
																										},
																									},
																								},
																							],
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'status' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'key' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: {
																							kind: 'Name',
																							value: '_neq',
																						},
																						value: {
																							kind: 'StringValue',
																							value: 'archived',
																							block: false,
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_lot' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auction_lot_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: '_and' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'winning_bid' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'bidder' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: {
																							kind: 'Name',
																							value: 'client',
																						},
																						value: {
																							kind: 'ObjectValue',
																							fields: [
																								{
																									kind: 'ObjectField',
																									name: {
																										kind: 'Name',
																										value: 'id',
																									},
																									value: {
																										kind: 'ObjectValue',
																										fields: [
																											{
																												kind: 'ObjectField',
																												name: {
																													kind: 'Name',
																													value: '_eq',
																												},
																												value: {
																													kind: 'Variable',
																													name: {
																														kind: 'Name',
																														value: 'id',
																													},
																												},
																											},
																										],
																									},
																								},
																							],
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'status' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: 'key' },
																			value: {
																				kind: 'ObjectValue',
																				fields: [
																					{
																						kind: 'ObjectField',
																						name: {
																							kind: 'Name',
																							value: '_neq',
																						},
																						value: {
																							kind: 'StringValue',
																							value: 'archived',
																							block: false,
																						},
																					},
																				],
																			},
																		},
																	],
																},
															},
														],
													},
												],
											},
										},
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'countDistinct' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetAuctionClientStatsQuery,
	GetAuctionClientStatsQueryVariables
>;
