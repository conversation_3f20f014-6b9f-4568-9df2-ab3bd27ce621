import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetReportConfigsQueryVariables = Types.Exact<{
	[key: string]: never;
}>;

export type GetReportConfigsQuery = {
	__typename?: 'Query';
	report_config: Array<{
		__typename?: 'report_config';
		id: string;
		sort?: number | null;
		status?: string | null;
		type: string;
		date_updated?: any | null;
		date_created?: any | null;
		params?: Array<{
			__typename?: 'report_config_report_param';
			report_param_id?: {
				__typename?: 'report_param';
				label?: string | null;
				param_name: string;
				required: boolean;
				status?: string | null;
				type?: string | null;
				description?: string | null;
				id: string;
			} | null;
		} | null> | null;
		user_updated?: {
			__typename?: 'directus_users';
			id?: string | null;
			first_name?: string | null;
			last_name?: string | null;
		} | null;
		user_created?: {
			__typename?: 'directus_users';
			id?: string | null;
			first_name?: string | null;
			last_name?: string | null;
		} | null;
	}>;
};

export const GetReportConfigsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getReportConfigs' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'report_config' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'ListValue',
									values: [
										{ kind: 'StringValue', value: 'type', block: false },
									],
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'sort' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'params' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'report_param_id' },
												arguments: [
													{
														kind: 'Argument',
														name: { kind: 'Name', value: 'sort' },
														value: {
															kind: 'StringValue',
															value: 'label',
															block: false,
														},
													},
												],
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'label' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'param_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'required' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'status' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_updated' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'date_created' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_updated' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'user_created' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'first_name' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'last_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetReportConfigsQuery,
	GetReportConfigsQueryVariables
>;
