import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetCurrencyQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Currency_Filter>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type GetCurrencyQuery = {
	__typename?: 'Query';
	currency: Array<{
		__typename?: 'currency';
		name: string;
		symbol?: string | null;
		code: string;
		full_name?: string | null;
	}>;
};

export const GetCurrencyDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getCurrency' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'currency_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'currency' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'symbol' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'full_name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetCurrencyQuery, GetCurrencyQueryVariables>;
