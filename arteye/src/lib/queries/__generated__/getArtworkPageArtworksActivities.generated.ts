import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetArtworkPageArtworksActivitiesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Artwork_Activity_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
	limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	offset?: Types.InputMaybe<Types.Scalars['Int']['input']>;
	activityStatus?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetArtworkPageArtworksActivitiesQuery = {
	__typename?: 'Query';
	artwork_activity: Array<{
		__typename?: 'artwork_activity';
		id: string;
		source_page_url?: string | null;
		timestamp: any;
		type?: {
			__typename?: 'artwork_activity_type';
			name: string;
			key: string;
		} | null;
		activity_status?: Array<{
			__typename?: 'artwork_activity_status';
			timestamp: any;
			status?: { __typename?: 'status'; name: string } | null;
			type?: {
				__typename?: 'artwork_activity_status_type';
				name: string;
			} | null;
		} | null> | null;
		associations?: Array<{
			__typename?: 'artwork_activity_association';
			entity?: {
				__typename?: 'entity';
				name: string;
				organisation?: {
					__typename?: 'organisation';
					location?: { __typename?: 'location'; name?: string | null } | null;
				} | null;
			} | null;
			type?: {
				__typename?: 'artwork_activity_association_type';
				key: string;
				name: string;
			} | null;
		} | null> | null;
		artwork_listing?: Array<{
			__typename?: 'artwork_listing';
			known_price?: {
				__typename?: 'currency_amount';
				usd_amount: number;
				amount: number;
			} | null;
			auction_lot?: {
				__typename?: 'auction_lot';
				sale_amount_includes_premium?: boolean | null;
				lot_number?: string | null;
				attributes?: Array<{
					__typename?: 'auction_lot_attribute';
					type?: {
						__typename?: 'auction_lot_attribute_type';
						name: string;
						description?: string | null;
					} | null;
				} | null> | null;
				auction?: {
					__typename?: 'auction';
					sale_name?: string | null;
					auction_house?: {
						__typename?: 'auction_house';
						organisation?: {
							__typename?: 'organisation';
							name: string;
							location?: {
								__typename?: 'location';
								name?: string | null;
								country?: {
									__typename?: 'location';
									name?: string | null;
								} | null;
							} | null;
						} | null;
					} | null;
				} | null;
			} | null;
			exhibition_listing?: {
				__typename?: 'exhibition_artwork_listing';
				exhibition?: {
					__typename?: 'exhibition';
					title: string;
					organisers?: Array<{
						__typename?: 'exhibition_organisers';
						entity_id?: { __typename?: 'entity'; name: string } | null;
					} | null> | null;
				} | null;
			} | null;
			gallery_listing?: {
				__typename?: 'gallery_artwork_listing';
				gallery?: {
					__typename?: 'gallery';
					organisation?: {
						__typename?: 'organisation';
						name: string;
						location?: { __typename?: 'location'; name?: string | null } | null;
					} | null;
				} | null;
			} | null;
			fair_listing?: {
				__typename?: 'fair_artwork_listing';
				fair_exhibitor?: {
					__typename?: 'fair_exhibitor';
					fair?: { __typename?: 'fair'; title: string } | null;
					entity?: { __typename?: 'entity'; name: string } | null;
				} | null;
			} | null;
			listing_type?: {
				__typename?: 'artwork_listing_type';
				key: string;
				name: string;
			} | null;
			sale_amount?: {
				__typename?: 'currency_amount';
				usd_amount: number;
				amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
			price_low_estimate?: {
				__typename?: 'currency_amount';
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
			price_high_estimate?: {
				__typename?: 'currency_amount';
				amount: number;
				usd_amount: number;
				currency?: {
					__typename?: 'currency';
					code: string;
					symbol?: string | null;
				} | null;
			} | null;
		} | null> | null;
	}>;
	artwork_activity_aggregated: Array<{
		__typename?: 'artwork_activity_aggregated';
		count?: {
			__typename?: 'artwork_activity_aggregated_count';
			id?: number | null;
		} | null;
	}>;
};

export const GetArtworkPageArtworksActivitiesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getArtworkPageArtworksActivities' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'artwork_activity_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'limit' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'offset' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'activityStatus' },
					},
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'limit' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'offset' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'offset' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'source_page_url' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activity_status' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'Variable',
												name: { kind: 'Name', value: 'activityStatus' },
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'status' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'associations' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artwork_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'known_price' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction_lot' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'sale_amount_includes_premium',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'lot_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'attributes' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'description',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'sale_name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_house',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'location',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'country',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'exhibition' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'organisers' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'entity_id',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'gallery_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'gallery' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fair_listing' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'fair_exhibitor' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'fair' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'title',
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'listing_type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'sale_amount' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_low_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'price_high_estimate' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'amount' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'currency' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'symbol' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'usd_amount' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_activity_aggregated' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'count' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetArtworkPageArtworksActivitiesQuery,
	GetArtworkPageArtworksActivitiesQueryVariables
>;
