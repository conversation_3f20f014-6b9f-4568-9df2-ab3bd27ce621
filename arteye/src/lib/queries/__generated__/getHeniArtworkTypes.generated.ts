import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types';

export type GetHeniArtworkTypesQueryVariables = Types.Exact<{
	filter?: Types.InputMaybe<Types.Heni_Artwork_Type_Filter>;
	sort?: Types.InputMaybe<
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>
	>;
}>;

export type GetHeniArtworkTypesQuery = {
	__typename?: 'Query';
	heni_artwork_type: Array<{
		__typename?: 'heni_artwork_type';
		key: string;
		name: string;
	}>;
};

export const GetHeniArtworkTypesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getHeniArtworkTypes' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'filter' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'heni_artwork_type_filter' },
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'sort' } },
					type: {
						kind: 'ListType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'heni_artwork_type' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'filter' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'filter' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'sort' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'sort' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetHeniArtworkTypesQuery,
	GetHeniArtworkTypesQueryVariables
>;
