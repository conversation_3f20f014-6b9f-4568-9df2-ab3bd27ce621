import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getExhibitions(
		$filter: exhibition_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		exhibition(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			id
			timezone {
				timezone
				offset
				offset_dst
			}
			local_start_date
			local_end_date
			date_updated
			date_created
			user_updated {
				first_name
				last_name
			}
			user_created {
				first_name
				last_name
			}

			start_date
			end_date
			exhibition_url
			title
			description
			artists {
				id
				exhibition_id {
					id
				}
				artist_id {
					id
					reference_id
					person {
						year_birth
						year_death
						entity {
							name
						}
						nationalities {
							country {
								code
								country_nationality
								name
								short_code
							}
						}
						type {
							person_type_key {
								key
								name
							}
						}
					}
					aggregations {
						artwork_count
						id
					}
				}
			}
			aggregations {
				artwork_listing_count
			}

			cover_image {
				id
				title
				type
				storage
				filename_download
				filename_disk
			}
			venue_address_1
			venue_address_2
			venue_address_3
			venue_post_code
			venue_country {
				short_code
				code
				name
			}
			venue_city {
				name
				code
				short_code
				country {
					code
					name
					short_code
				}
			}
			organisers {
				id

				entity_id {
					id

					date_updated
					date_created
					user_updated {
						first_name
						last_name
					}
					user_created {
						first_name
						last_name
					}

					name
					type {
						key
					}
					artist {
						id
					}
					gallery {
						id
					}
					organisation {
						id
						name
						type {
							organisation_type_key {
								key
								name
							}
						}
						location {
							code
							name
							short_code
							country {
								name
								code
								short_code
							}
							country_nationality
							type {
								key
							}
						}
					}
					person {
						id
						year_birth
						year_death
						type {
							person_type_key {
								key
								name
							}
						}
						nationalities {
							country {
								country_nationality
							}
						}
					}
					addresses {
						city {
							name
						}
						country {
							name
						}
					}
				}
			}
			attributes {
				id
				type {
					key
					name
				}
				exhibition {
					id
				}
			}
		}
		exhibition_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
