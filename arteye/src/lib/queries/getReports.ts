import { gql } from 'graphql-tag';

export const GET_REPORTS = gql`
	query getReports(
		$filter: report_filter
		$limit: Int
		$offset: Int
		$sort: [String]
	) {
		report(filter: $filter, limit: $limit, offset: $offset, sort: $sort) {
			id

			date_updated
			date_created
			user_updated {
				first_name
				last_name
			}
			user_created {
				first_name
				last_name
			}

			title
			config {
				type
			}
			date_created
			user_created {
				first_name
			}
			status
			pdf_file {
				id
				filename_disk
			}
			csv_file {
				id
				filename_disk
			}
		}
		report_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
