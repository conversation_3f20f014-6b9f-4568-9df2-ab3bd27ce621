import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getGalleryRepresentation($filter: gallery_representation_filter) {
		gallery_representation(filter: $filter) {
			id

			date_updated
			date_created
			user_updated {
				first_name
				last_name
			}
			user_created {
				first_name
				last_name
			}

			gallery {
				id
				organisation {
					name
					location {
						name
					}
				}
			}
			description
			email_date
		}
	}
`;
