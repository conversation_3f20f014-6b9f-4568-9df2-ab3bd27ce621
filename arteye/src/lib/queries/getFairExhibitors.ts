import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getFairExhibitors($filter: fair_exhibitor_filter, $limit: Int) {
		fair_exhibitor(filter: $filter, limit: $limit) {
			id
			entity {
				id
				type {
					key
				}
				name
				person {
					id
				}
				artist {
					id
				}
				organisation {
					id
					name
					location {
						name
						country {
							name
						}
					}
				}
				addresses {
					city {
						name
					}
					country {
						name
					}
				}
			}
		}
	}
`;
