import { gql } from 'graphql-tag';

export const GET_REPORT_CONFIGS = gql`
	query getReportConfigs {
		report_config(sort: ["type"]) {
			id
			sort
			status
			type
			params {
				report_param_id(sort: "label") {
					label
					param_name
					required
					status
					type
					description
					id
				}
			}
			date_updated
			date_created
			user_updated {
				id
				first_name
				last_name
			}
			user_created {
				id
				first_name
				last_name
			}
		}
	}
`;
