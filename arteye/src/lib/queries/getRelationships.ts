import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getRelationships($filter: relationship_filter) {
		relationship(filter: $filter) {
			id

			date_updated
			date_created
			user_updated {
				first_name
				last_name
			}
			user_created {
				first_name
				last_name
			}

			type {
				key
				from_entity_type {
					key
					name
				}
				to_entity_type {
					key
					name
				}
			}
			from_entity {
				id
				type {
					key
				}
				name
				person {
					id
				}
				artist {
					id
				}
				organisation {
					id
					name
				}
			}
			to_entity {
				id
				type {
					key
				}
				name
				person {
					id
				}
				artist {
					id
				}
				organisation {
					id
					name
				}
			}
			start_date
			end_date
			notes
		}
	}
`;
