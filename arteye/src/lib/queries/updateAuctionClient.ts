import { gql } from 'graphql-tag';

export const UPDATE_AUCTION_CLIENT_ITEM = gql`
	mutation updateAuctionClientItem(
		$id: ID!
		$data: update_auction_client_input!
	) {
		update_auction_client_item(id: $id, data: $data) {
			id
			auction {
				id
			}
			status {
				key
				name
			}
			paddle_number
			entity {
				id
				type {
					key
				}
				name
				person {
					id
					year_birth
					year_death
					type {
						person_type_key {
							key
							name
						}
					}
					nationalities {
						country {
							country_nationality
						}
					}
				}
				artist {
					id
				}
				organisation {
					id
					name
				}
			}
		}
	}
`;
