import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getFairs(
		$filter: fair_filter
		$sort: [String]
		$limit: Int
		$offset: Int
	) {
		fair(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			timezone {
				timezone
				offset
				offset_dst
			}
			local_start_date
			local_end_date
			id
			date_updated
			date_created
			user_updated {
				first_name
				last_name
			}
			user_created {
				first_name
				last_name
			}

			title
			fair_url
			start_date
			end_date
			venue_address_1
			venue_address_2
			venue_address_3
			venue_post_code
			venue_country {
				short_code
				code
				name
			}
			venue_city {
				name
				code
				short_code
				country {
					code
					name
					short_code
				}
			}
			aggregations {
				id
				exhibitor_count
			}
			exhibitors(limit: -1) {
				id
				date_updated
				date_created
				user_updated {
					first_name
					last_name
				}
				status {
					key
				}
				user_created {
					first_name
					last_name
				}
				entity {
					id
					name
					type {
						key
					}
					person {
						id
						year_birth
						year_death
						type {
							person_type_key {
								key
								name
							}
						}
						nationalities {
							country {
								country_nationality
							}
						}
					}
					artist {
						id
					}
					organisation {
						id
						name
						type {
							organisation_type_key {
								key
								name
							}
						}
						location {
							code
							name
							short_code
							country {
								name
								code
								short_code
							}
							country_nationality
							type {
								key
							}
						}
					}
					addresses {
						city {
							name
						}
						country {
							name
						}
					}
				}
				artwork_listings {
					id
				}
			}
			fair_organisation {
				id
				organisation {
					name
					type {
						organisation_type_key {
							name
							key
						}
					}
					id
					location {
						code
						country_nationality
						name
						short_code

						country {
							code
							country_nationality
							name
							short_code
						}
					}
					entity {
						aggregations {
							activity_count
						}
						name
					}
				}
			}
		}
		fair_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
