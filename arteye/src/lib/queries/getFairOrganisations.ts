import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getFairOrganisations($filter: fair_organisation_filter, $limit: Int) {
		fair_organisation(filter: $filter, limit: $limit) {
			id
			reference_id
			organisation {
				id
				name
				location {
					code
					name
					short_code
					country {
						name
						code
						short_code
					}
					country_nationality
					type {
						key
					}
				}
				type {
					organisation_type_key {
						key
						name
					}
				}
			}
		}
	}
`;
