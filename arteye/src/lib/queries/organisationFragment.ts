import { gql } from 'graphql-tag';
import { EntityFragment } from './entityFragment';

export const OrganisationFragment = gql`
	fragment OrganisationFragment on organisation {
		id
		reference_id
		date_updated
		date_created
		user_updated {
			first_name
			last_name
		}
		user_created {
			first_name
			last_name
		}

		name
		type {
			id
			organisation_type_key {
				key
				name
			}
		}
		children(filter: { status: { key: { _neq: "archived" } } }) {
			id
			type {
				organisation_type_key {
					key
					name
				}
			}
			name
		}
		location {
			name
			code
			short_code
			type {
				key
			}
			country {
				name
				code
				short_code
				type {
					key
				}
			}
		}
		year_founded
		year_dissolved
		description
		parent {
			id
			name

			type {
				organisation_type_key {
					key
					name
				}
			}

			location {
				code
				country_nationality
				name
				short_code

				country {
					code
					country_nationality
					name
					short_code
				}
			}
		}
		entity {
			...EntityFragment
		}
	}
	${EntityFragment}
`;
