import { gql } from 'graphql-tag';
import { EntityFragment } from './entityFragment';

export const PersonFragment = gql`
	fragment PersonFragment on person {
		id
		reference_id
		date_updated
		date_created
		user_updated {
			first_name
			last_name
		}
		user_created {
			first_name
			last_name
		}

		first_name
		last_name
		middle_name
		preferred_name
		year_birth
		year_death
		net_worth_usd
		industry
		job_title
		biography
		gender {
			name
			key
		}

		type {
			id
			person_type_key {
				key
				name
			}
		}
		nationalities {
			id
			country {
				code
				name
				short_code
				type {
					key
				}
				country_nationality
			}
			person {
				id
			}
		}

		entity {
			...EntityFragment
		}
	}
	${EntityFragment}
`;
