import { gql } from 'graphql-tag';

export const QUERY = gql`
	query getPeople(
		$filter: person_filter
		$sort: [String]
		$limit: Int
		$offset: Int
		$sortNationalities: [String]
	) {
		person(filter: $filter, sort: $sort, limit: $limit, offset: $offset) {
			entity {
				id
				name
				aggregations {
					activity_count
				}

				addresses {
					city {
						name
					}
					country {
						code
						name
						short_code
						country {
							name
						}
					}
				}

				collection_notes {
					note
					artist {
						person {
							entity {
								name
							}
						}
					}
					artwork_series {
						title
					}
				}
				activities {
					id
					artwork_activity {
						notes
						artworks {
							artwork {
								title
							}
						}
					}
				}
			}
			id
			reference_id
			type {
				person_type_key {
					name
				}
			}
			year_birth
			year_death
			nationalities(sort: $sortNationalities) {
				country {
					name
				}
			}
		}
		person_aggregated(filter: $filter) {
			count {
				id
			}
		}
	}
`;
