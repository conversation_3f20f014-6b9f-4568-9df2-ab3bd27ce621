<script lang="ts">
	import { FairsSearchParam } from './constants/search';
	import { FairsSearchForm } from './FairsSearchForm';
	import type { FairsSearchFormFields } from './FairsSearchForm/FairsSearchForm.svelte';
	import { FairsTable } from './FairsTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { FairsSearchPageData } from '$routes/fairs/types';

	let data = $derived(getPageData<FairsSearchPageData>(page.data));
	let showResults = $derived(data.showResults);

	const formatParamString = (
		values: FairsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			sort,
			title,
			name,
			startDate,
			startDateRange,
			endDate,
			endDateRange,
			city,
			country,
			exhibitor,
		} = values;

		const params: Record<FairsSearchParam, string> = {
			[FairsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[FairsSearchParam.ShowResults]: showResults,
			[FairsSearchParam.Title]: title,
			[FairsSearchParam.Name]: name,
			[FairsSearchParam.StartDate]: startDate,
			[FairsSearchParam.StartDateRange]: startDate ? startDateRange : '',
			[FairsSearchParam.EndDate]: endDate,
			[FairsSearchParam.EndDateRange]: endDate ? endDateRange : '',
			[FairsSearchParam.City]: city,
			[FairsSearchParam.Country]: country,
			[FairsSearchParam.Exhibitor]: exhibitor,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<FairsSearchForm {formatParamString} />
</div>

{#if showResults}
	<FairsTable />
{/if}
