import { FairsSearchParam } from '../../constants/search';
import type {
	FairSearchFilters,
	IntValueFilter,
	ValueFilterOperator,
} from '$gql/types-custom';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';

export const getFairsFilter = (searchParams: URLSearchParams) => {
	let filters: FairSearchFilters = {};

	const title = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.Title,
	});

	const name = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.Name,
	});

	const startDate = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.StartDate,
	});

	const startDateRange = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.StartDateRange,
	});

	const endDate = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.EndDate,
	});

	const endDateRange = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.EndDateRange,
	});

	const city = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.City,
	});

	const country = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.Country,
	});

	const exhibitor = getDecodedSearchParam({
		searchParams,
		key: FairsSearchParam.Exhibitor,
	});

	if (title) {
		filters = {
			...filters,
			titleOrId: title,
		};
	}

	if (name) {
		filters = {
			...filters,
			organisationName: name,
		};
	}

	if (startDate) {
		filters = {
			...filters,
			localStartDate: getValueFilter({
				value: startDate,
				range: startDateRange as ValueFilterOperator,
				type: 'date',
			}) as IntValueFilter,
		};
	}

	if (endDate) {
		filters = {
			...filters,
			localEndDate: getValueFilter({
				value: endDate,
				range: endDateRange as ValueFilterOperator,
				type: 'date',
			}) as IntValueFilter,
		};
	}

	if (city) {
		filters = {
			...filters,
			location: {
				...filters.location,
				cityNameOrCode: city,
			},
		};
	}

	if (country) {
		filters = {
			...filters,
			location: {
				...filters.location,
				countryNameOrCode: country,
			},
		};
	}

	if (exhibitor) {
		filters = {
			...filters,
			exhibitorName: exhibitor,
		};
	}

	return filters;
};
