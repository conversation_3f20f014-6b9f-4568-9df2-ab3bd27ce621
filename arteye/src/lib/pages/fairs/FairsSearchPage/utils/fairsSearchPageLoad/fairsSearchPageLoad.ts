import { FairsSearchParam } from '../../constants/search';
import { FAIR_DEFAULT_SORT } from '../../constants/sort';
import { getFairsFilter } from '../getFairsFilter/getFairsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type { FairSearchInput, FairSearchSortField } from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import type { FairSearchQuery } from '$lib/custom-queries/__generated__/fairSearch.generated';
import { FairSearchDocument } from '$lib/custom-queries/__generated__/fairSearch.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import {
	GetFairsDocument,
	type GetFairsQuery,
} from '$lib/queries/__generated__/getFairs.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import { getSort } from '$lib/utils/getSort/getSort';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { PeopleSearchPageLoadEvent } from '$routes/people/types';

export const fairsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: PeopleSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;
	const data = existingData || {};

	let res: FairSearchQuery | null = null;
	let fairs: FairSearchQuery['fairSearch']['data'] | null = null;
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: FairsSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filters = getFairsFilter(searchParams);
		const offset = getOffset(searchParams);

		const sort = getSortWithDirection<FairSearchSortField>(
			searchParams,
			FAIR_DEFAULT_SORT
		);

		const input: FairSearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
			sort,
		};

		res = await gqlClientCustom.request(
			FairSearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		fairs = res.fairSearch.data || [];
		resultsCount = res.fairSearchCount || 0;
	}

	return {
		...parentData,
		...data,
		fairs,
		resultsCount,
		showResults,
		currentPage,
	};
};
