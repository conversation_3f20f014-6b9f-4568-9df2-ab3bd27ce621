// import { redirect } from '@sveltejs/kit';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import type { FairsSearchPageServerLoadEvent } from '$routes/fairs/types';

// export const fairsSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: FairsSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Fair];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	return {
// 		...data,
// 	};
// };
