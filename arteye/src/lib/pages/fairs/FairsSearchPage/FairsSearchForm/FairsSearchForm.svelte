<script module lang="ts">
	export interface FairsSearchFormFields {
		[FairsSearchParam.Sort]: string[];
		[FairsSearchParam.Title]: string;
		[FairsSearchParam.Name]: string;
		[FairsSearchParam.StartDate]: string;
		[FairsSearchParam.StartDateRange]: string;
		[FairsSearchParam.EndDate]: string;
		[FairsSearchParam.EndDateRange]: string;
		[FairsSearchParam.City]: string;
		[FairsSearchParam.Country]: string;
		[FairsSearchParam.Exhibitor]: string;
	}
</script>

<script lang="ts">
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { FairsSearchParam } from '../constants/search';
	import { FAIRS_SORT_OPTIONS } from '../constants/sort';
	import { goto, beforeNavigate } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';

	interface Props {
		formatParamString: (
			data: FairsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let { formatParamString }: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			FairsSearchParam.Sort,
			FAIRS_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			FairsSearchParam.Sort,
			FAIRS_SORT_OPTIONS
		);
	});

	let title = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.Title,
		})
	);
	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.Name,
		})
	);
	let startDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.StartDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let startDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.StartDate,
		})
	);
	let endDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.EndDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let endDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.EndDate,
		})
	);
	let city = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.City,
		})
	);
	let country = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.Country,
		})
	);
	let exhibitor = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: FairsSearchParam.Exhibitor,
		})
	);

	const dataCyPrefix = 'search';

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (typeof window !== 'undefined' && to?.url.pathname !== Routes.Fairs) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				await setArteyeSearch(Searches.Fair, `${Routes.Fairs}?${queryParams}`);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		title,
		name,
		startDateRange,
		startDate,
		endDateRange,
		endDate,
		city,
		country,
		exhibitor,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		title = '';
		name = '';
		startDateRange = ValueFilterOperator.Equal;
		startDate = '';
		endDateRange = ValueFilterOperator.Equal;
		endDate = '';
		city = '';
		country = '';
		exhibitor = '';
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search fairs</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.Fairs}/new`}
			class="hidden lg:block"
		>
			Create New</LinkButton
		>
	</div>
	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="lg:col-span-1 col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-title`}
				name="title"
				placeholder="Enter title"
				label="Fair title"
				bind:value={title}
				size="sm"
			/>
		</div>
		<div class="lg:col-span-1 col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-organisation-name`}
				name="name"
				placeholder="Enter name"
				label="Organisation Name"
				bind:value={name}
				size="sm"
			/>
		</div>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Start date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-start-date`}
					name="start_date"
					bind:selectValue={startDateRange}
					bind:inputValue={startDate}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[startDateRange]}
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="End date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-end-date`}
					name="end_date"
					bind:selectValue={endDateRange}
					bind:inputValue={endDate}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[endDateRange]}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-city`}
					name="city"
					placeholder=""
					label="City"
					bind:value={city}
					size="sm"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-country`}
					name="country"
					placeholder=""
					label="Country"
					bind:value={country}
					size="sm"
				/>
			</div>
		</div>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-fair-contains-exhibitor`}
					name="exhibitor"
					placeholder=""
					label="Fair contains exhibitor"
					bind:value={exhibitor}
					size="sm"
				/>
			</div>
			<MultiSelect
				name="sort_by"
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				bind:selected={sort}
				placeholder="Sort by"
				options={FAIRS_SORT_OPTIONS}
				class="col-span-1"
				size="sm"
			/>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
