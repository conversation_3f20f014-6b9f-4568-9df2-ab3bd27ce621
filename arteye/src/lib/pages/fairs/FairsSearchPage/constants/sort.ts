import { FairSearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const FAIRS_SORT_OPTIONS = sortByField(
	[
		{ label: 'Date created (Asc)', value: FairSearchSortField.DateCreated },
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(FairSearchSortField.DateCreated),
		},
		{ label: 'Date updated (Asc)', value: FairSearchSortField.DateUpdated },
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(FairSearchSortField.DateUpdated),
		},
		{ label: 'Title (A-Z)', value: FairSearchSortField.Title },
		{
			label: 'Title (Z-A)',
			value: getDescendingDirection(FairSearchSortField.Title),
		},
		{
			label: 'Organisation name (A-Z)',
			value: FairSearchSortField.OrganisationName,
		},
		{
			label: 'Organisation name (Z-A)',
			value: getDescendingDirection(FairSearchSortField.OrganisationName),
		},
		{ label: 'Start date (Asc)', value: FairSearchSortField.StartDate },
		{
			label: 'Start date (Desc)',
			value: getDescendingDirection(FairSearchSortField.StartDate),
		},
		{ label: 'End date (Asc)', value: FairSearchSortField.EndDate },
		{
			label: 'End date (Desc)',
			value: getDescendingDirection(FairSearchSortField.EndDate),
		},
		{ label: 'City (A-Z)', value: FairSearchSortField.City },
		{
			label: 'City (Z-A)',
			value: getDescendingDirection(FairSearchSortField.City),
		},
		{ label: 'Country (A-Z)', value: FairSearchSortField.Country },
		{
			label: 'Country (Z-A)',
			value: getDescendingDirection(FairSearchSortField.Country),
		},
		{
			label: 'Exhibitor (A-Z)',
			value: FairSearchSortField.ExhibitorName,
		},
		{
			label: 'Exhibitor (Z-A)',
			value: getDescendingDirection(FairSearchSortField.ExhibitorName),
		},
		{
			label: 'No. of exhibitors (Asc)',
			value: FairSearchSortField.NumberOfExhibitors,
		},
		{
			label: 'No. of exhibitors (Desc)',
			value: getDescendingDirection(FairSearchSortField.NumberOfExhibitors),
		},
	],
	'label'
);

export const FAIR_DEFAULT_SORT = FairSearchSortField.Title;
