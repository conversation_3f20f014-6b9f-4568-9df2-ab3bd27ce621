<script lang="ts">
	import dayjs from 'dayjs';
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { FairSearchSortField } from '$gql/types-custom';
	import { TableHeaderSortArrow } from '$lib/components/TableHeaderSortArrow';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { Routes } from '$lib/constants/routes';
	import { SearchParam } from '$lib/types/types';
	import { getCityAndCountryFromLocation } from '$lib/utils/getCityAndCountry/getCityAndCountry';
	import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import type { FairsSearchPageData } from '$routes/fairs/types';

	let loading = false;

	let data = $derived(getPageData<FairsSearchPageData>(page.data));

	let resultsCount = $derived(data.resultsCount);
	let fairs = $derived(data.fairs);
	let searchParams = $derived(page.url.searchParams);
	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
		sort?: {
			asc: string;
			desc: string;
		};
	}

	interface Row {
		fair: NonNullable<FairsSearchPageData['fairs']>[0];
		fields: {
			[HeaderFieldName.Title]: string;
			[HeaderFieldName.OrganisationName]: string;
			[HeaderFieldName.StartDate]: string;
			[HeaderFieldName.EndDate]: string;
			[HeaderFieldName.Location]: string;
			[HeaderFieldName.NumberOfExhibitors]: number | string;
			[HeaderFieldName.Action]: '';
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.Title,
			title: 'Fair Title',
			sort: {
				asc: FairSearchSortField.Title,
				desc: getDescendingDirection(FairSearchSortField.Title),
			},
		},
		{
			fieldName: HeaderFieldName.OrganisationName,
			title: 'Fair Organisation Name',
			sort: {
				asc: FairSearchSortField.OrganisationName,
				desc: getDescendingDirection(FairSearchSortField.OrganisationName),
			},
		},
		{
			fieldName: HeaderFieldName.StartDate,
			title: 'Start Date',
			sort: {
				asc: FairSearchSortField.StartDate,
				desc: getDescendingDirection(FairSearchSortField.StartDate),
			},
		},
		{
			fieldName: HeaderFieldName.EndDate,
			title: 'End Date',
			sort: {
				asc: FairSearchSortField.EndDate,
				desc: getDescendingDirection(FairSearchSortField.EndDate),
			},
		},
		{
			fieldName: HeaderFieldName.Location,
			title: 'Location',
			sort: {
				asc: FairSearchSortField.City,
				desc: getDescendingDirection(FairSearchSortField.City),
			},
		},
		{
			fieldName: HeaderFieldName.NumberOfExhibitors,
			title: 'No. of exhibitors',
			sort: {
				asc: FairSearchSortField.NumberOfExhibitors,
				desc: getDescendingDirection(FairSearchSortField.NumberOfExhibitors),
			},
		},
		{
			fieldName: HeaderFieldName.Action,
			title: '',
		},
	];

	let rows = $derived(
		(fairs || []).map((fair) => ({
			fair,
			fields: {
				title: fair?.title || '',
				organisationName: fair?.fair_organisation?.organisation?.name || '',
				startDate: fair?.local_start_date
					? (dayjs(fair?.local_start_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						) as string)
					: '-',
				endDate: fair?.local_end_date
					? (dayjs(fair?.local_end_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						) as string)
					: '-',
				location: getCityAndCountryFromLocation(fair?.venue_city),
				numberOfExhibitors: fair?.aggregations?.[0]?.exhibitor_count || '-',
				action: '',
			},
		})) satisfies Row[]
	);

	const dataCyPrefix = 'table';

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		searchParams.set(SearchParam.Page, page.toString());

		const searchParamString = searchParams.toString();

		goto(`?${searchParamString}`, { invalidateAll: true });
	};

	const handleRowClick = (fairId: string) => {
		goto(`${Routes.Fairs}/${fairId}`);
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border border-b-0 border-gray-200 bg-white p-4"
>
	<Txt variant="h6">
		{getPaginationResultsText({
			currentPage,
			total: resultsCount,
			pageSize: TABLE_PAGE_SIZE,
		})}
	</Txt>
</div>

<div class="w-full overflow-x-auto mb-4">
	<div class="min-w-[1300px]">
		{#if fairs !== null}
			<table class=" w-full table-fixed border-collapse rounded-b-md bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader dataCy={dataCyPrefix}>
							{#snippet custom()}
								{#if header.sort}
									<TableHeaderSortArrow
										sortParamKey={SearchParam.Sort}
										asc={header.sort.asc}
										desc={header.sort.desc}
										{searchParams}
									>
										{header.title}
									</TableHeaderSortArrow>
								{:else}
									<Txt variant="label3">{header.title}</Txt>
								{/if}
							{/snippet}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#if loading}
						<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
							<CircularProgress dataCy={dataCyPrefix} />
						</TableNoResults>
					{/if}

					{#if !loading}
						{#each rows as row, i}
							<TableRow
								index={i}
								dataCy={dataCyPrefix}
								onclick={() => handleRowClick(row.fair?.id as string)}
							>
								{#each headers as header}
									{@const value = row.fields[header.fieldName]}

									{@const fair = row.fair}

									{#if header.fieldName === HeaderFieldName.Action}
										<TableActionCell dataCy={dataCyPrefix} class="w-10 py-0">
											<Button
												class="bg-transparent border-none px-0"
												size="sm"
												variant="tertiary"
												dataCy={`${dataCyPrefix}-cell`}
												onclick={(e) => {
													e?.stopPropagation();
													navigator.clipboard.writeText(`${fair?.id}`);
												}}
											>
												<CopyIcon />
											</Button>
											<LinkButton
												dataCy={`${dataCyPrefix}-cell`}
												variant="tertiary"
												href={`${Routes.Fairs}/${fair.id}`}
												size="sm"
												buttonProps={{ class: 'bg-transparent border-none' }}
											>
												<ChevronRightIcon class="h-[14px] w-[14px]" />
											</LinkButton>
										</TableActionCell>
									{:else}
										<TableCell
											dataCy={dataCyPrefix}
											content={value}
											class="py-0"
										>
											{value}
										</TableCell>
									{/if}
								{/each}
							</TableRow>
						{/each}

						{#if rows.length === 0}
							<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
								No results found
							</TableNoResults>
						{/if}
					{/if}
				</TableBody>
			</table>
		{/if}
	</div>
</div>

{#key resultsCount}
	{#key currentPage}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				{#key currentPage}
					{#key fairs?.length}
						<Pagination
							{currentPage}
							total={resultsCount}
							limit={TABLE_PAGE_SIZE}
							dataCy={dataCyPrefix}
							onClick={handlePaginationClick}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	{/key}
{/key}
