import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetFairsDocument } from '$lib/queries/__generated__/getFairs.generated';
import { PipelineSourceItem } from '$lib/types/types';
import type { FairDetailsPageLoadEvent } from '$routes/fairs/[id]/types';

export const fairDetailsPageLoad = async ({
	parent,
	params,
}: FairDetailsPageLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();
	const fairId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const fairsReq =
		fairId === PARAM_NEW
			? {
					fair: [],
				}
			: gqlClient.request(
					GetFairsDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: fairId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const pipelineSourceReq =
		fairId === PARAM_NEW
			? {
					getDatasource: { data_source: null },
				}
			: gqlClientCustom.request(
					GetDatasourceDocument,
					{
						collection: PipelineSourceItem.Fair,
						id: fairId,
					},
					authHeaders
				);

	const [fairsRes, pipelineSourceRes] = await Promise.all([
		fairsReq,
		pipelineSourceReq,
	]);

	const fair = fairsRes?.fair[0];
	const pipelineSource = pipelineSourceRes.getDatasource?.data_source;

	return {
		...data,
		fair,
		pipelineSource,
	};
};
