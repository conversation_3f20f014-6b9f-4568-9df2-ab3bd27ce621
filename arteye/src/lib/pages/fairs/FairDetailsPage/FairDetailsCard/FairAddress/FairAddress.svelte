<script lang="ts" module>
	export type CountryType = NonNullable<
		NonNullable<FairDetailsPageData['fair']>['venue_country']
	>;
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import type { FairDetailsForm } from '..';
	import { FairAddressesFieldName } from './types';
	import { page } from '$app/state';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableBody,
		TableCell,
		TableHeader,
		TableHeaderRow,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import {
		CityAutocomplete,
		formatLocation,
	} from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CityAutocomplete';
	import {
		CountrySelectedOption,
		formatCountry,
	} from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CountrySelectedOption';
	import type { FairDetailsPageData } from '$routes/fairs/[id]/types';

	interface Props {
		onChange: (form: FairDetailsForm) => void;
		fairDetailsForm: FairDetailsForm;
		handleInputChange: (field: keyof FairDetailsForm) => (event: Event) => void;
	}

	let { fairDetailsForm, handleInputChange, onChange }: Props = $props();

	let fair = $derived(page.data.fair as FairDetailsPageData['fair']);
	const dataCy = 'addresses';
	let cityValue = writable('');
	let countryValue = writable('');

	let countrySelectedOption = $state(
		(() => (fair?.venue_country ? formatCountry(fair.venue_country) : null))()
	) as ReturnType<typeof formatCountry> | null;

	$effect(() => {
		countrySelectedOption = fair?.venue_country
			? formatCountry(fair.venue_country)
			: null;
	});

	let selectedOption = $state(
		(() => (fair?.venue_city ? formatLocation(fair.venue_city) : null))()
	) as ReturnType<typeof formatLocation> | null;

	$effect(() => {
		selectedOption = fair?.venue_city ? formatLocation(fair.venue_city) : null;
	});

	const headers = [
		{
			fieldName: FairAddressesFieldName.Country,
			title: 'Country',
		},
		{
			fieldName: FairAddressesFieldName.City,
			title: 'Town/City',
		},
		{
			fieldName: FairAddressesFieldName.Line1,
			title: 'Street address 1',
		},
		{
			fieldName: FairAddressesFieldName.Line2,
			title: 'Street address 2',
		},
		{
			fieldName: FairAddressesFieldName.Line3,
			title: 'Street address 3',
		},
		{
			fieldName: FairAddressesFieldName.PostCode,
			title: 'Postcode/Zipcode',
		},
	];

	const handleCityClear = () => {
		onChange({
			...fairDetailsForm,
			venue_city: null,
			venue_country: null,
		});

		countrySelectedOption = null;
	};

	const handleChangeCity = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const cityOption = e.detail.value;
		const country = cityOption.line4 ? JSON.parse(cityOption.line4) : null;

		onChange({
			...fairDetailsForm,
			venue_city: {
				name: `${cityOption.line5}`,
				short_code: `${cityOption.line2}`,
				code: `${cityOption.line3}`,
			},
			...(cityOption.line4 && {
				venue_country: country,
			}),
		});

		countrySelectedOption = country ? formatCountry(country) : null;
		return Promise.resolve();
	};
</script>

<div class="mb-4">
	<Txt variant="label3" class="mb-2">Fair Address</Txt>
	<div class="max-lg:overflow-x-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1200px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					<TableHeader {dataCy}>
						{header.title}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				<TableRow index={0} {dataCy}>
					{#each headers as header}
						{#if header.fieldName === FairAddressesFieldName.Country}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<CountrySelectedOption
										{dataCy}
										bind:selectedOption={countrySelectedOption}
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.City}
							<TableCell {dataCy} class="">
								{#snippet custom()}
									<CityAutocomplete
										{dataCy}
										disabled={false}
										onRemoveSelectedOption={handleCityClear}
										bind:selectedOption
										onChange={handleChangeCity}
										value={cityValue}
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line1}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-1`}
										name="line-1"
										placeholder="line 1"
										value={fairDetailsForm.venue_address_1}
										onkeyup={handleInputChange('venue_address_1')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line2}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-2`}
										name="line-2"
										placeholder="line 2"
										value={fairDetailsForm.venue_address_2}
										onkeyup={handleInputChange('venue_address_2')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.Line3}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-3`}
										name="line-3"
										placeholder="line 3"
										value={fairDetailsForm.venue_address_3}
										onkeyup={handleInputChange('venue_address_3')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === FairAddressesFieldName.PostCode}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-postcode`}
										name="postcode"
										placeholder="postcode"
										value={fairDetailsForm.venue_post_code}
										onkeyup={handleInputChange(FairAddressesFieldName.PostCode)}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{/if}
					{/each}
				</TableRow>
			</TableBody>
		</table>
	</div>
</div>
