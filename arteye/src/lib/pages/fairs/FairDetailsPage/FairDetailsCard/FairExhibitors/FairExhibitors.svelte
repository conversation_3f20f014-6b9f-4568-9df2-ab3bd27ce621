<script lang="ts">
	import type { FairDetailsForm } from '..';
	import { ExhibitorsFieldName, FairExhibitorsRow } from './FairExhibitorsRow';
	import { formatEntity } from './FairExhibitorsRow/ExhibitorsNameAutoComplete/ExhibitorsNameAutoComplete.svelte';
	import type { ExhibitorType } from './FairExhibitorsRow/FairExhibitorsRow.svelte';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import type { EntitySearchQuery } from '$lib/custom-queries/__generated__/entitySearch.generated';

	const headers = [
		{
			title: 'Name',
			fieldName: ExhibitorsFieldName.Name,
		},
		{ title: '' },
	];

	interface Props {
		fairDetailsForm: FairDetailsForm;
		exhibitors?: FairDetailsForm['exhibitors'];
		onChange: (fairDetailsForm: FairDetailsForm) => void;
	}

	let { fairDetailsForm, exhibitors = [], onChange }: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(exhibitors || []).map((exhibitor: (typeof exhibitors)[number]) => {
			if (!exhibitor) return null;

			return formatEntity(
				exhibitor.entity as EntitySearchQuery['entitySearch']['data'][number]
			);
		})
	);

	const onExhibitorChange =
		(index: number) => (newExhibitor: ExhibitorType) => {
			onChange({
				...fairDetailsForm,
				exhibitors: (exhibitors || []).map(
					(exhibitor: (typeof exhibitors)[number], i: number) =>
						i === index
							? {
									...exhibitor,
									entity: newExhibitor,
									isNew: true,
								}
							: exhibitor
				),
			});
		};

	const handleExhibitorDelete = (
		deletedExhibitor: NonNullable<FairDetailsForm['exhibitors']>[number]
	) => {
		onChange({
			...fairDetailsForm,
			exhibitors: (exhibitors || []).map(
				(exhibitor: (typeof exhibitors)[number]) => {
					if (exhibitor?.id === deletedExhibitor.id) {
						return {
							...exhibitor,
							isDeleted: true,
						};
					}
					return exhibitor;
				}
			),
		});
	};

	const dataCy = 'exhibitors';

	const handleExhibitorAdd = () => {
		onChange({
			...fairDetailsForm,
			exhibitors: [
				...exhibitors,
				{
					isNew: true,
				},
			] as FairDetailsForm['exhibitors'],
		});

		selectedOptions = [...selectedOptions, null];
	};
</script>

<AccordionItem
	{dataCy}
	title="Exhibitor details"
	class="mb-20 rounded-md border bg-white"
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="px-4 pb-8 max-lg:overflow-x-auto">
		<table class="w-full table-fixed bg-white min-w-[1000px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName}
						<TableHeader {dataCy}>
							{#snippet custom()}
								<div class="flex items-center">
									<Txt variant="label3">
										{header.title}
									</Txt>
									<Mandatory />
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleExhibitorAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if exhibitors}
					{#each exhibitors as exhibitor, index}
						{#if !exhibitor.isDeleted}
							<FairExhibitorsRow
								{headers}
								{exhibitor}
								{index}
								bind:selectedOption={selectedOptions[index]}
								onChange={onExhibitorChange(index)}
								onDelete={handleExhibitorDelete}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</AccordionItem>
