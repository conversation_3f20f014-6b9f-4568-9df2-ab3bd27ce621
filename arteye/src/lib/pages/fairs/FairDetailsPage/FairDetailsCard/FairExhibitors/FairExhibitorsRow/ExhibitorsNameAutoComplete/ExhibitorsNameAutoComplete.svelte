<script lang="ts" module>
	export const formatEntity = (
		entity: EntitySearchQuery['entitySearch']['data'][number]
	) => {
		const url = (() => {
			if (entity?.type?.key === 'person') {
				if (entity?.artist) {
					return `${Routes.Artists}/${entity?.artist?.id}`;
				} else {
					return `${Routes.People}/${entity?.person?.id}`;
				}
			}
			return `${Routes.Organisations}/${entity?.organisation?.id}`;
		})();

		const location = (
			entity?.type?.key === 'person'
				? [
						entity?.addresses?.[0]?.city?.name,
						entity?.addresses?.[0]?.country?.name,
					]
				: [
						entity?.organisation?.location?.name,
						entity?.organisation?.location?.country?.name,
					]
		)
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import type { ExhibitorType } from '../FairExhibitorsRow.svelte';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Routes } from '$lib/constants/routes';
	import {
		EntitySearchDocument,
		type EntitySearchQuery,
		type EntitySearchQueryVariables,
	} from '$lib/custom-queries/__generated__/entitySearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	let entities: EntitySearchQuery['entitySearch']['data'] = [];

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		error: string;
		onBlur: () => void;
		onRemoveSelectedOption: (() => void) | undefined;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: ExhibitorType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		error,
		onBlur,
		onRemoveSelectedOption,
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity?.id && entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity as ExhibitorType,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getVariables = (value: string): EntitySearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : -1,
				filters: {
					// TODO arteye: support id
					nameOrId: value,
				},
			},
		};
	};

	const getOptions = (data: EntitySearchQuery | undefined) => {
		setTimeout(() => {
			entities = data?.entitySearch?.data || [];
		}, 0);

		return [...(data?.entitySearch?.data || []).map(formatEntity)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		{onRemoveSelectedOption}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		{onBlur}
		getError={() => error}
		dataCy={`${dataCy}-entity`}
		{placeholder}
		emptyValueResponse={{ entitySearch: { data: [] } }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientCustom}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={EntitySearchDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-exhibitiors-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				if (onRemoveSelectedOption) {
					onRemoveSelectedOption();
				}
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
