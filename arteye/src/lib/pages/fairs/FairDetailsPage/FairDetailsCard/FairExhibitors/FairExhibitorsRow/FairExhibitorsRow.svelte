<script lang="ts" module>
	export type ExhibitorType = NonNullable<
		NonNullable<NonNullable<FairDetailsPageData['fair']>['exhibitors']>[number]
	>['entity'];
</script>

<script lang="ts">
	import type { FairDetailsForm } from '../..';
	import { ExhibitorsNameAutoComplete } from './ExhibitorsNameAutoComplete';
	import { ExhibitorsFieldName } from './types';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import type { FairDetailsPageData } from '$routes/fairs/[id]/types';

	let blur = $state(false);

	interface Props {
		index: number;
		exhibitor: NonNullable<FairDetailsForm['exhibitors']>[number];
		headers: { fieldName?: ExhibitorsFieldName }[];
		selectedOption?: OptionType | null;
		onChange: (exhibitor: ExhibitorType) => void;
		onDelete: (
			exhibitor: NonNullable<FairDetailsForm['exhibitors']>[number]
		) => void;
	}

	let {
		index,
		exhibitor,
		headers,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
	}: Props = $props();

	let fair = $derived(page.data.fair as FairDetailsPageData['fair']);

	const dataCy = 'exhibitor-row';

	const handleDeleteExhibitor = () => {
		onDelete(exhibitor);
	};

	const handleEntityClear = () => {
		onChange(null);
	};

	const handleEntityChange = (e: {
		detail: {
			value: ExhibitorType;
		};
	}) => {
		onChange(e.detail.value);

		return Promise.resolve();
	};

	const getArtworksLink = (exhibitor: FairDetailsForm['exhibitors'][0]) => {
		if (!exhibitor) return '';
		return `${Routes.ArtworksAndActivities}?showResults=true&searchType=advanced&fairExhibitor=${exhibitor?.id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`;
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={exhibitor} />

					<Button
						disabled={!!exhibitor.artwork_listings?.length}
						onclick={handleDeleteExhibitor}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === ExhibitorsFieldName.Name}
			<TableCell {dataCy}>
				{#snippet custom()}
					<div class="flex items-center">
						<div class="mr-2 flex-grow">
							<ExhibitorsNameAutoComplete
								bind:selectedOption
								{dataCy}
								onBlur={() => {
									blur = true;
								}}
								error={!exhibitor.entity && (fair || blur)
									? 'Please provide an exhibitor'
									: ''}
								onRemoveSelectedOption={handleEntityClear}
								onChange={handleEntityChange}
							/>
						</div>
						<div class="w-[140px]">
							{#if exhibitor.entity}
								<LinkButton
									href={getArtworksLink(exhibitor)}
									dataCy={`${dataCy}-exhibitor-id`}
									class="h-[2rem]"
									variant="secondary"
									size="sm"
								>
									VIEW ARTWORKS
								</LinkButton>
							{/if}
						</div>
					</div>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
