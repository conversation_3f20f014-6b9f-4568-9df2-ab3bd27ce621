<script lang="ts">
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { FairAddress } from './FairAddress';
	import { FairOrganiserAutoComplete } from './FairOrganiserAutoComplete';
	import { formatOrganisation } from './FairOrganiserAutoComplete/FairOrganiserAutoComplete.svelte';
	import type { FairDetailsForm } from './types';
	import { page } from '$app/state';
	import { InfoIcon } from '$global/assets/icons/InfoIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Tooltip } from '$global/components/Tooltip';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';
	import { type OrganisationSearchQuery } from '$lib/custom-queries/__generated__/organisationSearch.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { GetFairOrganisationsDocument } from '$lib/queries/__generated__/getFairOrganisations.generated';
	import type { FairDetailsPageData } from '$routes/fairs/[id]/types';

	interface Props {
		fairDetailsForm: FairDetailsForm;
		onChange: (form: FairDetailsForm) => void;
		class?: string;
	}

	let { ...props }: Props = $props();

	let data = $derived(getPageData<FairDetailsPageData>(page.data));
	let fair = $derived(data.fair);

	let blur = $state({
		title: false,
		startDate: false,
		endDate: false,
		organiser: false,
		url: false,
	});

	const dataCy = 'fair-details-card';

	const handleInputChange =
		(field: keyof FairDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			props.onChange({ ...props.fairDetailsForm, [field]: value });
		};

	let selectedOption: OptionType | null = $state(null);

	$effect(() => {
		selectedOption = fair?.fair_organisation
			? (formatOrganisation(
					fair.fair_organisation.organisation as Parameters<
						typeof formatOrganisation
					>[0]
				) as OptionType)
			: null;
	});

	const handleOrganiserChange = async (e: {
		detail: {
			value:
				| OrganisationSearchQuery['organisationSearch']['data'][number]
				| null;
		};
	}) => {
		const organisation = e.detail.value;

		let fairOrganisationId: string | null | undefined;

		if (organisation) {
			const fairOrganisationRes = await gqlClient.request(
				GetFairOrganisationsDocument,
				{ filter: { organisation: { id: { _eq: organisation.id } } } },
				getAuthorizationHeaders(data)
			);

			fairOrganisationId = fairOrganisationRes?.fair_organisation?.[0]?.id;
		}

		props.onChange({
			...props.fairDetailsForm,
			fair_organisation: (fairOrganisationId
				? { id: fairOrganisationId }
				: {
						organisation: organisation
							? {
									id: organisation.id,
									name: organisation.name,
									type: organisation.type,
								}
							: null,
					}) as FairDetailsForm['fair_organisation'],
		});

		selectedOption = e.detail.value ? formatOrganisation(e.detail.value) : null;

		return Promise.resolve();
	};
</script>

<AccordionItem
	{dataCy}
	title="Fair details"
	class={twMerge('rounded-md border bg-white', props.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4">
		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-2">
				<div class="flex justify-between">
					<InputLabel {dataCy} required variant="label3" class="mb-2"
						>Fair organisation</InputLabel
					>
					<Tooltip {dataCy} content="Select a fair organistaion">
						<InfoIcon color="gray-300" class="h-[20px]" />
					</Tooltip>
				</div>
				<FairOrganiserAutoComplete
					onBlur={() => {
						blur.organiser = true;
					}}
					error={!props.fairDetailsForm.fair_organisation &&
					(fair || blur.organiser)
						? 'Please provide an organiser'
						: ''}
					bind:selectedOption
					{dataCy}
					onChange={handleOrganiserChange}
				/>
			</div>
			<div class="col-span-2">
				<Input
					dataCy={`${dataCy}-title`}
					name="title"
					placeholder=""
					label="Fair Title"
					tooltip="Enter the title of the fair"
					tooltipColor="gray-300"
					required
					value={props.fairDetailsForm.title || ''}
					onblur={() => {
						blur.title = true;
					}}
					error={!props.fairDetailsForm.title && (fair || blur.title)
						? 'Please provide a title'
						: ''}
					size="sm"
					onkeyup={handleInputChange('title')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-fairUrl`}
					name="fairUrl"
					placeholder=""
					label="Fair URL"
					value={props.fairDetailsForm.fair_url || ''}
					onblur={() => {
						blur.url = true;
					}}
					error={props.fairDetailsForm.fair_url &&
					!isValidUrl(props.fairDetailsForm.fair_url) &&
					blur.url
						? 'Please provide a valid URL'
						: ''}
					size="sm"
					onkeyup={handleInputChange('fair_url')}
				/>
			</div>
		</div>
		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-startDate`}
					name="startDate"
					placeholder="Start Date"
					required
					label="Start Date"
					type="date"
					tooltip="Enter the start date of the fair"
					tooltipColor="gray-300"
					onblur={() => {
						blur.startDate = true;
					}}
					error={!props.fairDetailsForm.start_date && (fair || blur.startDate)
						? 'Please provide a start date'
						: ''}
					value={getDayFromDirectus(props.fairDetailsForm.start_date || '')}
					size="sm"
					onkeyup={handleInputChange('start_date')}
					onchange={handleInputChange('start_date')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-endDate`}
					name="endDate"
					placeholder="End Date"
					required
					label="End Date"
					type="date"
					tooltip="Enter the end date of the fair"
					tooltipColor="gray-300"
					onblur={() => {
						blur.endDate = true;
					}}
					error={!props.fairDetailsForm.end_date && (fair || blur.endDate)
						? 'Please provide an end date'
						: ''}
					value={getDayFromDirectus(props.fairDetailsForm.end_date || '')}
					size="sm"
					onkeyup={handleInputChange('end_date')}
					onchange={handleInputChange('end_date')}
				/>
			</div>
		</div>

		<div class="mb-4">
			<FairAddress
				onChange={props.onChange}
				fairDetailsForm={props.fairDetailsForm}
				{handleInputChange}
			/>
		</div>
	</div>
</AccordionItem>
