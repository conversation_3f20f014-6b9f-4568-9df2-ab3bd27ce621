<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { FairDetailsCard } from './FairDetailsCard';
	import type { FairDetailsForm } from './FairDetailsCard';
	import { FairExhibitors } from './FairDetailsCard/FairExhibitors';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { UpdateFairItemDocument } from '$lib/mutations/__generated__/updateFair.generated';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { getQueryStrings } from '$lib/utils/getQueryStrings/getQueryStrings';
	import { mutateFair } from '$lib/utils/mutation-handlers/mutateFair/mutateFair';
	import type { FairDetailsPageData } from '$routes/fairs/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let data = $derived(getPageData<FairDetailsPageData>(page.data));
	let fair = $derived(page.data.fair as FairDetailsPageData['fair']);

	let getSortedExihbitors = $derived(() => {
		const sortedExhibitors = (fair?.exhibitors || []).sort((a, b) => {
			const nameA = (a?.entity?.name || '')?.toLowerCase();
			const nameB = (b?.entity?.name || '')?.toLowerCase();

			if (nameA < nameB) return -1;
			if (nameA > nameB) return 1;
			return 0;
		});

		return sortedExhibitors.map((exhibitor) =>
			exhibitor?.status?.key === 'archived'
				? { ...exhibitor, isDeleted: true }
				: exhibitor
		);
	});

	let fairDetailsForm = $state(
		(() => ({
			id: fair?.id,
			title: fair?.title,
			fair_url: fair?.fair_url,
			start_date: dayjs(fair?.local_start_date.slice(0, -1)).format(
				'YYYY-MM-DD'
			),
			end_date: dayjs(fair?.local_end_date.slice(0, -1)).format('YYYY-MM-DD'),
			fair_organisation: { id: fair?.fair_organisation?.id },
			venue_country: fair?.venue_country,
			venue_city: fair?.venue_city,
			venue_address_1: fair?.venue_address_1,
			venue_address_2: fair?.venue_address_2,
			venue_address_3: fair?.venue_address_3,
			venue_post_code: fair?.venue_post_code,
			exhibitors: getSortedExihbitors(),
		}))() as FairDetailsForm
	);

	$effect(() => {
		fairDetailsForm = {
			id: fair?.id,
			title: fair?.title,
			fair_url: fair?.fair_url,
			start_date: dayjs(fair?.local_start_date.slice(0, -1)).format(
				'YYYY-MM-DD'
			),
			end_date: dayjs(fair?.local_end_date.slice(0, -1)).format('YYYY-MM-DD'),
			fair_organisation: { id: fair?.fair_organisation?.id },
			venue_country: fair?.venue_country,
			venue_city: fair?.venue_city,
			venue_address_1: fair?.venue_address_1,
			venue_address_2: fair?.venue_address_2,
			venue_address_3: fair?.venue_address_3,
			venue_post_code: fair?.venue_post_code,
			exhibitors: getSortedExihbitors(),
		} as FairDetailsForm;
	});

	const mandatoryFields = [
		'title',
		'start_date',
		'end_date',
		'fair_organisation',
	];

	let loading = $state(false);
	let showSaveBar = $state(false);

	let crumbs = $derived([
		{ label: 'Search fairs', href: userRoutes.routes.fair },
		{ label: `${fair?.title || 'New Fair'}` },
	]);

	let deleting = $state(false);

	const dataCy = 'fair-details';

	const handleClickDeleteFair = async () => {
		try {
			deleting = true;
			await gqlClient.request(
				UpdateFairItemDocument,
				{
					id: fair.id,
					data: { status: { key: Status_Enum.Archived } },
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'This fair has been successfully deleted.',
			});

			goto(userRoutes.routes.fair);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while deleting this fair.',
			});

			deleting = false;
		}
	};

	const handleFairDetailsForm = (fairDetails: FairDetailsForm) => {
		fairDetailsForm = fairDetails;

		showSaveBar = mandatoryFields.every(
			(field) => fairDetailsForm[field as keyof FairDetailsForm]
		);
	};

	const handleExhibitorsChange = (fairDetails: FairDetailsForm) => {
		fairDetailsForm = fairDetails;
		showSaveBar = true;
	};

	const handleSaveClick = async () => {
		loading = true;
		try {
			const headers = getAuthorizationHeaders(data);

			const fairId = await mutateFair({
				fairDetailsForm,
				headers,
			});

			showToast({
				variant: 'success',
				message: `This fair has been successfully updated.`,
			});

			if (fairDetailsForm?.id) {
				(window.location as unknown as string) = `${Routes.Fairs}/${fairDetailsForm?.id}`;
			} else {
				(window.location as unknown as string) = `${Routes.Fairs}/${fairId}`;
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this fair update. Please contact the support team.',
			});

			loading = false;
			showSaveBar = false;
		}
	};
</script>

<div class={classNames({ 'pointer-events-none': loading })}>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if fair}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<Button
					onclick={handleClickDeleteFair}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&searchType=advanced&fairName=${fair?.id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					VIEW ARTWORKS
				</LinkButton>
				<Button
					onclick={() => {
						navigator.clipboard.writeText(`${fair?.id}`);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy fair id
				</Button>
			</div>
		{/if}
	</div>

	{#if fair}
		<CreateUpdate
			updateHistory={fair}
			pipelineSource={data.pipelineSource}
			class="block mb-5 mt-[-12px]"
		/>
	{/if}

	<FairDetailsCard
		{fairDetailsForm}
		onChange={handleFairDetailsForm}
		class="mb-4"
	/>
	<FairExhibitors
		onChange={handleExhibitorsChange}
		{fairDetailsForm}
		exhibitors={fairDetailsForm.exhibitors}
	/>
</div>

<PageSaveBar
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
	{loading}
	disabled={loading ||
		(fairDetailsForm?.fair_url && !isValidUrl(fairDetailsForm?.fair_url)) ||
		!fairDetailsForm?.fair_organisation ||
		!fairDetailsForm?.title ||
		!fairDetailsForm?.start_date ||
		!fairDetailsForm?.end_date ||
		dayjs(fairDetailsForm?.start_date) > dayjs(fairDetailsForm?.end_date) ||
		!fairDetailsForm.exhibitors.every(
			(exhibitor: (typeof fairDetailsForm)['exhibitors'][number]) =>
				exhibitor.isDeleted || (!exhibitor.isDeleted && exhibitor.entity)
		)}
/>
