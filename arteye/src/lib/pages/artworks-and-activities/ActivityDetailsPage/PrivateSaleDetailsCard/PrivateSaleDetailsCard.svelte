<script module lang="ts">
	export interface PrivateSaleDetailsForm {
		knownPriceLocalCurrency: {
			code: string;
		};
		knownPriceUsd: string;
		knownPriceLocal: string;

		lowEstimateLocalCurrency: {
			code: string;
		};
		lowEstimateUsd: string;
		lowEstimateLocal: string;

		highEstimateLocalCurrency: {
			code: string;
		};
		highEstimateUsd: string;
		highEstimateLocal: string;

		saleAmountLocalCurrency: {
			code: string;
		};
		saleAmountUsd: string;
		saleAmountLocal: string;

		updateHistory: UpdateHistory;
	}

	export interface PrivateSaleDetailsFormErrors {
		knownPriceLocalCurrencyCode: string;
		lowEstimateLocalCurrencyCode: string;
		highEstimateLocalCurrencyCode: string;
		saleAmountLocalCurrencyCode: string;
	}
</script>

<script lang="ts">
	import { Input } from '$global/components/Input';
	import { InputError } from '$global/components/Input/InputError';
	import { formatNumberWithSeparator } from '$global/components/Input/utils/formatNumberWithSeparator/formatNumberWithSeparator';
	import { formatWithThousandSeparator } from '$global/components/Input/utils/formatWithThousandSeparator/formatWithThousandSeparator';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { removeThousandSeparator } from '$global/components/Input/utils/removeThousandSeparator/removeThousandSeparator';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import type { InputWithSelectOption } from '$global/components/InputWithSelect';
	import { Txt } from '$global/components/Txt';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { UpdateHistory } from '$lib/components/UpdateHistoryTooltip';

	interface Props {
		privateSaleDetailsForm: PrivateSaleDetailsForm;
		privateSaleDetailsFormErrors: PrivateSaleDetailsFormErrors;
		currencyOptions: InputWithSelectOption[];
		onChange: (form: PrivateSaleDetailsForm) => void;
	}

	let {
		privateSaleDetailsForm,
		currencyOptions,
		onChange,
		privateSaleDetailsFormErrors,
	}: Props = $props();

	const dataCy = 'private-sale-details';

	const handleInputWithSelectChange =
		(
			field: keyof Pick<
				PrivateSaleDetailsForm,
				| 'knownPriceLocalCurrency'
				| 'lowEstimateLocalCurrency'
				| 'highEstimateLocalCurrency'
				| 'saleAmountLocalCurrency'
			>
		) =>
		(option: InputWithSelectOption) => {
			onChange({
				...privateSaleDetailsForm,
				[field]: {
					code: `${option?.value}`,
					name: `${option?.label}`,
				},
			});
		};

	const handleNumericInputChange =
		(
			field: keyof Pick<
				PrivateSaleDetailsForm,
				| 'knownPriceUsd'
				| 'knownPriceLocal'
				| 'lowEstimateUsd'
				| 'lowEstimateLocal'
				| 'highEstimateUsd'
				| 'highEstimateLocal'
				| 'saleAmountUsd'
				| 'saleAmountLocal'
			>
		) =>
		(event?: Event | undefined) => {
			const value = (event?.target as HTMLInputElement).value;
			onChange({
				...privateSaleDetailsForm,
				[field]: removeThousandSeparator(value),
			});
		};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-4">Private sale details</Txt>

	<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
		<div class="col-span-1">
			<Input
				label="Listing price (USD)"
				dataCy={`${dataCy}-knownPriceUsd`}
				name="knownPriceUsd"
				value={`${formatNumberWithSeparator(privateSaleDetailsForm.knownPriceUsd)}`}
				onkeyup={handleNumericInputChange('knownPriceUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>

		<div class="col-span-1">
			<InputWithSelect
				label="Listing price (local)"
				size="sm"
				dataCy={`${dataCy}-knownPriceLocal`}
				name="knownPriceLocal"
				selectValue={privateSaleDetailsForm.knownPriceLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(privateSaleDetailsForm.knownPriceLocal)}`}
				options={currencyOptions}
				placeholder=""
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange('knownPriceLocalCurrency')}
				onkeyup={handleNumericInputChange('knownPriceLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
			{#if privateSaleDetailsFormErrors.knownPriceLocalCurrencyCode}
				<InputError dataCy="knownPriceLocalError" class="mt-1">
					Please select a currency
				</InputError>
			{/if}
		</div>
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
		<div class="col-span-1">
			<Input
				label="Low estimate (USD)"
				dataCy={`${dataCy}-lowEstimateUsd`}
				name="lowEstimateUsd"
				value={`${formatNumberWithSeparator(privateSaleDetailsForm.lowEstimateUsd)}`}
				onkeyup={handleNumericInputChange('lowEstimateUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>

		<div class="col-span-1">
			<Input
				label="High estimate (USD)"
				dataCy={`${dataCy}-highEstimateUsd`}
				name="highEstimateUsd"
				value={`${formatNumberWithSeparator(privateSaleDetailsForm.highEstimateUsd)}`}
				onkeyup={handleNumericInputChange('highEstimateUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>

		<div class="col-span-1">
			<InputWithSelect
				label="Low estimate (local)"
				size="sm"
				dataCy={`${dataCy}-lowEstimateLocal`}
				name="lowEstimateLocal"
				selectValue={privateSaleDetailsForm.lowEstimateLocalCurrency.code}
				inputValue={`${privateSaleDetailsForm.lowEstimateLocal}`}
				options={currencyOptions}
				placeholder=""
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange('lowEstimateLocalCurrency')}
				onkeyup={handleNumericInputChange('lowEstimateLocal')}
				selectPlaceholder="Select"
			/>
			{#if privateSaleDetailsFormErrors.lowEstimateLocalCurrencyCode}
				<InputError dataCy="lowEstimateLocalError" class="mt-1">
					Please select a currency
				</InputError>
			{/if}
		</div>

		<div class="col-span-1">
			<InputWithSelect
				label="High estimate (local)"
				size="sm"
				dataCy={`${dataCy}-highEstimateLocal`}
				name="highEstimateLocal"
				selectValue={privateSaleDetailsForm.highEstimateLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(privateSaleDetailsForm.highEstimateLocal)}`}
				options={currencyOptions}
				placeholder=""
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange(
					'highEstimateLocalCurrency'
				)}
				onkeyup={handleNumericInputChange('highEstimateLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
			{#if privateSaleDetailsFormErrors.highEstimateLocalCurrencyCode}
				<InputError dataCy="highEstimateLocalError" class="mt-1">
					Please select a currency
				</InputError>
			{/if}
		</div>
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
		<div class="col-span-1">
			<Input
				label="Sale amount if known (USD)"
				dataCy={`${dataCy}-saleAmountUsd`}
				name="saleAmountUsd"
				value={`${formatNumberWithSeparator(privateSaleDetailsForm.saleAmountUsd)}`}
				onkeyup={handleNumericInputChange('saleAmountUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>

		<div class="col-span-1">
			<InputWithSelect
				label="Sale amount if known (local)"
				size="sm"
				dataCy={`${dataCy}-saleAmountLocal`}
				name="saleAmountLocal"
				selectValue={privateSaleDetailsForm.saleAmountLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(privateSaleDetailsForm.saleAmountLocal)}`}
				options={currencyOptions}
				placeholder=""
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange('saleAmountLocalCurrency')}
				onkeyup={handleNumericInputChange('saleAmountLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
			{#if privateSaleDetailsFormErrors.saleAmountLocalCurrencyCode}
				<InputError dataCy="saleAmountLocalError" class="mt-1">
					Please select a currency
				</InputError>
			{/if}
		</div>
	</div>

	<div class="flex justify-end">
		<UpdateHistoryTooltip
			updateHistory={privateSaleDetailsForm.updateHistory}
		/>
	</div>
</div>
