<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import type { AssociatedArtworksForm } from '../AssociatedArtworksCard.svelte';
	import { ArtworkAutocomplete } from './ArtworkAutocomplete';
	import ArtworkRemovalConfirmationDialog from './ArtworkRemovalConfirmationDialog/ArtworkRemovalConfirmationDialog.svelte';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import type { InputWithSelectOption } from '$global/components/InputWithSelect/InputWithSelect.svelte';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	interface Props {
		artworks: NonNullable<ActivityDetailsPageData['activity']>['artworks'];
		associatedArtworksForm: AssociatedArtworksForm;
		onArtworksChange: (artworks: AssociatedArtworksForm['artworks']) => void;
		onEditionsChange: (artworks: AssociatedArtworksForm['editions']) => void;
	}

	let {
		associatedArtworksForm,
		onArtworksChange,
		onEditionsChange,
		artworks,
	}: Props = $props();

	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));

	let editionTypesOptions = $derived(
		(data.user?.dropdowns.editionNumberTypes || []).map<InputWithSelectOption>(
			(type) => ({
				label: type.name || '',
				value: type.key,
			})
		)
	);

	const artworkRemovalConfirmationDialogStores = createDialog();
	const dataCy = 'associated-artworks';

	let showNew = $state(false);

	const handleAutocompleteChange = (option: OptionType) => {
		showNew = false;

		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';
		const activityArtworkRelationId = option.line7 || '';

		if (!id) return;

		if (!associatedArtworksForm.artworks[id]) {
			onArtworksChange({
				...associatedArtworksForm.artworks,
				[id]: {
					id,
					title,
					subTitle,
					activityArtworkRelationId,
					url,
					isNew: true,
				},
			});
			return;
		}

		onArtworksChange({
			...associatedArtworksForm.artworks,
			[id]: {
				...associatedArtworksForm.artworks[id],
				id,
				title,
				url,
				isDeleted: false,
			},
		});
	};

	let artworkToRemove: null | OptionType = $state(null);

	const handleOpenArtworkRemovalConfirmationDialog = (option: OptionType) => {
		artworkToRemove = option;
		artworkRemovalConfirmationDialogStores.states.open.set(true);
	};

	const handleCloseArtworkRemovalConfirmationDialog = () => {
		artworkRemovalConfirmationDialogStores.states.open.set(false);
		artworkToRemove = null;
	};

	const handleRemove = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';
		const activityArtworkRelationId = option.line7 || '';

		if (!id) return;

		onArtworksChange({
			...associatedArtworksForm.artworks,
			[id]: {
				id,
				title,
				subTitle,
				activityArtworkRelationId,
				url,
				isDeleted: true,
			},
		});
	};

	let options = $derived(
		Object.values(associatedArtworksForm?.artworks || {})
			.filter((artwork) => !artwork.isDeleted)
			.map((artwork) => {
				const option: OptionType = {
					line1: artwork.title,
					line2: artwork.subTitle,
					line3: artwork.url,
					line4: artwork.id,
					line7: artwork.activityArtworkRelationId,
				};

				return option;
			})
	);

	const handleClickAdd = () => {
		showNew = true;
	};

	export const SEARCH_RANGE_OPTIONS = [
		{ label: 'Equal to', value: 'SearchRange.EqualTo' },
	];

	const handleEditionTypeChange =
		(artworkId: string) => (option: InputWithSelectOption) => {
			onEditionsChange({
				...associatedArtworksForm.editions,
				[artworkId]: {
					...associatedArtworksForm.editions[artworkId],
					editionTypeKey: option.value,
				},
			});
		};

	const handleEditionNumberChange =
		(artworkId: string) => (event?: Event | undefined) => {
			onEditionsChange({
				...associatedArtworksForm.editions,
				[artworkId]: {
					...associatedArtworksForm.editions[artworkId],
					editionNumber: (event?.target as HTMLInputElement).value,
				},
			});
		};

	const handleLegacyEditionNumberChange =
		(artworkId: string) => (event?: Event | undefined) => {
			onEditionsChange({
				...associatedArtworksForm.editions,
				[artworkId]: {
					...associatedArtworksForm.editions[artworkId],
					legacyEditionNumber: (event?.target as HTMLInputElement).value,
				},
			});
		};
</script>

<div class="max-lg:overflow-x-auto">
	<div class="min-w-[700px]">
		<div class="mb-2 grid grid-cols-9 gap-4">
			<div class="col-span-5 flex justify-between">
				<InputLabel variant="label3" {dataCy} for="artists" required>
					Artwork associated with activity
				</InputLabel>

				<InfoTooltip {dataCy} placement="top-end" content="Tooltip tbc" />
			</div>
			<div class="col-span-2 flex justify-between">
				<InputLabel variant="label3" {dataCy} for="artists">
					Edition no.
				</InputLabel>

				<InfoTooltip {dataCy} placement="top-end" content="Tooltip tbc" />
			</div>
			<div class="col-span-2 flex justify-between">
				<InputLabel variant="label3" {dataCy} for="artists">
					Raw edition no.
				</InputLabel>

				<InfoTooltip {dataCy} placement="top-end" content="Tooltip tbc" />
			</div>
		</div>

		<div class="mb-4 flex flex-col gap-4">
			{#each options as option}
				{@const artworkId = option.line4 || ''}
				{@const editionTypeKey =
					associatedArtworksForm.editions[artworkId]?.editionTypeKey}
				{@const editionNumber =
					associatedArtworksForm.editions[artworkId]?.editionNumber}
				{@const legacyEditionNumber =
					associatedArtworksForm.editions[artworkId]?.legacyEditionNumber}
				{@const originalArtworkId = artworks?.find(
					(artwork) => artwork?.artwork?.id === artworkId
				)?.artwork?.activities_transferred_to?.[0]?.from_artwork?.id}

				<div class="grid grid-cols-9 gap-4">
					<div class="col-span-5">
						<ArtworkAutocomplete
							{dataCy}
							onChange={handleAutocompleteChange}
							onRemove={handleOpenArtworkRemovalConfirmationDialog}
							selectedOption={option}
						/>
					</div>

					<div class="col-span-2">
						<InputWithSelect
							size="sm"
							dataCy={`${dataCy}-edition-number`}
							name="editionNumber"
							selectValue={editionTypeKey}
							inputValue={editionNumber}
							options={editionTypesOptions}
							placeholder=""
							tooltip="Copy TBC"
							onSelectChange={handleEditionTypeChange(artworkId)}
							onkeyup={handleEditionNumberChange(artworkId)}
							selectPlaceholder="Select type"
							classes={{ options: 'z-30' }}
						/>
					</div>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-raw-edition-number`}
							name="legacyEditionNumber"
							placeholder=""
							value={legacyEditionNumber}
							onkeyup={handleLegacyEditionNumberChange(artworkId)}
							size="sm"
						/>
					</div>

					{#if originalArtworkId}
						<div class="col-span-9 mt-[-1rem]">
							<Txt
								target="_blank"
								rel="noopeneer noreferrer"
								href={`${Routes.ArtworkDetails}/${originalArtworkId}`}
								variant="body3"
								class="text-gray-500 underline"
								component="a"
							>
								View the artwork this activity was previously linked to
							</Txt>
						</div>
					{/if}
				</div>
			{/each}

			{#if showNew || options.length === 0}
				<div class="grid grid-cols-9">
					<div class="col-span-5">
						<ArtworkAutocomplete
							{dataCy}
							onChange={handleAutocompleteChange}
							onRemove={handleRemove}
							selectedOption={null}
						/>
					</div>
				</div>
			{/if}
		</div>

		{#if !showNew}
			<div class="flex justify-end">
				<Button
					onclick={handleClickAdd}
					dataCy={`${dataCy}-add`}
					variant="secondary"
					size="sm"
				>
					Add
					{#snippet trailing()}
						<PlusIcon class="h-3 w-3" />
					{/snippet}
				</Button>
			</div>
		{/if}
	</div>
</div>

<ArtworkRemovalConfirmationDialog
	dialogStores={artworkRemovalConfirmationDialogStores}
	{artworkToRemove}
	onSubmit={handleRemove}
	onClose={handleCloseArtworkRemovalConfirmationDialog}
/>
