<script lang="ts">
	import { type createDialog } from '@melt-ui/svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';

	interface Props {
		artworkToRemove: OptionType | null;
		dialogStores: ReturnType<typeof createDialog>;
		onClose: () => void;
		onSubmit: (artwork: OptionType) => void;
	}

	let { dialogStores, onClose, onSubmit, artworkToRemove }: Props = $props();

	const handleClickNo = () => {
		onClose();
	};

	const handleClickYes = () => {
		if (artworkToRemove) {
			onSubmit(artworkToRemove);
		}

		onClose();
	};

	const dataCyPrefix = 'artwork-removal-confirmation-dialog';
</script>

<Dialog
	dataCy="artwork-removal-confirmation"
	{dialogStores}
	title={`Are you sure you want to remove this artwork?`}
	shouldClose={false}
	showCloseIcon={false}
>
	<Txt>
		Upon saving of the page, the current activity will be moved over into the
		newly selected artwork
	</Txt>

	<div
		class="mt-6 flex flex-col-reverse gap-2 xs:grid xs:grid-cols-2 xs:flex-row"
	>
		<Button
			dataCy={`${dataCyPrefix}-no`}
			size="lg"
			variant="secondary"
			onclick={handleClickNo}>no</Button
		>
		<Button onclick={handleClickYes} dataCy={`${dataCyPrefix}-yes`} size="lg"
			>yes</Button
		>
	</div>
</Dialog>
