<script lang="ts" module>
	export const formatArtwork = (
		artwork: GetArtworksQuery['artwork'][number]
	) => {
		const artist = artwork?.artists?.[0]?.artist_id;

		const subTitle = getArtworkSubtitle({
			name: artist?.person?.entity?.name,
			nationality:
				artist?.person?.nationalities?.[0]?.country?.country_nationality,
			yearBirth: artist?.person?.year_birth,
			yearDeath: artist?.person?.year_death,
		});

		const url = `${Routes.ArtworkDetails}/${artwork?.id}`;

		return {
			line1: `${artwork.title}`,
			line2: subTitle,
			line3: url,
			line4: `${artwork.id}`,
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { getArtworkSubtitle } from '../../../utils/getArtworkSubtitle/getArtworkSubtitle';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetArtworksQuery,
		GetArtworksQueryVariables,
	} from '$lib/queries/__generated__/getArtworks.generated';
	import { GetArtworksDocument } from '$lib/queries/__generated__/getArtworks.generated';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { dataCy, selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetArtworksQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: isUuid
				? { id: { _eq: value } }
				: {
						title: {
							_icontains: value,
						},
					},
		};
	};

	const getOptions = (data: GetArtworksQuery | undefined) => {
		return [...(data?.artwork || []).map(formatArtwork)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={selectedOption.line2 ? `(${selectedOption.line2})` : ''}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="artists"
				dataCy={`${dataCy}-artists`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					artwork: [],
					artwork_aggregated: [{ count: { id: 0 } }],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetArtworksDocument}
				{value}
				{selectedOption}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults
						class="text-left"
						dataCy={`${dataCy}-artist-series-autocomplete`}
					>
						No artworks found.
					</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
