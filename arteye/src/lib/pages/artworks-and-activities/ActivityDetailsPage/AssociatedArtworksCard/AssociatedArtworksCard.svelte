<script module lang="ts">
	export interface AssociatedArtwork {
		id: string;
		title: string;
		subTitle: string;
		activityArtworkRelationId: string;
		url?: string;
		isNew?: boolean;
		isDeleted?: boolean;
	}

	export interface AssociatedArtworkEdition {
		editionTypeKey: string;
		editionTypeName: string;
		editionNumber: string;
		legacyEditionNumber: string;
		artworkId: string;

		isNew?: boolean;
		isDeleted?: boolean;
	}

	export interface AssociatedArtworksForm {
		artworks: Record<string, AssociatedArtwork>;
		editions: Record<string, AssociatedArtworkEdition>;
	}
</script>

<script lang="ts">
	import { AssociatedArtworks } from './AssociatedArtworks';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	interface Props {
		artworks: NonNullable<ActivityDetailsPageData['activity']>['artworks'];
		associatedArtworksForm: AssociatedArtworksForm;
		onChange: (associatedArtworksForm: AssociatedArtworksForm) => void;
	}

	let { associatedArtworksForm, onChange, artworks }: Props = $props();

	const handleArtworksChange = (
		artworks: AssociatedArtworksForm['artworks']
	) => {
		onChange({
			...associatedArtworksForm,
			artworks,
		});
	};

	const handleEditionsChange = (
		editions: AssociatedArtworksForm['editions']
	) => {
		onChange({
			...associatedArtworksForm,
			editions,
		});
	};
</script>

<div class="rounded-md border bg-white p-4">
	<AssociatedArtworks
		onArtworksChange={handleArtworksChange}
		onEditionsChange={handleEditionsChange}
		{artworks}
		{associatedArtworksForm}
	/>
</div>
