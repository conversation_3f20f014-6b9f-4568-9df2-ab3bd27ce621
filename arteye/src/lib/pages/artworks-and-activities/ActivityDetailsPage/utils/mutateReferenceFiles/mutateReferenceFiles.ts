import type { ReferenceFiles } from '../../ReferenceFilesCard/ReferenceFilesCard.svelte';
import type { Update_Artwork_Activity_Artwork_Info_Input } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkActivityArtworkInfoItemDocument } from '$lib/mutations/__generated__/updateArtworkActivityArtworkInfoItem.generated';
import { uploadFile } from '$lib/utils/uploadFile/uploadFile';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const mutateReferenceFiles = async ({
	activity,
	headers,
	referenceFiles,
}: {
	activity: ActivityDetailsPageData['activity'];
	headers: {
		Authorization: string;
	};
	referenceFiles: ReferenceFiles;
}) => {
	const getFiles = async (): Promise<
		Update_Artwork_Activity_Artwork_Info_Input['reference_files']
	> => {
		const promises = referenceFiles
			.map(async (referenceFile) => {
				const { file, relationId, uploadUrlSource } = referenceFile;

				if (relationId) {
					return {
						id: relationId,
						status: {
							key: referenceFile.isDeleted
								? Status_Enum.Archived
								: Status_Enum.Published,
						},
					};
				}

				if (file) {
					const res = await uploadFile(file, headers);

					return {
						directus_files_id: {
							id: res.id,
						},
					};
				}

				if (uploadUrlSource) {
					const res = await fetch('/api/upload-file-from-url', {
						method: 'POST',
						headers,
						body: JSON.stringify({ url: uploadUrlSource }),
					});

					const file = await res.json();

					return {
						directus_files_id: {
							id: file.id,
						},
					};
				}

				return null;
			})
			.filter(Boolean);

		return await Promise.all(promises);
	};

	return gqlClient.request(
		UpdateArtworkActivityArtworkInfoItemDocument,
		{
			id: activity?.activity_artwork_info?.id || '',
			data: {
				reference_files: (await getFiles())?.filter(Boolean),
			},
		},
		headers
	);
};
