import type { AuctionLotDetailsForm } from '../../AuctionLotDetailsCard';
import type {
	Create_Auction_Lot_Attribute_Input,
	Update_Auction_Lot_Attribute_Input,
} from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { CreateAuctionLotAttributeItemsDocument } from '$lib/mutations/__generated__/createAuctionLotAttributeItems.generated';
import { UpdateAuctionLotAttributeBatchDocument } from '$lib/mutations/__generated__/updateAuctionLotAttributeBatch.generated';
import { AuctionLotType } from '$lib/types/types';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

const KEY_TO_FORM_FIELD_MAP: Record<
	AuctionLotType,
	keyof Pick<
		AuctionLotDetailsForm,
		| 'gauranteedLot'
		| 'irrevocableBid'
		| 'ownedByAuctionHouse'
		| 'catalogueHighlight'
		| 'noReserve'
	>
> = {
	[AuctionLotType.Guaranteed]: 'gauranteedLot',
	[AuctionLotType.IrrevocableBid]: 'irrevocableBid',
	[AuctionLotType.OwnershipInterest]: 'ownedByAuctionHouse',
	[AuctionLotType.CatalogueHighlight]: 'catalogueHighlight',
	[AuctionLotType.NoReserve]: 'noReserve',
};

const ATTRIBUTE_KEYS = [
	AuctionLotType.OwnershipInterest,
	AuctionLotType.CatalogueHighlight,
	AuctionLotType.Guaranteed,
	AuctionLotType.IrrevocableBid,
	AuctionLotType.NoReserve,
];

const handleCreateItems = ({
	headers,
	auctionLotDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	const items = ATTRIBUTE_KEYS.filter((key) => {
		const formValue = auctionLotDetailsForm[KEY_TO_FORM_FIELD_MAP[key]];

		if (formValue && !auctionLotDetailsForm.attributeIds[key]) return true;

		return false;
	}).map((key) => {
		const formValue = auctionLotDetailsForm[KEY_TO_FORM_FIELD_MAP[key]];

		return {
			id: auctionLotDetailsForm.attributeIds[key] || undefined,
			key,
			formValue,
		};
	});

	if (items.length) {
		const data: Create_Auction_Lot_Attribute_Input[] = items.map((item) => {
			const key = item.key;
			const id = auctionLotDetailsForm.attributeIds[key] || undefined;

			const input: Create_Auction_Lot_Attribute_Input = {
				auction_lot: {
					id: activity?.artwork_listing?.[0]?.auction_lot?.id,
				},
				type: {
					key: item.key,
					name: '',
				},
			};

			if (item.key === AuctionLotType.Guaranteed) {
				const gauranteedAmount =
					parseFloat(auctionLotDetailsForm.gauranteedAmount) || 0;

				if (gauranteedAmount === 0) {
					input.amount = null;
				} else {
					input.amount = {
						id,
						amount: parseFloat(auctionLotDetailsForm.gauranteedAmount),
						conversion_timestamp: new Date().toISOString(),
						usd_amount: 0,
						currency: {
							code: 'USD',
							name: '',
						},
					};
				}
			}

			return input;
		});

		return gqlClient.request(
			CreateAuctionLotAttributeItemsDocument,
			{
				data,
			},
			headers
		);
	}
};

const handleUpdateItems = ({
	headers,
	auctionLotDetailsForm,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	const keys = ATTRIBUTE_KEYS.filter((key) => {
		return !!auctionLotDetailsForm.attributeIds[key];
	}).map((key) => {
		const formValue = auctionLotDetailsForm[KEY_TO_FORM_FIELD_MAP[key]];

		return {
			id: auctionLotDetailsForm.attributeIds[key],
			key,
			formValue,
		};
	});

	if (keys.length) {
		const data: Update_Auction_Lot_Attribute_Input[] = keys.map((item) => {
			const key = item.key;
			const id = auctionLotDetailsForm.attributeIds[key] || undefined;
			const formValue = item.formValue;

			const input: Update_Auction_Lot_Attribute_Input = {
				id,
				type: {
					key: key,
				},
				status: {
					key: formValue ? Status_Enum.Published : Status_Enum.Archived,
				},
			};

			if (key === AuctionLotType.Guaranteed) {
				const amount = parseFloat(auctionLotDetailsForm.gauranteedAmount) || 0;

				if (amount === 0) {
					input.amount = null;
				} else {
					input.amount = {
						id: auctionLotDetailsForm.amountIds.GUARANTEED || undefined,
						amount,
						conversion_timestamp: new Date().toISOString(),
						currency: {
							code: 'USD',
						},
					};
				}
			}

			return input;
		});

		return gqlClient.request(
			UpdateAuctionLotAttributeBatchDocument,
			{
				data,
			},
			headers
		);
	}
};

export const mutateAuctionLotAttributes = async ({
	headers,
	auctionLotDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	return Promise.all([
		handleCreateItems({ headers, auctionLotDetailsForm, activity }),
		handleUpdateItems({ headers, auctionLotDetailsForm, activity }),
	]);
};
