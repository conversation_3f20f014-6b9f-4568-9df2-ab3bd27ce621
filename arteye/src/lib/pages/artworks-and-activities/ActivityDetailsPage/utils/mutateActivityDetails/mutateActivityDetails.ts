import type { ActivityDetailsForm } from '../../ActivityDetailsCard';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import {
	UpdateArtworkActivityDocument,
	type UpdateArtworkActivityMutationVariables,
} from '$lib/mutations/__generated__/updateArtworkActivity.generated';

export const mutateActivityDetails = async ({
	headers,
	activityId,
	activityDetailsForm,
	notes,
}: {
	headers: {
		Authorization: string;
	};
	activityId: string;
	activityDetailsForm: ActivityDetailsForm;
	notes: string;
}) => {
	const data: UpdateArtworkActivityMutationVariables['data'] = {
		notes,
		source_page_url: activityDetailsForm.sourcePageUrl,
		activity_status: activityDetailsForm.activityStatuses.map((status) => {
			return {
				id: status.id,
				timestamp: status.timestamp,
				type: {
					key: status.typeKey,
				},
				status: {
					key: status.isDeleted ? Status_Enum.Archived : Status_Enum.Published,
				},
			};
		}),
	};

	return gqlClient.request(
		UpdateArtworkActivityDocument,
		{
			id: activityId,
			data,
		},
		headers
	);
};
