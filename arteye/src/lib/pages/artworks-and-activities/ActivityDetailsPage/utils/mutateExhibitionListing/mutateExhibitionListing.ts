import type { ExhibitionForm } from '../../ExhibitionCard';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateExhibitionArtworkListingMutationVariables } from '$lib/mutations/__generated__/updateExhibitionArtworkListing.generated';
import { UpdateExhibitionArtworkListingDocument } from '$lib/mutations/__generated__/updateExhibitionArtworkListing.generated';

export const mutateExhibitionListing = ({
	exhibitionListingId,
	exhibitionForm,
	headers,
}: {
	exhibitionListingId: string;
	exhibitionForm: ExhibitionForm;
	headers: {
		Authorization: string;
	};
}) => {
	const data: UpdateExhibitionArtworkListingMutationVariables['data'] = {
		exhibition: {
			id: exhibitionForm.exhibition.id,
		},
	};

	return gqlClient.request(
		UpdateExhibitionArtworkListingDocument,
		{
			id: exhibitionListingId,
			data,
		},
		headers
	);
};
