import type {
	Bid,
	BiddingChartDetailsForm,
} from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/BiddingChart.svelte';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const HAMMER_BID_ID = 'hammer';

export const formatBiddingChartDetailsForm = (
	activity: ActivityDetailsPageData['activity']
): BiddingChartDetailsForm => {
	return {
		artworkActivityId: activity?.id || '',
		artworkListingId: activity?.artwork_listing?.[0]?.id || '',
		auctionId: activity?.artwork_listing?.[0]?.auction_lot?.auction?.id || '',
		auctionLotId: activity?.artwork_listing?.[0]?.auction_lot?.id || '',
		activity_status: activity?.activity_status?.[0]?.type?.key || null,
		video_file_name:
			activity?.artwork_listing?.[0]?.auction_lot?.video_file_name || '',
		video_hammer_time_seconds:
			activity?.artwork_listing?.[0]?.auction_lot?.video_hammer_time_seconds ||
			'',
		auctioneer: activity?.artwork_listing?.[0]?.auction_lot?.auctioneer || null,
		bids: [
			...((activity?.artwork_listing?.[0]?.auction_lot?.bids as Bid[]) || []),
			...(activity?.artwork_listing?.[0]?.auction_lot?.hammer_timestamp &&
			(activity?.activity_status?.[0]?.type?.key === 'SOLD' ||
				activity?.activity_status?.[0]?.type?.key === 'BOUGHT_IN')
				? [
						{
							isHammerBid: true,
							id: HAMMER_BID_ID,
							timestamp:
								activity?.artwork_listing?.[0]?.auction_lot?.hammer_timestamp,
						},
					]
				: []),
		],
		winning_bid:
			activity?.artwork_listing?.[0]?.auction_lot?.winning_bid || null,
		associations:
			activity?.associations?.filter(
				(association) => association?.status?.key === 'published'
			) || [],
		hammer_timestamp:
			activity?.artwork_listing?.[0]?.auction_lot?.hammer_timestamp,
	} as BiddingChartDetailsForm;
};
