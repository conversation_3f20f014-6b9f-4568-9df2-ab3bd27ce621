import type { OtherDetailsForm } from '../../OtherInformationCard/OtherInformationCard.svelte';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkActivityArtworkInfoItemDocument } from '$lib/mutations/__generated__/updateArtworkActivityArtworkInfoItem.generated';
import { UpdateArtworkListingDocument } from '$lib/mutations/__generated__/updateArtworkListing.generated';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const mutateOtherDetails = async ({
	otherDetailsForm,
	activity,
	headers,
}: {
	otherDetailsForm: OtherDetailsForm;
	activity: ActivityDetailsPageData['activity'];
	headers: {
		Authorization: string;
	};
}) => {
	const artworkListing = activity?.artwork_listing?.[0];
	const artworkListingId = artworkListing?.id || '';

	const promises = [];

	const artworkListingPromise = gqlClient.request(
		UpdateArtworkListingDocument,
		{
			id: artworkListingId,
			data: {
				shipping: otherDetailsForm.shipping,
			},
		},
		headers
	);

	promises.push(artworkListingPromise);

	const artworkActivityArtworkInfoItemPromise = gqlClient.request(
		UpdateArtworkActivityArtworkInfoItemDocument,
		{
			id: activity?.activity_artwork_info?.id || '',
			data: {
				condition: otherDetailsForm.condition,
				provenance: otherDetailsForm.provenance,
				exhibition: otherDetailsForm.exhibition,
				literature: otherDetailsForm.literature,
				raw_artwork_description: otherDetailsForm.rawArtworkDescription,
				series_size: parseInt(otherDetailsForm.seriesSize) || 0,
				ingestion_notes: otherDetailsForm.ingestionNotes,
				is_bundle: otherDetailsForm.isBundle,
				is_full_set: otherDetailsForm.isFullSet,
				number_of_artworks: parseInt(otherDetailsForm.numberOfArtworks) || 0,
			},
		},
		headers
	);

	promises.push(artworkActivityArtworkInfoItemPromise);

	return Promise.all(promises);
};
