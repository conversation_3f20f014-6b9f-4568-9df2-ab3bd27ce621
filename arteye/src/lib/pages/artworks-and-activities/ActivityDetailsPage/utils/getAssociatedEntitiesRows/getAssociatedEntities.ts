import type { AssociatedEntityRow } from '../../AssociatedEntitiesCard';
import { Status_Enum } from '$gql/types-custom';
import { getEntityType } from '$lib/utils/getEntityType/getEntityType';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const getAssociatedEntities = (
	associations: NonNullable<ActivityDetailsPageData['activity']>['associations']
): AssociatedEntityRow[] => {
	return (associations || []).map((association, index) => {
		const isArtistType = !!association?.entity?.artist?.id;
		const isPersonType = !!association?.entity?.person?.id;

		const entityType = (() => {
			if (isArtistType) {
				return 'artist';
			} else if (isPersonType) {
				return 'person';
			} else {
				return 'organisation';
			}
		})();

		const displayedType = getEntityType(
			association?.entity as Parameters<typeof getEntityType>[0]
		);

		const yearBirth =
			isArtistType || isPersonType
				? association?.entity?.person?.year_birth || 0
				: 0;
		const yearDeath =
			isArtistType || isPersonType
				? association?.entity?.person?.year_death || 0
				: 0;
		const nationality =
			isArtistType || isPersonType
				? association?.entity?.person?.nationalities?.[0]?.country
						?.country_nationality || ''
				: '';
		const location =
			entityType === 'organisation'
				? association?.entity?.addresses?.[0]?.city?.name || ''
				: '';

		const item: AssociatedEntityRow = {
			rowId: index,
			readOnly: association?.read_only,
			association: {
				id: association?.id || '',
				type: {
					name: association?.type?.name || '',
					key: association?.type?.key || '',
				},
			},
			entity: {
				id: association?.entity?.id || '',
				name: association?.entity?.name || '',
				entityType,
				displayedType,
				yearBirth,
				yearDeath,
				nationality,
				location,
				artistId: association?.entity?.artist?.id || '',
				personId: association?.entity?.person?.id || '',
				orgId: association?.entity?.organisation?.id || '',
			},
			isDeleted: association?.status?.key === Status_Enum.Archived,
			updateHistory: association,
		};

		return item;
	});
};
