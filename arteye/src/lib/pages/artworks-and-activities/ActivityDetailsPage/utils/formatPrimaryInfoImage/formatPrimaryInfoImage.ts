import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatPrimaryInfoImage = ({
	activity,
	accessToken,
}: {
	activity: ActivityDetailsPageData['activity'];
	accessToken: string | undefined;
}) => {
	return {
		...activity?.activity_artwork_info?.primary_image,
		url: getImageUrl(
			activity?.activity_artwork_info?.primary_image?.id,
			accessToken
		),
		file: null as File | null,
		isDeleted: false,
	};
};
