import type { GalleryForm } from '../../GalleryCard';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateGalleryArtworkListingMutationVariables } from '$lib/mutations/__generated__/updateGalleryArtworkListing.generated';
import { UpdateGalleryArtworkListingDocument } from '$lib/mutations/__generated__/updateGalleryArtworkListing.generated';

export const mutateGalleryListing = ({
	galleryListingId,
	galleryForm,
	headers,
}: {
	galleryListingId: string;
	galleryForm: GalleryForm;
	headers: {
		Authorization: string;
	};
}) => {
	if (!galleryForm.gallery.id) {
		return;
	}

	const data: UpdateGalleryArtworkListingMutationVariables['data'] = {
		gallery: {
			id: galleryForm.gallery.id,
		},
	};

	return gqlClient.request(
		UpdateGalleryArtworkListingDocument,
		{
			id: galleryListingId,
			data,
		},
		headers
	);
};
