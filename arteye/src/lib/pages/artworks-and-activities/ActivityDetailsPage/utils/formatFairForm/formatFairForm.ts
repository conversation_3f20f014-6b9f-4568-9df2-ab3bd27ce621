import type { FairForm } from '../../FairCard';
import { Config } from '$lib/constants/config';
import { Routes } from '$lib/constants/routes';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatFairForm = (
	activity: ActivityDetailsPageData['activity']
): FairForm => {
	const fair =
		activity?.artwork_listing?.[0]?.fair_listing?.fair_exhibitor?.fair;
	const exhibitor =
		activity?.artwork_listing?.[0]?.fair_listing?.fair_exhibitor;

	const getFairSubTitle = () => {
		if (fair?.venue_city?.name && fair?.venue_country?.name) {
			return `${fair.venue_city.name}, ${fair.venue_country.name}`;
		}

		if (fair?.venue_city?.name) {
			return `${fair.venue_city.name}`;
		}

		if (fair?.venue_country?.name) {
			return `${fair.venue_country.name}`;
		}

		return '';
	};

	const getExhibitorSubTitle = () => {
		if (exhibitor?.entity?.organisation?.location?.name) {
			return `${exhibitor?.entity?.organisation?.location?.name}`;
		}

		return '';
	};

	return {
		fair: {
			id: fair?.id || '',
			title: fair?.title || '',
			subTitle: getFairSubTitle(),
			url: `${Routes.Fairs}/${fair?.id}`,
		},
		exhibitor: {
			id: exhibitor?.id || '',
			title: exhibitor?.entity?.name || '',
			subTitle: getExhibitorSubTitle(),
			url: `${Config.DirectusUrl}/admin/content/fair_exhibitor/${exhibitor?.id}`,
		},
	};
};
