import { Status_Enum } from '$gql/types-custom';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatAdditionalInfoImages = ({
	activity,
	accessToken,
}: {
	activity: ActivityDetailsPageData['activity'];
	accessToken: string | undefined;
}) => {
	return (activity?.activity_artwork_info?.additional_images || []).map(
		(image) => ({
			...image?.image,
			url: getImageUrl(image?.image?.id, accessToken),
			file: null as File | null,
			isDeleted: image?.status?.key === Status_Enum.Archived,
			relationId: image?.id,
		})
	);
};
