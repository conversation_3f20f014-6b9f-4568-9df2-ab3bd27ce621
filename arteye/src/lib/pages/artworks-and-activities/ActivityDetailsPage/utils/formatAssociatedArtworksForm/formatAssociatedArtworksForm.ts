import type {
	AssociatedArtwork,
	AssociatedArtworkEdition,
	AssociatedArtworksForm,
} from '../../AssociatedArtworksCard/AssociatedArtworksCard.svelte';
import { getArtworkSubtitle } from '../getArtworkSubtitle/getArtworkSubtitle';
import { Status_Enum } from '$gql/types-custom';
import { Routes } from '$lib/constants/routes';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatAssociatedArtworksForm = ({
	artworks,
	createActivityMeta,
}: {
	artworks: NonNullable<ActivityDetailsPageData['activity']>['artworks'];
	createActivityMeta: ActivityDetailsPageData['createActivityMeta'];
}): AssociatedArtworksForm => {
	let artworksList: AssociatedArtwork[] = [];

	if (createActivityMeta?.artwork) {
		const artwork = createActivityMeta?.artwork;

		const url = `${Routes.ArtworkDetails}/${artwork?.id}`;

		const artist = artwork?.artists?.[0]?.artist_id;

		const subTitle = getArtworkSubtitle({
			name: artist?.person?.entity?.name,
			nationality:
				artist?.person?.nationalities?.[0]?.country?.country_nationality,
			yearBirth: artist?.person?.year_birth,
			yearDeath: artist?.person?.year_death,
		});

		artworksList = [
			{
				id: artwork?.id || '',
				activityArtworkRelationId: artwork?.id || '',
				title: artwork?.title || '-',
				subTitle,
				url,
				isDeleted: artwork?.status?.key === Status_Enum.Archived,
			},
		];
	} else {
		artworksList = (artworks || []).map<AssociatedArtwork>((artwork) => {
			const artist = artwork?.artwork?.artists?.[0]?.artist_id;

			const subTitle = getArtworkSubtitle({
				name: artist?.person?.entity?.name,
				nationality:
					artist?.person?.nationalities?.[0]?.country?.country_nationality,
				yearBirth: artist?.person?.year_birth,
				yearDeath: artist?.person?.year_death,
			});

			const url = `${Routes.ArtworkDetails}/${artwork?.artwork?.id}`;

			return {
				id: artwork?.artwork?.id || '',
				activityArtworkRelationId: artwork?.id || '',
				title: artwork?.artwork?.title || '-',
				subTitle,
				url,
				isDeleted: artwork?.status?.key === Status_Enum.Archived,
			};
		});
	}

	const artworksMap = artworksList.reduce<AssociatedArtworksForm['artworks']>(
		(acc, artwork) => {
			acc[artwork.id] = artwork;
			return acc;
		},
		{}
	);

	const editionsList = (artworks || []).map<AssociatedArtworkEdition>(
		(artwork) => {
			return {
				artworkId: artwork?.artwork?.id || '',
				editionTypeKey: artwork?.edition_number_type?.key || '',
				editionTypeName: artwork?.edition_number_type?.name || '',
				editionNumber: artwork?.edition_number || '',
				legacyEditionNumber: artwork?.edition_number_legacy || '',
			};
		}
	);

	const editionsMap = editionsList.reduce<AssociatedArtworksForm['editions']>(
		(acc, edition) => {
			acc[edition.artworkId] = edition;
			return acc;
		},
		{}
	);

	return {
		artworks: artworksMap,
		editions: editionsMap,
	};
};
