import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkActivityDocument } from '$lib/mutations/__generated__/updateArtworkActivity.generated';
export const archiveArtworkActivity = async ({
	id,
	headers,
}: {
	id: string;
	headers: {
		Authorization: string;
	};
}) => {
	return gqlClient.request(
		UpdateArtworkActivityDocument,
		{
			id,
			data: {
				status: {
					key: Status_Enum.Archived,
					name: 'Archived',
				},
			},
		},
		headers
	);
};
