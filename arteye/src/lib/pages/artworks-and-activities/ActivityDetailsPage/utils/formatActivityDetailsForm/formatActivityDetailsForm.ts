import type { ActivityDetailsForm } from '../../ActivityDetailsCard';
import type { ActivityStatusRowItem } from '../../ActivityDetailsCard/ActivityStatusTable';
import { Status_Enum } from '$gql/types-custom';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatActivityDetailsForm = (
	activity: ActivityDetailsPageData['activity']
): ActivityDetailsForm => {
	return {
		activityType: {
			key: activity?.type?.key || '',
			name: activity?.type?.name || '',
			predefined: !!activity?.artwork_listing?.[0]?.listing_type?.key,
		},
		sourcePageUrl: activity?.source_page_url || '',
		activityStatuses: (
			activity?.activity_status || []
		).map<ActivityStatusRowItem>((activityStatus) => ({
			id: activityStatus?.id || '',
			type: activityStatus?.type?.name || '',
			typeKey: activityStatus?.type?.key || '',
			timestamp: activityStatus?.timestamp || '',
			isDeleted: activityStatus?.status?.key === Status_Enum.Archived,
			updateHistory: activityStatus,
		})),
	};
};
