import type { PrivateSaleDetailsForm } from '../../PrivateSaleDetailsCard';
import type { PrivateSaleDetailsFormErrors } from '../../PrivateSaleDetailsCard/PrivateSaleDetailsCard.svelte';

const CURRENCY_ERROR = 'A currency is required';

export const formatPrivateSaleDetailsFormErrors = (
	privateSaleDetailsForm: PrivateSaleDetailsForm
) => {
	const errors: PrivateSaleDetailsFormErrors = {
		knownPriceLocalCurrencyCode: '',
		lowEstimateLocalCurrencyCode: '',
		highEstimateLocalCurrencyCode: '',
		saleAmountLocalCurrencyCode: '',
	};

	if (
		!privateSaleDetailsForm.knownPriceLocalCurrency.code &&
		privateSaleDetailsForm.knownPriceLocal
	) {
		errors.knownPriceLocalCurrencyCode = CURRENCY_ERROR;
	}

	if (
		!privateSaleDetailsForm.lowEstimateLocalCurrency.code &&
		privateSaleDetailsForm.lowEstimateLocal
	) {
		errors.lowEstimateLocalCurrencyCode = CURRENCY_ERROR;
	}

	if (
		!privateSaleDetailsForm.highEstimateLocalCurrency.code &&
		privateSaleDetailsForm.highEstimateLocal
	) {
		errors.highEstimateLocalCurrencyCode = CURRENCY_ERROR;
	}

	if (
		!privateSaleDetailsForm.saleAmountLocalCurrency.code &&
		privateSaleDetailsForm.saleAmountLocal
	) {
		errors.saleAmountLocalCurrencyCode = CURRENCY_ERROR;
	}

	return errors;
};
