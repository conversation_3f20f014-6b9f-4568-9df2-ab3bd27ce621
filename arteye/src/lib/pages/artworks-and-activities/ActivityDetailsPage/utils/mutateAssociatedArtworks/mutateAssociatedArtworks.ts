import type { AssociatedArtworksForm } from '../../AssociatedArtworksCard';
import type { Update_Artwork_Activity_Artwork_Input } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateArtworkActivityMutationVariables } from '$lib/mutations/__generated__/updateArtworkActivity.generated';
import { UpdateArtworkActivityDocument } from '$lib/mutations/__generated__/updateArtworkActivity.generated';

export const mutateAssociatedArtworks = async ({
	activityId,
	associatedArtworksForm,
	headers,
}: {
	activityId: string;
	associatedArtworksForm: AssociatedArtworksForm;
	headers: {
		Authorization: string;
	};
}) => {
	const artworks = Object.values(associatedArtworksForm.artworks);

	const artworkActivityData: UpdateArtworkActivityMutationVariables['data'] = {
		artworks: artworks.map((artwork) => {
			const edition = associatedArtworksForm.editions[artwork.id];

			const item: Update_Artwork_Activity_Artwork_Input = {
				id: artwork.activityArtworkRelationId || undefined,
				edition_number: edition?.editionNumber || '',
				edition_number_legacy: edition?.legacyEditionNumber || '',
				artwork: {
					id: artwork.id,
				},
				status: {
					key: artwork.isDeleted ? Status_Enum.Archived : Status_Enum.Published,
				},
				...(edition?.editionTypeKey
					? {
							edition_number_type: {
								key: edition.editionTypeKey,
							},
						}
					: {}),
			};

			return item;
		}),
	};

	return gqlClient.request(
		UpdateArtworkActivityDocument,
		{
			id: activityId,
			data: artworkActivityData,
		},
		headers
	);
};
