import type { PrivateSaleDetailsForm } from '../../PrivateSaleDetailsCard';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateArtworkListingMutationVariables } from '$lib/mutations/__generated__/updateArtworkListing.generated';
import { UpdateArtworkListingDocument } from '$lib/mutations/__generated__/updateArtworkListing.generated';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const mutatePrivateSaleDetails = async ({
	headers,
	privateSaleDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	privateSaleDetailsForm: PrivateSaleDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	const artworkListing = activity?.artwork_listing?.[0];

	const knownPriceLocal =
		parseFloat(privateSaleDetailsForm.knownPriceLocal) || 0;
	const highEstimateLocal =
		parseFloat(privateSaleDetailsForm.highEstimateLocal) || 0;
	const lowEstimateLocal =
		parseFloat(privateSaleDetailsForm.lowEstimateLocal) || 0;
	const saleAmountLocal =
		parseFloat(privateSaleDetailsForm.saleAmountLocal) || 0;

	const data: UpdateArtworkListingMutationVariables['data'] = {
		known_price: {
			id: artworkListing?.known_price?.id,
			usd_amount: null,
			amount: knownPriceLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: privateSaleDetailsForm.knownPriceLocalCurrency.code,
			},
		},
		price_high_estimate: {
			id: artworkListing?.price_high_estimate?.id,
			usd_amount: null,
			amount: highEstimateLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: privateSaleDetailsForm.highEstimateLocalCurrency.code,
			},
		},
		price_low_estimate: {
			id: artworkListing?.price_low_estimate?.id,
			usd_amount: null,
			amount: lowEstimateLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: privateSaleDetailsForm.lowEstimateLocalCurrency.code,
			},
		},
		sale_amount: {
			id: artworkListing?.sale_amount?.id,
			usd_amount: null,
			amount: saleAmountLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: privateSaleDetailsForm.saleAmountLocalCurrency.code,
			},
		},
	};

	if (knownPriceLocal === 0) {
		data.known_price = null;
	}

	if (highEstimateLocal === 0) {
		data.price_high_estimate = null;
	}

	if (lowEstimateLocal === 0) {
		data.price_low_estimate = null;
	}

	if (saleAmountLocal === 0) {
		data.sale_amount = null;
	}

	return gqlClient.request(
		UpdateArtworkListingDocument,
		{
			id: artworkListing?.id || '',
			data,
		},
		headers
	);
};
