import type { AssociatedArtworksForm } from '../../AssociatedArtworksCard';
import type { AuctionForm } from '../../AuctionCard';
import type { ExhibitionForm } from '../../ExhibitionCard';
import type { FairForm } from '../../FairCard';
import type { GalleryForm } from '../../GalleryCard';
import { gqlClient } from '$lib/gqlClient';
import type { CreateArtworkActivityItemMutationVariables } from '$lib/queries/__generated__/createArtworkActivity.generated';
import { CreateArtworkActivityItemDocument } from '$lib/queries/__generated__/createArtworkActivity.generated';
import { ArtworkListingTypeKey } from '$lib/types/types';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';
import type { LayoutData } from '$routes/types';

const getArtworkListingType = ({
	listingTypeKey,
	artworkListingTypes,
}: {
	listingTypeKey: string;
	artworkListingTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkListingTypes'];
}) => {
	switch (listingTypeKey) {
		case ArtworkListingTypeKey.Fair:
		case ArtworkListingTypeKey.Gallery:
		case ArtworkListingTypeKey.Exhibition:
		case ArtworkListingTypeKey.Auction:
		case ArtworkListingTypeKey.Private:
			return artworkListingTypes?.find((type) => type.key === listingTypeKey);
		default:
			return null;
	}
};

const getArtworkActivityType = ({
	activityTypeKey,
	artworkActivityTypes,
}: {
	activityTypeKey: string;
	artworkActivityTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkActivityTypes'];
}) => {
	return artworkActivityTypes?.find((type) => type.key === activityTypeKey);
};

const getArtworkListing = ({
	galleryForm,
	fairForm,
	exhibitionForm,
	auctionForm,
	listingTypeKey,
	artworkListingTypes,
}: {
	galleryForm: GalleryForm;
	fairForm: FairForm;
	exhibitionForm: ExhibitionForm;
	auctionForm: AuctionForm;
	listingTypeKey: string;
	artworkListingTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkListingTypes'];
}) => {
	if (!listingTypeKey) return undefined;

	const artworkListingType = getArtworkListingType({
		listingTypeKey,
		artworkListingTypes,
	});

	const artworkListingItem: NonNullable<
		CreateArtworkActivityItemMutationVariables['data']['artwork_listing']
	>[number] = {
		listing_type: artworkListingType,
	};

	if (listingTypeKey === ArtworkListingTypeKey.Gallery) {
		artworkListingItem.gallery_listing = {
			gallery: {
				id: galleryForm.gallery.id,
			},
		};
	}

	if (listingTypeKey === ArtworkListingTypeKey.Fair) {
		artworkListingItem.fair_listing = {
			fair_exhibitor: {
				id: fairForm.exhibitor.id,
				fair: {
					id: fairForm.fair.id,
					title: fairForm.fair.title,
				},
			},
		};
	}

	if (listingTypeKey === ArtworkListingTypeKey.Exhibition) {
		artworkListingItem.exhibition_listing = {
			exhibition: {
				id: exhibitionForm.exhibition.id,
				title: exhibitionForm.exhibition.title,
			},
		};
	}

	if (listingTypeKey === ArtworkListingTypeKey.Auction) {
		artworkListingItem.auction_lot = {
			auction: {
				id: auctionForm.auction.id,
			},
		};
	}

	return [artworkListingItem];
};

export const createActivity = async ({
	associatedArtworksForm,
	galleryForm,
	fairForm,
	exhibitionForm,
	auctionForm,
	activityTypeKey,
	listingTypeKey,
	artworkListingTypes,
	artworkActivityTypes,
	headers,
}: {
	associatedArtworksForm: AssociatedArtworksForm;
	galleryForm: GalleryForm;
	fairForm: FairForm;
	exhibitionForm: ExhibitionForm;
	auctionForm: AuctionForm;
	activityTypeKey: string;
	listingTypeKey: string;
	artworkListingTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkListingTypes'];
	artworkActivityTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkActivityTypes'];
	headers: {
		Authorization: string;
	};
}) => {
	const artworks = Object.values(associatedArtworksForm.artworks);

	const artworkActivityType = getArtworkActivityType({
		activityTypeKey,
		artworkActivityTypes,
	});

	const artworkListing = getArtworkListing({
		galleryForm,
		fairForm,
		exhibitionForm,
		auctionForm,
		listingTypeKey,
		artworkListingTypes,
	});

	const data: CreateArtworkActivityItemMutationVariables['data'] = {
		timestamp: new Date().toISOString(),
		artworks: artworks.map((artwork) => {
			const edition = associatedArtworksForm.editions[artwork.id];

			return {
				artwork: {
					id: artwork.id,
				},
				edition_number: edition?.editionNumber || undefined,
				edition_number_legacy: edition?.legacyEditionNumber || undefined,
				...(edition?.editionTypeKey
					? {
							edition_number_type: {
								key: edition.editionTypeKey,
							},
						}
					: {}),
			};
		}),
		type: artworkActivityType,
		artwork_listing: artworkListing,
	};

	const createActivityRes = await gqlClient.request(
		CreateArtworkActivityItemDocument,
		{
			data,
		},
		headers
	);

	return createActivityRes;
};
