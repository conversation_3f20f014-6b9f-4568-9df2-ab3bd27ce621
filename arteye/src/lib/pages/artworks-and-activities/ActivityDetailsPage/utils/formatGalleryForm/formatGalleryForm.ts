import type { GalleryForm } from '../../GalleryCard';
import { Routes } from '$lib/constants/routes';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatGalleryForm = (
	activity: ActivityDetailsPageData['activity']
): GalleryForm => {
	const gallery = activity?.artwork_listing?.[0]?.gallery_listing?.gallery;

	const title = gallery?.organisation?.location?.name
		? `${gallery?.organisation?.name} (${gallery?.organisation?.location?.name})`
		: gallery?.organisation?.name || '';

	return {
		gallery: {
			id: gallery?.id || '',
			title,
			subTitle: gallery?.organisation?.location?.name || '',
			url: `${Routes.Galleries}/${gallery?.id}`,
		},
	};
};
