import type { FairForm } from '../../FairCard';
import { gqlClient } from '$lib/gqlClient';
import { UpdateFairArtworkListingDocument } from '$lib/mutations/__generated__/updateFairArtworkListing.generated';
import type { UpdateFairArtworkListingMutationVariables } from '$lib/mutations/__generated__/updateFairArtworkListing.generated';

export const mutateFairListing = ({
	fairListingId,
	fairForm,
	headers,
}: {
	fairListingId: string;
	fairForm: FairForm;
	headers: {
		Authorization: string;
	};
}) => {
	if (!fairForm.exhibitor.id) {
		return;
	}

	const data: UpdateFairArtworkListingMutationVariables['data'] = {
		fair_exhibitor: {
			id: fairForm.exhibitor.id,
		},
	};

	return gqlClient.request(
		UpdateFairArtworkListingDocument,
		{
			id: fairListingId,
			data,
		},
		headers
	);
};
