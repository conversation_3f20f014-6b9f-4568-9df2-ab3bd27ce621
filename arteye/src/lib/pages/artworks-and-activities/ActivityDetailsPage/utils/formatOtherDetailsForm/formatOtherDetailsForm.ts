import type { OtherDetailsForm } from '../../OtherInformationCard/OtherInformationCard.svelte';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatOtherDetailsForm = (
	activity: ActivityDetailsPageData['activity']
): OtherDetailsForm => {
	return {
		condition: activity?.activity_artwork_info?.condition || '',
		provenance: activity?.activity_artwork_info?.provenance || '',
		exhibition: activity?.activity_artwork_info?.exhibition || '',
		literature: activity?.activity_artwork_info?.literature || '',
		shipping: activity?.artwork_listing?.[0]?.shipping || '',
		seriesSize: activity?.activity_artwork_info?.series_size
			? `${activity?.activity_artwork_info?.series_size}`
			: '',
		rawArtworkDescription:
			activity?.activity_artwork_info?.raw_artwork_description || '',
		ingestionNotes: activity?.activity_artwork_info?.ingestion_notes || '',
		isBundle: !!activity?.activity_artwork_info?.is_bundle,
		isFullSet: !!activity?.activity_artwork_info?.is_full_set,
		numberOfArtworks: String(
			activity?.activity_artwork_info?.number_of_artworks || '0'
		),
		updateHistory: activity?.activity_artwork_info,
	};
};
