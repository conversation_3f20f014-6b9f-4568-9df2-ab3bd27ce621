import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { ActivitySearchByIdDocument } from '$lib/custom-queries/__generated__/activitySearch.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';

import {
	GetArtworkActivityByIdBaseDocument,
	type GetArtworkActivityByIdQuery,
	GetArtworkActivityInfoDocument,
	GetArtworksActivityDocument,
	GetArtworkStatusDocument,
	GetAssociationsDocument,
	GetAuctionLotDocument,
} from '$lib/queries/__generated__/getArtworkActivityById.generated';

import { isOnDev } from '$lib/utils/isOnDev/isOnDev';

export const fetchActivityServerLoad = async ({
	data,
	activityId,
}: {
	data: { user: { access_token: string } };
	activityId: string;
}) => {
	const legacyIdReq = isOnDev()
		? { getLegacyId: { legacyId: '' } }
		: gqlClientCustom.request(
				GetLegacyIdDocument,
				{
					collection: 'activity',
					id: activityId,
				},
				getAuthorizationHeaders(data)
			);

	const [activity, legacyIdRes] = await Promise.all([
		fetchActivity(activityId, getAuthorizationHeaders(data)),
		legacyIdReq,
	]);

	return { activity, legacyId: legacyIdRes?.getLegacyId?.legacyId };
};

export const fetchActivity = async (
	activityId: string,
	authHeaders: { Authorization: string }
) => {
	const artworkActivitySearchReq = gqlClientCustom.request(
		ActivitySearchByIdDocument,
		{ input: { limit: 1, activityFilters: { activityIds: [activityId] } } },
		authHeaders
	);

	// const activityPiecesReq = gqlClient.request(
	// 	GetArtworkActivityByIdPiecesDocument,
	// 	{
	// 		id: activityId,
	// 		artworkFilter: { artwork_activity: { id: { _eq: activityId } } },
	// 		associationFilter: { artwork_activity: { id: { _eq: activityId } } },
	// 		infoFilter: { artwork_activity: { id: { _eq: activityId } } },
	// 		statusFilter: { artwork_activity: { id: { _eq: activityId } } },
	// 		auctionFilter: {
	// 			artwork_listing: { artwork_activity: { id: { _eq: activityId } } },
	// 		},
	// 	},
	// 	authHeaders
	// );

	// const [activityPiecesRes, artworkActivitySearchRes] = await Promise.all([
	// 	activityPiecesReq,
	// 	artworkActivitySearchReq,
	// ]);

	// const activitySearchResult =
	// 	artworkActivitySearchRes?.activitySearch?.activities[0]?.artwork_activity;

	// if (
	// 	!artworkActivitySearchRes?.activitySearch?.activities[0]?.artwork_activity
	// ) {
	// 	return null;
	// }

	// return {
	// 	...activitySearchResult,
	// 	data_source:
	// 		artworkActivitySearchRes?.activitySearch?.activities[0]?.pipeline_source
	// 			?.data_source,
	// 	associations: activityPiecesRes?.artwork_activity_association,
	// 	notes: activityPiecesRes?.artwork_activity_by_id?.notes,
	// 	source_page_url: activityPiecesRes?.artwork_activity_by_id?.source_page_url,
	// 	artworks: activityPiecesRes?.artwork_activity_artwork,
	// 	activity_artwork_info: activityPiecesRes?.artwork_activity_artwork_info[0],
	// 	activity_status: activityPiecesRes?.artwork_activity_status,
	// 	artwork_listing: [
	// 		{
	// 			...activitySearchResult?.artwork_listing?.[0],
	// 			auction_lot: activityPiecesRes?.auction_lot?.[0] || null,
	// 		},
	// 	],
	// } as GetArtworkActivityByIdQuery['artwork_activity_by_id'] & {
	// 	data_source: string | null | undefined;
	// };

	const activityReq = gqlClient.request(
		GetArtworkActivityByIdBaseDocument,
		{
			id: activityId,
		},
		authHeaders
	);

	const artworksReq = gqlClient.request(
		GetArtworksActivityDocument,
		{
			filter: { artwork_activity: { id: { _eq: activityId } } },
		},
		authHeaders
	);

	const associationsReq = gqlClient.request(
		GetAssociationsDocument,
		{
			filter: { artwork_activity: { id: { _eq: activityId } } },
		},
		authHeaders
	);

	const artworkActivityInfoReq = gqlClient.request(
		GetArtworkActivityInfoDocument,
		{
			filter: { artwork_activity: { id: { _eq: activityId } } },
		},
		authHeaders
	);

	const activityStatusReq = gqlClient.request(
		GetArtworkStatusDocument,
		{
			filter: { artwork_activity: { id: { _eq: activityId } } },
		},
		authHeaders
	);

	const auctionLotReq = gqlClient.request(
		GetAuctionLotDocument,
		{
			filter: {
				artwork_listing: { artwork_activity: { id: { _eq: activityId } } },
			},
		},
		authHeaders
	);

	const [
		activityStatusRes,
		artworkActivityInfoRes,
		artworkActivitySearchRes,
		activityRes,
		associationsRes,
		artworksRes,
		auctionLotRes,
	] = await Promise.all([
		activityStatusReq,
		artworkActivityInfoReq,
		artworkActivitySearchReq,
		activityReq,
		associationsReq,
		artworksReq,
		auctionLotReq,
	]);

	const activitySearchResult =
		artworkActivitySearchRes?.activitySearch?.activities[0]?.artwork_activity;

	if (
		!artworkActivitySearchRes?.activitySearch?.activities[0]?.artwork_activity
	) {
		return null;
	}

	return {
		...activitySearchResult,
		data_source:
			artworkActivitySearchRes?.activitySearch?.activities[0]?.pipeline_source
				?.data_source,
		associations: associationsRes?.artwork_activity_association,
		notes: activityRes?.artwork_activity_by_id?.notes,
		source_page_url: activityRes?.artwork_activity_by_id?.source_page_url,
		artworks: artworksRes?.artwork_activity_artwork,
		activity_artwork_info:
			artworkActivityInfoRes?.artwork_activity_artwork_info[0],
		activity_status: activityStatusRes?.artwork_activity_status,
		artwork_listing: [
			{
				...activitySearchResult?.artwork_listing?.[0],
				auction_lot: auctionLotRes?.auction_lot?.[0] || null,
			},
		],
	} as GetArtworkActivityByIdQuery['artwork_activity_by_id'] & {
		data_source: string | null | undefined;
	};
};
