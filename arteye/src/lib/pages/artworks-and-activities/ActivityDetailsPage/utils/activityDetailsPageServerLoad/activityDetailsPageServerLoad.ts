import { error } from '@sveltejs/kit';
import { ActivityDetailsParam } from '../../types';
import { fetchActivityServerLoad } from './utils';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import type { AuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import {
	PaginationDirection,
	Status_Enum,
	type ActivitySearchSortField,
} from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { ActivitySearchIdOnlyDocument } from '$lib/custom-queries/__generated__/activitySearch.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { newGetArtworkActivitiesFilter } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/utils/newGetArtworkActivitiesFilter/newGetArtworkActivitiesFilter';
import { GetArtworkDocument } from '$lib/queries/__generated__/getArtwork.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { ActivityDetailsPageServerLoadEvent } from '$routes/artworks-and-activities/activity/[id]/types';

const getCreateActivityMetaData = async ({
	searchParams,
	authHeaders,
}: {
	searchParams: URLSearchParams;
	authHeaders: AuthorizationHeaders;
}) => {
	const artworkId = getDecodedSearchParam({
		searchParams,
		key: ActivityDetailsParam.ArtworkId,
	});

	try {
		const res = await gqlClient.request(
			GetArtworkDocument,
			{
				filter: {
					_and: [
						{
							id: {
								_eq: artworkId,
							},
						},
						{ status: { key: { _neq: Status_Enum.Archived } } },
					],
				},
			},
			authHeaders
		);

		const artwork = res.artwork[0];

		return { artwork };
	} catch (error) {
		return { artwork: null };
	}
};

export const activityDetailsPageServerLoad = async ({
	parent,
	params,
	url,
	// fetch,
}: ActivityDetailsPageServerLoadEvent & { nocache?: string }) => {
	const data = await parent();
	const activityId = params.id;
	const searchParams = url.searchParams;
	const isNewActivity = activityId === PARAM_NEW;

	const { dropzoneUrlDialogForm } = await getDropzoneUrlDialogSuperform();

	if (isNewActivity) {
		const createActivityMeta = await getCreateActivityMetaData({
			searchParams,
			authHeaders: getAuthorizationHeaders(data),
		});

		return {
			...data,
			activity: null,
			legacyId: null,
			dropzoneUrlDialogForm,
			pipelineSource: null,
			isNewActivity,
			createActivityMeta,
		};
	}

	// DO NOT REMOVE: PREV/NEXT CACHING SOLUTION FOR V2
	// const cachedActivityRes = await fetch(
	// 	`/api/get-activity-cache?activityId=${activityId}`
	// );

	// let cachedActivityServerLoad = await cachedActivityRes.json();

	// if (
	// 	cachedActivityServerLoad.activity &&
	// 	[
	// 		cachedActivityServerLoad.index,
	// 		cachedActivityServerLoad.legacyId,
	// 	].includes(undefined)
	// ) {
	// 	await fetch(`/api/clear-activity-cache?activityId=${activityId}`, {
	// 		method: 'DELETE',
	// 	});

	// 	cachedActivityServerLoad = { activity: null };
	// }

	const prevAndNextPagesPromises = [
		PaginationDirection.Previous,
		PaginationDirection.Next,
	].map(async (paginationDirection) => {
		if (isNewActivity) {
			return null;
		}

		const paginationTokenParam = getDecodedSearchParam({
			searchParams: url.searchParams,
			key: SearchParam.PaginationToken,
		});

		if (!paginationTokenParam) {
			return null;
		}

		const { artworkFilters, activityFilters, commonFilters } =
			newGetArtworkActivitiesFilter(url.searchParams);

		const sort = getSortWithDirection<ActivitySearchSortField>(
			url.searchParams
		);

		const input = {
			activityFilters,
			artworkFilters,
			commonFilters,
			limit: (() => {
				return 1;

				// if (
				// 	(cachedActivityServerLoad.activity &&
				// 		cachedActivityServerLoad.index !== 0) ||
				// 	cachedActivityServerLoad.pending ||
				// 	cachedActivityServerLoad.invalidate
				// ) {
				// 	return 1;
				// }

				// return 21;
			})(),
			sort,
			paginationToken: paginationTokenParam,
			paginationDirection,
		};

		const res = await gqlClientCustom.request(
			ActivitySearchIdOnlyDocument,
			{
				input,
			},
			getAuthorizationHeaders(data)
		);

		// if (
		// 	!cachedActivityServerLoad.pending &&
		// 	!cachedActivityServerLoad.invalidate &&
		// 	(!cachedActivityServerLoad.activity ||
		// 		cachedActivityServerLoad.index === 0)
		// ) {
		// 	res.activitySearch.activities.forEach(async (activity, i) => {
		// 		const promiseUrlSearchParams = new URL(url).searchParams;
		// 		promiseUrlSearchParams.set(
		// 			'paginationToken',
		// 			res.activitySearch.tokens[i]?.token as string
		// 		);

		// 		fetch(`/api/set-activity-cache`, {
		// 			method: 'POST',
		// 			priority: 'low',
		// 			body: JSON.stringify({
		// 				index:
		// 					PaginationDirection.Next === paginationDirection
		// 						? (i + 1) % 11
		// 						: (21 - i) % 11,
		// 				activityId: activity?.artwork_activity?.id,
		// 				accessToken: data.user?.access_token,
		// 			}),
		// 		});
		// 	});
		// }

		return Promise.resolve(res);
	});

	// if (
	// 	cachedActivityServerLoad.activity &&
	// 	!cachedActivityServerLoad.invalidate
	// ) {
	// 	return {
	// 		...data,
	// 		...cachedActivityServerLoad,
	// 		dropzoneUrlDialogForm,
	// 		pipelineSource: cachedActivityServerLoad.activity.data_source,
	// 		isNewActivity: false,
	// 		createActivityMeta: null,
	// 		prevPagesPromise: prevAndNextPagesPromises[0],
	// 		nextPagesPromise: prevAndNextPagesPromises[1],
	// 	};
	// }

	const activityServerLoad = await fetchActivityServerLoad({
		activityId,
		data: { user: { access_token: `${data.user?.access_token}` } },
	});

	if (!activityServerLoad.activity) {
		error(404, 'Activity not found.');
	}

	if (activityServerLoad.activity?.status?.key === Status_Enum.Archived) {
		error(404, 'Activity has been archived.');
	}

	return {
		...data,
		...activityServerLoad,
		dropzoneUrlDialogForm,
		pipelineSource: activityServerLoad.activity.data_source,
		isNewActivity: false,
		createActivityMeta: null,
		prevPagesPromise: prevAndNextPagesPromises[0],
		nextPagesPromise: prevAndNextPagesPromises[1],
	};
};
