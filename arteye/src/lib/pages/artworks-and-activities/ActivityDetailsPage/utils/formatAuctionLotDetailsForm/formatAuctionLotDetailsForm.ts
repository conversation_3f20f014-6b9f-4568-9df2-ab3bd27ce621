import type { AuctionLotDetailsForm } from '../../AuctionLotDetailsCard';
import { Status_Enum } from '$gql/types-custom';
import { AuctionLotType } from '$lib/types/types';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatAuctionLotDetailsForm = (
	activity: ActivityDetailsPageData['activity']
): AuctionLotDetailsForm => {
	const auctionLot = activity?.artwork_listing?.[0]?.auction_lot;

	const guaranteed = (auctionLot?.attributes || []).find(
		(attribute) => attribute?.type?.key === AuctionLotType.Guaranteed
	);

	const irrevocableBid = (auctionLot?.attributes || []).find(
		(attribute) => attribute?.type?.key === AuctionLotType.IrrevocableBid
	);

	const ownershipInterest = (auctionLot?.attributes || []).find(
		(attribute) => attribute?.type?.key === AuctionLotType.OwnershipInterest
	);

	const catalogueHighlight = (auctionLot?.attributes || []).find(
		(attribute) => attribute?.type?.key === AuctionLotType.CatalogueHighlight
	);

	const noReserve = (auctionLot?.attributes || []).find(
		(attribute) => attribute?.type?.key === AuctionLotType.NoReserve
	);

	const auctionCurrency = {
		code: auctionLot?.auction?.currency?.code || '',
		name: auctionLot?.auction?.currency?.name || '',
	};

	return {
		highEstimateLocal: String(
			activity?.artwork_listing?.[0]?.price_high_estimate?.amount || ''
		),
		highEstimateUsd: String(
			activity?.artwork_listing?.[0]?.price_high_estimate?.usd_amount || ''
		),
		highEstimateLocalCurrency: {
			code:
				activity?.artwork_listing?.[0]?.price_high_estimate?.currency?.code ||
				auctionCurrency.code,
		},

		lowEstimateLocal: String(
			activity?.artwork_listing?.[0]?.price_low_estimate?.amount || ''
		),
		lowEstimateUsd: String(
			activity?.artwork_listing?.[0]?.price_low_estimate?.usd_amount || ''
		),
		lowEstimateLocalCurrency: {
			code:
				activity?.artwork_listing?.[0]?.price_low_estimate?.currency?.code ||
				auctionCurrency.code,
		},

		saleAmountLocal: String(
			activity?.artwork_listing?.[0]?.sale_amount?.amount || ''
		),
		saleAmountUsd: String(
			activity?.artwork_listing?.[0]?.sale_amount?.usd_amount || ''
		),
		saleAmountLocalCurrency: {
			code:
				activity?.artwork_listing?.[0]?.sale_amount?.currency?.code ||
				auctionCurrency.code,
		},

		startingBidLocal: String(auctionLot?.starting_bid_amount || ''),

		salePriceIncludesPremium: !!auctionLot?.sale_amount_includes_premium,

		lotNumber: auctionLot?.lot_number || '',
		lotNotes: auctionLot?.lot_notes || '',
		saleRoomNotice: auctionLot?.saleroom_notice || '',

		attributeIds: {
			[AuctionLotType.Guaranteed]: guaranteed?.id || '',
			[AuctionLotType.IrrevocableBid]: irrevocableBid?.id || '',
			[AuctionLotType.OwnershipInterest]: ownershipInterest?.id || '',
			[AuctionLotType.CatalogueHighlight]: catalogueHighlight?.id || '',
			[AuctionLotType.NoReserve]: noReserve?.id || '',
		},

		amountIds: {
			[AuctionLotType.Guaranteed]: guaranteed?.amount?.id || '',
		},

		gauranteedLot: guaranteed?.status?.key === Status_Enum.Published,
		irrevocableBid: irrevocableBid?.status?.key === Status_Enum.Published,
		catalogueHighlight:
			catalogueHighlight?.status?.key === Status_Enum.Published,
		noReserve: noReserve?.status?.key === Status_Enum.Published,
		ownedByAuctionHouse:
			ownershipInterest?.status?.key === Status_Enum.Published,

		gauranteedAmount: String(guaranteed?.amount?.amount || ''),

		updateHistory: auctionLot,
	};
};
