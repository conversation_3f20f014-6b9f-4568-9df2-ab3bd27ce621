import type { ExhibitionForm } from '../../ExhibitionCard';
import { Routes } from '$lib/constants/routes';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatExhibitionForm = (
	activity: ActivityDetailsPageData['activity']
): ExhibitionForm => {
	const exhibition =
		activity?.artwork_listing?.[0]?.exhibition_listing?.exhibition;

	const title = exhibition?.title || '';

	const getSubTitle = () => {
		if (exhibition?.venue_city?.name && exhibition?.venue_country?.name) {
			return `${exhibition.venue_city.name}, ${exhibition.venue_country.name}`;
		}

		if (exhibition?.venue_city?.name) {
			return `${exhibition.venue_city.name}`;
		}

		if (exhibition?.venue_country?.name) {
			return `${exhibition.venue_country.name}`;
		}

		return '';
	};

	return {
		exhibition: {
			id: exhibition?.id || '',
			title,
			subTitle: getSubTitle(),
			url: `${Routes.Exhibitions}/${exhibition?.id}`,
		},
	};
};
