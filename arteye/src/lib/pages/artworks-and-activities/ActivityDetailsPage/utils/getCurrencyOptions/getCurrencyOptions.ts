import type { InputWithSelectOption } from '$global/components/InputWithSelect';
import type { LayoutData } from '$routes/types';

export const getCurrencyOptions = (
	currencies: NonNullable<LayoutData['user']>['dropdowns']['currencies']
): InputWithSelectOption[] => {
	return (currencies || []).map((currency) => {
		return {
			value: currency.code || '',
			label: currency.code || '',
		};
	});
};
