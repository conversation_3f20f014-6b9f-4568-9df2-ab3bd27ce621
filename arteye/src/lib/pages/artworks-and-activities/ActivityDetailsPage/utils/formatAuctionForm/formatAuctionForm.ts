import dayjs from 'dayjs';
import type { AuctionForm } from '../../AuctionCard';
import { Routes } from '$lib/constants/routes';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatAuctionForm = (
	activity: ActivityDetailsPageData['activity']
): AuctionForm => {
	const auction = activity?.artwork_listing?.[0]?.auction_lot?.auction;

	const auctionHouseName = auction?.auction_house?.organisation?.name || '';
	const auctionEndDate = auction?.local_auction_end_date
		? dayjs(auction?.local_auction_end_date.slice(0, -1)).format('DD/MM/YYYY')
		: '';
	const currency = auction?.currency?.code || '';
	const saleNumber = auction?.sale_number || '';

	const subTitle = [auctionHouseName, auctionEndDate, currency, saleNumber]
		.filter(Boolean)
		.join(', ');

	return {
		auction: {
			id: auction?.id || '',
			title: auction?.sale_name || '',
			subTitle,
			url: `${Routes.Auctions}/${auction?.id}`,
		},
	};
};
