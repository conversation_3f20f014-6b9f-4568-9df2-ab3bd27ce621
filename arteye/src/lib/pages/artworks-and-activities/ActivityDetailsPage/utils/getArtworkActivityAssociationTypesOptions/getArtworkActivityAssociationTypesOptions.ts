import type { SelectOption } from '$global/components/Select';
import type { LayoutData } from '$routes/types';

export const getArtworkActivityAssociationTypesOptions = (
	artworkActivityAssociationTypes: NonNullable<
		LayoutData['user']
	>['dropdowns']['artworkActivityAssociationTypes']
): SelectOption[] => {
	return (artworkActivityAssociationTypes || []).map((type) => {
		return {
			value: type.key || '',
			label: type.name || '',
		};
	});
};
