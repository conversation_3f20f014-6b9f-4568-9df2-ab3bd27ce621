import type { AssociatedEntityRow } from '../../AssociatedEntitiesCard';
import type {
	Create_Artwork_Activity_Association_Input,
	Update_Artwork_Activity_Association_Input,
} from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { CreateArtworkActivityAssociationItemsDocument } from '$lib/mutations/__generated__/createArtworkActivityAssociationItems.generated';
import { UpdateArtworkActivityAssociationBatchDocument } from '$lib/mutations/__generated__/updateArtworkActivityAssociationBatch.generated';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const mutateAssociatedEntities = async ({
	associatedEntities,
	activity,
	headers,
}: {
	associatedEntities: AssociatedEntityRow[];
	activity: ActivityDetailsPageData['activity'];
	headers: {
		Authorization: string;
	};
}) => {
	const promises = [];

	const updateItems: Update_Artwork_Activity_Association_Input[] =
		associatedEntities
			.filter((item) => item.isNew !== true)
			.map<Update_Artwork_Activity_Association_Input>((item) => ({
				id: item.association?.id,
				type: {
					key: item.association?.type.key,
					name: item.association?.type.name,
				},
				entity: {
					id: item.entity?.id,
					name: item.entity?.name,
				},
				status: {
					key: item.isDeleted ? Status_Enum.Archived : Status_Enum.Published,
				},
			}));

	const newItems: Create_Artwork_Activity_Association_Input[] =
		associatedEntities
			.filter((item) => item.isNew === true && !item.isDeleted)
			.map<Create_Artwork_Activity_Association_Input>((item) => ({
				artwork_activity: {
					id: activity?.id,
					timestamp: activity?.timestamp,
				},
				type: {
					key: item.association?.type.key || '',
					name: item.association?.type.name || '',
				},
				entity: {
					id: item.entity?.id,
					name: item.entity?.name || '',
				},
			}));

	const updateRes = gqlClient.request(
		UpdateArtworkActivityAssociationBatchDocument,
		{
			data: updateItems,
		},
		headers
	);

	promises.push(updateRes);

	if (newItems.length) {
		const createRes = gqlClient.request(
			CreateArtworkActivityAssociationItemsDocument,
			{
				data: newItems,
			},
			headers
		);

		promises.push(createRes);
	}

	return Promise.all(promises);
};
