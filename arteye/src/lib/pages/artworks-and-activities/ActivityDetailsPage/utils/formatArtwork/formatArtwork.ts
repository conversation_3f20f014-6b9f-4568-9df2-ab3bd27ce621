import { Routes } from '$lib/constants/routes';
import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';

export const formatArtwork = (artwork: {
	id: string | undefined | null;
	title: string | undefined | null;
	artist: {
		name: string | undefined | null;
		nationality: string | undefined | null;
		yearBirth: number | undefined | null;
		yearDeath: number | undefined | null;
	};
}) => {
	const artist = artwork.artist;
	const nationality = artist.nationality;

	let subTitle = '';

	if (artist.name && nationality && (artist.yearBirth || artist.yearDeath)) {
		subTitle = `${artist.name}, ${nationality}, ${getYearBirthDeathString({
			yearBirth: artist.yearBirth,
			yearDeath: artist.yearDeath,
			withWrapper: false,
		})}`;
	} else if (artist.name && nationality) {
		subTitle = `${artist.name}, ${nationality}`;
	} else if (artist.name) {
		subTitle = `${artist.name}`;
	}

	const url = `${Routes.ArtworkDetails}/${artwork?.id}`;

	return {
		line1: `${artwork.title}`,
		line2: subTitle,
		line3: url,
		line4: `${artwork.id}`,
	};
};
