import type { PrivateSaleDetailsForm } from '../../PrivateSaleDetailsCard';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatPrivateSaleDetailsForm = (
	activity: ActivityDetailsPageData['activity']
): PrivateSaleDetailsForm => {
	const artworkListing = activity?.artwork_listing?.[0];

	return {
		knownPriceUsd: String(artworkListing?.known_price?.usd_amount || ''),
		knownPriceLocal: String(artworkListing?.known_price?.amount || ''),
		knownPriceLocalCurrency: {
			code: artworkListing?.known_price?.currency?.code || '',
		},

		highEstimateLocal: String(
			artworkListing?.price_high_estimate?.amount || ''
		),
		highEstimateUsd: String(
			artworkListing?.price_high_estimate?.usd_amount || ''
		),
		highEstimateLocalCurrency: {
			code: artworkListing?.price_high_estimate?.currency?.code || '',
		},

		lowEstimateLocal: String(artworkListing?.price_low_estimate?.amount || ''),
		lowEstimateUsd: String(
			artworkListing?.price_low_estimate?.usd_amount || ''
		),
		lowEstimateLocalCurrency: {
			code: artworkListing?.price_low_estimate?.currency?.code || '',
		},

		saleAmountLocal: String(artworkListing?.sale_amount?.amount || ''),
		saleAmountUsd: String(artworkListing?.sale_amount?.usd_amount || ''),
		saleAmountLocalCurrency: {
			code: artworkListing?.sale_amount?.currency?.code || '',
		},

		updateHistory: artworkListing,
	};
};
