import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';

export const getArtworkSubtitle = (artworkArtist: {
	name: string | undefined | null;
	nationality: string | undefined | null;
	yearBirth: number | undefined | null;
	yearDeath: number | undefined | null;
}) => {
	let subTitle = '';

	if (
		artworkArtist.name &&
		artworkArtist.nationality &&
		(artworkArtist.yearBirth || artworkArtist.yearDeath)
	) {
		subTitle = `${artworkArtist.name}, ${artworkArtist.nationality}, ${getYearBirthDeathString(
			{
				yearBirth: artworkArtist.yearBirth,
				yearDeath: artworkArtist.yearDeath,
				withWrapper: false,
			}
		)}`;
	} else if (artworkArtist.name && artworkArtist.nationality) {
		subTitle = `${artworkArtist.name}, ${artworkArtist.nationality}`;
	} else if (artworkArtist.name) {
		subTitle = `${artworkArtist.name}`;
	}

	return subTitle;
};
