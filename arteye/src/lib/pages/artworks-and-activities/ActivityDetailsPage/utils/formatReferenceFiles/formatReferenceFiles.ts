import { Status_Enum } from '$gql/types-custom';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const formatReferenceFiles = ({
	activity,
	accessToken,
}: {
	activity: ActivityDetailsPageData['activity'];
	accessToken: string | undefined;
}) => {
	return (activity?.activity_artwork_info?.reference_files || []).map(
		(item) => {
			const referenceFile = item?.directus_files_id;

			return {
				...referenceFile,
				relationId: item?.id,
				url: getImageUrl(referenceFile?.id, accessToken),
				uploadUrlSource: '',
				file: null as File | null,
				isDeleted: item?.status?.key === Status_Enum.Archived,
				isNew: false,
			};
		}
	);
};
