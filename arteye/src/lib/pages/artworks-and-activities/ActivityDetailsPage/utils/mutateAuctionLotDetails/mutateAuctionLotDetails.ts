import type { AuctionLotDetailsForm } from '../../AuctionLotDetailsCard';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateArtworkListingMutationVariables } from '$lib/mutations/__generated__/updateArtworkListing.generated';
import { UpdateArtworkListingDocument } from '$lib/mutations/__generated__/updateArtworkListing.generated';
import type { UpdateAuctionLotItemMutationVariables } from '$lib/queries/__generated__/updateAuctionLotItems.generated';
import { UpdateAuctionLotItemDocument } from '$lib/queries/__generated__/updateAuctionLotItems.generated';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

const handleArtworkListingUpdate = async ({
	headers,
	auctionLotDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	const artworkListing = activity?.artwork_listing?.[0];

	const getAmount = (amount: string) => {
		const num = parseFloat(amount);

		return num || 0;
	};

	const highEstimateLocal = getAmount(auctionLotDetailsForm.highEstimateLocal);
	const lowEstimateLocal = getAmount(auctionLotDetailsForm.lowEstimateLocal);
	const saleAmountLocal = getAmount(auctionLotDetailsForm.saleAmountLocal);

	const data: UpdateArtworkListingMutationVariables['data'] = {
		price_high_estimate: {
			id: artworkListing?.price_high_estimate?.id,
			amount: highEstimateLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: auctionLotDetailsForm.highEstimateLocalCurrency.code,
			},
		},
		price_low_estimate: {
			id: artworkListing?.price_low_estimate?.id,
			amount: lowEstimateLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: auctionLotDetailsForm.lowEstimateLocalCurrency.code,
			},
		},
		sale_amount: {
			id: artworkListing?.sale_amount?.id,
			amount: saleAmountLocal,
			conversion_timestamp: new Date().toISOString(),
			currency: {
				code: auctionLotDetailsForm.saleAmountLocalCurrency.code,
			},
		},
	};

	if (highEstimateLocal === 0) {
		data.price_high_estimate = null;
	}

	if (lowEstimateLocal === 0) {
		data.price_low_estimate = null;
	}

	if (saleAmountLocal === 0) {
		data.sale_amount = null;
	}

	const res = await gqlClient.request(
		UpdateArtworkListingDocument,
		{
			id: artworkListing?.id || '',
			data,
		},
		headers
	);

	return { update_artwork_listing_item: res.update_artwork_listing_item };
};

const handleAuctionLotUpdate = ({
	headers,
	auctionLotDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	const artworkListing = activity?.artwork_listing?.[0];
	const auctionLot = artworkListing?.auction_lot;

	const data: UpdateAuctionLotItemMutationVariables['data'] = {
		starting_bid_amount: parseFloat(auctionLotDetailsForm.startingBidLocal),
		sale_amount_includes_premium:
			auctionLotDetailsForm.salePriceIncludesPremium,
		lot_number: auctionLotDetailsForm.lotNumber,
		lot_notes: auctionLotDetailsForm.lotNotes,
		saleroom_notice: auctionLotDetailsForm.saleRoomNotice,
	};

	return gqlClient.request(
		UpdateAuctionLotItemDocument,
		{
			id: auctionLot?.id || '',
			data,
		},
		headers
	);
};

export const mutateAuctionLotDetails = async ({
	headers,
	auctionLotDetailsForm,
	activity,
}: {
	headers: {
		Authorization: string;
	};
	auctionLotDetailsForm: AuctionLotDetailsForm;
	activity: ActivityDetailsPageData['activity'];
}) => {
	await handleAuctionLotUpdate({
		headers,
		auctionLotDetailsForm,
		activity,
	});

	return handleArtworkListingUpdate({
		headers,
		auctionLotDetailsForm,
		activity,
	});
};
