import type { AuctionForm } from '../../AuctionCard';
import { gqlClient } from '$lib/gqlClient';
import { UpdateAuctionLotItemDocument } from '$lib/mutations/__generated__/updateAuctionLotItem.generated';
import type { UpdateAuctionLotItemMutationVariables } from '$lib/queries/__generated__/updateAuctionLotItems.generated';

export const mutateAuctionLotListing = ({
	auctionLotId,
	auctionForm,
	headers,
}: {
	auctionLotId: string;
	auctionForm: AuctionForm;
	headers: {
		Authorization: string;
	};
}) => {
	if (!auctionForm.auction.id) {
		return;
	}

	const data: UpdateAuctionLotItemMutationVariables['data'] = {
		auction: {
			id: auctionForm.auction.id,
		},
	};

	return gqlClient.request(
		UpdateAuctionLotItemDocument,
		{
			id: auctionLotId,
			data,
		},
		headers
	);
};
