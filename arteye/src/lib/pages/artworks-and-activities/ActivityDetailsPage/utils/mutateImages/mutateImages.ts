import type {
	AdditionalInfoImages,
	PrimaryInfoImage,
} from '../../OtherInformationCard/OtherInformationCard.svelte';
import type { Update_Artwork_Activity_Artwork_Info_Input } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkActivityArtworkInfoItemDocument } from '$lib/mutations/__generated__/updateArtworkActivityArtworkInfoItem.generated';
import { uploadFile } from '$lib/utils/uploadFile/uploadFile';
import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export const mutateImages = async ({
	activity,
	headers,
	primaryImage,
	additionalImages,
}: {
	activity: ActivityDetailsPageData['activity'];
	headers: {
		Authorization: string;
	};
	primaryImage: PrimaryInfoImage;
	additionalImages: AdditionalInfoImages;
}) => {
	const getPrimaryImage = async (): Promise<
		Update_Artwork_Activity_Artwork_Info_Input['primary_image']
	> => {
		if (primaryImage.isDeleted) {
			return null;
		}

		if (primaryImage.id) {
			return activity?.activity_artwork_info?.primary_image;
		}

		if (primaryImage.file) {
			const uploadFileResponse = await uploadFile(primaryImage.file, headers);

			return uploadFileResponse;
		}

		return null;
	};

	const getAdditionalImages = async (): Promise<
		Update_Artwork_Activity_Artwork_Info_Input['additional_images']
	> => {
		if (additionalImages) {
			const imagePromises = additionalImages
				.map(async (image) => {
					const { file, relationId } = image;

					if (image.id) {
						return {
							id: relationId,
							status: {
								key: image.isDeleted
									? Status_Enum.Archived
									: Status_Enum.Published,
							},
						};
					}

					if (file) {
						const uploadFileResponse = await uploadFile(file, headers);

						return {
							artwork_info: {
								id: activity?.activity_artwork_info?.id || '',
							},
							type: { key: 'ARTWORK' },
							image: {
								id: uploadFileResponse.id,
								storage: 'PRIMARY',
								filename_download: uploadFileResponse.filename_download,
							},
						};
					}

					return null;
				})
				.filter(Boolean);

			return await Promise.all(imagePromises);
		}

		return null;
	};

	return gqlClient.request(
		UpdateArtworkActivityArtworkInfoItemDocument,
		{
			id: activity?.activity_artwork_info?.id || '',
			data: {
				primary_image: await getPrimaryImage(),
				additional_images: (await getAdditionalImages())?.filter(Boolean),
			},
		},
		headers
	);
};
