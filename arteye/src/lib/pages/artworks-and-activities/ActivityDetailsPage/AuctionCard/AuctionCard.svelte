<script module lang="ts">
	export interface ArtworkActivityAuction {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
	}

	export interface AuctionForm {
		auction: ArtworkActivityAuction;
	}
</script>

<script lang="ts">
	import { AuctionInput } from './AuctionInput';

	interface Props {
		auctionForm: AuctionForm;
		onChange: (auctionForm: AuctionForm) => void;
	}

	let { auctionForm, onChange }: Props = $props();

	const handleChange = (auction: AuctionForm['auction']) => {
		onChange({
			...auctionForm,
			auction,
		});
	};
</script>

<div class="rounded-md border bg-white p-4 max-lg:overflow-x-auto">
	<div class="min-w-[700px]">
		<AuctionInput onChange={handleChange} {auctionForm} />
	</div>
</div>
