<script lang="ts">
	import type { AuctionForm } from '../AuctionCard.svelte';
	import { AuctionAutocomplete } from './AuctionAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		auctionForm: AuctionForm;
		onChange: (gallery: AuctionForm['auction']) => void;
	}

	let { auctionForm, onChange }: Props = $props();

	const dataCy = 'auction';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			id,
			title,
			subTitle,
			url,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			title: '',
			subTitle: '',
			url: '',
			id: '',
		});
	};

	let selectedOption = $derived(
		!auctionForm.auction.id
			? null
			: {
					line1: auctionForm.auction.title || '',
					line2: auctionForm.auction.subTitle || '',
					line3: auctionForm.auction.url || '',
					line4: auctionForm.auction.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="auction" class="mb-2" required>
		Auction
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<AuctionAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
		/>
	</div>
</div>
