<script lang="ts" module>
	export const formatSeries = (
		auction: GetAuctionsQuery['auction'][number]
	) => {
		const url = `${Routes.Auctions}/${auction?.id}`;

		const auctionHouseName = auction?.auction_house?.organisation?.name || '';
		const auctionEndDate = auction?.local_auction_end_date
			? dayjs(auction?.local_auction_end_date.slice(0, -1)).format('DD/MM/YYYY')
			: '';
		const currency = auction?.currency?.code || '';
		const saleNumber = auction?.sale_number || '';

		const subTitle = [auctionHouseName, auctionEndDate, currency, saleNumber]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${auction?.auction_house?.organisation?.name}`,
			line2: subTitle,
			line3: url,
			line4: `${auction.id}`,
		};
	};
</script>

<script lang="ts">
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { GetAuctionsDocument } from '$lib/queries/__generated__/getAuctions.generated';
	import type {
		GetAuctionsQueryVariables,
		GetAuctionsQuery,
	} from '$lib/queries/__generated__/getAuctions.generated';

	const dataCy = 'auction';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetAuctionsQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: isUuid
				? { id: { _eq: value } }
				: {
						auction_house: {
							organisation: {
								name: {
									_icontains: value,
								},
							},
						},
					},
		};
	};

	const getOptions = (data: GetAuctionsQuery | undefined) => {
		return [...(data?.auction || []).map(formatSeries)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={`(${selectedOption.line2})`}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="parent-series"
				dataCy={`${dataCy}-parent-series`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					auction: [],
					auction_aggregated: [],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetAuctionsDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults class="text-left" dataCy={`${dataCy}-parent-series`}>
						No results found.
					</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
