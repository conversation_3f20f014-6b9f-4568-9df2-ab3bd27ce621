<script lang="ts">
	import classNames from 'classnames';
	import { EntityInput } from './EntityInput';
	import { AssociatedEntityFieldName } from './types';
	import type { AssociatedEntityHeader, AssociatedEntityRow } from './types';
	import { handleEntityChange } from './utils/handleEntityChange/handleEntityChange';
	import { handleEntityRemove } from './utils/handleEntityRemove/handleEntityRemove';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import type {
		SelectChangeEvent,
		SelectOption,
	} from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import {
		TableBody,
		TableCell,
		TableHeader,
		TableHeaderRow,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { LayoutData } from '$routes/types';

	interface Props {
		associationTypes: NonNullable<
			LayoutData['user']
		>['dropdowns']['artworkActivityAssociationTypes'];
		associatedEntities: AssociatedEntityRow[];
		onChange: (items: AssociatedEntityRow[]) => void;
	}

	let { associationTypes, associatedEntities, onChange }: Props = $props();

	const dataCy = 'associated-entities';

	let associationTypeOptions = $derived(
		(associationTypes || [])
			.filter((type) => type.key)
			.map((type) => {
				return {
					value: type.key || '',
					label: type.name || '',
				};
			}) satisfies SelectOption[]
	);

	let rows = $derived(
		associatedEntities?.filter((row) => !row.isDeleted) || []
	);

	const headers: AssociatedEntityHeader[] = [
		{
			fieldName: AssociatedEntityFieldName.Type,
			title: 'Association type',
		},
		{
			fieldName: AssociatedEntityFieldName.Name,
			title: 'Name',
		},
		{
			fieldName: AssociatedEntityFieldName.Action,
			title: '',
		},
	];

	const handleClickAdd = () => {
		onChange([
			{
				rowId: associatedEntities.length,
				association: null,
				entity: null,
				isNew: true,
				readOnly: false,
			},
			...associatedEntities,
		]);
	};

	const handleDelete = (rowId: number) => () => {
		onChange(
			associatedEntities.map((item) => {
				if (item.rowId === rowId) {
					return {
						...item,
						isDeleted: true,
					};
				}

				return item;
			})
		);
	};

	const handleAssociationTypeChange =
		(rowId: number) => (event: SelectChangeEvent) => {
			const items: AssociatedEntityRow[] = associatedEntities.map((item) => {
				if (item.rowId === rowId) {
					return {
						...item,
						association: {
							id: item.association?.id || '',
							type: {
								name: event.detail.selectedOption.label || '',
								key: event.detail.selectedOption.value || '',
							},
						},
					};
				}

				return item;
			});

			onChange(items);
		};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-4">Associated entities</Txt>

	<div class="max-lg:overflow-x-scroll">
		<table class="w-full table-fixed rounded-md bg-white min-w-[900px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName === AssociatedEntityFieldName.Action}
						<TableHeader {dataCy} class="px-3 py-0">
							{#snippet custom()}
								<div class="flex justify-end">
									<Button
										onclick={handleClickAdd}
										dataCy={`${dataCy}-add`}
										class="h-[2rem] w-[2rem] px-0"
										variant="secondary"
										size="xs"
									>
										<PlusIcon class="h-3 w-3" />
									</Button>
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy}>
							{header.title}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#each rows as row, index}
					{@const entity = row.entity}
					<TableRow {index} {dataCy}>
						{#each headers as header}
							{#if header.fieldName === AssociatedEntityFieldName.Type}
								<TableCell {dataCy} class="px-1 py-1">
									{#snippet custom()}
										<Select
											ariaLabel="Select an association type"
											dataCy={`${dataCy}-association-type`}
											name="association-type"
											options={associationTypeOptions}
											value={row.association?.type.key}
											size="sm"
											disabled={!!row.readOnly}
											classes={{ menu: '[&>div]:max-h-[150px]' }}
											onchange={handleAssociationTypeChange(row.rowId)}
										/>
									{/snippet}
								</TableCell>
							{:else if header.fieldName === AssociatedEntityFieldName.Name}
								<TableCell {dataCy}>
									{#snippet custom()}
										<div>
											<EntityInput
												{entity}
												class={classNames({
													'[&_button]:hidden': !!row.readOnly,
												})}
												onChange={handleEntityChange(
													row.rowId,
													associatedEntities,
													onChange
												)}
												onRemove={handleEntityRemove(
													row.rowId,
													associatedEntities,
													onChange
												)}
											/>
										</div>
									{/snippet}
								</TableCell>
							{:else if header.fieldName === AssociatedEntityFieldName.Action}
								<TableCell {dataCy} class="">
									{#snippet custom()}
										<div class="flex justify-end">
											{#if !row.readOnly}
												<div class="flex items-center justify-end gap-2">
													<UpdateHistoryTooltip
														updateHistory={row.updateHistory}
													/>

													<Button
														onclick={handleDelete(row.rowId)}
														dataCy={`${dataCy}-delete`}
														class="h-[2rem] w-[2rem] px-0"
														variant="secondary"
														size="xs"
													>
														<BinIcon class="h-3 w-3" />
													</Button>
												</div>
											{/if}
										</div>
									{/snippet}
								</TableCell>
							{/if}
						{/each}
					</TableRow>
				{/each}
			</TableBody>
		</table>
	</div>
</div>
