import type { UpdateHistory } from '$lib/components/UpdateHistoryTooltip';

export enum AssociatedEntityFieldName {
	Type = 'type',
	Name = 'name',
	Action = 'action',
}

export interface AssociatedEntity {
	id: string;
	name: string;
	displayedType: string;
	entityType: 'artist' | 'person' | 'organisation';
	orgType?: 'auction' | 'gallery' | 'fair' | 'exhibition';
	yearBirth?: number;
	yearDeath?: number;
	nationality?: string;
	location?: string;
	artistId?: string;
	personId?: string;
	orgId?: string;
}

export interface AssociatedEntityRow {
	rowId: number;
	readOnly: boolean | null | undefined;
	association: {
		id: string;
		type: {
			name: string;
			key: string;
		};
	} | null;
	entity: AssociatedEntity | null;
	isDeleted?: boolean;
	isNew?: boolean;
	updateHistory?: UpdateHistory;
}

export interface AssociatedEntityHeader {
	fieldName: AssociatedEntityFieldName;
	title: string;
}
