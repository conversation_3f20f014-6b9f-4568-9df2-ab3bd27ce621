<script lang="ts">
	import type { AssociatedEntity } from '..';
	import { EntityAutocomplete } from './EntityAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Routes } from '$lib/constants/routes';
	import type { GetEntitiesQuery } from '$lib/queries/__generated__/getEntities.generated';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import { formatPersonDetailsString } from '$lib/utils/getEntityDetails/formatPersonDetailsString/formatPersonDetailsString';
	import { formatOrganisationDetailsString } from '$lib/utils/getEntityDetails/formatOrganisationDetailsString/formatOrganisationDetailsString';
	import { formatOrganisationDetails } from '$lib/utils/entityFormatters/entityFormatters';

	interface Props {
		entity: AssociatedEntity | null;
		onChange: (entity: GetEntitiesQuery['entity'][number]) => void;
		onRemove: (entityId: string) => void;
		class?: string;
	}

	let { ...props }: Props = $props();

	const dataCy = 'entity';

	const handleChange = (option: OptionType) => {
		const entity = (
			option as OptionType & { entity: GetEntitiesQuery['entity'][number] }
		).entity;

		props.onChange(entity as GetEntitiesQuery['entity'][number]);
	};

	const handleRemove = () => {
		props.onRemove(props.entity?.id || '');
	};

	let getArtistOption = $derived(() => {
		if (!props.entity) {
			return null;
		}

		const line3 = formatPersonDetailsString({
			nationality: props.entity.nationality,
			yearBirth: props.entity.yearBirth,
			yearDeath: props.entity.yearDeath,
			type: props.entity.displayedType,
		});

		return {
			line1: props.entity.name,
			line2: `${Routes.Artists}/${props.entity.artistId}`,
			line3,
		};
	});

	let getPersonOption = $derived(() => {
		if (!props.entity) {
			return null;
		}

		const line3 = formatPersonDetailsString({
			nationality: props.entity.nationality,
			yearBirth: props.entity.yearBirth,
			yearDeath: props.entity.yearDeath,
			type: props.entity.displayedType,
		});

		return {
			line1: props.entity.name,
			line2: `${Routes.People}/${props.entity.personId}`,
			line3,
		};
	});

	let getOrganisationOption = $derived(() => {
		if (!props.entity) {
			return null;
		}

		const line3 = formatOrganisationDetailsString({
			location: props.entity.location,
			type: props.entity.displayedType,
		});

		return {
			line1: props.entity.name,
			line2: `${Routes.Organisations}/${props.entity.orgId}`,
			line3,
		};
	});

	let getSelectedOption = $derived((): OptionType | null => {
		if (!props.entity) {
			return null;
		}

		if (props.entity?.entityType === 'artist') {
			return getArtistOption();
		}

		if (props.entity?.entityType === 'person') {
			return getPersonOption();
		}

		if (props.entity?.entityType === 'organisation') {
			return getOrganisationOption();
		}

		return {
			line1: props.entity?.name,
		};
	});
</script>

<div class={props.class}>
	<EntityAutocomplete
		onChange={handleChange}
		onRemove={handleRemove}
		selectedOption={getSelectedOption()}
	/>
</div>
