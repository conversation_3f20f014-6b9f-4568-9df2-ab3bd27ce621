<script lang="ts" module>
	export const formatResults = (entity: GetEntitiesQuery['entity'][number]) => {
		return {
			entity,
			line1: `${entity.name}`,
			line1Suffix: getEntityDetails(entity),
			line2: getEntityUrl(entity),
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Button } from '$global/components/Button';
	import Link from '$global/components/Link/Link.svelte';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { gqlClient } from '$lib/gqlClient';
	import {
		GetEntitiesDocument,
		type GetEntitiesQuery,
		type GetEntitiesQueryVariables,
	} from '$lib/queries/__generated__/getEntities.generated';
	import { getEntityDetails } from '$lib/utils/getEntityDetails/getEntityDetails';
	import { getEntityUrl } from '$lib/utils/getEntityUrl/getEntityUrl';

	const dataCy = 'associated-entities';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: isUuid
				? { id: { _eq: value } }
				: {
						name: {
							_icontains: value,
						},
					},
		};
	};

	const getOptions = (data: GetEntitiesQuery | undefined) => {
		return [...(data?.entity || []).map(formatResults)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<div
			class="flex h-[32px] items-center justify-between gap-2 rounded border border-gray-200 pl-2"
		>
			<div class="flex w-[calc(100%-36px)] items-center gap-1">
				<Link
					href={selectedOption.line2}
					rel="noopener noreferrer"
					target="_blank"
					class="flex items-center gap-1"
				>
					<Txt
						class=" max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left text-blue-500"
						variant="label3"
					>
						{selectedOption.line1}
					</Txt>

					<ExternalIcon color="blue-500" class="h-4 w-4 flex-1" />
				</Link>

				<Txt
					class=" max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal text-gray-500"
					variant="label3"
				>
					{selectedOption.line3}
				</Txt>
			</div>

			<Button
				variant="secondary"
				class="w-[32px] !border-0 bg-transparent"
				size="xs"
				dataCy={`${dataCy}-remove`}
				onclick={handleRemove}
			>
				{#snippet leading()}
					<CrossIcon class="h-4 w-4" />
				{/snippet}
			</Button>
		</div>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="entity-search"
				{dataCy}
				placeholder="Start typing to search"
				emptyValueResponse={{
					entity: [],
				}}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetEntitiesDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults class="text-left" {dataCy}>No results found.</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
