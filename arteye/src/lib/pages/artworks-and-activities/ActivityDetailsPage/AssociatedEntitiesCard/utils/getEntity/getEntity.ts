import { type AssociatedEntity } from '../..';
import type { GetEntitiesQuery } from '$lib/queries/__generated__/getEntities.generated';
import { getEntityType } from '$lib/utils/getEntityType/getEntityType';

export const getEntity = (entity: GetEntitiesQuery['entity'][number]) => {
	const isArtistType = !!entity?.artist?.id;
	const isPersonType = !!entity?.person?.id;

	const entityType = (() => {
		if (isArtistType) {
			return 'artist';
		} else if (isPersonType) {
			return 'person';
		} else {
			return 'organisation';
		}
	})();

	const yearBirth =
		isArtistType || isPersonType ? entity?.person?.year_birth || 0 : 0;
	const yearDeath =
		isArtistType || isPersonType ? entity?.person?.year_death || 0 : 0;
	const nationality =
		isArtistType || isPersonType
			? entity?.person?.nationalities?.[0]?.country?.country_nationality || ''
			: '';
	const location =
		entityType === 'organisation'
			? entity?.addresses?.[0]?.city?.name || ''
			: '';

	const displayedType = getEntityType(entity);

	const item: AssociatedEntity = {
		id: entity?.id || '',
		name: entity?.name || '',
		entityType,
		displayedType,
		yearBirth,
		yearDeath,
		nationality,
		location,
		artistId: entity?.artist?.id || '',
		personId: entity?.person?.id || '',
		orgId: entity?.organisation?.id || '',
	};

	return item;
};
