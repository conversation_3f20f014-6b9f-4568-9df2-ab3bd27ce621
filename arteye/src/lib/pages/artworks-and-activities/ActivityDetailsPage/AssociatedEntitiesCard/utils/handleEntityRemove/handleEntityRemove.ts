import { type AssociatedEntityRow } from '../..';

export const handleEntityRemove =
	(
		rowId: number,
		associatedEntities: AssociatedEntityRow[],
		onChange: (items: AssociatedEntityRow[]) => void
	) =>
	() => {
		const items: AssociatedEntityRow[] = associatedEntities.map((item) => {
			if (item.rowId === rowId) {
				return {
					...item,
					entity: null,
				};
			}

			return item;
		});

		onChange(items);
	};
