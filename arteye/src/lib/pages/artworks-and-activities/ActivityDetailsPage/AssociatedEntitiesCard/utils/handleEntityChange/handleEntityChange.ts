import { type AssociatedEntityRow } from '../..';
import { getEntity } from '../getEntity/getEntity';
import type { GetEntitiesQuery } from '$lib/queries/__generated__/getEntities.generated';

export const handleEntityChange =
	(
		rowId: number,
		associatedEntities: AssociatedEntityRow[],
		onChange: (items: AssociatedEntityRow[]) => void
	) =>
	(rawEntity: GetEntitiesQuery['entity'][number]) => {
		const items: AssociatedEntityRow[] = associatedEntities.map((item) => {
			if (item.rowId === rowId) {
				return {
					...item,
					isDeleted: false,
					association: {
						id: item.association?.id || '',
						type: {
							name: item.association?.type.name || '',
							key: item.association?.type.key || '',
						},
					},
					entity: getEntity(rawEntity),
				};
			}

			return item;
		});

		onChange(items);
	};
