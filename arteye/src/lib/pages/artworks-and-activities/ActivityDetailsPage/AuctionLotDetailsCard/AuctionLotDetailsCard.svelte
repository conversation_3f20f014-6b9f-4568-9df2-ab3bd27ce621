<script module lang="ts">
	export interface AuctionLotDetailsForm {
		lowEstimateLocalCurrency: {
			code: string;
		};
		lowEstimateUsd: string;
		lowEstimateLocal: string;

		highEstimateLocalCurrency: {
			code: string;
		};
		highEstimateUsd: string;
		highEstimateLocal: string;

		saleAmountLocalCurrency: {
			code: string;
		};
		saleAmountUsd: string;
		saleAmountLocal: string;

		startingBidLocal: string;

		salePriceIncludesPremium: boolean;

		lotNumber: string;
		lotNotes: string;
		saleRoomNotice: string;

		attributeIds: {
			[AuctionLotType.Guaranteed]: string;
			[AuctionLotType.IrrevocableBid]: string;
			[AuctionLotType.OwnershipInterest]: string;
			[AuctionLotType.CatalogueHighlight]: string;
			[AuctionLotType.NoReserve]: string;
		};

		amountIds: {
			[AuctionLotType.Guaranteed]: string;
		};

		gauranteedLot: boolean;
		irrevocableBid: boolean;
		ownedByAuctionHouse: boolean;
		catalogueHighlight: boolean;
		noReserve: boolean;

		gauranteedAmount: string;

		updateHistory: UpdateHistory;
	}
</script>

<script lang="ts">
	import type { CheckboxValue } from '$global/components/Checkbox';
	import { Checkbox } from '$global/components/Checkbox';
	import Input from '$global/components/Input/Input.svelte';
	import { formatNumberWithSeparator } from '$global/components/Input/utils/formatNumberWithSeparator/formatNumberWithSeparator';
	import { formatWithThousandSeparator } from '$global/components/Input/utils/formatWithThousandSeparator/formatWithThousandSeparator';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { removeThousandSeparator } from '$global/components/Input/utils/removeThousandSeparator/removeThousandSeparator';
	import { InputLabel } from '$global/components/InputLabel';
	import type { InputWithSelectOption } from '$global/components/InputWithSelect';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { Txt } from '$global/components/Txt';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { UpdateHistory } from '$lib/components/UpdateHistoryTooltip';
	import type { AuctionLotType } from '$lib/types/types';

	interface Props {
		auctionLotDetailsForm: AuctionLotDetailsForm;
		currencyOptions: InputWithSelectOption[];
		onChange: (form: AuctionLotDetailsForm) => void;
	}

	let { auctionLotDetailsForm, currencyOptions, onChange }: Props = $props();

	const dataCy = 'auction-lot-details';

	const handleInputChange =
		(
			field: keyof Pick<
				AuctionLotDetailsForm,
				| 'lowEstimateUsd'
				| 'highEstimateUsd'
				| 'lowEstimateLocal'
				| 'highEstimateLocal'
				| 'startingBidLocal'
				| 'saleAmountUsd'
				| 'saleAmountLocal'
				| 'lotNumber'
				| 'lotNotes'
				| 'saleRoomNotice'
				| 'gauranteedAmount'
			>
		) =>
		(event?: Event | undefined) => {
			const value = (event?.target as HTMLInputElement).value;
			onChange({
				...auctionLotDetailsForm,
				[field]: removeThousandSeparator(value),
			});
		};

	const handleCheckboxChange =
		(
			field: keyof Pick<
				AuctionLotDetailsForm,
				| 'gauranteedLot'
				| 'irrevocableBid'
				| 'ownedByAuctionHouse'
				| 'catalogueHighlight'
				| 'noReserve'
				| 'salePriceIncludesPremium'
			>
		) =>
		(checked: CheckboxValue) => {
			onChange({
				...auctionLotDetailsForm,
				[field]: !!checked,
			});
		};

	const handleInputWithSelectChange =
		(
			field: keyof Pick<
				AuctionLotDetailsForm,
				| 'lowEstimateLocalCurrency'
				| 'highEstimateLocalCurrency'
				| 'saleAmountLocalCurrency'
			>
		) =>
		(option: InputWithSelectOption) => {
			onChange({
				...auctionLotDetailsForm,
				[field]: {
					code: `${option?.value}`,
					name: `${option?.label}`,
				},
			});
		};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-4">Auction lot details</Txt>

	<div class="mb-6 flex flex-col lg:grid grid-cols-5 gap-4">
		<div class="col-span-1">
			<Input
				label="Low estimate (USD)"
				dataCy={`${dataCy}-lowEstimateUsd`}
				name="lowEstimateUsd"
				value={`${formatNumberWithSeparator(auctionLotDetailsForm.lowEstimateUsd)}`}
				onkeyup={handleInputChange('lowEstimateUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>
		<div class="col-span-1">
			<Input
				label="High estimate (USD)"
				dataCy={`${dataCy}-highEstimateUsd`}
				name="highEstimateUsd"
				value={`${formatNumberWithSeparator(auctionLotDetailsForm.highEstimateUsd)}`}
				onkeyup={handleInputChange('highEstimateUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>
		<div class="col-span-1">
			<InputWithSelect
				label="Low estimate (local)"
				size="sm"
				dataCy={`${dataCy}-lowEstimateLocal`}
				name="lowEstimateLocal"
				selectValue={auctionLotDetailsForm.lowEstimateLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(auctionLotDetailsForm.lowEstimateLocal)}`}
				options={currencyOptions}
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange('lowEstimateLocalCurrency')}
				onkeyup={handleInputChange('lowEstimateLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
		</div>
		<div class="col-span-1">
			<InputWithSelect
				label="High estimate (local)"
				size="sm"
				dataCy={`${dataCy}-highEstimateLocal`}
				name="highEstimateLocal"
				selectValue={auctionLotDetailsForm.highEstimateLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(auctionLotDetailsForm.highEstimateLocal)}`}
				options={currencyOptions}
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange(
					'highEstimateLocalCurrency'
				)}
				onkeyup={handleInputChange('highEstimateLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
		</div>
		<div class="col-span-1">
			<Input
				label="Starting bid amount (local)"
				dataCy={`${dataCy}-startingBidLocal`}
				name="startingBidLocal"
				value={`${formatNumberWithSeparator(auctionLotDetailsForm.startingBidLocal)}`}
				onkeyup={handleInputChange('startingBidLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
			/>
		</div>
	</div>

	<div class="mb-6 flex flex-col lg:grid grid-cols-5 gap-4">
		<div class="col-span-1">
			<Input
				label="Sale amount (USD)"
				dataCy={`${dataCy}-saleAmountUsd`}
				name="saleAmountUsd"
				value={`${formatNumberWithSeparator(auctionLotDetailsForm.saleAmountUsd)}`}
				onkeyup={handleInputChange('saleAmountUsd')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				size="sm"
				tooltip="tbc"
				disabled
			/>
		</div>
		<div class="col-span-1">
			<InputWithSelect
				label="Sale amount (local)"
				size="sm"
				dataCy={`${dataCy}-saleAmountLocal`}
				name="saleAmountLocal"
				selectValue={auctionLotDetailsForm.saleAmountLocalCurrency.code}
				inputValue={`${formatNumberWithSeparator(auctionLotDetailsForm.saleAmountLocal)}`}
				options={currencyOptions}
				tooltip="Copy TBC"
				onSelectChange={handleInputWithSelectChange('saleAmountLocalCurrency')}
				onkeyup={handleInputChange('saleAmountLocal')}
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
				selectPlaceholder="Select"
			/>
		</div>
		<div class="col-span-1 flex items-end">
			<InputLabel
				dataCy={`${dataCy}-salePriceIncludesPremium`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-salePriceIncludesPremium`}
					name="salePriceIncludesPremium"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.salePriceIncludesPremium}
					onChange={handleCheckboxChange('salePriceIncludesPremium')}
				/>

				<div class="pl-1.5">Sale price includes premium</div>
			</InputLabel>
		</div>
	</div>

	<div class="mb-6 flex flex-col lg:grid grid-cols-5 gap-4">
		<div class="col-span-1">
			<Input
				label="Lot number"
				dataCy={`${dataCy}-lotNumber`}
				name="lotNumber"
				value={`${auctionLotDetailsForm.lotNumber}`}
				onkeyup={handleInputChange('lotNumber')}
				size="sm"
				tooltip="tbc"
			/>
		</div>
		<div class="col-span-1">
			<Input
				label="Lot notes"
				dataCy={`${dataCy}-lotNotes`}
				name="lotNotes"
				value={`${auctionLotDetailsForm.lotNotes}`}
				onkeyup={handleInputChange('lotNotes')}
				size="sm"
				tooltip="tbc"
			/>
		</div>
		<div class="col-span-1">
			<Input
				label="Sale room notice"
				dataCy={`${dataCy}-saleRoomNotice`}
				name="saleRoomNotice"
				value={`${auctionLotDetailsForm.saleRoomNotice}`}
				onkeyup={handleInputChange('saleRoomNotice')}
				size="sm"
				tooltip="tbc"
			/>
		</div>
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
		<div class="col-span-1">
			<InputLabel
				dataCy={`${dataCy}-gauranteedLot`}
				class="mb-4"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-gauranteedLot`}
					name="gauranteedLot"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.gauranteedLot}
					onChange={handleCheckboxChange('gauranteedLot')}
				/>

				<div class="pl-1.5">Gauranteed lot</div>
			</InputLabel>

			{#if auctionLotDetailsForm.gauranteedLot}
				<Input
					label="Gaurantee amount"
					dataCy={`${dataCy}-gauranteedAmount`}
					name="gauranteedAmount"
					value={auctionLotDetailsForm.gauranteedAmount}
					onkeyup={handleInputChange('gauranteedAmount')}
					size="sm"
					tooltip="tbc"
					class="mb-1"
				/>
			{/if}
		</div>
		<div class="col-span-1">
			<InputLabel
				dataCy={`${dataCy}-irrevocableBid`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-irrevocableBid`}
					name="irrevocableBid"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.irrevocableBid}
					onChange={handleCheckboxChange('irrevocableBid')}
				/>

				<div class="pl-1.5">Irrevocable bid</div>
			</InputLabel>
		</div>
		<div class="col-span-1">
			<InputLabel
				dataCy={`${dataCy}-ownedByAuctionHouse`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-ownedByAuctionHouse`}
					name="ownedByAuctionHouse"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.ownedByAuctionHouse}
					onChange={handleCheckboxChange('ownedByAuctionHouse')}
				/>

				<div class="pl-1.5">Owned by auction house</div>
			</InputLabel>
		</div>
		<div class="col-span-1">
			<InputLabel
				dataCy={`${dataCy}-catalogueHighlight`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-catalogueHighlight`}
					name="catalogueHighlight"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.catalogueHighlight}
					onChange={handleCheckboxChange('catalogueHighlight')}
				/>

				<div class="pl-1.5">Catalogue highlight</div>
			</InputLabel>
		</div>
		<div class="col-span-1">
			<InputLabel
				dataCy={`${dataCy}-no-reserve`}
				class="col-span-2"
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCy}-no-reserve`}
					name="no-reserve"
					class="mt-[-0.125rem]"
					checked={auctionLotDetailsForm.noReserve}
					onChange={handleCheckboxChange('noReserve')}
				/>

				<div class="pl-1.5">No reserve</div>
			</InputLabel>
		</div>
	</div>

	<div class="flex justify-end">
		<UpdateHistoryTooltip updateHistory={auctionLotDetailsForm.updateHistory} />
	</div>
</div>
