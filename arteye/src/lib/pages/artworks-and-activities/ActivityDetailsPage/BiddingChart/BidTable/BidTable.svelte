<script lang="ts">
	import classNames from 'classnames';
	import { type BiddingChartDetailsForm } from '../BiddingChart.svelte';
	import type { BidderInfoRow, BidRow } from '../types';
	import { Table } from './Table';
	import { Timer } from './Timer';
	import { Button } from '$global/components/Button';
	import { Txt } from '$global/components/Txt';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';

	interface Props {
		recordingInProgress: boolean;
		biddingChartDetailsForm: BiddingChartDetailsForm;
		auctionClients: null | GetAuctionClientQuery['auction_client'];
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		winningPaddleNumberValue: string;
		onOpenResetAndClearDialog: () => void;
		bidRows: BidRow[];
		hasRecordedBids: boolean | null;
		onChangeBidRows: (rows: BidRow[]) => void;
		onToggleBiddingChart: () => void;
		showBiddingProgressChart: boolean;
		deletedBids: { bid: BidRow; index: number }[];
	}

	let {
		recordingInProgress = $bindable(),
		biddingChartDetailsForm,
		auctionClients,
		bidderInfoRows,
		winningPaddleNumberValue = $bindable(),
		onOpenResetAndClearDialog,
		bidRows,
		hasRecordedBids = $bindable(),
		onChangeBidRows,
		onToggleBiddingChart,
		showBiddingProgressChart,
		deletedBids = $bindable(),
	}: Props = $props();

	let showResetAndClear = $derived(
		bidRows?.findIndex((row) => row.isHammerBid) > -1 && hasRecordedBids
	);

	const handleClickResetAndClear = () => {
		if (bidRows.every((bidRow) => bidRow.isNew)) {
			onChangeBidRows([]);
		} else {
			onOpenResetAndClearDialog();
		}
	};
</script>

<div
	class={classNames('w-full border border-gray-200', {
		'border-t-0': bidRows?.length,
	})}
>
	{#if bidRows?.filter((bidRow) => !bidRow.isHammerBid)?.length}
		<div>
			<Table
				bind:deletedBids
				{hasRecordedBids}
				{biddingChartDetailsForm}
				{auctionClients}
				{bidderInfoRows}
				bind:winningPaddleNumberValue
				{onChangeBidRows}
				{bidRows}
			/>
		</div>
	{:else}
		<div class="flex h-[278px] items-center justify-center bg-blue-100">
			<div class="w-[460px] rounded bg-white px-8 py-4">
				<Txt variant="label4" class="mb-2">Waiting for bids to be recorded.</Txt
				>

				<Txt variant="body3" class=" text-gray-500">
					Step 1. Ensure you're watching the correct auction video.
				</Txt>
				<Txt variant="body3" class=" text-gray-500">
					Step 2. Press 'Record Bid' as soon as the first bid is placed.
				</Txt>
				<Txt variant="body3" class=" text-gray-500">
					Step 3. Continue recording each bid promptly.
				</Txt>
				<Txt variant="body3" class=" text-gray-500">
					Step 4. Press 'Record Hammer' when the auctioneer closes the lot.
				</Txt>
				<Txt variant="body3" class=" text-gray-500">
					Step 5. Add extra information in Bid Table and Bidder Client Info.
				</Txt>
				<Txt variant="body3" class=" text-gray-500">
					Step 6. Save changes made to this auction lot page.
				</Txt>
			</div>
		</div>
	{/if}
	<div
		class={classNames('py-4 flex justify-center flex-col', {
			' bg-blue-100': showResetAndClear,
		})}
	>
		{#if !showResetAndClear}
			<Timer
				bind:recordingInProgress
				bind:hasRecordedBids
				{onChangeBidRows}
				{bidRows}
			/>
		{:else}
			<div class="flex justify-center">
				<div class="mx-auto text-center">
					<Txt class="mb-2">Save page to save updates or</Txt>
					<button onclick={handleClickResetAndClear}>
						<Txt class="underline">Reset and clear</Txt>
					</button>
				</div>
			</div>
		{/if}

		<div class="flex justify-center py-4">
			<Button
				dataCy="toggle-show-bidding-chart"
				variant="secondary"
				size="sm"
				onclick={onToggleBiddingChart}
			>
				{#if showBiddingProgressChart}
					Hide
				{:else}
					Show
				{/if}

				bidding chart
			</Button>
		</div>
	</div>
</div>
