<script lang="ts">
	import { type BidRow } from '../../types';
	import { formatTime } from '../../utils/formatTime/formatTime';
	import { getNewBid } from '../../utils/getNewBid/getNewBid';
	import { Button } from '$global/components/Button';
	import { Txt } from '$global/components/Txt';

	interface Props {
		bidRows: BidRow[];
		onChangeBidRows: (rows: BidRow[]) => void;
		hasRecordedBids: boolean | null;
		recordingInProgress: boolean;
	}

	let {
		bidRows,
		onChangeBidRows,
		hasRecordedBids = $bindable(),
		recordingInProgress = $bindable(),
	}: Props = $props();

	let displayTime = $state('00:00:00');
	let startTime: number | null = null;
	let elapsedTime = 0;
	// eslint-disable-next-line no-undef
	let interval: NodeJS.Timeout | null = null;

	const updateDisplay = () => {
		if (startTime !== null) {
			const currentTime = Date.now();
			elapsedTime = Math.floor((currentTime - startTime) / 1000);
		}
		displayTime = formatTime(elapsedTime);
	};

	const startTimer = () => {
		startTime = Date.now() - elapsedTime * 1000;
		recordingInProgress = true;
	};

	const stopTimer = () => {
		startTime = null;
		hasRecordedBids = true;
		recordingInProgress = false;
	};

	const handleClickRecordBid = () => {
		if (!interval) {
			startTimer();
			interval = setInterval(updateDisplay, 1000);
		}
		addBid();
		setTimeout(() => {
			document.getElementById(`text-notes-${bidRows.length - 1}`)?.focus();
		}, 0);
	};

	const handleClickRecordHammer = () => {
		stopTimer();
		addBid(true);
	};

	const handleClickReset = () => {
		startTime = null;
		interval = null;
		hasRecordedBids = false;
		recordingInProgress = false;
		elapsedTime = 0;
		displayTime = '00:00:00';
		onChangeBidRows([]);
	};

	const addBid = (isHammerBid?: boolean) => {
		const existingBids = bidRows || [];
		const newBids = [
			...(isHammerBid
				? existingBids.filter((bidRow) => !bidRow.isHammerBid)
				: existingBids),
			{
				...getNewBid(bidRows),
				formattedTime: displayTime,
				...(isHammerBid && { isHammerBid }),
			},
		];

		onChangeBidRows(newBids as BidRow[]);
	};
</script>

<Txt variant="label1" class="mb-2 text-center">{displayTime}</Txt>
<div class="flex justify-center gap-2">
	<Button
		dataCy="record-bid"
		class="w-[190px]"
		size="sm"
		onclick={handleClickRecordBid}>record bid</Button
	>
	<Button
		variant="secondary"
		dataCy="record-bid"
		class="w-[190px]"
		size="sm"
		disabled={!bidRows?.length}
		onclick={handleClickRecordHammer}>record hammer</Button
	>
</div>
{#if recordingInProgress}
	<div class="mt-1 flex justify-center">
		<button onclick={handleClickReset}
			><Txt component="span" variant="body2" class="underline"
				>Reset and clear</Txt
			></button
		>
	</div>
{/if}
