<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import duration from 'dayjs/plugin/duration';
	import { type BiddingChartDetailsForm } from '../../BiddingChart.svelte';
	import { type BidRow, type BidderInfoRow } from '../../types';
	import { findConversionTimestamp } from '../../utils/findConversionTimestamp/findConversionTimestamp';
	import { getBidderInfo } from '../../utils/getBidderInfo/getBidderInfo';
	import { getNewBid } from '../../utils/getNewBid/getNewBid';
	import { getPositionFromNotes } from '../../utils/getPositionFromNotes/getPositionFromNotes';
	import { isManualPaddleNumberValid } from '../../utils/isManualPaddleNumberValid/isManualPaddleNumberValid';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { HammerIcon } from '$global/assets/icons/HammerIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import {
		TableRow,
		TableHeader,
		TableHeaderRow,
		TableBody,
		TableCell,
		TableActionCell,
		getCellWidth,
	} from '$global/components/Table';
	import { Txt, TxtVariantsMap } from '$global/components/Txt';
	import { clickOutside } from '$global/utils/clickOutside/clickOutside';
	import { isPriceValid } from '$global/utils/isPriceValid/isPriceValid';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';

	interface Props {
		biddingChartDetailsForm: BiddingChartDetailsForm;
		auctionClients: null | GetAuctionClientQuery['auction_client'];
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		bidRows: BidRow[];
		hasRecordedBids: boolean | null;
		onChangeBidRows: (rows: BidRow[]) => void;
		winningPaddleNumberValue: string;
		deletedBids: { bid: BidRow; index: number }[];
	}

	let {
		deletedBids = $bindable(),
		biddingChartDetailsForm,
		auctionClients,
		bidderInfoRows,
		bidRows,
		hasRecordedBids,
		onChangeBidRows,
		winningPaddleNumberValue = $bindable(),
	}: Props = $props();

	dayjs.extend(duration);

	const actionCellWidth = '7rem';
	const dataCyPrefix = 'bid-table';
	const headers = [
		'',
		'Time',
		'Text notes',
		'Price',
		'Position',
		'Bidder entity',
		'Client entity',
		'',
	];

	let indexOpenPopover = $state(-1);

	const handleDeleteBid = (i: number) => () => {
		deletedBids = [...deletedBids, { index: i, bid: bidRows[i] }];

		onChangeBidRows(
			bidRows.map((bidRow, index) =>
				i === index ? { ...bidRow, isDeleted: true } : bidRow
			)
		);

		indexOpenPopover = -1;
	};

	const handleAddBidAbove = (i: number) => () => {
		onChangeBidRows([
			...bidRows.slice(0, i),
			getNewBid(bidRows),
			...bidRows.slice(i),
		]);
		indexOpenPopover = -1;
	};

	const handleAddBidBelow = (i: number) => () => {
		onChangeBidRows([
			...bidRows.slice(0, i + 1),
			getNewBid(bidRows),
			...bidRows.slice(i + 1),
		]);
		indexOpenPopover = -1;
	};

	const handleKeyDownFormattedTime = (index: number) => (e: KeyboardEvent) => {
		if (e.key.includes('Arrow')) {
			e.preventDefault();
		}

		if (e.key === 'ArrowUp') {
			(
				document.getElementById(`time-${index - 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowDown') {
			(
				document.getElementById(`time-${index + 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowRight') {
			(
				document.getElementById(`text-notes-${index}`) as HTMLInputElement
			)?.select();
		}
	};

	const handleKeyUpFormattedTime = (index: number) => (e: KeyboardEvent) => {
		onChangeBidRows(
			bidRows.map((bidRow, i) =>
				i === index
					? {
							...bidRow,
							formattedTime: (e.target as unknown as { value: string })?.value,
						}
					: bidRow
			)
		);
	};

	const handleKeyUpWinningPaddleNumber = () => {
		onChangeBidRows(bidRows);
	};

	const handleKeyDownPrice = (index: number) => (e: KeyboardEvent) => {
		handleKeyDownNumbersOnly(e, true, true);

		if (e.key.includes('Arrow')) {
			e.preventDefault();
		}

		if (e.key === 'ArrowUp') {
			(
				document.getElementById(`price-${index - 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowDown') {
			(
				document.getElementById(`price-${index + 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowLeft') {
			(
				document.getElementById(`text-notes-${index}`) as HTMLInputElement
			)?.select();
		}
	};

	const handleKeyUpPrice = (index: number) => (e: KeyboardEvent) => {
		const newAmount = {
			amount: (e.target as unknown as { value: string })?.value,
			usd_amount: (e.target as unknown as { value: string })?.value,
		};

		onChangeBidRows(
			bidRows.map((bidRow, i) =>
				i === index
					? {
							...bidRow,
							amount: bidRow.amount
								? {
										...bidRow.amount,
										...newAmount,
									}
								: {
										...newAmount,
										conversion_timestamp:
											findConversionTimestamp(bidRows) ||
											new Date().toISOString(),
										currency: {
											code: 'USD',
											name: 'usd',
										},
									},
						}
					: bidRow
			)
		);
	};

	const handleKeyDownNotes = (index: number) => (e: KeyboardEvent) => {
		if (e.key.includes('Arrow')) {
			e.preventDefault();
		}

		if (e.key === 'ArrowUp') {
			(
				document.getElementById(`text-notes-${index - 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowDown') {
			(
				document.getElementById(`text-notes-${index + 1}`) as HTMLInputElement
			)?.select();
		}

		if (e.key === 'ArrowLeft') {
			(document.getElementById(`time-${index}`) as HTMLInputElement)?.select();
		}

		if (e.key === 'ArrowRight') {
			(document.getElementById(`price-${index}`) as HTMLInputElement)?.select();
		}
	};

	const handleKeyUpNotes = (index: number) => (e: KeyboardEvent) => {
		onChangeBidRows(
			bidRows.map((bidRow, i) =>
				i === index
					? {
							...bidRow,
							notes: (e.target as unknown as { value: string })?.value,
						}
					: bidRow
			)
		);
	};

	let error = $derived(
		isManualPaddleNumberValid(
			bidRows,
			bidderInfoRows,
			auctionClients,
			winningPaddleNumberValue
		)
	);

	let isSold = $derived(biddingChartDetailsForm.activity_status === 'SOLD');
</script>

<table class="h-full w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix} class="[&>tr]:border-x-0">
		{#each headers as header, i}
			{#if i === headers.length - 1}
				<TableHeader dataCy={dataCyPrefix} width={actionCellWidth} />
			{:else}
				<TableHeader
					dataCy={dataCyPrefix}
					width={getCellWidth(i, actionCellWidth, headers)}
				>
					{header}
				</TableHeader>
			{/if}
		{/each}
	</TableHeaderRow>

	<TableBody dataCy={dataCyPrefix}>
		{#each bidRows as bidRow, i}
			{@const isRowHidden =
				bidRow.isDeleted || (bidRow.isHammerBid && !hasRecordedBids)}

			{#if !isRowHidden}
				{@const bidIndex =
					i - bidRows.slice(0, i).filter((bid) => bid?.isDeleted).length + 1}
				<TableRow
					index={0}
					dataCy={dataCyPrefix}
					class={classNames('border-x-0', {
						'bg-green-100':
							bidRow.isHammerBid &&
							biddingChartDetailsForm?.activity_status === 'SOLD',
						'bg-red-100':
							bidRow.isHammerBid &&
							biddingChartDetailsForm?.activity_status === 'BOUGHT_IN',
					})}
				>
					<TableCell
						dataCy={dataCyPrefix}
						width={getCellWidth(i, actionCellWidth, headers)}
						content={`Bid ${bidIndex}`}
					>
						{#snippet custom()}
							<div class="flex items-center gap-2">
								{#if bidRow.isHammerBid}
									{#if biddingChartDetailsForm?.activity_status === 'BOUGHT_IN'}
										<Txt
											variant="label3"
											class="rounded-2xl bg-[rgba(0,0,0,0.3)] px-2 py-1 text-white"
											>BI</Txt
										>
									{/if}
									<HammerIcon />
								{:else}
									<Txt variant="label3">
										Bid {bidIndex}
									</Txt>
								{/if}
							</div>
						{/snippet}
					</TableCell>
					<TableCell
						class={classNames('p-0')}
						dataCy={dataCyPrefix}
						width={bidRow.isHammerBid
							? `calc(100% - ((100% - 7rem) / 8))`
							: getCellWidth(i, actionCellWidth, headers)}
					>
						{#snippet custom()}
							<input
								class={classNames(
									'h-full w-full pl-3 outline-none',
									{
										'bg-green-100':
											bidRow.isHammerBid &&
											biddingChartDetailsForm?.activity_status === 'SOLD',
										'bg-red-100':
											bidRow.isHammerBid &&
											biddingChartDetailsForm?.activity_status === 'BOUGHT_IN',
									},
									TxtVariantsMap.body2
								)}
								id={`time-${i}`}
								value={bidRow.formattedTime}
								onkeyup={handleKeyUpFormattedTime(i)}
								onkeydown={handleKeyDownFormattedTime(i)}
							/>
						{/snippet}
					</TableCell>
					{#if bidRow.isHammerBid}
						<TableCell
							class="relative p-0"
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
						>
							{#snippet custom()}
								<div
									class={classNames(
										'absolute left-0 top-0 flex h-full w-[calc(500%+7rem)] items-center gap-4 pl-3',
										isSold ? 'bg-green-100' : 'bg-red-100'
									)}
								>
									{#if isSold}
										<Txt variant="label3" class="min-w-[169px]"
											>Winning paddle number:</Txt
										>
										<input
											class={classNames(
												'w-[4rem] py-1 pl-3 outline-none',
												TxtVariantsMap.body3
											)}
											placeholder="eg. 230"
											bind:value={winningPaddleNumberValue}
											onkeyup={handleKeyUpWinningPaddleNumber}
										/>{#if error.message}
											<Txt variant="body3" class="text-red-500"
												>{error.message}</Txt
											>
										{/if}
									{/if}
								</div>
							{/snippet}
						</TableCell>
					{:else}
						{@const position = getPositionFromNotes(bidRow.notes)}
						{@const bidderEntityName =
							bidderInfoRows[position]?.bidderEntity?.name}

						{@const auctionClient = bidderInfoRows[position]?.auctionClient}
						{@const auctionClientName = auctionClient?.entityName}

						<TableCell
							class="p-0"
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
						>
							{#snippet custom()}
								<input
									class={classNames(
										'h-full w-full pl-3 outline-none',
										{ 'bg-green-100': bidRow.isHammerBid },
										TxtVariantsMap.body2
									)}
									value={bidRow.notes}
									id={`text-notes-${i}`}
									onkeyup={handleKeyUpNotes(i)}
									onkeydown={handleKeyDownNotes(i)}
								/>
							{/snippet}
						</TableCell>
						<TableCell
							class="p-0"
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
						>
							{#snippet custom()}
								<input
									id={`price-${i}`}
									class={classNames(
										'h-full w-full pl-3 outline-none',
										{ 'bg-green-100': bidRow.isHammerBid },
										{
											'bg-red-100':
												bidRow.amount.amount === '' ||
												!isPriceValid(bidRow.amount.amount),
										},
										TxtVariantsMap.body2
									)}
									onkeydown={handleKeyDownPrice(i)}
									onkeyup={handleKeyUpPrice(i)}
									value={bidRow.amount.amount}
								/>
							{/snippet}
						</TableCell>
						<TableCell
							class={classNames('px-3', {
								'bg-red-100': position && !getBidderInfo(position),
							})}
							dataCy={dataCyPrefix}
							width={getCellWidth(i, actionCellWidth, headers)}
						>
							{position}
						</TableCell>
						{#if bidderEntityName}
							<TableCell
								class="px-3"
								dataCy={dataCyPrefix}
								width={getCellWidth(i, actionCellWidth, headers)}
								content={bidderEntityName}
							>
								{bidderEntityName}
							</TableCell>
						{:else}
							<TableCell
								class="px-3"
								dataCy={dataCyPrefix}
								width={getCellWidth(i, actionCellWidth, headers)}
							>
								{#snippet custom()}
									<div>
										<InfoTooltip
											class="translate-y-[2px]"
											dataCy={dataCyPrefix}
											content="The bidder entity can be added in the Bidder client info table"
										/>
									</div>
								{/snippet}
							</TableCell>
						{/if}
						{#if auctionClient}
							{#if auctionClientName}
								<TableCell
									class="px-3"
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
									content={auctionClientName}
								>
									{auctionClientName}
								</TableCell>
							{:else}
								<TableCell
									class="px-3"
									dataCy={dataCyPrefix}
									width={getCellWidth(i, actionCellWidth, headers)}
								>
									{#snippet custom()}
										<div class="flex items-center gap-2">
											<Txt variant="body2">N/A</Txt>
											<InfoTooltip
												class="translate-y-[-1px]"
												dataCy={dataCyPrefix}
												content="The auction client entity name can be added in the Bidder client info table"
											/>
										</div>
									{/snippet}
								</TableCell>
							{/if}
						{:else}
							<TableCell
								class="px-3"
								dataCy={dataCyPrefix}
								width={getCellWidth(i, actionCellWidth, headers)}
							>
								{#snippet custom()}
									<div>
										<InfoTooltip
											class="translate-y-[2px]"
											dataCy={dataCyPrefix}
											content="The auction client can be added in the Bidder client info table"
										/>
									</div>
								{/snippet}
							</TableCell>
						{/if}
						<TableActionCell dataCy={dataCyPrefix} width={actionCellWidth}>
							{#if !bidRow.isHammerBid}
								<div class="relative">
									<button
										tabindex="-1"
										onclick={() => {
											indexOpenPopover = i;
										}}
										class="flex h-6 w-6 items-center justify-center rounded border"
									>
										<div class="mb-2">...</div>
									</button>
									{#if indexOpenPopover === i}
										<div
											class="absolute right-0 z-10 mt-2 w-40 rounded-md bg-white shadow-lg"
											use:clickOutside
											onclick_outside={() => {
												indexOpenPopover = -1;
											}}
										>
											{#if i !== 0}
												<button
													class="flex w-full justify-between px-4 py-2"
													onclick={handleAddBidAbove(i)}
												>
													<Txt variant="body3">Add row above</Txt>
													<PlusIcon class="h-4 w-4" />
												</button>
												<div class="border-b border-gray-200"></div>
											{/if}

											<button
												class="flex w-full justify-between px-4 py-2"
												onclick={handleAddBidBelow(i)}
											>
												<Txt variant="body3">Add row below</Txt>
												<PlusIcon class="h-4 w-4" />
											</button>
											<div class="border-b border-gray-200"></div>

											<button
												class="flex w-full justify-between px-4 py-2"
												onclick={handleDeleteBid(i)}
											>
												<Txt variant="body3">Remove this row</Txt>
												<BinIcon class="h-4 w-4" />
											</button>
										</div>
									{/if}
								</div>
							{/if}
						</TableActionCell>
					{/if}
				</TableRow>
			{/if}
		{/each}
	</TableBody>
</table>
