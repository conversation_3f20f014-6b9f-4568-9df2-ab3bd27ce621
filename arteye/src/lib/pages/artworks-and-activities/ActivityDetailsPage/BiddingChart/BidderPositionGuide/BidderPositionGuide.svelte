<script lang="ts">
	import { BiddingPosition } from '../types';
	import { Txt } from '$global/components/Txt';

	// Create arrays of enum values for each section
	const centerPositions = [BiddingPosition.C, BiddingPosition.CH];
	const leftPositions = [
		BiddingPosition.P1,
		BiddingPosition.P2,
		BiddingPosition.P3,
		BiddingPosition.P4,
	];
	const rightPositions = [
		BiddingPosition.P5,
		BiddingPosition.P6,
		BiddingPosition.P7,
		BiddingPosition.P8,
	];
	const topPositions = [
		BiddingPosition.R1,
		BiddingPosition.R2,
		BiddingPosition.R3,
		BiddingPosition.R4,
		BiddingPosition.R5,
		BiddingPosition.R6,
		BiddingPosition.R7,
		BiddingPosition.R8,
	];
	const bottomPositions = [
		BiddingPosition.O1,
		BiddingPosition.O2,
		BiddingPosition.O3,
		BiddingPosition.O4,
		BiddingPosition.O5,
		BiddingPosition.O6,
		BiddingPosition.O7,
		BiddingPosition.O8,
	];
</script>

<div class="rounded border">
	<div class="grid grid-cols-4 py-8">
		<div class="col-span-4 mb-4 flex justify-center gap-2">
			{#each centerPositions as position}
				<div
					class="flex h-[44px] w-[44px] items-center justify-center rounded bg-gray-100"
				>
					<Txt>{position}</Txt>
				</div>
			{/each}
		</div>
		<div class="col-span-1 flex flex-col items-end space-y-2">
			{#each leftPositions as position}
				<div
					class="flex h-[30px] w-[30px] items-center justify-center rounded bg-gray-100"
				>
					<Txt>{position}</Txt>
				</div>
			{/each}
		</div>
		<div class="col-span-2 flex justify-center">
			<div>
				<div class="mb-4 grid grid-cols-4 gap-2">
					{#each topPositions as position}
						<div class="align-self-center col-span-1 justify-self-center">
							<div
								class="align-self-center flex h-[30px] w-[30px] items-center justify-center justify-self-center rounded bg-gray-100"
							>
								<Txt>{position}</Txt>
							</div>
						</div>
					{/each}
				</div>
				<div class="grid grid-cols-4 gap-2">
					{#each bottomPositions as position}
						<div class="col-span-1">
							<div
								class="flex h-[34px] w-[34px] items-center justify-center rounded bg-gray-100"
							>
								<Txt>{position}</Txt>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>
		<div class="col-span-1 flex flex-col items-start space-y-2">
			{#each rightPositions as position}
				<div
					class="flex h-[30px] w-[30px] items-center justify-center rounded bg-gray-100"
				>
					<Txt>{position}</Txt>
				</div>
			{/each}
		</div>
	</div>
</div>
