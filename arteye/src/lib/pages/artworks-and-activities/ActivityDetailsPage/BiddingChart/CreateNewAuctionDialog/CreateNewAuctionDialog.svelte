<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { formatAuctionClient } from '../AuctionClientAutocomplete/AuctionClientAutocomplete.svelte';
	import { EntityAutoComplete } from '../EntityAutoComplete';
	import { type BidderInfoRow } from '../types';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Input } from '$global/components/Input';
	import { InputError } from '$global/components/Input/InputError';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { gqlClient } from '$lib/gqlClient';
	import { CreateAuctionClientItemDocument } from '$lib/queries/__generated__/createAuctionClient.generated';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';
	import type { RootPageData } from '$routes/types';

	interface Props {
		changed: { biddingCharts: boolean };
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		auctionClients: null | GetAuctionClientQuery['auction_client'];
		auctionId: string;
		editAuctionBidderPosition: string | null;
		bidderPosition: string;
		showSaveBar: boolean;
		auctionClientSelectedOptions: Record<string, OptionType | null | undefined>;
	}

	let {
		changed = $bindable(),
		bidderInfoRows = $bindable(),
		auctionClients = $bindable(),
		auctionId,
		editAuctionBidderPosition = $bindable(),
		bidderPosition = $bindable(),
		showSaveBar = $bindable(),
		auctionClientSelectedOptions = $bindable(),
	}: Props = $props();

	let loading = $state(false);
	let paddleNumber = $state('');
	let entitySelectedOption: OptionType | null = $state(null);
	const entityValue = writable('');
	const dialogStores = createDialog();

	let uniqueAuctionClientsPaddleNumbers = $derived(
		(auctionClients || [])
			.map((auctionClient) => auctionClient?.paddle_number?.toUpperCase() || '')
			.filter(Boolean)
	);

	let data = $derived(getPageData<RootPageData>(page.data));

	let error = $derived(
		uniqueAuctionClientsPaddleNumbers.includes(
			paddleNumber.trim()?.toUpperCase()
		)
			? 'There is already a client in this auction with this panel number'
			: ''
	);

	$effect(() => {
		if (bidderPosition) {
			dialogStores.states.open.set(true);
		} else {
			dialogStores.states.open.set(false);
			paddleNumber = '';
			entitySelectedOption = null;
			entityValue.set('');
		}
	});

	const dataCyPrefix = `create-new-auction-client`;

	const handleClickEntityClear = () => {
		entitySelectedOption = null;
	};

	const handleClose = () => {
		bidderPosition = '';
	};

	const handleSubmit = async () => {
		loading = true;

		const createAuctionClientResponse = await gqlClient.request(
			CreateAuctionClientItemDocument,
			{
				data: {
					...(paddleNumber && { paddle_number: paddleNumber.trim() }),
					...(entitySelectedOption && {
						entity: {
							name: `${entitySelectedOption.line1}`,
							id: `${entitySelectedOption.line4}`,
						},
					}),
					auction: {
						id: auctionId,
					},
				},
			},
			getAuthorizationHeaders(data)
		);

		const newAuctionClient =
			createAuctionClientResponse?.create_auction_client_item;

		if (newAuctionClient) {
			const formattedAuctionClient = {
				...formatAuctionClient(newAuctionClient),
				onClick: () => {
					editAuctionBidderPosition = Object.values(bidderInfoRows).find(
						(bidderInfoRow) =>
							bidderInfoRow?.auctionClient?.id === newAuctionClient?.id
					)?.bidderPosition as string;
				},
			} as OptionType;

			auctionClientSelectedOptions[bidderPosition] = formattedAuctionClient;

			auctionClients = [...(auctionClients || []), newAuctionClient];

			bidderInfoRows = {
				...bidderInfoRows,
				[bidderPosition]: {
					...bidderInfoRows[bidderPosition],
					auctionClient: {
						id: `${formattedAuctionClient?.line5}`,
						entityName: formattedAuctionClient?.line8,
						entityId: formattedAuctionClient?.line4,
						paddleNumber: formattedAuctionClient?.line7,
					},
				},
			} as Record<string, BidderInfoRow>;

			showSaveBar = true;
			changed.biddingCharts = true;

			showToast({
				variant: 'success',
				message: 'Auction client has been successfully created',
			});
		}

		loading = false;
		bidderPosition = '';
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-full max-h-none w-full items-center justify-center rounded-none p-[1.5rem] pt-[1.25rem] sm:max-h-[30rem] sm:max-w-[40rem] sm:rounded sm:p-[1.5rem] sm:pt-[1rem]"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<Txt variant="h3" class="my-4 max-w-[24rem] text-center"
			>Add new auction client</Txt
		>
		<Txt variant="body2" class="mb-4 max-w-[26rem] text-center"
			>Please provide a paddle number and/or the entity corresponding to the
			auction client</Txt
		>

		<div class="mb-4 w-full">
			<Input
				name="paddle_number"
				placeholder="Enter paddle number..."
				dataCy={`${dataCyPrefix}-paddle-number`}
				bind:value={paddleNumber}
				label="Client paddle number"
			/>
		</div>

		<div class="mb-8 w-full">
			<InputLabel class="mb-2" dataCy={`${dataCyPrefix}-entity-label`}
				>Client entity</InputLabel
			>

			<EntityAutoComplete
				value={entityValue}
				excludedIds={[]}
				onClickClear={handleClickEntityClear}
				dataCy={`${dataCyPrefix}-entity`}
				bind:selectedOption={entitySelectedOption}
				class={classNames(
					'rounded border border-gray-200 hover:border-gray-700',
					{ 'py-3': !!entitySelectedOption }
				)}
			/>
		</div>

		<div class="flex w-full flex-col gap-4">
			<Button
				size="lg"
				fullWidth
				dataCy={`${dataCyPrefix}-submit`}
				onclick={handleSubmit}
				disabled={loading ||
					(!paddleNumber && !entitySelectedOption) ||
					!!error}
			>
				add new client to {bidderPosition}
			</Button>

			<Button
				fullWidth
				dataCy={`${dataCyPrefix}-cancel`}
				onclick={handleClose}
				variant="secondary"
				size="lg"
			>
				cancel
			</Button>
		</div>
		<div class="mt-2 text-center">
			<InputError dataCy={`${dataCyPrefix}-error`}>
				{error}
			</InputError>
		</div>
	</div></Dialog
>
