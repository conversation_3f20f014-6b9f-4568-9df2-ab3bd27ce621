<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { EntityAutoComplete } from '../../EntityAutoComplete';
	import { type BidderInfoRow } from '../../types';
	import { formatAuctionClient } from '../AuctionClientAutocomplete.svelte';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Input } from '$global/components/Input';
	import { InputError } from '$global/components/Input/InputError';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { gqlClient } from '$lib/gqlClient';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';
	import { UpdateAuctionClientItemDocument } from '$lib/queries/__generated__/updateAuctionClient.generated';
	import type { RootPageData } from '$routes/types';

	interface Props {
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		auctionClients: null | GetAuctionClientQuery['auction_client'];
		auctionId: string;
		editAuctionBidderPosition: string;
		selectedOption: OptionType | null | undefined;
	}

	let {
		editAuctionBidderPosition = $bindable(),
		bidderInfoRows = $bindable(),
		auctionClients = $bindable(),
		auctionId,
		selectedOption = $bindable(),
	}: Props = $props();

	const dialogStores = createDialog();

	let loading = $state(false);
	let deleting = $state(false);

	let originalValues = $state({
		paddleNumber: selectedOption?.line7 || '',
		entity: selectedOption?.line8 || undefined,
	});

	let paddleNumber = $state(selectedOption?.line7 || '');
	let entitySelectedOption: OptionType | null = $state(
		selectedOption?.line8
			? {
					line1: `${selectedOption?.line8}`,
					line2: selectedOption?.line2,
					line3: `${selectedOption?.line3}`,
					line4: `${selectedOption?.line4}`,
				}
			: null
	);

	const entityValue = writable('');

	let uniqueAuctionClientsPaddleNumbers = $derived(
		(auctionClients || [])
			.filter((auctionClient) => auctionClient?.id !== selectedOption?.line5)
			.map((auctionClient) => auctionClient?.paddle_number?.toUpperCase() || '')
			.filter(Boolean)
	);

	let data = $derived(getPageData<RootPageData>(page.data));

	let error = $derived(
		uniqueAuctionClientsPaddleNumbers.includes(
			paddleNumber.trim()?.toUpperCase()
		)
			? 'There is already a client in this auction with this panel number'
			: ''
	);

	$effect(() => {
		if (editAuctionBidderPosition) {
			dialogStores.states.open.set(true);
		} else {
			dialogStores.states.open.set(false);
		}
	});

	const dataCyPrefix = `edit-auction-client`;

	const handleClickEntityClear = () => {
		entitySelectedOption = null;
	};

	const handleClose = () => {
		editAuctionBidderPosition = '';
	};

	const handleClickDelete = async () => {
		deleting = true;

		await gqlClient.request(
			UpdateAuctionClientItemDocument,
			{
				id: `${selectedOption?.line5}`,
				data: {
					status: {
						name: 'Archived',
						key: Status_Enum.Archived,
					},
					auction: {
						id: auctionId,
					},
				},
			},
			getAuthorizationHeaders(data)
		);

		selectedOption = null;

		auctionClients = (auctionClients || [])
			?.map((auctionClient) =>
				auctionClient?.id === `${selectedOption?.line5}` ? null : auctionClient
			)
			.filter(Boolean) as typeof auctionClients;

		bidderInfoRows = {
			...bidderInfoRows,
			[editAuctionBidderPosition]: {
				...bidderInfoRows[editAuctionBidderPosition],
				auctionClient: null,
			},
		} as Record<string, BidderInfoRow>;

		showToast({
			variant: 'success',
			message: 'Auction client has been successfully deleted',
		});

		deleting = false;
		editAuctionBidderPosition = '';
	};

	const handleSubmit = async () => {
		loading = true;

		const updateAuctionClientResponse = await gqlClient.request(
			UpdateAuctionClientItemDocument,
			{
				id: `${selectedOption?.line5}`,
				data: {
					paddle_number: paddleNumber ? paddleNumber.trim() : null,
					entity: entitySelectedOption
						? {
								name: `${entitySelectedOption.line1}`,
								id: `${entitySelectedOption.line4}`,
							}
						: null,
					auction: {
						id: auctionId,
					},
				},
			},
			getAuthorizationHeaders(data)
		);

		const updatedAuctionClient =
			updateAuctionClientResponse?.update_auction_client_item;

		if (updatedAuctionClient) {
			const formattedUpdatedAuctionClient = {
				...formatAuctionClient(updatedAuctionClient),
				onClick: () => {
					editAuctionBidderPosition = Object.values(bidderInfoRows).find(
						(bidderInfoRow) =>
							bidderInfoRow?.auctionClient?.id === updatedAuctionClient?.id
					)?.bidderPosition as string;
				},
			} as OptionType;

			selectedOption = formattedUpdatedAuctionClient;

			auctionClients = (auctionClients || [])?.map((auctionClient) =>
				auctionClient?.id === `${selectedOption?.line5}`
					? updatedAuctionClient
					: auctionClient
			);

			bidderInfoRows = {
				...bidderInfoRows,
				[editAuctionBidderPosition]: {
					...bidderInfoRows[editAuctionBidderPosition],
					auctionClient: {
						id: `${formattedUpdatedAuctionClient?.line5}`,
						entityName: formattedUpdatedAuctionClient?.line8,
						entityId: formattedUpdatedAuctionClient?.line4,
						paddleNumber: formattedUpdatedAuctionClient?.line7,
					},
				},
			} as Record<string, BidderInfoRow>;

			showToast({
				variant: 'success',
				message: 'Auction client has been successfully edited',
			});
		}

		loading = false;
		editAuctionBidderPosition = '';
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-full max-h-none w-full items-center justify-center rounded-none p-[1.5rem] pt-[1.25rem] sm:max-h-[38rem] sm:max-w-[40rem] sm:rounded sm:p-[1.5rem] sm:pt-[1rem]"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<Txt variant="h3" class="my-4 max-w-[24rem] text-center"
			>Edit auction client</Txt
		>
		<Txt variant="body2" class="mb-4 max-w-[26rem] text-center"
			>Please provide a paddle number and/or the entity corresponding to the
			auction client</Txt
		>

		<div class="mb-4 w-full">
			<Input
				name="paddle_number"
				placeholder="Enter paddle number..."
				dataCy={`${dataCyPrefix}-paddle-number`}
				bind:value={paddleNumber}
				label="Client paddle number"
			/>
		</div>

		<div class="mb-8 w-full">
			<InputLabel class="mb-2" dataCy={`${dataCyPrefix}-entity-label`}
				>Client entity</InputLabel
			>

			<EntityAutoComplete
				value={entityValue}
				excludedIds={[]}
				onClickClear={handleClickEntityClear}
				dataCy={`${dataCyPrefix}-entity`}
				bind:selectedOption={entitySelectedOption}
				class={classNames(
					'rounded border border-gray-200 hover:border-gray-700',
					{ 'py-3': !!entitySelectedOption }
				)}
			/>
		</div>

		<div class="flex w-full flex-col gap-4">
			<Button
				size="md"
				class="col-span-4"
				dataCy={`${dataCyPrefix}-submit`}
				onclick={handleSubmit}
				{loading}
				disabled={loading ||
					deleting ||
					(originalValues.paddleNumber === paddleNumber &&
						originalValues.entity === selectedOption?.line1) ||
					(!paddleNumber && !entitySelectedOption) ||
					!!error}
			>
				save changes to {editAuctionBidderPosition}
			</Button>

			<Button
				dataCy={`${dataCyPrefix}-cancel`}
				onclick={handleClose}
				class={classNames({
					'pointer-events-none': loading || deleting,
				})}
				variant="secondary"
				size="md"
			>
				cancel
			</Button>
		</div>
		<div class="mt-2 text-center">
			<InputError dataCy={`${dataCyPrefix}-error`}>
				{error}
			</InputError>
		</div>

		<div class="relative w-full">
			<hr class="mb-3 mt-4 w-full" />
			<Txt
				variant="body3"
				class="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-30%] bg-white px-4 font-[500] tracking-widest"
				>OR</Txt
			>
		</div>

		<div class="mt-4 grid w-full">
			<Button
				size="md"
				dataCy={`${dataCyPrefix}-delete`}
				variant="secondary"
				onclick={handleClickDelete}
				class={classNames(
					'[&>span]:flex [&>span]:items-center [&>span]:text-red-500',
					{
						'pointer-events-none': loading || deleting,
					}
				)}
				loading={deleting}
			>
				{#snippet leading()}
					{#if !deleting}
						<BinIcon class="mr-2 h-4 w-4 stroke-red-500" />
					{/if}
				{/snippet}

				delete
			</Button>
		</div>

		<Txt variant="body3" class="mt-4 max-w-[26rem] text-center text-red-500"
			>Deleting the auction client will also affect the other bids they have
			made on other auction lots.<span class="font-[500]">
				{' Use with caution, this action is irreversible.'}</span
			></Txt
		>
	</div></Dialog
>
