<script lang="ts" module>
	export const formatAuctionClient = (
		auctionClient:
			| GetAuctionClientQuery['auction_client'][number]
			| undefined
			| null
	) => {
		if (!auctionClient) {
			return null;
		}

		const url = `${Routes.Auctions}/${auctionClient?.auction?.id}`;

		return {
			line1: `${auctionClient?.entity?.name || 'N/A'}`,
			line2: url,
			line3: auctionClient?.entity?.type?.key,
			line4: auctionClient?.entity?.id,
			line5: auctionClient?.id,
			line6: auctionClient?.paddle_number
				? `Paddle ${auctionClient?.paddle_number}`
				: 'No paddle',
			line7: auctionClient?.paddle_number,
			line8: auctionClient?.entity?.name,
			Icon: PencilIcon,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { type Writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { CannotFindAuctionClient } from './CannotFindAuctionClient';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { PencilIcon } from '$global/assets/icons/PencilIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import {
		GetAuctionClientDocument,
		type GetAuctionClientQuery,
		type GetAuctionClientQueryVariables,
	} from '$lib/queries/__generated__/getAuctionClient.generated';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	interface Props {
		bidderPosition: string;
		createNewAuctionBidderPosition: string | null;
		editAuctionBidderPosition: string | null;
		auctionId: string;
		onClickClear: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption: OptionType | null | undefined;
		value?: Writable<string>;
		excludedIds: string[];
		class?: string;
		onChange:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		auctionId,
		onClickClear,
		placeholder,
		dataCy,
		selectedOption = $bindable(null),
		value,
		excludedIds,
		bidderPosition,
		createNewAuctionBidderPosition = $bindable(),
		editAuctionBidderPosition = $bindable(),
		onChange = undefined,
		...rest
	}: Props = $props();
	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));

	const getVariables = (value: string): GetAuctionClientQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: value
				? {
						_and: [
							{
								_or: [
									{ entity: { name: { _icontains: value } } },
									{ paddle_number: { _icontains: value } },
									...(isValidUUID(value)
										? [
												{ entity: { organisation: { id: { _eq: value } } } },
												{ entity: { person: { id: { _eq: value } } } },
												{ entity: { artist: { id: { _eq: value } } } },
											]
										: []),
								],
							},
							{ status: { key: { _neq: Status_Enum.Archived } } },
							{ auction: { id: { _eq: auctionId } } },
							...(excludedIds.length ? [{ id: { _nin: excludedIds } }] : []),
						],
					}
				: {
						_and: [
							{ status: { key: { _neq: Status_Enum.Archived } } },
							{ auction: { id: { _eq: auctionId } } },
							...(excludedIds.length ? [{ id: { _nin: excludedIds } }] : []),
						],
					},
		};
	};

	const getOptions = (queryData: GetAuctionClientQuery | undefined) => {
		return [
			...(queryData?.auction_client || []).map((auctionClient) => ({
				...formatAuctionClient(auctionClient),
				onClick: () => {
					editAuctionBidderPosition = bidderPosition;
				},
			})),
		] as OptionType[];
	};
</script>

<div
	class={twMerge(
		classNames('relative justify-between bg-white', {
			'flex items-center gap-2 rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
			block: !selectedOption,
		}),
		rest.class
	)}
>
	<div
		class={classNames('w-full', {
			'max-w-[calc(100%-20px)]': !!selectedOption,
		})}
	>
		<QueryAutocomplete
			OptionComponent={LinkOption}
			SelectedOptionComponent={LinkOption}
			name="entity"
			dataCy={`${dataCy}-entity`}
			{placeholder}
			emptyValueResponse={{ auction_client: [] }}
			showResultsWhenEmpty={false}
			graphQlClient={gqlClient}
			classes={{
				listWithOptions: 'pb-[5rem] [&_button>svg]:hidden',
				longList: '!max-h-[232px] !min-h-[232px]',
				input: 'border-none bg-transparent',
				option: {
					line3: 'hidden',
					line4: 'hidden',
					line5: 'hidden',
					line6: 'text-gray-500 text-[0.75rem]',
					line7: 'hidden',
					line8: 'hidden',
				},
				selectedOption: {
					button: 'max-w-full',
					line3: 'hidden',
					line4: 'hidden',
					line5: 'hidden',
					line6: 'text-gray-500 text-[0.75rem]',
					line7: 'hidden',
					line8: 'hidden',
				},
			}}
			requestHeaders={getAuthorizationHeaders(data)}
			{getOptions}
			{getVariables}
			document={GetAuctionClientDocument}
			{value}
			bind:selectedOption
			{onChange}
		>
			{#snippet list()}
				<div
					class="absolute bottom-0 left-0 flex h-[5rem] w-full border-t border-gray-200 bg-white p-4 empty:hidden"
				>
					{#if createNewAuctionBidderPosition !== null}
						<CannotFindAuctionClient
							bind:createNewAuctionBidderPosition
							{bidderPosition}
						/>
					{/if}
				</div>
			{/snippet}

			{#snippet noResults()}
				<div class="flex flex-col items-center">
					<NoResults
						class="mb-2 pl-2"
						dataCy={`${dataCy}-gallery-offerings-autocomplete`}
						>No results.{' '}</NoResults
					>
					{#if createNewAuctionBidderPosition !== null}
						<CannotFindAuctionClient
							class="pl-4"
							bind:createNewAuctionBidderPosition
							{bidderPosition}
						/>
					{/if}
				</div>
			{/snippet}
		</QueryAutocomplete>
	</div>

	{#if !!selectedOption}
		<button class="z-10" onclick={onClickClear}>
			<CrossIcon class="max-h-4 min-h-4 min-w-4 max-w-4" />
		</button>
	{/if}
</div>
