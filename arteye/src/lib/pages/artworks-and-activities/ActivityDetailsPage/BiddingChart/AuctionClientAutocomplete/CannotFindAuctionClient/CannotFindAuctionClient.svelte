<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { Txt } from '$global/components/Txt';

	interface Props {
		createNewAuctionBidderPosition: string;
		bidderPosition: string;
		class?: string;
	}

	let {
		createNewAuctionBidderPosition = $bindable(),
		bidderPosition,
		...rest
	}: Props = $props();

	const handleClickCreateNew = () => {
		createNewAuctionBidderPosition = bidderPosition;
	};
</script>

<div class={twMerge('flex items-center gap-1', rest.class)}>
	<Txt variant="body3" component="span" class="text-gray-500"
		>Cannot find the auction client?{' '}<button
			onclick={handleClickCreateNew}
			class="inline text-blue-500 underline">Create new</button
		></Txt
	>
</div>
