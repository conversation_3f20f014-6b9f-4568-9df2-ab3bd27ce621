<script lang="ts">
	import classNames from 'classnames';
	import type { BiddingPosition } from '../../../types';
	import type { BidItem } from '../../types';
	import MinusIcon from '$global/assets/icons/MinusIcon/MinusIcon.svelte';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { ApexCircle } from '$global/components/Chart/shapes/ApexCircle';
	import { ApexCross } from '$global/components/Chart/shapes/ApexCross';
	import { ApexDiamond } from '$global/components/Chart/shapes/ApexDiamond';
	import { ApexLine } from '$global/components/Chart/shapes/ApexLine';
	import { ApexPlus } from '$global/components/Chart/shapes/ApexPlus';
	import { ApexSparkle } from '$global/components/Chart/shapes/ApexSparkle';
	import { ApexSquare } from '$global/components/Chart/shapes/ApexSquare';
	import { ApexStar } from '$global/components/Chart/shapes/ApexStar';
	import { ApexTriangle } from '$global/components/Chart/shapes/ApexTriangle';
	import { ApexMarkerShape } from '$global/components/Chart/types';
	import { Txt } from '$global/components/Txt';

	interface Props {
		bids: BidItem[];
	}

	let { bids }: Props = $props();

	const dataCy = 'bidding-progress-chart-legend';

	let expanded = $state(true);

	const getUniqueBiddersMap = () => {
		return bids.reduce<Record<BiddingPosition, BidItem>>(
			(acc, bid) => {
				return {
					...acc,
					[bid.bidderPosition]: bid,
				};
			},
			{} as Record<BiddingPosition, BidItem>
		);
	};

	let uniqueBiddersMap = $derived(getUniqueBiddersMap());

	const handleToggleExpanded = () => {
		expanded = !expanded;
	};
</script>

<div
	class={classNames(
		'bottom-[70px] right-[30px]',
		'absolute z-10 border border-gray-200 bg-white p-2 rounded-sm md:min-w-[150px]'
	)}
>
	<div class="flex items-center gap-2 justify-between">
		<Txt
			variant="label3"
			class={classNames({
				'hidden md:block': !expanded,
			})}>Legend</Txt
		>

		<Button
			size="sm"
			dataCy={`${dataCy}-expand`}
			class="h-[18px] w-[18px] px-0"
			variant="secondary"
			onclick={handleToggleExpanded}
		>
			{#if expanded}
				<MinusIcon class="h-[14px] w-[14px]" />
			{:else}
				<PlusIcon class="h-[14px] w-[14px]" />
			{/if}
		</Button>
	</div>

	<div
		class={classNames('flex flex-col gap-1 mt-4', {
			hidden: !expanded,
		})}
	>
		{#each Object.values(uniqueBiddersMap) as bidder}
			<div class="flex items-center gap-2">
				{#if bidder.shape === ApexMarkerShape.Circle}
					<ApexCircle class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Cross}
					<ApexCross class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Diamond}
					<ApexDiamond class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Line}
					<ApexLine class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Plus}
					<ApexPlus class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Sparkle}
					<ApexSparkle class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Square}
					<ApexSquare class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Star}
					<ApexStar class="h-[14px] w-[14px]" fill={bidder.color} />
				{:else if bidder.shape === ApexMarkerShape.Triangle}
					<ApexTriangle class="h-[14px] w-[14px]" fill={bidder.color} />
				{/if}

				{#if bidder.bidderName}
					<Txt variant="label4">{bidder.bidderPosition} {bidder.bidderName}</Txt
					>
				{:else if bidder.notes}
					<Txt variant="label4">{bidder.notes}</Txt>
				{:else}
					<Txt variant="label4">{bidder.bidderPosition}</Txt>
				{/if}
			</div>
		{/each}

		<div class="flex items-center gap-2">
			<div
				class="h-[1px] w-[30px] border-b border-[#00c951] border-dotted"
			></div>
			<Txt variant="label4">High estimate</Txt>
		</div>

		<div class="flex items-center gap-2">
			<div
				class="h-[1px] w-[30px] border-b border-[#fb2c36] border-dotted"
			></div>
			<Txt variant="label4">Low estimate</Txt>
		</div>
	</div>
</div>
