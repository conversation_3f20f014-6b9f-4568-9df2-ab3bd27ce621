<script lang="ts">
	import html2canvas from 'html2canvas';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Button } from '$global/components/Button';

	interface Props {
		auctionLotId: string;
		downloadContainer: HTMLDivElement | null;
	}

	let { auctionLotId, downloadContainer }: Props = $props();

	async function downloadAsImageHTML2Canvas() {
		if (!downloadContainer) return;

		try {
			const canvas = await html2canvas(downloadContainer, {
				backgroundColor: '#ffffff',
				scale: 2,
				useCORS: true,
				allowTaint: true,
			});

			const link = document.createElement('a');
			link.download = `bidding-chart-${auctionLotId}.png`;
			link.href = canvas.toDataURL('image/png');
			link.click();
		} catch (error) {
			console.error('Error capturing div:', error);
		}
	}

	const handleClick = async () => {
		downloadAsImageHTML2Canvas();
	};
</script>

<Button
	dataCy="download-bidding-chart"
	variant="secondary"
	size="sm"
	onclick={handleClick}
>
	{#snippet leading()}
		<DownloadIcon class="h-3 w-3" />
	{/snippet}
	Download image
</Button>
