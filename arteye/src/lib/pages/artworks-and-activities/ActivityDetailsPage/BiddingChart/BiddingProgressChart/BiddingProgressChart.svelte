<script lang="ts">
	import type { ApexOptions } from 'apexcharts';
	import type ApexCharts from 'apexcharts';
	import { HAMMER_BID_ID } from '../../utils/formatBiddingChartDetailsForm/formatBiddingChartDetailsForm';
	import { BIDDING_CHART_STYLE_MAP } from '../constants';
	import { BidChartColor } from '../types';
	import type { BiddingPosition, BidRow, BidderInfoRow } from '../types';
	import { getPositionFromNotes } from '../utils/getPositionFromNotes/getPositionFromNotes';
	import { BiddingProgressChartContents } from './BiddingProgressChartContents';
	import type { BidItem } from './types';
	import { ApexMarkerShape } from '$global/components/Chart/types';
	import { InputLabel } from '$global/components/InputLabel';
	import { abbreviateNumber } from '$global/utils/abbreviateNumber/abbreviateNumber';
	import { formatCurrency } from '$global/utils/formatCurrency/formatCurrency';
	import { getCurrencySymbol } from '$global/utils/getCurrencySymbol/getCurrencySymbol';

	interface Props {
		isSoldAuction: boolean;
		bidRows: BidRow[];
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		auctionLotDetails: {
			auctionLotId: string;
			lowEstimate: number;
			highEstimate: number;
			currency: string;
			irrevocableBid: boolean;
			gauranteedLot: boolean;
			gauranteedAmount: number;
		};
	}

	let { bidRows, bidderInfoRows, auctionLotDetails, isSoldAuction }: Props =
		$props();

	let chart: ApexCharts | undefined = $state();

	const getSortedBidRows = () =>
		bidRows
			.filter((bid) => !bid.isDeleted)
			.map((bid) => ({
				...bid,
				bidderPosition: getPositionFromNotes(bid.notes) as BiddingPosition,
				seconds: bid.formattedTime
					.split(':')
					.reduce(
						(acc, val, i) => acc + parseInt(val) * Math.pow(60, 2 - i),
						0
					),
			}))
			.sort((a, b) => {
				const timeA = a.seconds;
				const timeB = b.seconds;

				return timeA - timeB;
			});

	let bids = $derived(
		(() => {
			const sortedBidRows = getSortedBidRows();

			return sortedBidRows
				.map((bid, index) => ({
					...bid,
					isWinningBid: isSoldAuction
						? sortedBidRows[index + 1]?.isHammerBid
						: false,
				}))
				.filter((bid) => bid.id !== HAMMER_BID_ID)
				.map<BidItem>((bid, index) => {
					const bidderPosition = bid.bidderPosition;
					``;

					const bidderInfo = bidderInfoRows[bidderPosition];

					const paddleNumber = bidderInfo?.auctionClient?.paddleNumber || '';
					const bidderName = bidderInfo?.bidderEntity?.name || '';

					const bidAmount = parseFloat(bid.amount.amount || '0');

					return {
						x: bid.seconds,
						y: bidAmount,
						bidderPosition,
						paddleNumber,
						bidderName,
						isWinningBid: bid.isWinningBid,
						seconds: bid.seconds,
						bidIndex: index + 1,
						shape:
							BIDDING_CHART_STYLE_MAP[bidderPosition]?.marker ||
							ApexMarkerShape.Circle,
						color:
							BIDDING_CHART_STYLE_MAP[bidderPosition]?.color ||
							BidChartColor.Emerald,
						notes: bid.notes,
					};
				});
		})()
	);

	const getCurrencyValue = ({
		currencyCode,
		amount,
	}: {
		currencyCode: string;
		amount: number;
	}) => {
		const currencySymbol = getCurrencySymbol({ currencyCode, locale: 'en-US' });

		return `${currencySymbol}${abbreviateNumber(amount)}`;
	};

	const getLastBidMap = (bids: BidItem[]) =>
		bids.reduce<Record<BiddingPosition, number>>(
			(acc, bid) => {
				return {
					...acc,
					[bid.bidderPosition]: bid.seconds,
				};
			},
			{} as Record<BiddingPosition, number>
		);

	const getPointsAnnotations = () => {
		const annotations: NonNullable<ApexOptions['annotations']>['points'] = [];

		bids.forEach((bid, index) => {
			const isFirstBid = index === 0;
			const isLastBid = index === bids.length - 1;

			if (isFirstBid) {
				annotations.push({
					x: bid.seconds,
					y: bid.y,
					marker: {
						size: 0,
					},
					label: {
						borderColor: bid.color,
						orientation: 'horizontal',
						text: getCurrencyValue({
							amount: Number(bid.y),
							currencyCode: auctionLotDetails?.currency,
						}),
						offsetX: 40,
						offsetY: 11,
						style: {
							color: bid.color,
						},
					},
				});
			}

			if (isLastBid) {
				annotations.push({
					x: bid.seconds,
					y: bid.y,
					marker: {
						size: 0,
					},
					label: {
						borderColor: bid.color,
						orientation: 'horizontal',
						text: getCurrencyValue({
							amount: Number(bid.y),
							currencyCode: auctionLotDetails?.currency,
						}),
						offsetX: -40,
						offsetY: 10,
						style: {
							color: bid.color,
						},
					},
				});
			}
		});

		console.log(annotations, '<-- annotations');

		return annotations;
	};

	const getXaxisAnnotations = () => {
		const annotations: NonNullable<ApexOptions['annotations']>['xaxis'] = [];

		const bidders = new Set(bids.map((bid) => bid.bidderPosition));

		const lastBidMap = getLastBidMap(bids);

		bids.forEach((bid, index) => {
			if (bid.seconds === lastBidMap[bid.bidderPosition as BiddingPosition]) {
				bidders.delete(bid.bidderPosition);

				const nextBid = bids[index + 1];

				if (bidders.size > 1 && nextBid && !bid.isWinningBid) {
					annotations.push({
						x: nextBid.seconds,
						borderColor: '#3f9d7d',
						label: {
							borderColor: '#3f9d7d',
							orientation: 'horizontal',
							text: `${bidders.size} bidders`,
							offsetX: -25,
							offsetY: -16,
							style: {
								color: '#3f9d7d',
							},
						},
					});
				}

				if (bid.isWinningBid) {
					annotations.push({
						x: bid.seconds,
						borderColor: '#3f9d7d',
						label: {
							borderColor: '#3f9d7d',
							orientation: 'horizontal',
							text: `Paddle ${bid.paddleNumber}`,
							offsetX: -35,
							offsetY: -16,
							style: {
								color: '#3f9d7d',
							},
						},
					});
				}
			}
		});

		if (!isSoldAuction) {
			const boughtInBid = getBoughtInBid();

			annotations.push({
				x: boughtInBid.seconds,
				borderColor: '#FF0000',
				label: {
					borderColor: '#FF0000',
					orientation: 'horizontal',
					text: `Bought In`,
					offsetX: -16,
				},
			});
		}

		return annotations;
	};

	const getBoughtInBid = () => {
		const hammerBid = getSortedBidRows().find(
			(bid) => bid.id === HAMMER_BID_ID
		);

		const lastChartBid = bids[bids.length - 1];

		return {
			x: hammerBid?.seconds,
			y: lastChartBid.y,
			bidderPosition: '',
			paddleNumber: '',
			bidderName: '',
			isWinningBid: true,
			seconds: hammerBid?.seconds,
			bidIndex: bids.length + 1,
		};
	};

	const getHighEstimateLabel = () => {
		const highEstimate = auctionLotDetails?.highEstimate;

		if (highEstimate === 0) {
			return '';
		}

		return getCurrencyValue({
			currencyCode: auctionLotDetails?.currency,
			amount: highEstimate,
		});
	};

	const getLowEstimateLabel = () => {
		if (auctionLotDetails?.irrevocableBid) {
			return 'Irrevocable bid';
		}

		if (auctionLotDetails?.gauranteedLot) {
			return 'Guaranteed';
		}

		const lowEstimate = auctionLotDetails?.lowEstimate;

		if (lowEstimate === 0) {
			return '';
		}

		return getCurrencyValue({
			currencyCode: auctionLotDetails?.currency,
			amount: lowEstimate,
		});
	};

	const getLowEstimateAmount = () => {
		const firstLastBidHalfwayAmount = (bids[0].y + bids[bids.length - 1].y) / 2;

		if (auctionLotDetails?.irrevocableBid) {
			return auctionLotDetails?.lowEstimate || firstLastBidHalfwayAmount;
		}

		if (auctionLotDetails?.gauranteedLot) {
			return (
				auctionLotDetails?.gauranteedAmount ||
				auctionLotDetails?.lowEstimate ||
				firstLastBidHalfwayAmount
			);
		}

		return auctionLotDetails?.lowEstimate || undefined;
	};

	const getLowEstimateOffsetX = () => {
		const lowEstimate = getLowEstimateAmount();

		if (auctionLotDetails?.irrevocableBid) {
			return 80;
		}

		if (auctionLotDetails?.gauranteedLot) {
			return 66;
		}

		if (lowEstimate === undefined) {
			return 0;
		}

		return 45;
	};

	const getHighEstimateLineOffset = (highEstimate: number) => {
		return highEstimate - highEstimate * 0.001;
	};

	const createBiddingChartData = () => {
		const lowEstimate = auctionLotDetails?.lowEstimate;
		const highEstimate = auctionLotDetails?.highEstimate;

		const chartOptions: ApexOptions = {
			chart: {
				type: 'line',
				height: '100%',
				width: '100%',
				toolbar: {
					show: false,
				},
				zoom: {
					enabled: false,
				},
			},

			stroke: {
				colors: ['#dff2fe'],
				curve: 'monotoneCubic',
				width: 2,
				show: true,
			},
			markers: {
				hover: {
					size: 6,
				},
				discrete: [
					...bids.map((bidder, index) => ({
						seriesIndex: 0,
						dataPointIndex: index,
						shape: bidder.shape,
						fillColor: bidder.color,
						strokeColor: bidder.color,
						size: 5,
					})),
					...(isSoldAuction
						? []
						: [
								{
									seriesIndex: 1,
									dataPointIndex: 0,
									shape: bids[bids.length - 1].shape,
									fillColor: bids[bids.length - 1].color,
									strokeColor: bids[bids.length - 1].color,
									size: 5,
								},
							]),
				],
			},
			series: [
				{
					name: `Bid amount`,
					data: bids,
				},
				...(isSoldAuction
					? []
					: [
							{
								name: 'Bought In',
								data: [getBoughtInBid()],
							},
						]),
			],
			legend: {
				show: false,
			},
			annotations: {
				points: getPointsAnnotations(),
				yaxis: [
					{
						y: getLowEstimateAmount(),
						borderColor: '#fb2c36',
						label: {
							position: 'left',
							offsetX: getLowEstimateOffsetX(),
							borderColor: '#fb2c36',
							style: {
								color: '#fff',
								background: '#fb2c36',
							},
							text: getLowEstimateLabel(),
						},
					},
					{
						y: getHighEstimateLineOffset(highEstimate),
						borderColor: '#00c951',
						label: {
							position: 'left',
							offsetX: 45,
							borderColor: '#00c951',
							style: {
								color: '#fff',
								background: '#00c951',
							},
							text: getHighEstimateLabel(),
						},
					},
				],
				xaxis: getXaxisAnnotations(),
			},
			tooltip: {
				shared: false,
				intersect: true,
				custom: function ({ seriesIndex, dataPointIndex, w }) {
					const {
						x,
						y,
						bidderPosition,
						paddleNumber,
						bidderName,
						isWinningBid,
						notes,
					}: BidItem = w.config.series[seriesIndex].data[dataPointIndex];

					return `
						<div class="flex flex-col gap-1 p-2 bg-gray-700 text-white text-[12px]">
							<span>
								<span class="font-bold">Bidder position:</span> ${bidderPosition || '--'}
							</span>

							<span>
								<span class="font-bold">Bidder name:</span> ${bidderName || '--'}
							</span>

							<span>
								<span class="font-bold">Paddle number:</span> ${paddleNumber || '--'}
							</span>

							<span>
								<span class="font-bold">Bid amount:</span> ${formatCurrency({
									amount: y,
									currencyCode: auctionLotDetails?.currency,
								})}
							</span>

							<span>
								<span class="font-bold">Notes:</span> ${notes || '--'}
							</span>

							<span>
								<span class="font-bold">Time:</span> ${new Intl.NumberFormat('en-US').format(x as unknown as number) || '0'} seconds
							</span>

							${
								isWinningBid
									? `<span>
											<span class="font-bold">Winning bid:</span> Yes
										</span>`
									: ''
							}
						</div>`;
				},
				x: {
					formatter: function (val) {
						return `${new Intl.NumberFormat('en-US').format(val as unknown as number) || '0'} seconds`;
					},
				},
			},
			xaxis: {
				type: 'category',
				max: getAdditionalSeconds(),
				title: {
					text: 'Time (seconds)',
				},
				labels: {
					style: {
						fontSize: '12px',
						fontFamily: 'Silka',
					},
				},
			},
			yaxis: {
				title: {
					text: `Bid amount ${
						auctionLotDetails?.currency
							? `(${getCurrencySymbol({
									currencyCode: auctionLotDetails?.currency,
								})})`
							: ''
					}`,
				},
				min: getYAxisMin(lowEstimate),
				max: getYAxisMax(highEstimate),
				labels: {
					style: {
						fontSize: '12px',
						fontFamily: 'Silka',
					},
					formatter: function (val) {
						return getCurrencyValue({
							amount: Number(val),
							currencyCode: auctionLotDetails?.currency,
						});
					},
				},
				tickAmount: 2,
			},
		};

		return {
			rawData: {
				bids: bids,
				lowEstimate,
				highEstimate,
			},
			chartOptions,
		};
	};

	let chartData = $derived(createBiddingChartData());

	const getYAxisMin = (lowEstimate: number) => {
		const firstBid = bids[0];

		return Math.min(firstBid.y, lowEstimate);
	};

	const getYAxisMax = (highEstimate: number) => {
		const lastBid = bids[bids.length - 1];

		const highEstimatePadding = highEstimate + Math.floor(highEstimate * 0.01); // deprecated

		return Math.max(lastBid.y, highEstimate);
	};

	const getAdditionalSeconds = () => {
		const lastBid = bids[bids.length - 1];

		return lastBid.seconds + Math.floor(lastBid.seconds * 0.02);
	};
</script>

<div>
	<InputLabel variant="label3" dataCy="bid-chart" required class="mb-3">
		Bidding chart
	</InputLabel>

	<div class="chart-wrapper mb-4">
		<BiddingProgressChartContents
			{chart}
			options={chartData.chartOptions}
			{bids}
			auctionLotId={auctionLotDetails?.auctionLotId}
		/>
	</div>
</div>
