<script lang="ts">
	import type { ApexOptions } from 'apexcharts';
	import type ApexCharts from 'apexcharts';
	import type { BidItem } from '../types';
	import { BiddingProgressChartDownload } from './BiddingProgressChartDownload';
	import { BiddingProgressChartLegend } from './BiddingProgressChartLegend';
	import { Chart } from '$global/components/Chart';

	interface Props {
		chart: ApexCharts | undefined;
		options: ApexOptions;
		bids: BidItem[];
		auctionLotId: string;
	}

	let { chart, bids, options, auctionLotId }: Props = $props();

	let downloadContainer = $state(null) as HTMLDivElement | null;
</script>

<div class="relative">
	<BiddingProgressChartLegend {bids} />

	<div class="mb-4">
		<Chart
			bind:chart
			dataCy="bidding-progress-chart"
			id="bidding-progress-chart"
			{options}
			class="h-[400px] md:h-[700px]"
		/>
	</div>
</div>

<!-- Hidden element with specific sizing for image download -->
<div class="w-[1500px] absolute left-[-9999px] top-[-9999px]">
	<div bind:this={downloadContainer}>
		<BiddingProgressChartLegend {bids} />

		<div class="mb-4">
			<Chart
				bind:chart
				dataCy="bidding-progress-chart"
				{options}
				class="h-[700px]"
				id="bidding-progress-chart-download"
			/>
		</div>
	</div>
</div>

<div class="flex justify-center">
	<BiddingProgressChartDownload {auctionLotId} {downloadContainer} />
</div>
