<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		onSubmit: () => Promise<void>;
	}

	let { dialogStores, onSubmit }: Props = $props();

	let submitting = $state(false);

	const dataCyPrefix = `reset-and-clear-dialog`;

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};

	const handleSubmit = async () => {
		submitting = true;
		await onSubmit();
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-full max-h-none w-full items-center justify-center rounded-none p-[1.5rem] pt-[1.25rem] sm:max-h-[24rem] sm:max-w-[40rem] sm:rounded sm:p-[1.5rem] sm:pt-[1rem]"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<Txt variant="h3" class="my-4 max-w-[24rem] text-center"
			>Reset and clear bids</Txt
		>
		<Txt variant="body2" class="mb-8 max-w-[26rem] text-center"
			>Do you really want to reset and clear the existing bids ? This will
			archive the bidder client information too and should be used as a last
			resort. The other changes made on that page will be saved too.</Txt
		>

		<div class="grid grid-cols-5 gap-2">
			<Button
				dataCy={`${dataCyPrefix}-cancel`}
				onclick={handleClose}
				class={classNames('border-0', { 'pointer-events-none': submitting })}
				variant="secondary"
				size="md"
			>
				cancel
			</Button>

			<Button
				size="md"
				class="col-span-4"
				loading={submitting}
				disabled={submitting}
				dataCy={`${dataCyPrefix}-submit`}
				onclick={handleSubmit}
			>
				confirm
			</Button>
		</div>
	</div>
</Dialog>
