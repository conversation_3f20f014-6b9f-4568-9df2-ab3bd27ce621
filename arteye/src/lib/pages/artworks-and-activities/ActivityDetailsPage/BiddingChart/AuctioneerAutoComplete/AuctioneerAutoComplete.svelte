<script lang="ts" module>
	export const formatAuctioneer = (
		person: GetPeopleQuery['person'][number]
	) => {
		const url = `${Routes.People}/${person?.id}`;

		return {
			line1: `${person?.entity?.name}`,
			line2: url,
			line4: `${person?.id}`,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetPeopleQuery,
		GetPeopleQueryVariables,
	} from '$lib/queries/__generated__/getPeople.generated';
	import { GetPeopleDocument } from '$lib/queries/__generated__/getPeople.generated';

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		class?: string;
		onChange:
			| undefined
			| ((e: {
					detail: {
						value: GetPeopleQuery['person'][number] | undefined | null;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search auctioneer',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
		...rest
	}: Props = $props();

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	let people: GetPeopleQuery['person'] | [] = $state([]);

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		selectedOption = null;
		const entity = people.find((entity) => entity.id === e.detail.value.line4);
		if (onChange) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	const value = writable('');

	const getVariables = (value: string): GetPeopleQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: value
				? {
						_and: [
							...(() => {
								if (!value) {
									return [];
								}

								if (isUuidValid(value)) {
									return [{ id: { _eq: value } }];
								}

								if (!isNaN(+value)) {
									return [{ reference_id: { _eq: value } }];
								}

								return [
									{
										entity: {
											name: {
												_icontains: value,
											},
										},
									},
								];
							})(),
							{ status: { key: { _neq: 'archived' } } },
						],
					}
				: {},
		};
	};

	const handleDelete = () => {
		selectedOption = null;
		if (onChange) {
			onChange({
				detail: {
					value: null,
				},
			});
		}
	};

	const getOptions = (data: GetPeopleQuery | undefined) => {
		// Allows the update to work in a derived rune
		setTimeout(() => {
			people = data?.person || [];
		}, 0);
		return [...(data?.person || []).map(formatAuctioneer)];
	};
</script>

<div
	class={twMerge(
		classNames('relative', {
			'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
		}),
		rest.class
	)}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="auctioneer"
		dataCy={`${dataCy}-auctioneer`}
		{placeholder}
		emptyValueResponse={{ person: [], person_aggregated: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetPeopleDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults class="text-left" dataCy={`${dataCy}-auctioneer-autocomplete`}
				>No persons found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>
</div>
<Button
	dataCy={`${dataCy}-delete`}
	class="h-[2rem] w-[2rem] px-0"
	variant="secondary"
	onclick={handleDelete}
	size="xs"
>
	<BinIcon class="h-3 w-3" />
</Button>
