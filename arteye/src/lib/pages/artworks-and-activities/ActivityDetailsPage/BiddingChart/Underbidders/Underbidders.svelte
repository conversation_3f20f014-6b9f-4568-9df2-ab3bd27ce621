<script lang="ts">
	import { type AssociatedEntityRow } from '../../AssociatedEntitiesCard';
	import { EntityInput } from '../../AssociatedEntitiesCard/EntityInput';
	import { handleEntityChange } from '../../AssociatedEntitiesCard/utils/handleEntityChange/handleEntityChange';
	import { handleEntityRemove } from '../../AssociatedEntitiesCard/utils/handleEntityRemove/handleEntityRemove';
	import { Txt } from '$global/components/Txt';

	interface Props {
		associatedEntities: AssociatedEntityRow[];
		onChangeAssociatedEntities: (items: AssociatedEntityRow[]) => void;
	}

	let { associatedEntities, onChangeAssociatedEntities }: Props = $props();

	let underbidders = $derived(
		associatedEntities.filter(
			(associatedEntity) =>
				associatedEntity?.association?.type?.key === 'UNDERBIDDER' &&
				!associatedEntity?.isDeleted &&
				associatedEntity.entity
		)
	);
</script>

{#if underbidders.length}
	{#each underbidders as underbidder}
		<EntityInput
			class="mb-4 [&_button]:hidden"
			entity={underbidder.entity}
			onChange={handleEntityChange(
				underbidder.rowId,
				associatedEntities,
				onChangeAssociatedEntities
			)}
			onRemove={handleEntityRemove(
				underbidder.rowId,
				associatedEntities,
				onChangeAssociatedEntities
			)}
		/>
	{/each}
{:else}
	<Txt variant="body2" class="italic text-gray-500">No underbidders</Txt>
{/if}
