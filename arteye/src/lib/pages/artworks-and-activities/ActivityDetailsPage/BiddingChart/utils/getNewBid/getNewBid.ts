import dayjs from 'dayjs';
import { type BidRow, NEW_CURRENCY } from '../../types';
import { findConversionTimestamp } from '../findConversionTimestamp/findConversionTimestamp';
import { findCurrency } from '../findCurrency/findCurrency';

export const getNewBid = (bids: BidRow[]) => ({
	id: undefined,
	isNew: true,
	isHammerBid: false,
	isDeleted: false,
	amount: {
		currency: findCurrency(bids) || NEW_CURRENCY,
		conversion_timestamp:
			findConversionTimestamp(bids) || dayjs().toISOString(),
		amount: '',
		usd_amount: '',
	},
	notes: '',
	formattedTime: '',
});
