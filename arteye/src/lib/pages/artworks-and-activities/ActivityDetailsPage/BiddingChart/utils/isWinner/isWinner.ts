import type { BidRow } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/types';
import { getPositionFromNotes } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/utils/getPositionFromNotes/getPositionFromNotes';

export const isWinner = (bidRows: BidRow[], position: string) => {
	return (
		getPositionFromNotes(bidRows[bidRows.length - 2]?.notes) === position &&
		bidRows?.find((bidRow) => bidRow?.isHammerBid)
	);
};
