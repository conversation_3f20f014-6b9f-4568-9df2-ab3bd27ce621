import { getBidderInfo } from '../getBidderInfo/getBidderInfo';
import { type BidRow } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/types';
import { getPositionFromNotes } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/utils/getPositionFromNotes/getPositionFromNotes';

export const getUniqueBidderPositions = (bidRows: BidRow[]) =>
	Array.from(
		new Set(
			bidRows
				.filter((bidRow) => !bidRow.isDeleted)
				.map((bidRow) => getPositionFromNotes(bidRow.notes))
		)
	).filter((bidderPosition) => !!getBidderInfo(bidderPosition));
