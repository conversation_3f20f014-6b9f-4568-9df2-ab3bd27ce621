export const BidderTypeAbbreviationToKey = Object.freeze({
	C: 'COMMISSION',
	CH: 'CHANDELIER',
	O: 'ONLINE',
	P: 'PHONE',
	R: 'ROOM',
});

export const BidderTypeKeyToAbbreviation = Object.freeze({
	COMMISSION: 'C',
	CHANDELIER: 'CH',
	ONLINE: 'O',
	PHONE: 'P',
	ROOM: 'R',
});

const BidderTypes = Object.freeze({
	CHANDELIER: 'Chandelier Bidder',
	COMMISSION: 'Commission Bidder',
	ONLINE: 'Online Bidder',
	PHONE: 'Phone Bidder',
	ROOM: 'Room Bidder',
});

export const getBidderInfo = (value: string) => {
	const match = value.match(/^([a-zA-Z]+)(\d*)$/);

	if (!match) return null;

	const abbreviation = match[1].toUpperCase();

	const location = match[2] ? Number(match[2]) : null;

	const key =
		BidderTypeAbbreviationToKey[
			abbreviation as keyof typeof BidderTypeAbbreviationToKey
		];

	if (!key) return null;

	const matchingTypeName = BidderTypes[key as keyof typeof BidderTypes];

	if (!matchingTypeName) return null;

	// if ((abbreviation === 'CH' || abbreviation === 'C') && location) {
	// 	return null;
	// }

	return {
		location: location,
		type: {
			key,
			name: matchingTypeName,
			label: abbreviation,
		},
	};
};
