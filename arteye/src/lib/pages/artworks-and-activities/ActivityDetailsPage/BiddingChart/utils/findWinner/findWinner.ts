import { getUniqueBidderPositions } from '../getUniqueBidderPositions/getUniqueBidderPositions';
import { isWinner } from '../isWinner/isWinner';
import {
	type BidderInfoRow,
	type BidRow,
} from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/types';

export const findWinner = (
	bidRows: BidRow[],
	bidderInfoRows: Record<string, BidderInfoRow | undefined>
) => {
	const winnerPosition = getUniqueBidderPositions(bidRows).find(
		(uniqueBidderPosition) => isWinner(bidRows, uniqueBidderPosition)
	);

	return winnerPosition ? bidderInfoRows[winnerPosition] : null;
};
