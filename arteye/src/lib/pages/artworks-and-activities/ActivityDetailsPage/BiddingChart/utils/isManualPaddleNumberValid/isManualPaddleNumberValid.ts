import { findWinner } from '../findWinner/findWinner';
import type {
	BidRow,
	BidderInfoRow,
} from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/types';
import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';

export enum CODES {
	EXISTING_CLIENT_PADDLE_MATCH,
	EXISTING_CLIENT_PADDLE_MISMATCH,
	EXISTING_CLIENT_NO_PADDLE_NUMBER_ALREDY_EXIST,
	EXISTING_CLIENT_NO_PADDLE_CAN_ASSIGN,
	NO_CLIENT_PADDLE_NUMBER_ALREADY_EXIST,
	NO_CLIENT_CAN_ASSIGN_NUMBER_ON_EXISTING_CLIENT,
	NO_CLIENT_CAN_CREATE_NEW_CLIENT,
}

export const isManualPaddleNumberValid = (
	bidRows: BidRow[],
	bidderInfoRows: Record<string, BidderInfoRow | undefined>,
	auctionClients: null | GetAuctionClientQuery['auction_client'],
	winningPaddleNumberValue: string
) => {
	const activityPaddleNumbers = (auctionClients || [])
		?.map((auctionClient) => auctionClient?.paddle_number || '')
		.filter(Boolean);

	const winnerBidder = findWinner(bidRows, bidderInfoRows);
	const clientWithTheSamePaddleNumber = (activityPaddleNumbers || []).find(
		(activityPaddleNumber) =>
			activityPaddleNumber.toUpperCase() ===
			winningPaddleNumberValue.trim().toUpperCase()
	);

	const bidderWithTheSamePaddleNumber = Object.values(bidderInfoRows)
		.map((bidderInfoRow) =>
			bidderInfoRow?.isDeleted
				? null
				: bidderInfoRow?.auctionClient?.paddleNumber
		)
		.filter(Boolean)
		.find(
			(bidderPaddleNumber) =>
				bidderPaddleNumber?.toUpperCase() ===
				winningPaddleNumberValue.trim().toUpperCase()
		);

	if (winnerBidder?.auctionClient) {
		if (winnerBidder.auctionClient?.paddleNumber) {
			if (
				winnerBidder.auctionClient?.paddleNumber.toUpperCase() !==
				winningPaddleNumberValue.trim().toUpperCase()
			) {
				return {
					code: CODES.EXISTING_CLIENT_PADDLE_MISMATCH,
					message: `The winning bidder has a client with a different paddle number to the winning paddle you specified (${winnerBidder.bidderPosition} has paddle #${winnerBidder.auctionClient?.paddleNumber})`,
				};
			} else {
				return { code: CODES.EXISTING_CLIENT_PADDLE_MATCH, message: '' };
			}
		} else {
			if (clientWithTheSamePaddleNumber) {
				return {
					code: CODES.EXISTING_CLIENT_NO_PADDLE_NUMBER_ALREDY_EXIST,
					message:
						'The winning paddle number is already assigned to another auction client. Please double check the paddle number or client assignment.',
				};
			} else {
				return {
					code: CODES.EXISTING_CLIENT_NO_PADDLE_CAN_ASSIGN,
					message: '',
				};
			}
		}
	} else {
		if (clientWithTheSamePaddleNumber) {
			if (bidderWithTheSamePaddleNumber) {
				return {
					code: CODES.NO_CLIENT_PADDLE_NUMBER_ALREADY_EXIST,
					message:
						'This paddle number is already assigned to a bidder that did not win. Please double check the paddle number.',
				};
			} else {
				return {
					code: CODES.NO_CLIENT_CAN_ASSIGN_NUMBER_ON_EXISTING_CLIENT,
					message: '',
				};
			}
		} else {
			return {
				code: CODES.NO_CLIENT_CAN_CREATE_NEW_CLIENT,
				message: '',
			};
		}
	}
};
