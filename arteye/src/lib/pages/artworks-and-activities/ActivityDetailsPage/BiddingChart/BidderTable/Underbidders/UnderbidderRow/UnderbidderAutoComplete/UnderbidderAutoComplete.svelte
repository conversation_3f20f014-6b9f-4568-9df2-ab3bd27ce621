<script lang="ts" module>
	export const formatUnderbidder = (
		entity: GetEntitiesQuery['entity'][number] | undefined | null
	) => {
		if (!entity || Object.keys(entity).length === 0) {
			return null;
		}
		const url = (() => {
			if (entity?.type?.key === 'person') {
				if (entity?.artist) {
					return `${Routes.Artists}/${entity?.artist?.id}`;
				} else {
					return `${Routes.People}/${entity?.person?.id}`;
				}
			}

			return `${Routes.Organisations}/${entity?.organisation?.id}`;
		})();

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { page } from '$app/state';
	import SearchIcon from '$global/assets/icons/SearchIcon/SearchIcon.svelte';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import {
		GetEntitiesDocument,
		type GetEntitiesQuery,
		type GetEntitiesQueryVariables,
	} from '$lib/queries/__generated__/getEntities.generated';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption: OptionType | null;
		class?: string;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: GetEntitiesQuery['entity'][number];
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search entities',
		dataCy,
		selectedOption = null,
		onChange = undefined,
		...rest
	}: Props = $props();
	let entities: GetEntitiesQuery['entity'] | [] = $state([]);
	let data = $derived(getPageData<PersonDetailsPageData>(page.data));
	const value = writable('');

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(artist) => artist.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: value
				? {
						_and: [
							{ name: { _icontains: value } },
							{ status: { key: { _neq: Status_Enum.Archived } } },
						],
					}
				: { status: { key: { _neq: Status_Enum.Archived } } },
		};
	};

	const getOptions = (queryData: GetEntitiesQuery | undefined) => {
		entities = queryData?.entity || [];
		return [
			...(queryData?.entity || []).map(formatUnderbidder),
		] as OptionType[];
	};
</script>

<div
	class={twMerge(
		classNames('relative', {
			'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
		}),
		rest.class
	)}
>
	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
			}}
		>
			<SearchIcon class="h-3 w-3" />
		</button>
	{/if}
	<QueryAutocomplete
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		emptyValueResponse={{ entity: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			input: 'border-none bg-transparent',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetEntitiesDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults class="text-left" dataCy={`${dataCy}-entity-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>
</div>
