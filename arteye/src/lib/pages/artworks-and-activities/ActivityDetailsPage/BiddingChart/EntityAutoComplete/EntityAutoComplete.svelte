<script lang="ts" module>
	import { writable, type Writable } from 'svelte/store';

	export const formatEntity = (
		entity: GetEntitiesQuery['entity'][number] | undefined | null
	) => {
		if (!entity || Object.keys(entity).length === 0) {
			return null;
		}

		return {
			line1: `${entity?.name}`,
			line2: getEntityUrl(entity),
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line5: getEntityDetails(entity),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { twMerge } from 'tailwind-merge';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { gqlClient } from '$lib/gqlClient';

	import type {
		GetEntitiesQuery,
		GetEntitiesQueryVariables,
	} from '$lib/queries/__generated__/getEntities.generated';
	import { GetEntitiesDocument } from '$lib/queries/__generated__/getEntities.generated';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';
	import { getEntityUrl } from '$lib/utils/getEntityUrl/getEntityUrl';
	import { getEntityDetails } from '$lib/utils/getEntityDetails/getEntityDetails';

	interface Props {
		onClickClear: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption: OptionType | null | undefined;
		value?: Writable<string>;
		excludedIds: string[];
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
		class?: string;
	}

	let {
		onClickClear,
		placeholder = 'Search entities',
		dataCy,
		selectedOption = $bindable(null),
		value = writable(''),
		excludedIds,
		onChange = undefined,
		...rest
	}: Props = $props();

	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: value
				? {
						_and: [
							{
								_or: [
									...(isValidUUID(value)
										? [
												{ organisation: { id: { _eq: value } } },
												{ person: { id: { _eq: value } } },
												{ artist: { id: { _eq: value } } },
											]
										: []),
									{ name: { _icontains: value } },
								],
							},
							{ status: { key: { _neq: Status_Enum.Archived } } },
							...(excludedIds.length ? [{ id: { _nin: excludedIds } }] : []),
						],
					}
				: {
						_and: [
							{ status: { key: { _neq: Status_Enum.Archived } } },
							...(excludedIds.length ? [{ id: { _nin: excludedIds } }] : []),
						],
					},
		};
	};

	const getOptions = (queryData: GetEntitiesQuery | undefined) => {
		return [...(queryData?.entity || []).map(formatEntity)] as OptionType[];
	};
</script>

<div
	class={twMerge(
		classNames('relative justify-between bg-white', {
			'flex items-center gap-2 rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
			block: !selectedOption,
		}),
		rest.class
	)}
>
	<div
		class={classNames('w-full', {
			'max-w-[calc(100%-20px)]': !!selectedOption,
		})}
	>
		<QueryAutocomplete
			OptionComponent={LinkOption}
			SelectedOptionComponent={LinkOption}
			name="entity"
			dataCy={`${dataCy}-entity`}
			{placeholder}
			emptyValueResponse={{ entity: [] }}
			showResultsWhenEmpty={false}
			graphQlClient={gqlClient}
			classes={{
				listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
				input: 'border-none',
				option: {
					line3: 'hidden',
					line4: 'hidden',
				},
				selectedOption: {
					button: 'max-w-full',
					line3: 'hidden',
					line4: 'hidden',
				},
			}}
			requestHeaders={getAuthorizationHeaders(data)}
			{getOptions}
			{getVariables}
			document={GetEntitiesDocument}
			{value}
			bind:selectedOption
			{onChange}
		>
			{#snippet noResults()}
				<NoResults class="text-left" dataCy={`${dataCy}-entity-autocomplete`}
					>No results found.</NoResults
				>
			{/snippet}
		</QueryAutocomplete>
	</div>

	{#if !!selectedOption}
		<button class="z-10" onclick={onClickClear}>
			<CrossIcon class="max-h-4 min-h-4 min-w-4 max-w-4" />
		</button>
	{/if}
</div>
