import { ApexMarkerShape } from '$global/components/Chart/types';
import { type ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

export type Underbidder = NonNullable<
	NonNullable<
		NonNullable<ActivityDetailsPageData['activity']>['associations']
	>[number]
> & {
	isDeleted?: boolean;
	isNew?: boolean;
};

export type BidRow = {
	id: string | undefined;
	formattedTime: string;
	notes: string;
	amount: {
		id?: string | undefined;
		conversion_timestamp: string;
		amount: string;
		usd_amount: string;
		currency: {
			code: string;
			name: string;
		};
	};
	isNew: boolean;
	isHammerBid: boolean;
	isDeleted: boolean;
};

export type BidderInfoRow = {
	id: string | undefined;
	isDeleted: boolean;
	bidderEntity: null | { id: string; name: string };
	auctionClient: null | {
		id: string;
		entityName: string | null | undefined;
		entityId: string | null | undefined;
		paddleNumber: string | null | undefined;
	};
	bidderPosition: string;
};

export const NEW_CURRENCY = {
	code: 'USD',
	name: 'usd',
};

export const UnderbidderType = {
	key: 'UNDERBIDDER',
	name: 'Underbidder',
};

export enum BiddingPosition {
	C = 'C',
	CH = 'CH',
	P1 = 'P1',
	P2 = 'P2',
	P3 = 'P3',
	P4 = 'P4',
	R1 = 'R1',
	R2 = 'R2',
	R3 = 'R3',
	R4 = 'R4',
	R5 = 'R5',
	R6 = 'R6',
	R7 = 'R7',
	R8 = 'R8',
	O1 = 'O1',
	O2 = 'O2',
	O3 = 'O3',
	O4 = 'O4',
	O5 = 'O5',
	O6 = 'O6',
	O7 = 'O7',
	O8 = 'O8',
	P5 = 'P5',
	P6 = 'P6',
	P7 = 'P7',
	P8 = 'P8',
}

export enum BidChartColorOld {
	Emerald = '#00bc7d',
	Fuchsia = '#e12afb',
	Orange = '#ff6900',
	Cyan = '#00b8db',
	Indigo = '#615fff',
	Yellow = '#f0b100',
	Purple = '#ad46ff',
	Lime = '#7ccf00',
	Red = '#fb2c36',
}
export enum BidChartColor {
	Emerald = '#007a55',
	Fuchsia = '#a800b7',
	Orange = '#ca3500',
	Cyan = '#007595',
	Indigo = '#432dd7',
	Yellow = '#f0b100',
	Purple = '#6e11b0',
	Lime = '#497d00',
	Red = '#c10007',
}
