<script lang="ts">
	import classNames from 'classnames';
	import { type Writable } from 'svelte/store';
	import { AuctionClientAutocomplete } from '../AuctionClientAutocomplete';
	import { type BiddingChartDetailsForm } from '../BiddingChart.svelte';
	import { EntityAutoComplete } from '../EntityAutoComplete';
	import type { BidderInfoRow, BidRow } from '../types';
	import { isWinner } from '../utils/isWinner/isWinner';
	import { InputError } from '$global/components/Input/InputError';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableRow,
		TableCell,
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { hasDuplicates } from '$global/utils/hasDuplicates/hasDuplicates';

	const dataCyPrefix = 'bidder-client-table';
	const headers = ['Position', 'Bidder', 'Auction Client'];

	interface Props {
		changed: { biddingCharts: boolean };
		bidRows: BidRow[];
		editAuctionBidderPosition: string | null;
		createNewAuctionBidderPosition: string;
		biddingChartDetailsForm: BiddingChartDetailsForm;
		bidderInfoEntitySelectedOptions: Record<
			string,
			OptionType | null | undefined
		>;
		bidderInfoEntityValues: Record<string, Writable<string> | undefined>;
		auctionClientSelectedOptions: Record<string, OptionType | null | undefined>;
		auctionClientValues: Record<string, Writable<string> | undefined>;
		uniqueBidderPositions: string[];
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		showSaveBar: boolean;
	}

	let {
		changed = $bindable(),
		bidRows,
		editAuctionBidderPosition = $bindable(),
		createNewAuctionBidderPosition = $bindable(),
		biddingChartDetailsForm,
		bidderInfoEntitySelectedOptions = $bindable(),
		bidderInfoEntityValues,
		auctionClientSelectedOptions = $bindable(),
		auctionClientValues,
		uniqueBidderPositions,
		bidderInfoRows = $bindable(),
		showSaveBar = $bindable(),
	}: Props = $props();

	const handleClickClearBidder = (uniqueBidderPosition: string) => () => {
		const newBidderInfoRows = {
			...bidderInfoRows,
			[uniqueBidderPosition]: {
				...bidderInfoRows[uniqueBidderPosition],
				bidderEntity: null,
			},
		} as Record<string, BidderInfoRow>;

		bidderInfoRows = newBidderInfoRows;
		showSaveBar = true;
		changed.biddingCharts = true;
		bidderInfoEntitySelectedOptions[uniqueBidderPosition] = null;
		bidderInfoEntityValues[uniqueBidderPosition]?.set('');
		return Promise.resolve();
	};

	const handleClickClearAuctionClient =
		(uniqueBidderPosition: string) => () => {
			const newBidderInfoRows = {
				...bidderInfoRows,
				[uniqueBidderPosition]: {
					...bidderInfoRows[uniqueBidderPosition],
					auctionClient: null,
				},
			} as Record<string, BidderInfoRow>;

			bidderInfoRows = newBidderInfoRows;
			showSaveBar = true;
			changed.biddingCharts = true;

			auctionClientSelectedOptions[uniqueBidderPosition] = null;
			auctionClientValues[uniqueBidderPosition]?.set('');
			return Promise.resolve();
		};

	const handleAuctionClientChange =
		(uniqueBidderPosition: string) =>
		(e: {
			detail: {
				value: OptionType;
			};
		}) => {
			const newBidderInfoRows = {
				...bidderInfoRows,
				[uniqueBidderPosition]: {
					...bidderInfoRows[uniqueBidderPosition],
					auctionClient: {
						id: `${e.detail.value.line5}`,
						entityName: e.detail.value.line8,
						entityId: e.detail.value.line4,
						paddleNumber: e.detail.value.line7,
					},
				},
			} as Record<string, BidderInfoRow>;

			bidderInfoRows = newBidderInfoRows;
			showSaveBar = true;
			changed.biddingCharts = true;
			return Promise.resolve();
		};

	const handleBidderChange =
		(uniqueBidderPosition: string) =>
		(e: {
			detail: {
				value: OptionType;
			};
		}) => {
			const newBidderInfoRows = {
				...bidderInfoRows,
				[uniqueBidderPosition]: {
					...bidderInfoRows[uniqueBidderPosition],
					bidderEntity: {
						id: `${e.detail.value.line4}`,
						name: `${e.detail.value.line1}`,
					},
				},
			} as Record<string, BidderInfoRow>;

			bidderInfoRows = newBidderInfoRows;
			showSaveBar = true;
			changed.biddingCharts = true;
			return Promise.resolve();
		};

	let nonDeletedEntries = $derived(
		uniqueBidderPositions
			.map((uniqueBidderPosition) =>
				bidderInfoRows[uniqueBidderPosition] &&
				!bidderInfoRows[uniqueBidderPosition]?.isDeleted
					? bidderInfoRows[uniqueBidderPosition]
					: null
			)
			.filter(Boolean)
	);

	let auctionClientIds = $derived(
		nonDeletedEntries
			.map((entry) => entry?.auctionClient?.id)
			.filter(Boolean) as string[]
	);

	let bidderIds = $derived(
		nonDeletedEntries
			.map((entry) => entry?.bidderEntity?.id)
			.filter(Boolean) as string[]
	);

	let errors = $derived(
		(() => {
			let errors: string[] = [];

			if (hasDuplicates(auctionClientIds)) {
				errors = [...errors, 'Two bidders have the same auction client.'];
			}

			if (hasDuplicates(bidderIds)) {
				errors = [...errors, 'Two bidders have the same bidder entity.'];
			}

			return errors;
		})()
	);
</script>

<table class="mb-4 w-full table-fixed bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader dataCy={dataCyPrefix} width={'calc(100% / 3)'}>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>

	<TableBody dataCy={dataCyPrefix}>
		{#key JSON.stringify(uniqueBidderPositions)}
			{#each uniqueBidderPositions as uniqueBidderPosition}
				{#if bidderInfoRows[uniqueBidderPosition] && !bidderInfoRows[uniqueBidderPosition]?.isDeleted}
					{@const winner = isWinner(bidRows, uniqueBidderPosition)}
					<TableRow index={0} dataCy={dataCyPrefix} class="h-[41px]">
						<TableCell dataCy={dataCyPrefix} width={'calc(100% / 3)'}>
							{uniqueBidderPosition}{winner &&
							biddingChartDetailsForm?.activity_status === 'SOLD'
								? ' (Winner)'
								: ''}
						</TableCell>
						<TableCell class={classNames('px-3')} dataCy={dataCyPrefix}>
							{#snippet custom()}
								<EntityAutoComplete
									dataCy={`${dataCyPrefix}-bidder-entity`}
									onChange={handleBidderChange(uniqueBidderPosition)}
									placeholder="Search for bidder"
									value={bidderInfoEntityValues[uniqueBidderPosition]}
									class={classNames('flex w-full rounded-none border-none')}
									excludedIds={Array.from(new Set(bidderIds))}
									bind:selectedOption={bidderInfoEntitySelectedOptions[
										uniqueBidderPosition
									]}
									onClickClear={handleClickClearBidder(uniqueBidderPosition)}
								/>
							{/snippet}
						</TableCell>
						<TableCell
							class={classNames('px-3')}
							dataCy={dataCyPrefix}
							width={'calc(100% / 3)'}
						>
							{#snippet custom()}
								{#if auctionClientSelectedOptions[uniqueBidderPosition] !== undefined}
									<AuctionClientAutocomplete
										bind:editAuctionBidderPosition
										bidderPosition={uniqueBidderPosition}
										bind:createNewAuctionBidderPosition
										auctionId={`${biddingChartDetailsForm.auctionId}`}
										dataCy={`${dataCyPrefix}-auction-client`}
										onChange={handleAuctionClientChange(uniqueBidderPosition)}
										placeholder="Search for client"
										value={auctionClientValues[uniqueBidderPosition]}
										class={classNames('flex w-full rounded-none border-none')}
										excludedIds={Array.from(new Set(auctionClientIds))}
										bind:selectedOption={auctionClientSelectedOptions[
											uniqueBidderPosition
										]}
										onClickClear={handleClickClearAuctionClient(
											uniqueBidderPosition
										)}
									/>
								{/if}
							{/snippet}
						</TableCell>
					</TableRow>
				{/if}
			{/each}
		{/key}
	</TableBody>
</table>

{#if errors?.length}
	<div class="mb-4">
		{#each errors as error}
			<InputError class="mt-0" dataCy={dataCyPrefix}>{error}</InputError>
		{/each}
	</div>
{/if}
