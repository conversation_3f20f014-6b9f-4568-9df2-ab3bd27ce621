<script lang="ts" module>
	export type Bid = Partial<
		NonNullable<
			NonNullable<
				NonNullable<
					NonNullable<
						NonNullable<
							GetArtworkActivityByIdQuery['artwork_activity_by_id']
						>['artwork_listing']
					>[number]
				>['auction_lot']
			>['bids']
		>[number]
	> & {
		isNew?: boolean;
		isDeleted?: boolean;
		isHammerBid?: boolean;
	};

	export type BiddingChartDetailsForm = Partial<
		Pick<
			NonNullable<
				NonNullable<
					NonNullable<
						NonNullable<
							GetArtworkActivityByIdQuery['artwork_activity_by_id']
						>['artwork_listing']
					>[number]
				>['auction_lot']
			>,
			| 'id'
			| 'video_file_name'
			| 'video_hammer_time_seconds'
			| 'auctioneer'
			| 'winning_bid'
			| 'hammer_timestamp'
		> & {
			artworkActivityId: string;
			artworkListingId: string;
			auctionId: string;
			auctionLotId: string;
			activity_status: string | null;
			bids: Bid[];
			associations: Underbidder[];
		}
	>;
</script>

<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { type Writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { type AssociatedEntityRow } from '../AssociatedEntitiesCard';
	import type { AuctionLotDetailsForm } from '../AuctionLotDetailsCard';
	import { formatAuctionClient } from './AuctionClientAutocomplete/AuctionClientAutocomplete.svelte';
	import { EditAuctionClientDialog } from './AuctionClientAutocomplete/EditAuctionClientDialog';
	import { AuctioneerAutoComplete } from './AuctioneerAutoComplete';
	import { formatAuctioneer } from './AuctioneerAutoComplete/AuctioneerAutoComplete.svelte';
	import { BidderPositionGuide } from './BidderPositionGuide';
	import { BiddingProgressChart } from './BiddingProgressChart';
	import { BidTable } from './BidTable';
	import { ClientInfo } from './ClientInfo';
	import { formatEntity } from './EntityAutoComplete/EntityAutoComplete.svelte';
	import { ResetAndClearDialog } from './ResetAndClearDialog';
	import type { BidderInfoRow, BidRow, Underbidder } from './types';
	import { Underbidders } from './Underbidders';
	import { getPositionFromNotes } from './utils/getPositionFromNotes/getPositionFromNotes';
	import { getUniqueBidderPositions } from './utils/getUniqueBidderPositions/getUniqueBidderPositions';
	import { isWinner } from './utils/isWinner/isWinner';
	import { RefreshIcon } from '$global/assets/icons/RefreshIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Txt } from '$global/components/Txt';
	import { isTimecodeValid } from '$global/utils/isTimecodeValid/isTimecodeValid';
	import { Status_Enum } from '$gql/types-custom';
	import { gqlClient } from '$lib/gqlClient';
	import { CreateNewAuctionDialog } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/CreateNewAuctionDialog';
	import type { GetArtworkActivityByIdQuery } from '$lib/queries/__generated__/getArtworkActivityById.generated';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';
	import { GetAuctionClientDocument } from '$lib/queries/__generated__/getAuctionClient.generated';
	import type { GetPeopleQuery } from '$lib/queries/__generated__/getPeople.generated';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	interface Props {
		headers: {
			Authorization: string;
		};
		isSoldAuction: boolean;
		activity: ActivityDetailsPageData['activity'];
		activityId: string;
		changed: { biddingCharts: boolean };
		hasRecordedBids: boolean | null;
		showSaveBar: boolean;
		associatedEntities: AssociatedEntityRow[];
		onChangeAssociatedEntities: (items: AssociatedEntityRow[]) => void;
		onResetBids: () => Promise<void>;
		auctionClients: null | GetAuctionClientQuery['auction_client'];
		winningPaddleNumberValue: string;
		bidRows: BidRow[];
		recordingInProgress: boolean;
		bidderInfoRows: Record<string, BidderInfoRow | undefined>;
		onChangeBidRows: (rows: BidRow[]) => void;
		biddingChartDetailsForm: BiddingChartDetailsForm;
		auctionLotDetailsForm: AuctionLotDetailsForm;
		onChange: (form: BiddingChartDetailsForm) => void;
		bidderInfoEntitySelectedOptions: Record<
			string,
			OptionType | null | undefined
		>;
		bidderInfoEntityValues: Record<string, Writable<string> | undefined>;
		auctionClientSelectedOptions: Record<string, OptionType | null | undefined>;
		auctionClientValues: Record<string, Writable<string> | undefined>;
		class?: string;
	}

	let {
		headers,
		activity,
		isSoldAuction,
		activityId,
		auctionClientValues = $bindable(),
		auctionClientSelectedOptions = $bindable(),
		bidderInfoEntityValues = $bindable(),
		bidderInfoEntitySelectedOptions = $bindable(),
		onChange,
		biddingChartDetailsForm,
		onChangeBidRows,
		bidderInfoRows = $bindable(),
		recordingInProgress = $bindable(),
		bidRows = $bindable(),
		winningPaddleNumberValue = $bindable(),
		auctionClients = $bindable(),
		onResetBids,
		changed = $bindable(),
		hasRecordedBids = $bindable(),
		showSaveBar = $bindable(),
		associatedEntities,
		onChangeAssociatedEntities,
		auctionLotDetailsForm,
		...rest
	}: Props = $props();

	const dataCy = 'bidding-chart';

	const BIDDING_PROGRESS_CHART_CONTAINER_ID =
		'bidding-progress-chart-container';

	let deletedBids: { bid: BidRow; index: number }[] = $state([]);
	let hammerTimestampBlur = $state(false);
	let editAuctionBidderPosition = $state(null) as string | null;
	let createNewAuctionBidderPosition = $state('');
	let auctioneerSelectedOption: OptionType | null = $state(
		biddingChartDetailsForm.auctioneer
			? formatAuctioneer(
					biddingChartDetailsForm.auctioneer?.entity
						?.person as GetPeopleQuery['person'][number]
				)
			: null
	);

	let showBiddingProgressChart = $state(false);

	const handleInputChange =
		(field: keyof BiddingChartDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...biddingChartDetailsForm, [field]: value });
		};

	const handleAuctioneerChange = (e: {
		detail: {
			value: GetPeopleQuery['person'][number] | undefined | null;
		};
	}) => {
		const auctioneer = e.detail.value?.entity?.id
			? {
					entity: {
						id: e.detail.value?.entity?.id || '',
						person: {
							id: e.detail.value.id,
						},
					},
				}
			: null;

		onChange({
			...biddingChartDetailsForm,
			auctioneer,
		});
		return Promise.resolve();
	};

	const resetAndClearDialogStores = createDialog();

	const convertToSeconds = (timestamp: string | number | Date) => {
		return Math.floor(new Date(timestamp).getTime() / 1000);
	};

	const getInitialNotes = (bid: Bid) => {
		if (bid.notes) {
			return bid.notes;
		}

		if (bid.bidder) {
			return `${bid.bidder.bidder_type?.key?.slice(0, 1)}${
				bid.bidder.location_number || ''
			}`;
		}

		return '';
	};

	let bids = $derived(biddingChartDetailsForm.bids || []);

	const formatBid = (bid: Bid, i: number) => {
		const firstTimestampInSeconds =
			bids.length > 0 ? convertToSeconds(bids?.[0]?.timestamp) : 0;

		const currentBidTimestampInSeconds = convertToSeconds(bid?.timestamp);

		const timeElapsed =
			i === 0 ? 0 : currentBidTimestampInSeconds - firstTimestampInSeconds;
		const duration = dayjs.duration(timeElapsed * 1000, 'milliseconds');

		const formattedTime = duration.format('HH:mm:ss');
		const notes = getInitialNotes(bid);

		return {
			id: bid.id,
			formattedTime: bid?.timestamp === null ? '' : formattedTime,
			notes,
			amount: {
				...bids?.[i]?.amount,
				amount: `${bids?.[i]?.amount?.amount?.toFixed(2)}`,
				usd_amount: `${bids?.[i]?.amount?.usd_amount}`,
			},
			isHammerBid: !!bid.isHammerBid,
			isNew: false,
			isDeleted: false,
		} as BidRow;
	};

	const handleOpenResetAndClearDialog = () => {
		resetAndClearDialogStores.states.open.set(true);
	};

	$effect(() => {
		if (hasRecordedBids === null) {
			hasRecordedBids = !!biddingChartDetailsForm?.bids?.filter(
				(bid) => !bid.isHammerBid
			)?.length;
		}
	});

	$effect(() => {
		// Do not remove this dummy call. The whole effect is in a setTimeout to avoid unneeded effect runs.
		// This is a workaround to make the effect run only once when activityId changes.
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		((activityId) => {})(activityId);

		setTimeout(() => {
			gqlClient
				.request(
					GetAuctionClientDocument,
					{
						limit: -1,
						filter: {
							_and: [
								{
									auction: {
										id: { _eq: biddingChartDetailsForm?.auctionId },
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					headers
				)
				.then((response) => {
					auctionClients = response?.auction_client;
				});

			bidRows = bids
				.sort((bidA, bidB) => {
					const timeDiff =
						dayjs(bidA.timestamp).startOf('second').unix() -
						dayjs(bidB.timestamp).startOf('second').unix();

					if (timeDiff) {
						return timeDiff;
					}

					return (bidA.amount?.amount || 0) - (bidB.amount?.amount || 0);
				})
				.map(formatBid);

			bidderInfoRows = bids.reduce(
				(accumulator: Record<string, BidderInfoRow | undefined>, bid) => {
					const bidderPosition = getPositionFromNotes(getInitialNotes(bid));
					if (accumulator[bidderPosition] || !bidderPosition) {
						return accumulator;
					}

					return {
						...accumulator,
						[bidderPosition]: {
							id: bid.bidder?.id,
							bidderPosition,
							bidderEntity: bid.bidder?.bidder
								? {
										id: bid.bidder?.bidder?.id,
										name: bid.bidder?.bidder?.name,
									}
								: null,
							auctionClient: bid.bidder?.client
								? {
										id: bid.bidder?.client?.id,
										entityName: bid.bidder?.client?.entity?.name,
										entityId: bid.bidder?.client?.entity?.id,
										paddleNumber: bid.bidder?.client?.paddle_number,
									}
								: null,
						} as BidderInfoRow,
					};
				},
				{}
			);

			const winnerPosition = getUniqueBidderPositions(bidRows).find(
				(uniqueBidderPosition) => isWinner(bidRows, uniqueBidderPosition)
			);

			if (winnerPosition) {
				winningPaddleNumberValue =
					bidderInfoRows[winnerPosition]?.auctionClient?.paddleNumber || '';
			}

			bidderInfoEntitySelectedOptions = bids.reduce(
				(accumulator: Record<string, OptionType | null>, bid) => {
					const bidderPosition = getPositionFromNotes(getInitialNotes(bid));
					if (bidderPosition && accumulator[bidderPosition]) {
						return accumulator;
					}

					return {
						...accumulator,
						[bidderPosition]: formatEntity(bid.bidder?.bidder),
					};
				},
				{}
			);

			auctionClientSelectedOptions = bids.reduce(
				(accumulator: Record<string, OptionType | null>, bid) => {
					const bidderPosition = getPositionFromNotes(getInitialNotes(bid));
					if (
						(bidderPosition && accumulator[bidderPosition]) ||
						!bid.bidder?.client
					) {
						return accumulator;
					}

					return {
						...accumulator,
						[bidderPosition]: {
							...formatAuctionClient(bid.bidder?.client),
							onClick: () => {
								editAuctionBidderPosition = bidderPosition;
							},
						} as OptionType,
					};
				},
				{}
			);

			auctionClientValues = bids.reduce(
				(accumulator: Record<string, Writable<string>>, bid) => {
					const bidderPosition = getPositionFromNotes(getInitialNotes(bid));
					if (bidderPosition && accumulator[bidderPosition]) {
						return accumulator;
					}

					return {
						...accumulator,
						[bidderPosition]: writable(''),
					};
				},
				{}
			);

			bidderInfoEntityValues = bids.reduce(
				(accumulator: Record<string, Writable<string>>, bid) => {
					const bidderPosition = getPositionFromNotes(getInitialNotes(bid));
					if (bidderPosition && accumulator[bidderPosition]) {
						return accumulator;
					}

					return {
						...accumulator,
						[bidderPosition]: writable(''),
					};
				},
				{}
			);
		}, 0);
	});

	let uniqueBidderPositions = $derived(getUniqueBidderPositions(bidRows));

	$effect(() => {
		// Do not remove this dummy call. The whole effect is in a setTimeout to avoid unneeded effect runs.
		// This is a workaround to make the effect run only once when uniqueBidderPositions changes.
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		((uniqueBidderPositions) => {})(uniqueBidderPositions);

		setTimeout(() => {
			// Creates a new entry when a new bidder position has been added
			uniqueBidderPositions.forEach((uniqueBidderPosition) => {
				if (!bidderInfoRows[uniqueBidderPosition]) {
					bidderInfoEntityValues[uniqueBidderPosition] = writable('');
					auctionClientValues[uniqueBidderPosition] = writable('');
					bidderInfoEntitySelectedOptions[uniqueBidderPosition] = null;
					auctionClientSelectedOptions[uniqueBidderPosition] = null;
					bidderInfoRows = {
						...bidderInfoRows,
						[uniqueBidderPosition]: {
							isDeleted: false,
							id: undefined,
							bidderPosition: uniqueBidderPosition,
							bidderEntity: null,
							auctionClient: null,
						},
					};
				} else if (bidderInfoRows[uniqueBidderPosition]?.isDeleted) {
					bidderInfoRows = {
						...bidderInfoRows,
						[uniqueBidderPosition]: {
							...bidderInfoRows[uniqueBidderPosition],
							isDeleted: false,
						} as BidderInfoRow,
					};
				}
			});

			// Remove entry when a bidder is not used anymore
			Object.keys(bidderInfoRows).forEach((bidderPosition) => {
				if (!uniqueBidderPositions.includes(bidderPosition)) {
					if (bidderInfoRows[bidderPosition]?.id) {
						bidderInfoRows = {
							...bidderInfoRows,
							[bidderPosition]: {
								...bidderInfoRows[bidderPosition],
								isDeleted: true,
							} as BidderInfoRow,
						};
					} else {
						bidderInfoEntityValues[bidderPosition] = undefined;
						auctionClientValues[bidderPosition] = undefined;
						bidderInfoEntitySelectedOptions[bidderPosition] = undefined;
						auctionClientSelectedOptions[bidderPosition] = undefined;
						bidderInfoRows = {
							...bidderInfoRows,
							[bidderPosition]: undefined,
						};
					}
				}
			});
		}, 0);
	});

	const handleToggleBiddingChart = () => {
		showBiddingProgressChart = !showBiddingProgressChart;

		if (showBiddingProgressChart) {
			setTimeout(() => {
				const element = document.querySelector(
					`#${BIDDING_PROGRESS_CHART_CONTAINER_ID}`
				);

				if (!element) return;

				const offset = 124;
				const elementPosition = element.getBoundingClientRect().top;
				const offsetPosition = elementPosition + window.scrollY - offset;

				window.scrollTo({
					top: offsetPosition,
					behavior: 'smooth',
				});
			}, 0);
		}
	};

	const handleClickUndeleteLastBid = () => {
		const lastBid = deletedBids.pop();

		onChangeBidRows([
			...bidRows.slice(0, lastBid?.index),
			lastBid?.bid,
			...bidRows.slice(lastBid?.index),
		] as BidRow[]);
	};
</script>

<AccordionItem
	{dataCy}
	title="Record bidding"
	class={twMerge('rounded-md border bg-white', rest.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="items-top flex flex-col lg:grid grid-cols-4 gap-4 px-4">
		<div>
			<Input
				dataCy={`${dataCy}-video-file`}
				name="video-file"
				placeholder="eg. Z: Auctions\Recorded Auctions{'\x5C'}2024\May 2024{'\x5C'}2024-05-13 Sotheby's"
				label="Video file"
				value={biddingChartDetailsForm?.video_file_name || ''}
				size="sm"
				class="mb-3"
				onkeyup={handleInputChange('video_file_name')}
			/>
		</div>

		<div>
			<InputLabel variant="label3" {dataCy} for="artists" class="mb-[8px]">
				Auctioneer
			</InputLabel>
			<div class="flex w-full">
				<AuctioneerAutoComplete
					{dataCy}
					class="mb-3 mr-1 w-full"
					onChange={handleAuctioneerChange}
					bind:selectedOption={auctioneerSelectedOption}
				/>
			</div>
		</div>

		<div class="mb-3">
			<Input
				dataCy={`${dataCy}-hammer-time-stamp`}
				name="hammer-time-stamp"
				placeholder="00:37:24"
				label="Hammer time stamp from video"
				value={biddingChartDetailsForm?.video_hammer_time_seconds || ''}
				size="sm"
				onblur={() => {
					hammerTimestampBlur = true;
				}}
				error={hammerTimestampBlur &&
				biddingChartDetailsForm?.video_hammer_time_seconds &&
				!isTimecodeValid(biddingChartDetailsForm?.video_hammer_time_seconds)
					? 'This timecode is not valid (must be hh:mm:ss)'
					: ''}
				onkeyup={handleInputChange('video_hammer_time_seconds')}
			/>
		</div>
	</div>

	<div class="my-4 grid-cols-3 gap-4 px-4 hidden lg:grid">
		<div class="col-span-2">
			<div class="flex items-center mb-3">
				<InputLabel variant="label3" {dataCy} required>Bid table</InputLabel>
				{#if deletedBids.length}
					<Button
						onclick={handleClickUndeleteLastBid}
						dataCy="undo-deleted-bid"
						variant="secondary"
						size="sm"
					>
						{#snippet leading()}
							<RefreshIcon />
						{/snippet}

						un-delete last deleted bid
					</Button>
				{/if}
			</div>
			<BidTable
				bind:deletedBids
				bind:recordingInProgress
				onOpenResetAndClearDialog={handleOpenResetAndClearDialog}
				{auctionClients}
				{bidderInfoRows}
				{biddingChartDetailsForm}
				bind:winningPaddleNumberValue
				bind:hasRecordedBids
				{bidRows}
				{onChangeBidRows}
				onToggleBiddingChart={handleToggleBiddingChart}
				{showBiddingProgressChart}
			/>
			<InputLabel
				variant="label3"
				{dataCy}
				for="artists"
				required
				class="mb-3 mt-4"
			>
				Underbidders
			</InputLabel>
			<Underbidders {onChangeAssociatedEntities} {associatedEntities} />
		</div>
		<div class="col-span-1">
			<InputLabel variant="label3" {dataCy} for="artists" required class="mb-3">
				Bidder client info
			</InputLabel>
			<ClientInfo
				{bidRows}
				bind:createNewAuctionBidderPosition
				{biddingChartDetailsForm}
				{uniqueBidderPositions}
				bind:bidderInfoRows
				bind:showSaveBar
				bind:changed
				bind:editAuctionBidderPosition
				bind:bidderInfoEntitySelectedOptions
				bind:auctionClientSelectedOptions
				{bidderInfoEntityValues}
				{auctionClientValues}
			/>
			<BidderPositionGuide />
		</div>
	</div>

	<div class="flex justify-center py-4 lg:hidden">
		<Button
			dataCy="toggle-show-bidding-chart"
			variant="secondary"
			size="sm"
			onclick={handleToggleBiddingChart}
		>
			{#if showBiddingProgressChart}
				Hide
			{:else}
				Show
			{/if}

			bidding chart
		</Button>
	</div>

	<div class="p-4 lg:hidden">
		<div class="border border-blue-500 p-3 rounded-md">
			<Txt variant="body3"
				>To edit or record bids open this page on a desktop device.</Txt
			>
		</div>
	</div>

	{#if showBiddingProgressChart}
		<div class="p-4" id={BIDDING_PROGRESS_CHART_CONTAINER_ID}>
			<BiddingProgressChart
				{bidRows}
				{isSoldAuction}
				{bidderInfoRows}
				auctionLotDetails={{
					auctionLotId: biddingChartDetailsForm.auctionLotId || '',
					lowEstimate:
						activity?.artwork_listing?.[0]?.price_low_estimate?.amount || 0,
					highEstimate:
						activity?.artwork_listing?.[0]?.price_high_estimate?.amount || 0,
					currency:
						activity?.artwork_listing?.[0]?.price_high_estimate?.currency
							?.code || '',
					irrevocableBid: auctionLotDetailsForm.irrevocableBid,
					gauranteedLot: auctionLotDetailsForm.gauranteedLot,
					gauranteedAmount: Number(auctionLotDetailsForm.gauranteedAmount) || 0,
				}}
			/>
		</div>
	{/if}
</AccordionItem>

{#if biddingChartDetailsForm.auctionId}
	<CreateNewAuctionDialog
		bind:showSaveBar
		bind:changed
		bind:bidderInfoRows
		bind:editAuctionBidderPosition
		bind:auctionClients
		bind:auctionClientSelectedOptions
		bind:bidderPosition={createNewAuctionBidderPosition}
		auctionId={biddingChartDetailsForm.auctionId}
	/>

	<ResetAndClearDialog
		onSubmit={onResetBids}
		dialogStores={resetAndClearDialogStores}
	/>
{/if}

{#if editAuctionBidderPosition && biddingChartDetailsForm.auctionId}
	<EditAuctionClientDialog
		bind:bidderInfoRows
		bind:selectedOption={
			auctionClientSelectedOptions[editAuctionBidderPosition]
		}
		bind:auctionClients
		bind:editAuctionBidderPosition
		auctionId={biddingChartDetailsForm.auctionId}
	/>
{/if}
