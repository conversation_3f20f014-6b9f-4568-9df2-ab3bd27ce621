<script module lang="ts">
	export interface PrivateSaleGallery {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
	}

	export interface GalleryForm {
		gallery: PrivateSaleGallery;
	}
</script>

<script lang="ts">
	import { GalleryInput } from './GalleryInput';

	interface Props {
		galleryForm: GalleryForm;
		onChange: (galleryForm: GalleryForm) => void;
	}

	let { galleryForm, onChange }: Props = $props();

	const handleChange = (gallery: GalleryForm['gallery']) => {
		onChange({
			...galleryForm,
			gallery,
		});
	};
</script>

<div class="rounded-md border bg-white p-4 max-lg:overflow-x-auto">
	<div class="mb-4 min-w-[700px]">
		<GalleryInput onChange={handleChange} {galleryForm} />
	</div>
</div>
