<script lang="ts">
	import type { GalleryForm } from '../GalleryCard.svelte';
	import { GalleryAutocomplete } from './GalleryAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		galleryForm: GalleryForm;
		onChange: (gallery: GalleryForm['gallery']) => void;
	}

	let { galleryForm, onChange }: Props = $props();

	const dataCy = 'gallery';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			id,
			title,
			subTitle,
			url,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			title: '',
			subTitle: '',
			url: '',
			id: '',
		});
	};

	let selectedOption = $derived(
		!galleryForm.gallery.id
			? null
			: {
					line1: galleryForm.gallery.title || '',
					line2: galleryForm.gallery.subTitle || '',
					line3: galleryForm.gallery.url || '',
					line4: galleryForm.gallery.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="gallery" class="mb-2" required>
		Gallery
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<GalleryAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
		/>
	</div>
</div>
