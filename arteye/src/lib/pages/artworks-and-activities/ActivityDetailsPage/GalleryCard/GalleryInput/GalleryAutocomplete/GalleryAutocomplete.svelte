<script lang="ts" module>
	export const formatSeries = (
		gallery: GetGalleriesQuery['gallery'][number]
	) => {
		const url = `${Routes.Galleries}/${gallery?.id}`;
		const subTitle = gallery.organisation?.location?.name || '';

		return {
			line1: `${gallery.organisation?.name}`,
			line2: subTitle,
			line3: url,
			line4: `${gallery.id}`,
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetGalleriesQuery,
		GetGalleriesQueryVariables,
	} from '$lib/queries/__generated__/getGalleries.generated';
	import { GetGalleriesDocument } from '$lib/queries/__generated__/getGalleries.generated';

	const dataCy = 'gallery';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetGalleriesQueryVariables => {
		if (!value) return {};

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: {
				_and: [
					{ status: { key: { _neq: 'archived' } } },
					...(() => {
						if (!value) {
							return [];
						}

						if (isUuidValid(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [
							{
								organisation: {
									name: {
										_icontains: value,
									},
								},
							},
						];
					})(),
				],
			},
		};
	};

	const getOptions = (data: GetGalleriesQuery | undefined) => {
		return [...(data?.gallery || []).map(formatSeries)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={selectedOption.line2 ? `(${selectedOption.line2})` : ''}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="parent-series"
				dataCy={`${dataCy}-parent-series`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					gallery: [],
					gallery_aggregated: [],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetGalleriesDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults class="text-left" dataCy={`${dataCy}-parent-series`}>
						No results found.
					</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
