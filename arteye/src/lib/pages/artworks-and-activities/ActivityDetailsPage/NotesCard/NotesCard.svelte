<script lang="ts">
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';

	interface Props {
		notes: string;
		onChange: (notes: string) => void;
	}

	let { notes, onChange }: Props = $props();

	const dataCy = 'notes';

	const handleChange = (event: Event) => {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		onChange(value);
	};
</script>

<div class="rounded-md border bg-white p-4">
	<Input
		{dataCy}
		name="notes"
		placeholder=""
		label="Notes"
		value={notes}
		onkeyup={handleChange}
		size="sm"
		class="resize-y"
		rows={3}
		classes={{ wrapper: 'h-[calc(100%-24px)]' }}
	/>
</div>
