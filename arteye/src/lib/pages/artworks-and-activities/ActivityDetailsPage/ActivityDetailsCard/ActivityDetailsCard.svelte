<script module lang="ts">
	export interface ActivityDetailsForm {
		activityType: {
			key: string;
			name: string;
			predefined: boolean;
		};
		activityStatuses: ActivityStatusRowItem[];
		sourcePageUrl: string;
	}
</script>

<script lang="ts">
	import type { ActivityStatusRowItem } from './ActivityStatusTable';
	import { ActivityStatusTable } from './ActivityStatusTable';
	import ExternalIcon from '$global/assets/icons/ExternalIcon/ExternalIcon.svelte';
	import { Input } from '$global/components/Input';

	import { InputLabel } from '$global/components/InputLabel';
	import { Link } from '$global/components/Link';
	import type {
		SelectChangeEvent,
		SelectOption,
	} from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';
	import type { LayoutData } from '$routes/types';

	interface Props {
		artworkActivityTypes: NonNullable<
			LayoutData['user']
		>['dropdowns']['artworkActivityTypes'];
		activityDetailsForm: ActivityDetailsForm;
		statusOptions: SelectOption[];
		onChange: (form: ActivityDetailsForm) => void;
		activity: ActivityDetailsPageData['activity'];
	}

	let {
		artworkActivityTypes,
		activityDetailsForm,
		statusOptions,
		onChange,
		activity,
	}: Props = $props();

	let activityTypeOptions = $derived(
		(artworkActivityTypes || []).map<SelectOption>((type) => ({
			value: type.key || '',
			label: type.name || '',
		}))
	);

	const dataCy = 'activity-details';

	const handleActivityTypeChange = (event: SelectChangeEvent) => {
		const selectedOption = event.detail.selectedOption;

		onChange({
			...activityDetailsForm,
			activityType: {
				key: `${selectedOption?.value}`,
				name: `${selectedOption?.label}`,
				predefined: false,
			},
		});
	};

	const handleInputChange =
		(field: keyof Pick<ActivityDetailsForm, 'sourcePageUrl'>) =>
		(event?: Event | undefined) => {
			onChange({
				...activityDetailsForm,
				[field]: `${(event?.target as HTMLInputElement).value}`,
			});
		};

	const handleActivityStatusChange = (
		activityStatuses: ActivityStatusRowItem[]
	) => {
		onChange({
			...activityDetailsForm,
			activityStatuses,
		});
	};

	const getSourcePageUrl = (sourcePageUrl: string) => {
		if (sourcePageUrl.startsWith('http')) {
			return sourcePageUrl;
		}

		return `https://${sourcePageUrl}`;
	};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-4">Activity details</Txt>

	<div class="mb-4 flex flex-col lg:grid grid-cols-2 gap-4">
		<div class="col-span-1 mb-4 flex flex-col lg:grid grid-cols-2 gap-4">
			<div class="col-span-1">
				<Select
					ariaLabel="Activity type"
					dataCy={`${dataCy}-activity-type`}
					name="activityType"
					placeholder=""
					label="Activity type"
					value={activityDetailsForm?.activityType?.key}
					onchange={handleActivityTypeChange}
					options={activityTypeOptions}
					size="sm"
					disabled={!!activityDetailsForm?.activityType?.predefined}
					tooltip="tbc"
				/>
			</div>
			<div class="col-span-1">
				<div class="mb-2 flex items-center justify-between">
					<InputLabel
						variant="label3"
						dataCy={`${dataCy}-sourcePageUrl-input`}
						name="sourcePageUrl"
					>
						Source page URL
					</InputLabel>

					{#if activityDetailsForm?.sourcePageUrl}
						<Link
							href={getSourcePageUrl(activityDetailsForm?.sourcePageUrl)}
							target="_blank"
							rel="noopener noreferrer"
							data-cy={`${dataCy}-sourcePageUrl-view`}
							class="flex items-center gap-1"
						>
							<Txt variant="label3" class="text-blue-500">View</Txt>
							<ExternalIcon class="h-[18px] w-[18px]" color="blue-500" />
						</Link>
					{/if}
				</div>
				<Input
					dataCy={`${dataCy}-sourcePageUrl`}
					name="sourcePageUrl"
					value={activityDetailsForm?.sourcePageUrl}
					onkeyup={handleInputChange('sourcePageUrl')}
					onchange={handleInputChange('sourcePageUrl')}
					size="sm"
					tooltip="tbc"
				/>
			</div>
		</div>

		<div class="col-span-1 overflow-x-auto">
			<div class="min-w-[500px]">
				<ActivityStatusTable
					rows={activityDetailsForm?.activityStatuses || []}
					onChange={handleActivityStatusChange}
					{statusOptions}
				/>
			</div>
		</div>
	</div>
</div>
