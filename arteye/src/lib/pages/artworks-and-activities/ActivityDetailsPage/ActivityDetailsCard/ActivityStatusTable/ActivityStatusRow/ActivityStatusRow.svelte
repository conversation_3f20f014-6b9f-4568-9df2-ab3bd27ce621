<script lang="ts">
	import { ActivityStatusFieldName } from '..';
	import type { ActivityStatusRowItem, ActivityStatusHeader } from '..';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { Select } from '$global/components/Select';
	import type {
		SelectOption,
		SelectChangeEvent,
	} from '$global/components/Select';
	import { TableCell, TableRow } from '$global/components/Table';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';

	interface Props {
		index: number;
		row: ActivityStatusRowItem;
		statusOptions: SelectOption[];
		headers: ActivityStatusHeader[];
		onChange: (row: ActivityStatusRowItem) => void;
	}

	let { index, row, statusOptions, headers, onChange }: Props = $props();

	const dataCy = 'address-row';

	const handleDateChange = (event?: Event | undefined) => {
		onChange({
			...row,
			timestamp: (event?.target as HTMLInputElement).value,
		});
	};

	const handleTypeChange = (event: SelectChangeEvent) => {
		const selectedOption = statusOptions?.find(
			(entityContactDetailType) =>
				entityContactDetailType?.value === event.detail.value
		);

		onChange({
			...row,
			type: `${selectedOption?.label}`,
			typeKey: `${selectedOption?.value}`,
		});
	};

	const handleDelete = () => {
		onChange({
			...row,
			isDeleted: true,
		});
	};
</script>

{#if !row.isDeleted}
	<TableRow {index} {dataCy}>
		{#each headers as header}
			{#if header.fieldName === ActivityStatusFieldName.Type}
				<TableCell {dataCy} class="py-0">
					{#snippet custom()}
						<Select
							ariaLabel="Select a status"
							dataCy={`${dataCy}-status`}
							name="status"
							placeholder="Select a status"
							options={statusOptions}
							value={row.typeKey}
							size="sm"
							onchange={handleTypeChange}
						/>
					{/snippet}
				</TableCell>
			{:else if header.fieldName === ActivityStatusFieldName.Timestamp}
				<TableCell {dataCy} class="">
					{#snippet custom()}
						<Input
							dataCy={`${dataCy}-timestamp`}
							name="timestamp"
							placeholder=""
							onkeyup={handleDateChange}
							onchange={handleDateChange}
							value={row.timestamp}
							size="sm"
							type="date"
						/>
					{/snippet}
				</TableCell>
			{:else if header.fieldName === ActivityStatusFieldName.Action}
				<TableCell {dataCy} class="">
					{#snippet custom()}
						<div class="flex items-center justify-end gap-2">
							<UpdateHistoryTooltip updateHistory={row.updateHistory} />

							<Button
								onclick={handleDelete}
								dataCy={`${dataCy}-delete`}
								class="h-[2rem] w-[2rem] px-0"
								variant="secondary"
								size="xs"
							>
								<BinIcon class="h-3 w-3" />
							</Button>
						</div>
					{/snippet}
				</TableCell>
			{/if}
		{/each}
	</TableRow>
{/if}
