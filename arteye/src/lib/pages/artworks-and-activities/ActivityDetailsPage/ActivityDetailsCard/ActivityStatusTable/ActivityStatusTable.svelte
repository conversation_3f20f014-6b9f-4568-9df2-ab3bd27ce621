<script lang="ts">
	import cloneDeep from 'lodash.clonedeep';
	import { ActivityStatusRow } from './ActivityStatusRow';
	import {
		type ActivityStatusHeader,
		type ActivityStatusRowItem,
		ActivityStatusFieldName,
	} from './types';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { InputLabel } from '$global/components/InputLabel';
	import type { SelectOption } from '$global/components/Select';
	import {
		TableBody,
		TableHeader,
		TableHeaderRow,
	} from '$global/components/Table';

	interface Props {
		statusOptions: SelectOption[];
		rows: ActivityStatusRowItem[];
		onChange: (rows: ActivityStatusRowItem[]) => void;
	}

	let { statusOptions, rows, onChange }: Props = $props();

	let rowsForm = $derived(cloneDeep(rows));

	const headers: ActivityStatusHeader[] = [
		{
			fieldName: ActivityStatusFieldName.Type,
			title: 'Status type',
		},
		{
			fieldName: ActivityStatusFieldName.Timestamp,
			title: 'Date',
		},
		{
			fieldName: ActivityStatusFieldName.Action,
			title: '',
		},
	];

	const handleChange = (index: number) => (newRow: ActivityStatusRowItem) => {
		onChange(rowsForm.map((row, i) => (i === index ? newRow : row)));
	};

	const handleClickAdd = () => {
		onChange([
			{
				id: '',
				[ActivityStatusFieldName.Type]: '',
				[ActivityStatusFieldName.TypeKey]: '',
				[ActivityStatusFieldName.Timestamp]: '',
				isNew: true,
			},
			...rowsForm,
		]);
	};

	const dataCy = 'contact-details';
</script>

{#key rowsForm.length}
	<div>
		<div class="col-span-5 mb-2 flex justify-between">
			<InputLabel variant="label3" {dataCy} for="artists">
				Activity status
			</InputLabel>

			<InfoTooltip {dataCy} placement="top-end" content="Tooltip tbc" />
		</div>
		<table class="w-full table-fixed rounded-md bg-white">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName === ActivityStatusFieldName.Action}
						<TableHeader {dataCy}>
							{#snippet custom()}
								<div class="flex justify-end">
									<Button
										onclick={handleClickAdd}
										dataCy={`${dataCy}-add`}
										class="h-[2rem] w-[2rem] px-0"
										variant="secondary"
										size="xs"
									>
										<PlusIcon class="h-3 w-3" />
									</Button>
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy}>
							{header.title}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#each rowsForm as row, index}
					<ActivityStatusRow
						{statusOptions}
						{headers}
						{row}
						{index}
						onChange={handleChange(index)}
					/>
				{/each}
			</TableBody>
		</table>
	</div>
{/key}
