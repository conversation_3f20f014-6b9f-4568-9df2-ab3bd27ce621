import { type UpdateHistory } from '$lib/components/UpdateHistoryTooltip';

export enum ActivityStatusFieldName {
	Type = 'type',
	TypeKey = 'typeKey',
	Timestamp = 'timestamp',
	Action = 'action',
}

export interface ActivityStatusRowItem {
	id: string;
	[ActivityStatusFieldName.Type]: string;
	[ActivityStatusFieldName.TypeKey]: string;
	[ActivityStatusFieldName.Timestamp]: string;
	isDeleted?: boolean;
	isNew?: boolean;
	updateHistory?: UpdateHistory;
}

export interface ActivityStatusHeader {
	fieldName: ActivityStatusFieldName;
	title: string;
}
