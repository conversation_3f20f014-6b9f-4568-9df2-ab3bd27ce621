<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import duration from 'dayjs/plugin/duration';
	import utc from 'dayjs/plugin/utc';
	import { type Writable } from 'svelte/store';
	import type { ActivityDetailsForm } from './ActivityDetailsCard';
	import { ActivityDetailsCard } from './ActivityDetailsCard';
	import type { AssociatedArtworksForm } from './AssociatedArtworksCard';
	import { AssociatedArtworksCard } from './AssociatedArtworksCard';
	import { AssociatedEntitiesCard } from './AssociatedEntitiesCard';
	import type { AssociatedEntityRow } from './AssociatedEntitiesCard';
	import type { AuctionForm } from './AuctionCard';
	import { AuctionCard } from './AuctionCard';
	import type { AuctionLotDetailsForm } from './AuctionLotDetailsCard';
	import { AuctionLotDetailsCard } from './AuctionLotDetailsCard';
	import type { BidderInfoRow, BidRow } from './BiddingChart/types';
	import { getBidderInfo } from './BiddingChart/utils/getBidderInfo/getBidderInfo';
	import { getPositionFromNotes } from './BiddingChart/utils/getPositionFromNotes/getPositionFromNotes';
	import { isManualPaddleNumberValid } from './BiddingChart/utils/isManualPaddleNumberValid/isManualPaddleNumberValid';
	import type { ExhibitionForm } from './ExhibitionCard';
	import { ExhibitionCard } from './ExhibitionCard';
	import type { FairForm } from './FairCard';
	import { FairCard } from './FairCard';
	import type { GalleryForm } from './GalleryCard';
	import { GalleryCard } from './GalleryCard';
	import { NotesCard } from './NotesCard';
	import { OtherInformationCard } from './OtherInformationCard';
	import type {
		AdditionalInfoImages,
		OtherDetailsForm,
		PrimaryInfoImage,
	} from './OtherInformationCard/OtherInformationCard.svelte';
	import type { PrivateSaleDetailsForm } from './PrivateSaleDetailsCard/PrivateSaleDetailsCard.svelte';
	import PrivateSaleDetailsCard from './PrivateSaleDetailsCard/PrivateSaleDetailsCard.svelte';
	import { ReferenceFilesCard } from './ReferenceFilesCard';
	import type { ReferenceFiles } from './ReferenceFilesCard/ReferenceFilesCard.svelte';
	import { SectionId } from './types';
	import { archiveArtworkActivity } from './utils/archiveArtworkActivity/archiveArtworkActivity';
	import { createActivity } from './utils/createActivity/createActivity';
	import { formatActivityDetailsForm } from './utils/formatActivityDetailsForm/formatActivityDetailsForm';
	import { formatAdditionalInfoImages } from './utils/formatAdditionalInfoImages/formatAdditionalInfoImages';
	import { formatAssociatedArtworksForm } from './utils/formatAssociatedArtworksForm/formatAssociatedArtworksForm';
	import { formatAuctionForm } from './utils/formatAuctionForm/formatAuctionForm';
	import { formatAuctionLotDetailsForm } from './utils/formatAuctionLotDetailsForm/formatAuctionLotDetailsForm';
	import { formatBiddingChartDetailsForm } from './utils/formatBiddingChartDetailsForm/formatBiddingChartDetailsForm';
	import { formatExhibitionForm } from './utils/formatExhibitionForm/formatExhibitionForm';
	import { formatFairForm } from './utils/formatFairForm/formatFairForm';
	import { formatGalleryForm } from './utils/formatGalleryForm/formatGalleryForm';
	import { formatOtherDetailsForm } from './utils/formatOtherDetailsForm/formatOtherDetailsForm';
	import { formatPrimaryInfoImage } from './utils/formatPrimaryInfoImage/formatPrimaryInfoImage';
	import { formatPrivateSaleDetailsForm } from './utils/formatPrivateSaleDetailsForm/formatPrivateSaleDetailsForm';
	import { formatPrivateSaleDetailsFormErrors } from './utils/formatPrivateSaleDetailsFormErrors/formatPrivateSaleDetailsFormErrors';
	import { formatReferenceFiles } from './utils/formatReferenceFiles/formatReferenceFiles';
	import { getActivityTypeKey } from './utils/getActivityTypeKey/getActivityTypeKey';
	import { getAssociatedEntities } from './utils/getAssociatedEntitiesRows/getAssociatedEntities';
	import { getCurrencyOptions } from './utils/getCurrencyOptions/getCurrencyOptions';
	import { getListingTypeKey } from './utils/getListingTypeKey/getListingTypeKey';
	import { mutateActivityDetails } from './utils/mutateActivityDetails/mutateActivityDetails';
	import { mutateAssociatedArtworks } from './utils/mutateAssociatedArtworks/mutateAssociatedArtworks';
	import { mutateAssociatedEntities } from './utils/mutateAssociatedEntities/mutateAssociatedEntities';
	import { mutateAuctionLotAttributes } from './utils/mutateAuctionLotAttributes/mutateAuctionLotAttributes';
	import { mutateAuctionLotDetails } from './utils/mutateAuctionLotDetails/mutateAuctionLotDetails';
	import { mutateAuctionLotListing } from './utils/mutateAuctionLotListing/mutateAuctionLotListing';
	import { mutateExhibitionListing } from './utils/mutateExhibitionListing/mutateExhibitionListing';
	import { mutateFairListing } from './utils/mutateFairListing/mutateFairListing';
	import { mutateGalleryListing } from './utils/mutateGalleryListing/mutateGalleryListing';
	import { mutateImages } from './utils/mutateImages/mutateImages';
	import { mutateOtherDetails } from './utils/mutateOtherDetails/mutateOtherDetails';
	import { mutatePrivateSaleDetails } from './utils/mutatePrivateSaleDetails/mutatePrivateSaleDetails';
	import { mutateReferenceFiles } from './utils/mutateReferenceFiles/mutateReferenceFiles';
	import { goto, preloadData } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Tabs } from '$global/components/Tabs';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { isPriceValid } from '$global/utils/isPriceValid/isPriceValid';
	import { isTimecodeValid } from '$global/utils/isTimecodeValid/isTimecodeValid';

	import { ConfirmArchiveDialog } from '$lib/components/ConfirmArchiveDialog';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { InfoLabel } from '$lib/components/InfoLabel';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import type {
		ActivitySearchIdOnlyQuery,
		ArtworkActivityFragment,
	} from '$lib/custom-queries/__generated__/activitySearch.generated';
	import { UpdateAndReturnActivityDocument } from '$lib/custom-queries/__generated__/updateAndReturnActivity.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { BiddingChart } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart';
	import type { BiddingChartDetailsForm } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/BiddingChart/BiddingChart.svelte';
	import { getActivityHref } from '$lib/pages/artworks-and-activities/ArtworkDetailsPage/utils/getActivityHref/getActivityHref';
	import { getActivityDate } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults/NewActivityCard/utils/getActivityDate/getActivityDate';
	import { newGetSaleName } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/utils/newGetSaleName/newGetSaleName';
	import { type GetAuctionClientQuery } from '$lib/queries/__generated__/getAuctionClient.generated';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';
	import { ArtworkListingTypeKey } from '$lib/types/types';
	import { archiveBids } from '$lib/utils/archiveBids/archiveBids';
	import { deleteAssociations } from '$lib/utils/mutation-handlers/deleteAssociations/deleteAssociations';
	import { mutateBids } from '$lib/utils/mutation-handlers/mutateBids/mutateBids';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';
	// import { UpdateAndReturnArtworkDocument } from '$lib/custom-queries/__generated__/updateAndReturnArtwork.generated';

	dayjs.extend(duration);
	dayjs.extend(utc);

	let loading = $state(false);
	let showSaveBar = $state(false);
	let deleting = $state(false);
	let archivedBids: { id: string }[] = $state([]);
	let associatedEntities = $state([]) as AssociatedEntityRow[];
	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));
	let artworkListingTypes = $derived(
		data.user?.dropdowns.artworkListingTypes || []
	);
	let searchParams = $derived(page.url.searchParams);
	let isNewActivity = $derived(data.isNewActivity);
	let createActivityMeta = $derived(data.createActivityMeta);
	let pipelineSource = $derived(data.pipelineSource);
	let activity = $state(data.activity);
	let currencies = $derived(data.user?.dropdowns.currencies || []);
	let prevPagesPromise = $derived(data.prevPagesPromise);
	let nextPagesPromise = $derived(data.nextPagesPromise);
	let artworkActivityTypes = $derived(
		data.user?.dropdowns.artworkActivityTypes || []
	);
	let artworkActivityAssociationTypes = $derived(
		(data.user?.dropdowns?.artworkActivityAssociationTypes || []).filter(
			(artworkActivityAssociationType) =>
				(artworkActivityAssociationType.supported_activity_types || []).some(
					(supportedActivityType) =>
						supportedActivityType?.artwork_activity_type_key?.key ===
						activity?.type?.key
				)
		)
	);

	let artworkListing = $derived(activity?.artwork_listing?.[0]);

	$effect(() => {
		activity = data.activity;
	});

	$effect(() => {
		archivedBids = (artworkListing?.auction_lot?.archived_bids || [])?.map(
			(archivedBid) => ({ id: `${archivedBid?.id}` })
		);

		associatedEntities = getAssociatedEntities(activity?.associations);
	});

	let listingTypeKey = $derived(
		getListingTypeKey({
			isNewActivity,
			listingTypeKey: activity?.artwork_listing?.[0]?.listing_type?.key,
			searchParams,
		})
	);

	let activityTypeKey = $derived(
		getActivityTypeKey({
			isNewActivity,
			activityTypeKey: activity?.type?.key,
			searchParams,
		})
	);

	let legacyId = $derived(data.legacyId);
	let activityId = $derived(activity?.id || '');
	let headers = $derived(getAuthorizationHeaders(data));
	let fairForm = $state(formatFairForm(page.data.activity)) as ReturnType<
		typeof formatFairForm
	>;

	$effect(() => {
		fairForm = formatFairForm(activity);
	});

	let galleryForm = $state(formatGalleryForm(page.data.activity)) as ReturnType<
		typeof formatGalleryForm
	>;

	$effect(() => {
		galleryForm = formatGalleryForm(activity);
	});

	let exhibitionForm = $state(
		formatExhibitionForm(page.data.activity)
	) as ReturnType<typeof formatExhibitionForm>;

	$effect(() => {
		exhibitionForm = formatExhibitionForm(activity);
	});

	let auctionForm = $state(formatAuctionForm(page.data.activity)) as ReturnType<
		typeof formatAuctionForm
	>;

	$effect(() => {
		auctionForm = formatAuctionForm(activity);
	});

	let associatedArtworksForm = $state(
		(() => {
			return formatAssociatedArtworksForm({
				artworks: page.data.activity?.artworks,
				createActivityMeta,
			});
		})()
	) as ReturnType<typeof formatAssociatedArtworksForm>;

	$effect(() => {
		associatedArtworksForm = formatAssociatedArtworksForm({
			artworks: activity?.artworks,
			createActivityMeta,
		});
	});

	let notes = $state(page.data.activity?.notes || '') as string;

	$effect(() => {
		notes = activity?.notes || '';
	});

	let activityDetailsForm = $state(
		formatActivityDetailsForm(page.data.activity)
	) as ReturnType<typeof formatActivityDetailsForm>;

	$effect(() => {
		activityDetailsForm = formatActivityDetailsForm(activity);
	});

	let privateSaleDetailsForm = $state(
		formatPrivateSaleDetailsForm(page.data.activity)
	) as ReturnType<typeof formatPrivateSaleDetailsForm>;

	$effect(() => {
		privateSaleDetailsForm = formatPrivateSaleDetailsForm(activity);
	});

	let privateSaleDetailsFormErrors = $state({
		knownPriceLocalCurrencyCode: '',
		lowEstimateLocalCurrencyCode: '',
		highEstimateLocalCurrencyCode: '',
		saleAmountLocalCurrencyCode: '',
	});

	$effect(() => {
		privateSaleDetailsFormErrors = formatPrivateSaleDetailsFormErrors(
			privateSaleDetailsForm
		);
	});

	let auctionLotDetailsForm = $state(
		formatAuctionLotDetailsForm(page.data.activity)
	) as ReturnType<typeof formatAuctionLotDetailsForm>;

	$effect(() => {
		auctionLotDetailsForm = formatAuctionLotDetailsForm(activity);
	});

	let otherDetailsForm = $state(
		formatOtherDetailsForm(page.data.activity)
	) as ReturnType<typeof formatOtherDetailsForm>;

	$effect(() => {
		otherDetailsForm = formatOtherDetailsForm(activity);
	});

	let artworkActivityStatusOptions = $derived(
		(data.user?.dropdowns?.artworkActivityStatusTypes || [])
			.filter((artworkActivityStatusType) =>
				(artworkActivityStatusType.supported_activity_types || []).some(
					(supportedActivityType) =>
						supportedActivityType?.artwork_activity_type_key?.key ===
						activity?.type?.key
				)
			)
			.map((type) => ({
				value: type.key || '',
				label: type.name || '',
			}))
	);

	let currencyOptions = $derived(getCurrencyOptions(currencies));

	let biddingChartDetailsForm = $state(
		formatBiddingChartDetailsForm(page.data.activity)
	) as ReturnType<typeof formatBiddingChartDetailsForm>;

	$effect(() => {
		biddingChartDetailsForm = formatBiddingChartDetailsForm(activity);
	});

	let bids = $derived(biddingChartDetailsForm?.bids || []);

	let primaryImage = $state(
		formatPrimaryInfoImage({
			accessToken: page.data.user.access_token,
			activity: page.data.activity,
		})
	) as ReturnType<typeof formatPrimaryInfoImage>;

	$effect(() => {
		primaryImage = formatPrimaryInfoImage({
			accessToken: page.data.user.access_token,
			activity,
		});
	});

	let additionalImages = $state(
		formatAdditionalInfoImages({
			accessToken: page.data.user.access_token,
			activity: page.data.activity,
		})
	) as ReturnType<typeof formatAdditionalInfoImages>;

	$effect(() => {
		additionalImages = formatAdditionalInfoImages({
			accessToken: page.data.user.access_token,
			activity,
		});
	});

	let referenceFiles = $state(
		formatReferenceFiles({
			accessToken: page.data.user.access_token,
			activity: page.data.activity,
		})
	) as ReturnType<typeof formatReferenceFiles>;

	$effect(() => {
		referenceFiles = formatReferenceFiles({
			accessToken: page.data.user.access_token,
			activity,
		});
	});

	let crumbLabel = $derived(
		(() => {
			switch (listingTypeKey) {
				case ArtworkListingTypeKey.Fair:
					return 'Fair activity';
				case ArtworkListingTypeKey.Gallery:
					return 'Gallery activity';
				case ArtworkListingTypeKey.Exhibition:
					return 'Exhibition activity';
				case ArtworkListingTypeKey.Auction:
					return 'Auction lot activity';
				case ArtworkListingTypeKey.Private:
					return 'Private sale activity';
				default:
					return 'Non-listing activity';
			}
		})()
	);

	let currentPageLabel = $derived(
		(() => {
			if (isNewActivity) return 'Create new activity';

			const activityDate = dayjs
				.utc(
					getActivityDate(
						activity as ArtworksAndActivitiesSearchPageData['activities'][number]
					)
				)
				.format('DD MMM YYYY');

			const saleName = newGetSaleName(activity as ArtworkActivityFragment);

			if (activityDate && saleName) {
				return `${activityDate} - ${saleName}`;
			}

			if (activityDate) {
				return activityDate;
			}

			if (saleName) {
				return saleName;
			}

			return 'Activity details';
		})()
	);

	let crumbs = $derived([
		{
			label: 'Artworks & activities',
			href: userRoutes.routes.basic_search,
		},
		{
			label: crumbLabel,
		},
		{
			label: currentPageLabel,
		},
	]);

	let activeTab = $state(0);

	let getTabs = $derived(() => {
		const hasReferenceFiles = referenceFiles?.filter((file) => !file.isDeleted);

		let tabs = [];

		if (listingTypeKey === ArtworkListingTypeKey.Auction) {
			tabs = [
				{
					id: SectionId.ActivityDetails,
					title: 'Activity details',
				},
				{
					id: SectionId.AuctionLotDetails,
					title: 'Auction lot details',
				},
				{
					id: SectionId.AssociatedEntities,
					title: 'Associated entities',
				},
				{
					id: SectionId.BiddingCharts,
					title: 'Record bidding',
				},
				{
					id: SectionId.OtherInformation,
					title: 'Other information',
				},
			];
		}

		if (
			listingTypeKey === ArtworkListingTypeKey.Fair ||
			listingTypeKey === ArtworkListingTypeKey.Gallery ||
			listingTypeKey === ArtworkListingTypeKey.Exhibition
		) {
			tabs = [
				{
					id: SectionId.ActivityDetails,
					title: 'Activity details',
				},
				{
					id: SectionId.PrivateSaleDetails,
					title: 'Private sale details',
				},
				{
					id: SectionId.AssociatedEntities,
					title: 'Associated entities',
				},
				{
					id: SectionId.OtherInformation,
					title: 'Other information',
				},
			];
		}

		tabs = [
			{
				id: SectionId.ActivityDetails,
				title: 'Activity details',
			},
			{
				id: SectionId.AssociatedEntities,
				title: 'Associated entities',
			},
			{
				id: SectionId.OtherInformation,
				title: 'Other information',
			},
		];

		if (hasReferenceFiles?.length) {
			tabs = [
				...tabs,
				{
					id: SectionId.ReferenceFiles,
					title: 'Reference files',
				},
			];
		}

		return tabs;
	});

	let tabs = $derived(getTabs());

	let changed = $state({
		biddingCharts: false,
		fairListing: false,
		galleryListing: false,
		exhibitionListing: false,
		auctionListing: false,
		associatedArtworks: false,
		associatedEntities: false,
		notes: false,
		activityDetails: false,
		privateSaleDetails: false,
		auctionLotDetails: false,
		otherDetails: false,
		images: false,
		referenceFiles: false,
	});

	// Bidding chart state
	let recordingInProgress = $state(false);
	let auctionClients: null | GetAuctionClientQuery['auction_client'] =
		$state(null);
	let winningPaddleNumberValue = $state('');
	let bidRows: BidRow[] = $state([]);
	let bidderInfoRows: Record<string, BidderInfoRow | undefined> = $state({});
	let bidderInfoEntitySelectedOptions: Record<
		string,
		OptionType | null | undefined
	> = $state({});
	let auctionClientSelectedOptions: Record<string, OptionType | null> = $state(
		{}
	);
	let auctionClientValues: Record<string, Writable<string> | undefined> = {};
	let bidderInfoEntityValues: Record<string, Writable<string> | undefined> = {};
	let hasRecordedBids: boolean | null = $state(null);

	let isAuction = $derived(listingTypeKey === ArtworkListingTypeKey.Auction);

	let isSoldAuction = $derived(
		biddingChartDetailsForm?.activity_status === 'SOLD' && isAuction
	);

	let showBiddingCharts = $derived(
		['SOLD', 'BOUGHT_IN'].includes(
			biddingChartDetailsForm?.activity_status as string
		) && isAuction
	);

	const dataCy = 'artwork-activity';

	const handleConfirmDelete = async () => {
		try {
			deleting = true;

			await archiveArtworkActivity({ id: activity?.id || '', headers });

			showToast({
				variant: 'success',
				message: 'Activity successfully deleted.',
			});

			goto(userRoutes.routes.basic_search);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong. Please try again or contact the support team.',
			});
		} finally {
			deleting = false;
		}
	};

	const handleClickDelete = () => {
		dialogStores.states.open.set(true);
	};

	const handleArchiveBids = async () => {
		if (showBiddingCharts) {
			return archiveBids({
				archivedBids,
				bidRows,
				bidderInfoRows,
				biddingChartDetailsForm,
				headers,
			});
		}

		return Promise.resolve();
	};

	const handleSaveBids = async () => {
		if (showBiddingCharts) {
			const paddleNumberCode = isManualPaddleNumberValid(
				bidRows,
				bidderInfoRows,
				auctionClients,
				winningPaddleNumberValue
			).code;

			return mutateBids({
				auctionClients,
				winningPaddleNumberValue,
				archivedBids,
				bidRows,
				paddleNumberCode: isSoldAuction ? paddleNumberCode : null,
				firstTimestamp: bids?.[0]?.timestamp,
				bidderInfoRows,
				biddingChartDetailsForm,
				headers,
			});
		}

		return Promise.resolve();
	};

	const handleSaveClick = async (saveBids: () => Promise<unknown>) => {
		if (listingTypeKey === ArtworkListingTypeKey.Fair) {
			const fairId = fairForm.fair.id;
			const exhibitorId = fairForm.exhibitor.id;

			if (!fairId) {
				showToast({
					variant: 'warning',
					message: `A fair is required.`,
				});

				return;
			}

			if (!exhibitorId) {
				showToast({
					variant: 'warning',
					message: `A fair exhibitor is required.`,
				});

				return;
			}
		}

		if (listingTypeKey === ArtworkListingTypeKey.Gallery) {
			const galleryId = galleryForm.gallery.id;

			if (!galleryId) {
				showToast({
					variant: 'warning',
					message: `A gallery is required.`,
				});

				return;
			}
		}

		if (listingTypeKey === ArtworkListingTypeKey.Exhibition) {
			const exhibitionId = exhibitionForm.exhibition.id;

			if (!exhibitionId) {
				showToast({
					variant: 'warning',
					message: `An exhibition is required.`,
				});

				return;
			}
		}

		try {
			loading = true;

			const requests: {
				fairListing: ReturnType<typeof mutateFairListing> | null;
				galleryListing: ReturnType<typeof mutateGalleryListing> | null;
				exhibitionListing: ReturnType<typeof mutateExhibitionListing> | null;
				auctionListing: ReturnType<typeof mutateAuctionLotListing> | null;
				associatedArtworks: ReturnType<typeof mutateAssociatedArtworks> | null;
				activityDetails: ReturnType<typeof mutateActivityDetails> | null;
				privateSaleDetails: ReturnType<typeof mutatePrivateSaleDetails> | null;
				associatedEntities: ReturnType<typeof mutateAssociatedEntities> | null;
				otherDetails: ReturnType<typeof mutateOtherDetails> | null;
				images: ReturnType<typeof mutateImages> | null;
				referenceFiles: ReturnType<typeof mutateReferenceFiles> | null;
				auctionLotDetails: ReturnType<typeof mutateAuctionLotDetails> | null;
				auctionLotAttributes: ReturnType<
					typeof mutateAuctionLotAttributes
				> | null;
				deleteAssociations: ReturnType<typeof deleteAssociations> | null;
				saveBids: ReturnType<typeof saveBids> | null;
			} = {
				fairListing: null,
				galleryListing: null,
				exhibitionListing: null,
				auctionListing: null,
				associatedArtworks: null,
				activityDetails: null,
				privateSaleDetails: null,
				associatedEntities: null,
				otherDetails: null,
				images: null,
				referenceFiles: null,
				auctionLotDetails: null,
				auctionLotAttributes: null,
				deleteAssociations: null,
				saveBids: null,
			};

			if (changed.fairListing) {
				requests.fairListing = mutateFairListing({
					fairListingId: activity?.artwork_listing?.[0]?.fair_listing?.id || '',
					headers,
					fairForm,
				});
			}

			if (changed.galleryListing) {
				requests.galleryListing = mutateGalleryListing({
					galleryListingId:
						activity?.artwork_listing?.[0]?.gallery_listing?.id || '',
					headers,
					galleryForm,
				});
			}

			if (changed.exhibitionListing) {
				requests.exhibitionListing = mutateExhibitionListing({
					exhibitionListingId:
						activity?.artwork_listing?.[0]?.exhibition_listing?.id || '',
					headers,
					exhibitionForm,
				});
			}

			if (changed.auctionListing) {
				requests.auctionListing = mutateAuctionLotListing({
					auctionLotId: activity?.artwork_listing?.[0]?.auction_lot?.id || '',
					headers,
					auctionForm,
				});
			}

			if (changed.associatedArtworks) {
				requests.associatedArtworks = mutateAssociatedArtworks({
					associatedArtworksForm,
					activityId,
					headers,
				});
			}

			if (changed.notes || changed.activityDetails) {
				requests.activityDetails = mutateActivityDetails({
					activityId,
					headers,
					activityDetailsForm,
					notes,
				});
			}

			requests.deleteAssociations = deleteAssociations({
				biddingChartDetailsForm,
				headers,
			});

			if (showBiddingCharts && changed.biddingCharts) {
				requests.saveBids = saveBids();
			}

			if (changed.associatedEntities) {
				requests.associatedEntities = mutateAssociatedEntities({
					activity,
					headers,
					associatedEntities,
				});
			}

			if (changed.otherDetails) {
				requests.otherDetails = mutateOtherDetails({
					activity,
					headers,
					otherDetailsForm,
				});
			}

			if (changed.images) {
				requests.images = mutateImages({
					activity,
					headers,
					primaryImage,
					additionalImages,
				});
			}

			if (
				listingTypeKey === ArtworkListingTypeKey.Fair ||
				listingTypeKey === ArtworkListingTypeKey.Gallery ||
				listingTypeKey === ArtworkListingTypeKey.Private ||
				listingTypeKey === ArtworkListingTypeKey.Exhibition
			) {
				if (changed.privateSaleDetails) {
					requests.privateSaleDetails = mutatePrivateSaleDetails({
						activity,
						headers,
						privateSaleDetailsForm,
					});
				}
			}

			if (
				listingTypeKey === ArtworkListingTypeKey.Auction &&
				changed.auctionLotDetails
			) {
				requests.auctionLotDetails = mutateAuctionLotDetails({
					activity,
					headers,
					auctionLotDetailsForm,
				});

				requests.auctionLotAttributes = mutateAuctionLotAttributes({
					activity,
					headers,
					auctionLotDetailsForm,
				});
			}

			if (changed.referenceFiles) {
				requests.referenceFiles = mutateReferenceFiles({
					activity,
					headers,
					referenceFiles,
				});
			}

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: 'Activity updated successfully.',
			});

			if (activity?.id) {
				// await fetch(`/api/clear-activity-cache?activityId=${activity.id}`, {
				// 	method: 'DELETE',
				// });

				await Promise.all([
					gqlClientCustom.request(
						UpdateAndReturnActivityDocument,
						{ activityIds: [activity.id] },
						headers
					),
					// gqlClientCustom.request(
					// 	UpdateAndReturnArtworkDocument,
					// 	{
					// 		artworks: Object.values(associatedArtworksForm.artworks).map(
					// 			(artwork) => artwork?.id
					// 		),
					// 	},
					// 	headers
					// ),
				]);
			}

			window.location.reload();
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while updating this activity. Please try again or contact the support team.',
			});

			loading = false;
			showSaveBar = false;
		}
	};

	const handleFairFormChange = (form: FairForm) => {
		fairForm = form;
		showSaveBar = true;
		changed.fairListing = true;
	};

	const handleGalleryFormChange = (form: GalleryForm) => {
		galleryForm = form;
		showSaveBar = true;
		changed.galleryListing = true;
	};

	const handleExhibitionFormChange = (form: ExhibitionForm) => {
		exhibitionForm = form;
		showSaveBar = true;
		changed.exhibitionListing = true;
	};

	const handleAuctionFormChange = (form: AuctionForm) => {
		auctionForm = form;
		showSaveBar = true;
		changed.auctionListing = true;
	};

	const handleAssociatedArtworksChange = (form: AssociatedArtworksForm) => {
		associatedArtworksForm = form;
		showSaveBar = true;
		changed.associatedArtworks = true;
	};

	const handleNotesChange = (_notes: string) => {
		notes = _notes;
		showSaveBar = true;
		changed.notes = true;
	};

	const handleActivityDetailsFormChange = (form: ActivityDetailsForm) => {
		activityDetailsForm = form;
		showSaveBar = true;
		changed.activityDetails = true;
	};

	const handlePrivateSaleDetailsFormChange = (form: PrivateSaleDetailsForm) => {
		privateSaleDetailsForm = form;
		showSaveBar = true;
		changed.privateSaleDetails = true;
	};

	const handleChangeBidsRows = (newBidsRows: BidRow[]) => {
		bidRows = newBidsRows;
		showSaveBar = true;
		changed.biddingCharts = true;
	};

	const handleBiddingChartDetailsFormChange = (
		newBiddingChartDetailsForm: BiddingChartDetailsForm
	) => {
		biddingChartDetailsForm = newBiddingChartDetailsForm;
		showSaveBar = true;
		changed.biddingCharts = true;
	};

	const handleAssociatedEntitiesChange = (rows: AssociatedEntityRow[]) => {
		associatedEntities = rows;
		changed.associatedEntities = true;
		showSaveBar = true;
	};

	const handleOtherDetailsFormChange = (form: OtherDetailsForm) => {
		otherDetailsForm = form;
		showSaveBar = true;
		changed.otherDetails = true;
	};

	const handlePrimaryImageChange = (image: PrimaryInfoImage) => {
		primaryImage = image;
		showSaveBar = true;
		changed.images = true;
	};

	const handleAdditionalImagesChange = (images: AdditionalInfoImages) => {
		additionalImages = images;
		showSaveBar = true;
		changed.images = true;
	};

	const handleAuctionLotDetailsFormChange = (form: AuctionLotDetailsForm) => {
		auctionLotDetailsForm = form;
		showSaveBar = true;
		changed.auctionLotDetails = true;
	};

	const handleReferenceFilesChange = (files: ReferenceFiles) => {
		referenceFiles = files;
		showSaveBar = true;
		changed.referenceFiles = true;
	};

	const handleTabClick = (index: number) => {
		const item = tabs[index];

		const element = document.querySelector(`#${item.id}`);

		if (!element) return;

		const offset = 124;
		const elementPosition = element.getBoundingClientRect().top;
		const offsetPosition = elementPosition + window.scrollY - offset;

		window.scrollTo({
			top: offsetPosition,
			behavior: 'smooth',
		});
	};

	let isSaveDisabled = $state(false);

	$effect(() => {
		isSaveDisabled = (() => {
			if (showBiddingCharts && changed.biddingCharts) {
				if (
					biddingChartDetailsForm?.video_hammer_time_seconds &&
					!isTimecodeValid(biddingChartDetailsForm?.video_hammer_time_seconds)
				) {
					return true;
				}

				if (
					bidRows
						.filter((bidRow) => !bidRow.isHammerBid)
						.find((bidRow) => {
							return (
								!getBidderInfo(getPositionFromNotes(bidRow.notes)) ||
								!bidRow.amount.amount ||
								!isPriceValid(bidRow.amount.amount)
							);
						})
				) {
					return true;
				}

				if (
					!bidRows.find((bidRow) => bidRow.isHammerBid) &&
					!!hasRecordedBids
				) {
					return true;
				}

				if (
					isSoldAuction &&
					(!!isManualPaddleNumberValid(
						bidRows,
						bidderInfoRows,
						auctionClients,
						winningPaddleNumberValue
					).message ||
						!winningPaddleNumberValue) &&
					!!hasRecordedBids
				) {
					return true;
				}

				if (recordingInProgress) {
					return true;
				}
			}

			if (Object.values(privateSaleDetailsFormErrors).some(Boolean)) {
				return true;
			}

			return deleting || loading;
		})();
	});

	let isCreateActivityDisabled = $derived(() => {
		let createDisabled = true;

		if (listingTypeKey === ArtworkListingTypeKey.Fair) {
			createDisabled = !fairForm.fair.id || !fairForm.exhibitor.id;
		}

		if (listingTypeKey === ArtworkListingTypeKey.Gallery) {
			createDisabled = !galleryForm.gallery.id;
		}

		if (listingTypeKey === ArtworkListingTypeKey.Exhibition) {
			createDisabled = !exhibitionForm.exhibition.id;
		}

		if (listingTypeKey === ArtworkListingTypeKey.Auction) {
			createDisabled = !auctionForm.auction.id;
		}

		if (listingTypeKey === ArtworkListingTypeKey.Private) {
			createDisabled = false;
		}

		if (!listingTypeKey) {
			createDisabled = false;
		}

		return loading || createDisabled;
	});

	const dialogStores = createDialog();

	const handleCreateActivityClick = async () => {
		try {
			loading = true;

			let newActivityId = '';

			const res = await createActivity({
				associatedArtworksForm,
				galleryForm,
				fairForm,
				exhibitionForm,
				auctionForm,
				activityTypeKey,
				listingTypeKey,
				artworkListingTypes,
				artworkActivityTypes,
				headers,
			});

			newActivityId = res.create_artwork_activity_item?.id || '';

			await gqlClientCustom.request(
				UpdateAndReturnActivityDocument,
				{ activityIds: [newActivityId] },
				headers
			);

			showToast({
				variant: 'success',
				message: 'Activity successfully created.',
			});

			goto(`${Routes.ActivityDetails}/${newActivityId}`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong. Please try again or contact the support team.',
			});
		} finally {
			loading = false;
		}
	};

	let allPagePromisesResolved = $state(false);

	$effect(() => {
		allPagePromisesResolved = false;

		if (nextPagesPromise) {
			nextPagesPromise.then(
				(nextPagesRes: ActivitySearchIdOnlyQuery | null) => {
					const nextArtworkActivity =
						nextPagesRes?.activitySearch?.activities?.[0]?.artwork_activity;

					if (nextArtworkActivity?.id) {
						preloadData(
							getActivityHref({
								artworkActivityId: `${nextArtworkActivity?.id}`,
								token: `${nextPagesRes?.activitySearch?.tokens?.[0]?.token}`,
							})
						);
					}

					allPagePromisesResolved = true;
				}
			);
		}
	});
</script>

<div
	class={classNames('pb-24', { 'pointer-events-none': loading || deleting })}
>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0 " />
		{#if activityId}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<div class="mr-2 flex items-center gap-1">
					{#if allPagePromisesResolved}
						{#await prevPagesPromise then prevPagesRes}
							{@const prevActivities = prevPagesRes?.activitySearch?.activities}
							{@const prevTokens = prevPagesRes?.activitySearch?.tokens}
							{@const artworkActivityId =
								prevActivities?.[prevActivities.length - 1]?.artwork_activity
									?.id}
							{#if artworkActivityId}
								<LinkButton
									href={getActivityHref({
										artworkActivityId: `${artworkActivityId}`,
										token: `${prevTokens?.[prevTokens.length - 1]?.token}`,
									})}
									dataCy={`${dataCy}-prev`}
									class="[&>button]:h-[2rem] [&>button]:w-[2rem] px-0"
									variant="secondary"
									disabled={loading || deleting}
									size="xs"
								>
									<ChevronLeftIcon class="h-3 w-3" />
								</LinkButton>
							{/if}
						{/await}

						{#await nextPagesPromise then nextPagesRes}
							{@const artworkActivityId = (() => {
								return nextPagesRes?.activitySearch?.activities?.[0]
									?.artwork_activity?.id;
							})()}
							{#if artworkActivityId}
								<LinkButton
									href={getActivityHref({
										artworkActivityId: `${artworkActivityId}`,
										token: `${nextPagesRes?.activitySearch?.tokens?.[0]?.token}`,
									})}
									dataCy={`${dataCy}-next`}
									class="[&>button]:h-[2rem] [&>button]:w-[2rem] px-0"
									variant="secondary"
									disabled={loading || deleting}
									size="xs"
								>
									<ChevronRightIcon class="h-3 w-3" />
								</LinkButton>
							{/if}
						{/await}
					{/if}
				</div>

				{#if legacyId}
					<InfoLabel title="Legacy ID" value={legacyId} />
				{/if}

				<Button
					onclick={() => {
						navigator.clipboard.writeText(activityId);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy activity id
				</Button>

				<Button
					onclick={handleClickDelete}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if !isNewActivity}
		<CreateUpdate
			class="mb-4 block mt-[-12px]"
			updateHistory={activity}
			{pipelineSource}
		/>
	{/if}

	<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
		<div
			class={classNames({
				'col-span-4': isNewActivity,
				'col-span-3': !isNewActivity,
			})}
		>
			<div class="mb-4">
				{#if listingTypeKey === ArtworkListingTypeKey.Fair}
					<FairCard {fairForm} onChange={handleFairFormChange} />
				{/if}

				{#if listingTypeKey === ArtworkListingTypeKey.Exhibition}
					<ExhibitionCard
						{exhibitionForm}
						onChange={handleExhibitionFormChange}
					/>
				{/if}

				{#if listingTypeKey === ArtworkListingTypeKey.Gallery}
					<GalleryCard {galleryForm} onChange={handleGalleryFormChange} />
				{/if}

				{#if listingTypeKey === ArtworkListingTypeKey.Auction}
					<AuctionCard {auctionForm} onChange={handleAuctionFormChange} />
				{/if}
			</div>
			<AssociatedArtworksCard
				{associatedArtworksForm}
				onChange={handleAssociatedArtworksChange}
				artworks={activity?.artworks}
			/>
		</div>

		{#if !isNewActivity}
			<div class="col-span-1 grid gap-4">
				<NotesCard {notes} onChange={handleNotesChange} />
			</div>
		{/if}
	</div>

	{#if isNewActivity}
		<div class="flex justify-end">
			<Button
				dataCy={`${dataCy}-save-new-activity`}
				variant="primary"
				size="md"
				onclick={handleCreateActivityClick}
				disabled={isCreateActivityDisabled()}
				{loading}
			>
				create activity
			</Button>
		</div>
	{/if}

	{#if !isNewActivity}
		<div class="sticky top-[60px] z-20 bg-gray-100 hidden lg:block">
			<Tabs
				classes={{ button: 'pr-5' }}
				{dataCy}
				bind:activeTab
				{tabs}
				fullLine
				onClickTab={handleTabClick}
			/>
		</div>

		<div class="mb-4" id={SectionId.ActivityDetails}>
			<ActivityDetailsCard
				{artworkActivityTypes}
				{activityDetailsForm}
				onChange={handleActivityDetailsFormChange}
				statusOptions={artworkActivityStatusOptions}
				{activity}
			/>
		</div>

		{#if listingTypeKey === ArtworkListingTypeKey.Fair || listingTypeKey === ArtworkListingTypeKey.Exhibition || listingTypeKey === ArtworkListingTypeKey.Gallery || listingTypeKey === ArtworkListingTypeKey.Private}
			<div class="mb-4" id={SectionId.PrivateSaleDetails}>
				<PrivateSaleDetailsCard
					{currencyOptions}
					{privateSaleDetailsForm}
					onChange={handlePrivateSaleDetailsFormChange}
					{privateSaleDetailsFormErrors}
				/>
			</div>
		{/if}

		{#if listingTypeKey === ArtworkListingTypeKey.Auction}
			<div class="mb-4" id={SectionId.AuctionLotDetails}>
				<AuctionLotDetailsCard
					{currencyOptions}
					{auctionLotDetailsForm}
					onChange={handleAuctionLotDetailsFormChange}
				/>
			</div>
		{/if}

		<div class="mb-4" id={SectionId.AssociatedEntities}>
			<AssociatedEntitiesCard
				{associatedEntities}
				onChange={handleAssociatedEntitiesChange}
				associationTypes={artworkActivityAssociationTypes}
			/>
		</div>

		<div class="mb-8" id={SectionId.OtherInformation}>
			<OtherInformationCard
				{otherDetailsForm}
				onChange={handleOtherDetailsFormChange}
				onPrimaryImageChange={handlePrimaryImageChange}
				onAdditionalImagesChange={handleAdditionalImagesChange}
				{primaryImage}
				{additionalImages}
				listingType={listingTypeKey}
			/>
		</div>

		{#if showBiddingCharts}
			<div class="mb-4" id={SectionId.BiddingCharts}>
				<BiddingChart
					bind:recordingInProgress
					bind:bidRows
					bind:auctionClients
					bind:showSaveBar
					bind:changed
					bind:hasRecordedBids
					onResetBids={() => {
						changed.biddingCharts = true;
						handleSaveClick(handleArchiveBids);
						return Promise.resolve();
					}}
					bind:winningPaddleNumberValue
					bind:bidderInfoEntitySelectedOptions
					bind:auctionClientSelectedOptions
					{bidderInfoEntityValues}
					{auctionClientValues}
					{headers}
					bind:bidderInfoRows
					{activity}
					{activityId}
					{biddingChartDetailsForm}
					{auctionLotDetailsForm}
					onChangeBidRows={handleChangeBidsRows}
					onChange={handleBiddingChartDetailsFormChange}
					{associatedEntities}
					{isSoldAuction}
					onChangeAssociatedEntities={handleAssociatedEntitiesChange}
				/>
			</div>
		{/if}

		<div id={SectionId.ReferenceFiles}>
			<ReferenceFilesCard
				files={referenceFiles}
				onChange={handleReferenceFilesChange}
			/>
		</div>
	{/if}
</div>

{#if !isNewActivity}
	<PageSaveBar
		{loading}
		disabled={isSaveDisabled}
		visible={showSaveBar}
		onSaveClick={() => {
			handleSaveClick(handleSaveBids);
		}}
	/>
{/if}

<ConfirmArchiveDialog
	{dataCy}
	{dialogStores}
	onDelete={handleConfirmDelete}
	confirmationText="Are you sure you want to archive this activity?"
/>
