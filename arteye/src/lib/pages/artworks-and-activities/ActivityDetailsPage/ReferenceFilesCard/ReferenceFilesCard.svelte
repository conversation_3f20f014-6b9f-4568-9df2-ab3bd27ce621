<script module lang="ts">
	export type ReferenceFiles = ReturnType<typeof formatReferenceFiles>;
</script>

<script lang="ts">
	import type { formatReferenceFiles } from '../utils/formatReferenceFiles/formatReferenceFiles';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { UploadIcon } from '$global/assets/icons/UploadIcon';
	import { Button } from '$global/components/Button';
	import { Dropzone } from '$global/components/Dropzone';
	import { Link } from '$global/components/Link';
	import {
		TableActionCell,
		TableBody,
		TableCell,
		TableHeader,
		TableHeaderRow,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getSuperForm } from '$global/utils/getSuperForm';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	interface Props {
		files: ReferenceFiles;
		onChange: (files: ReferenceFiles) => void;
	}

	let { files, onChange }: Props = $props();

	const dataCy = 'reference-files';

	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));
	let dropzoneUrlDialogForm = $derived(data.dropzoneUrlDialogForm);

	let visibleFiles = $derived(files.filter((file) => !file.isDeleted));

	let showCard = $state((() => !!visibleFiles.length)()) as boolean;
	$effect(() => {
		showCard = !!visibleFiles.length;
	});
	let showUploadContainer = $state(false);

	const handleShowCardClick = () => {
		showCard = true;
		showUploadContainer = true;
	};

	const handleShowUploadContainerClick = () => {
		showUploadContainer = true;
	};

	const handleSubmitFile = async (uploaded: File[]) => {
		const file = uploaded[0];

		onChange([
			...files,
			{
				file,
				title: file.name,
				relationId: '',
				type: file.type,
				url: '',
				uploadUrlSource: '',
				filename_download: '',
				id: '',
				storage: '',
				isDeleted: false,
				isNew: true,
			},
		]);
	};

	const handleSubmitUrl = (uploadUrlSource: string) => {
		onChange([
			...files,
			{
				file: null,
				title: `Pending url upload - ${new Date().toISOString()}`,
				relationId: '',
				type: '',
				url: '',
				uploadUrlSource,
				filename_download: '',
				id: '',
				storage: '',
				isDeleted: false,
				isNew: true,
			},
		]);
	};

	const handleDeleteClick = (id: string) => () => {
		onChange(
			files.map((file) => {
				if (file.id === id) {
					return {
						...file,
						isDeleted: true,
					};
				}

				return file;
			})
		);
	};
</script>

{#if !showCard}
	<div class="flex justify-center">
		<Button
			size="sm"
			variant="secondary"
			dataCy={`${dataCy}-show-card`}
			onclick={handleShowCardClick}
		>
			{#snippet leading()}
				<UploadIcon />
			{/snippet}
			Upload Reference Files
		</Button>
	</div>
{/if}

{#if showCard}
	<div class="rounded-md border bg-white p-4">
		<Txt variant="h5" class="mb-6">Reference files</Txt>

		{#if showUploadContainer}
			<Dropzone
				{dropzoneUrlDialogForm}
				{getSuperForm}
				multiple={false}
				accept={['application/pdf']}
				dataCy={`${dataCy}-files`}
				class="mb-6 max-w-[300px]"
				onSubmitFiles={handleSubmitFile}
				onSubmitUrl={handleSubmitUrl}
				showFiles={false}
			/>
		{:else}
			<Button
				size="sm"
				variant="secondary"
				dataCy={`${dataCy}-show-upload-container`}
				onclick={handleShowUploadContainerClick}
				class="mb-6"
			>
				{#snippet leading()}
					<UploadIcon />
				{/snippet}
				Upload Reference Files
			</Button>
		{/if}

		{#if !!visibleFiles.length}
			<div class="p-4 max-lg:overflow-x-scroll">
				<table class="w-full table-fixed rounded-md bg-white min-w-[600px]">
					<TableHeaderRow {dataCy}>
						<TableHeader {dataCy}>File Name</TableHeader>
						<TableHeader {dataCy}>File Format</TableHeader>
						<TableHeader {dataCy} />
					</TableHeaderRow>

					<TableBody {dataCy}>
						{#each visibleFiles as file, index}
							<TableRow {index} {dataCy}>
								<TableCell {dataCy}>{file.title}</TableCell>
								<TableCell {dataCy}>{file.type}</TableCell>
								<TableActionCell {dataCy}>
									<div class="flex w-full justify-end gap-4">
										{#if file.url}
											<Link
												href={file.url}
												rel="noopener noreferrer"
												target="_blank"
											>
												<Button
													size="sm"
													variant="secondary"
													dataCy={`${dataCy}-download`}
												>
													{#snippet leading()}
														<DownloadIcon />
													{/snippet}
													Download
												</Button>
											</Link>
										{/if}

										<Button
											onclick={handleDeleteClick(file.id || '')}
											dataCy={`${dataCy}-delete`}
											class="h-[2rem] w-[2rem] px-0"
											variant="secondary"
											size="xs"
										>
											<BinIcon class="h-3 w-3" />
										</Button>
									</div>
								</TableActionCell>
							</TableRow>
						{/each}
					</TableBody>
				</table>
			</div>
		{/if}
	</div>
{/if}
