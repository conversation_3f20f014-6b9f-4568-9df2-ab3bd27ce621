<script lang="ts">
	import type { FairForm } from '../FairCard.svelte';
	import { FairAutocomplete } from './FairAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		fairForm: FairForm;
		onChange: (fair: FairForm['fair']) => void;
	}

	let { fairForm, onChange }: Props = $props();

	const dataCy = ' parent-series';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			id,
			title,
			subTitle,
			url,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			title: '',
			subTitle: '',
			url: '',
			id: '',
		});
	};

	let selectedOption = $derived(
		!fairForm.fair.id
			? null
			: {
					line1: fairForm.fair.title || '',
					line2: fairForm.fair.subTitle || '',
					line3: fairForm.fair.url || '',
					line4: fairForm.fair.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="fair" class="mb-2" required>
		Fair
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<FairAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
		/>
	</div>
</div>
