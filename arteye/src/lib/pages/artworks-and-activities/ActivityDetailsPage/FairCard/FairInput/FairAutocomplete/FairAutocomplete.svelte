<script lang="ts" module>
	export const formatSeries = (fair: GetFairsQuery['fair'][number]) => {
		const url = `${Routes.Fairs}/${fair?.id}`;

		const getSubTitle = () => {
			if (fair?.venue_city?.name && fair?.venue_country?.name) {
				return `${fair.venue_city.name}, ${fair.venue_country.name}`;
			}

			if (fair?.venue_city?.name) {
				return `${fair.venue_city.name}`;
			}

			if (fair?.venue_country?.name) {
				return `${fair.venue_country.name}`;
			}

			return '';
		};

		return {
			line1: fair.title || '',
			line2: getSubTitle(),
			line3: url,
			line4: `${fair.id}`,
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetFairsQuery,
		GetFairsQueryVariables,
	} from '$lib/queries/__generated__/getFairs.generated';
	import { GetFairsDocument } from '$lib/queries/__generated__/getFairs.generated';

	const dataCy = 'fair';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetFairsQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: isUuid
				? { id: { _eq: value } }
				: {
						title: {
							_icontains: value,
						},
					},
		};
	};

	const getOptions = (data: GetFairsQuery | undefined) => {
		return [...(data?.fair || []).map(formatSeries)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={selectedOption.line2 ? `(${selectedOption.line2})` : ''}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="parent-series"
				dataCy={`${dataCy}-parent-series`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					fair: [],
					fair_aggregated: [],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetFairsDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults class="text-left" dataCy={`${dataCy}-parent-series`}>
						No results found.
					</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
