<script module lang="ts">
	export interface PrivateSaleFair {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
	}

	export interface PrivateSaleFairExhibitor {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
	}

	export interface FairForm {
		fair: PrivateSaleFair;
		exhibitor: PrivateSaleFairExhibitor;
	}
</script>

<script lang="ts">
	import { FairExhibitorInput } from './FairExhibitorInput';
	import { FairInput } from './FairInput';

	interface Props {
		fairForm: FairForm;
		onChange: (galleryForm: FairForm) => void;
	}

	let { fairForm, onChange }: Props = $props();

	const dataCy = 'private-sale';

	const handleFairChange = (fair: FairForm['fair']) => {
		onChange({
			...fairForm,
			fair,
			exhibitor: {
				id: '',
				title: '',
				subTitle: '',
				url: '',
			},
		});
	};

	const handleExhibitorChange = (exhibitor: FairForm['exhibitor']) => {
		onChange({
			...fairForm,
			exhibitor,
		});
	};
</script>

<div class="rounded-md border bg-white p-4 max-lg:overflow-x-auto">
	<div class="mb-4 min-w-[700px]">
		<FairInput onChange={handleFairChange} {fairForm} />
	</div>

	<div class="mb-4 min-w-[700px]">
		<FairExhibitorInput onChange={handleExhibitorChange} {fairForm} />
	</div>
</div>
