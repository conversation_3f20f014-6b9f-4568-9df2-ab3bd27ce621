<script lang="ts" module>
	export const formatSeries = (
		exhibitor: GetFairExhibitorsQuery['fair_exhibitor'][number]
	) => {
		const url = `${Config.DirectusUrl}/admin/content/fair_exhibitor/${exhibitor?.id}`;

		const getSubTitle = () => {
			if (
				exhibitor.entity?.organisation?.location?.name &&
				exhibitor.entity?.organisation?.location?.country?.name
			) {
				return `${exhibitor.entity?.organisation?.location?.name}, ${exhibitor.entity?.organisation?.location?.country?.name}`;
			}

			if (exhibitor.entity?.organisation?.location?.name) {
				return `${exhibitor.entity?.organisation?.location?.name}`;
			}

			if (exhibitor.entity?.organisation?.location?.country?.name) {
				return `${exhibitor.entity?.organisation?.location?.country?.name}`;
			}

			return '';
		};

		return {
			line1: exhibitor.entity?.name || '',
			line2: getSubTitle(),
			line3: url,
			line4: `${exhibitor.id}`,
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Config } from '$lib/constants/config';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetFairExhibitorsQuery,
		GetFairExhibitorsQueryVariables,
	} from '$lib/queries/__generated__/getFairExhibitors.generated';
	import { GetFairExhibitorsDocument } from '$lib/queries/__generated__/getFairExhibitors.generated';
	import type { ActivityDetailsPageData } from '$routes/artworks-and-activities/activity/[id]/types';

	let data = $derived(getPageData<ActivityDetailsPageData>(page.data));

	const dataCy = 'exhibitor';

	interface Props {
		selectedOption?: OptionType | null;
		fairId: string;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, fairId, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetFairExhibitorsQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: isUuid
				? { id: { _eq: value } }
				: {
						_and: [
							{
								entity: {
									name: {
										_icontains: value,
									},
								},
							},
							{
								fair: {
									id: {
										_eq: fairId,
									},
								},
							},
						],
					},
		};
	};

	const getOptions = (data: GetFairExhibitorsQuery | undefined) => {
		return [...(data?.fair_exhibitor || []).map(formatSeries)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={selectedOption.line2 ? `(${selectedOption.line2})` : ''}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				disabled={!fairId}
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="parent-series"
				dataCy={`${dataCy}-parent-series`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					fair_exhibitor: [],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetFairExhibitorsDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet noResults()}
					<NoResults class="text-left" dataCy={`${dataCy}-parent-series`}>
						No exhibitors found.
					</NoResults>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
