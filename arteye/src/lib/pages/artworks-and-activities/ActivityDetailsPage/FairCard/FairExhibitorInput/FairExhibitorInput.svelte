<script lang="ts">
	import type { FairForm } from '../FairCard.svelte';
	import { FairExhibitorAutocomplete } from './FairExhibitorAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		fairForm: FairForm;
		onChange: (exhibitor: FairForm['exhibitor']) => void;
	}

	let { fairForm, onChange }: Props = $props();

	let fairId = $derived(fairForm.fair.id);

	const dataCy = 'fair-exhibitor';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			id,
			title,
			subTitle,
			url,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			title: '',
			subTitle: '',
			url: '',
			id: '',
		});
	};

	let selectedOption = $derived(
		!fairForm.exhibitor.id
			? null
			: {
					line1: fairForm.exhibitor.title || '',
					line2: fairForm.exhibitor.subTitle || '',
					line3: fairForm.exhibitor.url || '',
					line4: fairForm.exhibitor.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="exhibitor" class="mb-2" required>
		Fair exhibitor
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<FairExhibitorAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
			{fairId}
		/>
	</div>
</div>
