<script module lang="ts">
	export interface PrivateSaleExhibition {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
	}

	export interface ExhibitionForm {
		exhibition: PrivateSaleExhibition;
	}
</script>

<script lang="ts">
	import { ExhibitionInput } from './ExhibitionInput';

	interface Props {
		exhibitionForm: ExhibitionForm;
		onChange: (form: ExhibitionForm) => void;
	}

	let { exhibitionForm, onChange }: Props = $props();

	const handleFairChange = (exhibition: ExhibitionForm['exhibition']) => {
		onChange({
			...exhibitionForm,
			exhibition,
		});
	};
</script>

<div class="rounded-md border bg-white p-4 max-lg:overflow-x-auto">
	<div class="min-w-[700px]">
		<ExhibitionInput onChange={handleFairChange} {exhibitionForm} />
	</div>
</div>
