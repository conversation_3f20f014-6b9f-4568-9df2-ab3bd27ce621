<script lang="ts">
	import type { ExhibitionForm } from '../ExhibitionCard.svelte';
	import { ExhibitionAutocomplete } from './ExhibitionAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		exhibitionForm: ExhibitionForm;
		onChange: (exhibition: ExhibitionForm['exhibition']) => void;
	}

	let { exhibitionForm, onChange }: Props = $props();

	const dataCy = 'exhibition';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			id,
			title,
			subTitle,
			url,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			title: '',
			subTitle: '',
			url: '',
			id: '',
		});
	};

	let selectedOption = $derived(
		!exhibitionForm.exhibition.id
			? null
			: {
					line1: exhibitionForm.exhibition.title || '',
					line2: exhibitionForm.exhibition.subTitle || '',
					line3: exhibitionForm.exhibition.url || '',
					line4: exhibitionForm.exhibition.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="exhibition" class="mb-2" required>
		Exhibition
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<ExhibitionAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
		/>
	</div>
</div>
