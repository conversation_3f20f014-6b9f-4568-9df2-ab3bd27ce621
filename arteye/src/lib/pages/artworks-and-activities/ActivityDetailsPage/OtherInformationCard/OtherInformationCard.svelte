<script module lang="ts">
	export interface OtherDetailsForm {
		condition: string;
		provenance: string;
		exhibition: string;
		literature: string;
		shipping: string;
		seriesSize: string;
		rawArtworkDescription: string;
		ingestionNotes: string;
		isFullSet: boolean;
		isBundle: boolean;
		numberOfArtworks: string;
		updateHistory: UpdateHistory;
	}

	export type PrimaryInfoImage = ReturnType<typeof formatPrimaryInfoImage>;

	export type AdditionalInfoImages = ReturnType<
		typeof formatAdditionalInfoImages
	>;
</script>

<script lang="ts">
	import type { formatAdditionalInfoImages } from '../utils/formatAdditionalInfoImages/formatAdditionalInfoImages';
	import type { formatPrimaryInfoImage } from '../utils/formatPrimaryInfoImage/formatPrimaryInfoImage';
	import { ImagesCard } from './ImagesCard';
	import type { CheckboxValue } from '$global/components/Checkbox';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { UpdateHistory } from '$lib/components/UpdateHistoryTooltip';
	import { ArtworkListingTypeKey } from '$lib/types/types';

	interface Props {
		otherDetailsForm: OtherDetailsForm;
		onChange: (otherDetailsForm: OtherDetailsForm) => void;
		onPrimaryImageChange: (image: PrimaryInfoImage) => void;
		onAdditionalImagesChange: (images: AdditionalInfoImages) => void;
		primaryImage: PrimaryInfoImage;
		additionalImages: AdditionalInfoImages;
		listingType: string;
	}

	let {
		otherDetailsForm,
		onChange,
		onPrimaryImageChange,
		onAdditionalImagesChange,
		primaryImage,
		additionalImages,
		listingType,
	}: Props = $props();

	const dataCy = 'other-information';

	const handleInputChange =
		(
			field: keyof Pick<
				OtherDetailsForm,
				| 'condition'
				| 'provenance'
				| 'exhibition'
				| 'literature'
				| 'shipping'
				| 'seriesSize'
				| 'rawArtworkDescription'
				| 'ingestionNotes'
				| 'numberOfArtworks'
			>
		) =>
		(event?: Event | undefined) => {
			onChange({
				...otherDetailsForm,
				[field]: (event?.target as HTMLInputElement).value,
			});
		};

	const handleCheckboxChange =
		(field: keyof Pick<OtherDetailsForm, 'isBundle' | 'isFullSet'>) =>
		(checked: CheckboxValue) => {
			onChange({
				...otherDetailsForm,
				[field]: !!checked,
			});
		};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-4">Other information</Txt>

	<div class="mb-4 flex flex-col lg:grid grid-cols-3 gap-4">
		<div class="col-span-1">
			<ImagesCard
				{onPrimaryImageChange}
				{onAdditionalImagesChange}
				{primaryImage}
				{additionalImages}
			/>
		</div>
		<div class="col-span-2 flex flex-col lg:grid grid-cols-3 gap-4">
			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-condition`}
					name="condition"
					placeholder=""
					label="Condition"
					rows={5}
					value={otherDetailsForm?.condition}
					onkeyup={handleInputChange('condition')}
					size="sm"
					class="resize-y"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-provenance`}
					name="provenance"
					placeholder=""
					label="Provenance"
					rows={5}
					value={otherDetailsForm?.provenance}
					onkeyup={handleInputChange('provenance')}
					size="sm"
					class="resize-y"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-exhibition`}
					name="exhibition"
					placeholder=""
					label="Exhibition"
					rows={5}
					value={otherDetailsForm?.exhibition}
					onkeyup={handleInputChange('exhibition')}
					size="sm"
					class="resize-y"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-literature`}
					name="literature"
					placeholder=""
					label="Literature"
					rows={5}
					value={otherDetailsForm?.literature}
					onkeyup={handleInputChange('literature')}
					size="sm"
					class="resize-y"
				/>
			</div>

			{#if [ArtworkListingTypeKey.Auction, ArtworkListingTypeKey.Exhibition, ArtworkListingTypeKey.Fair, ArtworkListingTypeKey.Gallery, ArtworkListingTypeKey.Private].includes(listingType as ArtworkListingTypeKey)}
				<div class="col-span-1">
					<Input
						dataCy={`${dataCy}-shipping`}
						name="shipping"
						placeholder=""
						label="Shipping"
						rows={5}
						value={otherDetailsForm?.shipping}
						onkeyup={handleInputChange('shipping')}
						size="sm"
						class="resize-y"
					/>
				</div>
			{/if}

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-raw-artwork-description`}
					name="rawArtworkDescription"
					placeholder=""
					label="Raw Artwork Description"
					rows={5}
					value={otherDetailsForm?.rawArtworkDescription}
					onkeyup={handleInputChange('rawArtworkDescription')}
					size="sm"
					class="resize-y"
				/>
			</div>

			{#if listingType === ArtworkListingTypeKey.Private}
				<div class="col-span-1">
					<Input
						dataCy={`${dataCy}-ingestionNotes`}
						name="ingestionNotes"
						placeholder=""
						label="Ingestion notes"
						rows={5}
						value={otherDetailsForm?.ingestionNotes}
						onkeyup={handleInputChange('ingestionNotes')}
						size="sm"
						class="resize-y"
					/>
				</div>
			{/if}

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-series-size`}
					name="series_size"
					placeholder=""
					label="Series Size"
					value={otherDetailsForm?.seriesSize}
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleInputChange('seriesSize')}
					size="sm"
				/>
			</div>

			{#if listingType === ArtworkListingTypeKey.Private || listingType === ArtworkListingTypeKey.Auction}
				<div class="col-span-1 flex h-[60px] items-center">
					<InputLabel
						dataCy={`${dataCy}-isFullSet`}
						class="col-span-2"
						variant="body3"
					>
						<Checkbox
							dataCy={`${dataCy}-isFullSet`}
							name="isFullSet"
							class="mt-[-0.125rem]"
							checked={otherDetailsForm?.isFullSet}
							onChange={handleCheckboxChange('isFullSet')}
						/>

						<div class="pl-1.5">Is full set</div>
					</InputLabel>
				</div>

				<div class="col-span-1 flex h-[60px] items-center">
					<InputLabel
						dataCy={`${dataCy}-isBundle`}
						class="col-span-2"
						variant="body3"
					>
						<Checkbox
							dataCy={`${dataCy}-isBundle`}
							name="isBundle"
							class="mt-[-0.125rem]"
							checked={otherDetailsForm?.isBundle}
							onChange={handleCheckboxChange('isBundle')}
						/>

						<div class="pl-1.5">Is bundle</div>
					</InputLabel>
				</div>

				{#if otherDetailsForm?.isBundle}
					<div class="col-span-1">
						<Input
							dataCy={`${dataCy}-numberOfArtworks`}
							name="numberOfArtworks"
							label="No. of artworks"
							placeholder=""
							value={otherDetailsForm?.numberOfArtworks}
							onkeyup={handleInputChange('numberOfArtworks')}
							size="sm"
						/>
					</div>
				{/if}
			{/if}
		</div>
	</div>

	<div class="flex justify-end">
		<UpdateHistoryTooltip updateHistory={otherDetailsForm?.updateHistory} />
	</div>
</div>
