<script lang="ts">
	import type {
		AdditionalInfoImages,
		PrimaryInfoImage,
	} from '../OtherInformationCard.svelte';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Dropzone } from '$global/components/Dropzone';
	import { Tabs } from '$global/components/Tabs';
	import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';

	interface Props {
		onPrimaryImageChange: (image: PrimaryInfoImage) => void;
		onAdditionalImagesChange: (images: AdditionalInfoImages) => void;
		primaryImage: PrimaryInfoImage;
		additionalImages: AdditionalInfoImages;
	}

	let {
		onPrimaryImageChange,
		onAdditionalImagesChange,
		primaryImage,
		additionalImages,
	}: Props = $props();

	const tabs = [
		{
			id: '1',
			title: 'Profile image',
		},
		{
			id: '2',
			title: 'Additional images',
		},
	];

	let activeTab = $state(0);

	const dataCy = 'image-card';

	const handleClickDeleteImage = () => {
		onPrimaryImageChange({
			...primaryImage,
			isDeleted: true,
		});
	};

	const handleClickDeleteAdditionalImage = (id: string) => {
		onAdditionalImagesChange(
			additionalImages.map((additionalImage) =>
				additionalImage?.id === id
					? {
							...additionalImage,
							isDeleted: true,
						}
					: additionalImage
			)
		);
	};

	const handleSubmitPrimaryImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		onPrimaryImageChange({
			file: newFile,
			url: base64Image.url,
			filename_download: '',
			id: '',
			storage: '',
			isDeleted: false,
		});
	};

	const handleSubmitAdditionalImage = async (newFiles: File[]) => {
		try {
			const newFile = newFiles[0];
			const base64Image = await imageFileToBase64(newFile);

			onAdditionalImagesChange([
				...additionalImages,
				{
					file: newFile,
					url: base64Image.url,
					filename_download: '',
					id: '',
					storage: '',
					isDeleted: false,
					relationId: '',
				},
			]);
		} catch {
			// empty catch block
		}
	};
</script>

<div
	class="h-full max-h-[320px] overflow-y-scroll rounded-md border bg-white p-4 pt-0"
>
	<div class="overflow-x-auto w-full">
		<Tabs class="min-w-[280px]" {dataCy} {tabs} bind:activeTab />
	</div>

	{#if !activeTab}
		<div
			class="relative flex h-[200px] w-full items-center justify-center bg-gray-200 p-4"
		>
			{#if primaryImage?.url && !primaryImage?.isDeleted}
				<img class="max-h-full" src={primaryImage?.url} alt="profile" />

				<Button
					dataCy={`${dataCy}-delete-profile`}
					class="absolute bottom-2 right-2 h-[2rem] w-[2rem] px-0"
					variant="secondary"
					onclick={handleClickDeleteImage}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			{:else}
				<Dropzone
					multiple={false}
					accept={['image/jpeg', 'image/png']}
					dataCy={`${dataCy}`}
					onSubmitFiles={handleSubmitPrimaryImage}
					showFiles={false}
				/>
			{/if}
		</div>
	{:else}
		<div class="mb-4 grid grid-cols-3 gap-4">
			{#if additionalImages}
				{#each additionalImages as additionalImage}
					{#if !additionalImage?.isDeleted}
						<div
							class="relative flex flex-col border border-gray-200 pb-[100%]"
						>
							<div
								class="absolute left-0 top-0 flex h-full w-full items-center justify-center p-1"
							>
								<img
									src={additionalImage?.url}
									class="max-h-full max-w-full"
									alt="additional"
								/>

								<Button
									dataCy={`${dataCy}-delete-additional`}
									class="absolute bottom-1 right-1 h-[1.5rem] w-[1.5rem] px-0"
									variant="secondary"
									size="xs"
									onclick={() =>
										handleClickDeleteAdditionalImage(additionalImage?.id || '')}
								>
									<BinIcon class="h-3 w-3" />
								</Button>
							</div>
						</div>
					{/if}
				{/each}
			{/if}
		</div>
		<Dropzone
			multiple={false}
			accept={['image/jpeg', 'image/png']}
			dataCy={`${dataCy}`}
			onSubmitFiles={handleSubmitAdditionalImage}
			showFiles={false}
		/>
	{/if}
</div>
