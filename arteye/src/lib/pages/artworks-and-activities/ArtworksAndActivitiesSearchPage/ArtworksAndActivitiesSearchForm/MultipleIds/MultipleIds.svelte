<script module lang="ts">
	export interface MultipleIdsSearchFormFields {
		[MultipleIdsSearchParam.ActivityIds]?: string;
		[MultipleIdsSearchParam.ArtworkIds]?: string;
		[MultipleIdsSearchParam.Type]?: string;
	}
</script>

<script lang="ts">
	import {
		ArtActSearchType,
		MultipleIdsSearchParam,
	} from '../../constants/search';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Radio } from '$global/components/Radio';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { Routes } from '$lib/constants/routes';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';

	interface Props {
		formatParamString: (
			data: MultipleIdsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let { formatParamString }: Props = $props();

	const searchParams = page.url.searchParams;

	let activityIds = $state('');
	let artworkIds = $state('');
	let type = $state('');

	$effect(() => {
		activityIds = getDecodedSearchParam({
			searchParams,
			key: MultipleIdsSearchParam.ActivityIds,
		});
	});

	$effect(() => {
		artworkIds = getDecodedSearchParam({
			searchParams,
			key: MultipleIdsSearchParam.ArtworkIds,
		});
	});

	$effect(() => {
		type =
			getDecodedSearchParam({
				searchParams,
				key: MultipleIdsSearchParam.Type,
			}) || MultipleIdsSearchParam.ActivityIds;
	});

	let ids = $derived(
		type === MultipleIdsSearchParam.ActivityIds ? activityIds : artworkIds
	);

	let idsArray = $derived(ids?.split(',').map((id) => id.trim()));

	let validIds = $derived(idsArray?.filter((id) => isValidUUID(id)));
	let invalidIds = $derived(idsArray?.filter((id) => !isValidUUID(id)));
	let submitDisabled = $derived(!type || !ids?.length);

	const dataCyPrefix = 'search';

	const getParams = () => {
		if (validIds) {
			return {
				type,
				searchType: 'multipleIds',
				...(type === 'artworkIds'
					? {
							artworkIds: validIds,
						}
					: {
							activityIds: validIds,
						}),
			};
		}

		return {};
	};

	export const formatStateUrl = async (href: string | undefined) => {
		if (!href) {
			return;
		}

		const a = new URLSearchParams(`?${href?.split('?')[1]}`);
		const b = new URLSearchParams(
			`?${formatParamString(await getParams(), StringBoolean.True, true)}`
		);

		if (b.get('showResults') && !a.get('showResults')) {
			a.append('showResults', b.get('showResults') as string);
		}

		a.sort();
		b.sort();

		return formatParamString(
			await getParams(),
			`${a.toString() === b.toString()}` as StringBoolean,
			true
		);
	};

	const handleSearchClick = async () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		type = '';
		activityIds = '';
		artworkIds = '';
	};
</script>

<div>
	<div class="p-4">
		<div class="w-[20rem]">
			<InputLabel dataCy={dataCyPrefix} required class="mb-4"
				>Search type</InputLabel
			>
			<fieldset class="mb-8 flex flex-col gap-3 md:flex-row">
				<InputLabel dataCy={dataCyPrefix} class="w-36"
					><Radio
						bind:group={type}
						id="artworkIds"
						dataCy="artworkIds"
						name="artworkIds"
						value="artworkIds"
						size="md"
					/>Artwork Ids</InputLabel
				>

				<InputLabel dataCy={dataCyPrefix} class="w-36"
					><Radio
						bind:group={type}
						id="activityIds"
						dataCy="activityIds"
						name="activityIds"
						value="activityIds"
						size="md"
					/>Activity Ids</InputLabel
				>
			</fieldset>
		</div>
		{#if type === MultipleIdsSearchParam.ActivityIds}
			<Input
				labelVariant="label3"
				name="Ids"
				required
				rows={10}
				class="mb-2 w-[60rem] resize-none leading-[1rem]"
				dataCy={`${dataCyPrefix}-ids`}
				label="IDs"
				bind:value={activityIds}
			/>
		{:else}
			<Input
				labelVariant="label3"
				name="Ids"
				required
				rows={10}
				class="mb-2 w-[60rem] resize-none leading-[1rem]"
				dataCy={`${dataCyPrefix}-ids`}
				label="IDs"
				bind:value={artworkIds}
			/>
		{/if}
		<Txt variant="label3" class="text-red-500">
			{invalidIds && invalidIds?.length > 0
				? `${invalidIds?.length} Invalid IDs Detected: ${invalidIds.join(', ')}`
				: ''}
		</Txt>
		<Txt variant="label3" class="text-gray-500">
			{validIds && validIds?.length > 0
				? `Total Valid IDs: ${validIds?.length}`
				: ''}
		</Txt>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
			disabled={submitDisabled}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
