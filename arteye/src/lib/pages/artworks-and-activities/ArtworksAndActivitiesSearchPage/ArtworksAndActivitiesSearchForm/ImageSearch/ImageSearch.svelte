<script lang="ts">
	import { onMount } from 'svelte';
	import { searchImage } from '../../api/searchImage/searchImage';
	import { searchImageUrl } from '../../api/searchImageUrl/searchImageUrl';
	import {
		ArtActSearchType,
		type ImageUploadSearchParam,
	} from '../../constants/search';
	import {
		matchingImageIds,
		uploadedImage,
	} from '../../stores/imageSearchStore/imageSearchStore';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import { Dropzone } from '$global/components/Dropzone';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import { isValidUrl } from '$global/utils/isValidUrl/isValidUrl';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		onSubmit: (data: { image: string }) => void;
	}

	let { onSubmit }: Props = $props();

	let image = $state('');
	let files: DropzoneFile[] = $state([]);
	let loading = $state(false);
	let blurImageUrl = $state(false);
	let imagePreviewUrl = $state('');

	const accept = ['image/png', 'image/jpeg'];
	const maxSize = 100000000;

	const dataCyPrefix = 'search';

	let imageFile: File | null = $state(null);
	let imageUrl: string | null = $state(null);
	let error = $state('');

	const pageRoute = `${
		Routes.ArtworksAndActivities
	}?searchType=${encodeURIComponent(ArtActSearchType.ByImage)}`;

	const handleFileChange = (files: File[]) => {
		if (files && files[0]) {
			imageFile = files[0];
			image = files[0].name;
			uploadedImage.set(imageFile);

			imagePreviewUrl = URL.createObjectURL(files[0]);
		}
	};

	const handleSearchClick = async () => {
		loading = true;
		error = '';

		if (imageUrl) {
			try {
				const ids = await searchImageUrl(imageUrl);
				matchingImageIds.set(ids);

				onSubmit({
					image: imageUrl,
				});
			} catch (e) {
				console.error('Error uploading image:', e);
				matchingImageIds.set([]);
				error =
					'Something went wrong, please check the URL corresponds to an accessible image or contact the support team.';
			} finally {
				loading = false;
			}
		} else if (imageFile) {
			try {
				const ids = await searchImage(imageFile);
				matchingImageIds.set(ids);

				onSubmit({
					image,
				});
			} catch (e) {
				matchingImageIds.set([]);
				error = 'Something went wrong, please contact the support team.';
			} finally {
				loading = false;
			}
		}
	};

	const handleClearClick = () => {
		image = '';
		error = '';
		imageFile = null;
		imageUrl = null;
		imagePreviewUrl = '';
		uploadedImage.set(null);
		files = [];

		if (imagePreviewUrl) {
			URL.revokeObjectURL(imagePreviewUrl);
		}
	};

	let submitDisabled = $derived(
		(!imageFile && !imageUrl) || (!!imageUrl && !isValidUrl(imageUrl))
	);

	const handleRemoveFiles = () => {
		imageFile = null;
		imagePreviewUrl = '';
		if (imagePreviewUrl) {
			URL.revokeObjectURL(imagePreviewUrl);
		}
	};

	onMount(() => {
		if (
			page.url.searchParams.get('searchType') === 'byImage' &&
			typeof window !== 'undefined'
		) {
			uploadedImage.set(null);
			matchingImageIds.set([]);

			const currentUrl = new URL(window.location.href);
			const searchParams = currentUrl.searchParams;

			if ([...searchParams].length > 0) {
				currentUrl.search = '';
				goto(pageRoute, { replaceState: true });
			}
		}
	});

	onMount(() => {
		return () => {
			if (imagePreviewUrl) {
				URL.revokeObjectURL(imagePreviewUrl);
			}
		};
	});
</script>

<div>
	<div class="p-4">
		<div class="max-w-[25rem]">
			<Txt variant="body2" class="mb-4 mt-[-8px]">
				You can search by uploading an image or by entering an image URL. If
				both fields are provided, then only the image URL will be taken into
				account.
			</Txt>
			<InputLabel dataCy={dataCyPrefix} class="mb-1">Upload Image</InputLabel>

			{#if imagePreviewUrl}
				<div
					class="relative mb-4 h-[300px] bg-gray-100 rounded-lg shadow-sm overflow-hidden"
				>
					<img
						src={imagePreviewUrl}
						alt="Preview"
						class="rounded-lg shadow-sm w-full h-full object-contain"
					/>
					<Button
						dataCy="{`${dataCyPrefix}-remove`}}"
						size="sm"
						variant="secondary"
						class="absolute top-2 right-2 w-[32px]"
						onclick={handleRemoveFiles}
					>
						<CrossIcon />
					</Button>
				</div>
			{:else}
				<Dropzone
					{maxSize}
					bind:files
					dataCy={dataCyPrefix}
					class="col-span-2 mb-4"
					{accept}
					dropzoneUrlDialogForm={page.data.dropzoneUrlDialogForm}
					onSubmitFiles={handleFileChange}
					onRemoveFiles={handleRemoveFiles}
					showFiles={false}
				/>
			{/if}
		</div>
		<div class="relative max-w-[25rem] my-4">
			<hr />
			<Txt
				class="bg-gray-0 top-[-9px] left-[50%] translate-x-[-50%] mx-auto px-2 inline-block absolute"
				component="span"
				variant="body2">OR</Txt
			>
		</div>
		<div class="max-w-[25rem]">
			<InputLabel dataCy={dataCyPrefix} class="mb-1">Image URL</InputLabel>
			<Input
				placeholder="Image URL"
				dataCy={`${dataCyPrefix}-image-url`}
				name="url"
				bind:value={imageUrl}
				onblur={() => {
					blurImageUrl = true;
				}}
				error={!!imageUrl && !isValidUrl(imageUrl) && blurImageUrl
					? 'This URL is not valid'
					: ''}
			/>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
			disabled={submitDisabled}
			{loading}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>

	{#if error}
		<Txt variant="body3" class="text-red-500 ml-4 max-w-[25rem]">
			{error}
		</Txt>
	{/if}
</div>
