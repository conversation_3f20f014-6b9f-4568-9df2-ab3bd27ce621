<script module lang="ts">
	export interface AaAdvancedSearchFormFields {
		[ArtworkAdvancedSearchParam.Name]: string;
		[ArtworkAdvancedSearchParam.ResultsTab]: string;
		[ArtworkAdvancedSearchParam.Sort]: string[];
		[ArtworkAdvancedSearchParam.Title]: string;
		[ArtworkAdvancedSearchParam.Crid]: string;
		[ArtworkAdvancedSearchParam.Media]: string;
		[ArtworkAdvancedSearchParam.ExecutedYear]: string;
		[ArtworkAdvancedSearchParam.ExecutedYearRange]: string;
		[ArtworkAdvancedSearchParam.ArtworkType]: string[];
		[ArtworkAdvancedSearchParam.Height]: string;
		[ArtworkAdvancedSearchParam.HeightRange]: string;
		[ArtworkAdvancedSearchParam.Width]: string;
		[ArtworkAdvancedSearchParam.WidthRange]: string;
		[ArtworkAdvancedSearchParam.Depth]: string;
		[ArtworkAdvancedSearchParam.DepthRange]: string;
		[ArtworkAdvancedSearchParam.NumberOfPieces]: string;
		[ArtworkAdvancedSearchParam.NumberOfArtworks]: string;
		[ArtworkAdvancedSearchParam.HeniArtworkType]: string[];
		[ArtworkAdvancedSearchParam.IsHeniArtwork]: boolean;
		[ArtworkAdvancedSearchParam.IsBundle]: boolean;
		[ArtworkAdvancedSearchParam.IsFullSet]: boolean;
		[ArtworkAdvancedSearchParam.EditionSize]: string;
		[ArtworkAdvancedSearchParam.EditionSizeRange]: string;
		[ArtworkAdvancedSearchParam.EditionNumber]: string;
		[ArtworkAdvancedSearchParam.SeriesTitle]: string;
		[ArtworkAdvancedSearchParam.SeriesCrid]: string;
		[ArtworkAdvancedSearchParam.SeriesSize]: string;
		[ArtworkAdvancedSearchParam.SeriesSizeRange]: string;
		[ArtworkAdvancedSearchParam.IsUnlimitedEdition]: boolean;
		[ArtworkAdvancedSearchParam.ExcludeMultiples]: boolean;
		[ArtworkAdvancedSearchParam.CategoryTags]: string[];
		[ArtworkAdvancedSearchParam.SubjectTags]: string[];
		[ArtworkAdvancedSearchParam.ShowFavouriteArtists]: boolean;
		[ActivityAdvancedSearchParam.ActivityType]: string[];
		[ActivityAdvancedSearchParam.ListingType]: string[];
		[ActivityAdvancedSearchParam.ActivityStatus]: string[];
		[ActivityAdvancedSearchParam.ActivityDate]: string;
		[ActivityAdvancedSearchParam.ActivityDateRange]: string;
		[ActivityAdvancedSearchParam.ListingPrice]: string;
		[ActivityAdvancedSearchParam.ListingPriceRange]: string;
		[ActivityAdvancedSearchParam.SaleAmount]: string;
		[ActivityAdvancedSearchParam.SaleAmountRange]: string;
		[ActivityAdvancedSearchParam.Association]: string;
		[ActivityAdvancedSearchParam.AssociationType]: string;
		[ActivityAdvancedSearchParam.AuctionHouse]: string;
		[ActivityAdvancedSearchParam.AuctionName]: string;
		[ActivityAdvancedSearchParam.AuctionSaleName]: string;
		[ActivityAdvancedSearchParam.SaleNumber]: string;
		[ActivityAdvancedSearchParam.LotNumber]: string;
		[ActivityAdvancedSearchParam.AuctionType]: string[];
		[ActivityAdvancedSearchParam.FairName]: string;
		[ActivityAdvancedSearchParam.FairDate]: string;
		[ActivityAdvancedSearchParam.FairExhibitor]: string;
		[ActivityAdvancedSearchParam.FairDateRange]: string;
		[ActivityAdvancedSearchParam.GalleryName]: string;
		[ActivityAdvancedSearchParam.GalleryCity]: string;
		[ActivityAdvancedSearchParam.GalleryCountry]: string;
		[ActivityAdvancedSearchParam.ExhibitionName]: string;
		[ActivityAdvancedSearchParam.ExhibitionDate]: string;
		[ActivityAdvancedSearchParam.ExhibitionDateRange]: string;
		[ActivityAdvancedSearchParam.ExhibitionCity]: string;
		[ActivityAdvancedSearchParam.ExhibitionCountry]: string;
		[DataEntryAdvancedSearchParam.CreatedBy]: string;
		[DataEntryAdvancedSearchParam.CreatedDate]: string;
		[DataEntryAdvancedSearchParam.CreatedDateRange]: string;
		[DataEntryAdvancedSearchParam.UpdatedBy]: string;
		[DataEntryAdvancedSearchParam.UpdatedByDate]: string;
		[DataEntryAdvancedSearchParam.UpdatedByDateRange]: string;
		[DataEntryAdvancedSearchParam.Source]: string[];
		[OmittedItemsSearchParam.OmittedArtworkIds]: string;
		[OmittedItemsSearchParam.OmittedActivityIds]: string;
		[OmittedItemsSearchParam.HideItemsWithoutImages]: boolean;
	}
</script>

<script lang="ts">
	import {
		ArtworkAdvancedSearchParam,
		ActivityAdvancedSearchParam,
		DataEntryAdvancedSearchParam,
		OmittedItemsSearchParam,
	} from '../../constants/search';
	import { createDialog } from '@melt-ui/svelte';
	import { NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS } from '../../constants/sort';
	import { ArtistName } from '../components/ArtistName';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Accordion, AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { formatWithThousandSeparator } from '$global/components/Input/utils/formatWithThousandSeparator/formatWithThousandSeparator';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import {
		PARAM_SEPARATOR,
		STRING_LIST_SEPARATOR,
	} from '$lib/constants/params';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
		SEARCH_RANGE_PLACEHOLDERS_FULL,
		SearchRange,
	} from '$lib/constants/search-range-options';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getBooleanFromParam } from '$lib/utils/getBooleanFromParam/getBooleanFromParam';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { ArtworkAndActivitiesResultsTab } from '../../ArtworksResults';
	import { PresetDialog } from '../PresetDialog';

	interface Props {
		dataSourceOptions?: MultiSelectOption[];
		artworkTypeOptions?: MultiSelectOption[];
		activityTypeOptions?: MultiSelectOption[];
		activityStatusTypeOptions?: MultiSelectOption[];
		associationTypeOptions?: MultiSelectOption[];
		listingTypeOptions?: MultiSelectOption[];
		auctionTypeOptions?: MultiSelectOption[];
		categoryTagsOptions?: MultiSelectOption[];
		heniArtworkTypeOptions?: MultiSelectOption[];
		subjectTagsOptions?: MultiSelectOption[];
		formatParamString: (
			data: AaAdvancedSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let presetUrl = $state('');
	const presetDialogStores = createDialog();

	let {
		dataSourceOptions = [],
		artworkTypeOptions = [],
		activityTypeOptions = [],
		activityStatusTypeOptions = [],
		associationTypeOptions = [],
		listingTypeOptions = [],
		auctionTypeOptions = [],
		categoryTagsOptions = [],
		heniArtworkTypeOptions = [],
		subjectTagsOptions = [],
		formatParamString,
	}: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.Sort,
			NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.Sort,
			NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS
		);
	});

	// artwork information
	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Name,
		})
	);

	$effect(() => {
		name = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Name,
		});
	});

	let title = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Title,
		})
	);

	$effect(() => {
		title = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Title,
		});
	});

	let crid = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Crid,
		})
	);

	$effect(() => {
		crid = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Crid,
		});
	});

	let media = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Media,
		})
	);

	$effect(() => {
		media = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Media,
		});
	});

	let executedYear = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.ExecutedYear,
		})
	);

	$effect(() => {
		executedYear = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.ExecutedYear,
		});
	});

	let executedYearRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.ExecutedYearRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		executedYearRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.ExecutedYearRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let artworkType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ArtworkType,
			artworkTypeOptions
		)
	);

	$effect(() => {
		artworkType = findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ArtworkType,
			artworkTypeOptions
		);
	});

	let height = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Height,
		})
	);

	$effect(() => {
		height = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Height,
		});
	});

	let heightRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.HeightRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		heightRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.HeightRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let width = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Width,
		})
	);

	$effect(() => {
		width = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Width,
		});
	});

	let widthRange = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.WidthRange,
		})
	);

	$effect(() => {
		widthRange = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.WidthRange,
		});
	});

	let depth = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Depth,
		})
	);

	$effect(() => {
		depth = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.Depth,
		});
	});

	let depthRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.DepthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		depthRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.DepthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let numberOfPieces = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.NumberOfPieces,
		})
	);

	$effect(() => {
		numberOfPieces = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.NumberOfPieces,
		});
	});

	let numberOfArtworks = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.NumberOfArtworks,
		})
	);

	$effect(() => {
		numberOfArtworks = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.NumberOfArtworks,
		});
	});

	let isHeniArtwork = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsHeniArtwork
		)
	);

	$effect(() => {
		isHeniArtwork = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsHeniArtwork
		);
	});

	let isBundle = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsBundle
		)
	);

	$effect(() => {
		isBundle = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsBundle
		);
	});

	let isFullSet = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsFullSet
		)
	);

	$effect(() => {
		isFullSet = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsFullSet
		);
	});

	let heniArtworkType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.HeniArtworkType,
			heniArtworkTypeOptions
		)
	);

	$effect(() => {
		heniArtworkType = findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.HeniArtworkType,
			heniArtworkTypeOptions
		);
	});

	let editionSize = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionSize,
		})
	);

	$effect(() => {
		editionSize = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionSize,
		});
	});

	let editionSizeRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		editionSizeRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let editionNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionNumber,
		})
	);

	$effect(() => {
		editionNumber = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.EditionNumber,
		});
	});

	let seriesTitle = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesTitle,
		})
	);

	$effect(() => {
		seriesTitle = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesTitle,
		});
	});

	let seriesCrid = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesCrid,
		})
	);

	$effect(() => {
		seriesCrid = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesCrid,
		});
	});

	let seriesSize = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesSize,
		})
	);

	$effect(() => {
		seriesSize = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesSize,
		});
	});

	let seriesSizeRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		seriesSizeRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.SeriesSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let isUnlimitedEdition = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsUnlimitedEdition
		)
	);

	$effect(() => {
		isUnlimitedEdition = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.IsUnlimitedEdition
		);
	});

	let excludeMultiples = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ExcludeMultiples
		)
	);

	$effect(() => {
		excludeMultiples = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ExcludeMultiples
		);
	});

	let categoryTags: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.CategoryTags,
			categoryTagsOptions
		)
	);

	$effect(() => {
		categoryTags = findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.CategoryTags,
			categoryTagsOptions
		);
	});

	let subjectTags: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.SubjectTags,
			subjectTagsOptions
		)
	);

	$effect(() => {
		subjectTags = findMultiselectOptions(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.SubjectTags,
			subjectTagsOptions
		);
	});

	// activity information
	let activityType: MultiSelectOption[] = $state([]);
	$effect(() => {
		activityType = findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.ActivityType,
			activityTypeOptions
		);
	});

	let listingType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.ListingType,
			listingTypeOptions
		)
	);

	$effect(() => {
		listingType = findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.ListingType,
			listingTypeOptions
		);
	});

	let activityStatus: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.ActivityStatus,
			activityStatusTypeOptions
		)
	);

	$effect(() => {
		activityStatus = findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.ActivityStatus,
			activityStatusTypeOptions
		);
	});

	let activityDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ActivityDate,
		})
	);

	$effect(() => {
		activityDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ActivityDate,
		});
	});

	let activityDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ActivityDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		activityDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ActivityDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let listingPrice = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ListingPrice,
		})
	);

	$effect(() => {
		listingPrice = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ListingPrice,
		});
	});

	let listingPriceRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ListingPriceRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		listingPriceRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ListingPriceRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let saleAmount = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleAmount,
		})
	);

	$effect(() => {
		saleAmount = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleAmount,
		});
	});

	let saleAmountRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleAmountRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		saleAmountRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleAmountRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let association = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.Association,
		})
	);

	$effect(() => {
		association = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.Association,
		});
	});

	let associationType = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AssociationType,
		})
	);

	$effect(() => {
		associationType = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AssociationType,
		});
	});

	let auctionHouse = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionHouse,
		})
	);

	$effect(() => {
		auctionHouse = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionHouse,
		});
	});

	let auctionName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionName,
		})
	);

	$effect(() => {
		auctionName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionName,
		});
	});

	let auctionSaleName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionSaleName,
		})
	);

	$effect(() => {
		auctionSaleName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.AuctionSaleName,
		});
	});

	let saleNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleNumber,
		})
	);

	$effect(() => {
		saleNumber = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.SaleNumber,
		});
	});

	let lotNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.LotNumber,
		})
	);

	$effect(() => {
		lotNumber = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.LotNumber,
		});
	});

	let auctionType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.AuctionType,
			auctionTypeOptions
		)
	);

	$effect(() => {
		auctionType = findMultiselectOptions(
			page.url.searchParams,
			ActivityAdvancedSearchParam.AuctionType,
			auctionTypeOptions
		);
	});

	let fairName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairName,
		})
	);

	$effect(() => {
		fairName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairName,
		});
	});

	let fairDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairDate,
		})
	);

	$effect(() => {
		fairDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairDate,
		});
	});

	let fairExhibitor = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairName,
		})
	);

	$effect(() => {
		fairExhibitor = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairExhibitor,
		});
	});

	let fairDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		fairDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.FairDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let galleryName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryName,
		})
	);

	$effect(() => {
		galleryName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryName,
		});
	});

	let galleryCity = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryCity,
		})
	);

	$effect(() => {
		galleryCity = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryCity,
		});
	});

	let galleryCountry = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryCountry,
		})
	);

	$effect(() => {
		galleryCity = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.GalleryCountry,
		});
	});

	let exhibitionName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionName,
		})
	);

	$effect(() => {
		exhibitionName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionName,
		});
	});

	let exhibitionDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionDate,
		})
	);

	$effect(() => {
		exhibitionDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionDate,
		});
	});

	let exhibitionDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		exhibitionDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let exhibitionCity = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionCity,
		})
	);

	$effect(() => {
		exhibitionCity = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionCity,
		});
	});

	let exhibitionCountry = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionCountry,
		})
	);

	$effect(() => {
		exhibitionCountry = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ActivityAdvancedSearchParam.ExhibitionCountry,
		});
	});

	// data entry
	let createdBy = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedBy,
		})
	);

	$effect(() => {
		createdBy = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedBy,
		});
	});

	let createdDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedDate,
		})
	);

	$effect(() => {
		createdDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedDate,
		});
	});

	let createdDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		createdDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.CreatedDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let updatedBy = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedBy,
		})
	);

	$effect(() => {
		updatedBy = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedBy,
		});
	});

	let updatedByDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedByDate,
		})
	);

	let resultsTab = $derived(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtworkAdvancedSearchParam.ResultsTab,
		})
	);

	$effect(() => {
		updatedByDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedByDate,
		});
	});

	let updatedByDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedByDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		updatedByDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: DataEntryAdvancedSearchParam.UpdatedByDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let source: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			DataEntryAdvancedSearchParam.Source,
			dataSourceOptions
		)
	);
	let omittedActivityIds = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OmittedItemsSearchParam.OmittedActivityIds,
		})
			.split(PARAM_SEPARATOR)
			.join(STRING_LIST_SEPARATOR)
	);
	let omittedArtworkIds = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OmittedItemsSearchParam.OmittedArtworkIds,
		})
			.split(PARAM_SEPARATOR)
			.join(STRING_LIST_SEPARATOR)
	);

	let hideItemsWithoutImages = $state(
		getBooleanFromParam(
			page.url.searchParams,
			OmittedItemsSearchParam.HideItemsWithoutImages
		)
	);

	let showFavouriteArtists = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ShowFavouriteArtists
		)
	);

	$effect(() => {
		showFavouriteArtists = getBooleanFromParam(
			page.url.searchParams,
			ArtworkAdvancedSearchParam.ShowFavouriteArtists
		);
	});

	$effect(() => {
		source = findMultiselectOptions(
			page.url.searchParams,
			DataEntryAdvancedSearchParam.Source,
			dataSourceOptions
		);
	});

	const dataCyPrefix = 'search';

	const getValuesFromSelectOptions = (options: { value: string }[]) =>
		options.map((option) => option.value);

	const getParams = () => ({
		name,
		resultsTab,
		sort: getValuesFromSelectOptions(sort),
		title,
		crid,
		media,
		executedYear,
		executedYearRange,
		artworkType: getValuesFromSelectOptions(artworkType),
		height,
		heightRange,
		width,
		widthRange,
		depth,
		depthRange,
		numberOfPieces,
		numberOfArtworks,
		isHeniArtwork,
		isBundle,
		isFullSet,
		editionSize,
		editionSizeRange,
		editionNumber,
		seriesTitle,
		seriesCrid,
		seriesSize,
		seriesSizeRange,
		isUnlimitedEdition,
		excludeMultiples,
		heniArtworkType: getValuesFromSelectOptions(heniArtworkType),
		categoryTags: getValuesFromSelectOptions(categoryTags),
		subjectTags: getValuesFromSelectOptions(subjectTags),
		activityType: getValuesFromSelectOptions(activityType),
		listingType: getValuesFromSelectOptions(listingType),
		activityStatus: getValuesFromSelectOptions(activityStatus),
		activityDate,
		activityDateRange,
		listingPrice,
		listingPriceRange,
		saleAmount,
		saleAmountRange,
		association,
		associationType,
		auctionHouse,
		auctionNameOrId: auctionName,
		auctionSaleName,
		saleNumber,
		lotNumber,
		auctionType: getValuesFromSelectOptions(auctionType),
		fairName,
		fairDate,
		fairExhibitor,
		fairDateRange,
		galleryName,
		galleryCity,
		galleryCountry,
		exhibitionName,
		exhibitionDate,
		exhibitionDateRange,
		exhibitionCity,
		exhibitionCountry,
		createdBy,
		createdDate,
		createdDateRange,
		updatedBy,
		updatedByDate,
		updatedByDateRange,
		source: getValuesFromSelectOptions(source),
		omittedActivityIds,
		omittedArtworkIds,
		hideItemsWithoutImages,
		showFavouriteArtists,
	});

	export const formatStateUrl = async (href: string | undefined) => {
		if (!href) {
			return;
		}

		const a = new URLSearchParams(`?${href.split('?')[1]}`);
		const b = new URLSearchParams(
			`?${formatParamString(getParams(), StringBoolean.True, true)}`
		);

		if (b.get('showResults') && !a.get('showResults')) {
			a.append('showResults', b.get('showResults') as string);
		}

		if (a.get('paginationDirection') && !b.get('paginationDirection')) {
			b.append('paginationDirection', a.get('paginationDirection') as string);
		}

		if (!a.get('paginationToken') && b.get('paginationToken')) {
			a.append('paginationToken', b.get('paginationToken') as string);
		}

		if (
			a.get('paginationToken') &&
			b.get('paginationToken') &&
			a.get('paginationToken') !== b.get('paginationToken')
		) {
			b.set('paginationToken', a.get('paginationToken') as string);
		}

		if (
			a.get('paginationDirection') &&
			b.get('paginationDirection') &&
			a.get('paginationDirection') !== b.get('paginationDirection')
		) {
			b.set('paginationDirection', a.get('paginationDirection') as string);
		}

		a.sort();
		b.sort();

		return formatParamString(
			getParams(),
			`${a.toString() === b.toString()}` as StringBoolean,
			true
		);
	};

	const handleClickPreset = () => {
		const { resultsTab, ...params } = getParams();
		const queryParams = formatParamString(
			params as ReturnType<typeof getParams>,
			StringBoolean.True,
			false
		);

		presetUrl = `?${queryParams}`;
		presetDialogStores.states.open.set(true);
	};

	const handleSearchClick = (showActivities: boolean) => {
		const { resultsTab, ...params } = getParams();
		const queryParams = formatParamString(
			params as ReturnType<typeof getParams>,
			StringBoolean.True,
			false
		);

		goto(
			`?${queryParams}${showActivities ? '' : `&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}`
		);
	};

	const handleClearClick = () => {
		// Artwork information
		name = '';
		title = '';
		crid = '';
		media = '';
		executedYear = '';
		executedYearRange = SearchRange.EqualTo;
		artworkType = [];
		height = '';
		heightRange = SearchRange.EqualTo;
		width = '';
		widthRange = SearchRange.EqualTo;
		depth = '';
		depthRange = SearchRange.EqualTo;
		numberOfPieces = '';
		numberOfArtworks = '';
		isHeniArtwork = false;
		isBundle = false;
		isFullSet = false;
		editionSize = '';
		editionSizeRange = SearchRange.EqualTo;
		editionNumber = '';
		seriesTitle = '';
		seriesCrid = '';
		seriesSize = '';
		seriesSizeRange = SearchRange.EqualTo;
		isUnlimitedEdition = false;
		excludeMultiples = false;
		categoryTags = [];
		subjectTags = [];

		// Activity information
		activityType = [];
		listingType = [];
		activityStatus = [];
		activityDate = '';
		activityDateRange = SearchRange.EqualTo;
		listingPrice = '';
		listingPriceRange = SearchRange.EqualTo;
		saleAmount = '';
		saleAmountRange = SearchRange.EqualTo;
		association = '';
		associationType = '';
		auctionHouse = '';
		auctionSaleName = '';
		saleNumber = '';
		lotNumber = '';
		auctionType = [];
		fairName = '';
		fairDate = '';
		fairExhibitor = '';
		fairDateRange = SearchRange.EqualTo;
		galleryName = '';
		galleryCity = '';
		galleryCountry = '';
		exhibitionName = '';
		exhibitionDate = '';
		exhibitionDateRange = SearchRange.EqualTo;
		exhibitionCity = '';
		exhibitionCountry = '';

		// Data entry
		createdBy = '';
		createdDate = '';
		createdDateRange = SearchRange.EqualTo;
		updatedBy = '';
		updatedByDate = '';
		updatedByDateRange = SearchRange.EqualTo;
		source = [];

		// Omitted items
		omittedArtworkIds = '';
		omittedActivityIds = '';
		hideItemsWithoutImages = false;

		// Sort
		sort = [];
	};

	let artworkParamsCount = $state(0);
	let activityParamsCount = $state(0);
	let dataParamsCount = $state(0);
	let omittedItemsParamsCount = $state(0);

	$effect(() => {
		const urlParams = new URLSearchParams(page.url.search);

		const countParams = (paramValues: string[]): number =>
			paramValues
				.filter((param) => !param.includes('Range'))
				.reduce((count, param) => count + (urlParams.has(param) ? 1 : 0), 0);

		artworkParamsCount = countParams(Object.values(ArtworkAdvancedSearchParam));
		activityParamsCount = countParams(
			Object.values(ActivityAdvancedSearchParam)
		);
		dataParamsCount = countParams(Object.values(DataEntryAdvancedSearchParam));
		omittedItemsParamsCount = countParams(
			Object.values(OmittedItemsSearchParam)
		);
	});

	const handleKeyUpNumberOfArtworks = (e?: Event | undefined) => {
		if ((e?.target as HTMLInputElement)?.value) {
			excludeMultiples = false;
		}
	};
</script>

<div>
	<Accordion>
		<AccordionItem dataCy={`${dataCyPrefix}-artwork-information`} defaultOpen>
			{#snippet titleSlot()}
				<div class="flex items-center">
					<Txt
						data-cy={`${dataCyPrefix}-title`}
						variant="h6"
						class="py-4 text-left"
					>
						Artwork information:
					</Txt>
					<Txt variant="label3" class="ml-4 rounded bg-gray-100 p-1 px-4"
						>{artworkParamsCount}</Txt
					>
				</div>
			{/snippet}
			<div class="lg:mx-8 mt-4">
				<Txt variant="label3" class="mb-1 text-gray-500">Artwork details</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<ArtistName {dataCyPrefix} bind:name />
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-artwork-title`}
								name="title"
								placeholder="Enter artwork title or ID"
								label="Artwork title or ID"
								bind:value={title}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-crid`}
								name="crid"
								placeholder="Eg. H-12-1"
								bind:value={crid}
								label="CRID"
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-media`}
								name="media"
								placeholder="Enter media"
								label="Media"
								bind:value={media}
								size="sm"
							/>
						</div>
						<div class="z-40 col-span-1">
							<InputWithSelect
								size="sm"
								label="Executed year"
								dataCy={`${dataCyPrefix}-executed-year`}
								name="executedYear"
								bind:selectValue={executedYearRange}
								bind:inputValue={executedYear}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[executedYearRange]}
							/>
						</div>
					</div>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<MultiSelect
								name="artworkType"
								dataCy={`${dataCyPrefix}-artwork-type`}
								label="Artwork type"
								bind:selected={artworkType}
								placeholder="Artwork type"
								options={artworkTypeOptions}
								size="sm"
							/>
						</div>
						<div class="z-30 col-span-1">
							<InputWithSelect
								size="sm"
								label="Height (cm)"
								dataCy={`${dataCyPrefix}-height`}
								name="height"
								bind:selectValue={heightRange}
								bind:inputValue={height}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
							/>
						</div>
						<div class="z-30 col-span-1">
							<InputWithSelect
								size="sm"
								label="Width (cm)"
								dataCy={`${dataCyPrefix}-width`}
								name="width"
								bind:selectValue={widthRange}
								bind:inputValue={width}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
							/>
						</div>
						<div class="z-30 col-span-1">
							<InputWithSelect
								size="sm"
								label="Depth (cm)"
								dataCy={`${dataCyPrefix}-depth`}
								name="depth"
								bind:selectValue={depthRange}
								bind:inputValue={depth}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-number-of-pieces`}
								name="numberOfPieces"
								placeholder="Eg. 24 or 8-12"
								label="Number of pieces"
								bind:value={numberOfPieces}
								size="sm"
							/>
						</div>
					</div>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-number-of-artworks`}
								name="numberOfArtworks"
								placeholder="0"
								label="Number of artworks"
								bind:value={numberOfArtworks}
								onkeyup={handleKeyUpNumberOfArtworks}
								size="sm"
							/>
						</div>
					</div>
					<div class="mx-4 mb-4 flex space-x-4">
						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-is-heni-artwork`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-is-heni-artwork`}
									bind:checked={isHeniArtwork}
								/>
								Is HENI artwork
							</InputLabel>
						</div>
						<div class="col-span-1 flex h-full">
							<InputLabel dataCy={`${dataCyPrefix}-is-bundle`} variant="body3">
								<Checkbox
									dataCy={`${dataCyPrefix}-is-bundle`}
									bind:checked={isBundle}
								/>
								Is bundle
							</InputLabel>
						</div>
						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-is-full-set`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-is-full-set`}
									bind:checked={isFullSet}
								/>
								Is full set
							</InputLabel>
						</div>

						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-show-favourite-artists`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-show-favourite-artists`}
									bind:checked={showFavouriteArtists}
								/>
								Favourite artists only
							</InputLabel>
						</div>
					</div>

					{#if isHeniArtwork}
						<div class="mx-4 mb-4 flex space-x-4">
							<MultiSelect
								class="w-[200px] [&>div]:z-40"
								dataCy={`${dataCyPrefix}-heni-artwork-type`}
								name="heniArtworkType"
								placeholder="HENI artwork types"
								options={heniArtworkTypeOptions}
								bind:selected={heniArtworkType}
								size="sm"
							/>
						</div>
					{/if}
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Editions & series</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="z-30 col-span-1">
							<InputWithSelect
								size="sm"
								label="Edition size"
								dataCy={`${dataCyPrefix}-edition-size-year`}
								name="editionSize"
								bind:selectValue={editionSizeRange}
								bind:inputValue={editionSize}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-edition-number`}
								name="editionNumber"
								placeholder="Eg. 20"
								label="Edition number"
								bind:value={editionNumber}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-series-title`}
								name="seriesTitle"
								placeholder="Enter series title or ID"
								label="Series title or ID"
								bind:value={seriesTitle}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-series-crid`}
								name="seriesCrid"
								placeholder="Eg. H-12"
								label="Series CRID"
								bind:value={seriesCrid}
								size="sm"
							/>
						</div>
						<div class="z-30 col-span-1">
							<InputWithSelect
								size="sm"
								label="Series size"
								dataCy={`${dataCyPrefix}-series-size`}
								name="seriesSize"
								bind:selectValue={seriesSizeRange}
								bind:inputValue={seriesSize}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
							/>
						</div>
					</div>
					<div class="mx-4 mb-4 flex space-x-4">
						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-unlimited-edition`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-unlimited-edition`}
									bind:checked={isUnlimitedEdition}
								/>
								Unlimited edition
							</InputLabel>
						</div>
						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-exclude-multiple`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-exclude-multiple`}
									disabled={!!numberOfArtworks}
									bind:checked={excludeMultiples}
								/>
								Excludes multiples >10
							</InputLabel>
							<InfoTooltip
								class="ml-1"
								dataCy={`${dataCyPrefix}-exclude-multiple-tooltip`}
								content="Cannot be ticked if number of artworks is specified"
							/>
						</div>
					</div>
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Tags</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="z-40 col-span-2">
							<MultiSelect
								name="categoryTags"
								dataCy={`${dataCyPrefix}-category-tags`}
								label="Category tags"
								bind:selected={categoryTags}
								placeholder="Category tags"
								options={categoryTagsOptions}
								size="sm"
							/>
						</div>
						<div class="z-40 col-span-2">
							<MultiSelect
								name="subjectTags"
								dataCy={`${dataCyPrefix}-subject-tags`}
								label="Subject tags"
								bind:selected={subjectTags}
								placeholder="Subject tags"
								options={subjectTagsOptions}
								size="sm"
							/>
						</div>
					</div>
				</div>
			</div>
		</AccordionItem>
	</Accordion>
	<div class="border-b border-gray-200"></div>
	<Accordion>
		<AccordionItem dataCy={`${dataCyPrefix}-activity-information`} defaultOpen>
			{#snippet titleSlot()}
				<div class="flex items-center">
					<Txt
						data-cy={`${dataCyPrefix}-title`}
						variant="h6"
						class="py-4 text-left"
					>
						Activity information:
					</Txt>
					<Txt variant="label3" class="ml-4 rounded bg-gray-100 p-1 px-4"
						>{activityParamsCount}</Txt
					>
				</div>
			{/snippet}
			<div class="lg:mx-8 mt-4">
				<Txt variant="label3" class="mb-1 text-gray-500">Activity details</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="z-30 col-span-1">
							<MultiSelect
								name="activityType"
								dataCy={`${dataCyPrefix}-activity-type`}
								label="Activity type"
								bind:selected={activityType}
								placeholder="Activity type"
								options={activityTypeOptions}
								size="sm"
							/>
						</div>
						<div class="z-30 col-span-1">
							<MultiSelect
								name="listingType"
								dataCy={`${dataCyPrefix}-listing-type`}
								label="Private listing type"
								bind:selected={listingType}
								placeholder="Private listing type"
								options={listingTypeOptions}
								size="sm"
							/>
						</div>
						<div class="z-30 col-span-1">
							<MultiSelect
								name="activityStatus"
								dataCy={`${dataCyPrefix}-activity-status`}
								label="Activity status"
								bind:selected={activityStatus}
								placeholder="Activity status"
								options={activityStatusTypeOptions}
								size="sm"
							/>
						</div>
					</div>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="z-20 col-span-2">
							<InputWithSelect
								size="sm"
								label="Activity date (DD/MM/YYYY)"
								dataCy={`${dataCyPrefix}-activity-date`}
								name="activityDate"
								bind:selectValue={activityDateRange}
								bind:inputValue={activityDate}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[activityDateRange]}
							/>
						</div>
						<div class="z-20 col-span-1">
							<InputWithSelect
								size="sm"
								label="Listing price (USD)"
								dataCy={`${dataCyPrefix}-listing-price`}
								name="listingPrice"
								bind:selectValue={listingPriceRange}
								bind:inputValue={listingPrice}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
								onkeydown={(e) => {
									handleKeyDownNumbersOnly(e, true);
									formatWithThousandSeparator(e);
								}}
							/>
						</div>
						<div class="z-20 col-span-1">
							<InputWithSelect
								size="sm"
								label="Sold for (USD)"
								dataCy={`${dataCyPrefix}-sold-for`}
								name="saleAmount"
								bind:selectValue={saleAmountRange}
								bind:inputValue={saleAmount}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder="0"
								onkeydown={(e) => {
									handleKeyDownNumbersOnly(e, true);
									formatWithThousandSeparator(e);
								}}
							/>
						</div>
						<div class="z-40 col-span-2">
							<InputWithSelect
								size="sm"
								label="Association"
								dataCy={`${dataCyPrefix}-association`}
								name="association"
								bind:selectValue={associationType}
								bind:inputValue={association}
								options={associationTypeOptions}
								placeholder="Name or entity ID"
							/>
						</div>
					</div>
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Auction details</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-auction-house`}
								name="auctionHouse"
								placeholder="Name"
								label="Auction house or ID"
								bind:value={auctionHouse}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-auction-name`}
								name="auctionNameOrId"
								placeholder="Name"
								label="Auction name or ID"
								bind:value={auctionName}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-auction-sale-name`}
								name="auctionSaleName"
								placeholder="Name"
								label="Auction sale name or ID"
								bind:value={auctionSaleName}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-sale-number`}
								name="saleNumber"
								placeholder="Sale number"
								label="Sale number"
								bind:value={saleNumber}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-lot-number`}
								name="lotNumber"
								placeholder="Eg. 24 or 8-12"
								label="Lot number"
								bind:value={lotNumber}
								size="sm"
							/>
						</div>
						<div class="z-30 col-span-1">
							<MultiSelect
								name="auctionType"
								dataCy={`${dataCyPrefix}-auction-type`}
								label="Auction type"
								bind:selected={auctionType}
								placeholder="Auction type"
								options={auctionTypeOptions}
								size="sm"
							/>
						</div>
					</div>
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Fair details</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-fair-name`}
								name="fairName"
								placeholder="Title or ID"
								label="Fair title or ID"
								bind:value={fairName}
								size="sm"
							/>
						</div>
						<div class="col-span-2">
							<InputWithSelect
								size="sm"
								label="Fair date (DD/MM/YYYY)"
								dataCy={`${dataCyPrefix}-fair-date`}
								name="fairDate"
								bind:selectValue={fairDateRange}
								bind:inputValue={fairDate}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[fairDateRange]}
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-fair-exhibitor`}
								name="fairExhibitor"
								placeholder="Name or ID"
								label="Fair exhibitor or ID"
								bind:value={fairExhibitor}
								size="sm"
							/>
						</div>
					</div>
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Gallery details</Txt>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-gallery-name`}
								name="galleryName"
								placeholder="Name or ID"
								label="Gallery name or ID"
								bind:value={galleryName}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-gallery-city`}
								name="galleryCity"
								placeholder=""
								label="Address (City)"
								bind:value={galleryCity}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-gallery-country`}
								name="galleryCountry"
								placeholder=""
								label="Address (Country)"
								bind:value={galleryCountry}
								size="sm"
							/>
						</div>
					</div>
				</div>
				<Txt variant="label3" class="mb-1 text-gray-500">Exhibition details</Txt
				>
				<div
					class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
				>
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-exhibition-name`}
								name="ExhibitionName"
								placeholder="Name or ID"
								label="Exhibition name"
								bind:value={exhibitionName}
								size="sm"
							/>
						</div>
						<div class="col-span-2">
							<InputWithSelect
								size="sm"
								label="Exhibition date (DD/MM/YYYY)"
								dataCy={`${dataCyPrefix}-exhibition-date`}
								name="exhibitionDate"
								bind:selectValue={exhibitionDateRange}
								bind:inputValue={exhibitionDate}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[
									exhibitionDateRange
								]}
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-exhibition-city`}
								name="exhibitionCity"
								placeholder=""
								label="Address (City)"
								bind:value={exhibitionCity}
								size="sm"
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-exhibition-country`}
								name="exhibitionCountry"
								placeholder=""
								label="Address (Country)"
								bind:value={exhibitionCountry}
								size="sm"
							/>
						</div>
					</div>
				</div>
			</div>
		</AccordionItem>
	</Accordion>

	<div class="border-b border-gray-200"></div>

	<Accordion>
		<AccordionItem dataCy={`${dataCyPrefix}-data-entry`} defaultOpen>
			{#snippet titleSlot()}
				<div class="flex items-center">
					<Txt
						data-cy={`${dataCyPrefix}-title`}
						variant="h6"
						class="py-4 text-left"
					>
						Data entry:
					</Txt>
					<Txt variant="label3" class="ml-4 rounded bg-gray-100 p-1 px-4"
						>{dataParamsCount}</Txt
					>
				</div>
			{/snippet}
			<div class="lg:mx-8 mt-4">
				<div class="rounded-md bg-white mb-6">
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-created-by`}
								name="createdBy"
								placeholder="<EMAIL>"
								label="Created by"
								bind:value={createdBy}
								size="sm"
							/>
						</div>
						<div class="z-40 col-span-1">
							<InputWithSelect
								size="sm"
								label="Created date"
								dataCy={`${dataCyPrefix}-created-date`}
								name="createdDate"
								bind:selectValue={createdDateRange}
								bind:inputValue={createdDate}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[createdDateRange]}
							/>
						</div>
						<div class="col-span-1">
							<Input
								dataCy={`${dataCyPrefix}-updated-by`}
								name="updatedBy"
								placeholder="<EMAIL>"
								label="Updated by"
								bind:value={updatedBy}
								size="sm"
							/>
						</div>
						<div class="z-40 col-span-1">
							<InputWithSelect
								size="sm"
								label="Updated date"
								dataCy={`${dataCyPrefix}-updated-date`}
								name="updatedDate"
								bind:selectValue={updatedByDateRange}
								bind:inputValue={updatedByDate}
								options={NEW_SEARCH_RANGE_OPTIONS}
								placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[updatedByDateRange]}
							/>
						</div>
						<div class="col-span-1">
							<MultiSelect
								name="source"
								dataCy={`${dataCyPrefix}-source`}
								label="Source"
								bind:selected={source}
								placeholder="- Select -"
								options={dataSourceOptions}
								class="col-span-1"
								size="sm"
							/>
						</div>
					</div>
				</div>
			</div>
		</AccordionItem>
	</Accordion>
	<div class="border-b border-gray-200"></div>
	<Accordion>
		<AccordionItem dataCy={`${dataCyPrefix}-data-entry`} defaultOpen>
			{#snippet titleSlot()}
				<div class="flex items-center">
					<Txt
						data-cy={`${dataCyPrefix}-title`}
						variant="h6"
						class="py-4 text-left"
					>
						Omitted items:
					</Txt>
					<Txt variant="label3" class="ml-4 rounded bg-gray-100 p-1 px-4">
						{omittedItemsParamsCount}
					</Txt>
				</div>
			{/snippet}

			<div class="lg:mx-8 mt-4">
				<div class="rounded-md bg-white mb-2">
					<div class="flex flex-col lg:grid grid-cols-6 gap-4 p-4">
						<div class="col-span-2">
							<Input
								dataCy={`${dataCyPrefix}-omitted-artwork-ids`}
								name="omittedArtworkIds"
								placeholder="Comma separated IDs"
								label="Omit artwork IDs"
								bind:value={omittedArtworkIds}
								size="sm"
								rows={3}
							/>
						</div>

						<div class="col-span-2">
							<Input
								dataCy={`${dataCyPrefix}-omitted-activity-ids`}
								name="omittedActivityIds"
								placeholder="Comma separated IDs"
								label="Omit activity IDs"
								bind:value={omittedActivityIds}
								size="sm"
								rows={3}
							/>
						</div>

						<div class="col-span-1 flex h-full">
							<InputLabel
								dataCy={`${dataCyPrefix}-hide-items-without-images`}
								variant="body3"
							>
								<Checkbox
									dataCy={`${dataCyPrefix}-hide-items-without-images`}
									bind:checked={hideItemsWithoutImages}
								/>
								Hide items without images
							</InputLabel>
						</div>

						<div class="col-span-3">
							<MultiSelect
								name="sort_by"
								dataCy={`${dataCyPrefix}-sort-by`}
								label="Sort by"
								bind:selected={sort}
								placeholder="Sort by"
								options={NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS}
								class="col-span-1"
								size="sm"
								maxLabelLength={40}
							/>
						</div>
					</div>
				</div>
			</div>
		</AccordionItem>
	</Accordion>

	<div class="flex flex-col md:flex-row gap-4 py-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={() => handleSearchClick(true)}
		>
			Search activities
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={() => handleSearchClick(false)}
		>
			Search artworks
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-save-preset`}
			variant="secondary"
			onclick={handleClickPreset}
		>
			Save preset
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>

<PresetDialog dialogStores={presetDialogStores} url={presetUrl} />
