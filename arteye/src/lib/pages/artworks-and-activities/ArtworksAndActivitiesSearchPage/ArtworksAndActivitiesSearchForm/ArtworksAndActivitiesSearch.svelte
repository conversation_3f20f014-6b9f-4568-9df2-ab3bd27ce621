<script lang="ts">
	import classNames from 'classnames';
	import {
		ArtActBasicSearchParam,
		ArtActSearchType,
	} from '../constants/search';
	import { AdvancedSearch } from './AdvancedSearch';
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import type { AaAdvancedSearchFormFields } from './AdvancedSearch/AdvancedSearch.svelte';
	import { BasicSearch } from './BasicSearch';
	import type { AaBasicSearchFormFields } from './BasicSearch/BasicSearch.svelte';
	import { ImageSearch } from './ImageSearch';
	import type { MultipleIdsSearchFormFields } from './MultipleIds/MultipleIds.svelte';
	import MultipleIds from './MultipleIds/MultipleIds.svelte';
	import Presets from './Presets/Presets.svelte';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { LinkButton } from '$global/components/LinkButton';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Tabs } from '$global/components/Tabs';
	import { Txt } from '$global/components/Txt';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { ActivitySearchSortField } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { Searches } from '$lib/constants/searches';
	import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	interface Props {
		formatParamStringBasic: (
			data: AaBasicSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
		formatParamStringAdvanced: (
			data: AaAdvancedSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
		formatParamStringMultiple: (
			data: MultipleIdsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
		onSubmitImage: (data: { image: string }) => void;
		dataSourceOptions?: MultiSelectOption[];
		artworkTypeOptions?: MultiSelectOption[];
		activityTypeOptions?: MultiSelectOption[];
		activityStatusTypeOptions?: MultiSelectOption[];
		associationTypeOptions?: MultiSelectOption[];
		listingTypeOptions?: MultiSelectOption[];
		auctionTypeOptions?: MultiSelectOption[];
		categoryTagsOptions?: MultiSelectOption[];
		subjectTagsOptions?: MultiSelectOption[];
		searchPresetsOptions?: MultiSelectOption[];
		heniArtworkTypeOptions?: MultiSelectOption[];
	}

	let {
		formatParamStringBasic,
		formatParamStringAdvanced,
		formatParamStringMultiple,
		onSubmitImage,
		dataSourceOptions = [],
		artworkTypeOptions = [],
		activityTypeOptions = [],
		activityStatusTypeOptions = [],
		associationTypeOptions = [],
		listingTypeOptions = [],
		auctionTypeOptions = [],
		categoryTagsOptions = [],
		subjectTagsOptions = [],
		searchPresetsOptions = [],
		heniArtworkTypeOptions = [],
	}: Props = $props();

	const dataCy = 'search';

	let searchType = $derived(page.url.searchParams.get('searchType'));
	const searchTypesArray = Object.values(ArtActSearchType);

	let activeTab = $state(
		(() =>
			!searchType
				? 0
				: searchTypesArray.indexOf(searchType as ArtActSearchType))()
	) as number;

	$effect(() => {
		activeTab = !searchType
			? 0
			: searchTypesArray.indexOf(searchType as ArtActSearchType);
	});

	const tabs = [
		{ id: '1', title: 'Basic Search' },
		{ id: '2', title: 'Advanced Search' },
		{ id: '3', title: 'Multiple IDs' },
		{ id: '4', title: 'Presets' },
		{ id: '5', title: 'By Image' },
	];

	const handleTabClick = async (
		tabIndex: number,
		previousTabIndex?: number
	) => {
		if (previousTabIndex !== undefined) {
			await saveTabState(previousTabIndex, window.location.href);
		}

		switch (tabIndex) {
			case 0: {
				goto(userRoutes.routes.basic_search, { replaceState: true });
				return;
			}
			case 1: {
				goto(userRoutes.routes.advanced_search, { replaceState: true });
				return;
			}
			case 2: {
				goto(userRoutes.routes.multiple_search, { replaceState: true });
				return;
			}
			default: {
				const searchType = Object.values(ArtActSearchType)[tabIndex] || null;
				if (!searchType) return;
				const currentUrl = new URL(page.url);
				for (const key of Array.from(currentUrl.searchParams.keys())) {
					currentUrl.searchParams.delete(key);
				}

				currentUrl.searchParams.set('searchType', searchType);
				goto(currentUrl.toString(), { replaceState: true });
			}
		}
	};

	let basicSearchFormRef = $state(null) as
		| null
		| undefined
		| {
				formatStateUrl: (
					href: string | undefined
				) => Promise<string | undefined>;
		  };

	let advancedSearchFormRef = $state(null) as
		| null
		| undefined
		| {
				formatStateUrl: (
					href: string | undefined
				) => Promise<string | undefined>;
		  };

	let multipleIdsSearchFormRef = $state(null) as
		| null
		| undefined
		| {
				formatStateUrl: (
					href: string | undefined
				) => Promise<string | undefined>;
		  };

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url?.pathname !== Routes.ArtworksAndActivities
			) {
				await saveTabState(activeTab, from?.url?.href);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const saveTabState = async (tabIndex: number, href: string | undefined) => {
		if (!tabIndex) {
			const params = await basicSearchFormRef?.formatStateUrl(href);
			await setArteyeSearch(
				Searches.BasicSearch,
				`${Routes.ArtworksAndActivities}?${params}`
			);
		}

		if (tabIndex === 1) {
			const params = await advancedSearchFormRef?.formatStateUrl(href);
			await setArteyeSearch(
				Searches.AdvancedSearch,
				`${Routes.ArtworksAndActivities}?${params}`
			);
		}

		if (tabIndex === 2) {
			const params = await multipleIdsSearchFormRef?.formatStateUrl(href);
			await setArteyeSearch(
				Searches.MultipleSearch,
				`${Routes.ArtworksAndActivities}?${params}`
			);
		}
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="border-b border-gray-200 p-4">
		<div class="mb-4 flex items-center justify-between">
			<Txt variant="h5">Search artworks and activities</Txt>
			<LinkButton
				dataCy={`${dataCy}-create-new`}
				size="sm"
				href={`${Routes.ArtworkDetails}/new`}
				class="hidden lg:block"
			>
				Create New</LinkButton
			>
		</div>
		<div class="border-b border-gray-200"></div>

		<div class="overflow-x-auto">
			{#if activeTab !== null}
				<Tabs
					headerVariant="h6"
					{dataCy}
					{tabs}
					bind:activeTab
					onClickTab={handleTabClick}
					class="min-w-[600px]"
				/>
			{/if}
		</div>

		{#if !searchType || searchType === 'basic'}
			<BasicSearch
				bind:this={basicSearchFormRef}
				formatParamString={formatParamStringBasic}
				{artworkTypeOptions}
				{activityTypeOptions}
				{activityStatusTypeOptions}
				{associationTypeOptions}
			/>
		{/if}

		{#if searchType === 'advanced'}
			<AdvancedSearch
				bind:this={advancedSearchFormRef}
				formatParamString={formatParamStringAdvanced}
				{dataSourceOptions}
				{artworkTypeOptions}
				{activityTypeOptions}
				{activityStatusTypeOptions}
				{associationTypeOptions}
				{listingTypeOptions}
				{heniArtworkTypeOptions}
				{auctionTypeOptions}
				{categoryTagsOptions}
				{subjectTagsOptions}
			/>
		{/if}

		{#if searchType === 'multipleIds'}
			<MultipleIds
				bind:this={multipleIdsSearchFormRef}
				formatParamString={formatParamStringMultiple}
			/>
		{/if}

		<div class={classNames({ hidden: activeTab !== 3 })}>
			<Presets {searchPresetsOptions} />
		</div>
		<div class={classNames({ hidden: activeTab !== 4 })}>
			<ImageSearch onSubmit={onSubmitImage} />
		</div>
	</div>
</div>
