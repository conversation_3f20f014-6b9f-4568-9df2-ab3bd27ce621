<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$global/components/Button';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { decodePeriodString } from '$global/utils/decodePeriodString/decodePeriodString';
	import { Routes } from '$lib/constants/routes';
	import { getMultiselectSingleValue } from '$lib/utils/getMultiselectSingleValue/getMultiselectSingleValue';
	import dayjs, { type ManipulateType } from 'dayjs';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';

	interface Props {
		searchPresetsOptions?: MultiSelectOption[];
		preset?: MultiSelectOption[];
	}

	let { searchPresetsOptions = [], preset = $bindable([]) }: Props = $props();

	let submitDisabled = $derived(!preset);

	const dataCyPrefix = 'search';

	const handleSearchClick = () => {
		let searchParams = new URLSearchParams(
			getMultiselectSingleValue(preset).slice(1)
		);

		const activityDateSearchParams = new URLSearchParams();

		const periodSearchParam = searchParams.get('period');
		if (periodSearchParam) {
			const { unit, quantity } = decodePeriodString(periodSearchParam);

			activityDateSearchParams.set(
				'activityDate',
				dayjs()
					.subtract(quantity, unit as ManipulateType)
					.format('DD/MM/YYYY')
			);

			activityDateSearchParams.set('activityDateRange', 'GreaterThan');
			searchParams.delete('period');
		}

		goto(
			`${Routes.ArtworksAndActivities}?${searchParams.toString()}&${getSearchParamString(Object.fromEntries(activityDateSearchParams.entries()))}`
		);
	};

	const handleClearClick = () => {
		preset = [];
	};
</script>

<div>
	<div class="p-4">
		<div class="md:w-[14rem]">
			<MultiSelect
				name="Choose preset"
				required
				dataCy={`${dataCyPrefix}-preset`}
				label="Preset"
				bind:selected={preset}
				placeholder="Type or select"
				options={searchPresetsOptions}
				class="col-span-1"
				size="sm"
				maxSelect={1}
			/>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
			disabled={submitDisabled}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
