<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import type { PageData } from '../../../../../../routes/artworks-and-activities/$types';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Input } from '$global/components/Input';
	import { InputError } from '$global/components/Input/InputError';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { gqlClient } from '$lib/gqlClient';
	import { CreateSearchPresetDocument } from '$lib/mutations/__generated__/createSearchPreset.generated';
	import { GetSearchPresetsDocument } from '$lib/queries/__generated__/getSearchPresets.generated';

	const dataCyPrefix = 'preset';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		url: string;
	}

	let data = $derived(getPageData<PageData>(page.data));
	let { dialogStores, url = $bindable() }: Props = $props();
	let title = $state('');
	let error = $state('');
	let submitting = $state(false);

	const handleClose = () => {
		dialogStores.states.open.set(false);
		title = '';
		error = '';
		submitting = false;
	};

	const handleSubmit = async () => {
		submitting = true;
		const trimmedTitle = title.trim();

		try {
			const presetRes = await gqlClient.request(
				GetSearchPresetsDocument,
				{
					filter: {
						_and: [
							{
								_or: [{ title: { _eq: trimmedTitle } }, { url: { _eq: url } }],
							},
							{ status: { key: { _neq: 'archived' } } },
						],
					},
				},
				getAuthorizationHeaders(data)
			);

			const sameUrlPreset = presetRes?.search_preset?.find(
				(preset) => preset.url === url
			);
			if (sameUrlPreset) {
				error = `A preset with this URL already exists. (${sameUrlPreset.title})`;
				submitting = false;
				return;
			}

			if (presetRes?.search_preset?.find((preset) => preset.title === title)) {
				error =
					'A preset with this title already exists. Please choose another name.';
				submitting = false;
				return;
			}

			await gqlClient.request(
				CreateSearchPresetDocument,
				{
					data: { title: trimmedTitle, url },
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'The search preset has been successfully created.',
			});

			handleClose();
			url = '';
		} catch {
			error = 'An error occurred. Please let the support team know.';
			submitting = false;
		}
	};
</script>

<Dialog
	onClose={handleClose}
	dataCy={dataCyPrefix}
	{dialogStores}
	title="Save preset"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<div class="mb-4 w-full">
			<Input
				name="title"
				placeholder="Enter preset title..."
				dataCy={`${dataCyPrefix}-title`}
				bind:value={title}
				required
				onchange={() => {
					error = '';
				}}
				label="Preset title"
			/>
		</div>

		<div class="mb-4 w-full">
			<Input
				name="url"
				disabled
				dataCy={`${dataCyPrefix}-url`}
				value={url}
				label="Preset URL"
			/>
		</div>

		<div class="flex w-full flex-col gap-2 mt-8">
			<Button
				size="lg"
				fullWidth
				dataCy={`${dataCyPrefix}-submit`}
				onclick={handleSubmit}
				loading={submitting}
				disabled={submitting || !title || !!error}
			>
				submit
			</Button>

			<Button
				fullWidth
				dataCy={`${dataCyPrefix}-cancel`}
				onclick={handleClose}
				variant="secondary"
				size="lg"
			>
				cancel
			</Button>
		</div>
		<div class="mt-2 text-center">
			<InputError dataCy={`${dataCyPrefix}-error`}>
				{error}
			</InputError>
		</div>
	</div>
</Dialog>
