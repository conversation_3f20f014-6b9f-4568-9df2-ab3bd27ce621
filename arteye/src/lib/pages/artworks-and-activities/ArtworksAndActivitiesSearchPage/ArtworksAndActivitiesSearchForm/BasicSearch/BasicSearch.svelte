<script module lang="ts">
	export interface AaBasicSearchFormFields {
		[ArtActBasicSearchParam.Sort]: string[];
		[ArtActBasicSearchParam.Name]: string;
		[ArtActBasicSearchParam.ResultsTab]: string;
		[ArtActBasicSearchParam.Title]: string;
		[ArtActBasicSearchParam.Crid]: string;
		[ArtActBasicSearchParam.Media]: string;
		[ArtActBasicSearchParam.ExecutedYear]: string;
		[ArtActBasicSearchParam.ExecutedYearRange]: string;
		[ArtActBasicSearchParam.ArtworkType]: string[];
		[ArtActBasicSearchParam.ActivityId]: string;
		[ArtActBasicSearchParam.ActivityType]: string[];
		[ArtActBasicSearchParam.ActivityStatus]: string[];
		[ArtActBasicSearchParam.Height]: string;
		[ArtActBasicSearchParam.HeightRange]: string;
		[ArtActBasicSearchParam.Width]: string;
		[ArtActBasicSearchParam.WidthRange]: string;
		[ArtActBasicSearchParam.EditionSize]: string;
		[ArtActBasicSearchParam.EditionSizeRange]: string;
		[ArtActBasicSearchParam.ActivityDate]: string;
		[ArtActBasicSearchParam.ActivityDateRange]: string;
		[ArtActBasicSearchParam.SaleAmount]: string;
		[ArtActBasicSearchParam.SaleAmountRange]: string;
		[ArtActBasicSearchParam.LotNumber]: string;
		[ArtActBasicSearchParam.SaleName]: string;
		[ArtActBasicSearchParam.SaleNumber]: string;
		[ArtActBasicSearchParam.SortResultsBy]: string;
		[ArtActBasicSearchParam.Association]: string;
		[ArtActBasicSearchParam.AssociationType]: string;

		[ArtActBasicSearchParam.OmittedArtworkIds]: string;
		[ArtActBasicSearchParam.OmittedActivityIds]: string;
		[ArtActBasicSearchParam.HideItemsWithoutImages]: boolean;
		[ArtActBasicSearchParam.ShowFavouriteArtists]: boolean;
	}
</script>

<script lang="ts">
	import { PresetDialog } from '../PresetDialog';
	import { createDialog } from '@melt-ui/svelte';
	import { ArtActBasicSearchParam } from '../../constants/search';
	import { NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS } from '../../constants/sort';
	import { ArtistName } from '../components/ArtistName';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { formatWithThousandSeparator } from '$global/components/Input/utils/formatWithThousandSeparator/formatWithThousandSeparator';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { ValueFilterOperator } from '$gql/types-custom';
	import {
		PARAM_SEPARATOR,
		STRING_LIST_SEPARATOR,
	} from '$lib/constants/params';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
		SEARCH_RANGE_PLACEHOLDERS_FULL,
	} from '$lib/constants/search-range-options';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getBooleanFromParam } from '$lib/utils/getBooleanFromParam/getBooleanFromParam';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';
	import { ArtworkAndActivitiesResultsTab } from '../../ArtworksResults';

	interface Props {
		artworkTypeOptions?: MultiSelectOption[];
		activityTypeOptions?: MultiSelectOption[];
		activityStatusTypeOptions?: MultiSelectOption[];
		associationTypeOptions?: MultiSelectOption[];
		formatParamString: (
			data: AaBasicSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let presetUrl = $state('');
	const presetDialogStores = createDialog();

	let {
		artworkTypeOptions = [],
		activityTypeOptions = [],
		activityStatusTypeOptions = [],
		associationTypeOptions = [],
		formatParamString,
	}: Props = $props();

	let data = $derived(
		getPageData<ArtworksAndActivitiesSearchPageData>(page.data)
	);

	let searchParams = $derived(page.url.searchParams);

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.Sort,
			NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.Sort,
			NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS
		);
	});

	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Name,
		})
	);

	$effect(() => {
		name = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Name,
		});
	});

	let title = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Title,
		})
	);

	$effect(() => {
		title = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Title,
		});
	});

	let crid = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Crid,
		})
	);

	$effect(() => {
		crid = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Crid,
		});
	});

	let media = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Media,
		})
	);

	$effect(() => {
		media = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Media,
		});
	});

	let executedYear = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ExecutedYear,
		})
	);

	$effect(() => {
		executedYear = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ExecutedYear,
		});
	});

	let executedYearRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ExecutedYearRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		executedYearRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ExecutedYearRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let artworkType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ArtworkType,
			artworkTypeOptions
		)
	);

	$effect(() => {
		artworkType = findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ArtworkType,
			artworkTypeOptions
		);
	});

	let activityId = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityId,
		})
	);

	$effect(() => {
		activityId = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityId,
		});
	});

	let activityType: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ActivityType,
			activityTypeOptions
		)
	);

	$effect(() => {
		activityType = findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ActivityType,
			activityTypeOptions
		);
	});

	let activityStatus: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ActivityStatus,
			activityStatusTypeOptions
		)
	);

	$effect(() => {
		activityStatus = findMultiselectOptions(
			page.url.searchParams,
			ArtActBasicSearchParam.ActivityStatus,
			activityStatusTypeOptions
		);
	});

	let height = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Height,
		})
	);

	$effect(() => {
		height = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Height,
		});
	});

	let heightRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.HeightRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		heightRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.HeightRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let width = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Width,
		})
	);

	$effect(() => {
		width = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Width,
		});
	});

	let widthRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.WidthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		widthRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.WidthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let editionSize = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.EditionSize,
		})
	);

	$effect(() => {
		editionSize = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.EditionSize,
		});
	});

	let editionSizeRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.EditionSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		editionSizeRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.EditionSizeRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let activityDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityDate,
		})
	);

	$effect(() => {
		activityDate = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityDate,
		});
	});

	let activityDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		activityDateRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ActivityDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let saleAmount = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleAmount,
		})
	);

	$effect(() => {
		saleAmount = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleAmount,
		});
	});

	let saleAmountRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleAmountRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	$effect(() => {
		saleAmountRange = getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleAmountRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		});
	});

	let lotNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.LotNumber,
		})
	);

	$effect(() => {
		lotNumber = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.LotNumber,
		});
	});

	let saleName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleName,
		})
	);

	$effect(() => {
		saleName = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleName,
		});
	});

	let saleNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleNumber,
		})
	);

	$effect(() => {
		saleNumber = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SaleNumber,
		});
	});

	let sortResultsBy = getDecodedSearchParam({
		searchParams: page.url.searchParams,
		key: ArtActBasicSearchParam.SortResultsBy,
	});

	$effect(() => {
		sortResultsBy = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.SortResultsBy,
		});
	});

	let resultsTab = $derived(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.ResultsTab,
		})
	);

	let association = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Association,
		})
	);

	$effect(() => {
		association = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.Association,
		});
	});

	let associationType = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.AssociationType,
		})
	);
	let omittedActivityIds = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.OmittedActivityIds,
		})
			.split(PARAM_SEPARATOR)
			.join(STRING_LIST_SEPARATOR)
	);
	let omittedArtworkIds = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.OmittedArtworkIds,
		})
			.split(PARAM_SEPARATOR)
			.join(STRING_LIST_SEPARATOR)
	);

	let hideItemsWithoutImages = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtActBasicSearchParam.HideItemsWithoutImages
		)
	);

	let showFavouriteArtists = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtActBasicSearchParam.ShowFavouriteArtists
		)
	);

	$effect(() => {
		associationType = getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtActBasicSearchParam.AssociationType,
		});
	});

	const dataCyPrefix = 'search';

	const getValuesFromSelectOptions = (options: { value: string }[]) =>
		options.map((option) => option.value);

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		resultsTab,
		title,
		name,
		crid,
		media,
		executedYear,
		executedYearRange,
		artworkType: getValuesFromSelectOptions(artworkType),
		activityId,
		activityType: getValuesFromSelectOptions(activityType),
		activityStatus: getValuesFromSelectOptions(activityStatus),
		height,
		heightRange,
		width,
		widthRange,
		editionSize,
		editionSizeRange,
		activityDate,
		activityDateRange,
		saleAmount,
		saleAmountRange,
		lotNumber,
		saleName,
		saleNumber,
		sortResultsBy,
		association,
		associationType,
		omittedActivityIds,
		omittedArtworkIds,
		hideItemsWithoutImages,
		showFavouriteArtists,
	});

	export const formatStateUrl = async (href: string | undefined) => {
		if (!href) {
			return;
		}

		const a = new URLSearchParams(`?${href.split('?')[1]}`);
		const b = new URLSearchParams(
			`?${formatParamString(getParams(), StringBoolean.True, true)}`
		);

		if (b.get('showResults') && !a.get('showResults')) {
			a.append('showResults', b.get('showResults') as string);
		}

		if (!a.get('paginationToken') && b.get('paginationToken')) {
			a.append('paginationToken', b.get('paginationToken') as string);
		}

		if (a.get('paginationDirection') && !b.get('paginationDirection')) {
			b.append('paginationDirection', a.get('paginationDirection') as string);
		}

		if (
			a.get('paginationToken') &&
			b.get('paginationToken') &&
			a.get('paginationToken') !== b.get('paginationToken')
		) {
			b.set('paginationToken', a.get('paginationToken') as string);
		}

		if (
			a.get('paginationDirection') &&
			b.get('paginationDirection') &&
			a.get('paginationDirection') !== b.get('paginationDirection')
		) {
			b.set('paginationDirection', a.get('paginationDirection') as string);
		}

		a.sort();
		b.sort();

		return formatParamString(
			getParams(),
			`${a.toString() === b.toString()}` as StringBoolean,
			true
		);
	};

	const handleClickPreset = () => {
		const { resultsTab, ...params } = getParams();
		const queryParams = formatParamString(
			params as ReturnType<typeof getParams>,
			StringBoolean.True,
			false
		);

		presetUrl = `?${queryParams}`;
		presetDialogStores.states.open.set(true);
	};

	const handleSearchClick = (showActivities: boolean) => {
		const { resultsTab, ...params } = getParams();
		const queryParams = formatParamString(
			params as ReturnType<typeof getParams>,
			StringBoolean.True,
			false
		);

		goto(
			`?${queryParams}${showActivities ? '' : `&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}`
		);
	};

	const handleClearClick = () => {
		sort = [];
		title = '';
		name = '';
		crid = '';
		media = '';
		executedYear = '';
		executedYearRange = ValueFilterOperator.Equal;
		activityId = '';
		artworkType = [];
		activityType = [];
		activityStatus = [];
		height = '';
		heightRange = ValueFilterOperator.Equal;
		width = '';
		widthRange = ValueFilterOperator.Equal;
		editionSize = '';
		editionSizeRange = ValueFilterOperator.Equal;
		activityDate = '';
		activityDateRange = ValueFilterOperator.Equal;
		saleAmount = '';
		saleAmountRange = ValueFilterOperator.Equal;
		lotNumber = '';
		saleName = '';
		saleNumber = '';
		sortResultsBy = '';
		association = '';
		associationType = '';
		omittedActivityIds = '';
		omittedArtworkIds = '';
		hideItemsWithoutImages = false;
		showFavouriteArtists = false;
	};
</script>

<div class="rounded-md lg:border lg:border-gray-200 bg-white">
	<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0">
		<div class="col-span-1">
			<ArtistName {dataCyPrefix} bind:name />
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-artwork-title`}
				name="name"
				placeholder="Enter artwork title or ID"
				label="Artwork title or ID"
				bind:value={title}
				size="sm"
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-crid`}
				name="crid"
				placeholder="Eg. H-12-1"
				bind:value={crid}
				label="CRID"
				size="sm"
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-media`}
				name="media"
				placeholder="Enter media"
				label="Media"
				bind:value={media}
				size="sm"
			/>
		</div>
		<div class="z-40 col-span-1">
			<InputWithSelect
				size="sm"
				label="Executed year"
				dataCy={`${dataCyPrefix}-executed-year`}
				name="executedYear"
				bind:selectValue={executedYearRange}
				bind:inputValue={executedYear}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[executedYearRange]}
			/>
		</div>
		<div class="z-40 col-span-1">
			<MultiSelect
				name="artworkType"
				dataCy={`${dataCyPrefix}-artwork-type`}
				label="Artwork type"
				bind:selected={artworkType}
				placeholder="Artwork type"
				options={artworkTypeOptions}
				class="col-span-1"
				size="sm"
			/>
		</div>
	</div>

	<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0">
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-activity-id`}
				name="activityId"
				placeholder="Enter activity ID"
				label="Activity ID"
				bind:value={activityId}
				size="sm"
			/>
		</div>

		<div class="z-30 col-span-1">
			<MultiSelect
				name="activityType"
				dataCy={`${dataCyPrefix}-activity-type`}
				label="Activity type"
				bind:selected={activityType}
				placeholder="Activity type"
				options={activityTypeOptions}
				class="col-span-1"
				size="sm"
			/>
		</div>
		<div class="z-30 col-span-1">
			<MultiSelect
				name="activityStatus"
				dataCy={`${dataCyPrefix}-activity-status`}
				label="Activity status"
				bind:selected={activityStatus}
				placeholder="Activity status"
				options={activityStatusTypeOptions}
				class="col-span-1"
				size="sm"
			/>
		</div>
		<div class="z-30 col-span-1">
			<InputWithSelect
				size="sm"
				label="Height (cm)"
				dataCy={`${dataCyPrefix}-height`}
				name="height"
				bind:selectValue={heightRange}
				bind:inputValue={height}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder="0"
			/>
		</div>
		<div class="z-30 col-span-1">
			<InputWithSelect
				size="sm"
				label="Width (cm)"
				dataCy={`${dataCyPrefix}-width`}
				name="width"
				bind:selectValue={widthRange}
				bind:inputValue={width}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder="0"
			/>
		</div>
		<div class="z-30 col-span-1">
			<InputWithSelect
				size="sm"
				label="Edition size"
				dataCy={`${dataCyPrefix}-edition-size-year`}
				name="editionSize"
				bind:selectValue={editionSizeRange}
				bind:inputValue={editionSize}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder="0"
			/>
		</div>
	</div>

	<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0">
		<div class="z-20 col-span-2">
			<InputWithSelect
				size="sm"
				label="Activity date (DD/MM/YYYY)"
				dataCy={`${dataCyPrefix}-activity-date`}
				name="activityDate"
				bind:selectValue={activityDateRange}
				bind:inputValue={activityDate}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[activityDateRange]}
			/>
		</div>
		<div class="z-20 col-span-1">
			<InputWithSelect
				size="sm"
				label="Sold for (USD)"
				dataCy={`${dataCyPrefix}-sold-for`}
				name="saleAmount"
				bind:selectValue={saleAmountRange}
				bind:inputValue={saleAmount}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder="0"
				onkeydown={(e) => {
					handleKeyDownNumbersOnly(e, true);
					formatWithThousandSeparator(e);
				}}
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-lot-number`}
				name="lotNumber"
				placeholder="Eg. 24 or 8-12"
				label="Lot number"
				bind:value={lotNumber}
				size="sm"
			/>
		</div>
	</div>

	<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0">
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-sale-name`}
				name="saleName"
				placeholder=""
				label="Sale name"
				bind:value={saleName}
				size="sm"
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-sale-number`}
				name="saleNumber"
				placeholder=""
				label="Sale number"
				bind:value={saleNumber}
				size="sm"
			/>
		</div>
		<div class="col-span-2">
			<MultiSelect
				name="sort_by"
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				bind:selected={sort}
				placeholder="Sort by"
				options={NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS}
				class="col-span-1"
				size="sm"
				maxLabelLength={40}
			/>
		</div>
		<div class="col-span-2">
			<InputWithSelect
				size="sm"
				label="Association"
				dataCy={`${dataCyPrefix}-association`}
				name="association"
				bind:selectValue={associationType}
				bind:inputValue={association}
				options={associationTypeOptions}
				placeholder="Name or entity ID"
			/>
		</div>
	</div>

	<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0">
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-omitted-artwork-ids`}
				name="omittedArtworkIds"
				placeholder="Comma separated IDs"
				label="Omit artwork IDs"
				bind:value={omittedArtworkIds}
				size="sm"
				rows={3}
			/>
		</div>

		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-omitted-activity-ids`}
				name="omittedActivityIds"
				placeholder="Comma separated IDs"
				label="Omit activity IDs"
				bind:value={omittedActivityIds}
				size="sm"
				rows={3}
			/>
		</div>

		<div class="col-span-1 flex h-full">
			<InputLabel
				dataCy={`${dataCyPrefix}-hide-items-without-images`}
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-hide-items-without-images`}
					bind:checked={hideItemsWithoutImages}
				/>
				Hide items without images
			</InputLabel>
		</div>

		<div class="col-span-1 flex h-full">
			<InputLabel
				dataCy={`${dataCyPrefix}-show-favourite-artists`}
				variant="body3"
			>
				<Checkbox
					dataCy={`${dataCyPrefix}-show-favourite-artists`}
					bind:checked={showFavouriteArtists}
				/>
				Favourite artists only
			</InputLabel>
		</div>
	</div>

	<div class="flex flex-col md:flex-row gap-4 lg:px-4 py-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={() => handleSearchClick(true)}
		>
			Search activities
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={() => handleSearchClick(false)}
		>
			Search artworks
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-save-preset`}
			variant="secondary"
			onclick={handleClickPreset}
		>
			Save preset
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>

<PresetDialog dialogStores={presetDialogStores} url={presetUrl} />
