import { ActivitySearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const NEW_ARTWORKS_ACTIVITY_SORT_OPTIONS = sortByField(
	[
		{
			label: 'Activity status (Asc)',
			value: ActivitySearchSortField.ActivityDate,
		},
		{
			label: 'Activity status (Desc)',
			value: getDescendingDirection(ActivitySearchSortField.ActivityDate),
		},

		{
			label: 'Artwork title (A-Z)',
			value: ActivitySearchSortField.ArtworkTitle,
		},
		{
			label: 'Artwork title (Z-A)',
			value: getDescendingDirection(ActivitySearchSortField.ArtworkTitle),
		},

		{ label: 'Artist name (A-Z)', value: ActivitySearchSortField.ArtistName },
		{
			label: 'Artist name (Z-A)',
			value: getDescendingDirection(ActivitySearchSortField.ArtistName),
		},

		{ label: 'Price (Asc)', value: ActivitySearchSortField.Price },
		{
			label: 'Price (Desc)',
			value: getDescendingDirection(ActivitySearchSortField.Price),
		},

		{
			label: 'High estimate (Asc)',
			value: ActivitySearchSortField.HighEstimate,
		},
		{
			label: 'High estimate (Desc)',
			value: getDescendingDirection(ActivitySearchSortField.HighEstimate),
		},
		{
			label: 'Oldest updated record',
			value: ActivitySearchSortField.DateUpdated,
		},
		{
			label: 'Latest updated record',
			value: getDescendingDirection(ActivitySearchSortField.DateUpdated),
		},

		{ label: 'Lot number (Asc)', value: ActivitySearchSortField.LotNumber },
		{
			label: 'Lot number (Desc)',
			value: getDescendingDirection(ActivitySearchSortField.LotNumber),
		},

		{
			label: 'Low estimate (Asc)',
			value: ActivitySearchSortField.LowEstimate,
		},
		{
			label: 'Low estimate (Desc)',
			value: getDescendingDirection(ActivitySearchSortField.LowEstimate),
		},
	],
	'label'
);

export const NEW_ARTWORKS_ACTIVITY_DEFAULT_SORT = getDescendingDirection(
	ActivitySearchSortField.DateUpdated
) as ActivitySearchSortField;

export const ARTWORKS_ACTIVITY_DEFAULT_SORT = 'artworks.artwork.title';
