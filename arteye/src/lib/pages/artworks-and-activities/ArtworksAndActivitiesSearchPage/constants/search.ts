import { SearchParam } from '$lib/types/types';

export enum ArtActBasicSearchParam {
	ShowResults = SearchParam.ShowResults,
	Sort = SearchParam.Sort,
	ResultsTab = 'resultsTab',
	Name = 'name',
	Title = 'title',
	Crid = 'crid',
	Media = 'media',
	ExecutedYear = 'executedYear',
	ExecutedYearRange = 'executedYearRange',
	ActivityId = 'activityId',
	ArtworkType = 'artworkType',
	ActivityType = 'activityType',
	ActivityStatus = 'activityStatus',
	Height = 'height',
	HeightRange = 'heightRange',
	Width = 'width',
	WidthRange = 'widthRange',
	EditionSize = 'editionSize',
	EditionSizeRange = 'editionSizeRange',
	ActivityDate = 'activityDate',
	ActivityDateRange = 'activityDateRange',
	SaleAmount = 'saleAmount',
	SaleAmountRange = 'saleAmountRange',
	LotNumber = 'lotNumber',
	SaleName = 'saleName',
	SaleNumber = 'saleNumber',
	SortResultsBy = 'sortResultsBy',
	Association = 'association',
	AssociationType = 'associationType',
	OmittedArtworkIds = 'omittedArtworkIds',
	OmittedActivityIds = 'omittedActivityIds',
	HideItemsWithoutImages = 'hideItemsWithoutImages',
	ShowFavouriteArtists = 'showFavouriteArtists',
}

export enum ArtActAdvancedSearchParam {
	ShowResults = SearchParam.ShowResults,
}

export enum ArtworkAdvancedSearchParam {
	ResultsTab = SearchParam.ResultsTab,
	// Artwork details
	Sort = SearchParam.Sort,
	Name = 'name',
	Title = 'title',
	Crid = 'crid',
	Media = 'media',
	ExecutedYear = 'executedYear',
	ExecutedYearRange = 'executedYearRange',
	ArtworkType = 'artworkType',
	Height = 'height',
	HeightRange = 'heightRange',
	Width = 'width',
	WidthRange = 'widthRange',
	Depth = 'depth',
	DepthRange = 'depthRange',
	NumberOfPieces = 'numberOfPieces',
	NumberOfArtworks = 'numberOfArtworks',
	ShowFavouriteArtists = 'showFavouriteArtists',
	IsHeniArtwork = 'isHeniArtwork',
	IsBundle = 'isBundle',
	IsFullSet = 'isFullSet',
	HeniArtworkType = 'heniArtworkType',

	// Editions & series
	EditionSize = 'editionSize',
	EditionSizeRange = 'editionSizeRange',
	EditionNumber = 'editionNumber',
	SeriesTitle = 'seriesTitle',
	SeriesCrid = 'seriesCrid',
	SeriesSize = 'seriesSize',
	SeriesSizeRange = 'seriesSizeRange',
	IsUnlimitedEdition = 'isUnlimitedEdition',
	ExcludeMultiples = 'excludeMultiples',

	// Tags
	CategoryTags = 'categoryTags',
	SubjectTags = 'subjectTags',
}

export enum ActivityAdvancedSearchParam {
	// Activity details
	ActivityType = 'activityType',
	ListingType = 'listingType',
	ActivityStatus = 'activityStatus',
	ActivityDate = 'activityDate',
	ActivityDateRange = 'activityDateRange',
	ListingPrice = 'listingPrice',
	ListingPriceRange = 'listingPriceRange',
	SaleAmount = 'saleAmount',
	SaleAmountRange = 'saleAmountRange',
	Association = 'association',
	AssociationType = 'associationType',

	// Auction details
	AuctionHouse = 'auctionHouse',
	AuctionName = 'auctionNameOrId',
	AuctionSaleName = 'auctionSaleName',
	SaleNumber = 'saleNumber',
	LotNumber = 'lotNumber',
	AuctionType = 'auctionType',

	// Fair Details
	FairName = 'fairName',
	FairDate = 'fairDate',
	FairDateRange = 'fairDateRange',
	FairExhibitor = 'fairExhibitor',

	// Gallery details
	GalleryName = 'galleryName',
	GalleryCity = 'galleryCity',
	GalleryCountry = 'galleryCountry',

	// Exhibition details
	ExhibitionName = 'exhibitionName',
	ExhibitionDate = 'exhibitionDate',
	ExhibitionDateRange = 'exhibitionDateRange',
	ExhibitionCity = 'exhibitionCity',
	ExhibitionCountry = 'exhibitionCountry',
}

export enum DataEntryAdvancedSearchParam {
	// Data entry
	CreatedBy = 'createdBy',
	CreatedDate = 'createdDate',
	CreatedDateRange = 'createdDateRange',
	UpdatedBy = 'updatedBy',
	UpdatedByDate = 'updatedByDate',
	UpdatedByDateRange = 'updatedByDateRange',
	Source = 'source',
}

export enum OmittedItemsSearchParam {
	// Omitted Items
	OmittedArtworkIds = 'omittedArtworkIds',
	OmittedActivityIds = 'omittedActivityIds',
	HideItemsWithoutImages = 'hideItemsWithoutImages',
}

export enum MultipleIdsSearchParam {
	ShowResults = SearchParam.ShowResults,
	ArtworkIds = 'artworkIds',
	ActivityIds = 'activityIds',
	Type = 'type',
}

export enum ImageUploadSearchParam {
	ShowResults = SearchParam.ShowResults,
	Image = 'image',
}

export enum ArtActSearchType {
	Basic = 'basic',
	Advanced = 'advanced',
	MultipleIds = 'multipleIds',
	Presets = 'presets',
	ByImage = 'byImage',
}
