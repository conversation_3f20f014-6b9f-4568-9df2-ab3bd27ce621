<script lang="ts">
	import {
		ArtActBasicSearchParam,
		ArtActSearchType,
	} from '../constants/search';
	import { CopyIdsButton } from './CopyIdsButton';
	import { NewActivityCard } from './NewActivityCard';
	import { NewArtworkCard } from './NewArtworkCard';
	import { ArtworkAndActivitiesResultsTab } from './types';
	import { page } from '$app/state';
	import { Toggle } from '$global/components/Toggle';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import ElasticPaginationBar from '$lib/components/ElasticPaginationBar/ElasticPaginationBar.svelte';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { SearchParam } from '$lib/types/types';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';
	import { goto } from '$app/navigation';

	const dataCy = 'activity-results';

	let data = $derived(
		getPageData<ArtworksAndActivitiesSearchPageData>(page.data)
	);

	let sectionInfo = $derived(data.sectionInfo);
	let resultsCount = $state(0);
	let error = $state('');
	let nextToken = $state('');
	let previousToken = $state('');
	let tokens = $state([] as string[]);

	let searchParams = $derived(page.url.searchParams);
	let resultsTab = $derived(
		searchParams.get(ArtActBasicSearchParam.ResultsTab) ||
			ArtworkAndActivitiesResultsTab.Activities
	);

	let activities = $derived(data.activities);
	let artworks = $derived(data.artworks);

	let artworkIds = $derived(data.artworkIds);
	let activitiesIds = $derived(data.activitiesIds);

	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);
	let pageSize = $derived(
		Number(searchParams.get(SearchParam.PageSize)) || TABLE_PAGE_SIZE
	);

	let showActivities = $state(
		(() => {
			return resultsTab === ArtworkAndActivitiesResultsTab.Activities;
		})()
	) as boolean;

	$effect(() => {
		const info = showActivities ? sectionInfo.activities : sectionInfo.artworks;

		error = info.error;
		resultsCount = info.resultsCount || 0;
		nextToken = info.nextToken;
		previousToken = info.previousToken;
		tokens = info.tokens;
	});

	$effect(() => {
		showActivities = resultsTab === ArtworkAndActivitiesResultsTab.Activities;
	});

	const handleToggleClick = async (value: boolean) => {
		showActivities = value;

		if (
			[null, ArtActSearchType.Basic, ArtActSearchType.Advanced].includes(
				searchParams.get('searchType') as null | ArtActSearchType
			)
		) {
			const url = new URL(page.url);

			url.searchParams.delete('paginationDirection');
			url.searchParams.delete('page');
			url.searchParams.delete('paginationToken');

			if (value) {
				url.searchParams.delete('resultsTab');
			} else {
				url.searchParams.set(
					'resultsTab',
					ArtworkAndActivitiesResultsTab.Artworks
				);
			}

			url.searchParams.set('showResults', 'true');

			await goto(url.toString(), {
				replaceState: true, // Don't add to browser history
				noScroll: true, // Don't scroll to top
			});
		}

		setTimeout(() => {
			document.getElementById('results-count')?.scrollIntoView();
		}, 0);
	};

	$effect(() => {
		if (searchParams.toString()) {
			setTimeout(
				() => {
					document.getElementById('results-count')?.scrollIntoView();
				},
				!resultsCount ? 1000 : 0
			);
		}
	});
</script>

<div class="pb-[120px] md:pb-16">
	<div
		class="relative flex flex-col md:flex-row w-full md:items-center justify-between gap-4"
	>
		<span id="results-count" class="absolute top-[-64px]"></span>
		<Txt variant="h6" class="mb-2">
			{#if [ArtActSearchType.ByImage, ArtActSearchType.MultipleIds].includes(searchParams.get('searchType') as ArtActSearchType)}
				Showing {resultsCount} results
			{:else}
				{getPaginationResultsText({
					currentPage,
					total: resultsCount,
					pageSize,
				})}
			{/if}
		</Txt>

		<div class="mb-4 flex flex-col md:flex-row md:items-center gap-4 md:gap-0">
			<div class="max-w-[240px]">
				<CopyIdsButton {artworkIds} {activitiesIds} {showActivities} />
			</div>

			<div class="flex items-center">
				<Txt variant="label3" class="p-4">VIEW:</Txt>
				<Toggle
					dataCy={`${dataCy}-tab`}
					leftLabel="ACTIVITIES"
					rightLabel="ARTWORKS"
					value={showActivities}
					onClick={handleToggleClick}
				/>
			</div>
		</div>
	</div>

	{#if showActivities}
		<div class="flex flex-col lg:grid grid-cols-5 gap-2">
			{#if activities.length}
				{#each activities as artworkActivity, i}
					<NewActivityCard
						{dataCy}
						{artworkActivity}
						token={tokens[i]}
						prepareClickLink={() => {
							return Promise.resolve();
						}}
					/>
				{/each}
			{/if}
		</div>
	{:else}
		<div class="flex flex-col lg:grid gap-2">
			{#if error}
				{#if error === 'payload'}
					<Txt variant="label1" class="text-center max-w-[500px] mx-auto my-8">
						Maximum payload size reached. Please select a smaller page size to
						see results.
					</Txt>
				{:else}
					<Txt variant="label1" class="text-center max-w-[500px] mx-auto my-8">
						Internal error.
					</Txt>
				{/if}
			{:else if artworks.length}
				{#each artworks as artwork, i}
					<NewArtworkCard
						{dataCy}
						token={tokens[i]}
						{artwork}
						prepareClickLink={() => {
							return Promise.resolve();
						}}
					/>
				{/each}
			{/if}
		</div>
	{/if}
</div>

<ElasticPaginationBar
	{dataCy}
	{searchParams}
	total={resultsCount}
	{pageSize}
	{currentPage}
	{nextToken}
	{previousToken}
/>
