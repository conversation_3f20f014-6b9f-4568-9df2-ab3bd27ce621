<script lang="ts">
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { Button } from '$global/components/Button';
	import { showToast } from '$global/components/Toasts';

	interface Props {
		showActivities: boolean;
		activitiesIds: string[];
		artworkIds: string[];
	}

	let { showActivities, activitiesIds, artworkIds }: Props = $props();

	const dataCy = 'copy-ids';

	let buttonText = $derived(
		showActivities ? 'Copy activities on page' : 'Copy artworks on page'
	);

	const handleCopyClick = () => {
		const ids = showActivities
			? activitiesIds.join(', ')
			: artworkIds.join(', ');

		navigator.clipboard.writeText(ids);

		showToast({
			message: `${showActivities ? 'Activity' : 'Artwork'} IDs copied to clipboard`,
			variant: 'success',
		});
	};
</script>

<Button
	size="sm"
	dataCy={`${dataCy}-button`}
	variant="secondary"
	onclick={handleCopyClick}
>
	{#snippet leading()}
		<CopyIcon />
	{/snippet}

	{buttonText}
</Button>
