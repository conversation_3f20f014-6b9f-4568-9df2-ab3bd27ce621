<script lang="ts">
	import classNames from 'classnames';
	import { goto, preloadData } from '$app/navigation';
	import { Txt } from '$global/components/Txt';
	import type { Artwork_Artist, Maybe } from '$gql/types';
	import { Edition_Number_Type_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';

	interface Props {
		dataCyPrefix?: string;
		artists?: Maybe<Maybe<Artwork_Artist>[]> | undefined;
		artworkTitle?: string | undefined | null;
		crid?: string | undefined | null;
		id?: string | undefined | null;
		yearExecutedStart?: number | undefined | null;
		yearExecutedEnd?: number | undefined | null;
		medium?: string | undefined | null;
		height?: number | undefined | null;
		width?: number | undefined | null;
		depth?: number | undefined | null;
		editionNumberType?: Edition_Number_Type_Enum | undefined | null;
		editionNumber?: string | undefined | null;
		editionSize?: number | undefined | null;
		apSize?: number | undefined | null;
		hcSize?: number | undefined | null;
		prepareClickLink: () => Promise<void>;
		class?: string;
	}

	let {
		prepareClickLink,
		editionNumberType = undefined,
		editionNumber = undefined,
		editionSize = undefined,
		apSize = undefined,
		hcSize = undefined,
		medium = undefined,
		height = undefined,
		width = undefined,
		depth = undefined,
		dataCyPrefix,
		artists = undefined,
		artworkTitle = undefined,
		crid = undefined,
		id = undefined,
		yearExecutedStart = undefined,
		yearExecutedEnd = undefined,
		...rest
	}: Props = $props();

	const handleClickArtworkCard = async (e: MouseEvent) => {
		if (window.getSelection()?.toString()) {
			return;
		}

		await prepareClickLink();
		const route = `${Routes.ArtworkDetails}/${id}`;

		if (e.ctrlKey || e.metaKey) {
			window.open(route, '_open');
		} else {
			goto(route);
		}
	};

	// const handleHoverArtworkCard = () => {
	// 	preloadData(`${Routes.ArtworkDetails}/${id}`);
	// };

	let artistsInfo = $derived(
		(artists ?? []).map((artist) => {
			const person = artist?.artist_id?.person;
			const firstName = person?.first_name ?? '';
			const lastName = person?.last_name ?? '';
			const displayName = `${firstName} ${lastName}`.trim();

			const birthYear = person?.year_birth ?? '';
			const deathYear = person?.year_death ?? '';
			const displayLifespan = deathYear
				? `${birthYear} - ${deathYear}`
				: birthYear
					? `b.${birthYear}`
					: '';

			const nationality = person?.nationalities?.[0]?.country?.name;

			const details = [nationality, displayLifespan].filter(Boolean).join(', ');

			return details ? `${displayName} (${details})` : displayName;
		})
	);

	const formatEditionDetails = (
		editionNumberType?: Edition_Number_Type_Enum | null,
		editionNumber?: string | null,
		editionSize?: number | null,
		apSize?: number | null,
		hcSize?: number | null
	) => {
		const getAbbreviatedEditionType = (
			type: Edition_Number_Type_Enum | null | undefined
		) => {
			switch (type) {
				case Edition_Number_Type_Enum.ArtistsProof:
					return 'AP';
				case Edition_Number_Type_Enum.GeneralProof:
					return 'GP';
				case Edition_Number_Type_Enum.HorsDeCommerce:
					return 'HC';
				case Edition_Number_Type_Enum.Regular:
					return 'Ed.';
				default:
					return '';
			}
		};

		const abbreviatedEditionType = getAbbreviatedEditionType(editionNumberType);

		const editionPart = [abbreviatedEditionType, editionNumber]
			.filter(Boolean)
			.join(' ');

		const sizePart = [
			editionSize ? `${editionSize} Editions` : '',
			apSize ? `${apSize} AP` : '',
			hcSize ? `${hcSize} HC` : '',
		]
			.filter(Boolean)
			.join(', ');

		return `${editionPart}${sizePart && editionPart ? ` (${sizePart})` : sizePart}`;
	};

	let yearExecuted = $derived(
		(() => {
			if (yearExecutedStart && yearExecutedEnd) {
				return `${yearExecutedStart} - ${yearExecutedEnd}`;
			}
			return yearExecutedStart || yearExecutedEnd;
		})()
	);
</script>

<button
	onclick={handleClickArtworkCard}
	class={classNames('text-left', rest.class)}
>
	<div class="p-3">
		{#each artistsInfo as artistInfo}
			<Txt
				variant="strong2"
				dataCy={`${dataCyPrefix}-artist-info`}
				class="mb-1 line-clamp-1 text-gray-500"
			>
				{artistInfo}
			</Txt>
		{/each}
		<Txt variant="h6" dataCy={`${dataCyPrefix}-artwork-title`} class="mb-1">
			{artworkTitle}
		</Txt>
	</div>
	<div class="border-b border-gray-200"></div>
	<div class="p-3 flex flex-col gap-1 min-h-[42px] pb-10">
		{#if crid}
			<Txt
				variant="body2"
				dataCy={`${dataCyPrefix}-crid`}
				class="text-gray-700"
			>
				CR {crid}
			</Txt>
		{/if}
		{#if yearExecuted}
			<Txt
				variant="body2"
				dataCy={`${dataCyPrefix}-year-executed`}
				class="text-gray-700"
			>
				{yearExecuted}
			</Txt>
		{/if}
		{#if medium}
			<Txt
				variant="body2"
				dataCy={`${dataCyPrefix}-medium`}
				class="text-gray-700"
			>
				{medium}
			</Txt>
		{/if}
		{#if height && width}
			<Txt
				variant="body2"
				dataCy={`${dataCyPrefix}-dimensions`}
				class="text-gray-700"
			>
				{height} x {width}
				{#if depth}
					x {depth}{/if} cm
			</Txt>
		{/if}
		<Txt
			variant="body2"
			dataCy={`${dataCyPrefix}-edition-number-type`}
			class="text-gray-700"
		>
			{formatEditionDetails(
				editionNumberType,
				editionNumber,
				editionSize,
				apSize,
				hcSize
			)}
		</Txt>
	</div>
</button>
