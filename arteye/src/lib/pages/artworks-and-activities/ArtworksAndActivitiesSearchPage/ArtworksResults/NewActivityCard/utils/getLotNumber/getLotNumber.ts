import { Artwork_Activity_Type_Enum } from '$gql/types-custom';
import type { ArtworkActivityFragment } from '$lib/custom-queries/__generated__/activitySearch.generated';
import { ArtworkListingKey } from '$lib/types/types';

export const getLotNumber = (artworkActivity: ArtworkActivityFragment) => {
	const number = artworkActivity?.artwork_listing?.find(
		(listing) =>
			(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
			ArtworkListingKey.Auction
	)?.auction_lot?.lot_number;

	return artworkActivity?.type?.key === Artwork_Activity_Type_Enum.Auction &&
		number
		? number
		: null;
};
