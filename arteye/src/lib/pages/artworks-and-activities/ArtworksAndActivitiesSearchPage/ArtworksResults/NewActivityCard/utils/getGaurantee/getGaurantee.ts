import {
	Artwork_Activity_Type_Enum,
	Auction_Lot_Attribute_Type_Enum,
} from '$gql/types-custom';
import { ArtworkListingKey } from '$lib/types/types';
import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';

export const getGaurantee = (
	artworkActivity: ArtworksAndActivitiesSearchPageData['activities'][number]
) => {
	if (artworkActivity?.type?.key === Artwork_Activity_Type_Enum.Auction) {
		const ib = artworkActivity?.artwork_listing
			?.find(
				(listing) =>
					(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
					ArtworkListingKey.Auction
			)
			?.auction_lot?.attributes?.find(
				(attribute) =>
					attribute?.type?.key ===
					Auction_Lot_Attribute_Type_Enum.IrrevocableBid
			);

		const g = artworkActivity?.artwork_listing
			?.find(
				(listing) =>
					(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
					ArtworkListingKey.Auction
			)
			?.auction_lot?.attributes?.find(
				(attribute) =>
					attribute?.type?.key === Auction_Lot_Attribute_Type_Enum.Guaranteed
			);

		if (ib) {
			return {
				title: 'IB',
				text: ib?.type?.description as string,
			};
		} else if (g) {
			return {
				title: 'G',
				text: g?.type?.description as string,
			};
		}
		return null;
	} else {
		return null;
	}
};
