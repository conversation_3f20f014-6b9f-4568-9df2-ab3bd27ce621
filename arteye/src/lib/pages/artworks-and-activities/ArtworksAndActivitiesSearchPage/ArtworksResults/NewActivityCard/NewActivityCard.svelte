<script lang="ts">
	import dayjs from 'dayjs';
	import { getActivityHref } from '../../../ArtworkDetailsPage/utils/getActivityHref/getActivityHref';
	import { newGetDealerName } from '../../utils/newGetDealerName/newGetDealerName';
	import { newGetSaleName } from '../../utils/newGetSaleName/newGetSaleName';
	import { NewArtworkInfo } from '../NewArtworkInfo';
	import { getActivityDate } from './utils/getActivityDate/getActivityDate';
	import { getGaurantee } from './utils/getGaurantee/getGaurantee';
	import { getLotNumber } from './utils/getLotNumber/getLotNumber';
	import { getSaleAmount } from './utils/getSaleAmount/getSaleAmount';
	import { goto, preloadData } from '$app/navigation';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { HammerIcon } from '$global/assets/icons/HammerIcon';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';
	import type { Artwork_Artist } from '$gql/types';
	import {
		Artwork_Activity_Status_Type_Enum,
		Artwork_Activity_Type_Enum,
		Auction_Type_Enum,
	} from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import type { ArtworkActivityFragment } from '$lib/custom-queries/__generated__/activitySearch.generated';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';

	interface Props {
		artworkActivity: ArtworksAndActivitiesSearchPageData['activities'][number];
		dataCy: string;
		token: string;
		prepareClickLink: () => Promise<void>;
	}

	let { artworkActivity, dataCy, prepareClickLink, token }: Props = $props();
	const dataCyPrefix = `${dataCy}-activity-card`;

	let artworkActivityId = $derived(artworkActivity?.id);

	let artworkActivityFragment = $derived(
		artworkActivity as ArtworkActivityFragment
	);
	let activityDate = $derived(getActivityDate(artworkActivity));
	let dealerName = $derived(newGetDealerName(artworkActivityFragment));
	let saleName = $derived(newGetSaleName(artworkActivityFragment));
	let lotNumber = $derived(getLotNumber(artworkActivityFragment));
	let gaurantee = $derived(getGaurantee(artworkActivity));
	let price = $derived(getSaleAmount(artworkActivityFragment));

	let artists = $derived(
		artworkActivity?.artworks?.[0]?.artwork?.artists as Artwork_Artist[]
	);

	// let saleAmount = $derived(
	// 	artworkActivity?.artwork_listing?.[0]?.sale_amount?.amount
	// 		? new Intl.NumberFormat('en-US').format(
	// 				artworkActivity?.artwork_listing?.[0]?.sale_amount
	// 					?.amount
	// 			)
	// 		: null
	// );

	const handleCopyClick = (event: Event) => {
		event.preventDefault();
		event.stopPropagation();
		navigator.clipboard.writeText(`${artworkActivity?.id}`);
	};

	const handleCopyArtworkClick = (event: Event) => {
		event.preventDefault();
		event.stopPropagation();
		navigator.clipboard.writeText(
			`${artworkActivity?.artworks?.[0]?.artwork?.id}`
		);
	};

	const handleClickCard = async (e: MouseEvent) => {
		if (window.getSelection()?.toString()) {
			return;
		}

		await prepareClickLink();
		const route = getActivityHref({
			artworkActivityId: artworkActivityId || '',
			token,
		});

		if (e.ctrlKey || e.metaKey) {
			window.open(route, '_open');
		} else {
			goto(route);
		}
	};

	const handleClickArtworkCard = async (e: MouseEvent) => {
		if (window.getSelection()?.toString()) {
			return;
		}

		await prepareClickLink();
		const route = `${Routes.ArtworkDetails}/${artworkActivity?.artworks?.[0]?.artwork?.id}`;

		if (e.ctrlKey || e.metaKey) {
			window.open(route, '_open');
		} else {
			goto(route);
		}
	};

	// const handleHoverCard = () => {
	// 	preloadData(
	// 		getActivityHref({ artworkActivityId: artworkActivityId || '', token })
	// 	);
	// };

	// const handleHoverArtworkCard = () => {
	// 	preloadData(
	// 		`${Routes.ArtworkDetails}/${artworkActivity?.artworks?.[0]?.artwork?.id}`
	// 	);
	// };

	let lastActivityStatus = $derived(
		artworkActivity?.activity_status?.sort(
			(statusA, statusB) =>
				dayjs(statusA?.timestamp).unix() - dayjs(statusB?.timestamp).unix()
		)?.[artworkActivity?.activity_status.length - 1]
	);

	let showHammer = $derived(
		lastActivityStatus?.type?.key === Artwork_Activity_Status_Type_Enum.Sold &&
			artworkActivity?.artwork_listing?.[0]?.auction_lot &&
			!artworkActivity?.artwork_listing?.[0]?.auction_lot
				?.sale_amount_includes_premium
	);

	let imageUrl = $derived(
		getImageUrl(artworkActivity?.artworks?.[0]?.artwork?.primary_image?.id) ||
			getImageUrl(artworkActivity?.activity_artwork_info?.primary_image?.id)
	);

	const artworkActivityType =
		artworkActivity?.type?.key === Artwork_Activity_Type_Enum.Auction &&
		artworkActivity?.artwork_listing?.[0]?.auction_lot?.auction
			?.auction_types?.[0]?.auction_type_key?.key === Auction_Type_Enum.Online
			? 'Online auction'
			: artworkActivity?.type?.name;
</script>

<div>
	<div class="relative self-start h-full rounded border bg-gray-0">
		<button
			onclick={handleCopyClick}
			class="absolute right-0 top-0 m-1 rounded border p-2 active:bg-gray-100"
			data-cy={`${dataCy}-copy-button`}
		>
			<CopyIcon class="h-[16px] w-[16px]" />
		</button>
		<div class="select-text">
			<button onclick={handleClickCard} class="m-3 h-[9rem] w-full">
				<Txt
					variant="label3"
					class="text-left text-gray-700"
					dataCy={`${dataCyPrefix}-type-date`}
				>
					{artworkActivityType},
					{dayjs.utc(activityDate).format('DD MMM YYYY')}
				</Txt>
				{#if saleName && saleName.toLowerCase() !== artworkActivityType?.toLowerCase()}
					<Txt
						variant="body2"
						dataCy={`${dataCyPrefix}-sale-name`}
						class="text-left text-gray-700"
					>
						{saleName}
					</Txt>
				{/if}
				{#if dealerName}
					<Txt
						variant="body2"
						dataCy={`${dataCyPrefix}-dealer-name`}
						class="line-clamp-1 text-left text-gray-700"
					>
						{dealerName}
					</Txt>
				{/if}

				{#if lotNumber}
					<Txt
						variant="body2"
						dataCy={`${dataCyPrefix}-lot`}
						class="text-left text-gray-700"
					>
						Lot {lotNumber}
					</Txt>
				{/if}

				<div class="mt-1 flex flex-row-reverse items-center justify-end">
					{#if lastActivityStatus}
						<div
							class="rounded-md border border-gray-300 bg-gray-200 px-2 py-1"
						>
							<Txt variant="strong2" dataCy={`${dataCyPrefix}-price-type`}>
								{lastActivityStatus?.type?.name}
							</Txt>
						</div>
					{/if}
					{#if showHammer}
						<HammerIcon class="mr-1" />
					{/if}
					{#if price}
						<Txt
							variant="body2"
							class="mr-2 max-w-[45%] text-left"
							dataCy={`${dataCyPrefix}-price`}
						>
							{price}
						</Txt>
					{/if}
				</div>
			</button>
			<div class="border-b border-gray-200"></div>
			<div class="relative">
				{#if gaurantee}
					<Tooltip
						class="text-black absolute right-0 top-0 z-10 m-2 flex h-[26px] w-[26px] items-center justify-center rounded-full bg-white text-xs font-bold"
						classes={{
							content: 'w-60',
						}}
						{dataCy}
						content={gaurantee?.text}
					>
						<div>{gaurantee?.title}</div>
					</Tooltip>
				{/if}
				<button onclick={handleClickArtworkCard} class="w-full">
					<div
						class="flex h-[15rem] items-center justify-center bg-gray-200 p-2"
					>
						{#if imageUrl}
							<img
								class="max-h-full max-w-full"
								alt={artworkActivity?.artworks?.[0]?.artwork?.title || ''}
								src={imageUrl}
							/>
						{/if}
					</div>
				</button>
			</div>
			<NewArtworkInfo
				{artists}
				{prepareClickLink}
				class="w-full"
				id={artworkActivity?.artworks?.[0]?.artwork?.id}
				artworkTitle={artworkActivity?.artworks?.[0]?.artwork?.title}
				crid={artworkActivity?.artworks?.[0]?.artwork?.crid}
				yearExecutedStart={artworkActivity?.artworks?.[0]?.artwork
					?.execution_start_year}
				yearExecutedEnd={artworkActivity?.artworks?.[0]?.artwork
					?.execution_end_year}
				medium={artworkActivity?.artworks?.[0]?.artwork?.media}
				height={artworkActivity?.artworks?.[0]?.artwork?.dimensions_height_cm}
				width={artworkActivity?.artworks?.[0]?.artwork?.dimensions_width_cm}
				depth={artworkActivity?.artworks?.[0]?.artwork?.dimensions_depth_cm}
				editionNumberType={artworkActivity?.artworks?.[0]?.edition_number_type
					?.key}
				editionNumber={artworkActivity?.artworks?.[0]?.edition_number}
				editionSize={artworkActivity?.artworks?.[0]?.artwork?.edition_info
					?.edition_size_total}
				apSize={artworkActivity?.artworks?.[0]?.artwork?.edition_info
					?.artists_proof_size}
				hcSize={artworkActivity?.artworks?.[0]?.artwork?.edition_info
					?.hors_de_commerce_size}
			/>
		</div>
		<button
			onclick={handleCopyArtworkClick}
			class="absolute bottom-0 right-0 m-1 rounded border p-2 active:bg-gray-100"
			data-cy={`${dataCy}-copy-artwork-button`}
		>
			<CopyIcon class="h-[16px] w-[16px]" />
		</button>
	</div>
</div>
