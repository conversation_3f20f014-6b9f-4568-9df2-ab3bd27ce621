import type { Artwork_Activity } from '$gql/types';
import { ArtworkActivityType<PERSON><PERSON>, ArtworkListingKey } from '$lib/types/types';

export const getLotNumber = (artworkActivity: Artwork_Activity) => {
	const number = artworkActivity?.artwork_listing?.find(
		(listing) =>
			(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
			ArtworkListingKey.Auction
	)?.auction_lot?.lot_number;

	return artworkActivity.type?.key === ArtworkActivityTypeKey.Auction && number
		? number
		: null;
};
