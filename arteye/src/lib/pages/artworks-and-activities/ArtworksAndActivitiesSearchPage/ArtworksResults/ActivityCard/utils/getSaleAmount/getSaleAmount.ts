import type { Artwork_Activity } from '$gql/types';

export const getSaleAmount = (artworkActivity: Artwork_Activity) => {
	const knownAmount = artworkActivity?.artwork_listing?.[0]?.known_price
		?.usd_amount
		? new Intl.NumberFormat('en-US').format(
				artworkActivity?.artwork_listing?.[0]?.known_price?.usd_amount
			)
		: null;

	const saleAmount = artworkActivity?.artwork_listing?.[0]?.sale_amount
		?.usd_amount
		? new Intl.NumberFormat('en-US').format(
				artworkActivity?.artwork_listing?.[0]?.sale_amount?.usd_amount
			)
		: null;

	const priceLowEstimate = artworkActivity?.artwork_listing?.[0]
		?.price_low_estimate?.usd_amount
		? new Intl.NumberFormat('en-US').format(
				artworkActivity.artwork_listing[0].price_low_estimate.usd_amount
			)
		: null;
	const highPriceEstimate = artworkActivity?.artwork_listing?.[0]
		?.price_high_estimate?.usd_amount
		? new Intl.NumberFormat('en-US').format(
				artworkActivity.artwork_listing[0].price_high_estimate.usd_amount
			)
		: null;
	const range = `Est $${priceLowEstimate} - ${highPriceEstimate}`;

	if (saleAmount) {
		return `$${saleAmount}`;
	} else if (knownAmount) {
		return `$${knownAmount}`;
	} else if (priceLowEstimate && highPriceEstimate) {
		return range;
	} else if (priceLowEstimate) {
		return `Est $${priceLowEstimate}`;
	} else if (highPriceEstimate) {
		return `Est $${highPriceEstimate}`;
	} else {
		return null;
	}
};
