<script lang="ts">
	import dayjs from 'dayjs';
	import { getActivityHref } from '../../../../ArtworkDetailsPage/utils/getActivityHref/getActivityHref';
	import { getSaleAmount } from '../../NewActivityCard/utils/getSaleAmount/getSaleAmount';
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
	} from '$global/components/Table';
	import type { ArtworkActivityFragment } from '$lib/custom-queries/__generated__/activitySearch.generated';
	import { newGetDealerName } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/utils/newGetDealerName/newGetDealerName';
	import { newGetSaleName } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/utils/newGetSaleName/newGetSaleName';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';

	interface Props {
		artwork: ArtworksAndActivitiesSearchPageData['artworks'][number];
		token: string;
	}

	let { artwork, token }: Props = $props();

	let activities = $derived(artwork?.activity);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
	}

	interface Row {
		fields: {
			[HeaderFieldName.Id]: string;
			[HeaderFieldName.ActivityType]: string;
			[HeaderFieldName.Status]: string;
			[HeaderFieldName.DealerName]: string;
			[HeaderFieldName.SaleName]: string;
			[HeaderFieldName.Date]: string;
			[HeaderFieldName.Price]: string;
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.ActivityType,
			title: 'Activity type',
		},
		{
			fieldName: HeaderFieldName.Date,
			title: 'Date',
		},
		{
			fieldName: HeaderFieldName.Status,
			title: 'Status',
		},
		{
			fieldName: HeaderFieldName.Price,
			title: 'Price',
		},
		{
			fieldName: HeaderFieldName.DealerName,
			title: 'Main association',
		},
		{
			fieldName: HeaderFieldName.SaleName,
			title: 'Sale name',
		},
	];

	let rows = $derived(
		[...(activities || [])]
			.sort((activityA, activityB) => {
				const dateA = activityA?.artwork_activity?.timestamp;
				const dateB = activityB?.artwork_activity?.timestamp;

				if (!dateA && !dateB) {
					return 0;
				}

				if (!dateA) {
					return 1;
				}

				if (!dateB) {
					return -1;
				}

				if (dateA < dateB) {
					return 1;
				}

				if (dateA > dateB) {
					return -1;
				}

				return 0;
			})
			.map((activity) => {
				if (!activity) return null;

				const row: Row = {
					fields: {
						id: activity?.artwork_activity?.id || '',
						activityType: activity?.artwork_activity?.type?.name || '',
						status:
							activity?.artwork_activity?.activity_status?.[0]?.type?.name ||
							'',
						dealerName: newGetDealerName(activity?.artwork_activity),
						saleName: newGetSaleName(activity?.artwork_activity),
						date: dayjs
							.utc(activity?.artwork_activity?.timestamp)
							.format('DD MMMM YYYY'),
						price: getSaleAmount(
							activity?.artwork_activity as ArtworkActivityFragment
						),
					},
				};

				return row;
			})
			.filter(Boolean) as Row[]
	);

	const dataCyPrefix = 'artwork-activities-table';

	const handleClickRow = (row: Row, e?: MouseEvent) => {
		if (window.getSelection()?.toString()) {
			return;
		}

		const route = getActivityHref({
			artworkActivityId: row.fields[HeaderFieldName.Id],
			token,
		});

		if (e) {
			e.preventDefault();
			e.stopImmediatePropagation();

			if (e.ctrlKey || e.metaKey) {
				window.open(route, '_blank');
				return;
			}
		}

		goto(route);
	};
</script>

<div class="p-4 pb-0 flex justify-between rounded-t-md bg-white">
	<div class="h-[240px] w-full overflow-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[700px]">
			<TableHeaderRow dataCy={dataCyPrefix} class="sticky top-0 z-10 bg-white">
				{#each headers as header, i}
					<TableHeader dataCy={dataCyPrefix} class="text-center">
						{header.title}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody dataCy={dataCyPrefix}>
				{#each rows as row, i}
					<TableRow
						index={i}
						dataCy={dataCyPrefix}
						class="select-text"
						onclick={(e) => {
							handleClickRow(row, e);
						}}
					>
						{#each headers as header}
							{@const value = row?.fields[header.fieldName]}

							<TableCell
								dataCy={`${dataCyPrefix}-${header.fieldName}-cell`}
								content={value}
								class="h-[32px] py-0 text-left"
							>
								{value || '-'}
							</TableCell>
						{/each}
					</TableRow>
				{/each}
			</TableBody>
		</table>
	</div>
</div>
