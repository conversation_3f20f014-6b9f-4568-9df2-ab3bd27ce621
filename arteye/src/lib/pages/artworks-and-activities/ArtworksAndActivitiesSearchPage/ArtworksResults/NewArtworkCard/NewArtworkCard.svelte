<script lang="ts">
	import { NewArtworkInfo } from '../NewArtworkInfo';
	import { ArtworkActivityList } from './ArtworkActivityList';
	import { goto, preloadData } from '$app/navigation';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import type { Artwork_Artist } from '$gql/types';
	import { Routes } from '$lib/constants/routes';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';

	interface Props {
		artwork: ArtworksAndActivitiesSearchPageData['artworks'][number];
		dataCy: string;
		token: string;
		prepareClickLink: () => Promise<void>;
	}

	let { artwork, dataCy, prepareClickLink, token }: Props = $props();

	const handleClickArtworkCard = async (e: MouseEvent) => {
		if (window.getSelection()?.toString()) {
			return;
		}

		await prepareClickLink();
		const route = `${Routes.ArtworkDetails}/${artwork?.id}`;

		if (e.ctrlKey || e.metaKey) {
			e.stopImmediatePropagation();
			window.open(route, '_blank');
		} else {
			goto(`${Routes.ArtworkDetails}/${artwork?.id}`);
		}
	};

	// const handleHoverArtworkCard = () => {
	// 	preloadData(`${Routes.ArtworkDetails}/${artwork?.id}`);
	// };

	const handleCopyArtworkClick = (event: Event) => {
		event.preventDefault();
		event.stopPropagation();
		navigator.clipboard.writeText(`${artwork?.id}`);
	};

	let dataCyPrefix = `${dataCy}-artwork-card`;

	let artists = $derived(artwork?.artists as Artwork_Artist[]);

	let imageUrl = $derived(getImageUrl(artwork?.primary_image?.id));
</script>

<button
	onclick={handleClickArtworkCard}
	class="flex flex-col lg:flex-row rounded border bg-gray-0"
>
	<div
		class="relative flex flex-col items-center lg:items-start lg:flex-row select-text"
	>
		<div
			class="flex h-[16rem] w-[16rem] items-center justify-center bg-gray-200 p-2"
		>
			{#if imageUrl}
				<img
					class="max-h-full max-w-full"
					alt={artwork?.title || ''}
					src={imageUrl}
				/>
			{/if}
		</div>

		<NewArtworkInfo
			class="lg:w-[16rem] lg:border-x lg:border-gray-200"
			{artists}
			{prepareClickLink}
			id={artwork?.id}
			artworkTitle={artwork?.title}
			crid={artwork?.crid}
			yearExecutedStart={artwork?.execution_start_year}
			yearExecutedEnd={artwork?.execution_end_year}
			medium={artwork?.media}
			height={artwork?.dimensions_height_cm}
			width={artwork?.dimensions_width_cm}
			depth={artwork?.dimensions_depth_cm}
			editionSize={artwork?.edition_info?.edition_size_total}
			apSize={artwork?.edition_info?.artists_proof_size}
			hcSize={artwork?.edition_info?.hors_de_commerce_size}
		/>

		<!-- eslint-disable-next-line svelte/valid-compile -->
		<div
			onclick={handleCopyArtworkClick}
			class="absolute bottom-0 right-0 m-1 rounded border p-2 active:bg-gray-100"
			data-cy={`${dataCy}-copy-artwork-button`}
		>
			<CopyIcon class="h-[16px] w-[16px]" />
		</div>
	</div>

	<ArtworkActivityList {token} {artwork} />
</button>
