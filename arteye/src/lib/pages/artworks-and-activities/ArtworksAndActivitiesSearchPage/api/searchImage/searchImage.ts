import { Config } from '$lib/constants/config';

export const searchImage = async (imageFile: File): Promise<string[]> => {
	const formData = new FormData();
	formData.append('file', imageFile);

	const response = await fetch(
		`${Config.ClientGraphqlApiDomain}/advanced/image-search`,
		{
			method: 'POST',
			body: formData,
		}
	);

	if (!response.ok) {
		throw new Error('Failed to upload image');
	}

	const data = await response.json();
	return data.map((item: { id: string }) => item.id);
};
