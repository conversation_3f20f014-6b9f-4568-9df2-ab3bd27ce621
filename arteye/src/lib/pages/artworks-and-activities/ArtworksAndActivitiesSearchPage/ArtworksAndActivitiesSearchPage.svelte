<script lang="ts">
	import { ArtworksAndActivitiesSearch } from './ArtworksAndActivitiesSearchForm';
	import type { AaAdvancedSearchFormFields } from './ArtworksAndActivitiesSearchForm/AdvancedSearch/AdvancedSearch.svelte';
	import type { AaBasicSearchFormFields } from './ArtworksAndActivitiesSearchForm/BasicSearch/BasicSearch.svelte';
	import type { MultipleIdsSearchFormFields } from './ArtworksAndActivitiesSearchForm/MultipleIds/MultipleIds.svelte';
	import { ArtworksResults } from './ArtworksResults';
	import {
		MultipleIdsSearchParam,
		ActivityAdvancedSearchParam,
		ArtActAdvancedSearchParam,
		ArtActBasicSearchParam,
		ArtworkAdvancedSearchParam,
		DataEntryAdvancedSearchParam,
		ImageUploadSearchParam,
		OmittedItemsSearchParam,
	} from './constants/search';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Artwork_Activity_Type_Enum } from '$gql/types-custom';
	import { STRING_LIST_SEPARATOR } from '$lib/constants/params';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { ArtworksAndActivitiesSearchPageData } from '$routes/artworks-and-activities/types';

	let data = $derived(
		getPageData<ArtworksAndActivitiesSearchPageData>(page.data)
	);
	let showResults = $derived(data.showResults);
	let dataSources = $derived(data.user?.dropdowns.dataSources || []);

	let artworkTypes = $derived(data.user?.dropdowns.artworkTypes || []);
	let heniArtworkTypes = $derived(data.user?.dropdowns.heniArtworkTypes || []);
	let artworkActivityTypes = $derived(
		data.user?.dropdowns.artworkActivityTypes || []
	);
	let artworkActivityStatusTypes = $derived(
		data.user?.dropdowns.artworkActivityStatusTypes || []
	);
	let artworkActivityAssociationTypes = $derived(
		data.user?.dropdowns?.artworkActivityAssociationTypes || []
	);
	let listingTypes = $derived(data.user?.dropdowns.artworkListingTypes || []);
	let auctionTypes = $derived(data.user?.dropdowns.auctionTypes || []);
	let sources = $derived(data.sources);
	let categoryTags = $derived(data.user?.dropdowns.categoryTags || []);
	let subjectTags = $derived(data.user?.dropdowns.subjectTags || []);
	let searchPresets = $derived(data.searchPresets);

	let dataSourceOptions: MultiSelectOption[] = $state([]);
	let artworkTypeOptions: MultiSelectOption[] = $state([]);
	let activityTypeOptions: MultiSelectOption[] = $state([]);
	let activityStatusTypeOptions: MultiSelectOption[] = $state([]);
	let associationTypeOptions: MultiSelectOption[] = $state([]);
	let listingTypeOptions: MultiSelectOption[] = $state([]);
	let auctionTypeOptions: MultiSelectOption[] = $state([]);
	let categoryTagsOptions: MultiSelectOption[] = $state([]);
	let subjectTagsOptions: MultiSelectOption[] = $state([]);
	let heniArtworkTypeOptions: MultiSelectOption[] = $state([]);
	let searchPresetsOptions: MultiSelectOption[] = $state([]);

	$effect(() => {
		artworkTypeOptions = artworkTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	$effect(() => {
		heniArtworkTypeOptions = heniArtworkTypes.map<MultiSelectOption>(
			(type) => ({
				label: type?.name || '',
				value: type?.key || '',
			})
		);
	});

	$effect(() => {
		dataSourceOptions = dataSources
			.filter(
				(source, i) =>
					dataSources.findIndex(
						(ssource) =>
							source?.name?.toLowerCase() === ssource?.name?.toLowerCase()
					) === i
			)
			.map<MultiSelectOption>((source) => ({
				label: source?.name,
				value: source?.name,
			}));
	});

	$effect(() => {
		const types = artworkActivityTypes.filter(
			(type) => type?.key !== Artwork_Activity_Type_Enum.PrivateSale
		);
		const privateSaleType = artworkActivityTypes.find(
			(type) => type?.key === Artwork_Activity_Type_Enum.PrivateSale
		);

		activityTypeOptions = [types[0], privateSaleType, ...types.slice(1)].map(
			(type) => ({
				label: type?.name ?? '',
				value: type?.key ?? '',
			})
		);
	});

	$effect(() => {
		activityStatusTypeOptions =
			artworkActivityStatusTypes.map<MultiSelectOption>((type) => ({
				label: type?.name || '',
				value: type?.key || '',
			}));
	});

	$effect(() => {
		associationTypeOptions = (() => {
			const options = artworkActivityAssociationTypes
				.filter((type) => type.key)
				.map<MultiSelectOption>((type) => ({
					label: type?.name || '',
					value: type?.key || '',
				}));

			return [{ value: '', label: '- None -' }, ...options];
		})();
	});

	$effect(() => {
		listingTypeOptions = listingTypes
			.filter((type) => type.key !== 'AUCTION')
			.map<MultiSelectOption>((type) => ({
				label: type?.name || '',
				value: type?.key || '',
			}));
	});

	$effect(() => {
		auctionTypeOptions = auctionTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	$effect(() => {
		categoryTagsOptions = categoryTags.map<MultiSelectOption>((tag) => ({
			label: tag?.tag || '',
			value: tag?.tag || '',
		}));
	});

	$effect(() => {
		subjectTagsOptions = subjectTags.map<MultiSelectOption>((tag) => ({
			label: tag?.tag || '',
			value: tag?.tag || '',
		}));
	});

	$effect(() => {
		searchPresetsOptions = searchPresets.map<MultiSelectOption>((tag) => ({
			label: tag?.title || '',
			value: tag?.url || '',
		}));
	});

	const formatParamStringBasic = (
		values: AaBasicSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			sort,
			name,
			title,
			crid,
			media,
			executedYear,
			executedYearRange,
			artworkType,
			activityId,
			activityType,
			activityStatus,
			height,
			heightRange,
			width,
			widthRange,
			editionSize,
			editionSizeRange,
			activityDate,
			activityDateRange,
			saleAmount,
			saleAmountRange,
			lotNumber,
			saleName,
			saleNumber,
			sortResultsBy,
			association,
			associationType,
			omittedArtworkIds,
			omittedActivityIds,
			hideItemsWithoutImages,
			showFavouriteArtists,
			resultsTab,
		} = values;

		const params = {
			[ArtActBasicSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[ArtActBasicSearchParam.ShowResults]: showResults,
			[ArtActBasicSearchParam.Name]: name,
			[ArtActBasicSearchParam.ResultsTab]: resultsTab,
			[ArtActBasicSearchParam.Title]: title,
			[ArtActBasicSearchParam.Crid]: crid,
			[ArtActBasicSearchParam.Media]: media,
			[ArtActBasicSearchParam.ExecutedYear]: executedYear,
			[ArtActBasicSearchParam.ExecutedYearRange]: executedYear
				? executedYearRange
				: '',
			[ArtActBasicSearchParam.ArtworkType]:
				getSearchParamFromStringArray(artworkType),
			[ArtActBasicSearchParam.ActivityId]: activityId,
			[ArtActBasicSearchParam.ActivityType]:
				getSearchParamFromStringArray(activityType),
			[ArtActBasicSearchParam.ActivityStatus]:
				getSearchParamFromStringArray(activityStatus),
			[ArtActBasicSearchParam.Height]: height,
			[ArtActBasicSearchParam.HeightRange]: height ? heightRange : '',
			[ArtActBasicSearchParam.Width]: width,
			[ArtActBasicSearchParam.WidthRange]: width ? widthRange : '',
			[ArtActBasicSearchParam.EditionSize]: editionSize,
			[ArtActBasicSearchParam.EditionSizeRange]: editionSize
				? editionSizeRange
				: '',
			[ArtActBasicSearchParam.ActivityDate]: activityDate,
			[ArtActBasicSearchParam.ActivityDateRange]: activityDate
				? activityDateRange
				: '',
			[ArtActBasicSearchParam.SaleAmount]: saleAmount,
			[ArtActBasicSearchParam.SaleAmountRange]: saleAmount
				? saleAmountRange
				: '',
			[ArtActBasicSearchParam.LotNumber]: lotNumber,
			[ArtActBasicSearchParam.SaleName]: saleName,
			[ArtActBasicSearchParam.SaleNumber]: saleNumber,
			[ArtActBasicSearchParam.SortResultsBy]: sortResultsBy,
			[ArtActBasicSearchParam.Association]: association,
			[ArtActBasicSearchParam.AssociationType]: association
				? associationType
				: '',
			[ArtActBasicSearchParam.OmittedArtworkIds]: getSearchParamFromStringArray(
				omittedArtworkIds.split(STRING_LIST_SEPARATOR).map((id) => id.trim())
			),
			[ArtActBasicSearchParam.OmittedActivityIds]:
				getSearchParamFromStringArray(
					omittedActivityIds.split(STRING_LIST_SEPARATOR).map((id) => id.trim())
				),
			[ArtActBasicSearchParam.HideItemsWithoutImages]: hideItemsWithoutImages
				? StringBoolean.True
				: '',
			[ArtActBasicSearchParam.ShowFavouriteArtists]: showFavouriteArtists
				? StringBoolean.True
				: '',
			searchType: 'basic',
		} as Record<ArtActBasicSearchParam, string>;

		return `${getSearchParamString(params)}${includePageParams ? getESPageParams() : ''}`;
	};

	const getESPageParams = () => {
		const windowQueryParams = new URLSearchParams(page.url.href.split('?')[1]);

		const params = {
			page: windowQueryParams.get('page') || undefined,
			paginationDirection:
				windowQueryParams.get('paginationDirection') || undefined,
			paginationToken: windowQueryParams.get('paginationToken') || undefined,
			pageSize: windowQueryParams.get('pageSize') || undefined,
		};

		const filteredParams = Object.keys(params).reduce(
			(accumulator: Record<string, string>, key: string) => {
				if (params[key as keyof typeof params]) {
					return {
						...accumulator,
						[key]: params[key as keyof typeof params] as string,
					};
				}

				return accumulator;
			},
			{} as Record<string, string>
		);

		const filteredParamsString = new URLSearchParams(filteredParams).toString();
		return filteredParamsString.length ? `&${filteredParamsString}` : '';
	};

	const formatParamStringAdvanced = (
		values: AaAdvancedSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			name,
			sort,
			title,
			crid,
			media,
			executedYear,
			executedYearRange,
			artworkType,
			height,
			heightRange,
			width,
			widthRange,
			depth,
			depthRange,
			numberOfPieces,
			numberOfArtworks,
			isHeniArtwork,
			isBundle,
			isFullSet,
			editionSize,
			editionSizeRange,
			editionNumber,
			seriesTitle,
			seriesCrid,
			seriesSize,
			seriesSizeRange,
			isUnlimitedEdition,
			excludeMultiples,
			categoryTags,
			subjectTags,
			activityType,
			listingType,
			activityStatus,
			activityDate,
			activityDateRange,
			listingPrice,
			listingPriceRange,
			saleAmount,
			saleAmountRange,
			association,
			associationType,
			auctionHouse,
			auctionNameOrId,
			auctionSaleName,
			saleNumber,
			lotNumber,
			auctionType,
			fairName,
			fairDate,
			fairDateRange,
			fairExhibitor,
			galleryName,
			galleryCity,
			galleryCountry,
			exhibitionName,
			exhibitionDate,
			exhibitionDateRange,
			exhibitionCity,
			exhibitionCountry,
			createdBy,
			createdDate,
			createdDateRange,
			updatedBy,
			updatedByDate,
			updatedByDateRange,
			source,
			omittedArtworkIds,
			omittedActivityIds,
			hideItemsWithoutImages,
			showFavouriteArtists,
			resultsTab,
			heniArtworkType,
		} = values;

		const params = {
			[ArtActAdvancedSearchParam.ShowResults]: showResults,
			[ArtworkAdvancedSearchParam.Name]: name,
			[ArtworkAdvancedSearchParam.ResultsTab]: resultsTab,
			[ArtworkAdvancedSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[ArtworkAdvancedSearchParam.Title]: title,
			[ArtworkAdvancedSearchParam.Crid]: crid,
			[ArtworkAdvancedSearchParam.Media]: media,
			[ArtworkAdvancedSearchParam.ExecutedYear]: executedYear,
			[ArtworkAdvancedSearchParam.ExecutedYearRange]: executedYear
				? executedYearRange
				: '',
			[ArtworkAdvancedSearchParam.HeniArtworkType]:
				getSearchParamFromStringArray(heniArtworkType),
			[ArtworkAdvancedSearchParam.ArtworkType]:
				getSearchParamFromStringArray(artworkType),
			[ArtworkAdvancedSearchParam.Height]: height,
			[ArtworkAdvancedSearchParam.HeightRange]: height ? heightRange : '',
			[ArtworkAdvancedSearchParam.Width]: width,
			[ArtworkAdvancedSearchParam.WidthRange]: width ? widthRange : '',
			[ArtworkAdvancedSearchParam.Depth]: depth,
			[ArtworkAdvancedSearchParam.DepthRange]: depth ? depthRange : '',
			[ArtworkAdvancedSearchParam.NumberOfPieces]: numberOfPieces,
			[ArtworkAdvancedSearchParam.NumberOfArtworks]: numberOfArtworks,
			[ArtworkAdvancedSearchParam.IsHeniArtwork]: isHeniArtwork ? 'true' : '',
			[ArtworkAdvancedSearchParam.IsBundle]: isBundle ? 'true' : '',
			[ArtworkAdvancedSearchParam.IsFullSet]: isFullSet ? 'true' : '',
			[ArtworkAdvancedSearchParam.EditionSize]: editionSize,
			[ArtworkAdvancedSearchParam.EditionSizeRange]: editionSize
				? editionSizeRange
				: '',
			[ArtworkAdvancedSearchParam.EditionNumber]: editionNumber,
			[ArtworkAdvancedSearchParam.SeriesTitle]: seriesTitle,
			[ArtworkAdvancedSearchParam.SeriesCrid]: seriesCrid,
			[ArtworkAdvancedSearchParam.SeriesSize]: seriesSize,
			[ArtworkAdvancedSearchParam.SeriesSizeRange]: seriesSize
				? seriesSizeRange
				: '',
			[ArtworkAdvancedSearchParam.IsUnlimitedEdition]: isUnlimitedEdition
				? StringBoolean.True
				: '',
			[ArtworkAdvancedSearchParam.ExcludeMultiples]: excludeMultiples
				? StringBoolean.True
				: '',
			[ArtworkAdvancedSearchParam.CategoryTags]:
				getSearchParamFromStringArray(categoryTags),
			[ArtworkAdvancedSearchParam.SubjectTags]:
				getSearchParamFromStringArray(subjectTags),

			[ActivityAdvancedSearchParam.ActivityType]:
				getSearchParamFromStringArray(activityType),
			[ActivityAdvancedSearchParam.ListingType]:
				getSearchParamFromStringArray(listingType),
			[ActivityAdvancedSearchParam.ActivityStatus]:
				getSearchParamFromStringArray(activityStatus),
			[ActivityAdvancedSearchParam.ActivityDate]: activityDate,
			[ActivityAdvancedSearchParam.ActivityDateRange]: activityDate
				? activityDateRange
				: '',
			[ActivityAdvancedSearchParam.ListingPrice]: listingPrice,
			[ActivityAdvancedSearchParam.ListingPriceRange]: listingPrice
				? listingPriceRange
				: '',
			[ActivityAdvancedSearchParam.SaleAmount]: saleAmount,
			[ActivityAdvancedSearchParam.SaleAmountRange]: saleAmount
				? saleAmountRange
				: '',
			[ActivityAdvancedSearchParam.Association]: association,
			[ActivityAdvancedSearchParam.AssociationType]: association
				? associationType
				: '',
			[ActivityAdvancedSearchParam.AuctionHouse]: auctionHouse,
			[ActivityAdvancedSearchParam.AuctionName]: auctionNameOrId,
			[ActivityAdvancedSearchParam.AuctionSaleName]: auctionSaleName,
			[ActivityAdvancedSearchParam.SaleNumber]: saleNumber,
			[ActivityAdvancedSearchParam.LotNumber]: lotNumber,
			[ActivityAdvancedSearchParam.AuctionType]:
				getSearchParamFromStringArray(auctionType),
			[ActivityAdvancedSearchParam.FairName]: fairName,
			[ActivityAdvancedSearchParam.FairDate]: fairDate,
			[ActivityAdvancedSearchParam.FairDateRange]: fairDate
				? fairDateRange
				: '',
			[ActivityAdvancedSearchParam.FairExhibitor]: fairExhibitor,
			[ActivityAdvancedSearchParam.GalleryName]: galleryName,
			[ActivityAdvancedSearchParam.GalleryCity]: galleryCity,
			[ActivityAdvancedSearchParam.GalleryCountry]: galleryCountry,
			[ActivityAdvancedSearchParam.ExhibitionName]: exhibitionName,
			[ActivityAdvancedSearchParam.ExhibitionDate]: exhibitionDate,
			[ActivityAdvancedSearchParam.ExhibitionDateRange]: exhibitionDate
				? exhibitionDateRange
				: '',
			[ActivityAdvancedSearchParam.ExhibitionCity]: exhibitionCity,
			[ActivityAdvancedSearchParam.ExhibitionCountry]: exhibitionCountry,

			[DataEntryAdvancedSearchParam.CreatedBy]: createdBy,
			[DataEntryAdvancedSearchParam.CreatedDate]: createdDate,
			[DataEntryAdvancedSearchParam.CreatedDateRange]: createdDate
				? createdDateRange
				: '',
			[DataEntryAdvancedSearchParam.UpdatedBy]: updatedBy,
			[DataEntryAdvancedSearchParam.UpdatedByDate]: updatedByDate,
			[DataEntryAdvancedSearchParam.UpdatedByDateRange]: updatedByDate
				? updatedByDateRange
				: '',
			[DataEntryAdvancedSearchParam.Source]:
				getSearchParamFromStringArray(source),
			[OmittedItemsSearchParam.OmittedArtworkIds]:
				getSearchParamFromStringArray(
					omittedArtworkIds.split(STRING_LIST_SEPARATOR).map((id) => id.trim())
				),
			[OmittedItemsSearchParam.OmittedActivityIds]:
				getSearchParamFromStringArray(
					omittedActivityIds.split(STRING_LIST_SEPARATOR).map((id) => id.trim())
				),
			[OmittedItemsSearchParam.HideItemsWithoutImages]: hideItemsWithoutImages
				? StringBoolean.True
				: '',
			[ArtworkAdvancedSearchParam.ShowFavouriteArtists]: showFavouriteArtists
				? StringBoolean.True
				: '',
			searchType: 'advanced',
		} as Record<
			| ArtworkAdvancedSearchParam
			| ActivityAdvancedSearchParam
			| ArtActAdvancedSearchParam
			| DataEntryAdvancedSearchParam
			| OmittedItemsSearchParam,
			string
		>;

		return `${getSearchParamString(params)}${includePageParams ? getESPageParams() : ''}`;
	};

	const formatParamStringMultiple = (
		values: MultipleIdsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const { activityIds, artworkIds, type } = values;

		const params = {
			[MultipleIdsSearchParam.Type]: type || '',
			[MultipleIdsSearchParam.ShowResults]: showResults,
			[MultipleIdsSearchParam.ActivityIds]: activityIds || '',
			[MultipleIdsSearchParam.ArtworkIds]: artworkIds || '',
			searchType: 'multipleIds',
		} as Record<MultipleIdsSearchParam, string>;

		return `${getSearchParamString(params)}${includePageParams ? getESPageParams() : ''}`;
	};

	const handleSubmitImage = async (values: { image: string }) => {
		const { image } = values;

		const params: Record<ImageUploadSearchParam, string> = {
			[ImageUploadSearchParam.ShowResults]: StringBoolean.True,
			[ImageUploadSearchParam.Image]: image,
		};

		const searchParamString = getSearchParamString(params);
		let searchType = page.url.searchParams.get('searchType');

		goto(
			searchType
				? `?${searchParamString}&searchType=${encodeURIComponent(searchType)}`
				: `?${searchParamString}`
		);
	};
</script>

<div class="mb-6">
	<ArtworksAndActivitiesSearch
		{formatParamStringBasic}
		{formatParamStringAdvanced}
		{formatParamStringMultiple}
		onSubmitImage={handleSubmitImage}
		{dataSourceOptions}
		{artworkTypeOptions}
		{activityTypeOptions}
		{activityStatusTypeOptions}
		{associationTypeOptions}
		{listingTypeOptions}
		{auctionTypeOptions}
		{categoryTagsOptions}
		{subjectTagsOptions}
		{searchPresetsOptions}
		{heniArtworkTypeOptions}
	/>
</div>

{#if showResults}
	{#key page.url.searchParams.toString()}
		<ArtworksResults />
	{/key}
{/if}
