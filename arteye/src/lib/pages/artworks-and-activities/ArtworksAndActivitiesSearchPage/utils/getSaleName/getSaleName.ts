import type { Artwork_Activity } from '$gql/types';
import { ArtowrkActivityTypes } from '$lib/pages/artworks-and-activities/ArtworkDetailsPage/NewActivityDialog/DialogContent';
import { ArtworkListingKey } from '$lib/types/types';

export const getSaleName = (artworkActivity: Artwork_Activity | null) => {
	if (artworkActivity?.type?.key === ArtowrkActivityTypes.AUCTION) {
		return artworkActivity?.artwork_listing?.find(
			(listing) =>
				(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
				ArtworkListingKey.Auction
		)?.auction_lot?.auction?.sale_name;
	} else if (artworkActivity?.type?.key === ArtowrkActivityTypes.PRIVATE_SALE) {
		switch (artworkActivity?.artwork_listing?.[0]?.listing_type?.key) {
			case ArtworkListingKey.Auction:
				return artworkActivity?.artwork_listing?.find(
					(listing) =>
						(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
						ArtworkListingKey.Auction
				)?.auction_lot?.auction?.sale_name;
			case ArtworkListingKey.Exhibition:
				return artworkActivity?.artwork_listing?.find(
					(listing) =>
						(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
						ArtworkListingKey.Exhibition
				)?.exhibition_listing?.exhibition?.title;
			case ArtworkListingKey.Fair:
				return artworkActivity?.artwork_listing?.find(
					(listing) =>
						(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
						ArtworkListingKey.Fair
				)?.fair_listing?.fair_exhibitor?.fair?.title;
			case ArtworkListingKey.Gallery:
				return 'Gallery Offer';
			case ArtworkListingKey.Private:
				return null;
			default:
				return null;
		}
	} else {
		return null;
	}
};
