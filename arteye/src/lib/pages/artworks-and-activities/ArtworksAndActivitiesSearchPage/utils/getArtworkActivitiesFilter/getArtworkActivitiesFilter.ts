import {
	ActivityAdvancedSearchParam,
	ArtActBasicSearchParam,
	ArtworkAdvancedSearchParam,
	DataEntryAdvancedSearchParam,
} from '../../constants/search';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	Artwork_Activity_Artwork_Filter,
	Artwork_Activity_Filter,
	Artwork_Activity_Status_Filter,
	Artwork_Activity_Type_Filter,
	Artwork_Listing_Filter,
} from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import type { SearchRange } from '$lib/constants/search-range-options';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSearchRangeFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';
import { getSearchRangeFilterFullDate } from '$lib/utils/getSearchRangeFilterFullDate/getSearchRangeFilterFullDate';
import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';

export const getArtworkActivitiesFilter = (
	searchParams: URLSearchParams
): Artwork_Activity_Filter => {
	const _and: Artwork_Activity_Filter[] = [
		{ status: { key: { _neq: Status_Enum.Archived } } },
	];

	// basic search
	const saleName = getDecodedSearchParam({
		searchParams,
		key: ArtActBasicSearchParam.SaleName,
	});

	const name = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Name,
	});

	const title = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Title,
	});

	const crid = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Crid,
	});

	const media = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Media,
	});

	const yearExecuted = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExecutedYear,
	});

	const yearExecutedRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExecutedYearRange,
	});

	const artworkTypes = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ArtworkType,
	});

	const height = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Height,
	});

	const heightRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.HeightRange,
	});

	const width = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Width,
	});

	const widthRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.WidthRange,
	});

	const depth = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Depth,
	});

	const depthRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.DepthRange,
	});

	const numberOfPieces = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.NumberOfPieces,
	});

	const numberOfArtworks = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.NumberOfArtworks,
	});

	const isHeniArtwork = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsHeniArtwork,
	});

	const isBundle = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsBundle,
	});

	const isFullSet = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsFullSet,
	});

	const editionSize = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionSize,
	});

	const editionSizeRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionSizeRange,
	});

	const editionNumber = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionNumber,
	});

	const seriesTitle = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesTitle,
	});

	const seriesCrid = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesCrid,
	});

	const seriesSize = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesSize,
	});

	const seriesSizeRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesSizeRange,
	});

	const isUnlimitedEdition = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsUnlimitedEdition,
	});

	const excludeMultiples = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExcludeMultiples,
	});

	const categoryTags = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.CategoryTags,
	});

	const subjectTags = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SubjectTags,
	});

	// activity information
	const activityTypes = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityType,
	});

	const listingType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingType,
	});

	const activityStatus = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityStatus,
	});

	const activityDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityDate,
	});

	const activityDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityDateRange,
	});

	const listingPrice = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingPrice,
	});

	const listingPriceRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingPriceRange,
	});

	const saleAmount = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleAmount,
	});

	const saleAmountRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleAmountRange,
	});

	const association = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.Association,
	});

	const associationType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AssociationType,
	});

	const auctionHouse = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionHouse,
	});

	const auctionSaleName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionSaleName,
	});

	const saleNumber = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleNumber,
	});

	const lotNumber = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.LotNumber,
	});

	const auctionType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionType,
	});

	const fairName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairName,
	});

	const fairDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairDate,
	});

	const fairDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairDateRange,
	});

	const galleryName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryName,
	});

	const galleryCity = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryCity,
	});

	const galleryCountry = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryCountry,
	});

	const exhibitionName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionName,
	});

	const exhibitionDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionDate,
	});

	const exhibitionDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionDateRange,
	});

	const exhibitionCity = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionCity,
	});

	const exhibitionCountry = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionCountry,
	});

	// data entry
	const createdBy = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedBy,
	});

	const createdDate = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedDate,
	});

	const createdDateRange = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedDateRange,
	});

	const updatedBy = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedBy,
	});

	const updatedByDate = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedByDate,
	});

	const updatedByDateRange = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedByDateRange,
	});

	const source = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.Source,
	});

	if (name) {
		const isId = isValidUUID(name);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artworks: {
					artwork: {
						artists: {
							artist_id: {
								id: { _eq: name },
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artworks: {
					artwork: {
						artists: {
							artist_id: {
								person: {
									entity: {
										name: {
											_icontains: name,
										},
									},
								},
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (title) {
		const isId = isValidUUID(title);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artworks: {
					artwork: {
						id: { _eq: title },
					},
				},
			};
		} else {
			filterItem = {
				artworks: {
					artwork: {
						title: { _icontains: title },
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (crid) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					crid: {
						_icontains: crid,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (media) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					media: {
						_icontains: media,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (yearExecuted) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					execution_start_year: getSearchRangeFilter({
						value: yearExecuted,
						range: yearExecutedRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (artworkTypes) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				_or: artworkTypes
					.split(PARAM_SEPARATOR)
					.map<Artwork_Activity_Artwork_Filter>((type) => ({
						artwork: {
							artwork_type: {
								name: {
									_icontains: type,
								},
							},
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (height) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					dimensions_height_cm: getSearchRangeFilter({
						value: height,
						range: heightRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (width) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					dimensions_width_cm: getSearchRangeFilter({
						value: width,
						range: widthRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (depth) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					dimensions_depth_cm: getSearchRangeFilter({
						value: depth,
						range: depthRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (numberOfPieces) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					number_of_pieces: {
						_eq: numberOfPieces,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (numberOfArtworks) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					number_of_artworks: {
						_eq: numberOfArtworks,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (isHeniArtwork === StringBoolean.True) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					heni_artwork_type: {
						name: {
							_nnull: isHeniArtwork === StringBoolean.True,
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (isBundle === StringBoolean.True) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					is_bundle: {
						_eq: isBundle === StringBoolean.True,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (isFullSet === StringBoolean.True) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					is_full_set: {
						_eq: isFullSet === StringBoolean.True,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (editionSize) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					edition_info: {
						edition_size_total: getSearchRangeFilter({
							value: editionSize,
							range: editionSizeRange as SearchRange,
						}),
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (editionNumber) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				edition_number: {
					_eq: editionNumber,
				},
			},
		};

		_and.push(filterItem);
	}

	if (seriesTitle) {
		const isId = isValidUUID(seriesTitle);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artworks: {
					artwork: {
						series: {
							id: { _eq: seriesTitle },
						},
					},
				},
			};
		} else {
			filterItem = {
				artworks: {
					artwork: {
						series: {
							title: { _icontains: seriesTitle },
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (seriesCrid) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					series: {
						crid: {
							_eq: seriesCrid,
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (seriesSize) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					series: {
						number_of_artworks: {
							_eq: getSearchRangeFilter({
								value: seriesSize,
								range: seriesSizeRange as SearchRange,
							}),
						},
					},
				},
			},
			// TODO: Check with Sanket if this is the correct filter
			// artworks: {
			// 	artwork: {
			// 		series: {
			// 			artists_func: {
			// 				count: {
			// 					_eq: getSearchRangeFilter({
			// 						value: seriesSize,
			// 						range: seriesSize as SearchRange,
			// 					}),
			// 				},
			// 			},
			// 		},
			// 	},
			// },
		};

		_and.push(filterItem);
	}

	if (isUnlimitedEdition === StringBoolean.True) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					edition_info: {
						is_unlimited: {
							_eq: isUnlimitedEdition === StringBoolean.True,
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (excludeMultiples === StringBoolean.True) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					edition_info: {
						edition_size_total: {
							_gte: 10,
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (categoryTags || subjectTags) {
		const combinedTags = [categoryTags, subjectTags]
			.filter(Boolean)
			.join(PARAM_SEPARATOR);
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				_or: combinedTags
					.split(PARAM_SEPARATOR)
					.map<Artwork_Activity_Artwork_Filter>((type) => ({
						artwork: {
							tags: {
								tag_tag: {
									tag: {
										_icontains: type,
									},
								},
							},
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (activityTypes) {
		const filterItem: Artwork_Activity_Filter = {
			type: {
				_or: activityTypes
					.split(PARAM_SEPARATOR)
					.map<Artwork_Activity_Type_Filter>((type) => ({
						key: {
							_eq: type,
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (listingType) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				_or: listingType
					.split(PARAM_SEPARATOR)
					.map<Artwork_Listing_Filter>((type) => ({
						listing_type: {
							key: {
								_eq: type,
							},
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (activityStatus) {
		const filterItem: Artwork_Activity_Filter = {
			activity_status: {
				_or: activityStatus
					.split(PARAM_SEPARATOR)
					.map<Artwork_Activity_Status_Filter>((status) => ({
						type: {
							name: {
								_eq: status,
							},
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (activityDate) {
		const filterItem: Artwork_Activity_Filter = {
			timestamp: getSearchRangeFilterFullDate({
				value: activityDate,
				range: activityDateRange as SearchRange,
			}),
		};

		_and.push(filterItem);
	}

	if (listingPrice) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				known_price: {
					amount: getSearchRangeFilter({
						value: listingPrice,
						range: listingPriceRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (saleAmount) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				sale_amount: {
					usd_amount: getSearchRangeFilter({
						value: saleAmount,
						range: saleAmountRange as SearchRange,
					}),
				},
			},
		};

		_and.push(filterItem);
	}

	if (association) {
		const isId = isValidUUID(association);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				associations: {
					entity: {
						id: {
							_eq: association,
						},
					},
				},
			};
		} else {
			filterItem = {
				_and: [
					{
						associations: {
							type: {
								name: {
									_eq: associationType,
								},
							},
						},
					},
					{
						associations: {
							entity: {
								name: {
									_icontains: association,
								},
							},
						},
					},
				],
			};
		}

		_and.push(filterItem);
	}

	if (auctionHouse) {
		const isId = isValidUUID(auctionHouse);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artwork_listing: {
					auction_lot: {
						auction: {
							auction_house: {
								id: {
									_eq: auctionHouse,
								},
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artwork_listing: {
					auction_lot: {
						auction: {
							auction_house: {
								organisation: {
									name: {
										_icontains: auctionHouse,
									},
								},
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (auctionSaleName) {
		const isId = isValidUUID(auctionSaleName);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artwork_listing: {
					auction_lot: {
						auction: {
							id: {
								_eq: auctionSaleName,
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artwork_listing: {
					auction_lot: {
						auction: {
							sale_name: {
								_icontains: auctionSaleName,
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (saleNumber) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				auction_lot: {
					auction: {
						sale_number: {
							_eq: saleNumber,
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (lotNumber) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				auction_lot: {
					lot_number: {
						_eq: lotNumber,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (saleName) {
		const filterItem: Artwork_Activity_Filter = {
			_or: [
				{
					artwork_listing: {
						auction_lot: {
							auction: {
								sale_name: {
									_icontains: saleName,
								},
							},
						},
					},
				},
				{
					artwork_listing: {
						exhibition_listing: {
							exhibition: {
								title: {
									_icontains: saleName,
								},
							},
						},
					},
				},
				{
					artwork_listing: {
						gallery_listing: {
							gallery: {
								organisation: {
									name: {
										_icontains: saleName,
									},
								},
							},
						},
					},
				},
				{
					artwork_listing: {
						fair_listing: {
							fair_exhibitor: {
								fair: {
									title: {
										_icontains: saleName,
									},
								},
							},
						},
					},
				},
			],
		};

		_and.push(filterItem);
	}

	if (auctionType) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				_or: auctionType
					.split(PARAM_SEPARATOR)
					.map<Artwork_Listing_Filter>((type) => ({
						auction_lot: {
							auction: {
								auction_types: {
									auction_type_key: {
										name: {
											_icontains: type,
										},
									},
								},
							},
						},
					})),
			},
		};

		_and.push(filterItem);
	}

	if (fairName) {
		const isId = isValidUUID(name);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artwork_listing: {
					fair_listing: {
						fair_exhibitor: {
							fair: {
								id: {
									_eq: fairName,
								},
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artwork_listing: {
					fair_listing: {
						fair_exhibitor: {
							fair: {
								title: {
									_icontains: fairName,
								},
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (fairDate) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				fair_listing: {
					fair_exhibitor: {
						fair: {
							start_date: getSearchRangeFilterFullDate({
								value: fairDate,
								range: fairDateRange as SearchRange,
							}),
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (galleryName) {
		const isId = isValidUUID(name);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artwork_listing: {
					gallery_listing: {
						gallery: {
							id: {
								_eq: galleryName,
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artwork_listing: {
					gallery_listing: {
						gallery: {
							organisation: {
								name: {
									_icontains: galleryName,
								},
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (galleryCity) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				gallery_listing: {
					gallery: {
						organisation: {
							location: {
								name: {
									_icontains: galleryCity,
								},
							},
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (galleryCountry) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				gallery_listing: {
					gallery: {
						organisation: {
							location: {
								country: {
									name: {
										_icontains: galleryCountry,
									},
								},
							},
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (exhibitionName) {
		const isId = isValidUUID(name);
		let filterItem: Artwork_Activity_Filter;

		if (isId) {
			filterItem = {
				artwork_listing: {
					exhibition_listing: {
						exhibition: {
							id: {
								_eq: exhibitionName,
							},
						},
					},
				},
			};
		} else {
			filterItem = {
				artwork_listing: {
					exhibition_listing: {
						exhibition: {
							title: {
								_icontains: exhibitionName,
							},
						},
					},
				},
			};
		}

		_and.push(filterItem);
	}

	if (exhibitionDate) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				exhibition_listing: {
					exhibition: {
						start_date: getSearchRangeFilterFullDate({
							value: exhibitionDate,
							range: exhibitionDateRange as SearchRange,
						}),
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (exhibitionCity) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				exhibition_listing: {
					exhibition: {
						venue_city: {
							name: {
								_icontains: exhibitionCity,
							},
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (exhibitionCountry) {
		const filterItem: Artwork_Activity_Filter = {
			artwork_listing: {
				exhibition_listing: {
					exhibition: {
						venue_country: {
							name: {
								_icontains: exhibitionCountry,
							},
						},
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (createdBy) {
		const filterItem: Artwork_Activity_Filter = {
			_or: [
				{
					user_created: {
						first_name: {
							_icontains: createdBy,
						},
					},
				},
				{
					user_created: {
						last_name: {
							_icontains: createdBy,
						},
					},
				},
				{
					user_created: {
						email: {
							_icontains: createdBy,
						},
					},
				},
			],
		};

		_and.push(filterItem);
	}

	if (createdDate) {
		const filterItem: Artwork_Activity_Filter = {
			date_created: getSearchRangeFilterFullDate({
				value: createdDate,
				range: createdDateRange as SearchRange,
			}),
		};

		_and.push(filterItem);
	}

	if (updatedBy) {
		const filterItem: Artwork_Activity_Filter = {
			_or: [
				{
					user_updated: {
						first_name: {
							_icontains: updatedBy,
						},
					},
				},
				{
					user_updated: {
						last_name: {
							_icontains: updatedBy,
						},
					},
				},
				{
					user_updated: {
						email: {
							_icontains: updatedBy,
						},
					},
				},
			],
		};

		_and.push(filterItem);
	}

	if (updatedByDate) {
		const filterItem: Artwork_Activity_Filter = {
			date_updated: getSearchRangeFilterFullDate({
				value: updatedByDate,
				range: updatedByDateRange as SearchRange,
			}),
		};

		_and.push(filterItem);
	}

	return {
		_and,
	};
};
