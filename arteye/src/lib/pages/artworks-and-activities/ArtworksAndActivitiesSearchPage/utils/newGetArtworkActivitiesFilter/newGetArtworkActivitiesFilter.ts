import {
	ActivityAdvancedSearchParam,
	ArtActBasicSearchParam,
	ArtworkAdvancedSearchParam,
	DataEntryAdvancedSearchParam,
	OmittedItemsSearchParam,
} from '../../constants/search';
import { removeThousandSeparator } from '$global/components/Input/utils/removeThousandSeparator/removeThousandSeparator';
import { StringBoolean } from '$global/constants/string-boolean';
import { ValueFilterOperator } from '$gql/types-custom';
import type {
	ActivitySearchInput,
	Artwork_Type_Enum,
	FloatValueFilter,
	IntValueFilter,
	Artwork_Activity_Type_Enum,
	Artwork_Listing_Type_Enum,
	Artwork_Activity_Status_Type_Enum,
	Artwork_Activity_Association_Type_Enum,
	StringValueFilter,
	Auction_Type_Enum,
	Heni_Artwork_Type_Enum,
} from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';

export const newGetArtworkActivitiesFilter = (
	searchParams: URLSearchParams
): {
	commonFilters: Partial<ActivitySearchInput['commonFilters']>;
	artworkFilters: Partial<ActivitySearchInput['artworkFilters']>;
	activityFilters: Partial<ActivitySearchInput['activityFilters']>;
} => {
	let commonFilters: Partial<ActivitySearchInput['commonFilters']> = {};
	let artworkFilters: Partial<ActivitySearchInput['artworkFilters']> = {};
	let activityFilters: Partial<ActivitySearchInput['activityFilters']> = {};

	// basic search
	const saleName = getDecodedSearchParam({
		searchParams,
		key: ArtActBasicSearchParam.SaleName,
	});

	const name = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Name,
	});

	const title = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Title,
	});

	const crid = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Crid,
	});

	const media = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Media,
	});

	const yearExecuted = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExecutedYear,
	});

	const yearExecutedRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExecutedYearRange,
	});

	const artworkTypes = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ArtworkType,
	});

	const heniArtworkTypes = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.HeniArtworkType,
	});

	const height = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Height,
	});

	const heightRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.HeightRange,
	});

	const width = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Width,
	});

	const widthRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.WidthRange,
	});

	const depth = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.Depth,
	});

	const depthRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.DepthRange,
	});

	const numberOfPieces = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.NumberOfPieces,
	});

	const numberOfArtworks = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.NumberOfArtworks,
	});

	const isHeniArtwork = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsHeniArtwork,
	});

	const showFavouriteArtists = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ShowFavouriteArtists,
	});

	const isBundle = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsBundle,
	});

	const isFullSet = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsFullSet,
	});

	const editionSize = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionSize,
	});

	const editionSizeRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionSizeRange,
	});

	const editionNumber = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.EditionNumber,
	});

	const seriesTitle = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesTitle,
	});

	const seriesCrid = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesCrid,
	});

	const seriesSize = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesSize,
	});

	const seriesSizeRange = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SeriesSizeRange,
	});

	const isUnlimitedEdition = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.IsUnlimitedEdition,
	});

	const excludeMultiples = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.ExcludeMultiples,
	});

	const categoryTags = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.CategoryTags,
	});

	const subjectTags = getDecodedSearchParam({
		searchParams,
		key: ArtworkAdvancedSearchParam.SubjectTags,
	});

	// activity information
	const activityId = getDecodedSearchParam({
		searchParams,
		key: ArtActBasicSearchParam.ActivityId,
	});

	const activityTypes = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityType,
	});

	const listingType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingType,
	});

	const activityStatus = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityStatus,
	});

	const activityDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityDate,
	});

	const activityDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ActivityDateRange,
	});

	const listingPrice = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingPrice,
	});

	const listingPriceRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ListingPriceRange,
	});

	const saleAmount = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleAmount,
	});

	const saleAmountRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleAmountRange,
	});

	const association = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.Association,
	});

	const associationType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AssociationType,
	});

	const auctionHouse = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionHouse,
	});

	const auctionName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionName,
	});

	const auctionSaleName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionSaleName,
	});

	const saleNumber = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.SaleNumber,
	});

	const lotNumber = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.LotNumber,
	});

	const auctionType = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.AuctionType,
	});

	const fairName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairName,
	});

	const fairDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairDate,
	});

	const fairExhibitor = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairExhibitor,
	});

	const fairDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.FairDateRange,
	});

	const galleryName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryName,
	});

	const galleryCity = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryCity,
	});

	const galleryCountry = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.GalleryCountry,
	});

	const exhibitionName = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionName,
	});

	const exhibitionDate = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionDate,
	});

	const exhibitionDateRange = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionDateRange,
	});

	const exhibitionCity = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionCity,
	});

	const exhibitionCountry = getDecodedSearchParam({
		searchParams,
		key: ActivityAdvancedSearchParam.ExhibitionCountry,
	});

	// data entry
	const createdBy = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedBy,
	});

	const createdDate = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedDate,
	});

	const createdDateRange = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.CreatedDateRange,
	});

	const updatedBy = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedBy,
	});

	const updatedByDate = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedByDate,
	});

	const updatedByDateRange = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.UpdatedByDateRange,
	});

	const source = getDecodedSearchParam({
		searchParams,
		key: DataEntryAdvancedSearchParam.Source,
	});

	// omitted items
	const omittedArtworkIds = getDecodedSearchParam({
		searchParams,
		key: OmittedItemsSearchParam.OmittedArtworkIds,
	});

	const omittedActivityIds = getDecodedSearchParam({
		searchParams,
		key: OmittedItemsSearchParam.OmittedActivityIds,
	});

	const hideItemsWithoutImages = getDecodedSearchParam({
		searchParams,
		key: OmittedItemsSearchParam.HideItemsWithoutImages,
	});

	if (name) {
		artworkFilters = {
			...artworkFilters,
			artistNameOrId: name,
		};
	}

	if (title) {
		artworkFilters = {
			...artworkFilters,
			artworkTitleOrId: title,
		};
	}

	if (crid) {
		artworkFilters = {
			...artworkFilters,
			artworkCrid: crid,
		};
	}

	if (media) {
		artworkFilters = {
			...artworkFilters,
			artworkMedia: media,
		};
	}

	if (yearExecuted) {
		artworkFilters = {
			...artworkFilters,
			executedYear: getValueFilter({
				value: yearExecuted,
				range: yearExecutedRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (artworkTypes) {
		artworkFilters = {
			...artworkFilters,
			artworkType: artworkTypes.split(
				PARAM_SEPARATOR
			) as unknown as Artwork_Type_Enum[],
		};
	}

	if (heniArtworkTypes) {
		artworkFilters = {
			...artworkFilters,
			heniArtworkType: heniArtworkTypes.split(
				PARAM_SEPARATOR
			) as unknown as Heni_Artwork_Type_Enum[],
		};
	}

	if (height) {
		artworkFilters = {
			...artworkFilters,
			artworkHeight: getValueFilter({
				value: height,
				range: heightRange as ValueFilterOperator,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (width) {
		artworkFilters = {
			...artworkFilters,
			artworkWidth: getValueFilter({
				value: width,
				range: widthRange as ValueFilterOperator,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (depth) {
		artworkFilters = {
			...artworkFilters,
			artworkDepth: getValueFilter({
				value: depth,
				range: depthRange as ValueFilterOperator,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (numberOfPieces) {
		artworkFilters = {
			...artworkFilters,
			numberOfPieces: getValueFilter({
				value: numberOfPieces,
				range: ValueFilterOperator.Equal,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (numberOfArtworks) {
		artworkFilters = {
			...artworkFilters,
			numberOfArtworks: getValueFilter({
				value: numberOfArtworks,
				range: ValueFilterOperator.Equal,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (showFavouriteArtists === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			showFavouriteArtists: true,
		};
	}

	if (isHeniArtwork === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			isHeniArtwork: true,
		};
	}

	if (isBundle === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			isBundle: true,
		};
	}

	if (isFullSet === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			isFullSet: true,
		};
	}

	if (editionSize) {
		artworkFilters = {
			...artworkFilters,
			artworkWidth: getValueFilter({
				value: editionSize,
				range: editionSizeRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (editionNumber) {
		artworkFilters = {
			...artworkFilters,
			editionNumber,
		};
	}

	if (seriesTitle) {
		artworkFilters = {
			...artworkFilters,
			seriesTitleOrId: seriesTitle,
		};
	}

	if (seriesCrid) {
		artworkFilters = {
			...artworkFilters,
			seriesCrid,
		};
	}

	if (seriesSize) {
		artworkFilters = {
			...artworkFilters,
			seriesSize: getValueFilter({
				value: seriesSize,
				range: seriesSizeRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (isUnlimitedEdition === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			unlimitedEdition: true,
		};
	}

	if (excludeMultiples === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			numberOfArtworks: getValueFilter({
				value: '10',
				range: ValueFilterOperator.GreaterThan,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (categoryTags) {
		artworkFilters = {
			...artworkFilters,
			categoryTags: categoryTags.split(PARAM_SEPARATOR),
		};
	}

	if (subjectTags) {
		artworkFilters = {
			...artworkFilters,
			subjectTags: categoryTags.split(PARAM_SEPARATOR),
		};
	}

	if (activityId) {
		activityFilters = {
			...activityFilters,
			activityIds: [activityId],
		};
	}

	if (activityTypes) {
		activityFilters = {
			...activityFilters,
			activityType: activityTypes.split(
				PARAM_SEPARATOR
			) as unknown as Artwork_Activity_Type_Enum[],
		};
	}

	if (listingType) {
		activityFilters = {
			...activityFilters,
			listingType: listingType.split(
				PARAM_SEPARATOR
			) as unknown as Artwork_Listing_Type_Enum[],
		};
	}

	if (activityStatus) {
		activityFilters = {
			...activityFilters,
			activityStatus: activityStatus.split(
				PARAM_SEPARATOR
			) as unknown as Artwork_Activity_Status_Type_Enum[],
		};
	}

	if (activityDate) {
		activityFilters = {
			...activityFilters,
			activityDate: getValueFilter({
				value: activityDate,
				range: activityDateRange as ValueFilterOperator,
				type: 'date',
			}) as StringValueFilter,
		};
	}

	if (listingPrice) {
		activityFilters = {
			...activityFilters,
			knownPrice: getValueFilter({
				value: removeThousandSeparator(listingPrice),
				range: listingPriceRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (saleAmount) {
		activityFilters = {
			...activityFilters,
			saleAmount: getValueFilter({
				value: removeThousandSeparator(saleAmount),
				range: saleAmountRange as ValueFilterOperator,
				type: 'number',
			}) as FloatValueFilter,
		};
	}

	if (association) {
		if (associationType) {
			activityFilters = {
				...activityFilters,
				association: {
					type: associationType as Artwork_Activity_Association_Type_Enum,
					nameOrId: association,
				},
			};
		} else {
			activityFilters = {
				...activityFilters,
				association: {
					nameOrId: association,
				},
			};
		}
	}

	if (auctionHouse) {
		activityFilters = {
			...activityFilters,
			auctionHouseNameOrId: auctionHouse,
		};
	}

	if (auctionName) {
		activityFilters = {
			...activityFilters,
			auctionNameOrId: auctionName,
		};
	}

	if (auctionSaleName) {
		activityFilters = {
			...activityFilters,
			saleName: auctionSaleName,
		};
	}

	if (saleNumber) {
		activityFilters = {
			...activityFilters,
			saleNumber,
		};
	}

	if (lotNumber) {
		activityFilters = {
			...activityFilters,
			lotNumber,
		};
	}

	if (saleName) {
		activityFilters = {
			...activityFilters,
			saleName,
		};
	}

	if (auctionType) {
		activityFilters = {
			...activityFilters,
			auctionType: auctionType.split(PARAM_SEPARATOR) as Auction_Type_Enum[],
		};
	}

	if (fairName) {
		activityFilters = {
			...activityFilters,
			fairTitleOrId: fairName,
		};
	}

	if (fairDate) {
		activityFilters = {
			...activityFilters,
			fairDate: getValueFilter({
				value: fairDate,
				range: fairDateRange as ValueFilterOperator,
				type: 'date',
			}) as StringValueFilter,
		};
	}

	if (fairExhibitor) {
		activityFilters = {
			...activityFilters,
			fairExhibitorNameOrId: fairExhibitor,
		};
	}

	if (galleryName) {
		activityFilters = {
			...activityFilters,
			galleryNameOrId: galleryName,
		};
	}

	if (galleryCity || galleryCountry) {
		activityFilters = {
			...activityFilters,
			galleryLocation: {
				cityNameOrCode: galleryCity,
				countryNameOrCode: galleryCountry,
			},
		};
	}

	if (exhibitionName) {
		activityFilters = {
			...activityFilters,
			exhibitionTitleOrId: exhibitionName,
		};
	}

	if (exhibitionDate) {
		activityFilters = {
			...activityFilters,
			exhibitionDate: getValueFilter({
				value: exhibitionDate,
				range: exhibitionDateRange as ValueFilterOperator,
				type: 'date',
			}) as StringValueFilter,
		};
	}

	if (exhibitionCity || exhibitionCountry) {
		activityFilters = {
			...activityFilters,
			exhibitionVenueAddress: {
				cityNameOrCode: exhibitionCity,
				countryNameOrCode: exhibitionCountry,
			},
		};
	}

	if (createdBy) {
		commonFilters = {
			...commonFilters,
			userCreated: createdBy,
		};
	}

	if (createdDate) {
		commonFilters = {
			...commonFilters,
			dateCreated: getValueFilter({
				value: createdDate,
				range: createdDateRange as ValueFilterOperator,
				type: 'date',
			}) as StringValueFilter,
		};
	}

	if (updatedBy) {
		commonFilters = {
			...commonFilters,
			userUpdated: updatedBy,
		};
	}

	if (updatedByDate) {
		commonFilters = {
			...commonFilters,
			dateUpdated: getValueFilter({
				value: updatedByDate,
				range: updatedByDateRange as ValueFilterOperator,
				type: 'date',
			}) as StringValueFilter,
		};
	}

	if (source) {
		commonFilters = {
			...commonFilters,
			datasource: source,
		};
	}

	if (omittedActivityIds) {
		activityFilters = {
			...activityFilters,
			excludeActivityIds: omittedActivityIds.split(PARAM_SEPARATOR),
		};
	}

	if (omittedArtworkIds) {
		artworkFilters = {
			...artworkFilters,
			excludeArtworkIds: omittedArtworkIds.split(PARAM_SEPARATOR),
		};
	}

	if (hideItemsWithoutImages === StringBoolean.True) {
		artworkFilters = {
			...artworkFilters,
			hasImage: true,
		};
	}

	return {
		commonFilters,
		activityFilters,
		artworkFilters,
	};
};
