import { redirect } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Cookies } from '$lib/constants/cookies';
import { Searches } from '$lib/constants/searches';
import { gqlClient } from '$lib/gqlClient';
import { GetArtworkActivityAssociationTypesDocument } from '$lib/queries/__generated__/getArtworkActivityAssociationTypes.generated';
import { GetArtworkActivityStatusTypesDocument } from '$lib/queries/__generated__/getArtworkActivityStatusTypes.generated';
import { GetArtworkActivityTypesDocument } from '$lib/queries/__generated__/getArtworkActivityTypes.generated';
import { GetArtworkListingTypesDocument } from '$lib/queries/__generated__/getArtworkListingTypes.generated';
import { GetArtworkTypesDocument } from '$lib/queries/__generated__/getArtworkTypes.generated';
import { GetAuctionTypesDocument } from '$lib/queries/__generated__/getAuctionTypes.generated';
import { GetDatasourcesDocument } from '$lib/queries/__generated__/getDatasources.generated';
import { GetSearchPresetsDocument } from '$lib/queries/__generated__/getSearchPresets.generated';
import { GetTagsDocument } from '$lib/queries/__generated__/getTags.generated';
import type { ArtworksAndActivitiesSearchPageServerLoadEvent } from '$routes/artworks-and-activities/types';

export const artworksAndActivitiesSearchPageServerLoad = async ({
	parent,
	url,
	cookies,
}: ArtworksAndActivitiesSearchPageServerLoadEvent) => {
	const data = await parent();
	const searchType = url.searchParams.get('searchType');

	if (!searchType || searchType === 'basic') {
		const prevBasicSearchUrl = JSON.parse(
			(cookies.get(Cookies.Searches) as Searches) || {}
		)?.[Searches.BasicSearch];

		if (
			prevBasicSearchUrl &&
			['sort=-DateUpdated', 'searchType=basic&sort=-DateUpdated'].includes(
				url.searchParams.toString()
			)
		) {
			redirect(302, prevBasicSearchUrl);
		}
	} else if (searchType === 'advanced') {
		const prevAdvancedSearchUrl = JSON.parse(
			(cookies.get(Cookies.Searches) as Searches) || {}
		)?.[Searches.AdvancedSearch];

		if (
			prevAdvancedSearchUrl &&
			url.searchParams.toString() === 'searchType=advanced'
		) {
			redirect(302, prevAdvancedSearchUrl);
		}
	} else if (searchType === 'multipleIds') {
		const prevMultipleIdsUrl = JSON.parse(
			(cookies.get(Cookies.Searches) as Searches) || {}
		)?.[Searches.MultipleSearch];

		if (
			prevMultipleIdsUrl &&
			url.searchParams.toString() === 'searchType=multipleIds'
		) {
			redirect(302, prevMultipleIdsUrl);
		}
	}

	const authHeaders = getAuthorizationHeaders(data);

	const dataSourcesReq = gqlClient.request(
		GetDatasourcesDocument,
		{},
		authHeaders
	);

	const artworkTypeReq = gqlClient.request(
		GetArtworkTypesDocument,
		{},
		authHeaders
	);

	const artworkActivityTypeReq = gqlClient.request(
		GetArtworkActivityTypesDocument,
		{},
		authHeaders
	);

	// const artworkActivityStatusTypeReq = gqlClient.request(
	// 	GetArtworkActivityStatusTypesDocument,
	// 	{},
	// 	authHeaders
	// );

	// const artworkActivityAssociationTypeReq = gqlClient.request(
	// 	GetArtworkActivityAssociationTypesDocument,
	// 	{},
	// 	authHeaders
	// );

	const listingTypeReq = gqlClient.request(
		GetArtworkListingTypesDocument,
		{
			filter: {
				key: {
					_neq: 'AUCTION',
				},
			},
		},
		authHeaders
	);

	const auctionTypeReq = gqlClient.request(
		GetAuctionTypesDocument,
		{},
		authHeaders
	);

	const categoryTagReq = gqlClient.request(
		GetTagsDocument,
		{
			filter: {
				type: {
					key: {
						_eq: 'CATEGORY',
					},
				},
			},
		},
		authHeaders
	);

	const subjectTagReq = gqlClient.request(
		GetTagsDocument,
		{
			filter: {
				type: {
					key: {
						_eq: 'SUBJECT',
					},
				},
			},
		},
		authHeaders
	);

	const searchPresetsReq = gqlClient.request(
		GetSearchPresetsDocument,
		{ filter: { status: { key: { _neq: 'archived' } } } },
		authHeaders
	);

	const [
		dataSourcesRes,
		artworkTypeRes,
		artworkActivityTypeRes,
		// artworkActivityStatusTypeRes,
		// artworkActivityAssociationTypeRes,
		listingTypeRes,
		auctionTypeRes,
		categoryTagRes,
		subjectTagRes,
		searchPresetsRes,
	] = await Promise.all([
		dataSourcesReq,
		artworkTypeReq,
		artworkActivityTypeReq,
		// artworkActivityStatusTypeReq,
		// artworkActivityAssociationTypeReq,
		listingTypeReq,
		auctionTypeReq,
		categoryTagReq,
		subjectTagReq,
		searchPresetsReq,
	]);

	const dataSources = dataSourcesRes?.data_source;
	const artworkTypes = artworkTypeRes?.artwork_type;
	const artworkActivityTypes = artworkActivityTypeRes?.artwork_activity_type;
	// const artworkActivityStatusTypes =
	// 	artworkActivityStatusTypeRes?.artwork_activity_status_type;
	// const artworkActivityAssociationTypes =
	// 	artworkActivityAssociationTypeRes?.artwork_activity_association_type;
	const listingTypes = listingTypeRes?.artwork_listing_type;
	const auctionTypes = auctionTypeRes?.auction_type;
	const searchPresets = searchPresetsRes?.search_preset;

	// TODO: Get sources from the API once confirmed
	const sources = [
		{
			key: 'Example source',
			name: 'Example source',
		},
	];
	const categoryTags = categoryTagRes?.tag.map((item) => {
		const tag = item?.tag;
		return {
			key: tag,
			name: tag,
		};
	});
	const subjectTags = subjectTagRes?.tag.map((item) => {
		const tag = item?.tag;
		return {
			key: tag,
			name: tag,
		};
	});

	return {
		...data,
		dataSources,
		artworkTypes,
		artworkActivityTypes,
		// artworkActivityStatusTypes,
		// artworkActivityAssociationTypes,
		listingTypes,
		auctionTypes,
		sources,
		categoryTags,
		subjectTags,
		searchPresets,
	};
};
