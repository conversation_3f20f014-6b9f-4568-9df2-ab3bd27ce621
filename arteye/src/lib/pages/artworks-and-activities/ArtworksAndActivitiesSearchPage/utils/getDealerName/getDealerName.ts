import type { Artwork_Activity } from '$gql/types';
import { ArtworkActivityTypeKey, ArtworkListingKey } from '$lib/types/types';

export enum AssociationTypeName {
	Dealer = 'DEALER',
}

export const getDealerName = (artworkActivity: Artwork_Activity) => {
	if (!artworkActivity) {
		return null;
	}

	const findAssociation = (key: string) =>
		artworkActivity?.associations?.find((association) => {
			return association?.type?.key === key;
		});

	const dealer = findAssociation('DEALER');
	const seller = findAssociation('SELLER');
	const buyer = findAssociation('BUYER');

	const fallback = (() => {
		if (buyer) {
			return `${buyer?.entity?.name} (Buyer)`;
		}

		return seller ? `${seller?.entity?.name} (Seller)` : '';
	})();

	if (dealer) {
		const dealerName = dealer?.entity?.name;
		const locationName = dealer?.entity?.organisation?.location?.name;
		return `${dealerName}${locationName ? `, ${locationName}` : ''}`;
	}

	if (artworkActivity.type?.key === ArtworkActivityTypeKey.Auction) {
		const auctionHouse = artworkActivity?.artwork_listing?.find(
			(listing) =>
				(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
				ArtworkListingKey.Auction
		)?.auction_lot?.auction?.auction_house;

		const organisationName = auctionHouse?.organisation?.name;
		const locationName = auctionHouse?.organisation?.location?.name;

		return `${organisationName}${locationName ? `, ${locationName}` : ''}`;
	} else if (artworkActivity.type?.key === ArtworkActivityTypeKey.PrivateSale) {
		switch (artworkActivity?.artwork_listing?.[0]?.listing_type?.key) {
			case ArtworkListingKey.Fair: {
				const name =
					artworkActivity?.artwork_listing?.[0]?.fair_listing?.fair_exhibitor
						?.entity?.name;

				if (!name) {
					return fallback;
				}

				const location =
					artworkActivity?.artwork_listing?.[0]?.fair_listing?.fair_exhibitor
						?.entity?.organisation?.location?.name;

				return `${name}${location ? `, ${location}` : ''}`;
			}
			case ArtworkListingKey.Gallery: {
				const name =
					artworkActivity?.artwork_listing?.[0]?.gallery_listing?.gallery
						?.organisation?.name;

				if (!name) {
					return fallback;
				}

				const location =
					artworkActivity?.artwork_listing?.[0]?.gallery_listing?.gallery
						?.organisation?.location?.name;

				return `${name}${location ? `, ${location}` : ''}`;
			}
			case ArtworkListingKey.Exhibition: {
				const name =
					artworkActivity?.artwork_listing?.[0]?.exhibition_listing?.exhibition
						?.organisers?.[0]?.entity_id?.name;

				if (!name) {
					return fallback;
				}

				const location =
					artworkActivity?.artwork_listing?.[0]?.exhibition_listing?.exhibition
						?.organisers?.[0]?.entity_id?.organisation?.location?.name;

				return `${name}${location ? `, ${location}` : ''}`;
			}
			default:
				return fallback;
		}
	} else {
		return '';
	}
};
