import { MultipleIdsSearchParam } from '../../constants/search';
import type { Artwork_Activity_Filter } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';

export const getMultiplIdsSearchFilter = (searchParams: URLSearchParams) => {
	const _and: Artwork_Activity_Filter[] = [
		{ status: { key: { _neq: Status_Enum.Archived } } },
	];

	const activityIds = getDecodedSearchParam({
		searchParams,
		key: MultipleIdsSearchParam.ActivityIds,
	});

	const artworkIds = getDecodedSearchParam({
		searchParams,
		key: MultipleIdsSearchParam.ArtworkIds,
	});

	if (activityIds) {
		const filterItem: Artwork_Activity_Filter = {
			id: {
				_in: activityIds.split(','),
			},
		};

		_and.push(filterItem);
	}

	if (artworkIds) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					id: {
						_in: artworkIds.split(','),
					},
				},
			},
		};

		_and.push(filterItem);
	}

	return {
		_and,
	};
};
