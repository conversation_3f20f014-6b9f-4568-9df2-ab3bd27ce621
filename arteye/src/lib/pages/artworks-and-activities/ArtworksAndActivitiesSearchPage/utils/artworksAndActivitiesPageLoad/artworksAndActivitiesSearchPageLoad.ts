import { ArtworkAndActivitiesResultsTab } from '../../ArtworksResults';
import {
	ArtActBasicSearchParam,
	ArtActSearchType,
} from '../../constants/search';
import {
	ARTWORKS_ACTIVITY_DEFAULT_SORT,
	NEW_ARTWORKS_ACTIVITY_DEFAULT_SORT,
} from '../../constants/sort';
import { getArtworkActivitiesFilter } from '../getArtworkActivitiesFilter/getArtworkActivitiesFilter';
import { getMatchingImagesFilter } from '../getMatchingImagesFilter/getMatchingImagesFilter';
import { getMultiplIdsSearchFilter } from '../getMultiplIdsSearchFilter/getMultiplIdsSearchFilter';
import { newGetArtworkActivitiesFilter } from '../newGetArtworkActivitiesFilter/newGetArtworkActivitiesFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import { ActivitySearchSortField, SortDirection } from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import {
	ActivitySearchDocument,
	type ActivitySearchQuery,
} from '$lib/custom-queries/__generated__/activitySearch.generated';
import {
	ArtworkSearchDocument,
	type ArtworkSearchQuery,
} from '$lib/custom-queries/__generated__/artworkSearch.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetSearchPresetsDocument } from '$lib/queries/__generated__/getSearchPresets.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import { getPageSizeFromParam } from '$lib/utils/getPageSizeFromParam/getPageSizeFromParam';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { ArtworksAndActivitiesSearchPageLoadEvent } from '$routes/artworks-and-activities/types';

const getActivities = (res: ActivitySearchQuery | null) =>
	res?.activitySearch.activities.map(
		(activity) => activity?.artwork_activity
	) || [];

const getArtworks = (res: ArtworkSearchQuery | null) =>
	res?.artworkSearch.artworks.map((artwork) => artwork?.artwork) || [];

export const artworksAndActivitiesSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: ArtworksAndActivitiesSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;

	const data = existingData || {};

	// TODO: Get sources from the API once confirmed
	const sources = [
		{
			key: 'Example source',
			name: 'Example source',
		},
	];

	let activitySearchRes: ActivitySearchQuery | null = null;
	let artworkSearchRes: ArtworkSearchQuery | null = null;
	let activities: ReturnType<typeof getActivities> = [];
	let artworks: ReturnType<typeof getArtworks> = [];
	let offset = 0;
	let activitiesIds: string[] = [];
	let artworkIds: string[] = [];
	let input = {};

	const searchPresetsRes = await gqlClient.request(
		GetSearchPresetsDocument,
		{ filter: { status: { key: { _neq: 'archived' } } } },
		{ authorization }
	);

	const sectionInfo = {
		[ArtworkAndActivitiesResultsTab.Activities]: {
			resultsCount: 0,
			nextToken: '',
			previousToken: '',
			tokens: [] as string[],
			error: '',
		},
		[ArtworkAndActivitiesResultsTab.Artworks]: {
			resultsCount: 0,
			nextToken: '',
			previousToken: '',
			tokens: [] as string[],
			error: '',
		},
	};

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: SearchParam.ShowResults,
		}) === StringBoolean.True;

	const paginationTokenParam = getDecodedSearchParam({
		searchParams,
		key: SearchParam.PaginationToken,
	});

	const paginationDirectionParam = getDecodedSearchParam({
		searchParams,
		key: SearchParam.PaginationDirection,
	});

	const pageSize = getPageSizeFromParam({
		searchParams,
		defaultPageSize: TABLE_PAGE_SIZE,
	});

	if (showResults) {
		if (
			!searchParams.get('searchType') ||
			[ArtActSearchType.Basic, ArtActSearchType.Advanced].includes(
				searchParams.get('searchType') as ArtActSearchType
			)
		) {
			const { artworkFilters, activityFilters, commonFilters } =
				newGetArtworkActivitiesFilter(searchParams);

			const sort = getSortWithDirection<ActivitySearchSortField>(searchParams);

			offset = getOffset(searchParams);

			input = {
				activityFilters,
				artworkFilters,
				commonFilters,
				limit: pageSize,
				sort,
			};

			if (paginationTokenParam && paginationDirectionParam) {
				input = {
					...input,
					paginationToken: paginationTokenParam,
					paginationDirection: paginationDirectionParam,
				};
			}

			/* Artworks */
			if (
				searchParams.get('resultsTab') ===
				ArtworkAndActivitiesResultsTab.Artworks
			) {
				try {
					artworkSearchRes = await gqlClientCustom.request(
						ArtworkSearchDocument,
						{
							input: {
								...input,
								sort: [
									...((
										input as {
											sort: {
												field: ActivitySearchSortField;
												direction: SortDirection;
											}[];
										}
									).sort || []),
									{
										direction: SortDirection.Desc,
										field: ActivitySearchSortField.ArtworkActivityCount,
									},
								],
							},
						},
						{
							authorization,
						}
					);

					sectionInfo.artworks.resultsCount =
						artworkSearchRes.artworkSearch.meta.totalCount;

					sectionInfo.artworks.nextToken =
						artworkSearchRes.artworkSearch.meta.nextToken || '';

					sectionInfo.artworks.previousToken =
						artworkSearchRes.artworkSearch.meta.previousToken || '';

					sectionInfo.artworks.error = '';

					sectionInfo.artworks.tokens =
						artworkSearchRes.artworkSearch.tokens.map(
							(token) => `${token?.token}`
						);

					artworks = getArtworks(artworkSearchRes);

					artworkIds = artworks.map((artwork) => `${artwork?.id}`);
				} catch {
					sectionInfo.artworks.resultsCount = 0;
					sectionInfo.artworks.nextToken = '';
					sectionInfo.artworks.previousToken = '';
					sectionInfo.artworks.tokens = [];
					sectionInfo.artworks.error = 'payload';

					artworks = [];
					artworkIds = [];
				}
				/* Activities */
			} else {
				activitySearchRes = await gqlClientCustom.request(
					ActivitySearchDocument,
					{
						input,
					},
					{
						authorization,
					}
				);

				sectionInfo.activities.resultsCount =
					activitySearchRes.activitySearch.meta.totalCount;

				sectionInfo.activities.nextToken =
					activitySearchRes.activitySearch.meta.nextToken || '';

				sectionInfo.activities.error = '';

				sectionInfo.activities.previousToken =
					activitySearchRes.activitySearch.meta.previousToken || '';

				sectionInfo.activities.tokens =
					activitySearchRes.activitySearch.tokens.map(
						(token) => `${token?.token}`
					);

				activities = getActivities(activitySearchRes);

				activitiesIds = activitySearchRes.activitySearch.activities.map(
					(activity) => `${activity?.artwork_activity.id}`
				);
			}
		} else {
			const multipleIdsFilter = getMultiplIdsSearchFilter(searchParams);
			const matchingImagesFilter = getMatchingImagesFilter(searchParams);
			offset = getOffset(searchParams);

			const searchForArtworkIds = async (ids: string[]) => {
				const artworksPromise = gqlClientCustom.request(
					ArtworkSearchDocument,
					{
						input: { artworkFilters: { artworkIds: ids }, limit: 10000 },
					},
					{
						authorization,
					}
				);

				const activitiesPromise = gqlClientCustom.request(
					ActivitySearchDocument,
					{
						input: { artworkFilters: { artworkIds: ids }, limit: 10000 },
					},
					{
						authorization,
					}
				);

				const [artworksRes, activitiesRes] = await Promise.all([
					artworksPromise,
					activitiesPromise,
				]);

				const getArtworkIdIndex = (
					artwork: NonNullable<
						typeof artworksRes
					>['artworkSearch']['artworks'][number]
				) => {
					return ids.indexOf(`${artwork?.artwork?.id}`);
				};

				artworksRes.artworkSearch.artworks.sort((artworkA, artworkB) => {
					return getArtworkIdIndex(artworkA) - getArtworkIdIndex(artworkB);
				});

				artworks = getArtworks(artworksRes);

				const activityIds = artworks.flatMap((artwork) =>
					artwork?.activity?.map((activity) => activity?.artwork_activity?.id)
				);

				const getActivityArtworkIdIndex = (
					activity: NonNullable<
						typeof activitiesRes
					>['activitySearch']['activities'][number]
				) => {
					return activityIds.indexOf(`${activity?.artwork_activity?.id}`);
				};

				activitiesRes.activitySearch.activities.sort((activityA, activityB) => {
					return (
						getActivityArtworkIdIndex(activityA) -
						getActivityArtworkIdIndex(activityB)
					);
				});

				activities = getActivities(activitiesRes);

				artworkIds = ids;
				activitiesIds = activities.map((activity) => `${activity?.id}`);
				sectionInfo.activities.resultsCount = activities.length;
				sectionInfo.artworks.resultsCount = artworks.length;
			};

			if (
				(searchParams.get('searchType') === ArtActSearchType.MultipleIds &&
					(multipleIdsFilter?._and?.length || 0) <= 1) ||
				(searchParams.get('searchType') === ArtActSearchType.ByImage &&
					(matchingImagesFilter?._and?.length || 0) <= 1)
			) {
				console.log('empty');
			} else if (
				searchParams.get('searchType') === ArtActSearchType.MultipleIds
			) {
				artworkIds = multipleIdsFilter?._and?.[1]?.artworks?.artwork?.id
					?._in as string[];

				if (artworkIds) {
					await searchForArtworkIds(artworkIds);
				} else {
					activitiesIds = multipleIdsFilter?._and?.[1]?.id?._in as string[];

					const artworksPromise = gqlClientCustom.request(
						ArtworkSearchDocument,
						{
							input: {
								activityFilters: { activityIds: activitiesIds },
								limit: 10000,
							},
						},
						{
							authorization,
						}
					);

					const activitiesPromise = gqlClientCustom.request(
						ActivitySearchDocument,
						{
							input: {
								activityFilters: { activityIds: activitiesIds },
								limit: 10000,
							},
						},
						{
							authorization,
						}
					);

					const [artworksRes, activitiesRes] = await Promise.all([
						artworksPromise,
						activitiesPromise,
					]);

					const getActivityArtworkIdIndex = (
						activity: NonNullable<
							typeof activitiesRes
						>['activitySearch']['activities'][number]
					) => {
						return activitiesIds.indexOf(`${activity?.artwork_activity?.id}`);
					};

					activitiesRes.activitySearch.activities.sort(
						(activityA, activityB) => {
							return (
								getActivityArtworkIdIndex(activityA) -
								getActivityArtworkIdIndex(activityB)
							);
						}
					);

					activities = getActivities(activitiesRes);

					artworkIds = activities.flatMap((activity) =>
						activity?.artworks?.map((artwork) => artwork?.artwork?.id)
					) as string[];

					const getArtworkIdIndex = (
						artwork: NonNullable<
							typeof artworksRes
						>['artworkSearch']['artworks'][number]
					) => {
						return artworkIds.indexOf(`${artwork?.artwork?.id}`);
					};

					artworksRes.artworkSearch.artworks.sort((artworkA, artworkB) => {
						return getArtworkIdIndex(artworkA) - getArtworkIdIndex(artworkB);
					});

					artworks = getArtworks(artworksRes);

					sectionInfo.activities.resultsCount = activities.length;
					sectionInfo.artworks.resultsCount = artworks.length;
				}
			} else if (searchParams.get('searchType') === ArtActSearchType.ByImage) {
				artworkIds = matchingImagesFilter?._and?.[1]?.artworks?.artwork?.id
					?._in as string[];

				await searchForArtworkIds(artworkIds);
			}
		}
	}

	return {
		...parentData,
		...data,
		sources,
		input,
		offset,
		activities,
		artworks,
		activitiesIds,
		artworkIds,
		searchPresets: searchPresetsRes?.search_preset,
		showResults: showResults === true,
		currentPage,
		sectionInfo,
	};
};
