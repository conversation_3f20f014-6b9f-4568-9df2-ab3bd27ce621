import { ImageUploadSearchParam } from '../../constants/search';
import { matchingImageIds } from '../../stores/imageSearchStore/imageSearchStore';
import type { Artwork_Activity_Filter } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';

export const getMatchingImagesFilter = (searchParams: URLSearchParams) => {
	let imageIds: string[] = [];

	matchingImageIds.subscribe((ids) => {
		imageIds = ids;
	});

	const _and: Artwork_Activity_Filter[] = [
		{ status: { key: { _neq: Status_Enum.Archived } } },
	];

	const image = getDecodedSearchParam({
		searchParams,
		key: ImageUploadSearchParam.Image,
	});

	if (imageIds.length === 0) {
		return {};
	}

	if (image) {
		const filterItem: Artwork_Activity_Filter = {
			artworks: {
				artwork: {
					id: {
						_in: [...imageIds],
					},
				},
			},
		};

		_and.push(filterItem);
	}
	matchingImageIds.set([]);

	return {
		_and,
	};
};
