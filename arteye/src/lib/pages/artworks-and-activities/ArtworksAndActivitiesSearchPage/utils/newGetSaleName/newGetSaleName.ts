import {
	Artwork_Activity_Type_Enum,
	Artwork_Listing_Type_Enum,
} from '$gql/types-custom';
import type { ArtworkActivityFragment } from '$lib/custom-queries/__generated__/activitySearch.generated';
import { ArtworkListingKey } from '$lib/types/types';

export const newGetSaleName = (
	artworkActivity: ArtworkActivityFragment | undefined | null
) => {
	if (artworkActivity?.type?.key === Artwork_Activity_Type_Enum.Auction) {
		return (
			artworkActivity?.artwork_listing?.find(
				(listing) =>
					(listing?.listing_type?.key as unknown as ArtworkListingKey) ===
					ArtworkListingKey.Auction
			)?.auction_lot?.auction?.sale_name || ''
		);
	} else if (
		artworkActivity?.type?.key === Artwork_Activity_Type_Enum.PrivateSale
	) {
		switch (artworkActivity?.artwork_listing?.[0]?.listing_type?.key) {
			case Artwork_Listing_Type_Enum.Auction:
				return (
					artworkActivity?.artwork_listing?.find(
						(listing) =>
							listing?.listing_type?.key === Artwork_Listing_Type_Enum.Auction
					)?.auction_lot?.auction?.sale_name || ''
				);
			case Artwork_Listing_Type_Enum.Exhibition:
				return (
					artworkActivity?.artwork_listing?.find(
						(listing) =>
							listing?.listing_type?.key ===
							Artwork_Listing_Type_Enum.Exhibition
					)?.exhibition_listing?.exhibition?.title || ''
				);
			case Artwork_Listing_Type_Enum.Fair:
				return (
					artworkActivity?.artwork_listing?.find(
						(listing) =>
							listing?.listing_type?.key === Artwork_Listing_Type_Enum.Fair
					)?.fair_listing?.fair_exhibitor?.fair?.title || ''
				);
			case Artwork_Listing_Type_Enum.Gallery:
				return 'Gallery Offer';
			case Artwork_Listing_Type_Enum.Private:
				return '';
			default:
				return '';
		}
	} else {
		return '';
	}
};
