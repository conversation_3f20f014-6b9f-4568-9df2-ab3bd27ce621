<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { createQuery } from '@tanstack/svelte-query';
	import dayjs from 'dayjs';
	import { derived as derivedStore, writable } from 'svelte/store';
	import { getActivityDate } from '../../ArtworksAndActivitiesSearchPage/ArtworksResults/ActivityCard/utils/getActivityDate/getActivityDate';
	import { getLotNumber } from '../../ArtworksAndActivitiesSearchPage/ArtworksResults/ActivityCard/utils/getLotNumber/getLotNumber';
	import { getSaleAmount } from '../../ArtworksAndActivitiesSearchPage/ArtworksResults/ActivityCard/utils/getSaleAmount/getSaleAmount';
	import { getSaleName } from '../../ArtworksAndActivitiesSearchPage/utils/getSaleName/getSaleName';
	import { newGetDealerName } from '../../ArtworksAndActivitiesSearchPage/utils/newGetDealerName/newGetDealerName';
	import { MergeActivities } from '../MergeActivities';
	import { NewActivityDialog } from '../NewActivityDialog';
	import type { AssociatedEntityChip } from '../utils/getAssociatedEntitiesChip/getAssociatedEntitiesChip';
	import {
		getAssociatedEntitiesChip,
		isAssociatedEntityChip,
	} from '../utils/getAssociatedEntitiesChip/getAssociatedEntitiesChip';
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Artwork_Activity } from '$gql/types';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { PARAM_NEW } from '$lib/constants/params';
	import type { ArtworkActivityFragment } from '$lib/custom-queries/__generated__/activitySearch.generated';
	import { GetArtworkPageArtworksActivitiesDocument } from '$lib/queries/__generated__/getArtworkPageArtworksActivities.generated';
	import type { GetArtworksActivitiesQuery } from '$lib/queries/__generated__/getArtworksActivities.generated';
	import { getQuery } from '$lib/query-utils/getQuery';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		artworkId: string;
	}

	let { artworkId }: Props = $props();

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let resultsCount = $derived(data.resultsCount);
	let pageNumber = writable(1);
	let artworkActivities = $derived(data.activitiesRes);
	let activitiesVariables = $derived(data.activitiesVariables);
	let activitiesCount = $derived(
		artworkActivities?.artwork_activity_aggregated?.[0]?.count?.id || 0
	);

	$effect(() => {
		if (artworkId) {
			pageNumber.set(1);
		}
	});

	let artworkActivitiesQuery = $derived(
		createQuery(
			derivedStore([pageNumber], ([$pageNumber]) => {
				return getQuery(
					GetArtworkPageArtworksActivitiesDocument,
					{
						...activitiesVariables,
						offset: ($pageNumber - 1) * TABLE_PAGE_SIZE,
					},
					getAuthorizationHeaders({
						user: { access_token: page.data.user.access_token },
					})
				);
			})
		)
	);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
	}

	interface Row {
		artworkActivity: NonNullable<
			GetArtworksActivitiesQuery['artwork_activity']
		>[number];
		fields: {
			[HeaderFieldName.ActivityType]: string;
			[HeaderFieldName.ActivityStatus]: string;
			[HeaderFieldName.ActivityDate]: string;
			[HeaderFieldName.Price]: string;
			[HeaderFieldName.ListingName]: string;
			[HeaderFieldName.DealerName]: string;
			[HeaderFieldName.AssociatedEntities]: AssociatedEntityChip[];
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.ActivityType,
			title: 'Activity type',
		},
		{
			fieldName: HeaderFieldName.ActivityDate,
			title: 'Date',
		},
		{
			fieldName: HeaderFieldName.ActivityStatus,
			title: 'Status',
		},
		{
			fieldName: HeaderFieldName.Price,
			title: 'Price',
		},
		{
			fieldName: HeaderFieldName.DealerName,
			title: 'Main association',
		},
		{
			fieldName: HeaderFieldName.ListingName,
			title: 'Sale name',
		},
		{
			fieldName: HeaderFieldName.AssociatedEntities,
			title: 'Associated entities',
		},
	];

	let rows = $derived(
		($artworkActivitiesQuery.data?.artwork_activity || [])
			.map<Row>((artworkActivity) => {
				return {
					artworkActivity,
					fields: {
						activityType: artworkActivity?.type?.name || '',
						activityStatus:
							artworkActivity?.activity_status?.[0]?.type?.name || '',
						activityDate:
							dayjs
								.utc(getActivityDate(artworkActivity as Artwork_Activity))
								.format('DD/MM/YYYY') || '',
						price: getSaleAmount(artworkActivity as Artwork_Activity) || '',
						dealerName: newGetDealerName(
							artworkActivity as ArtworkActivityFragment
						),
						listingName:
							`${[
								getLotNumber(artworkActivity as Artwork_Activity),
								getSaleName(artworkActivity as Artwork_Activity),
							]
								.filter(Boolean)
								.join(', ')}` || '',
						associatedEntities: getAssociatedEntitiesChip(
							artworkActivity as Artwork_Activity
						),
					},
				};
			})
			.sort((a, b) => {
				const dateA = dayjs(a.fields.activityDate, 'DD/MM/YYYY');
				const dateB = dayjs(b.fields.activityDate, 'DD/MM/YYYY');
				return dateB.unix() - dateA.unix();
			})
	);

	const mergeActivitiesDialogStores = createDialog();

	const addActivityDialogStores = createDialog();

	const dataCyPrefix = 'table';

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		pageNumber.set(page);
	};

	const handleOpenNewActivityDialog = () => {
		try {
			addActivityDialogStores.states.open.set(true);
		} catch (error) {
			console.error(error);
		}
	};

	const handleMergeActivitesClick = () => {
		mergeActivitiesDialogStores.states.open.set(true);
	};

	const handleRowClick = (activityId: string) => {
		goto(`/artworks-and-activities/activity/${activityId}`);
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border border-b-0 border-gray-200 bg-white p-4"
>
	<div class="flex w-full items-center justify-between border-gray-200 gap-4">
		<Txt variant="h6">Artwork activity</Txt>

		<div class="lg:flex items-center justify-between gap-4 hidden">
			<Button
				dataCy={`${dataCyPrefix}-open-merge-activities-dialog`}
				size="sm"
				variant="secondary"
				onclick={handleMergeActivitesClick}
				disabled={activitiesCount === 0}
			>
				Merge Activities With Another Artwork
			</Button>

			<Button
				dataCy={`${dataCyPrefix}-open-new-activity-dialog`}
				size="sm"
				onclick={handleOpenNewActivityDialog}
				disabled={page.params.id === PARAM_NEW}
			>
				{#snippet leading()}
					<PlusIcon class="mr-2" />
				{/snippet}
				Add New Activity
			</Button>
		</div>
	</div>
</div>

<div class="overflow-x-auto">
	<table
		class="mb-6 w-full table-fixed border-collapse rounded-b-md bg-white min-w-[1200px]"
	>
		<TableHeaderRow dataCy={dataCyPrefix}>
			{#each headers as header, i}
				<TableHeader dataCy={dataCyPrefix}>
					{header.title}
				</TableHeader>
			{/each}
		</TableHeaderRow>

		<TableBody dataCy={dataCyPrefix}>
			{#if $artworkActivitiesQuery.isPending}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
					<CircularProgress dataCy={dataCyPrefix} />
				</TableNoResults>
			{:else}
				{#each rows as row, i}
					<TableRow
						index={i}
						dataCy={dataCyPrefix}
						onclick={() => handleRowClick(row.artworkActivity.id)}
					>
						{#each headers as header}
							{@const entityChips = row.fields[
								header.fieldName
							] as AssociatedEntityChip[]}

							{#if header.fieldName === HeaderFieldName.AssociatedEntities}
								<TableCell dataCy={dataCyPrefix}>
									{#snippet custom()}
										<span class="flex">
											{#if isAssociatedEntityChip(entityChips) && !!entityChips.length}
												{#each entityChips as entityChip, i}
													<Tooltip
														dataCy={dataCyPrefix}
														content={`${entityChip?.type}: ${entityChip?.name}`}
													>
														<div
															class="mr-1 flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-white"
															data-cy={`${dataCyPrefix}-associated-entity-${i}`}
														>
															<Txt variant="label3" class="text-xs">
																{entityChip?.initials}
															</Txt>
														</div>
													</Tooltip>
												{/each}
											{:else}
												<Txt variant="body2">-</Txt>
											{/if}
										</span>
									{/snippet}
								</TableCell>
							{:else if !isAssociatedEntityChip(entityChips)}
								<TableCell dataCy={dataCyPrefix} content={entityChips}>
									{entityChips || '-'}
								</TableCell>
							{/if}
						{/each}
					</TableRow>
				{/each}

				{#if rows.length === 0}
					<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
						No results found
					</TableNoResults>
				{/if}
			{/if}
		</TableBody>
	</table>
</div>

{#key resultsCount}
	{#key pageNumber}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				<Pagination
					currentPage={$pageNumber}
					total={resultsCount}
					limit={TABLE_PAGE_SIZE}
					dataCy={dataCyPrefix}
					onClick={handlePaginationClick}
				/>
			</div>
		{/if}
	{/key}
{/key}

<MergeActivities dialogStores={mergeActivitiesDialogStores} />

<NewActivityDialog
	dataCy={dataCyPrefix}
	dialogStores={addActivityDialogStores}
	{artworkId}
/>
