<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { createMutation } from '@tanstack/svelte-query';
	import { page } from '$app/state';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { UpdateArtworkItemDocument } from '$lib/queries/__generated__/updateArtwork.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import type { ReportsSearchPageData } from '$routes/reports/types';
	interface Props {
		dataCy: string;
		dialogStores: ReturnType<typeof createDialog>;
		id?: string | undefined;
		onSuccessfulArchive?: (() => void) | undefined;
	}

	let {
		dataCy,
		dialogStores,
		id = undefined,
		onSuccessfulArchive = undefined,
	}: Props = $props();

	let data = $derived(getPageData<ReportsSearchPageData>(page.data));

	let dataCyPrefix = $derived(`${dataCy}-delete-report`);

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};

	let archiveReportMutation = $derived(
		createMutation(
			getMutation(UpdateArtworkItemDocument, getAuthorizationHeaders(data))
		)
	);

	const handleDelete = async () => {
		if (id) {
			try {
				$archiveReportMutation
					.mutateAsync({
						id,
						data: {
							status: {
								key: Status_Enum.Archived,
								name: 'Archived',
							},
						},
					})
					.finally(() => {
						dialogStores.states.open.set(false);
						if (onSuccessfulArchive) {
							onSuccessfulArchive();
						}
					});
			} catch (e) {
				console.error(e);
			}
		}
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-full max-h-none w-full items-center justify-center rounded-none p-[1.5rem] pt-[1.25rem] sm:max-h-[16rem] sm:max-w-[32rem] sm:rounded sm:p-[1.5rem] sm:pt-[1rem]"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<Txt variant="h3" class=" mb-4 mt-4 max-w-[24rem] text-center"
			>Confirm archive</Txt
		>
		<Txt variant="body2" class=" max-w-[26rem] text-center"
			>Are you sure you want to archive this artwork?</Txt
		>

		<div class="mt-6 flex w-full">
			<Button
				class="sm:mr-4 sm:w-1/3"
				{dataCy}
				variant="tertiary"
				size="md"
				fullWidth
				onclick={handleClose}
			>
				Back
				{#snippet leading()}
					<ChevronLeftIcon class="ml-2 h-4 w-4" />
				{/snippet}
			</Button>
			<Button
				dataCy={`${dataCyPrefix}-delete`}
				onclick={handleDelete}
				size="md"
			>
				CONFIRM ARCHIVE
			</Button>
		</div>
	</div></Dialog
>
