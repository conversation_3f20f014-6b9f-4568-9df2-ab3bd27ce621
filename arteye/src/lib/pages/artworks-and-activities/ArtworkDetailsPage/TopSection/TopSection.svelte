<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import type { ArtworkDetailsForm } from '../ArtworkDetailsPage.svelte';
	import { ArchiveArtworkDialog } from './ArchiveArtworkDialog';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { InfoLabel } from '$lib/components/InfoLabel';
	import { Config } from '$lib/constants/config';
	import { PARAM_NEW } from '$lib/constants/params';
	import { DirectusRoutes, Routes } from '$lib/constants/routes';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	interface Props {
		artworkId: string;
		loading: boolean;
		artworkDetailsForm?: ArtworkDetailsForm;
		onChange: (
			activityArtwork: ArtworkDetailsForm,
			preventShowSaveBar: boolean
		) => void;
	}

	let { loading, artworkDetailsForm = {}, onChange }: Props = $props();

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));

	let legacyId = $derived(data.legacyId);
	let artworkActivities = $derived(data.activitiesRes);

	const dataCy = 'artist-details-top-section';

	let crumbs = $derived([
		{ label: 'Artworks', href: userRoutes.routes.basic_search },
		{
			label:
				page.params.id === PARAM_NEW
					? 'New artwork'
					: `${artworkDetailsForm.title || '-'}`,
		},
	]);

	const handleClickCopyArtworkId = () => {
		navigator.clipboard.writeText(artworkDetailsForm?.id || '');
		showToast({
			message: 'Artwork Id copied to clipboard',
			variant: 'success',
		});
	};

	const dialogStores = createDialog();

	const handleSuccessfulArchive = () => {
		onChange(
			{
				...artworkDetailsForm,
				status: {
					key: Status_Enum.Archived,
					name: 'Archived',
				},
			},
			true
		);
		showToast({
			message: 'Artwork has been archived',
			variant: 'success',
		});
	};

	const handleArchiveArtwork = () => {
		dialogStores.states.open.set(true);
	};
</script>

<div
	class="flex flex-col lg:flex-row lg:h-12 w-full lg:items-center justify-between"
>
	<div class="flex w-auto items-center">
		<Breadcrumbs class="pl-0 sm:pl-0" {dataCy} {crumbs} txtVariant="h5" />

		<Txt variant="label3" class="bg-gray-0 w-auto text-nowrap rounded p-1 px-2">
			{artworkActivities?.artwork_activity_aggregated?.[0]?.count?.id}
			activities
		</Txt>
	</div>

	<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
		<div class="mr-2 flex items-center gap-1">
			{#if false}
				<Button
					onclick={() => {
						console.log('TODO arteye when artwork search is available');
					}}
					dataCy={`${dataCy}-prev`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading}
					size="xs"
				>
					<ChevronLeftIcon class="h-3 w-3" />
				</Button>
			{/if}

			{#if false}
				<Button
					onclick={() => {
						console.log('TODO arteye when artwork search is available');
					}}
					dataCy={`${dataCy}-next`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading}
					size="xs"
				>
					<ChevronRightIcon class="h-3 w-3" />
				</Button>
			{/if}
		</div>

		{#if legacyId}
			<InfoLabel title="Legacy ID" value={legacyId} />
		{/if}
		{#if artworkActivities.artwork_activity.length > 1}
			<Button
				dataCy={`${dataCy}-delete`}
				class="h-[2rem] w-[2rem]"
				variant="secondary"
				onclick={handleArchiveArtwork}
				size="xs"
			>
				<BinIcon class="h-3 w-3" />
			</Button>
			<Button
				{dataCy}
				variant="secondary"
				size="sm"
				onclick={handleClickCopyArtworkId}
			>
				Copy Artwork Id
			</Button>
			<LinkButton
				{dataCy}
				variant="secondary"
				size="sm"
				icon
				newTab
				href={`${Config.DirectusUrl}${DirectusRoutes.Artwork}/${artworkDetailsForm.id}`}
			>
				View in directus
				{#snippet trailing()}
					<ExternalIcon class="h-4 w-4" />
				{/snippet}
			</LinkButton>
		{/if}
	</div>
</div>
<ArchiveArtworkDialog
	{dataCy}
	id={artworkDetailsForm.id}
	{dialogStores}
	onSuccessfulArchive={handleSuccessfulArchive}
/>
