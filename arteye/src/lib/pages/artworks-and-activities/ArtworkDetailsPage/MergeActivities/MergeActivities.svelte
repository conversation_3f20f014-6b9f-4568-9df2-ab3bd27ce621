<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import { MergeActivitiesDialog } from './MergeActivitiesDialog';
	import type { ReceivingArtwork } from './MergeActivitiesDialog/ReceivingArtworkAutocomplete/ReceivingArtworkAutocomplete.svelte';
	import { MergeActivitiesSuccessDialog } from './MergeActivitiesSuccessDialog';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
	}

	let { dialogStores }: Props = $props();

	const successDialogStores = createDialog();

	let toArtwork: ReceivingArtwork | null = $state(null);
	let activitiesCount = $state(0);

	const handleMergeActivitiesComplete = (event: {
		toArtwork: ReceivingArtwork;
		activitiesCount: number;
	}) => {
		toArtwork = event.toArtwork;
		activitiesCount = event.activitiesCount;

		dialogStores.states.open.set(false);
		successDialogStores.states.open.set(true);
	};
</script>

<MergeActivitiesDialog
	{dialogStores}
	onComplete={handleMergeActivitiesComplete}
/>

<MergeActivitiesSuccessDialog
	{toArtwork}
	{activitiesCount}
	dialogStores={successDialogStores}
/>
