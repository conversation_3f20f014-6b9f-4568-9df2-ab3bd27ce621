<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { ReceivingArtworkAutocomplete } from './ReceivingArtworkAutocomplete';
	import type { ReceivingArtwork } from './ReceivingArtworkAutocomplete/ReceivingArtworkAutocomplete.svelte';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { InputError } from '$global/components/Input/InputError';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { Routes } from '$lib/constants/routes';
	import { TransferActivitiesDocument } from '$lib/custom-queries/__generated__/transferActivities.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';
	import { UpdateAndReturnArtworkDocument } from '$lib/custom-queries/__generated__/updateAndReturnArtwork.generated';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		onComplete: (event: {
			toArtwork: ReceivingArtwork;
			activitiesCount: number;
		}) => void;
	}

	let { dialogStores, onComplete }: Props = $props();

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));

	let fromArtwork = $derived(data.artwork);
	let artworkActivities = $derived(data.activitiesRes);
	let toArtwork: ReceivingArtwork | null = $state(null);

	let activitiesCount = $derived(
		artworkActivities?.artwork_activity_aggregated?.[0]?.count?.id || 0
	);

	let activitiesCountString = $derived(
		activitiesCount === 1
			? `${activitiesCount} activity`
			: `${activitiesCount} activities`
	);

	const dataCy = 'merge-activities-dialog';

	let error = $state('');
	let loading = $state(false);

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};

	const handleMergeClick = async () => {
		if (!fromArtwork.id || !toArtwork?.id) {
			error = 'An error ocurred while merging activities.';

			return;
		}

		loading = true;

		try {
			await gqlClientCustom.request(
				TransferActivitiesDocument,
				{
					fromArtworkId: fromArtwork.id,
					toArtworkId: toArtwork?.id,
				},
				getAuthorizationHeaders(data)
			);

			await gqlClientCustom.request(
				UpdateAndReturnArtworkDocument,
				{
					artworks: [fromArtwork.id, toArtwork.id],
				},
				getAuthorizationHeaders(data)
			);

			onComplete({ toArtwork, activitiesCount });
		} catch {
			error = 'An error ocurred while merging activities.';
		} finally {
			loading = false;
		}
	};

	const handleReceivingArtworkChange = (artwork: ReceivingArtwork) => {
		toArtwork = artwork;
	};

	const handleReceivingArtworkRemove = () => {
		toArtwork = null;
	};

	let getSendingArtworkSubtitle = $derived(() => {
		const artist = fromArtwork?.artists?.[0]?.artist_id;

		let subTitle = '';

		if (
			artist?.person?.entity?.name &&
			artist?.person?.nationalities?.[0]?.country?.country_nationality &&
			(artist?.person?.year_birth || artist?.person?.year_death)
		) {
			subTitle = `${artist?.person?.entity?.name}, ${
				artist?.person?.nationalities?.[0]?.country?.country_nationality
			}, ${getYearBirthDeathString({
				yearBirth: artist?.person?.year_birth,
				yearDeath: artist?.person?.year_death,
				withWrapper: false,
			})}`;
		} else if (
			artist?.person?.entity?.name &&
			artist?.person?.nationalities?.[0]?.country?.country_nationality
		) {
			subTitle = `${artist?.person?.entity?.name}, ${artist?.person?.nationalities?.[0]?.country?.country_nationality}`;
		} else if (artist?.person?.entity?.name) {
			subTitle = `${artist?.person?.entity?.name}`;
		}

		return subTitle ? `(${subTitle})` : '';
	});
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	{dataCy}
	class=" p-[1.5rem]"
	title={`Merge ${activitiesCountString}`}
>
	<div>
		<Txt class="mb-8 text-center">
			Please double check all the information before merging the activities from
			one artwork to another.
		</Txt>

		<div class="mb-2 flex justify-between">
			<InputLabel dataCy={`${dataCy}-artwork-from`}>
				Artwork with {activitiesCountString}
			</InputLabel>

			<InfoTooltip dataCy={`${dataCy}-artwork-from`} content="tbc" />
		</div>

		<AutocompleteSelectedOption
			dataCy={`${dataCy}-artwork-from`}
			title={fromArtwork.title || ''}
			subTitle={getSendingArtworkSubtitle()}
			url={`${Routes.ArtworkDetails}/${fromArtwork.id}`}
			class="mb-6"
		/>

		<InputLabel dataCy={`${dataCy}-artwork-to`} required class="mb-2">
			Artwork you want to add the {activitiesCountString} to
		</InputLabel>

		<div class="mb-8">
			<ReceivingArtworkAutocomplete
				dataCy={`${dataCy}-artwork--to`}
				onChange={handleReceivingArtworkChange}
				onRemove={handleReceivingArtworkRemove}
				{toArtwork}
			/>
		</div>

		{#if error}
			<InputError dataCy={`${dataCy}-error`} class="mb-4 text-center">
				{error}
			</InputError>
		{/if}

		<Button
			dataCy={`${dataCy}-merge`}
			size="md"
			variant="primary"
			onclick={handleMergeClick}
			class="mb-4"
			fullWidth
			disabled={!toArtwork || loading}
			{loading}
		>
			Merge All Activities
		</Button>

		<Button
			dataCy={`${dataCy}-cancel`}
			size="md"
			variant="secondary"
			onclick={handleClose}
			fullWidth
		>
			Cancel
		</Button>
	</div>
</Dialog>
