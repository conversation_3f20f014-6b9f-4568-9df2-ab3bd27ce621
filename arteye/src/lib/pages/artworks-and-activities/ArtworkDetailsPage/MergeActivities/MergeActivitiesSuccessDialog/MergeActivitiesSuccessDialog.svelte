<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import type { ReceivingArtwork } from '../MergeActivitiesDialog/ReceivingArtworkAutocomplete/ReceivingArtworkAutocomplete.svelte';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import { goto } from '$app/navigation';
	import { navigating } from '$app/state';

	interface Props {
		dialogStores: ReturnType<typeof createDialog>;
		toArtwork: ReceivingArtwork | null;
		activitiesCount: number;
	}

	let { dialogStores, toArtwork, activitiesCount }: Props = $props();

	let activitiesCountString = $derived(
		activitiesCount === 1
			? `${activitiesCount} activity`
			: `${activitiesCount} activities`
	);

	const dataCy = 'merge-activities-success-dialog';

	const handleClickContinue = async () => {
		await goto(`${Routes.ArtworkDetails}/${toArtwork?.id}`);
		dialogStores.states.open.set(false);
	};
</script>

<Dialog {dialogStores} {dataCy} class=" p-[1.5rem]" title="Merge success">
	<Txt class="mb-8 text-center">
		Successfully merged {activitiesCountString} to artwork "{toArtwork?.title}".
	</Txt>

	<Button
		onclick={handleClickContinue}
		dataCy={`${dataCy}-continue`}
		size="md"
		variant="primary"
		fullWidth
		loading={!!navigating.complete}
	>
		Continue to artwork
	</Button>
</Dialog>
