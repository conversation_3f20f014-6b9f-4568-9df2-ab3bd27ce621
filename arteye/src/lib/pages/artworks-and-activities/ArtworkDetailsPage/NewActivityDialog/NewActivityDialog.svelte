<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { DialogContent, NewActivityDialogSteps } from './DialogContent';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';

	interface Props {
		artworkId: string;
		dataCy: string;
		dialogStores: ReturnType<typeof createDialog>;
	}

	let { artworkId, dataCy, dialogStores }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-select-new-activity`);

	const handleClose = () => {
		dialogStores.states.open.set(false);
		currentStep = NewActivityDialogSteps.NewActivity;
	};

	let currentStep = $state(
		NewActivityDialogSteps.NewActivity
	) as NewActivityDialogSteps;

	$effect(() => {
		currentStep = NewActivityDialogSteps.NewActivity;
	});

	const handleNextDialogStep = (step: NewActivityDialogSteps) => {
		currentStep = step;
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-[auto] max-h-none w-[auto] max-w-none items-center justify-center rounded p-[1.5rem] pt-[1.25rem]"
>
	<div class="flex h-[auto] flex-col items-center justify-center">
		<DialogContent
			{artworkId}
			{currentStep}
			class="mb-4"
			onNextDialogStep={handleNextDialogStep}
		/>
		<div class="flex w-full justify-start">
			{#if currentStep === NewActivityDialogSteps.NewActivity}
				<div class="flex w-full">
					<Button
						dataCy={`${dataCyPrefix}-button`}
						size="md"
						variant="secondary"
						fullWidth={true}
						onclick={handleClose}
					>
						Cancel
					</Button>
				</div>
			{:else}
				<Button
					dataCy={`${dataCyPrefix}-apply`}
					size="md"
					class="col-span-4"
					variant="tertiary"
					onclick={() =>
						handleNextDialogStep(NewActivityDialogSteps.NewActivity)}
				>
					{#snippet leading()}
						<ChevronLeftIcon />
					{/snippet}
					Back
				</Button>
			{/if}
		</div>
	</div></Dialog
>
