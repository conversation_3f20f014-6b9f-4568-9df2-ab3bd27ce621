<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { NewActivityDialogSteps, type DialogContent } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { ActivityDetailsParam } from '$lib/pages/artworks-and-activities/ActivityDetailsPage/types';
	import {
		ArtworkActivityTypeKey,
		ArtworkListingTypeKey,
	} from '$lib/types/types';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let otherArtworkActivityTypes = $derived(
		(data?.user?.dropdowns?.artworkActivityTypes || []).filter(
			(type) =>
				type.key !== ArtworkActivityTypeKey.Wanted &&
				type.key !== ArtworkActivityTypeKey.PrivateSale &&
				type.key !== ArtworkActivityTypeKey.Auction
		)
	);

	interface Props {
		artworkId: string;
		onNextDialogStep: (nextDialogStep: NewActivityDialogSteps) => void;
		currentStep: NewActivityDialogSteps;
		class?: string;
	}

	let props: Props = $props();

	const dataCy = 'new-activity-dialog-content';

	const goToCreateNewActivityPage = ({
		activityTypeKey,
		listingTypeKey,
	}: {
		activityTypeKey: ArtworkActivityTypeKey;
		listingTypeKey?: ArtworkListingTypeKey;
	}) => {
		let params = '';

		if (listingTypeKey) {
			params = `${ActivityDetailsParam.ActivityTypeKey}=${activityTypeKey}&${ActivityDetailsParam.ListingTypeKey}=${listingTypeKey}&${ActivityDetailsParam.ArtworkId}=${props.artworkId}`;
		} else {
			params = `${ActivityDetailsParam.ActivityTypeKey}=${activityTypeKey}&${ActivityDetailsParam.ArtworkId}=${props.artworkId}`;
		}

		goto(`${Routes.ActivityDetails}/new?${params}`);
	};

	let dialogContent = $derived(
		(() => ({
			[NewActivityDialogSteps.NewActivity]: {
				title: 'Select new activity type',
				description:
					'Each activity should fit into one of the following categories. You can update the status of the activity at any time.',
				options: [
					{
						title: 'Auction lot',
						description: 'Record auction lot activity including any bidding',
						onClick: () => {
							goToCreateNewActivityPage({
								activityTypeKey: ArtworkActivityTypeKey.Auction,
								listingTypeKey: ArtworkListingTypeKey.Auction,
							});
						},
					},
					{
						title: 'Private sale',
						description:
							'Record activity in fairs, exhibitions, between collectors etc.',
						onClick: () =>
							props.onNextDialogStep(NewActivityDialogSteps.PrivateSale),
					},
					{
						title: 'Other',
						description:
							'Non-listing activities inc. gifts, consignments, ownership etc.',
						onClick: () =>
							props.onNextDialogStep(NewActivityDialogSteps.NonListing),
					},
				],
			},
			[NewActivityDialogSteps.PrivateSale]: {
				title: 'Select private sale type',
				description:
					'Choose a type of private sale below. This should not be an auction or a non-listing activity.',
				options: [
					{
						title: 'Exhibition',
						description:
							'When an artwork is for sale/sold/unsold in an exhibition',
						onClick: () => {
							goToCreateNewActivityPage({
								activityTypeKey: ArtworkActivityTypeKey.PrivateSale,
								listingTypeKey: ArtworkListingTypeKey.Exhibition,
							});
						},
					},
					{
						title: 'Fair',
						description:
							'When an artwork is for sale/sold/unsold at an art fair',
						onClick: () => {
							goToCreateNewActivityPage({
								activityTypeKey: ArtworkActivityTypeKey.PrivateSale,
								listingTypeKey: ArtworkListingTypeKey.Fair,
							});
						},
					},
					{
						title: 'Gallery offering',
						description:
							'When an artwork is for sale/sold/unsold by a gallery/museum',
						onClick: () => {
							goToCreateNewActivityPage({
								activityTypeKey: ArtworkActivityTypeKey.PrivateSale,
								listingTypeKey: ArtworkListingTypeKey.Gallery,
							});
						},
					},
					{
						title: 'Other private sale',
						description:
							'Other methods of private sale activity eg. between collectors',
						onClick: () => {
							goToCreateNewActivityPage({
								activityTypeKey: ArtworkActivityTypeKey.PrivateSale,
								listingTypeKey: ArtworkListingTypeKey.Private,
							});
						},
					},
				],
			},
			[NewActivityDialogSteps.NonListing]: {
				title: 'Select non-listing activity type',
				description:
					'Choose the type of activity below. If the artwork is for sale, you should go back and select the auction or private sale option.',
				options: (otherArtworkActivityTypes || [])?.map((type) => ({
					title: type?.name,
					description: type?.description ?? '',
					onClick: () => {
						goToCreateNewActivityPage({
							activityTypeKey: type.key as ArtworkActivityTypeKey,
						});
					},
				})),
			},
		}))()
	);

	let currentStepContent = $derived(
		dialogContent[props.currentStep] as DialogContent
	);
</script>

{#if currentStepContent}
	<div
		class={twMerge(
			'flex h-full w-full flex-col items-center justify-center',
			props.class
		)}
	>
		<Txt
			variant="h3"
			class={`mb-4 mt-4 text-center ${
				currentStepContent?.options?.length > 5
					? 'max-w-[60rem]'
					: 'max-w-[30rem]'
			}`}
		>
			{currentStepContent?.title}
		</Txt>
		<Txt
			variant="body2"
			class={`mb-4 text-center ${
				currentStepContent?.options?.length > 5
					? 'max-w-[60rem]'
					: 'max-w-[30rem]'
			}`}
		>
			{currentStepContent?.description}
		</Txt>

		<div
			class={`grid gap-2 ${
				currentStepContent?.options?.length > 5
					? 'w-[60rem] grid-cols-2'
					: 'w-[30rem] grid-cols-1'
			}`}
		>
			{#each currentStepContent?.options as option}
				<button
					onclick={() => {
						if (option?.onClick) {
							option.onClick();
						}
					}}
					class={`flex cursor-pointer items-center rounded border p-3 hover:border-gray-900 ${
						currentStepContent?.options?.length > 5
							? 'col-span-1'
							: 'col-span-2'
					}`}
				>
					<div class="flex-1 min-w-0">
						<Txt
							variant="label3"
							class={`mb-1 text-left ${
								currentStepContent?.options?.length > 5
									? 'max-w-[60rem]'
									: 'max-w-[30rem]'
							}`}
						>
							{option.title}
						</Txt>
						<Tooltip
							{dataCy}
							content={option.description}
							openDelay={2000}
							class="flex"
						>
							<Txt
								variant="body3"
								class={`line-clamp-1 text-gray-500 !text-left ${
									currentStepContent?.options?.length > 5
										? 'max-w-[60rem]'
										: 'max-w-[30rem]'
								}`}
							>
								{option.description}
							</Txt>
						</Tooltip>
					</div>
					<div class="flex-shrink-0 ml-4">
						<ChevronRightIcon class="h-6 w-6" />
					</div>
				</button>
			{/each}
		</div>
	</div>
{/if}
