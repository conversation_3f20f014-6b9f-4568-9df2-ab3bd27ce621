<script lang="ts" module>
	export type CollaborationOrganisationType = NonNullable<
		NonNullable<
			NonNullable<
				NonNullable<
					ArtworkDetailsPageData['artwork']
				>['collaborator_organisations']
			>[number]
		>['organisation_id']
	> & {
		isDeleted?: boolean;
		isNew?: boolean;
	};
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { CollaborationOrganisationAutoComplete } from './CollaborationOrganisationAutoComplete';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { type GetOrganisationsQuery } from '$lib/queries/__generated__/getOrganisations.generated';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		organisation: CollaborationOrganisationType;
		selectedOption?: OptionType | null;
		onChange: (organisation: CollaborationOrganisationType) => void;
		onDelete: (organisation: CollaborationOrganisationType) => void;
		onAdd: (organisation: CollaborationOrganisationType) => void;
		showDelete?: boolean;
		class?: string;
	}

	let {
		organisation,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
		onAdd,
		showDelete,
		...rest
	}: Props = $props();

	const dataCy = 'organisation-row';

	const handleDeleteOrganisation = () => {
		onDelete(organisation);
	};

	const handleAddOrganisation = () => {
		onAdd(organisation);
	};

	const handleOrganisationChange = (e: {
		detail: {
			value: GetOrganisationsQuery['organisation'][number];
		};
	}) => {
		onChange(e.detail.value as CollaborationOrganisationType);

		return Promise.resolve();
	};
</script>

<div class={twMerge('flex items-center', rest.class)}>
	<CollaborationOrganisationAutoComplete
		class="w-full"
		bind:selectedOption
		{dataCy}
		onChange={handleOrganisationChange}
	/>
	{#if showDelete}
		<Button
			onclick={handleDeleteOrganisation}
			dataCy={`${dataCy}-delete`}
			class="ml-1 h-[2rem] w-[2rem] px-0"
			variant="secondary"
			size="xs"
		>
			<BinIcon class="h-3 w-3" />
		</Button>
	{/if}
	<Button
		onclick={handleAddOrganisation}
		dataCy={`${dataCy}-add`}
		class="ml-1 h-[2rem] w-[2rem] px-0"
		variant="secondary"
		size="xs"
	>
		<PlusIcon class="h-3 w-3" />
	</Button>
</div>
