<script lang="ts">
	import { CollaborationOrganisationRow } from './CollaborationOrganisationRow';
	import { formatCollaborationOrganisation } from './CollaborationOrganisationRow/CollaborationOrganisationAutoComplete/CollaborationOrganisationAutoComplete.svelte';
	import type { CollaborationOrganisationType } from './CollaborationOrganisationRow/CollaborationOrganisationRow.svelte';
	import type { CheckboxValue } from '$global/components/Checkbox';
	import { Checkbox } from '$global/components/Checkbox';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import type { GetOrganisationsQuery } from '$lib/queries/__generated__/getOrganisations.generated';

	interface Props {
		organisations?: CollaborationOrganisationType[];
		onChange: (organisations: CollaborationOrganisationType[]) => void;
		onChangeIsCollaboration: (checked: boolean) => void;
	}

	let {
		organisations = $bindable([]),
		onChange,
		onChangeIsCollaboration,
	}: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(organisations || []).map((organisation) => {
			if (!organisation) return null;

			return formatCollaborationOrganisation(
				organisation as GetOrganisationsQuery['organisation'][number]
			);
		})
	);

	const onOrganisationChange =
		(index: number) =>
		(selectedOrganisation: CollaborationOrganisationType) => {
			const newOrganisations = (organisations || []).map((organisation, i) =>
				i === index
					? {
							...selectedOrganisation,
							isNew: true,
						}
					: organisation
			) as CollaborationOrganisationType[];
			onChange(newOrganisations);
		};

	const handleOrganisationDelete = (
		selectedOrganisation: CollaborationOrganisationType
	) => {
		const newOrganisations = (organisations || []).map((organisation) => {
			if (organisation?.id === selectedOrganisation.id) {
				return {
					...organisation,
					isDeleted: true,
				};
			}
			return organisation;
		});

		onChange(newOrganisations);
	};

	const dataCy = 'organisations';

	const handleOrganisationAdd = () => {
		organisations = [
			...(organisations || []).map((organisation) => organisation),
			{
				isNew: true,
			},
		] as CollaborationOrganisationType[];

		selectedOptions = [...selectedOptions, null];
	};

	let isCollaboration = $derived(
		(organisations && organisations?.length > 0) ?? false
	);

	const handleToggleIsCollaboration = (checked: CheckboxValue) => {
		if (checked !== 'indeterminate') {
			onChangeIsCollaboration(checked);
			if (!checked) {
				onChange([] as CollaborationOrganisationType[]);
			}
			if (checked && organisations.length === 0) {
				onChange([
					{
						isNew: true,
					},
				] as CollaborationOrganisationType[]);
			}
		}
	};
</script>

<InputLabel dataCy={`${dataCy}-is-full-set`} variant="body3">
	<Checkbox
		dataCy={`${dataCy}-is-full-set`}
		checked={isCollaboration}
		onChange={handleToggleIsCollaboration}
	/>
	is collaboration with organisation
</InputLabel>
<div>
	{#if organisations?.length > 0}
		<InputLabel {dataCy} required variant="label3" class="mb-2 mt-4"
			>Organisations</InputLabel
		>
		{#each organisations as organisation, index}
			{#if !organisation.isDeleted}
				<CollaborationOrganisationRow
					{organisation}
					bind:selectedOption={selectedOptions[index]}
					onChange={onOrganisationChange(index)}
					onDelete={handleOrganisationDelete}
					showDelete={organisations.length > 1}
					onAdd={handleOrganisationAdd}
					class={organisations.length > 1 && organisations.length !== index + 1
						? 'mb-2'
						: ''}
				/>
			{/if}
		{/each}
	{/if}
</div>
