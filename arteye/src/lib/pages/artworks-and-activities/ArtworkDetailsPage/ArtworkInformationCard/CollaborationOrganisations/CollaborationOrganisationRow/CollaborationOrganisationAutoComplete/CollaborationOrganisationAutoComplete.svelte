<script lang="ts" module>
	export const formatCollaborationOrganisation = (
		organisation: GetOrganisationsQuery['organisation'][number]
	) => {
		const url = (() => {
			return `${Routes.Organisations}/${organisation?.id}`;
		})();

		const location = [
			organisation?.location?.name,
			organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${organisation?.name}`,
			line2: url,
			line4: `${organisation?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetOrganisationsQuery,
		GetOrganisationsQueryVariables,
	} from '$lib/queries/__generated__/getOrganisations.generated';
	import { GetOrganisationsDocument } from '$lib/queries/__generated__/getOrganisations.generated';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		class?: string;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: GetOrganisationsQuery['organisation'][number];
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search organisation',
		dataCy,
		selectedOption = $bindable(null),
		onChange,
		...rest
	}: Props = $props();
	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let organisations: GetOrganisationsQuery['organisation'] | [] = [];

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const organisation = organisations.find(
			(organisation) => organisation.id === e.detail.value.line4
		);
		if (onChange && organisation) {
			onChange({
				detail: {
					value: organisation,
				},
			});
		}
	};

	const value = writable('');

	const getVariables = (value: string): GetOrganisationsQueryVariables => {
		const filter = {
			_and: [
				{ status: { key: { _neq: 'archived' } } },
				...(() => {
					if (!value) {
						return [];
					}

					if (isValidUUID(value)) {
						return [{ id: { _eq: value } }];
					}

					if (!isNaN(+value)) {
						return [{ reference_id: { _eq: value } }];
					}

					return [
						{
							name: { _icontains: value },
						},
					];
				})(),
			],
		};

		return { limit: value.length < 3 ? 20 : -1, filter };
	};

	const getOptions = (data: GetOrganisationsQuery | undefined) => {
		setTimeout(() => {
			organisations = data?.organisation || [];
		}, 0);

		return [...(data?.organisation || []).map(formatCollaborationOrganisation)];
	};
</script>

<div
	class={classNames(
		'relative',
		{
			'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
		},
		rest.class
	)}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="organisation"
		dataCy={`${dataCy}-organisation`}
		{placeholder}
		emptyValueResponse={{ organisation: [], organisation_aggregated: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetOrganisationsDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-collaborators-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
