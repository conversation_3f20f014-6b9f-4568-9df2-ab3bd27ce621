<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import type { ArtworkDetailsForm } from '../ArtworkDetailsPage.svelte';
	import { ArtworkArtists } from './ArtworkArtists';
	import type { ArtworkArtistType } from './ArtworkArtists/ArtworkArtistRow/ArtworkArtistRow.svelte';
	import { ArtworkSeries } from './ArtworkSeries';
	import type { ArtworkSeriesType } from './ArtworkSeries/ArtworkSeriesRow/ArtworkSeriesRow.svelte';
	import { CollaborationOrganisations } from './CollaborationOrganisations';
	import type { CollaborationOrganisationType } from './CollaborationOrganisations/CollaborationOrganisationRow/CollaborationOrganisationRow.svelte';
	import { page } from '$app/state';
	import { AccordionItem } from '$global/components/Accordion';
	import type { CheckboxValue } from '$global/components/Checkbox';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import type { SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let artworkTypes = $derived(data.user?.dropdowns?.artworkTypes || []);
	let artworkDimensionTypes = $derived(
		data.user?.dropdowns?.artworkDimensionTypes || []
	);
	let heniArtworkTypes = $derived(data.user?.dropdowns?.heniArtworkTypes || []);

	let artworkTypeOptions = $derived(
		artworkTypes?.map((artworkType) => ({
			label: `${artworkType?.name}`,
			value: `${artworkType?.key}`,
		}))
	);

	let dimensionTypeOptions = $derived(
		artworkDimensionTypes?.map((artworkDimension) => ({
			label: `${artworkDimension?.name}`,
			value: `${artworkDimension?.key}`,
		}))
	);

	let heniArtworkTypeOptions = $derived(
		heniArtworkTypes?.map((heniArtworkType) => ({
			label: `${heniArtworkType?.name}`,
			value: `${heniArtworkType?.key}`,
		}))
	);

	let { artworkDetailsForm = {}, onChange, ...rest }: Props = $props();

	let selectedArtworkTypeOption = $derived(
		artworkDetailsForm?.artwork_type?.key
			? artworkDetailsForm?.artwork_type?.key
			: null
	);

	let selectedArtworkDimension = $derived(
		artworkDetailsForm?.dimensions_type?.key
			? artworkDetailsForm?.dimensions_type?.key
			: null
	);

	let selectedHeniArtworkType = $state(
		artworkDetailsForm?.heni_artwork_type?.key
			? artworkDetailsForm?.heni_artwork_type?.key
			: null
	) as string | null;

	$effect(() => {
		selectedHeniArtworkType = artworkDetailsForm?.heni_artwork_type?.key
			? artworkDetailsForm?.heni_artwork_type?.key
			: null;
	});

	interface Props {
		artworkDetailsForm: ArtworkDetailsForm;
		onChange: (activityArtwork: ArtworkDetailsForm) => void;
		class?: string;
	}

	const handleInputChange =
		(field: keyof ArtworkDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...artworkDetailsForm, [field]: value });
		};

	const handleArtistsChange = (artists: ArtworkArtistType[]) => {
		onChange({ ...artworkDetailsForm, artists });
	};

	const handleOrganisationsChange = (
		organisations: CollaborationOrganisationType[]
	) => {
		onChange({
			...artworkDetailsForm,
			collaborator_organisations: organisations,
		});
	};

	const handleSeriesChange = (series: ArtworkSeriesType) => {
		onChange({
			...artworkDetailsForm,
			series: series,
		});
	};

	const handleSeriesRemove = () => {
		onChange({
			...artworkDetailsForm,
			series: null,
		});
	};

	const handleChangeArtworkType = (event: SelectChangeEvent) => {
		const artworkType = artworkTypeOptions.find(
			(artworkType) => artworkType.value === event.detail.value
		);
		if (artworkType) {
			onChange({
				...artworkDetailsForm,
				artwork_type: {
					key: artworkType.value,
					name: artworkType.label,
				},
			});
		}
	};

	const handleChangeArtworkDimension = (event: SelectChangeEvent) => {
		const artworkDimensionType = dimensionTypeOptions.find(
			(dimensionType) => dimensionType.value === event.detail.value
		);
		if (artworkDimensionType) {
			onChange({
				...artworkDetailsForm,
				dimensions_type: {
					key: artworkDimensionType.value,
					name: artworkDimensionType.label,
				},
			});
		}
	};

	let isHeniArtwork = $state();
	$effect(() => {
		isHeniArtwork = selectedHeniArtworkType ? true : false;
	});

	const handleChangeHeniArtworkType = (event: SelectChangeEvent) => {
		selectedHeniArtworkType = event.detail.value;

		const heniArtworkType = heniArtworkTypeOptions.find(
			(heniArtworkType) => heniArtworkType.value === event.detail.value
		);

		if (heniArtworkType) {
			onChange({
				...artworkDetailsForm,
				heni_artwork_type: {
					key: heniArtworkType.value,
					name: heniArtworkType.label,
				},
			});
		}
	};

	const handleChangeIsHeniArtwork = (checked: CheckboxValue) => {
		if (!checked) {
			onChange({
				...artworkDetailsForm,
				heni_artwork_type: null,
			});
		}
		if (checked !== 'indeterminate') {
			isHeniArtwork = checked;
		}
	};

	const handleChangeIsCollaboration = (checked: CheckboxValue) => {
		if (!checked) {
			onChange({
				...artworkDetailsForm,
				collaborator_organisations: [],
			});
		}
	};

	let isBundle = $derived(artworkDetailsForm.is_bundle ?? false);
	let isFullSet = $derived(artworkDetailsForm.is_full_set ?? false);
	let isUnlimited = $derived(
		artworkDetailsForm.edition_info?.is_unlimited ?? false
	);
	let isNumbered = $derived(
		artworkDetailsForm.edition_info?.is_numbered ?? false
	);
	let isUnknown = $derived(
		artworkDetailsForm.edition_info?.is_unknown ?? false
	);

	const handleChangeIsBundle = (checked: CheckboxValue) => {
		if (checked !== 'indeterminate') {
			onChange({
				...artworkDetailsForm,
				is_bundle: checked,
			});
		}
	};
	const handleChangeIsFullSet = (checked: CheckboxValue) => {
		if (checked !== 'indeterminate') {
			onChange({
				...artworkDetailsForm,
				is_full_set: checked,
			});
		}
	};

	const handleChangeEditionInfoBooleanChange =
		(key: string) => (checked: CheckboxValue) => {
			if (checked !== 'indeterminate') {
				onChange({
					...artworkDetailsForm,
					edition_info: {
						...artworkDetailsForm.edition_info,
						[key]: checked,
					},
				} as ArtworkDetailsForm);
			}
		};

	const handleEditionInfoValueChange =
		(key: string) =>
		(e: { target: (EventTarget & { value?: string | undefined }) | null }) => {
			onChange({
				...artworkDetailsForm,
				edition_info: {
					...artworkDetailsForm.edition_info,
					[key]: e.target?.value,
				},
			} as ArtworkDetailsForm);
		};

	const dataCy = 'artwork-information-card';
</script>

<div class={twMerge('rounded border bg-gray-0 p-6 pb-6', rest.class)}>
	<Txt variant="h5" class="mb-4">Artwork information</Txt>
	<Txt variant="label3" class="mb-2 text-gray-500">Artwork details</Txt>
	<div
		class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
	>
		<div class="lg:flex gap-2">
			<div class="lg:flex-1">
				<div class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4">
					<div class="col-span-6 max-lg:overflow-x-auto">
						<ArtworkArtists
							artists={artworkDetailsForm.artists}
							onChange={handleArtistsChange}
						/>
					</div>
					<div class="col-span-3">
						<Input
							dataCy={`${dataCy}-artwork-title`}
							name="artwork-title"
							placeholder=""
							label="Artwork Title"
							value={artworkDetailsForm.title || ''}
							size="sm"
							onkeyup={handleInputChange('title')}
						/>
					</div>
					<div class="col-span-3">
						<Input
							dataCy={`${dataCy}-crid`}
							name="crid"
							placeholder=""
							label="CRID"
							value={artworkDetailsForm.crid || ''}
							size="sm"
							onkeyup={handleInputChange('crid')}
						/>
					</div>
					<div class="col-span-6">
						<Input
							dataCy={`${dataCy}-media`}
							name="media"
							placeholder=""
							label="Media"
							value={artworkDetailsForm.media || ''}
							size="sm"
							onkeyup={handleInputChange('media')}
						/>
					</div>
					<div class="col-span-3">
						<Input
							dataCy={`${dataCy}-execution-start-year`}
							name="execution-start-year"
							placeholder=""
							label="Executed start year"
							value={`${artworkDetailsForm.execution_start_year ?? ''}`}
							size="sm"
							onkeyup={handleInputChange('execution_start_year')}
						/>
					</div>
					<div class="col-span-3">
						<Input
							dataCy={`${dataCy}-execution-end-year`}
							name="execution-end-year"
							placeholder=""
							label="Executed end year"
							value={`${artworkDetailsForm.execution_end_year ?? ''}`}
							size="sm"
							onkeyup={handleInputChange('execution_end_year')}
						/>
					</div>
					<div class="col-span-3">
						<InputLabel {dataCy} variant="label3" class="mb-2"
							>Dimensions (cm)</InputLabel
						>
						<div class="grid grid-cols-3 gap-2">
							<div class="col-span-1">
								<Input
									dataCy={`${dataCy}-dimensions-height-cm`}
									name="dimensions-height-cm"
									class="mb-1"
									value={`${artworkDetailsForm.dimensions_height_cm ?? ''}`}
									size="sm"
									onkeyup={handleInputChange('dimensions_height_cm')}
								/>
								<InputLabel {dataCy} variant="label4" class="text-gray-500"
									>Height</InputLabel
								>
							</div>
							<div class="col-span-1">
								<Input
									dataCy={`${dataCy}-dimensions-width-cm`}
									name="dimensions-width-cm"
									class="mb-1"
									value={`${artworkDetailsForm.dimensions_width_cm ?? ''}`}
									size="sm"
									onkeyup={handleInputChange('dimensions_width_cm')}
								/>
								<InputLabel {dataCy} variant="label4" class="text-gray-500"
									>Width</InputLabel
								>
							</div>
							<div class="col-span-1">
								<Input
									dataCy={`${dataCy}-dimensions-depth-cm`}
									name="dimensions-depth-cm"
									class="mb-1"
									value={`${artworkDetailsForm.dimensions_depth_cm ?? ''}`}
									size="sm"
									onkeyup={handleInputChange('dimensions_depth_cm')}
								/>
								<InputLabel {dataCy} variant="label4" class="text-gray-500"
									>Depth</InputLabel
								>
							</div>
						</div>
					</div>
					<div class="col-span-3">
						<InputLabel {dataCy} class="mb-2">Artwork type</InputLabel>
						<Select
							ariaLabel="Select"
							dataCy={`${dataCy}-artwork-type`}
							name="artwork-type"
							placeholder="-Select-"
							options={artworkTypeOptions}
							value={selectedArtworkTypeOption}
							size="sm"
							onchange={handleChangeArtworkType}
						/>
					</div>
					<div class="col-span-3">
						<AccordionItem
							{dataCy}
							title="Multiple piece information"
							titleVariant="label3"
							class={twMerge('bg-white', rest.class)}
							classes={{ titleButton: 'px-0' }}
						>
							<div class="ml-2 mt-2 grid grid-cols-2 gap-2 border-l pl-3">
								<div class="col-span-1">
									<Input
										dataCy={`${dataCy}-number-of-pieces`}
										name="number-of-pieces"
										class="mb-1"
										label="Number of pieces"
										value={`${artworkDetailsForm.number_of_pieces ?? ''}`}
										size="sm"
										onkeyup={handleInputChange('number_of_pieces')}
									/>
								</div>
								<div class="col-span-1">
									<Input
										dataCy={`${dataCy}-number-of-artworks`}
										name="number-of-artworks"
										class="mb-1"
										label="Number of artworks"
										value={`${artworkDetailsForm.number_of_artworks ?? ''}`}
										size="sm"
										onkeyup={handleInputChange('number_of_artworks')}
									/>
								</div>
								<div class="col-span-2">
									<InputLabel {dataCy} class="mb-2"
										>What do the above dimensions refer to?</InputLabel
									>
									<Select
										ariaLabel="Select"
										dataCy={`${dataCy}-artwork-type`}
										name="artwork-type"
										placeholder="-Select-"
										options={dimensionTypeOptions}
										value={selectedArtworkDimension}
										size="sm"
										onchange={handleChangeArtworkDimension}
									/>
								</div>
								<div class="col-span-2">
									<div class="col-span-1 mb-2">
										<InputLabel dataCy={`${dataCy}-is-bundle`} variant="body3">
											<Checkbox
												dataCy={`${dataCy}-is-bundle`}
												onChange={handleChangeIsBundle}
												checked={isBundle}
											/>
											Is bundle
										</InputLabel>
									</div>
									<div class="col-span-1">
										<InputLabel
											dataCy={`${dataCy}-is-full-set`}
											variant="body3"
										>
											<Checkbox
												dataCy={`${dataCy}-is-full-set`}
												onChange={handleChangeIsFullSet}
												checked={isFullSet}
											/>
											Is full set
										</InputLabel>
									</div>
								</div>
							</div>
						</AccordionItem>
					</div>
					<div class="col-span-3">
						<InputLabel
							dataCy={`${dataCy}-is-heni-artwork`}
							variant="body3"
							class="mt-3"
						>
							<Checkbox
								dataCy={`${dataCy}-heni-artwork`}
								checked={!!isHeniArtwork}
								onChange={handleChangeIsHeniArtwork}
							/>
							is HENI artwork
						</InputLabel>
						<div class="col-span-3 ml-2 mt-2 border-l pl-3">
							{#if isHeniArtwork}
								<Select
									ariaLabel="Select"
									dataCy={`${dataCy}-heni-artwork-type`}
									name="heni-artwork-type"
									placeholder="-Select-"
									options={heniArtworkTypeOptions}
									value={selectedHeniArtworkType}
									size="sm"
									onchange={handleChangeHeniArtworkType}
								/>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-span-6 mb-6">
		<div class="col-span-3 lg:ml-2 mt-2 lg:border-l lg:pl-3">
			<CollaborationOrganisations
				organisations={artworkDetailsForm.collaborator_organisations}
				onChange={handleOrganisationsChange}
				onChangeIsCollaboration={handleChangeIsCollaboration}
			/>
		</div>
	</div>
	<Txt variant="label3" class="mb-2 text-gray-500">Edition details</Txt>
	<div
		class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
	>
		<div class="lg:flex gap-2">
			<div class="lg:flex-1">
				<div
					class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 lg:pb-2 mb-4 lg:mb-0"
				>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-edition-size-regular`}
							name="edition-size-regular"
							placeholder=""
							label="Edition size regular"
							value={artworkDetailsForm.edition_info?.regular_edition_size
								? `${artworkDetailsForm.edition_info?.regular_edition_size}`
								: ''}
							size="sm"
							onkeydown={handleKeyDownNumbersOnly}
							onkeyup={handleEditionInfoValueChange('regular_edition_size')}
							onchange={handleEditionInfoValueChange('regular_edition_size')}
						/>
					</div>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-edition-size-total`}
							name="edition-size-total"
							placeholder=""
							label="Edition size total"
							value={artworkDetailsForm.edition_info?.edition_size_total
								? `${artworkDetailsForm.edition_info?.edition_size_total}`
								: ''}
							size="sm"
							onkeydown={handleKeyDownNumbersOnly}
							onkeyup={handleEditionInfoValueChange('edition_size_total')}
							onchange={handleEditionInfoValueChange('edition_size_total')}
						/>
					</div>
					<div class="col-span-2"></div>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-ap-size`}
							name="ap-size"
							placeholder=""
							label="AP size"
							value={artworkDetailsForm.edition_info?.artists_proof_size
								? `${artworkDetailsForm.edition_info?.artists_proof_size}`
								: ''}
							size="sm"
							onkeydown={handleKeyDownNumbersOnly}
							onkeyup={handleEditionInfoValueChange('artists_proof_size')}
							onchange={handleEditionInfoValueChange('artists_proof_size')}
						/>
					</div>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-hc-size`}
							name="hc-size"
							placeholder=""
							label="HC size"
							value={artworkDetailsForm.edition_info?.hors_de_commerce_size
								? `${artworkDetailsForm.edition_info?.hors_de_commerce_size}`
								: ''}
							size="sm"
							onkeyup={handleEditionInfoValueChange('hors_de_commerce_size')}
						/>
					</div>
					<div class="col-span-2">
						<Input
							dataCy={`${dataCy}-gp-size`}
							name="gp-size"
							placeholder=""
							label="GP size"
							value={artworkDetailsForm.edition_info?.general_proof_size
								? `${artworkDetailsForm.edition_info?.general_proof_size}`
								: ''}
							size="sm"
							onkeyup={handleEditionInfoValueChange('general_proof_size')}
						/>
					</div>
				</div>
				<div
					class="flex flex-col lg:grid grid-cols-6 gap-4 lg:p-4 mb-4 lg:mb-0"
				>
					<div class="col-span-2">
						<InputLabel dataCy={`${dataCy}-is-unlimited`} variant="body3">
							<Checkbox
								dataCy={`${dataCy}-is-unlimited`}
								checked={isUnlimited}
								onChange={handleChangeEditionInfoBooleanChange('is_unlimited')}
							/>
							Unlimited
						</InputLabel>
					</div>
					<div class="col-span-2">
						<InputLabel dataCy={`${dataCy}-is-numbered`} variant="body3">
							<Checkbox
								dataCy={`${dataCy}-is-numbered`}
								checked={isNumbered}
								onChange={handleChangeEditionInfoBooleanChange('is_numbered')}
							/>
							Numbered
						</InputLabel>
					</div>
					<div class="col-span-2">
						<InputLabel dataCy={`${dataCy}-is-unknown`} variant="body3">
							<Checkbox
								dataCy={`${dataCy}-is-unknown`}
								checked={isUnknown}
								onChange={handleChangeEditionInfoBooleanChange('is_unknown')}
							/>
							Edition size unknown
						</InputLabel>
					</div>
				</div>
			</div>

			<div class="lg:flex w-[24px] flex-col justify-end pb-1">
				<UpdateHistoryTooltip updateHistory={artworkDetailsForm.edition_info} />
			</div>
		</div>
	</div>
	<Txt variant="label3" class="mb-2 text-gray-500">Series details</Txt>
	<div
		class="mb-6 rounded-bl-md lg:border-b lg:border-l lg:border-gray-200 bg-white"
	>
		<div class="lg:flex gap-2">
			<div class="max-w-[calc(100%-32px)] lg:flex-1 lg:p-4 mb-4 lg:mb-0">
				<ArtworkSeries
					onRemoveSelectedOption={handleSeriesRemove}
					onChange={handleSeriesChange}
					series={artworkDetailsForm.series}
				/>
			</div>

			<div class="lg:flex w-[24px] flex-col justify-end pb-1">
				<UpdateHistoryTooltip updateHistory={artworkDetailsForm.series} />
			</div>
		</div>
	</div>
</div>
