<script lang="ts">
	import classNames from 'classnames';
	import { onMount } from 'svelte';
	import { ArtworkArtistRow } from './ArtworkArtistRow';
	import { formatArtworkArtist } from './ArtworkArtistRow/ArtworkArtistAutoComplete/ArtworkArtistAutoComplete.svelte';
	import type { ArtworkArtistType } from './ArtworkArtistRow/ArtworkArtistRow.svelte';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isLastItem } from '$global/utils/isLastItem';
	import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import type { GetArtistsQuery } from '$lib/queries/__generated__/getArtists.generated';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';

	interface Props {
		artists?: ArtworkArtistType[];
		onChange: (artists: ArtworkArtistType[]) => void;
	}

	let { artists = $bindable([]), onChange }: Props = $props();
	let loadingLegacyIds = $state(false);

	let selectedOptions: (OptionType | null)[] = $state(
		(artists || []).map((artist) => {
			if (!artist) return null;

			return formatArtworkArtist(artist as GetArtistsQuery['artist'][number]);
		})
	);

	onMount(() => {
		if (page.params.id === 'new') {
			handleArtistAdd();
		}
	});

	// onMount(() => {
	// 	const setArtists = async () => {
	// 		if (!isOnDev()) {
	// 			const legacyIdRes = await Promise.all(
	// 				(artists || []).map((artist) =>
	// 					gqlClientCustom.request(
	// 						GetLegacyIdDocument,
	// 						{ id: artist?.id, collection: 'artist' },
	// 						getAuthorizationHeaders(
	// 							page.data as { user: { access_token: string } }
	// 						)
	// 					)
	// 				)
	// 			);

	// 			legacyIdRes.forEach((legacyIdR, i) => {
	// 				const legacyId = legacyIdR?.getLegacyId?.legacyId;
	// 				if (legacyId) {
	// 					selectedOptions[i] = formatArtworkArtist({
	// 						...artists[i],
	// 						legacy_id: legacyId,
	// 					});
	// 				}
	// 			});
	// 		}

	// 		loadingLegacyIds = false;
	// 	};

	// 	setArtists();
	// });

	const onArtistChange =
		(index: number) => (selectedArtist: ArtworkArtistType) => {
			if (artists.length === 0) {
				artists = [
					{
						isNew: true,
					},
				] as ArtworkArtistType[];
			}
			const newArtists = (artists || []).map((artist, i) =>
				i === index
					? {
							...selectedArtist,
							isNew: true,
						}
					: artist
			) as ArtworkArtistType[];
			onChange(newArtists);
		};

	const handleArtistDelete = (selectedArtist: ArtworkArtistType) => {
		const newArtists = (artists || []).map((artist) => {
			if (artist?.id === selectedArtist.id) {
				return {
					...artist,
					isDeleted: true,
				};
			}
			return artist;
		});

		onChange(newArtists);
	};

	const dataCy = 'artists';

	const handleArtistAdd = () => {
		artists = [
			...(artists || []).map((artist) => artist),
			{
				isNew: true,
			},
		] as ArtworkArtistType[];

		selectedOptions = [...selectedOptions, null];
	};
</script>

<div
	class={classNames('min-w-[700px]', {
		'pointer-events-none': loadingLegacyIds,
	})}
>
	<InputLabel {dataCy} required variant="label3" class="mb-2"
		>Artists</InputLabel
	>

	{#if artists?.filter((artists) => !artists.isDeleted).length > 0}
		{#each artists as artist, index}
			{#if !artist?.isDeleted}
				<ArtworkArtistRow
					{artist}
					bind:selectedOption={selectedOptions[index]}
					onChange={onArtistChange(index)}
					onDelete={handleArtistDelete}
					showAdd={isLastItem(artists, index)}
					onAdd={handleArtistAdd}
					class={artists?.length > 1 && artists.length !== index + 1
						? 'mb-2'
						: ''}
				/>
			{/if}
		{/each}
	{:else}
		<div class="end flex">
			<Button
				onclick={handleArtistAdd}
				dataCy={`${dataCy}-add`}
				class="ml-1 h-[2rem] w-[2rem] px-0"
				variant="secondary"
				size="xs"
			>
				<PlusIcon class="h-3 w-3" />
			</Button>
		</div>
	{/if}
</div>
