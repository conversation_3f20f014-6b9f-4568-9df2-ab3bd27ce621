<script lang="ts" module>
	const UNKNOWN_ARTIST_NAME = 'Unknown artist';
	const NO_ARTIST_NAME = 'no artist';

	export const formatArtworkArtist = (
		artist: GetArtistsQuery['artist'][number] & { legacy_id?: string | null }
	) => {
		const url = `${Routes.Artists}/${artist?.id}`;

		const nationalities = (artist?.person?.nationalities || [])
			.map((nationality) => nationality?.country?.country_nationality)
			.filter(Boolean)
			.join(', ');

		const birthYear = artist?.person?.year_birth
			? `b.${artist.person?.year_birth}`
			: '';
		const deathYear = artist?.person?.year_death
			? `d.${artist.person?.year_death}`
			: '';
		const yearsInfo = [birthYear, deathYear].filter(Boolean).join(', ');

		const bioInfo = [nationalities, yearsInfo].filter(Boolean).join(' ');
		const bioInfoFormatted = bioInfo ? `(${bioInfo})` : '';

		const artworksCount = artist.aggregations?.[0]?.artwork_count || 0;
		const artworksInfo = artworksCount ? `${artworksCount} artworks` : '';

		const line1Suffix = [bioInfoFormatted, artworksInfo]
			.filter(Boolean)
			.join(', ');

		return {
			line1: artist?.person?.entity?.name,
			line2: url,
			line4: `${artist?.id}`,
			line1Suffix: line1Suffix,
			linkTitle: 'ARTWORKS',
			linkHref: `${Routes.ArtworksAndActivities}?showResults=true&name=${artist?.id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`,
			...(artist?.reference_id && {
				line5: `Ref ID: ${artist?.reference_id}`,
			}),
			// ...(artist?.legacy_id && {
			// 	line6: `Legacy ID: ${artist?.legacy_id}`,
			// }),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';

	import {
		GetArtistsDocument,
		type GetArtistsQuery,
		type GetArtistsQueryVariables,
	} from '$lib/queries/__generated__/getArtists.generated';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		class?: string;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: GetArtistsQuery['artist'][number];
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search artist',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
		...rest
	}: Props = $props();
	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let entities: GetArtistsQuery['artist'] | [] = $state([]);

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const artist = entities.find(
			(artist) => artist.id === e.detail.value.line4
		);
		if (onChange && artist) {
			onChange({
				detail: {
					value: artist,
				},
			});
		}
	};

	const value = writable('');

	const getVariables = (value: string): GetArtistsQueryVariables => {
		const filter: GetArtistsQueryVariables['filter'] = {
			_and: [
				{ status: { key: { _neq: Status_Enum.Archived } } },
				...(() => {
					if (!value) {
						return [];
					}

					if (isValidUUID(value)) {
						return [{ id: { _eq: value } }];
					}

					if (!isNaN(+value)) {
						return [{ reference_id: { _eq: value } }];
					}

					return [
						{
							person: {
								entity: {
									name: {
										_icontains: value,
									},
								},
							},
						},
					];
				})(),
			],
		};

		return { limit: value.length < 3 ? 20 : -1, filter };
	};

	const getOptions = async (value: string) => {
		const optionsRes = await gqlClient.request(
			GetArtistsDocument,
			getVariables(value),
			getAuthorizationHeaders(data)
		);

		setTimeout(() => {
			entities = optionsRes?.artist || [];
		}, 0);

		return (optionsRes?.artist || [])
			.map((artist) => formatArtworkArtist(artist))
			.sort((a, b) => (a.line1?.length || 0) - (b.line1?.length || 0));

		// if (isOnDev()) {
		// 	return (optionsRes?.artist || []).map((artist) =>
		// 		formatArtworkArtist(artist)
		// 	);
		// }

		// const legacyIdRes = await Promise.all(
		// 	(optionsRes?.artist || []).map((artist) =>
		// 		gqlClientCustom.request(
		// 			GetLegacyIdDocument,
		// 			{ id: artist.id, collection: 'artist' },
		// 			getAuthorizationHeaders(data)
		// 		)
		// 	)
		// );

		// return (optionsRes?.artist || []).map((artist, i) =>
		// 	formatArtworkArtist({
		// 		...artist,
		// 		legacy_id: legacyIdRes[i]?.getLegacyId?.legacyId,
		// 	})
		// );
	};
</script>

<div
	class={classNames(
		'relative',
		{
			'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
		},
		rest.class
	)}
>
	<PromiseAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="artist"
		dataCy={`${dataCy}-artist`}
		{placeholder}
		showResultsWhenEmpty={false}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({
			'max-w-[calc(100%-20px)] flex-grow': !!selectedOption,
		})}
		{getOptions}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-artist-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</PromiseAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				value.set('');
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
