<script lang="ts" module>
	export type ArtworkArtistType = NonNullable<
		NonNullable<
			NonNullable<
				NonNullable<ArtworkDetailsPageData['artwork']>['artists']
			>[number]
		>['artist_id']
	> & {
		isDeleted?: boolean;
		isNew?: boolean;
	};
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { ArtworkArtistAutoComplete } from './ArtworkArtistAutoComplete';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { type GetArtistsQuery } from '$lib/queries/__generated__/getArtists.generated';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		artist: ArtworkArtistType;
		selectedOption?: OptionType | null;
		onChange: (artist: ArtworkArtistType) => void;
		onDelete: (artist: ArtworkArtistType) => void;
		onAdd: (artist: ArtworkArtistType) => void;
		showAdd?: boolean;
		class?: string;
	}

	let {
		artist,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
		onAdd,
		showAdd,
		...rest
	}: Props = $props();

	const dataCy = 'artist-row';

	const handleDeleteArtist = () => {
		onDelete(artist);
	};

	const handleAddArtist = () => {
		onAdd(artist);
	};

	const handleArtistChange = (e: {
		detail: {
			value: GetArtistsQuery['artist'][number];
		};
	}) => {
		onChange(e.detail.value);

		return Promise.resolve();
	};
</script>

<div
	class={twMerge(
		'flex items-center justify-between w-full gap-0.5',
		rest.class
	)}
>
	<ArtworkArtistAutoComplete
		class="w-full"
		bind:selectedOption
		{dataCy}
		onChange={handleArtistChange}
	/>

	<div class="flex items-center gap-0.5">
		<Button
			onclick={handleDeleteArtist}
			dataCy={`${dataCy}-delete`}
			class="ml-1 h-[2rem] w-[2rem] px-0"
			variant="secondary"
			size="xs"
		>
			<BinIcon class="h-3 w-3" />
		</Button>

		{#if showAdd}
			<Button
				onclick={handleAddArtist}
				dataCy={`${dataCy}-add`}
				class="ml-1 h-[2rem] w-[2rem] px-0"
				variant="secondary"
				size="xs"
			>
				<PlusIcon class="h-3 w-3" />
			</Button>
		{/if}
	</div>
</div>
