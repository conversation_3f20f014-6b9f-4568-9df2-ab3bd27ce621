<script lang="ts">
	import { ArtworkSeriesRow } from './ArtworkSeriesRow';
	import { formatArtworkSeries } from './ArtworkSeriesRow/ArtworkSeriesAutoComplete/ArtworkSeriesAutoComplete.svelte';
	import type { ArtworkSeriesType } from './ArtworkSeriesRow/ArtworkSeriesRow.svelte';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import type { GetArtworkSeriesQuery } from '$lib/queries/__generated__/getArtworkSeries.generated';

	interface Props {
		series?: ArtworkSeriesType | null | undefined;
		onRemoveSelectedOption: () => void;
		onChange: (series: ArtworkSeriesType) => void;
	}

	let {
		series = undefined,
		onRemoveSelectedOption,
		onChange,
	}: Props = $props();

	let selectedOption: OptionType | null = $state(
		series
			? formatArtworkSeries(
					series as GetArtworkSeriesQuery['artwork_series'][number]
				)
			: null
	);

	const onSeriesChange = (selectedSeries: ArtworkSeriesType) => {
		const newSeries = {
			...selectedSeries,
			isNew: true,
		} as ArtworkSeriesType;

		onChange(newSeries);
	};

	const dataCy = 'series';
</script>

<div>
	<InputLabel {dataCy} variant="label3" class="mb-2">Series</InputLabel>

	<ArtworkSeriesRow
		{onRemoveSelectedOption}
		bind:selectedOption
		onChange={onSeriesChange}
	/>
</div>
