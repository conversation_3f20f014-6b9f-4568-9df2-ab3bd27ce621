<script lang="ts" module>
	export type ArtworkSeriesType = NonNullable<
		NonNullable<ArtworkDetailsPageData['artwork']>['series']
	> & {
		isDeleted?: boolean;
		isNew?: boolean;
	};
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { ArtworkSeriesAutoComplete } from './ArtworkSeriesAutoComplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import type { GetArtworkSeriesQuery } from '$lib/queries/__generated__/getArtworkSeries.generated';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		selectedOption?: OptionType | null;
		onRemoveSelectedOption: () => void;
		onChange: (series: ArtworkSeriesType) => void;
		class?: string;
	}

	let {
		selectedOption = $bindable(null),
		onRemoveSelectedOption,
		onChange,
		...rest
	}: Props = $props();

	const dataCy = 'series-row';

	const handleArtistChange = (e: {
		detail: {
			value: GetArtworkSeriesQuery['artwork_series'][number];
		};
	}) => {
		onChange(e.detail.value);

		return Promise.resolve();
	};
</script>

<div class={twMerge('flex items-center', rest.class)}>
	<ArtworkSeriesAutoComplete
		{onRemoveSelectedOption}
		bind:selectedOption
		{dataCy}
		onChange={handleArtistChange}
	/>
</div>
