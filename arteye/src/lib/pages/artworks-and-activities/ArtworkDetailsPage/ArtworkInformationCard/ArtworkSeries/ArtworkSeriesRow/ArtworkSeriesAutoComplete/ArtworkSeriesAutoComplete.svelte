<script lang="ts" module>
	export const formatArtworkSeries = (
		series: GetArtworkSeriesQuery['artwork_series'][number]
	) => {
		const url = `${Routes.Series}/${series?.id}`;

		const artistsInfo = (series?.artists || [])
			.map((artist) => {
				const person = artist?.artist_id?.person;

				if (!person) return '';

				const info = (person?.nationalities || [])
					.map((nationality) => nationality?.country?.country_nationality)
					.concat(person?.year_birth ? [`b.${person.year_birth}`] : [])
					.filter(Boolean)
					.join(', ');

				return `${person?.entity?.name}${info ? ` (${info})` : ''}`;
			})
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${series?.title}`,
			line2: url,
			line4: `${series?.id}`,
			line1Suffix: artistsInfo,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';

	import {
		GetArtworkSeriesDocument,
		type GetArtworkSeriesQuery,
		type GetArtworkSeriesQueryVariables,
	} from '$lib/queries/__generated__/getArtworkSeries.generated';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	interface Props {
		onRemoveSelectedOption: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		class?: string;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: GetArtworkSeriesQuery['artwork_series'][number];
					};
			  }) => Promise<void>);
	}

	let {
		onRemoveSelectedOption,
		placeholder = 'Search series',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
		...rest
	}: Props = $props();
	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));
	let entities: GetArtworkSeriesQuery['artwork_series'] | [] = $state([]);

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const series = entities.find(
			(series) => series.id === e.detail.value.line4
		);
		if (onChange && series) {
			onChange({
				detail: {
					value: series,
				},
			});
		}
	};

	const value = writable('');

	const getVariables = (value: string): GetArtworkSeriesQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: {
				_and: [
					{ status: { key: { _neq: Status_Enum.Archived } } },
					{
						_or: [
							{
								title: { _icontains: value },
							},
							{
								crid: { _icontains: value },
							},
						],
					},
				],
			},
		};
	};

	const getOptions = (data: GetArtworkSeriesQuery | undefined) => {
		setTimeout(() => {
			entities = data?.artwork_series || [];
		}, 0);

		return [...(data?.artwork_series || []).map(formatArtworkSeries)];
	};
</script>

<div
	class={classNames(
		'relative min-w-[66%]',
		{
			'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
				!!selectedOption,
		},
		rest.class
	)}
>
	<QueryAutocomplete
		size="sm"
		{onRemoveSelectedOption}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="series"
		dataCy={`${dataCy}-series`}
		{placeholder}
		emptyValueResponse={{ artwork_series: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'overflow-visible',
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				line1: 'overflow-visible',
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetArtworkSeriesDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults class="text-left" dataCy={`${dataCy}-series-autocomplete`}
				>No series found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>
</div>
{#if !!selectedOption}
	<Button
		onclick={() => {
			selectedOption = null;
			onRemoveSelectedOption();
		}}
		dataCy={`${dataCy}-delete`}
		class="ml-1 min-h-[36px] min-w-[36px] px-0"
		variant="secondary"
		size="xs"
	>
		<BinIcon class="h-3 w-3" />
	</Button>
{/if}
