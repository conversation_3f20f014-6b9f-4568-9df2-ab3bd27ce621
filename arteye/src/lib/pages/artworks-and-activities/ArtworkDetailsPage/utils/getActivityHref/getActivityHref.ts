import { page } from '$app/state';
import { Routes } from '$lib/constants/routes';

export const getActivityHref = ({
	artworkActivityId,
	token,
}: {
	artworkActivityId: string;
	token: string;
}) => {
	const detailsPageParams = page.url.searchParams;
	detailsPageParams.set('paginationToken', token);
	detailsPageParams.delete('paginationDirection');
	detailsPageParams.delete('showResults');

	return `${Routes.ActivityDetails}/${artworkActivityId}?${detailsPageParams.toString()}`;
};
