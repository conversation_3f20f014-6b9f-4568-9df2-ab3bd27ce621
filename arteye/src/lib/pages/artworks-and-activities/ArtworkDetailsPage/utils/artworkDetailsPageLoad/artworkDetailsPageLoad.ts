import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getQueryKeys } from '$global/query-utils/getQueryKeys';
import { Status_Enum } from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import {
	GetArtworkDocument,
	type GetArtworkQuery,
} from '$lib/queries/__generated__/getArtwork.generated';
import {
	GetArtworkPageArtworksActivitiesDocument,
	type GetArtworkPageArtworksActivitiesQuery,
} from '$lib/queries/__generated__/getArtworkPageArtworksActivities.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { ArtworkDetailsPageLoadEvent } from '$routes/artworks-and-activities/artwork/[id]/types';

export const artworkDetailsPageLoad = async ({
	data,
	parent,
	params,
}: ArtworkDetailsPageLoadEvent) => {
	const parentData = await parent();
	const queryClient = parentData.queryClient;
	const artworkId = params?.id;
	const authHeaders = getAuthorizationHeaders(parentData);

	const artworksReq =
		artworkId === PARAM_NEW
			? {
					artwork: [] as GetArtworkQuery['artwork'],
				}
			: gqlClient.request(
					GetArtworkDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: artworkId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const activitiesVariables = {
		filter: {
			_and: [
				{ status: { key: { _neq: Status_Enum.Archived } } },
				{
					artworks: {
						artwork: {
							id: {
								_eq: artworkId,
							},
						},
					},
				},
			],
		},
		limit: TABLE_PAGE_SIZE,
		offset: 0,
		sort: ['-timestamp'],
	};

	const activitiesReq =
		artworkId === PARAM_NEW
			? ({
					artwork_activity: [],
					artwork_activity_aggregated: [{ count: { id: 0 } }],
				} as GetArtworkPageArtworksActivitiesQuery)
			: gqlClient.request(
					GetArtworkPageArtworksActivitiesDocument,
					activitiesVariables,
					authHeaders
				);

	const legacyIdReq =
		artworkId === PARAM_NEW || isOnDev()
			? { getLegacyId: { legacyId: '' } }
			: gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'artwork',
						id: artworkId,
					},
					authHeaders
				);

	const pipelineSourceReq =
		artworkId === PARAM_NEW
			? {
					getDatasource: { data_source: null },
				}
			: gqlClientCustom.request(
					GetDatasourceDocument,
					{
						collection: PipelineSourceItem.Artwork,
						id: artworkId,
					},
					authHeaders
				);

	const [artworksRes, activitiesRes, legacyIdRes, pipelineSourceRes] =
		await Promise.all([
			artworksReq,
			activitiesReq,
			legacyIdReq,
			pipelineSourceReq,
		]);

	queryClient.setQueryData(
		getQueryKeys(GetArtworkPageArtworksActivitiesDocument, activitiesVariables),
		activitiesRes
	);

	const resultsCount =
		activitiesRes.artwork_activity_aggregated?.[0].count?.id || 0;
	const legacyId = legacyIdRes?.getLegacyId?.legacyId;
	const artwork = artworksRes?.artwork[0];
	const pipelineSource = pipelineSourceRes.getDatasource?.data_source;

	if (!artwork?.id && artworkId !== PARAM_NEW) {
		error(404, 'Artwork cannot be found');
	}

	return {
		...parentData,
		legacyId,
		artwork,
		pipelineSource,
		activitiesRes,
		resultsCount,
		activitiesVariables,
	};
};
