import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import type { GetArtworkQuery } from '$lib/queries/__generated__/getArtwork.generated';
import { GetArtworkDocument } from '$lib/queries/__generated__/getArtwork.generated';
import { GetArtworkActivityTypesDocument } from '$lib/queries/__generated__/getArtworkActivityTypes.generated';
import { GetArtworkDimensionTypesDocument } from '$lib/queries/__generated__/getArtworkDimensionTypes.generated';
import {
	GetArtworkPageArtworksActivitiesDocument,
	type GetArtworkPageArtworksActivitiesQuery,
} from '$lib/queries/__generated__/getArtworkPageArtworksActivities.generated';
import { GetArtworkTypesDocument } from '$lib/queries/__generated__/getArtworkTypes.generated';
import { GetHeniArtworkTypesDocument } from '$lib/queries/__generated__/getHeniArtworkTypes.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { ArtworkDetailsPageServerLoadEvent } from '$routes/artworks-and-activities/artwork/[id]/types';

export const artworkDetailsPageServerLoad = async ({
	parent,
	params,
}: ArtworkDetailsPageServerLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();

	const artworkId = params?.id;

	const authHeaders = getAuthorizationHeaders(data);

	const artworksReq =
		artworkId === PARAM_NEW
			? {
					artwork: [] as GetArtworkQuery['artwork'],
				}
			: gqlClient.request(
					GetArtworkDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: artworkId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const activitiesVariables = {
		filter: {
			_and: [
				{ status: { key: { _neq: Status_Enum.Archived } } },
				{
					artworks: {
						artwork: {
							id: {
								_eq: artworkId,
							},
						},
					},
				},
			],
		},
		limit: TABLE_PAGE_SIZE,
		offset: 0,
		sort: ['-timestamp'],
	};

	const activitiesReq =
		artworkId === PARAM_NEW
			? ({
					artwork_activity: [],
					artwork_activity_aggregated: [{ count: { id: 0 } }],
				} as GetArtworkPageArtworksActivitiesQuery)
			: gqlClient.request(
					GetArtworkPageArtworksActivitiesDocument,
					activitiesVariables,
					authHeaders
				);

	const artworkTypeReq = gqlClient.request(
		GetArtworkTypesDocument,
		{},
		authHeaders
	);

	const artworkDimensionReq = gqlClient.request(
		GetArtworkDimensionTypesDocument,
		{},
		authHeaders
	);

	const heniArtworkTypesReq = gqlClient.request(
		GetHeniArtworkTypesDocument,
		{},
		authHeaders
	);

	// const artworkActivityTypesReq = gqlClient.request(
	// 	GetArtworkActivityTypesDocument,
	// 	{},
	// 	authHeaders
	// );

	const legacyIdReq =
		artworkId === PARAM_NEW || isOnDev()
			? { getLegacyId: { legacyId: '' } }
			: gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'artwork',
						id: artworkId,
					},
					getAuthorizationHeaders(data)
				);

	const pipelineSourceReq =
		artworkId === PARAM_NEW
			? {
					getDatasource: { data_source: null },
				}
			: gqlClientCustom.request(
					GetDatasourceDocument,
					{
						collection: PipelineSourceItem.Artwork,
						id: artworkId,
					},
					getAuthorizationHeaders(data)
				);

	const [
		artworksRes,
		activitiesRes,
		artworkTypeRes,
		artworkDimensionRes,
		heniArtworkTypesRes,
		// artworkActivityTypesRes,
		legacyIdRes,
		pipelineSourceRes,
	] = await Promise.all([
		artworksReq,
		activitiesReq,
		artworkTypeReq,
		artworkDimensionReq,
		heniArtworkTypesReq,
		// artworkActivityTypesReq,
		legacyIdReq,
		pipelineSourceReq,
	]);

	const resultsCount =
		activitiesRes.artwork_activity_aggregated?.[0].count?.id || 0;
	const legacyId = legacyIdRes?.getLegacyId?.legacyId;
	const artwork = artworksRes?.artwork[0];
	const artworkTypes = artworkTypeRes?.artwork_type || [];
	const artworkDimensionTypes =
		artworkDimensionRes?.artwork_dimension_type || [];
	const heniArtworkTypes = heniArtworkTypesRes?.heni_artwork_type || [];
	// const artworkActivityTypes =
	// 	artworkActivityTypesRes?.artwork_activity_type || [];
	const pipelineSource = pipelineSourceRes.getDatasource?.data_source;

	if (!artwork?.id && artworkId !== PARAM_NEW) {
		error(404, 'Artwork cannot be found');
	}

	return {
		...data,
		legacyId,
		artwork,
		artworkTypes,
		artworkDimensionTypes,
		heniArtworkTypes,
		// artworkActivityTypes,
		pipelineSource,
		activitiesRes,
		resultsCount,
		activitiesVariables,
	};
};
