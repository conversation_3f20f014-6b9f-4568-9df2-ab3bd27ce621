import type { Artwork_Activity } from '$gql/types';

const getInitials = (name?: string) => {
	if (!name) return '';
	const namesArray = name.split(' ');
	const initials = namesArray
		.map((n) => n[0])
		.slice(0, 2)
		.join('');
	return initials.toUpperCase();
};

export interface AssociatedEntityChip {
	name: string;
	initials: string;
	type: string;
}
export const isAssociatedEntityChip = (
	entity: string | AssociatedEntityChip[]
): entity is AssociatedEntityChip[] => {
	return (
		Array.isArray(entity) &&
		entity.every(
			(item) =>
				typeof item.name === 'string' && typeof item.initials === 'string'
		)
	);
};

export const getAssociatedEntitiesChip = (
	artworkActivity: Artwork_Activity
) => {
	return artworkActivity?.associations?.map((entity) => ({
		name: entity?.entity?.name,
		initials: getInitials(entity?.entity?.name),
		type: entity?.type?.name,
	})) as AssociatedEntityChip[];
};
