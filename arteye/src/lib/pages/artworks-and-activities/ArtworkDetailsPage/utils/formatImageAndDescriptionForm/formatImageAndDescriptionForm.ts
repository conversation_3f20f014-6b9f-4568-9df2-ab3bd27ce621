import { Tag_Type_Enum } from '$gql/types-custom';
import type { ImageAndDescriptionForm } from '$lib/components/details-pages/ImageDescriptionAndTags';
import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

export const formatImageAndDescriptionForm = (
	artwork: ArtworkDetailsPageData['artwork']
): ImageAndDescriptionForm => {
	return {
		description: artwork?.description || '',
		categoryTags: (artwork?.tags || [])
			.filter(
				(category) => category?.tag_tag?.type?.key === Tag_Type_Enum.Category
			)
			.map((category) => ({
				value: category?.tag_tag?.tag || '',
				label: category?.tag_tag?.tag || '',
			}))
			.filter(
				(category, pos, arr) =>
					arr.findIndex((cat) => cat.value === category.value) === pos
			),
		subjectTags: (artwork?.tags || [])
			.filter(
				(category) => category?.tag_tag?.type?.key === Tag_Type_Enum.Subject
			)
			.map((category) => ({
				value: category?.tag_tag?.tag || '',
				label: category?.tag_tag?.tag || '',
			}))
			.filter(
				(category, pos, arr) =>
					arr.findIndex((cat) => cat.value === category.value) === pos
			),
	};
};
