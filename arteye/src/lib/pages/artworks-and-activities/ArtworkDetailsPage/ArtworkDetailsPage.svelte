<script lang="ts" module>
	export type ArtworkDetailsForm = Partial<
		Omit<
			Pick<
				NonNullable<ArtworkDetailsPageData['artwork']>,
				| 'id'
				| 'title'
				| 'crid'
				| 'media'
				| 'execution_start_year'
				| 'execution_end_year'
				| 'dimensions_depth_cm'
				| 'dimensions_height_cm'
				| 'dimensions_width_cm'
				| 'number_of_pieces'
				| 'number_of_artworks'
				| 'artwork_type'
				| 'dimensions_type'
				| 'is_bundle'
				| 'is_full_set'
				| 'heni_artwork_type'
				| 'edition_info'
				| 'series'
				| 'status'
			>,
			'artists' | 'collaborator_organisations'
		> & {
			collaborator_organisations: CollaborationOrganisationType[];
			artists: ArtworkArtistType[];
		}
	>;

	export type ArtworkImageType =
		| (NonNullable<
				NonNullable<ArtworkDetailsPageData['artwork']>['primary_image']
		  > & { file?: File; url?: string | null })
		| null
		| undefined;
</script>

<script lang="ts">
	import { ArtworkActivity } from './ArtworkActivity';
	import { ArtworkInformationCard } from './ArtworkInformationCard';
	import type { ArtworkArtistType } from './ArtworkInformationCard/ArtworkArtists/ArtworkArtistRow/ArtworkArtistRow.svelte';
	import type { CollaborationOrganisationType } from './ArtworkInformationCard/CollaborationOrganisations/CollaborationOrganisationRow/CollaborationOrganisationRow.svelte';
	import { TopSection } from './TopSection';
	import { formatImageAndDescriptionForm } from './utils/formatImageAndDescriptionForm/formatImageAndDescriptionForm';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import type { ImageAndDescriptionForm } from '$lib/components/details-pages/ImageDescriptionAndTags';
	import { ImageDescriptionAndTags } from '$lib/components/details-pages/ImageDescriptionAndTags';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { UpdateAndReturnArtworkDocument } from '$lib/custom-queries/__generated__/updateAndReturnArtwork.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import { mutateArtwork } from '$lib/utils/mutation-handlers/mutateArtwork/mutateArtworkActivityArtwork';
	import type { ArtworkDetailsPageData } from '$routes/artworks-and-activities/artwork/[id]/types';

	let loading = $state(false);
	let showSaveBar = $state(false);

	let data = $derived(getPageData<ArtworkDetailsPageData>(page.data));

	let artwork = $derived(data.artwork);

	let artworkDetailsForm = $state(
		(() => ({
			id: artwork?.id ?? '',
			artists: artwork?.artists?.map((artist) => artist?.artist_id),
			title: artwork?.title,
			crid: artwork?.crid,
			media: artwork?.media,
			execution_start_year: artwork?.execution_start_year,
			execution_end_year: artwork?.execution_end_year,
			dimensions_depth_cm: artwork?.dimensions_depth_cm,
			dimensions_height_cm: artwork?.dimensions_height_cm,
			dimensions_width_cm: artwork?.dimensions_width_cm,
			number_of_pieces: artwork?.number_of_pieces,
			number_of_artworks: artwork?.number_of_artworks,
			dimensions_type: artwork?.dimensions_type,
			is_bundle: artwork?.is_bundle,
			is_full_set: artwork?.is_full_set,
			artwork_type: artwork?.artwork_type,
			heni_artwork_type: artwork?.heni_artwork_type,
			collaborator_organisations: artwork?.collaborator_organisations?.map(
				(organisation) => organisation?.organisation_id
			),
			edition_info: artwork?.edition_info,
			series: artwork?.series,
			status: artwork?.status,
		}))() as ArtworkDetailsForm
	);

	$effect(() => {
		artworkDetailsForm = {
			id: artwork?.id ?? '',
			artists: artwork?.artists?.map((artist) => artist?.artist_id),
			title: artwork?.title,
			crid: artwork?.crid,
			media: artwork?.media,
			execution_start_year: artwork?.execution_start_year,
			execution_end_year: artwork?.execution_end_year,
			dimensions_depth_cm: artwork?.dimensions_depth_cm,
			dimensions_height_cm: artwork?.dimensions_height_cm,
			dimensions_width_cm: artwork?.dimensions_width_cm,
			number_of_pieces: artwork?.number_of_pieces,
			number_of_artworks: artwork?.number_of_artworks,
			dimensions_type: artwork?.dimensions_type,
			is_bundle: artwork?.is_bundle,
			is_full_set: artwork?.is_full_set,
			artwork_type: artwork?.artwork_type,
			heni_artwork_type: artwork?.heni_artwork_type,
			collaborator_organisations: artwork?.collaborator_organisations?.map(
				(organisation) => organisation?.organisation_id
			),
			edition_info: artwork?.edition_info,
			series: artwork?.series,
			status: artwork?.status,
		} as ArtworkDetailsForm;
	});

	let image = $state(
		(() =>
			artwork?.primary_image
				? {
						...artwork?.primary_image,
						url:
							getImageUrl(
								artwork?.primary_image?.id,
								page.data.user.access_token
							) || '',
					}
				: null)()
	) as ArtworkImageType;

	$effect(() => {
		image = (
			artwork?.primary_image
				? {
						...artwork?.primary_image,
						url:
							getImageUrl(
								artwork?.primary_image?.id,
								page.data.user.access_token
							) || '',
					}
				: null
		) as ArtworkImageType;
	});

	let imageAndDescriptionForm = $state(
		(() => formatImageAndDescriptionForm(artwork))()
	) as ReturnType<typeof formatImageAndDescriptionForm>;

	$effect(() => {
		imageAndDescriptionForm = formatImageAndDescriptionForm(artwork);
	});

	const handleImageAndDescriptionFormChange = (
		form: ImageAndDescriptionForm
	) => {
		imageAndDescriptionForm = form;
		showSaveBar = true;
	};

	const handleImageChange = (newImage: { url: string; file: File } | null) => {
		image = newImage as ArtworkImageType;
		showSaveBar = true;
	};

	const handleArtworkDetailsForm = (
		newArtworkDetailsForm: ArtworkDetailsForm,
		preventShowSaveBar?: boolean
	) => {
		artworkDetailsForm = newArtworkDetailsForm;

		if (!preventShowSaveBar) {
			showSaveBar = true;
		}
	};

	const handleSaveClick = async () => {
		loading = true;
		try {
			const headers = getAuthorizationHeaders(data);

			const artworkId = await mutateArtwork({
				artwork,
				artworkDetailsForm,
				image,
				imageAndDescriptionForm,
				headers,
			});

			if (artworkId) {
				await gqlClientCustom.request(
					UpdateAndReturnArtworkDocument,
					{ artworks: [artworkId] },
					headers
				);
			}

			if (artworkDetailsForm?.id) {
				window.location.reload();
			} else {
				goto(`${Routes.ArtworkDetails}/${artworkId}`);
			}

			showToast({
				variant: 'success',
				message: `This artwork has been successfully ${artworkDetailsForm?.id ? 'updated' : 'created'}.`,
			});
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this artwork update. Please contact the support team.',
			});
		} finally {
			loading = false;
		}
	};
</script>

<div class="mb-14 flex flex-col lg:grid grid-cols-6 gap-4">
	{#if artworkDetailsForm?.status?.key === Status_Enum.Archived}
		<div class="col-span-6 justify-center">
			<Txt variant="label3">This artwork is archived</Txt>
		</div>
	{:else}
		<div class="col-span-6">
			<TopSection
				{loading}
				artworkId={artwork?.id}
				{artworkDetailsForm}
				onChange={handleArtworkDetailsForm}
			/>
		</div>
		{#if artwork?.id}
			<div class="col-span-6">
				<CreateUpdate
					updateHistory={{
						date_updated: artwork?.date_updated,
						date_created: artwork?.date_created,
						user_updated: artwork?.user_updated,
						user_created: artwork?.user_created,
					}}
					pipelineSource={data.pipelineSource}
				/>
			</div>
		{/if}
		<div class="col-span-2">
			<ImageDescriptionAndTags
				{image}
				{imageAndDescriptionForm}
				onChange={handleImageAndDescriptionFormChange}
				onImageChange={handleImageChange}
			/>
		</div>
		<div class="col-span-4">
			<ArtworkInformationCard
				{artworkDetailsForm}
				onChange={handleArtworkDetailsForm}
			/>
		</div>
		<div class="col-span-6">
			<ArtworkActivity artworkId={artwork?.id} />
		</div>
	{/if}
</div>

<PageSaveBar
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
	{loading}
	disabled={loading || !artworkDetailsForm?.artists?.length}
/>
