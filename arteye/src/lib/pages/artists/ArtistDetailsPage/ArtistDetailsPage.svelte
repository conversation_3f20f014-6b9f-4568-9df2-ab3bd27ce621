<script lang="ts">
	import classNames from 'classnames';
	import { PersonDetailsCard } from '../../../components/details-pages/PersonDetailsCard';
	import { type PersonalDetailsForm } from '../../../components/details-pages/PersonDetailsCard/PersonDetailsCard.svelte';
	import { ArtistInputs } from './ArtistInputs';
	import type { ArtistForm } from './ArtistInputs/ArtistInputs.svelte';
	import { goto, invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Accordion } from '$global/components/Accordion';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Link } from '$global/components/Link';
	import { LinkButton } from '$global/components/LinkButton';
	import { Tabs } from '$global/components/Tabs';
	import { showToast } from '$global/components/Toasts';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { EntityCards } from '$lib/components/details-pages/EntityCards';
	import { type AddressesType } from '$lib/components/details-pages/EntityCards/AddressesCard/AddressesCard.svelte';
	import { type CollectionNotesType } from '$lib/components/details-pages/EntityCards/CollectionNotesCard';
	import { type NotesType } from '$lib/components/details-pages/EntityCards/NotesCard/NotesCard.svelte';
	import {
		type AdditionalImagesType,
		type ProfileImageType,
	} from '$lib/components/details-pages/EntityCards/ProfileImageCard';
	import type { RelationshipItem } from '$lib/components/details-pages/EntityCards/RelationshipsCard/RelationshipRow/types';
	import { GalleryRepresentationsCard } from '$lib/components/details-pages/GalleryRepresentationsCard';
	import { type GalleryRepresentationsType } from '$lib/components/details-pages/GalleryRepresentationsCard/GalleryRepresentationsCard.svelte';
	import { InfoLabel } from '$lib/components/InfoLabel';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { UpdateArtistDocument } from '$lib/mutations/__generated__/udpateArtist.generated';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import {
		ArtActBasicSearchParam,
		ArtActSearchType,
		ArtworkAdvancedSearchParam,
	} from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/constants/search';
	import { SearchParam } from '$lib/types/types';
	import { areContactDetailsValid } from '$lib/utils/areContactDetailsValid/areContactDetailsValid';
	import { areEntityAddressesValid } from '$lib/utils/areEntityAddressesValid/areEntityAddressesValid';
	import { areEntityCollectionNotesValid } from '$lib/utils/areEntityCollectionNotesValid/areEntityCollectionNotesValid';
	import { areEntityNotesValid } from '$lib/utils/areEntityNotesValid/areEntityNotesValid';
	import { areEntityRelationshipsValid } from '$lib/utils/areEntityRelationshipsValid/areEntityRelationshipsValid';
	import {
		formatPersonalDetailsForm,
		formatProfileImage,
		formatAdditionalImages,
		formatContactDetailsRows,
		formatAddresses,
		formatEntityNotes,
		formatRelationships,
		formatCollectionEntityNotes,
		formatCurrentEntity,
	} from '$lib/utils/entityFormatters/entityFormatters';
	import { archiveEntity } from '$lib/utils/mutation-handlers/archiveEntity/archiveEntity';
	import { archivePerson } from '$lib/utils/mutation-handlers/archivePerson/archivePerson';
	import { createEntity } from '$lib/utils/mutation-handlers/createEntity/createEntity';
	import { mutateArtistDetails } from '$lib/utils/mutation-handlers/mutateArtistDetails/mutateArtistDetails';
	import { mutateEntity } from '$lib/utils/mutation-handlers/mutateEntity/mutateEntity';
	import { mutateEntityImages } from '$lib/utils/mutation-handlers/mutateEntityImages/mutateEntityImages';
	import { mutateGalleryRepresentations } from '$lib/utils/mutation-handlers/mutateGalleryRepresentations/mutateGalleryRepresentations';
	import { mutatePersonalDetails } from '$lib/utils/mutation-handlers/mutatePersonalDetails/mutatePersonalDetails';
	import type { ArtistsIdPageData as ArtistDetailsPageData } from '$routes/artists/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let loading = $state(false);
	let deleting = $state(false);

	const INITIAL_CHANGED_STATE = {
		artistForm: false,
		personalDetailsForm: false,
		profileImage: false,
		additionalImages: false,
		entityAddresses: false,
		entityNotes: false,
		collectionEntityNotes: false,
		contactDetailsRows: false,
		relationships: false,
		galleryRepresentations: false,
	};

	let data = $derived(getPageData<ArtistDetailsPageData>(page.data));

	let changed = $state(INITIAL_CHANGED_STATE);
	let artist = $derived(data.artist);
	let person = $derived(artist?.person);

	let artistForm = $state(
		(() => ({
			id: artist?.id,
			year_active_from: artist?.year_active_from,
			year_active_to: artist?.year_active_to,
		}))()
	) as ArtistForm;

	$effect(() => {
		artistForm = {
			id: artist?.id,
			year_active_from: artist?.year_active_from,
			year_active_to: artist?.year_active_to,
		} as ArtistForm;
	});

	let galleryRepresentations = $state(
		(() => data.galleryRepresentations)()
	) as GalleryRepresentationsType;

	$effect(() => {
		galleryRepresentations = data.galleryRepresentations;
	});

	let personalDetailsForm = $state(
		(() => formatPersonalDetailsForm(person, !page.params.id))()
	) as ReturnType<typeof formatPersonalDetailsForm>;

	$effect(() => {
		personalDetailsForm = formatPersonalDetailsForm(person, !page.params.id);
	});

	let profileImage = $state(
		(() => formatProfileImage(person?.entity, page.data.user.access_token))()
	) as ProfileImageType;

	$effect(() => {
		profileImage = formatProfileImage(
			person?.entity,
			page.data.user.access_token
		);
	});

	let additionalImages = $state(
		(() =>
			formatAdditionalImages(person?.entity, page.data.user.access_token))()
	) as ReturnType<typeof formatAdditionalImages>;

	$effect(() => {
		additionalImages = formatAdditionalImages(
			person?.entity,
			page.data.user.access_token
		);
	});

	let dropzoneUrlDialogForm = $derived(data.dropzoneUrlDialogForm);
	let legacyId = $derived(data.legacyId);
	let currentEntity = $derived(formatCurrentEntity(person?.entity));

	let contactDetailsRows = $state(
		(() => formatContactDetailsRows(person?.entity))()
	) as ReturnType<typeof formatContactDetailsRows>;

	$effect(() => {
		contactDetailsRows = formatContactDetailsRows(person?.entity);
	});

	let entityAddresses = $state(
		(() => formatAddresses(person?.entity))()
	) as ReturnType<typeof formatAddresses>;

	$effect(() => {
		entityAddresses = formatAddresses(person?.entity);
	});

	let entityNotes = $state(
		(() => formatEntityNotes(person?.entity))()
	) as ReturnType<typeof formatEntityNotes>;
	$effect(() => {
		entityNotes = formatEntityNotes(person?.entity);
	});

	let collectionEntityNotes = $state(
		(() => formatCollectionEntityNotes(person?.entity))()
	) as ReturnType<typeof formatCollectionEntityNotes>;

	$effect(() => {
		collectionEntityNotes = formatCollectionEntityNotes(person?.entity);
	});

	let relationships = $state(
		(() =>
			formatRelationships({
				relationships: data.relationships,
				entity: person?.entity,
			}))()
	) as ReturnType<typeof formatRelationships>;

	$effect(() => {
		relationships = formatRelationships({
			relationships: data.relationships,
			entity: person?.entity,
		});
	});

	let crumbs = $derived([
		{ label: 'Search Artist', href: userRoutes.routes.artist },
		{
			label: person
				? `${person?.first_name}${
						person?.last_name ? ` ${person?.last_name}` : ''
					}`
				: 'New Artist',
		},
	]);

	let showSaveBar = $state(false);
	let activeTab = $state(0);

	const tabs = [
		{
			id: '1',
			title: 'Artist details',
		},
		{
			id: '2',
			title: 'Activities',
		},
	];

	const dataCy = 'artist-details';

	const handleGalleryRepresentationsChange = (
		newGalleryRepresentations: GalleryRepresentationsType
	) => {
		galleryRepresentations = newGalleryRepresentations;
		showSaveBar = true;
		changed.galleryRepresentations = true;
	};

	const handleAdditionalImagesChange = (images: AdditionalImagesType) => {
		additionalImages = images;
		showSaveBar = true;
		changed.additionalImages = true;
	};

	const handleArtistChange = (artist: ArtistForm) => {
		artistForm = artist;
		showSaveBar = true;
		changed.artistForm = true;
	};

	const handleProfileImageChange = (image: ProfileImageType) => {
		profileImage = image;
		showSaveBar = true;
		changed.profileImage = true;
	};

	const handlePersonDetailsChange = (person: PersonalDetailsForm) => {
		personalDetailsForm = person;
		showSaveBar = true;
		changed.personalDetailsForm = true;
	};

	const handleEntityAddressesChange = (addresses: AddressesType) => {
		entityAddresses = addresses;
		showSaveBar = true;
		changed.entityAddresses = true;
	};

	const handleEntityNotesChange = (notes: NotesType) => {
		entityNotes = notes;
		showSaveBar = true;
		changed.entityNotes = true;
	};

	const handleCollectionEntityNotesChange = (
		collectionNotes: CollectionNotesType
	) => {
		collectionEntityNotes = collectionNotes;
		showSaveBar = true;
		changed.collectionEntityNotes = true;
	};

	const handleContactDetailsChange = (rows: typeof contactDetailsRows) => {
		contactDetailsRows = rows;
		showSaveBar = true;
		changed.contactDetailsRows = true;
	};

	const handleRelationshipsChange = (newRelationships: RelationshipItem[]) => {
		relationships = newRelationships;
		showSaveBar = true;
		changed.relationships = true;
	};

	const handleClickDeleteArtist = async () => {
		try {
			deleting = true;
			const headers = getAuthorizationHeaders(data);
			const entityId = `${person?.entity?.id}`;
			const entityName = `${person?.entity?.name}`;
			const entityType = `${person?.entity?.type?.key}`;
			const personId = `${person?.id}`;

			await archiveEntity({ entityId, entityName, entityType, headers });
			await archivePerson({ personId, headers });

			await gqlClient.request(
				UpdateArtistDocument,
				{
					id: page.params.id,
					data: {
						status: {
							key: Status_Enum.Archived,
							name: 'Archived',
						},
					},
				},
				headers
			);

			showToast({
				variant: 'success',
				message: 'This artist has been successfully deleted',
			});

			goto(userRoutes.routes.artist);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this artist deletion. Please contact the support team.',
			});
		} finally {
			deleting = false;
		}
	};

	const create = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName = [
				personalDetailsForm?.first_name,
				personalDetailsForm?.middle_name,
				personalDetailsForm?.last_name,
			]
				.filter(Boolean)
				.join(' ');

			const entityType = 'person';

			let personId = '';
			let artistId = '';

			const { entityId } = await createEntity({
				entityName,
				entityType,
				headers,
			});

			const requests: {
				artistDetails: ReturnType<typeof mutateArtistDetails> | null;
				images: ReturnType<typeof mutateEntityImages> | null;
				entity: ReturnType<typeof mutateEntity> | null;
			} = {
				artistDetails: null,
				images: null,
				entity: null,
			};

			const res = await mutatePersonalDetails({
				personId,
				entityId,
				entityName,
				entityType,
				personalDetailsForm,
				originalPersonTypes: person?.type,
				originalNationalities: person?.nationalities,
				headers,
			});

			personId = res.personId;

			requests.artistDetails = mutateArtistDetails({
				personId,
				artistForm,
				headers,
			});

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				changed: {
					...changed,
					attributes: changed.personalDetailsForm,
				},
			});

			const [artistDetailsRes, personImagesRes, entityRes] = await Promise.all([
				requests.artistDetails,
				requests.images,
				requests.entity,
			]);

			artistId = artistDetailsRes.artistId;

			showToast({
				variant: 'success',
				message: `This artist has been created successfully.`,
			});

			await goto(`${Routes.Artists}/${artistId}`);

			return artistId;
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while creating this person. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const update = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName = person?.entity?.name || '';

			const entityType = person?.entity?.type?.key || 'person';

			let personId = person?.id || '';
			let entityId = person?.entity?.id || '';

			const requests: {
				personalDetails: ReturnType<typeof mutatePersonalDetails> | null;
				artistDetails: ReturnType<typeof mutateArtistDetails> | null;
				images: ReturnType<typeof mutateEntityImages> | null;
				entity: ReturnType<typeof mutateEntity> | null;
				galleryRepresentations: ReturnType<
					typeof mutateGalleryRepresentations
				> | null;
			} = {
				galleryRepresentations: null,
				personalDetails: null,
				artistDetails: null,
				images: null,
				entity: null,
			};

			if (changed.personalDetailsForm) {
				requests.personalDetails = mutatePersonalDetails({
					personId,
					entityId: person?.entity?.id || '',
					entityName,
					entityType,
					personalDetailsForm,
					originalPersonTypes: person?.type,
					originalNationalities: person?.nationalities,
					headers,
				});
			}

			if (changed.artistForm) {
				requests.artistDetails = mutateArtistDetails({
					personId,
					artistForm,
					headers,
				});
			}

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			if (changed.galleryRepresentations) {
				requests.galleryRepresentations = mutateGalleryRepresentations({
					galleryRepresentations,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				changed: {
					...changed,
					attributes: changed.personalDetailsForm,
				},
			});

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: `This artist has been updated successfully.`,
			});

			await invalidateAll();

			changed = INITIAL_CHANGED_STATE;
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this artist update. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const handleSaveClick = async () => {
		let artistId = artist?.id;

		loading = true;

		if (artistId) {
			await update();
		} else {
			artistId = await create();
		}

		return artistId;
	};

	const originalEntityAddresses = (page.data as ArtistDetailsPageData)?.artist
		?.person?.entity?.addresses;

	const originalCollectionEntityNotes = (page.data as ArtistDetailsPageData)
		?.artist?.person?.entity?.collection_notes;

	let saveChangesDisabled = $derived(
		deleting ||
			loading ||
			`${personalDetailsForm.net_worth_usd || ''}`.endsWith('.') ||
			!personalDetailsForm.first_name ||
			!personalDetailsForm.last_name ||
			!areContactDetailsValid(contactDetailsRows) ||
			!areEntityAddressesValid(entityAddresses) ||
			!areEntityNotesValid(entityNotes) ||
			!areEntityCollectionNotesValid(collectionEntityNotes) ||
			!areEntityRelationshipsValid(relationships)
	);
</script>

<div
	class={classNames('pb-12', { 'pointer-events-none': loading || deleting })}
>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if person}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				{#if legacyId}
					<InfoLabel title="Legacy ID" value={legacyId} />
				{/if}

				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&name=${page.params.id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					class="h-[2rem]"
					size="sm"
					dataCy={`${dataCy}-view-artworks`}
					variant="secondary"
					disabled={loading || deleting}
				>
					view artworks
				</LinkButton>

				<Button
					onclick={() => {
						navigator.clipboard.writeText(
							`${artist?.person?.entity?.reference_id}`
						);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy artist id
				</Button>

				<Button
					onclick={handleClickDeleteArtist}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if data.artist}
		<CreateUpdate
			updateHistory={data.artist}
			class="mb-2 block mt-[-8px]"
			pipelineSource={data.pipelineSource}
		/>
	{/if}

	<Tabs {dataCy} bind:activeTab {tabs} fullLine />

	{#if !activeTab}
		<Accordion class="mb-4" multiple>
			<PersonDetailsCard
				onArtistPage
				{personalDetailsForm}
				onChange={handlePersonDetailsChange}
				title="Artist details"
				nationalities={data.user?.dropdowns.countries || []}
				genders={data.user?.dropdowns.genders || []}
				personTypes={data.user?.dropdowns.personTypes || []}
				attributeTypes={data.user?.dropdowns.attributeTypes || []}
			>
				<ArtistInputs {artistForm} onChange={handleArtistChange} />
			</PersonDetailsCard>
		</Accordion>

		<GalleryRepresentationsCard
			onSave={handleSaveClick}
			onChange={handleGalleryRepresentationsChange}
			{showSaveBar}
			{galleryRepresentations}
			title="Artist representation"
			{saveChangesDisabled}
		/>

		<EntityCards
			{currentEntity}
			{profileImage}
			onProfileImageChange={handleProfileImageChange}
			{additionalImages}
			onAdditionalImagesChange={handleAdditionalImagesChange}
			{contactDetailsRows}
			{originalEntityAddresses}
			{originalCollectionEntityNotes}
			onContactDetailsChange={handleContactDetailsChange}
			{entityAddresses}
			onCollectionEntityNotesChange={handleCollectionEntityNotesChange}
			{entityNotes}
			onEntityNotesChange={handleEntityNotesChange}
			{relationships}
			onRelationshipsChange={handleRelationshipsChange}
			{collectionEntityNotes}
			onEntityAddressesChange={handleEntityAddressesChange}
			{dropzoneUrlDialogForm}
		/>
	{/if}

	{#if activeTab}
		<div class="flex flex-col md:flex-row gap-4">
			<Link
				target="_blank"
				rel="noopener noreferrer"
				href={`${Routes.ArtworksAndActivities}?${SearchParam.ShowResults}=${StringBoolean.True}&${ArtworkAdvancedSearchParam.Name}=${person?.reference_id}&searchType=${ArtActSearchType.Advanced}`}
			>
				<Button dataCy="show-activities" size="md" variant="secondary">
					Show activities
				</Button>
			</Link>

			<Link
				target="_blank"
				rel="noopener noreferrer"
				href={`${Routes.ArtworksAndActivities}?${SearchParam.ShowResults}=${StringBoolean.True}&${ArtworkAdvancedSearchParam.Name}=${person?.reference_id}&searchType=${ArtActSearchType.Advanced}&${ArtActBasicSearchParam.ResultsTab}=${ArtworkAndActivitiesResultsTab.Artworks}`}
			>
				<Button dataCy="show-activities" size="md" variant="secondary">
					Show artworks
				</Button>
			</Link>
		</div>
	{/if}
</div>

<PageSaveBar
	{loading}
	disabled={saveChangesDisabled}
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
/>
