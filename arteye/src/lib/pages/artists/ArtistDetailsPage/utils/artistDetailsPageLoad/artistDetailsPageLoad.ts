import { error } from '@sveltejs/kit';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetArtistDocument } from '$lib/queries/__generated__/getArtist.generated';
import { GetGalleryRepresentationDocument } from '$lib/queries/__generated__/getGalleryRepresentation.generated';
import { GetRelationshipsDocument } from '$lib/queries/__generated__/getRelationships.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { ArtistsIdPageLoadEvent } from '$routes/artists/[id]/types';

export const artistDetailsPageLoad = async ({
	parent,
	params,
}: ArtistsIdPageLoadEvent) => {
	const data = await parent();
	const authHeaders = getAuthorizationHeaders(data);
	const artistId = params.id;

	const artistReq = (async () => {
		if (!artistId) {
			return null;
		}

		const res = await gqlClient.request(
			GetArtistDocument,
			{
				filter: {
					_and: [
						{ id: { _eq: artistId } },
						{ status: { key: { _neq: Status_Enum.Archived } } },
					],
				},
			},
			authHeaders
		);

		return res.artist?.[0];
	})();

	const relationshipsReq = (async () => {
		if (!artistId) {
			return [];
		}

		const res = await gqlClient.request(
			GetRelationshipsDocument,
			{
				filter: {
					_and: [
						{ status: { key: { _neq: Status_Enum.Archived } } },
						{
							_or: [
								{
									from_entity: {
										artist: {
											id: { _eq: artistId },
										},
									},
								},
								{
									to_entity: {
										artist: {
											id: { _eq: artistId },
										},
									},
								},
							],
						},
					],
				},
			},
			authHeaders
		);

		return res.relationship;
	})();

	const galleryRepresentationsReq = (async () => {
		if (!artistId) {
			return [];
		}

		const galleryRepresentationsRes = await gqlClient.request(
			GetGalleryRepresentationDocument,
			{
				filter: {
					_and: [
						{
							artist: {
								id: { _eq: artistId },
							},
						},
						{ status: { key: { _neq: Status_Enum.Archived } } },
					],
				},
			},
			authHeaders
		);

		return galleryRepresentationsRes.gallery_representation;
	})();

	const legacyIdReq = (
		artistId === PARAM_NEW || isOnDev() || !artistId
			? { getLegacyId: { legacyId: '' } }
			: await gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'artist',
						id: artistId,
					},
					getAuthorizationHeaders(data)
				)
	)?.getLegacyId?.legacyId;

	const pipelineSourceReq = (async () => {
		if (artistId === PARAM_NEW || !artistId) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{
				collection: PipelineSourceItem.Artist,
				id: artistId,
			},
			getAuthorizationHeaders(data)
		);

		return res.getDatasource?.data_source;
	})();

	const [
		artist,
		relationships,
		galleryRepresentations,
		legacyId,
		pipelineSource,
		{ dropzoneUrlDialogForm },
	] = await Promise.all([
		artistReq,
		relationshipsReq,
		galleryRepresentationsReq,
		legacyIdReq,
		pipelineSourceReq,
		getDropzoneUrlDialogSuperform(),
	]);

	if (!artist && artistId) {
		error(404, 'Artist not found');
	}

	return {
		...data,
		legacyId,
		artist,
		galleryRepresentations,
		relationships,
		pipelineSource,
		dropzoneUrlDialogForm,
	};
};
