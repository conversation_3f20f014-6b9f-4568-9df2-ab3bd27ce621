<script module lang="ts">
	export type ArtistForm = Partial<
		Pick<
			NonNullable<ArtistDetailsPageData['artist']>,
			'id' | 'year_active_from' | 'year_active_to'
		>
	>;
</script>

<script lang="ts">
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import type { ArtistsIdPageData as ArtistDetailsPageData } from '$routes/artists/[id]/types';

	interface Props {
		artistForm: ArtistForm;
		onChange: (artist: ArtistForm) => void;
	}

	let { artistForm, onChange }: Props = $props();

	const dataCy = 'artist-inputs';

	const handleNumberInputChange =
		(field: keyof ArtistForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...artistForm, [field]: value });
		};
</script>

<div class="col-span-1">
	<Input
		dataCy={`${dataCy}-year-active-from`}
		name="year-active-from"
		placeholder=""
		label="Active From"
		value={artistForm?.year_active_from
			? `${artistForm?.year_active_from}`
			: ''}
		size="sm"
		onkeyup={handleNumberInputChange('year_active_from')}
		onkeydown={handleKeyDownNumbersOnly}
		onchange={handleNumberInputChange('year_active_from')}
		tooltip="tbc"
	/>
</div>

<div class="col-span-1">
	<Input
		dataCy={`${dataCy}-year-active-to`}
		name="year-active-to"
		placeholder=""
		label="Active To"
		value={artistForm?.year_active_to ? `${artistForm?.year_active_to}` : ''}
		size="sm"
		onkeyup={handleNumberInputChange('year_active_to')}
		onkeydown={handleKeyDownNumbersOnly}
		onchange={handleNumberInputChange('year_active_to')}
		tooltip="tbc"
	/>
</div>
