import { ArtistSearchParam } from '../../constants/search';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	ArtistSearchFilters,
	IntValueFilter,
	ValueFilterOperator,
} from '$gql/types-custom';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';

export const getArtistFilter = (searchParams: URLSearchParams) => {
	let filters: ArtistSearchFilters = {};

	const artistName = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.ArtistName,
	});

	const nationality = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.Nationality,
	});

	const yearBirth = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.YearBirth,
	});

	const yearBirthRange = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.YearBirthRange,
	});

	const yearDeath = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.YearDeath,
	});

	const yearDeathRange = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.YearDeathRange,
	});

	const isFavouriteArtist = getDecodedSearchParam({
		searchParams,
		key: ArtistSearchParam.IsFavouriteArtist,
	});

	if (artistName) {
		filters = {
			...filters,
			nameOrId: artistName,
		};
	}

	if (nationality) {
		filters = {
			...filters,
			nationality,
		};
	}

	if (yearBirth) {
		filters = {
			...filters,
			yearOfBirth: getValueFilter({
				value: yearBirth,
				range: yearBirthRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (yearDeath) {
		filters = {
			...filters,
			yearOfDeath: getValueFilter({
				value: yearDeath,
				range: yearDeathRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (isFavouriteArtist === StringBoolean.True) {
		filters = {
			...filters,
			isFavorite: true,
		};
	}

	return filters;
};
