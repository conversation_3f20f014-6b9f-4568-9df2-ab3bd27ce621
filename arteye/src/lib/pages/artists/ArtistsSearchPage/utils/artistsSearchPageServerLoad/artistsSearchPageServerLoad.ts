// import { redirect } from '@sveltejs/kit';
// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
// import { ActivityCache } from '$lib/activityCache';
// import { CacheEntries } from '$lib/constants/cache';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import { gqlClient } from '$lib/gqlClient';
// import {
// 	GetLocationsDocument,
// 	type GetLocationsQuery,
// } from '$lib/queries/__generated__/getLocations.generated';
// import { LocationType } from '$lib/types/location-type';
// import type { ArtistsSearchPageServerLoadEvent } from '$routes/artists/types';

// export const artistsSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: ArtistsSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Artist];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	const authHeaders = getAuthorizationHeaders(data);

// 	const countries = await (async () => {
// 		const cachedCountries = ActivityCache.get(CacheEntries.Countries);
// 		if (cachedCountries) {
// 			return cachedCountries as GetLocationsQuery['location'];
// 		}

// 		const countriesRes = await gqlClient.request(
// 			GetLocationsDocument,
// 			{
// 				filter: { type: { key: { _eq: LocationType.Country } } },
// 				sort: ['name'],
// 				limit: -1,
// 			},
// 			authHeaders
// 		);

// 		const locations = countriesRes?.location || [];
// 		ActivityCache.set(CacheEntries.Countries, locations);
// 		return locations;
// 	})();

// 	return {
// 		...data,
// 		countries,
// 	};
// };
