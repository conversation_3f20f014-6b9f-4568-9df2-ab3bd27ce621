import { ArtistSearchParam } from '../../constants/search';
import { ARTIST_DEFAULT_SORT } from '../../constants/sort';
import { getArtistFilter } from '../getArtistFilter/getArtistFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	ArtistSearchInput,
	ArtistSearchSortField,
} from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import {
	ArtistSearchDocument,
	type ArtistSearchQuery,
} from '$lib/custom-queries/__generated__/artistSearch.generated';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { ArtistsSearchPageLoadEvent } from '$routes/artists/types';

const getOffset = (searchParams: URLSearchParams) => {
	const page = Number(
		getDecodedSearchParam({
			searchParams,
			key: SearchParam.Page,
		})
	);

	const offset = page ? (page - 1) * TABLE_PAGE_SIZE : 0;

	return offset;
};

export const artistsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: ArtistsSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;
	const data = existingData || {};

	let res: ArtistSearchQuery | null = null;
	let artists: ArtistSearchQuery['artistSearch']['data'] | null = null;
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: ArtistSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filters = getArtistFilter(searchParams);
		const offset = getOffset(searchParams);

		const sort = getSortWithDirection<ArtistSearchSortField>(
			searchParams,
			ARTIST_DEFAULT_SORT as ArtistSearchSortField
		);

		const input: ArtistSearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
			sort,
		};

		res = await gqlClientCustom.request(
			ArtistSearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		artists = res.artistSearch.data;
		resultsCount = res.artistSearchCount || 0;
	}

	return {
		...parentData,
		...data,
		artists,
		resultsCount,
		showResults,
		currentPage,
	};
};
