<script lang="ts">
	import { ArtistsSearchForm } from './ArtistsSearchForm';
	import type { ArtistSearchFormFields } from './ArtistsSearchForm/ArtistsSearchForm.svelte';
	import { ArtistsTable } from './ArtistsTable';
	import { ArtistSearchParam } from './constants/search';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';

	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { ArtistsSearchPageData } from '$routes/artists/types';

	let data = $derived(getPageData<ArtistsSearchPageData>(page.data));
	let showResults = $derived(data.showResults);
	let countries = $derived(data.user?.dropdowns?.countries);

	let nationalitiesOptions = $derived(
		(countries || []).map<MultiSelectOption>((country) => {
			let label = country?.country_nationality
				? `${country?.country_nationality} (${country?.name})`
				: `${country?.name}`;

			return {
				label,
				value: country?.name || '',
			};
		})
	);

	const formatParamString = (
		values: ArtistSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	): string => {
		const {
			sort,
			yearBirth,
			yearBirthRange,
			yearDeath,
			yearDeathRange,
			isFavouriteArtist,
			nationality,
			artistName,
		} = values;

		const params = {
			[ArtistSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[ArtistSearchParam.ShowResults]: showResults,
			[ArtistSearchParam.ArtistName]: artistName,
			[ArtistSearchParam.YearBirth]: yearBirth,
			[ArtistSearchParam.YearBirthRange]: yearBirth ? yearBirthRange : '',
			[ArtistSearchParam.YearDeath]: yearDeath,
			[ArtistSearchParam.YearDeathRange]: yearDeath ? yearDeathRange : '',
			[ArtistSearchParam.Nationality]: nationality,
			[ArtistSearchParam.IsFavouriteArtist]: isFavouriteArtist
				? isFavouriteArtist.toString()
				: '',
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<ArtistsSearchForm {nationalitiesOptions} {formatParamString} />
</div>

{#if showResults}
	<ArtistsTable />
{/if}
