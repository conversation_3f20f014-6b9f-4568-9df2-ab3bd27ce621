<script module lang="ts">
	export interface ArtistSearchFormFields {
		artistName: string;
		sort: string[];
		nationality: string;
		yearBirthRange: string;
		yearBirth: string;
		yearDeathRange: string;
		yearDeath: string;
		isFavouriteArtist: boolean;
	}
</script>

<script lang="ts">
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { ArtistSearchParam } from '../constants/search';
	import { ARTIST_SORT_OPTIONS } from '../constants/sort';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { SearchParam } from '$lib/types/types';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getBooleanFromParam } from '$lib/utils/getBooleanFromParam/getBooleanFromParam';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { getMultiselectSingleValue } from '$lib/utils/getMultiselectSingleValue/getMultiselectSingleValue';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';

	interface Props {
		nationalitiesOptions: MultiSelectOption[];
		formatParamString: (
			values: ArtistSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let { nationalitiesOptions, formatParamString }: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			SearchParam.Sort,
			ARTIST_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			SearchParam.Sort,
			ARTIST_SORT_OPTIONS
		);
	});

	let artistName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtistSearchParam.ArtistName,
		})
	);

	let nationality: MultiSelectOption[] = $state([]);
	$effect(() => {
		nationality = findMultiselectOptions(
			page.url.searchParams,
			ArtistSearchParam.Nationality,
			nationalitiesOptions
		);
	});

	let yearBirthRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtistSearchParam.YearBirthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	let yearBirth = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtistSearchParam.YearBirth,
		})
	);

	let yearDeathRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ArtistSearchParam.YearDeathRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);

	let yearDeath = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ArtistSearchParam.YearDeath,
		})
	);

	let isFavouriteArtist = $state(
		getBooleanFromParam(
			page.url.searchParams,
			ArtistSearchParam.IsFavouriteArtist
		)
	);

	const dataCyPrefix = 'search';

	const getParams = () => ({
		artistName,
		sort: getValuesFromSelectOptions(sort),
		nationality: getMultiselectSingleValue(nationality),
		yearBirthRange,
		yearBirth,
		yearDeathRange,
		yearDeath,
		isFavouriteArtist,
	});

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url.pathname !== Routes.Artists
			) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				if (queryParams !== 'showResults=false') {
					await setArteyeSearch(
						Searches.Artist,
						`${Routes.Artists}?${queryParams}`
					);
				}
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const handleSearchClick = () => {
		const searchParamString = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${searchParamString}`);
	};

	const handleClearClick = () => {
		artistName = '';
		sort = [];
		nationality = [];
		yearBirthRange = ValueFilterOperator.Equal;
		yearBirth = '';
		yearDeathRange = ValueFilterOperator.Equal;
		yearDeath = '';
		isFavouriteArtist = false;
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search artists</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.Artists}/new`}
			class="hidden lg:block"
		>
			Create New
		</LinkButton>
	</div>

	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-name`}
				name="name"
				placeholder="Enter name"
				label="Artist name"
				bind:value={artistName}
				size="sm"
			/>
		</div>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="lg:col-span-1 col-span-2">
				<InputWithSelect
					size="sm"
					label="Year of birth"
					dataCy={`${dataCyPrefix}-year-birth`}
					name="year_birth"
					bind:selectValue={yearBirthRange}
					bind:inputValue={yearBirth}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearBirthRange]}
					tooltip="Copy TBC"
				/>
			</div>

			<div class="lg:col-span-1 col-span-2">
				<InputWithSelect
					size="sm"
					label="Year of death"
					dataCy={`${dataCyPrefix}-year-death`}
					name="year_birth"
					bind:selectValue={yearDeathRange}
					bind:inputValue={yearDeath}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearDeathRange]}
					tooltip="Copy TBC"
				/>
			</div>

			<MultiSelect
				name="nationality"
				dataCy={`${dataCyPrefix}-nationality`}
				label="Nationality"
				bind:selected={nationality}
				placeholder="Type or select"
				options={nationalitiesOptions}
				class="lg:col-span-1 col-span-2"
				size="sm"
				maxSelect={1}
			/>

			<MultiSelect
				name="sort_by"
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				bind:selected={sort}
				placeholder="Sort by"
				options={ARTIST_SORT_OPTIONS}
				class="lg:col-span-1 col-span-2"
				size="sm"
			/>

			<div class="lg:col-span-1 col-span-2 flex items-end">
				<InputLabel
					dataCy={`${dataCyPrefix}-is-favourite-artist`}
					variant="body3"
				>
					<Checkbox
						dataCy={`${dataCyPrefix}-is-favourite-artist`}
						bind:checked={isFavouriteArtist}
					/>
					Is favourite artist
				</InputLabel>
			</div>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
