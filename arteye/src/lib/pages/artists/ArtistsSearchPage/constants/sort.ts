import { ArtistSearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const ARTIST_SORT_OPTIONS = sortByField(
	[
		{ label: 'Artist name (A-Z)', value: ArtistSearchSortField.Name },
		{
			label: 'Artist name (Z-A)',
			value: getDescendingDirection(ArtistSearchSortField.Name),
		},
		{ label: 'Year of Birth (Asc)', value: ArtistSearchSortField.YearOfBirth },
		{
			label: 'Year of Birth (Desc)',
			value: getDescendingDirection(ArtistSearchSortField.YearOfBirth),
		},
		{ label: 'Nationality (A-Z)', value: ArtistSearchSortField.Nationality },
		{
			label: 'Nationality (Z-A)',
			value: getDescendingDirection(ArtistSearchSortField.Nationality),
		},
		{ label: 'Date created (Asc)', value: ArtistSearchSortField.DateCreated },
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(ArtistSearchSortField.DateCreated),
		},
		{ label: 'Date updated (Asc)', value: ArtistSearchSortField.DateUpdated },
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(ArtistSearchSortField.DateUpdated),
		},
		{
			label: 'Number of artworks (Asc)',
			value: ArtistSearchSortField.NumberOfArtworks,
		},
		{
			label: 'Number of artworks (Desc)',
			value: getDescendingDirection(ArtistSearchSortField.NumberOfArtworks),
		},
	],
	'label'
);

export const ARTIST_DEFAULT_SORT = getDescendingDirection(
	ArtistSearchSortField.NumberOfArtworks
);
