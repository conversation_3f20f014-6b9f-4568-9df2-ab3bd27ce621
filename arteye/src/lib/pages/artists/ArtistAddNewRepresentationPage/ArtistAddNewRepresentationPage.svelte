<script lang="ts">
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import { GalleryAutocomplete } from './GalleryAutocomplete';
	import { AddNewRepresentationFieldNames } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Dropzone, type DropzoneFile } from '$global/components/Dropzone';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { showToast } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { StepButtons } from '$global/features/form/components/StepButtons';
	import { StepContainer } from '$global/features/form/components/StepContainer';
	import { StepInput } from '$global/features/form/components/StepInput';
	import { getSuperForm } from '$global/utils/getSuperForm';
	import { getUTCDayDate } from '$global/utils/getUTCDayDate/getUTCDayDate';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { CreateGalleryRepresentationDocument } from '$lib/queries/__generated__/createGalleryRepresentation.generated';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';
	import { uploadFile } from '$lib/utils/uploadFile/uploadFile';
	import type { ArtistsIdAddNewRepresentationPageData as ArtistAddNewRepresentationPageData } from '$routes/artists/[id]/add-new-representation/types';
	interface Props {
		class?: string;
	}

	let { ...props }: Props = $props();

	const dataCyPrefix = 'add-new-representation';

	let submitting = $state(false);
	let files: DropzoneFile[] = $state([]) as DropzoneFile[];
	let gallerySelectedOption: OptionType | null = $state(null);
	let form: Partial<Record<AddNewRepresentationFieldNames, string>> = $state(
		{}
	);
	let blur: Partial<Record<AddNewRepresentationFieldNames, boolean>> = $state(
		{}
	);

	const galleryValue = writable('');

	const handleBlur = (field: AddNewRepresentationFieldNames) => () => {
		blur = {
			...blur,
			[field]: true,
		};
	};

	let data = $derived(page.data as ArtistAddNewRepresentationPageData);
	let artist = $derived(data.artist);
	let artistName = $derived(
		`${artist?.person?.first_name} ${artist?.person?.last_name}`
	);
	let artistYearBirth = $derived(
		artist.person?.year_birth ? `b. ${artist.person?.year_birth}` : ''
	);
	let artistNationalities = $derived(
		artist?.person?.nationalities
			?.map((nationality) => nationality?.country?.name || '')
			.join(', ') || ''
	);

	let artistUrl = $derived(`${Routes.Artists}/${artist?.id}`);
	let artistArtworkCount = $derived(
		artist?.aggregations?.[0]?.artwork_count || 0
	);

	let crumbs = $derived([
		{ label: 'Search Artist', href: userRoutes.routes.artist },
		{
			label: artistName,
			href: artistUrl,
		},
		{ label: 'Add new representation' },
	]);

	const handleSubmit = async (e: Event) => {
		e.preventDefault();
		const headers = getAuthorizationHeaders(data);
		submitting = true;

		try {
			let file = null;

			if (files[0]) {
				if (!files[0].url) {
					file = await uploadFile(files[0] as File, headers);
				} else {
					const uplodedPdfFileResponse = await fetch(
						'/api/upload-file-from-url',
						{
							method: 'POST',
							headers: getAuthorizationHeaders(data),
							body: JSON.stringify({ url: files[0].url }),
						}
					);

					const uploadedPdfFile = await uplodedPdfFileResponse.json();
					file = uploadedPdfFile;
				}
			}

			await gqlClient.request(
				CreateGalleryRepresentationDocument,
				{
					data: {
						gallery: {
							id: gallerySelectedOption?.line3,
						},
						artist: {
							id: page.params.id,
						},
						news_url: form[AddNewRepresentationFieldNames.NewsUrl] || '',
						description: form[AddNewRepresentationFieldNames.Description],
						receiver: form[AddNewRepresentationFieldNames.Receiver],
						email_subject: form[AddNewRepresentationFieldNames.EmailSubject],
						...(form[AddNewRepresentationFieldNames.EmailDate] && {
							email_date: getUTCDayDate(
								form[AddNewRepresentationFieldNames.EmailDate]
							),
						}),
						image: file,
					},
				},
				headers
			);

			showToast({
				variant: 'success',
				message: 'New representation successfully added',
			});

			goto(`${Routes.Artists}/${page.params.id}`);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while adding the representation',
			});

			submitting = false;
		}
	};
</script>

<PageBody>
	<Breadcrumbs
		txtVariant="h5"
		dataCy={dataCyPrefix}
		{crumbs}
		class="ml-0 pl-0 sm:ml-[-8px] sm:pl-0"
	/>

	<form
		onsubmit={handleSubmit}
		class={twMerge('col-span-2 mb-[4rem] sm:mb-[7rem]', props.class)}
	>
		<StepContainer>
			<Txt class="col-span-2 mt-4 text-center" variant="h3"
				>Add gallery representation</Txt
			>

			<div class="col-span-2">
				<InputLabel class="mb-2" dataCy={`${dataCyPrefix}-artist`} required
					>Artist</InputLabel
				>

				<div class="rounded border border-gray-200 bg-white px-3 py-2">
					<Txt
						class="text-blue-500"
						component="a"
						variant="body2"
						href={artistUrl}
						target="_blank"
						rel="noopener noreferrer"
					>
						{artistName}
					</Txt>

					<Txt component="span" class="text-gray-500" variant="body2">
						{' '}
						{#if artistNationalities || artistYearBirth}
							({artistNationalities}{artistNationalities && artistYearBirth
								? `, ${artistYearBirth}`
								: artistYearBirth}) {artistArtworkCount}
							{`artwork${artistArtworkCount !== 1 ? 's' : ''}`}
						{/if}
					</Txt>
				</div>
			</div>

			<div class="col-span-2">
				<InputLabel class="mb-2" dataCy={`${dataCyPrefix}-gallery`} required
					>Gallery</InputLabel
				>
				<GalleryAutocomplete
					dataCy={dataCyPrefix}
					placeholder="Start typing to search"
					bind:selectedOption={gallerySelectedOption}
					value={galleryValue}
				/>
			</div>

			<StepInput
				dataCy={`${dataCyPrefix}-news-url`}
				label="News URL"
				placeholder="Enter specific URL if it exists. If not, enter gallery URL"
				id={AddNewRepresentationFieldNames.NewsUrl}
				name={AddNewRepresentationFieldNames.NewsUrl}
				type="text"
				class="col-span-2"
				required
				tooltip="Enter specific URL if it exists. If not, enter gallery URL"
				stopPropagationWhenPressingEnter
				error={!form[AddNewRepresentationFieldNames.NewsUrl] &&
				blur[AddNewRepresentationFieldNames.NewsUrl]
					? 'The News URL is missing'
					: ''}
				bind:value={form[AddNewRepresentationFieldNames.NewsUrl]}
				onblur={handleBlur(AddNewRepresentationFieldNames.NewsUrl)}
			/>

			<StepInput
				dataCy={`${dataCyPrefix}-description`}
				label="Description"
				placeholder="Enter description"
				id={AddNewRepresentationFieldNames.Description}
				name={AddNewRepresentationFieldNames.Description}
				type="text"
				rows={5}
				class="col-span-2"
				stopPropagationWhenPressingEnter
				bind:value={form[AddNewRepresentationFieldNames.Description]}
				onblur={handleBlur(AddNewRepresentationFieldNames.Description)}
			/>

			<StepInput
				dataCy={`${dataCyPrefix}-email-subject`}
				label="Email subject"
				placeholder="Enter email subject"
				id={AddNewRepresentationFieldNames.EmailSubject}
				name={AddNewRepresentationFieldNames.EmailSubject}
				class="col-span-2"
				tooltip="Enter the subject of the email relative to the gallery representation"
				stopPropagationWhenPressingEnter
				bind:value={form[AddNewRepresentationFieldNames.EmailSubject]}
				onblur={handleBlur(AddNewRepresentationFieldNames.EmailSubject)}
			/>

			<StepInput
				dataCy={`${dataCyPrefix}-email-date`}
				label="Email date"
				placeholder="Enter email date"
				id={AddNewRepresentationFieldNames.EmailDate}
				name={AddNewRepresentationFieldNames.EmailDate}
				class="col-span-2"
				type="date"
				stopPropagationWhenPressingEnter
				tooltip="Enter the date of the email relative to the gallery representation"
				bind:value={form[AddNewRepresentationFieldNames.EmailDate]}
				onblur={handleBlur(AddNewRepresentationFieldNames.EmailDate)}
			/>

			<StepInput
				dataCy={`${dataCyPrefix}-receiver`}
				label="Receiver"
				placeholder="Enter receiver"
				id={AddNewRepresentationFieldNames.Receiver}
				name={AddNewRepresentationFieldNames.Receiver}
				class="col-span-2"
				stopPropagationWhenPressingEnter
				tooltip="Enter the email address where the email relative to the gallery representation was sent"
				bind:value={form[AddNewRepresentationFieldNames.Receiver]}
				onblur={handleBlur(AddNewRepresentationFieldNames.Receiver)}
			/>

			<div class="col-span-2">
				<InputLabel dataCy={`${dataCyPrefix}-pdf-label`} class="mb-2"
					>News image</InputLabel
				>
				<Dropzone
					dropzoneUrlDialogForm={page.data.dropzoneUrlDialogForm}
					bind:files
					{getSuperForm}
					maxSize={100000000}
					dataCy={`${dataCyPrefix}-news-image`}
					accept={['image/jpeg', 'image/jpg', 'image/png']}
					multiple={false}
				/>
			</div>
		</StepContainer>

		<StepButtons
			backButtonProps={{
				class: '[&>button]:bg-gray-100',
				href: `${Routes.Artists}/${page.params.id}`,
			}}
			continueButtonProps={{
				loading: submitting,
				disabled:
					!form[AddNewRepresentationFieldNames.NewsUrl] ||
					!gallerySelectedOption ||
					submitting,
			}}
		>
			confirm
		</StepButtons>
	</form>
</PageBody>
