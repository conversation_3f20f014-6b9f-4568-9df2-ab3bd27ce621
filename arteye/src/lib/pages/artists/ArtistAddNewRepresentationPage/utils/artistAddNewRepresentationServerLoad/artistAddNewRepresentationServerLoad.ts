import { error } from '@sveltejs/kit';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { GetArtistDocument } from '$lib/queries/__generated__/getArtist.generated';
import type { ArtistsIdAddNewRepresentationPageServerLoadEvent } from '$routes/artists/[id]/add-new-representation/types';

export const artistAddNewRepresentationServerLoad = async ({
	parent,
	params,
}: ArtistsIdAddNewRepresentationPageServerLoadEvent) => {
	const data = await parent();
	const authHeaders = getAuthorizationHeaders(data);
	const artistId = params.id;

	if (!artistId) {
		error(404, 'Artist id not provided');
	}

	const res = await gqlClient.request(
		GetArtistDocument,
		{
			filter: {
				_and: [
					{
						id: { _eq: artistId },
					},
					{ status: { key: { _neq: Status_Enum.Archived } } },
				],
			},
		},
		authHeaders
	);

	const artist = res.artist?.[0];

	if (!artist) {
		error(404, 'Artist not found');
	}

	const dropzoneUrlForm = await getDropzoneUrlDialogSuperform();

	return {
		...data,
		...dropzoneUrlForm,
		artist,
	};
};
