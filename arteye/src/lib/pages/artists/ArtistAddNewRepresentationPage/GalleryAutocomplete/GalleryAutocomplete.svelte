<script lang="ts" module>
	export const formatGallery = (
		gallery: GetGalleryDetailsQuery['gallery'][number]
	) => ({
		line1: `${gallery.organisation?.name}`,
		line1Suffix: gallery.organisation?.location?.name
			? `(${gallery.organisation?.location?.name})`
			: '',
		line2: `${Routes.Galleries}/${gallery.id}`,
		line3: `${gallery.id}`,
	});
</script>

<script lang="ts">
	import { type Writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import {
		GetGalleryDetailsDocument,
		type GetGalleryDetailsQuery,
		type GetGalleryDetailsQueryVariables,
	} from '$lib/queries/__generated__/getGalleryDetails.generated';

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		value: Writable<string>;
	}

	let {
		placeholder = 'Start typing to search galleries',
		dataCy,
		selectedOption = $bindable(null),
		value = $bindable(),
	}: Props = $props();

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	const getVariables = (value: string): GetGalleryDetailsQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: {
				...(value
					? {
							_and: [
								...(() => {
									if (!value) {
										return [];
									}

									if (isUuidValid(value)) {
										return [{ id: { _eq: value } }];
									}

									if (!isNaN(+value)) {
										return [{ reference_id: { _eq: value } }];
									}

									return [
										{
											organisation: {
												name: {
													_icontains: value,
												},
											},
										},
									];
								})(),

								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						}
					: { status: { key: { _neq: Status_Enum.Archived } } }),
			},
		};
	};

	const getOptions = (data: GetGalleryDetailsQuery | undefined) => {
		return (data?.gallery || []).map(formatGallery);
	};
</script>

<div class={'relative'}>
	<QueryAutocomplete
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="gallery"
		dataCy={`${dataCy}-gallery`}
		{placeholder}
		emptyValueResponse={{ gallery: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix:
					'text-[0.875rem] sm:text-[0.875rem] font-[400] text-gray-500',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				line1: 'text-[0.875rem] sm:text-[0.875rem] font-[400]',
				line1Suffix:
					'text-[0.875rem] sm:text-[0.875rem] font-[400] text-gray-500',
				button: 'max-w-full',
				wrapper:
					'py-2 bg-white rounded border border-gray-200 bg-white pl-3 pr-7 py-2',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetGalleryDetailsDocument}
		{value}
		bind:selectedOption
	>
		{#snippet noResults()}
			<NoResults class="text-center" dataCy={`${dataCy}-gallery-autocomplete`}
				><span class="font-[400]">No gallery found.</span>
				<Txt
					variant="body2"
					class="font-[400] text-blue-500 underline hidden lg:block"
					component="a"
					target="_blank"
					href={`${Routes.Galleries}/new`}>Create new gallery</Txt
				></NoResults
			>{/snippet}
	</QueryAutocomplete>
	{#if selectedOption}
		<button
			class="absolute right-3 top-3"
			onclick={() => {
				selectedOption = null;
			}}
		>
			<CrossIcon class="h-4 w-4" />
		</button>
	{/if}
</div>
