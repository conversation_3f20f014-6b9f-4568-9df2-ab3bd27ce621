<script lang="ts">
	import { PeopleSearchParam } from './constants/search';
	import { PeopleSearchForm } from './PeopleSearchForm';
	import type { PeopleSearchFormFields } from './PeopleSearchForm';
	import { PeopleTable } from './PeopleTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { PeopleSearchPageData } from '$routes/people/types';

	let data = $derived(getPageData<PeopleSearchPageData>(page.data));
	let countries = $derived(data.user?.dropdowns?.countries);
	let attributeTypes = $derived(data.user?.dropdowns?.attributeTypes);
	let personTypes = $derived(data.user?.dropdowns?.personTypes);
	let showResults = $derived(data.showResults);

	let nationalitiesOptions: MultiSelectOption[] = $state([]);
	let personTypesOptions: MultiSelectOption[] = $state([]);
	let attributesOptions: MultiSelectOption[] = $state([]);

	$effect(() => {
		nationalitiesOptions = (countries || []).map<MultiSelectOption>(
			(country) => {
				let label = country?.country_nationality
					? `${country?.country_nationality} (${country?.name})`
					: `${country?.name}`;

				return {
					label,
					value: country?.name || '',
				};
			}
		);
	});

	$effect(() => {
		personTypesOptions = (personTypes || []).map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	$effect(() => {
		attributesOptions = (attributeTypes || []).map<MultiSelectOption>(
			(type) => ({
				label: type?.name || '',
				value: type?.key || '',
			})
		);
	});

	const formatParamString = (
		{
			sort,
			name,
			types,
			attributes,
			yearBirth,
			yearBirthRange,
			yearDeath,
			yearDeathRange,
			nationality,
			addressCity,
			addressCountry,
			owns,
			ownsCategory,
			wants,
			wantsCategory,
		}: PeopleSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const params: Record<PeopleSearchParam, string> = {
			[PeopleSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[PeopleSearchParam.ShowResults]: showResults,
			[PeopleSearchParam.Name]: name,
			[PeopleSearchParam.Types]: getSearchParamFromStringArray(types),
			[PeopleSearchParam.Attributes]: getSearchParamFromStringArray(attributes),
			[PeopleSearchParam.YearBirth]: yearBirth,
			[PeopleSearchParam.YearBirthRange]: yearBirth ? yearBirthRange : '',
			[PeopleSearchParam.YearDeath]: yearDeath,
			[PeopleSearchParam.YearDeathRange]: yearDeath ? yearDeathRange : '',
			[PeopleSearchParam.Nationality]: nationality,
			[PeopleSearchParam.AddressCity]: addressCity,
			[PeopleSearchParam.AddressCountry]: addressCountry,
			[PeopleSearchParam.Owns]: owns,
			[PeopleSearchParam.OwnsCategory]: ownsCategory,
			[PeopleSearchParam.Wants]: wants,
			[PeopleSearchParam.WantsCategory]: wantsCategory,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<PeopleSearchForm
		{nationalitiesOptions}
		{personTypesOptions}
		{attributesOptions}
		{formatParamString}
	/>
</div>

{#if showResults}
	<PeopleTable />
{/if}
