import type { PeopleSearchParam } from '../constants/search';

export interface PeopleSearchFormFields {
	[PeopleSearchParam.Sort]: string[];
	[PeopleSearchParam.Name]: string;
	[PeopleSearchParam.Types]: string[];
	[PeopleSearchParam.Attributes]: string[];
	[PeopleSearchParam.YearBirth]: string;
	[PeopleSearchParam.YearBirthRange]: string;
	[PeopleSearchParam.YearDeath]: string;
	[PeopleSearchParam.YearDeathRange]: string;
	[PeopleSearchParam.Nationality]: string;
	[PeopleSearchParam.AddressCity]: string;
	[PeopleSearchParam.AddressCountry]: string;
	[PeopleSearchParam.Owns]: string;
	[PeopleSearchParam.OwnsCategory]: string;
	[PeopleSearchParam.Wants]: string;
	[PeopleSearchParam.WantsCategory]: string;
}

export enum OwnsWantsCategory {
	Artist = 'artist',
	Artwork = 'artwork',
	ArtworkSeries = 'artworkSeries',
}

export const OWNS_WANTS_OPTIONS = [
	{ label: 'Artist', value: OwnsWantsCategory.Artist },
	{ label: 'Artwork', value: OwnsWantsCategory.Artwork },
	{ label: 'Series', value: OwnsWantsCategory.ArtworkSeries },
];
