<script lang="ts">
	import { PeopleSearchParam } from '../constants/search';
	import { PEOPLE_SORT_OPTIONS } from '../constants/sort';
	import {
		OWNS_WANTS_OPTIONS,
		OwnsWantsCategory,
		type PeopleSearchFormFields,
	} from './types';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { getMultiselectSingleValue } from '$lib/utils/getMultiselectSingleValue/getMultiselectSingleValue';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';

	interface Props {
		nationalitiesOptions: MultiSelectOption[];
		personTypesOptions?: MultiSelectOption[];
		attributesOptions?: MultiSelectOption[];
		formatParamString: (
			data: PeopleSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let {
		nationalitiesOptions,
		personTypesOptions = [],
		attributesOptions = [],
		formatParamString,
	}: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			PeopleSearchParam.Sort,
			PEOPLE_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			PeopleSearchParam.Sort,
			PEOPLE_SORT_OPTIONS
		);
	});

	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.Name,
		})
	);
	let types: MultiSelectOption[] = $state([]);
	$effect(() => {
		types = findMultiselectOptions(
			page.url.searchParams,
			PeopleSearchParam.Types,
			personTypesOptions
		);
	});
	let attributes: MultiSelectOption[] = $state([]);
	$effect(() => {
		attributes = findMultiselectOptions(
			page.url.searchParams,
			PeopleSearchParam.Attributes,
			attributesOptions
		);
	});
	let yearBirthRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.YearBirthRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let yearBirth = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.YearBirth,
		})
	);
	let yearDeathRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.YearDeathRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let yearDeath = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.YearDeath,
		})
	);
	let nationality: MultiSelectOption[] = $state([]);
	$effect(() => {
		nationality = findMultiselectOptions(
			page.url.searchParams,
			PeopleSearchParam.Nationality,
			nationalitiesOptions
		);
	});
	let addressCity = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.AddressCity,
		})
	);
	let addressCountry = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.AddressCountry,
		})
	);
	let owns = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.Owns,
		})
	);
	let ownsCategory = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.OwnsCategory,
		}) || OwnsWantsCategory.Artist
	);
	let wants = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.Wants,
		})
	);
	let wantsCategory = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: PeopleSearchParam.WantsCategory,
		}) || OwnsWantsCategory.Artist
	);

	const dataCyPrefix = 'search';

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (typeof window !== 'undefined' && to?.url.pathname !== Routes.People) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				const queryParamsObj = Object.fromEntries(
					new URLSearchParams(queryParams as string)
				);

				// showResults, ownsCategory, wantsCategory
				if (Object.keys(queryParamsObj).length > 3) {
					await setArteyeSearch(
						Searches.People,
						`${Routes.People}?${queryParams}`
					);
				}
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		name,
		types: getValuesFromSelectOptions(types),
		attributes: getValuesFromSelectOptions(attributes),
		nationality: getMultiselectSingleValue(nationality),
		yearBirthRange,
		yearBirth,
		yearDeathRange,
		yearDeath,
		addressCity,
		addressCountry,
		owns,
		ownsCategory,
		wants,
		wantsCategory,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		name = '';
		types = [];
		attributes = [];
		yearBirthRange = ValueFilterOperator.Equal;
		yearBirth = '';
		yearDeathRange = ValueFilterOperator.Equal;
		yearDeath = '';
		nationality = [];
		addressCity = '';
		addressCountry = '';
		owns = '';
		wants = '';
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search people</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.People}/new`}
			class="hidden lg:block"
		>
			Create New
		</LinkButton>
	</div>

	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-name`}
				name="name"
				placeholder="Enter name"
				label="Person name"
				bind:value={name}
				size="sm"
			/>
		</div>

		<MultiSelect
			name="type"
			dataCy={`${dataCyPrefix}-type`}
			label="Person type"
			bind:selected={types}
			placeholder="Type or select"
			options={personTypesOptions}
			class="col-span-1"
			size="sm"
		/>

		<MultiSelect
			name="type"
			dataCy={`${dataCyPrefix}-attributes`}
			label="Attributes"
			bind:selected={attributes}
			placeholder="Type or select"
			options={attributesOptions}
			class="col-span-1"
			size="sm"
		/>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Year of birth"
					dataCy={`${dataCyPrefix}-year-birth`}
					name="year_birth"
					bind:selectValue={yearBirthRange}
					bind:inputValue={yearBirth}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearBirthRange]}
					tooltip="Copy TBC"
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Year of death"
					dataCy={`${dataCyPrefix}-year-death`}
					name="year_birth"
					bind:selectValue={yearDeathRange}
					bind:inputValue={yearDeath}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearDeathRange]}
					tooltip="Copy TBC"
				/>
			</div>

			<MultiSelect
				name="nationality"
				dataCy={`${dataCyPrefix}-nationality`}
				label="Nationality"
				bind:selected={nationality}
				placeholder="Type or select"
				options={nationalitiesOptions}
				class="col-span-1"
				size="sm"
				maxSelect={1}
			/>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-address-city`}
					name="address_city"
					placeholder=""
					label="Address (city)"
					bind:value={addressCity}
					size="sm"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-address-country`}
					name="address_country"
					placeholder=""
					label="Address (country)"
					bind:value={addressCountry}
					size="sm"
				/>
			</div>
		</div>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Owns"
					dataCy={`${dataCyPrefix}-owns`}
					name="owns"
					bind:selectValue={ownsCategory}
					bind:inputValue={owns}
					options={OWNS_WANTS_OPTIONS}
					placeholder="Search notes"
					tooltip="Copy TBC"
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Wants"
					dataCy={`${dataCyPrefix}-wants`}
					name="wants"
					bind:selectValue={wantsCategory}
					bind:inputValue={wants}
					options={OWNS_WANTS_OPTIONS}
					placeholder="Search notes"
					tooltip="Copy TBC"
				/>
			</div>

			<MultiSelect
				name="sort_by"
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				bind:selected={sort}
				placeholder="Sort by"
				options={PEOPLE_SORT_OPTIONS}
				class="col-span-1"
				size="sm"
			/>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
