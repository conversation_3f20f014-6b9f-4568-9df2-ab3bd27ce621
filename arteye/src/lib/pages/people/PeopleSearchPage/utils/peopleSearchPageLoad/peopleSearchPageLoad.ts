import { PeopleSearchParam } from '../../constants/search';
import { PEOPLE_DEFAULT_SORT } from '../../constants/sort';
import { getPeopleFilter } from '../getPeopleFilter/getPeopleFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	PersonSearchInput,
	PersonSearchSortField,
} from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import type { PersonSearchQuery } from '$lib/custom-queries/__generated__/personSearch.generated';
import { PersonSearchDocument } from '$lib/custom-queries/__generated__/personSearch.generated';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { PeopleSearchPageLoadEvent } from '$routes/people/types';

export const peopleSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: PeopleSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;

	let res: PersonSearchQuery | null = null;

	const data = existingData || {};

	let people: PersonSearchQuery['personSearch']['data'] | null = null;

	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: PeopleSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filters = getPeopleFilter(searchParams);
		const offset = getOffset(searchParams);

		const sort = getSortWithDirection<PersonSearchSortField>(
			searchParams,
			PEOPLE_DEFAULT_SORT
		);

		const input: PersonSearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
			sort,
		};

		res = await gqlClientCustom.request(
			PersonSearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		people = res.personSearch.data || [];
		resultsCount = res.personSearchCount;
	}

	return {
		...parentData,
		...data,
		people,
		resultsCount,
		showResults,
		currentPage,
	};
};
