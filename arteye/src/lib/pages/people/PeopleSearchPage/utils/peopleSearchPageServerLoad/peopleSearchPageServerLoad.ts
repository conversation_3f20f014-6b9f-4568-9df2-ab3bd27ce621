// import { redirect } from '@sveltejs/kit';
// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
// import { ActivityCache } from '$lib/activityCache';
// import { CacheEntries } from '$lib/constants/cache';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import { gqlClient } from '$lib/gqlClient';
// import {
// 	GetEntityAttributeTypesDocument,
// 	type GetEntityAttributeTypesQuery,
// } from '$lib/queries/__generated__/getEntityAttributeTypes.generated';
// import {
// 	GetLocationsDocument,
// 	type GetLocationsQuery,
// } from '$lib/queries/__generated__/getLocations.generated';
// import {
// 	GetPersonTypesDocument,
// 	type GetPersonTypesQuery,
// } from '$lib/queries/__generated__/getPersonTypes.generated';
// import { LocationType } from '$lib/types/location-type';
// import type { PeopleSearchPageServerLoadEvent } from '$routes/people/types';

// export const peopleSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: PeopleSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.People];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	const authHeaders = getAuthorizationHeaders(data);

// 	const countriesReq = (async () => {
// 		const cachedCountries = ActivityCache.get(CacheEntries.Countries);
// 		if (cachedCountries) {
// 			return cachedCountries as GetLocationsQuery['location'];
// 		}

// 		const countriesRes = await gqlClient.request(
// 			GetLocationsDocument,
// 			{
// 				filter: { type: { key: { _eq: LocationType.Country } } },
// 				sort: ['name'],
// 				limit: -1,
// 			},
// 			authHeaders
// 		);

// 		const locations = countriesRes?.location || [];
// 		ActivityCache.set(CacheEntries.Countries, locations);
// 		return locations;
// 	})();

// 	const personTypesReq = (async () => {
// 		const cachedPersonTypes = ActivityCache.get(CacheEntries.PersonTypes);
// 		if (cachedPersonTypes) {
// 			return cachedPersonTypes as GetPersonTypesQuery['person_type'];
// 		}

// 		const personTypesRes = await gqlClient.request(
// 			GetPersonTypesDocument,
// 			{},
// 			authHeaders
// 		);

// 		const personTypes = personTypesRes?.person_type || [];
// 		ActivityCache.set(CacheEntries.PersonTypes, personTypes);
// 		return personTypes;
// 	})();

// 	const attributeTypesReq = (async () => {
// 		const cachedAttributeTypes = ActivityCache.get(CacheEntries.AttributeTypes);
// 		if (cachedAttributeTypes) {
// 			return cachedAttributeTypes as GetEntityAttributeTypesQuery['entity_attribute_type'];
// 		}

// 		const attributeTypesRes = await gqlClient.request(
// 			GetEntityAttributeTypesDocument,
// 			{},
// 			authHeaders
// 		);

// 		const attributeTypes = attributeTypesRes?.entity_attribute_type || [];
// 		ActivityCache.set(CacheEntries.AttributeTypes, attributeTypes);
// 		return attributeTypes;
// 	})();

// 	const [countries, attributeTypes, personTypes] = await Promise.all([
// 		countriesReq,
// 		attributeTypesReq,
// 		personTypesReq,
// 	]);

// 	return {
// 		...data,
// 		attributeTypes,
// 		countries,
// 		personTypes,
// 	};
// };
