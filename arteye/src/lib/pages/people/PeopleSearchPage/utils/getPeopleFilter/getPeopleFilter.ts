import { PeopleSearchParam } from '../../constants/search';
import { OwnsWantsCategory } from '../../PeopleSearchForm';
import type {
	Entity_Attribute_Type_Enum,
	IntValueFilter,
	Person_Type_Enum,
	PersonSearchFilters,
	ValueFilterOperator,
} from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';

export const getPeopleFilter = (
	searchParams: URLSearchParams
): PersonSearchFilters => {
	let filters: PersonSearchFilters = {};

	const name = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Name,
	});

	const types = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Types,
	});

	const attributes = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Attributes,
	});

	const yearBirth = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.YearBirth,
	});

	const yearBirthRange = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.YearBirthRange,
	});

	const yearDeath = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.YearDeath,
	});

	const yearDeathRange = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.YearDeathRange,
	});

	const nationality = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Nationality,
	});

	const addressCity = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.AddressCity,
	});

	const addressCountry = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.AddressCountry,
	});

	const wants = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Wants,
	});

	const wantsCategory = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.WantsCategory,
	});

	const owns = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.Owns,
	});

	const ownsCategory = getDecodedSearchParam({
		searchParams,
		key: PeopleSearchParam.OwnsCategory,
	});

	if (name) {
		filters = {
			...filters,
			nameOrId: name,
		};
	}

	if (types) {
		filters = {
			...filters,
			type: types.split(PARAM_SEPARATOR).map((type) => {
				return type as Person_Type_Enum;
			}),
		};
	}

	if (attributes) {
		filters = {
			...filters,
			attributes: attributes.split(PARAM_SEPARATOR).map((attribute) => {
				return attribute as Entity_Attribute_Type_Enum;
			}),
		};
	}

	if (yearBirth) {
		filters = {
			...filters,
			yearOfBirth: getValueFilter({
				value: yearBirth,
				range: yearBirthRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (yearDeath) {
		filters = {
			...filters,
			yearOfDeath: getValueFilter({
				value: yearDeath,
				range: yearDeathRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (nationality) {
		filters = {
			...filters,
			nationality,
		};
	}

	if (addressCity) {
		filters = {
			...filters,
			location: {
				...filters.location,
				cityNameOrCode: addressCity,
			},
		};
	}

	if (addressCountry) {
		filters = {
			...filters,
			location: {
				...filters.location,
				countryNameOrCode: addressCountry,
			},
		};
	}

	if (wants) {
		if (wantsCategory === OwnsWantsCategory.Artist) {
			filters = {
				...filters,
				wants: {
					artistNameOrId: wants,
				},
			};
		}

		if (wantsCategory === OwnsWantsCategory.Artwork) {
			filters = {
				...filters,
				wants: {
					artworkNameOrId: wants,
				},
			};
		}

		if (wantsCategory === OwnsWantsCategory.ArtworkSeries) {
			filters = {
				...filters,
				wants: {
					artworkSeriesTitleOrId: wants,
				},
			};
		}
	}

	if (owns) {
		if (ownsCategory === OwnsWantsCategory.Artist) {
			filters = {
				...filters,
				owns: {
					artistNameOrId: owns,
				},
			};
		}

		if (ownsCategory === OwnsWantsCategory.Artwork) {
			filters = {
				...filters,
				owns: {
					artworkNameOrId: owns,
				},
			};
		}

		if (ownsCategory === OwnsWantsCategory.ArtworkSeries) {
			filters = {
				...filters,
				owns: {
					artworkSeriesTitleOrId: owns,
				},
			};
		}
	}

	return filters;
};
