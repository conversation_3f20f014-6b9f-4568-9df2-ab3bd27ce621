<script lang="ts">
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { PersonSearchSortField } from '$gql/types-custom';
	import { TableHeaderSortArrow } from '$lib/components/TableHeaderSortArrow';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { PARAM_SEPARATOR } from '$lib/constants/params';
	import { Routes } from '$lib/constants/routes';
	import type { PersonSearchQuery } from '$lib/custom-queries/__generated__/personSearch.generated';
	import { SearchParam } from '$lib/types/types';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import type { PeopleSearchPageData } from '$routes/people/types';

	let loading = false;

	let data = $derived(getPageData<PeopleSearchPageData>(page.data));

	let resultsCount = $derived(data.resultsCount);
	let people = $derived(data.people);
	let searchParams = $derived(page.url.searchParams);
	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
		sort?: {
			asc: string;
			desc: string;
		};
	}

	interface Row {
		person: PersonSearchQuery['personSearch']['data'][0];
		fields: {
			[HeaderFieldName.Name]: string;
			[HeaderFieldName.Nationality]: string;
			[HeaderFieldName.NumberOfActivities]: number;
			[HeaderFieldName.Action]: '';
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.Name,
			title: 'Person name',
			sort: {
				asc: PersonSearchSortField.Name,
				desc: getDescendingDirection(PersonSearchSortField.Name),
			},
		},
		{
			fieldName: HeaderFieldName.Nationality,
			title: 'Nationality',
			sort: {
				asc: PersonSearchSortField.Nationality,
				desc: getDescendingDirection(PersonSearchSortField.Nationality),
			},
		},
		{
			fieldName: HeaderFieldName.Types,
			title: 'Person types',
			sort: {
				asc: PersonSearchSortField.PersonType,
				desc: getDescendingDirection(PersonSearchSortField.PersonType),
			},
		},
		{
			fieldName: HeaderFieldName.NumberOfActivities,
			title: 'No. of activities',
			sort: {
				asc: PersonSearchSortField.NumberOfActivities,
				desc: getDescendingDirection(PersonSearchSortField.NumberOfActivities),
			},
		},
		{
			fieldName: HeaderFieldName.Action,
			title: '',
		},
	];

	let rows = $derived(
		(people || []).map((person) => {
			const sortParams = getDecodedSearchParam({
				searchParams,
				key: SearchParam.Sort,
			}).split(PARAM_SEPARATOR);

			const nationality =
				(person?.nationalities || [])
					.filter(
						(nationality) =>
							nationality?.country?.country_nationality ||
							nationality?.country?.name
					)
					.map(
						(nationality) =>
							nationality?.country?.country_nationality ||
							nationality?.country?.name ||
							''
					)
					.sort((nationalityA, nationalityB) => {
						if (sortParams.includes(`-${PersonSearchSortField.Nationality}`)) {
							if (nationalityA > nationalityB) return -1;
							if (nationalityA < nationalityB) return 1;
						}

						if (nationalityA < nationalityB) return -1;
						if (nationalityA > nationalityB) return 1;
						return 0;
					})
					.join(', ') || '';

			return {
				person,
				fields: {
					name: `${person?.entity?.name} ${getYearBirthDeathString({
						yearBirth: person?.year_birth || 0,
						yearDeath: person?.year_death || 0,
					})}`,
					nationality,
					types:
						person?.type
							?.map((type) => type?.person_type_key?.name || '')
							.join(', ') || '',
					numberOfActivities:
						person?.entity?.aggregations?.[0]?.activity_count || 0,
					action: '',
				},
			};
		}) satisfies Row[]
	);

	const dataCyPrefix = 'table';

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		searchParams.set(SearchParam.Page, page.toString());

		const searchParamString = searchParams.toString();

		goto(`?${searchParamString}`, { invalidateAll: true });
	};

	const handleRowClick = (peopleId: string) => {
		goto(`${Routes.People}/${peopleId}`);
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border border-b-0 border-gray-200 bg-white p-4"
>
	<Txt variant="h6">
		{getPaginationResultsText({
			currentPage,
			total: resultsCount,
			pageSize: TABLE_PAGE_SIZE,
		})}
	</Txt>
</div>

<div class="w-full overflow-x-auto mb-4">
	<div class="min-w-[1000px]">
		{#if people !== null}
			<table class=" w-full table-fixed border-collapse rounded-b-md bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader dataCy={dataCyPrefix}>
							{#snippet custom()}
								{#if header.sort}
									<TableHeaderSortArrow
										sortParamKey={SearchParam.Sort}
										asc={header.sort.asc}
										desc={header.sort.desc}
										{searchParams}
									>
										{header.title}
									</TableHeaderSortArrow>
								{:else}
									<Txt variant="label3">{header.title}</Txt>
								{/if}
							{/snippet}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#if loading}
						<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
							<CircularProgress dataCy={dataCyPrefix} />
						</TableNoResults>
					{/if}

					{#if !loading}
						{#each rows as row, i}
							<TableRow
								index={i}
								dataCy={dataCyPrefix}
								onclick={() => handleRowClick(row.person?.id as string)}
							>
								{#each headers as header}
									{@const value = row.fields[header.fieldName]}

									{@const person = row.person}

									{#if header.fieldName === HeaderFieldName.Action}
										<TableActionCell dataCy={dataCyPrefix} class="py-0">
											<Button
												class="bg-transparent border-none px-0"
												size="sm"
												variant="tertiary"
												dataCy={`${dataCyPrefix}-cell`}
												onclick={(e) => {
													e?.stopPropagation();
													navigator.clipboard.writeText(
														`${person?.reference_id}`
													);
												}}
											>
												<CopyIcon />
											</Button>

											<LinkButton
												dataCy={`${dataCyPrefix}-cell`}
												variant="tertiary"
												href={`${Routes.People}/${person?.id}`}
												size="sm"
												buttonProps={{ class: 'bg-transparent border-none' }}
											>
												<ChevronRightIcon class="h-[14px] w-[14px]" />
											</LinkButton>
										</TableActionCell>
									{:else}
										<TableCell
											dataCy={dataCyPrefix}
											content={value}
											class="py-0"
										>
											{value}
										</TableCell>
									{/if}
								{/each}
							</TableRow>
						{/each}

						{#if rows.length === 0}
							<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
								No results found
							</TableNoResults>
						{/if}
					{/if}
				</TableBody>
			</table>
		{/if}
	</div>
</div>

{#key resultsCount}
	{#key currentPage}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				{#key currentPage}
					{#key people?.length}
						<Pagination
							{currentPage}
							total={resultsCount}
							limit={TABLE_PAGE_SIZE}
							dataCy={dataCyPrefix}
							onClick={handlePaginationClick}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	{/key}
{/key}
