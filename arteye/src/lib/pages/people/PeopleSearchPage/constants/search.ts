import { SearchParam } from '$lib/types/types';

export enum PeopleSearchParam {
	ShowResults = SearchParam.ShowResults,
	Sort = SearchParam.Sort,
	Name = 'name',
	Types = 'types',
	Attributes = 'attributes',
	YearBirth = 'yearBirth',
	YearBirthRange = 'yearBirthRange',
	YearDeath = 'yearDeath',
	YearDeathRange = 'yearDeathRange',
	Nationality = 'nationality',
	AddressCity = 'addressCity',
	AddressCountry = 'addressCountry',
	Owns = 'owns',
	OwnsCategory = 'ownsCategory',
	Wants = 'wants',
	WantsCategory = 'wantsCategory',
}
