import { PersonSearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const PEOPLE_SORT_OPTIONS = sortByField(
	[
		{ label: 'Name (A-Z)', value: PersonSearchSortField.Name },
		{
			label: 'Name (Z-A)',
			value: getDescendingDirection(PersonSearchSortField.Name),
		},
		{ label: 'Year of birth (Asc)', value: PersonSearchSortField.YearOfBirth },
		{
			label: 'Year of birth (Desc)',
			value: getDescendingDirection(PersonSearchSortField.YearOfBirth),
		},
		{ label: 'Nationality (A-Z)', value: PersonSearchSortField.Nationality },
		{
			label: 'Nationality (Z-A)',
			value: getDescendingDirection(PersonSearchSortField.Nationality),
		},
		{ label: 'Person type (A-Z)', value: PersonSearchSortField.PersonType },
		{
			label: 'Person type (Z-A)',
			value: getDescendingDirection(PersonSearchSortField.PersonType),
		},
		{ label: 'Date created (Asc)', value: PersonSearchSortField.DateCreated },
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(PersonSearchSortField.DateCreated),
		},
		{ label: 'Date updated (Asc)', value: PersonSearchSortField.DateUpdated },
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(PersonSearchSortField.DateUpdated),
		},
	],
	'label'
);

export const PEOPLE_DEFAULT_SORT = PersonSearchSortField.Name;
