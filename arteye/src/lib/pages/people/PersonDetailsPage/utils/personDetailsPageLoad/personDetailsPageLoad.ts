import { error } from '@sveltejs/kit';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetPersonDocument } from '$lib/queries/__generated__/getPerson.generated';
import { GetRelationshipsDocument } from '$lib/queries/__generated__/getRelationships.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { PersonDetailsPageLoadEvent } from '$routes/people/[id]/types';

export const personDetailsPageLoad = async ({
	parent,
	params,
}: PersonDetailsPageLoadEvent) => {
	const data = await parent();
	const authHeaders = getAuthorizationHeaders(data);
	const personId = params.id;

	const personReq = (async () => {
		if (!personId) {
			return null;
		}

		const res = await gqlClient.request(
			GetPersonDocument,
			{
				id: personId,
			},
			authHeaders
		);

		return res.person_by_id;
	})();

	const pipelineSourceReq = (async () => {
		if (personId === PARAM_NEW || !personId) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{
				id: personId,
				collection: PipelineSourceItem.Person,
			},
			authHeaders
		);

		return res.getDatasource?.data_source;
	})();

	const relationshipsReq = (async () => {
		if (!personId) {
			return [];
		}

		const res = await gqlClient.request(
			GetRelationshipsDocument,
			{
				filter: {
					_and: [
						{ status: { key: { _neq: Status_Enum.Archived } } },
						{
							_or: [
								{
									from_entity: { person: { id: { _eq: personId } } },
								},
								{
									to_entity: { person: { id: { _eq: personId } } },
								},
							],
						},
					],
				},
			},
			authHeaders
		);

		return res.relationship;
	})();

	const legacyIdReq =
		personId === PARAM_NEW || isOnDev() || !personId
			? { getLegacyId: { legacyId: '' } }
			: gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'person',
						id: personId,
					},
					getAuthorizationHeaders(data)
				);

	const [
		person,
		relationships,
		pipelineSource,
		legacyIdRes,
		{ dropzoneUrlDialogForm },
	] = await Promise.all([
		personReq,
		relationshipsReq,
		pipelineSourceReq,
		legacyIdReq,
		getDropzoneUrlDialogSuperform(),
	]);

	const legacyId = legacyIdRes?.getLegacyId?.legacyId;

	if (!person && params.id) {
		error(404, 'Person not found');
	}

	return {
		...data,
		legacyId,
		person,
		relationships,
		pipelineSource,
		dropzoneUrlDialogForm,
	};
};
