<script lang="ts">
	import classNames from 'classnames';
	import { PersonDetailsCard } from '../../../components/details-pages/PersonDetailsCard';
	import { type PersonalDetailsForm } from '../../../components/details-pages/PersonDetailsCard/PersonDetailsCard.svelte';
	import { goto, invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Accordion } from '$global/components/Accordion';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Link } from '$global/components/Link';
	import { LinkButton } from '$global/components/LinkButton';
	import { Tabs } from '$global/components/Tabs';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { EntityCards } from '$lib/components/details-pages/EntityCards';
	import { type AddressesType } from '$lib/components/details-pages/EntityCards/AddressesCard/AddressesCard.svelte';
	import { type CollectionNotesType } from '$lib/components/details-pages/EntityCards/CollectionNotesCard';
	import { type NotesType } from '$lib/components/details-pages/EntityCards/NotesCard/NotesCard.svelte';
	import {
		type AdditionalImagesType,
		type ProfileImageType,
	} from '$lib/components/details-pages/EntityCards/ProfileImageCard';
	import type { RelationshipItem } from '$lib/components/details-pages/EntityCards/RelationshipsCard/RelationshipRow/types';
	import { InfoLabel } from '$lib/components/InfoLabel';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { areContactDetailsValid } from '$lib/utils/areContactDetailsValid/areContactDetailsValid';
	import { areEntityAddressesValid } from '$lib/utils/areEntityAddressesValid/areEntityAddressesValid';
	import { areEntityCollectionNotesValid } from '$lib/utils/areEntityCollectionNotesValid/areEntityCollectionNotesValid';
	import { areEntityNotesValid } from '$lib/utils/areEntityNotesValid/areEntityNotesValid';
	import { areEntityRelationshipsValid } from '$lib/utils/areEntityRelationshipsValid/areEntityRelationshipsValid';
	import {
		formatPersonalDetailsForm,
		formatProfileImage,
		formatAdditionalImages,
		formatContactDetailsRows,
		formatAddresses,
		formatEntityNotes,
		formatRelationships,
		formatCollectionEntityNotes,
		formatCurrentEntity,
	} from '$lib/utils/entityFormatters/entityFormatters';
	import { getQueryStrings } from '$lib/utils/getQueryStrings/getQueryStrings';
	import { archiveEntity } from '$lib/utils/mutation-handlers/archiveEntity/archiveEntity';
	import { archivePerson } from '$lib/utils/mutation-handlers/archivePerson/archivePerson';
	import { createEntity } from '$lib/utils/mutation-handlers/createEntity/createEntity';
	import { mutateEntity } from '$lib/utils/mutation-handlers/mutateEntity/mutateEntity';
	import { mutateEntityImages } from '$lib/utils/mutation-handlers/mutateEntityImages/mutateEntityImages';
	import { mutatePersonalDetails } from '$lib/utils/mutation-handlers/mutatePersonalDetails/mutatePersonalDetails';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	let loading = $state(false);
	let deleting = $state(false);

	const INITIAL_CHANGED_STATE = {
		personalDetailsForm: false,
		profileImage: false,
		additionalImages: false,
		contactDetailsRows: false,
		entityAddresses: false,
		entityNotes: false,
		collectionEntityNotes: false,
		relationships: false,
	};

	let data = $derived(getPageData<PersonDetailsPageData>(page.data));

	let changed = $state(INITIAL_CHANGED_STATE);

	let person = $derived(data.person);
	let legacyId = $derived(data.legacyId);
	let id = $derived(page.params.id);
	let pipelineSource = $derived(data.pipelineSource);
	let personalDetailsForm = $state(
		(() => formatPersonalDetailsForm(person, false))()
	) as ReturnType<typeof formatPersonalDetailsForm>;
	$effect(() => {
		personalDetailsForm = formatPersonalDetailsForm(person, false);
	});
	let profileImage = $state(
		(() => formatProfileImage(person?.entity, page.data.user.access_token))()
	) as ReturnType<typeof formatProfileImage>;
	$effect(() => {
		profileImage = formatProfileImage(
			person?.entity,
			page.data.user.access_token
		);
	});

	let additionalImages = $state(
		(() =>
			formatAdditionalImages(person?.entity, page.data.user.access_token))()
	) as ReturnType<typeof formatAdditionalImages>;
	$effect(() => {
		additionalImages = formatAdditionalImages(
			person?.entity,
			page.data.user.access_token
		);
	});

	let dropzoneUrlDialogForm = $derived(data.dropzoneUrlDialogForm);

	let contactDetailsRows = $state(
		(() => formatContactDetailsRows(person?.entity))()
	) as ReturnType<typeof formatContactDetailsRows>;
	$effect(() => {
		contactDetailsRows = formatContactDetailsRows(person?.entity);
	});
	let entityAddresses = $state(
		(() => formatAddresses(person?.entity))()
	) as ReturnType<typeof formatAddresses>;
	$effect(() => {
		entityAddresses = formatAddresses(person?.entity);
	});
	let entityNotes = $state(
		(() => formatEntityNotes(person?.entity))()
	) as ReturnType<typeof formatEntityNotes>;
	$effect(() => {
		entityNotes = formatEntityNotes(person?.entity);
	});
	let collectionEntityNotes = $state(
		(() => formatCollectionEntityNotes(person?.entity))()
	) as ReturnType<typeof formatCollectionEntityNotes>;
	$effect(() => {
		collectionEntityNotes = formatCollectionEntityNotes(person?.entity);
	});
	let relationships = $state(
		(() =>
			formatRelationships({
				relationships: data.relationships,
				entity: person?.entity,
			}))()
	) as ReturnType<typeof formatRelationships>;
	$effect(() => {
		relationships = formatRelationships({
			relationships: data.relationships,
			entity: person?.entity,
		});
	});
	let currentEntity = $derived(formatCurrentEntity(person?.entity));

	let crumbs = $derived([
		{ label: 'Search People', href: getQueryStrings(Routes.People) },
		{
			label: person
				? `${person?.first_name}${
						person?.last_name ? ` ${person?.last_name}` : ''
					}`
				: 'New Person',
		},
	]);

	let showSaveBar = $state(false);
	let activeTab = $state(0);

	const tabs = [
		{
			id: '1',
			title: 'Person details',
		},
		{
			id: '2',
			title: 'Activities',
		},
	];

	const dataCy = 'person-details';

	const handleAdditionalImagesChange = (images: AdditionalImagesType) => {
		additionalImages = images;
		showSaveBar = true;
		changed.additionalImages = true;
	};

	const handleProfileImageChange = (image: ProfileImageType) => {
		profileImage = image;
		showSaveBar = true;
		changed.profileImage = true;
	};

	const handlePersonDetailsChange = (person: PersonalDetailsForm) => {
		personalDetailsForm = person;
		showSaveBar = true;
		changed.personalDetailsForm = true;
	};

	const handleEntityAddressesChange = (addresses: AddressesType) => {
		entityAddresses = addresses;
		showSaveBar = true;
		changed.entityAddresses = true;
	};

	const handleEntityNotesChange = (notes: NotesType) => {
		entityNotes = notes;
		showSaveBar = true;
		changed.entityNotes = true;
	};

	const handleCollectionEntityNotesChange = (
		collectionNotes: CollectionNotesType
	) => {
		collectionEntityNotes = collectionNotes;
		showSaveBar = true;
		changed.collectionEntityNotes = true;
	};

	const handleContactDetailsChange = (rows: typeof contactDetailsRows) => {
		contactDetailsRows = rows;
		showSaveBar = true;
		changed.contactDetailsRows = true;
	};

	const handleRelationshipsChange = (newRelationships: RelationshipItem[]) => {
		relationships = newRelationships;
		showSaveBar = true;
		changed.relationships = true;
	};

	const handleClickDeletePerson = async () => {
		try {
			deleting = true;
			const headers = getAuthorizationHeaders(data);
			const entityId = `${person?.entity?.id}`;
			const entityName = `${person?.entity?.name}`;
			const entityType = `${person?.entity?.type?.key}`;
			const personId = `${person?.id}`;

			await archiveEntity({ entityId, entityName, entityType, headers });
			await archivePerson({ personId, headers });

			showToast({
				variant: 'success',
				message: 'This person has been successfully deleted',
			});

			goto(`${Routes.People}`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this person deletion. Please contact the support team.',
			});
		} finally {
			deleting = false;
		}
	};

	const create = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName = [
				personalDetailsForm?.first_name,
				personalDetailsForm?.middle_name,
				personalDetailsForm?.last_name,
			]
				.filter(Boolean)
				.join(' ');

			const entityType = 'person';

			let personId = '';

			const { entityId } = await createEntity({
				entityName,
				entityType,
				headers,
			});

			const requests: {
				images: ReturnType<typeof mutateEntityImages> | null;
				entity: ReturnType<typeof mutateEntity> | null;
			} = {
				images: null,
				entity: null,
			};

			const res = await mutatePersonalDetails({
				personId,
				entityId,
				entityName,
				entityType,
				personalDetailsForm,
				originalPersonTypes: person?.type,
				originalNationalities: person?.nationalities,
				headers,
			});

			personId = res.personId;

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				changed: {
					...changed,
					attributes: changed.personalDetailsForm,
				},
			});

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: `This person has been created successfully.`,
			});

			await goto(`${Routes.People}/${personId}`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while creating this person. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const update = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName = person?.entity?.name || '';

			const entityType = person?.entity?.type?.key || 'person';

			const personId = person?.id || '';
			const entityId = person?.entity?.id || '';

			const requests: {
				personalDetails: ReturnType<typeof mutatePersonalDetails> | null;
				images: ReturnType<typeof mutateEntityImages> | null;
				entity: ReturnType<typeof mutateEntity> | null;
			} = {
				personalDetails: null,
				images: null,
				entity: null,
			};

			if (changed.personalDetailsForm) {
				requests.personalDetails = mutatePersonalDetails({
					personId,
					entityId,
					entityName,
					entityType,
					personalDetailsForm,
					originalPersonTypes: person?.type,
					originalNationalities: person?.nationalities,
					headers,
				});
			}

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				changed: {
					...changed,
					attributes: changed.personalDetailsForm,
				},
			});

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: `This person has been updated successfully.`,
			});

			await invalidateAll();

			changed = INITIAL_CHANGED_STATE;
		} catch (e) {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this person update. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const handleSaveClick = () => {
		let personId = person?.id || '';

		loading = true;

		if (personId) {
			update();
		} else {
			create();
		}
	};

	const originalEntityAddresses = (page.data as PersonDetailsPageData)?.person
		?.entity?.addresses;

	const originalCollectionEntityNotes = (page.data as PersonDetailsPageData)
		?.person?.entity?.collection_notes;
</script>

<div
	class={classNames('pb-12', { 'pointer-events-none': loading || deleting })}
>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if person}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				{#if legacyId}
					<InfoLabel title="Legacy ID" value={legacyId} />
				{/if}

				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&association=${person?.reference_id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					class="h-[2rem]"
					size="sm"
					dataCy={`${dataCy}-view-artworks`}
					variant="secondary"
					disabled={loading || deleting}
				>
					view artworks
				</LinkButton>

				<Button
					onclick={() => {
						navigator.clipboard.writeText(`${person?.entity?.reference_id}`);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy person id
				</Button>

				<Button
					onclick={handleClickDeletePerson}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if data.person}
		<CreateUpdate
			updateHistory={data.person}
			pipelineSource={data.pipelineSource}
			class="mb-2 block mt-[-8px]"
		/>
	{/if}

	<Tabs {dataCy} bind:activeTab {tabs} fullLine />

	{#if !activeTab}
		<Accordion class="mb-4" multiple>
			<PersonDetailsCard
				{personalDetailsForm}
				onChange={handlePersonDetailsChange}
				nationalities={data.user?.dropdowns.countries || []}
				genders={data.user?.dropdowns.genders || []}
				personTypes={data.user?.dropdowns.personTypes || []}
				attributeTypes={data.user?.dropdowns.attributeTypes || []}
			/>
		</Accordion>

		<EntityCards
			{currentEntity}
			{profileImage}
			onProfileImageChange={handleProfileImageChange}
			{additionalImages}
			onAdditionalImagesChange={handleAdditionalImagesChange}
			{contactDetailsRows}
			{originalEntityAddresses}
			{originalCollectionEntityNotes}
			onContactDetailsChange={handleContactDetailsChange}
			{entityAddresses}
			onCollectionEntityNotesChange={handleCollectionEntityNotesChange}
			{entityNotes}
			onEntityNotesChange={handleEntityNotesChange}
			{relationships}
			onRelationshipsChange={handleRelationshipsChange}
			{collectionEntityNotes}
			onEntityAddressesChange={handleEntityAddressesChange}
			{dropzoneUrlDialogForm}
		/>
	{/if}

	{#if activeTab}
		<Link
			target="_blank"
			rel="noopener noreferrer"
			href={`${Routes.ArtworksAndActivities}?showResults=true&association=${person?.reference_id}`}
		>
			<Button dataCy="show-activities" size="md" variant="secondary">
				Show activities
			</Button>
		</Link>
	{/if}
</div>

<PageSaveBar
	{loading}
	disabled={deleting ||
		loading ||
		!personalDetailsForm.first_name ||
		!personalDetailsForm.last_name ||
		`${personalDetailsForm.net_worth_usd || ''}`.endsWith('.') ||
		!areContactDetailsValid(contactDetailsRows) ||
		!areEntityAddressesValid(entityAddresses) ||
		!areEntityNotesValid(entityNotes) ||
		!areEntityCollectionNotesValid(collectionEntityNotes) ||
		!areEntityRelationshipsValid(relationships)}
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
/>
