<script module lang="ts">
	export interface OrganisationsSearchFormFields {
		[OrganisationsSearchParam.Sort]: string[];
		[OrganisationsSearchParam.Name]: string;
		[OrganisationsSearchParam.AddressCity]: string;
		[OrganisationsSearchParam.AddressCountry]: string;
		[OrganisationsSearchParam.Types]: string[];
		[OrganisationsSearchParam.Attributes]: string[];
		[OrganisationsSearchParam.YearFounded]: string;
		[OrganisationsSearchParam.YearFoundedRange]: string;
		[OrganisationsSearchParam.YearDissolved]: string;
		[OrganisationsSearchParam.YearDissolvedRange]: string;
	}
</script>

<script lang="ts">
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { OrganisationsSearchParam } from '../constants/search';
	import { ORGANISATION_SORT_OPTIONS } from '../constants/sort';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';

	interface Props {
		typesOptions?: MultiSelectOption[];
		attributesOptions?: MultiSelectOption[];
		formatParamString: (
			data: OrganisationsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let {
		typesOptions = [],
		attributesOptions = [],
		formatParamString,
	}: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			OrganisationsSearchParam.Sort,
			ORGANISATION_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			OrganisationsSearchParam.Sort,
			ORGANISATION_SORT_OPTIONS
		);
	});

	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.Name,
		})
	);
	let addressCity = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.AddressCity,
		})
	);
	let addressCountry = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.AddressCountry,
		})
	);

	let types: MultiSelectOption[] = $state([]);
	$effect(() => {
		types = findMultiselectOptions(
			page.url.searchParams,
			OrganisationsSearchParam.Types,
			typesOptions
		);
	});

	let attributes: MultiSelectOption[] = $state([]);
	$effect(() => {
		attributes = findMultiselectOptions(
			page.url.searchParams,
			OrganisationsSearchParam.Attributes,
			attributesOptions
		);
	});

	let yearFoundedRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.YearFoundedRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let yearFounded = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.YearFounded,
		})
	);
	let yearDissolvedRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.YearDissolvedRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let yearDissolved = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: OrganisationsSearchParam.YearDissolved,
		})
	);

	const dataCyPrefix = 'search';

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url.pathname !== Routes.Organisations
			) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				await setArteyeSearch(
					Searches.Organisation,
					`${Routes.Organisations}?${queryParams}`
				);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		name,
		addressCity,
		addressCountry,
		types: getValuesFromSelectOptions(types),
		attributes: getValuesFromSelectOptions(attributes),
		yearFoundedRange,
		yearFounded,
		yearDissolvedRange,
		yearDissolved,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		name = '';
		types = [];
		attributes = [];
		yearFoundedRange = ValueFilterOperator.Equal;
		yearFounded = '';
		yearDissolvedRange = ValueFilterOperator.Equal;
		yearDissolved = '';
		addressCity = '';
		addressCountry = '';
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search organisations</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.Organisations}/new`}
			class="hidden lg:block"
		>
			Create New
		</LinkButton>
	</div>

	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-organisation-name`}
				name="organisation-name"
				placeholder="Enter name"
				label="Organisation name"
				bind:value={name}
				size="sm"
			/>
		</div>

		<MultiSelect
			name="type"
			dataCy={`${dataCyPrefix}-type`}
			label="Organisation type"
			bind:selected={types}
			placeholder="Type or select"
			options={typesOptions}
			class="col-span-1"
			size="sm"
		/>

		<MultiSelect
			name="type"
			dataCy={`${dataCyPrefix}-attributes`}
			label="Attributes"
			bind:selected={attributes}
			placeholder="Type or select"
			options={attributesOptions}
			class="col-span-1"
			size="sm"
		/>

		<div class="col-span-5 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Year founded"
					dataCy={`${dataCyPrefix}-year-founded`}
					name="year_founded"
					bind:selectValue={yearFoundedRange}
					bind:inputValue={yearFounded}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearFoundedRange]}
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Year dissolved"
					dataCy={`${dataCyPrefix}-year-dissolved`}
					name="year_dissolved"
					bind:selectValue={yearDissolvedRange}
					bind:inputValue={yearDissolved}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[yearDissolvedRange]}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-address-city`}
					name="address_city"
					placeholder=""
					label="Address (city)"
					bind:value={addressCity}
					size="sm"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-address-country`}
					name="address_country"
					placeholder=""
					label="Address (country)"
					bind:value={addressCountry}
					size="sm"
				/>
			</div>
			<div class="col-span-1">
				<MultiSelect
					name="sort_by"
					dataCy={`${dataCyPrefix}-sort-by`}
					label="Sort by"
					bind:selected={sort}
					placeholder="Sort by"
					options={ORGANISATION_SORT_OPTIONS}
					class="col-span-1"
					size="sm"
				/>
			</div>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
