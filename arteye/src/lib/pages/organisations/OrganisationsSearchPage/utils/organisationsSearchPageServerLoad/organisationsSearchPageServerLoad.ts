// import { redirect } from '@sveltejs/kit';
// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import { gqlClient } from '$lib/gqlClient';
// import { GetEntityAttributeTypesDocument } from '$lib/queries/__generated__/getEntityAttributeTypes.generated';
// import { GetOrganisationTypesDocument } from '$lib/queries/__generated__/getOrganisationTypes.generated';
// import type { OrganisationsSearchPageServerLoadEvent } from '$routes/organisations/types';

// export const organisationsSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: OrganisationsSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Organisation];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	const authHeaders = getAuthorizationHeaders(data);

// 	const organisationTypesReq = gqlClient.request(
// 		GetOrganisationTypesDocument,
// 		{},
// 		authHeaders
// 	);

// 	const attributeTypesReq = gqlClient.request(
// 		GetEntityAttributeTypesDocument,
// 		{},
// 		authHeaders
// 	);

// 	const [attributeTypesRes, organisationTypesRes] = await Promise.all([
// 		attributeTypesReq,
// 		organisationTypesReq,
// 	]);

// 	const attributeTypes = attributeTypesRes?.entity_attribute_type;
// 	const organisationTypes = organisationTypesRes?.organisation_type;

// 	return {
// 		...data,
// 		attributeTypes,
// 		organisationTypes,
// 	};
// };
