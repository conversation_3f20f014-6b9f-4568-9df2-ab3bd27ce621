import { OrganisationsSearchParam } from '../../constants/search';
import { ORGANISATION_DEFAULT_SORT } from '../../constants/sort';
import { getOrganisationsFilter } from '../getOrganisationsFilter/getOrganisationsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	OrganisationSearchInput,
	OrganisationSearchSortField,
} from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import type { OrganisationSearchQuery } from '$lib/custom-queries/__generated__/organisationSearch.generated';
import { OrganisationSearchDocument } from '$lib/custom-queries/__generated__/organisationSearch.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import type { GetOrganisationsQuery } from '$lib/queries/__generated__/getOrganisations.generated';
import { GetOrganisationsDocument } from '$lib/queries/__generated__/getOrganisations.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import { getSort } from '$lib/utils/getSort/getSort';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { OrganisationsSearchPageLoadEvent } from '$routes/organisations/types';

export const organisationsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: OrganisationsSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;
	const data = existingData || {};

	let res: OrganisationSearchQuery | null = null;
	let organisations:
		| OrganisationSearchQuery['organisationSearch']['data']
		| null = null;
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: OrganisationsSearchParam.ShowResults,
		}) === StringBoolean.True;

	// if (showResults) {
	// 	const filter = getOrganisationsFilter(searchParams);
	// 	const sort = getSort(searchParams, ORGANISATION_DEFAULT_SORT);
	// 	const offset = getOffset(searchParams);

	// 	res = await gqlClient.request(
	// 		GetOrganisationsDocument,
	// 		{
	// 			filter,
	// 			sort,
	// 			limit: TABLE_PAGINATION_LIMIT,
	// 			offset,
	// 		},
	// 		{
	// 			authorization,
	// 		}
	// 	);
	// }

	if (showResults) {
		const filters = getOrganisationsFilter(searchParams);
		const offset = getOffset(searchParams);

		const sort = getSortWithDirection<OrganisationSearchSortField>(
			searchParams,
			ORGANISATION_DEFAULT_SORT
		);

		const input: OrganisationSearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
			sort,
		};

		res = await gqlClientCustom.request(
			OrganisationSearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		organisations = res.organisationSearch.data;
		resultsCount = res.organisationSearchCount || 0;
	}

	return {
		...parentData,
		...data,
		organisations,
		resultsCount,
		showResults,
		currentPage,
	};
};
