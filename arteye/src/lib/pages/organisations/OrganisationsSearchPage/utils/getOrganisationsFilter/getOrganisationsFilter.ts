import { OrganisationsSearchParam } from '../../constants/search';
import type {
	Entity_Attribute_Type_Enum,
	IntValueFilter,
	Organisation_Type_Enum,
	OrganisationSearchFilters,
	ValueFilterOperator,
} from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';

export const getOrganisationsFilter = (searchParams: URLSearchParams) => {
	let filters: OrganisationSearchFilters = {};

	const name = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.Name,
	});

	const types = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.Types,
	});

	const attributes = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.Attributes,
	});

	const yearFounded = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.YearFounded,
	});

	const yearFoundedRange = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.YearFoundedRange,
	});

	const yearDissolved = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.YearDissolved,
	});

	const yearDissolvedRange = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.YearDissolvedRange,
	});

	const addressCity = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.AddressCity,
	});

	const addressCountry = getDecodedSearchParam({
		searchParams,
		key: OrganisationsSearchParam.AddressCountry,
	});

	if (name) {
		filters = {
			...filters,
			nameOrId: name,
		};
	}

	if (types) {
		filters = {
			...filters,
			type: types.split(PARAM_SEPARATOR).map((type) => {
				return type as Organisation_Type_Enum;
			}),
		};
	}

	if (attributes) {
		filters = {
			...filters,
			attributes: attributes.split(PARAM_SEPARATOR).map((attribute) => {
				return attribute as Entity_Attribute_Type_Enum;
			}),
		};
	}

	if (yearFounded) {
		filters = {
			...filters,
			yearFounded: getValueFilter({
				value: yearFounded,
				range: yearFoundedRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (yearDissolved) {
		filters = {
			...filters,
			yearDissolved: getValueFilter({
				value: yearDissolved,
				range: yearDissolvedRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (addressCity) {
		filters = {
			...filters,
			location: {
				...filters.location,
				cityNameOrCode: addressCity,
			},
		};
	}

	if (addressCountry) {
		filters = {
			...filters,
			location: {
				...filters.location,
				countryNameOrCode: addressCountry,
			},
		};
	}

	return filters;
};
