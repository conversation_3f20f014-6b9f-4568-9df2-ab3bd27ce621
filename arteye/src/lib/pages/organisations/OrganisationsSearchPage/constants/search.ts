import { SearchParam } from '$lib/types/types';

export enum OrganisationsSearchParam {
	ShowResults = SearchParam.ShowResults,
	Sort = SearchParam.Sort,
	Name = 'name',
	AddressCity = 'addressCity',
	AddressCountry = 'addressCountry',
	Types = 'types',
	Attributes = 'attributes',
	YearFounded = 'yearFounded',
	YearFoundedRange = 'yearFoundedRange',
	YearDissolved = 'yearDissolved',
	YearDissolvedRange = 'yearDissolvedRange',
}
