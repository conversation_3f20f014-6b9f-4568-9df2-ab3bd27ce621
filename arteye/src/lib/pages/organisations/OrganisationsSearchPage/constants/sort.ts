import { OrganisationSearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const ORGANISATION_SORT_OPTIONS = sortByField(
	[
		{
			label: 'Date created (Asc)',
			value: OrganisationSearchSortField.DateCreated,
		},
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(OrganisationSearchSortField.DateCreated),
		},
		{
			label: 'Date updated (Asc)',
			value: OrganisationSearchSortField.DateUpdated,
		},
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(OrganisationSearchSortField.DateUpdated),
		},
		{
			label: 'Organisation name (A-Z)',
			value: OrganisationSearchSortField.Name,
		},
		{
			label: 'Organisation name (Z-A)',
			value: getDescendingDirection(OrganisationSearchSortField.Name),
		},
		{ label: 'City (A-Z)', value: OrganisationSearchSortField.City },
		{
			label: 'City (Z-A)',
			value: getDescendingDirection(OrganisationSearchSortField.City),
		},
		{ label: 'Country (A-Z)', value: OrganisationSearchSortField.Country },
		{
			label: 'Country (Z-A)',
			value: getDescendingDirection(OrganisationSearchSortField.Country),
		},
		{
			label: 'Organisation type (A-Z)',
			value: OrganisationSearchSortField.Type,
		},
		{
			label: 'Organisation type (Z-A)',
			value: getDescendingDirection(OrganisationSearchSortField.Type),
		},
		{
			label: 'No. of activities (Asc)',
			value: OrganisationSearchSortField.NumberOfActivities,
		},
		{
			label: 'No. of activities (Desc)',
			value: getDescendingDirection(
				OrganisationSearchSortField.NumberOfActivities
			),
		},
	],
	'label'
);

export const ORGANISATION_DEFAULT_SORT = OrganisationSearchSortField.Name;
