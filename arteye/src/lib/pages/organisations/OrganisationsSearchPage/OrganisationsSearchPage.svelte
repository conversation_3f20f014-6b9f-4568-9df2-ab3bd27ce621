<script lang="ts">
	import { OrganisationsSearchParam } from './constants/search';
	import { OrganisationsSearchForm } from './OrganisationsSearchForm';
	import type { OrganisationsSearchFormFields } from './OrganisationsSearchForm/OrganisationsSearchForm.svelte';
	import { OrganisationsTable } from './OrganisationsTable';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { OrganisationsSearchPageData } from '$routes/organisations/types';

	let data = $derived(getPageData<OrganisationsSearchPageData>(page.data));
	let attributeTypes = $derived(data.user?.dropdowns.attributeTypes || []);
	let organisationTypes = $derived(
		data.user?.dropdowns.organisationTypes || []
	);
	let showResults = $derived(data.showResults);

	let typesOptions: MultiSelectOption[] = $state([]);
	let attributesOptions: MultiSelectOption[] = $state([]);

	$effect(() => {
		typesOptions = organisationTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	$effect(() => {
		attributesOptions = attributeTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	const formatParamString = (
		values: OrganisationsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			sort,
			name,
			types,
			attributes,
			yearFounded,
			yearFoundedRange,
			yearDissolved,
			yearDissolvedRange,
			addressCity,
			addressCountry,
		} = values;

		const params: Record<OrganisationsSearchParam, string> = {
			[OrganisationsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[OrganisationsSearchParam.ShowResults]: showResults,
			[OrganisationsSearchParam.Name]: name,
			[OrganisationsSearchParam.Types]: getSearchParamFromStringArray(types),
			[OrganisationsSearchParam.Attributes]:
				getSearchParamFromStringArray(attributes),
			[OrganisationsSearchParam.YearFounded]: yearFounded,
			[OrganisationsSearchParam.YearFoundedRange]: yearFounded
				? yearFoundedRange
				: '',
			[OrganisationsSearchParam.YearDissolved]: yearDissolved,
			[OrganisationsSearchParam.YearDissolvedRange]: yearDissolved
				? yearDissolvedRange
				: '',
			[OrganisationsSearchParam.AddressCity]: addressCity,
			[OrganisationsSearchParam.AddressCountry]: addressCountry,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<OrganisationsSearchForm
		{typesOptions}
		{attributesOptions}
		{formatParamString}
	/>
</div>

{#if showResults}
	<OrganisationsTable />
{/if}
