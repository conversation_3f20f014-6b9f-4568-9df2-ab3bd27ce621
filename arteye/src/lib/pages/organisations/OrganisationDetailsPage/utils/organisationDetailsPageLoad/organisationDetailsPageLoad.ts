import { error } from '@sveltejs/kit';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetOrganisationsDetailsDocument } from '$lib/queries/__generated__/getOrganisationDetails.generated';
import { GetRelationshipsDocument } from '$lib/queries/__generated__/getRelationships.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { OrganisationPageLoadEvent } from '$routes/organisations/[id]/types';

export const organisationDetailsPageLoad = async ({
	parent,
	params,
}: OrganisationPageLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();
	const organisationId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const organisationReq = (async () => {
		if (!organisationId) {
			return null;
		}

		const res = await gqlClient.request(
			GetOrganisationsDetailsDocument,
			{
				filter: {
					_and: [
						{
							id: {
								_eq: organisationId,
							},
						},
						{ status: { key: { _neq: Status_Enum.Archived } } },
					],
				},
			},
			authHeaders
		);

		return res?.organisation[0];
	})();

	const relationshipsReq = (async () => {
		if (!organisationId) {
			return [];
		}

		const relationshipsRes = await gqlClient.request(
			GetRelationshipsDocument,
			{
				filter: {
					_and: [
						{ status: { key: { _neq: Status_Enum.Archived } } },
						{
							_or: [
								{
									from_entity: {
										organisation: { id: { _eq: organisationId } },
									},
								},
								{
									to_entity: {
										organisation: { id: { _eq: organisationId } },
									},
								},
							],
						},
					],
				},
			},
			authHeaders
		);

		return relationshipsRes.relationship;
	})();

	const legacyIdReq =
		organisationId === PARAM_NEW || isOnDev() || !organisationId
			? { getLegacyId: { legacyId: '' } }
			: gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'organisation',
						id: organisationId,
					},
					getAuthorizationHeaders(data)
				);

	const pipelineSourceReq = (async () => {
		if (organisationId === PARAM_NEW || !organisationId) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{
				collection: PipelineSourceItem.Organisation,
				id: organisationId,
			},
			authHeaders
		);

		return res.getDatasource?.data_source;
	})();

	const [
		organisation,
		relationships,
		legacyIdRes,
		pipelineSource,
		{ dropzoneUrlDialogForm },
	] = await Promise.all([
		organisationReq,
		relationshipsReq,
		legacyIdReq,
		pipelineSourceReq,
		getDropzoneUrlDialogSuperform(),
	]);

	if (!organisation && organisationId) {
		error(404, 'Organisation cannot be found');
	}

	const legacyId = legacyIdRes?.getLegacyId?.legacyId;

	return {
		...data,
		legacyId,
		organisation,
		relationships,
		pipelineSource,
		dropzoneUrlDialogForm,
	};
};
