<script lang="ts">
	import classNames from 'classnames';
	import { goto, invalidateAll } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Link } from '$global/components/Link';
	import { LinkButton } from '$global/components/LinkButton';
	import { Tabs } from '$global/components/Tabs';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { ChildrenCard } from '$lib/components/details-pages/ChildrenCard';
	import { EntityCards } from '$lib/components/details-pages/EntityCards';
	import { type AddressesType } from '$lib/components/details-pages/EntityCards/AddressesCard/AddressesCard.svelte';
	import { type CollectionNotesType } from '$lib/components/details-pages/EntityCards/CollectionNotesCard';
	import { type NotesType } from '$lib/components/details-pages/EntityCards/NotesCard/NotesCard.svelte';
	import {
		type AdditionalImagesType,
		type ProfileImageType,
	} from '$lib/components/details-pages/EntityCards/ProfileImageCard';
	import type { RelationshipItem } from '$lib/components/details-pages/EntityCards/RelationshipsCard/RelationshipRow/types';
	import { OrganisationDetailsCard } from '$lib/components/details-pages/OrganisationDetailsCard';
	import { type OrganisationDetailsForm } from '$lib/components/details-pages/OrganisationDetailsCard/OrganisationDetailsCard.svelte';
	import { InfoLabel } from '$lib/components/InfoLabel';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { areContactDetailsValid } from '$lib/utils/areContactDetailsValid/areContactDetailsValid';
	import { areEntityAddressesValid } from '$lib/utils/areEntityAddressesValid/areEntityAddressesValid';
	import { areEntityCollectionNotesValid } from '$lib/utils/areEntityCollectionNotesValid/areEntityCollectionNotesValid';
	import { areEntityNotesValid } from '$lib/utils/areEntityNotesValid/areEntityNotesValid';
	import { areEntityRelationshipsValid } from '$lib/utils/areEntityRelationshipsValid/areEntityRelationshipsValid';
	import {
		formatProfileImage,
		formatAdditionalImages,
		formatContactDetailsRows,
		formatAddresses,
		formatEntityNotes,
		formatRelationships,
		formatCollectionEntityNotes,
		formatCurrentEntity,
		formatOrganisationDetails,
	} from '$lib/utils/entityFormatters/entityFormatters';
	import { archiveEntity } from '$lib/utils/mutation-handlers/archiveEntity/archiveEntity';
	import { archiveOrganisation } from '$lib/utils/mutation-handlers/archiveOrganisation/archiveOrganisation';
	import { createEntity } from '$lib/utils/mutation-handlers/createEntity/createEntity';
	import { mutateEntity } from '$lib/utils/mutation-handlers/mutateEntity/mutateEntity';
	import { mutateEntityImages } from '$lib/utils/mutation-handlers/mutateEntityImages/mutateEntityImages';
	import { mutateOrganisationDetails } from '$lib/utils/mutation-handlers/mutateOrganisationDetails/mutateOrganisationDetails';
	import type { OrganisationDetailsPageData as OrganisationDetailsPageData } from '$routes/organisations/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let loading = $state(false);
	let deleting = $state(false);

	const INITIAL_CHANGED_STATE = {
		organisationDetails: false,
		profileImage: false,
		additionalImages: false,
		contactDetailsRows: false,
		entityAddresses: false,
		entityNotes: false,
		collectionEntityNotes: false,
		relationships: false,
	};

	let data = $derived(getPageData<OrganisationDetailsPageData>(page.data));
	let organisation = $derived(data.organisation);
	let id = $derived(page.params.id);
	let legacyId = $derived(data.legacyId);
	let attributeTypes = $derived(data.user?.dropdowns?.attributeTypes || []);
	let changed = $state(INITIAL_CHANGED_STATE);

	let profileImage = $state(
		(() =>
			formatProfileImage(organisation?.entity, page.data.user.access_token))()
	) as ReturnType<typeof formatProfileImage>;
	$effect(() => {
		profileImage = formatProfileImage(
			organisation?.entity,
			page.data.user.access_token
		);
	});

	let additionalImages = $state(
		(() =>
			formatAdditionalImages(
				organisation?.entity,
				page.data.user.access_token
			))()
	) as ReturnType<typeof formatAdditionalImages>;
	$effect(() => {
		additionalImages = formatAdditionalImages(
			organisation?.entity,
			page.data.user.access_token
		);
	});

	let dropzoneUrlDialogForm = $derived(data.dropzoneUrlDialogForm);

	let organisationDetails = $state(
		(() => formatOrganisationDetails(organisation))()
	) as ReturnType<typeof formatOrganisationDetails>;
	$effect(() => {
		organisationDetails = formatOrganisationDetails(organisation);
	});
	let contactDetailsRows = $state(
		(() => formatContactDetailsRows(organisation?.entity))()
	) as ReturnType<typeof formatContactDetailsRows>;
	$effect(() => {
		contactDetailsRows = formatContactDetailsRows(organisation?.entity);
	});
	let entityAddresses = $state(
		(() => formatAddresses(organisation?.entity))()
	) as ReturnType<typeof formatAddresses>;
	$effect(() => {
		entityAddresses = formatAddresses(organisation?.entity);
	});
	let entityNotes = $state(
		(() => formatEntityNotes(organisation?.entity))()
	) as ReturnType<typeof formatEntityNotes>;
	$effect(() => {
		entityNotes = formatEntityNotes(organisation?.entity);
	});
	let collectionEntityNotes = $state(
		(() => formatCollectionEntityNotes(organisation?.entity))()
	) as ReturnType<typeof formatCollectionEntityNotes>;
	$effect(() => {
		collectionEntityNotes = formatCollectionEntityNotes(organisation?.entity);
	});
	let relationships = $state(
		(() =>
			formatRelationships({
				relationships: data.relationships,
				entity: organisation?.entity,
			}))()
	) as ReturnType<typeof formatRelationships>;
	$effect(() => {
		relationships = formatRelationships({
			relationships: data.relationships,
			entity: organisation?.entity,
		});
	});
	let currentEntity = $derived(formatCurrentEntity(organisation?.entity));

	let crumbs = $derived([
		{
			label: 'Search Organisation',
			href: userRoutes.routes.organisation,
		},
		{
			label: organisation ? organisation.name : 'New Organisation',
		},
	]);

	let showSaveBar = $state(false);
	let activeTab = $state(0);

	const tabs = [
		{
			id: '1',
			title: 'Organisation details',
		},
		{
			id: '2',
			title: 'Activities',
		},
	];

	const dataCy = 'organisation-details';

	const handleOrganisationDetailsChange = (
		details: OrganisationDetailsForm
	) => {
		organisationDetails = details;
		showSaveBar = true;
		changed.organisationDetails = true;
	};

	const handleAdditionalImagesChange = (images: AdditionalImagesType) => {
		additionalImages = images;
		showSaveBar = true;
		changed.additionalImages = true;
	};

	const handleProfileImageChange = (image: ProfileImageType) => {
		profileImage = image;
		showSaveBar = true;
		changed.profileImage = true;
	};

	const handleEntityAddressesChange = (addresses: AddressesType) => {
		entityAddresses = addresses;
		showSaveBar = true;
		changed.entityAddresses = true;
	};

	const handleEntityNotesChange = (notes: NotesType) => {
		entityNotes = notes;
		showSaveBar = true;
		changed.entityNotes = true;
	};

	const handleCollectionEntityNotesChange = (
		collectionNotes: CollectionNotesType
	) => {
		collectionEntityNotes = collectionNotes;
		showSaveBar = true;
		changed.collectionEntityNotes = true;
	};

	const handleContactDetailsChange = (rows: typeof contactDetailsRows) => {
		contactDetailsRows = rows;
		showSaveBar = true;
		changed.contactDetailsRows = true;
	};

	const handleRelationshipsChange = (newRelationships: RelationshipItem[]) => {
		relationships = newRelationships;
		showSaveBar = true;
		changed.relationships = true;
	};

	const handleClickDeleteOrganisation = async () => {
		try {
			deleting = true;
			const headers = getAuthorizationHeaders(data);
			const entityId = `${organisation?.entity?.id}`;
			const entityName = `${organisation?.entity?.name}`;
			const entityType = `${organisation?.entity?.type?.key}`;
			const organisationId = `${organisation?.id}`;

			await archiveEntity({ entityId, entityName, entityType, headers });
			await archiveOrganisation({ organisationId, headers });

			showToast({
				variant: 'success',
				message: 'This organisation has been successfully deleted',
			});

			goto(userRoutes.routes.organisation);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this organisation deletion. Please contact the support team.',
			});
		} finally {
			deleting = false;
		}
	};

	const create = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName = organisationDetails?.name || '';
			const entityType = 'organisation';

			const requests: {
				images: ReturnType<typeof mutateEntityImages> | null;
				entity: ReturnType<typeof mutateEntity> | null;
			} = {
				images: null,
				entity: null,
			};

			const { entityId } = await createEntity({
				entityName,
				entityType,
				headers,
			});

			const { organisationId } = await mutateOrganisationDetails({
				entityId,
				entityName,
				entityType,
				organisationDetailsForm: organisationDetails,
				headers,
			});

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				attributes: organisationDetails?.attributes,
				originalAttributes: organisation?.entity?.attributes,
				changed: {
					...changed,
					attributes: changed.organisationDetails,
				},
			});

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: 'This organisation has been successfully created.',
			});

			goto(`${Routes.Organisations}/${organisationId}`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while creating this organisation. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const update = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const entityName =
				organisation?.entity?.name || `${organisationDetails?.name}`;
			const entityType = organisation?.entity?.type?.key || 'organisation';
			const entityId = organisation?.entity?.id || '';

			const requests: {
				images: ReturnType<typeof mutateEntityImages> | null;
				organisationDetails: ReturnType<
					typeof mutateOrganisationDetails
				> | null;
				entity: ReturnType<typeof mutateEntity> | null;
			} = {
				images: null,
				organisationDetails: null,
				entity: null,
			};

			if (changed.profileImage || changed.additionalImages) {
				requests.images = mutateEntityImages({
					entityId,
					entityName,
					entityType,
					profileImage,
					additionalImages,
					headers,
				});
			}

			if (changed.organisationDetails) {
				requests.organisationDetails = mutateOrganisationDetails({
					entityId,
					entityName,
					entityType,
					organisationDetailsForm: organisationDetails,
					headers,
				});
			}

			requests.entity = mutateEntity({
				entityId,
				entityName,
				entityType,
				contactDetailsRows,
				entityAddresses,
				entityNotes,
				collectionEntityNotes,
				relationships,
				headers,
				attributes: organisationDetails?.attributes,
				originalAttributes: organisation?.entity?.attributes,
				changed: {
					...changed,
					attributes: changed.organisationDetails,
				},
			});

			await Promise.all(Object.values(requests));

			showToast({
				variant: 'success',
				message: 'This organisation has been successfully updated.',
			});

			changed = INITIAL_CHANGED_STATE;

			await invalidateAll();
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while updating this organisation. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const handleSaveClick = () => {
		loading = true;

		if (organisation?.id) {
			update();
		} else {
			create();
		}
	};

	let originalEntityAddresses = $derived(
		(page.data as OrganisationDetailsPageData)?.organisation?.entity?.addresses
	);

	let originalCollectionEntityNotes = $derived(
		(page.data as OrganisationDetailsPageData)?.organisation?.entity
			?.collection_notes
	);
</script>

<div
	class={classNames('pb-12', { 'pointer-events-none': loading || deleting })}
>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if organisation}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				{#if legacyId}
					<InfoLabel title="Legacy ID" value={legacyId} />
				{/if}

				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&association=${organisation?.reference_id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					class="h-[2rem]"
					size="sm"
					dataCy={`${dataCy}-view-artworks`}
					variant="secondary"
					disabled={loading || deleting}
				>
					view artworks
				</LinkButton>

				<Button
					onclick={() => {
						navigator.clipboard.writeText(
							`${organisation?.entity?.reference_id}`
						);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy organisation id
				</Button>

				<Button
					onclick={handleClickDeleteOrganisation}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if organisation}
		<CreateUpdate
			class="block mb-2 mt-1"
			pipelineSource={data.pipelineSource}
			updateHistory={organisation}
		/>
	{/if}

	<Tabs {dataCy} bind:activeTab {tabs} fullLine />

	{#if !activeTab}
		<OrganisationDetailsCard
			organisationDetailsForm={organisationDetails}
			onChange={handleOrganisationDetailsChange}
			organisationTypes={data.user?.dropdowns?.organisationTypes || []}
			{organisation}
			{attributeTypes}
		/>

		{#if organisation?.children?.length}
			<ChildrenCard children={organisation?.children} />
		{/if}

		<EntityCards
			{currentEntity}
			{profileImage}
			onProfileImageChange={handleProfileImageChange}
			{additionalImages}
			onAdditionalImagesChange={handleAdditionalImagesChange}
			{contactDetailsRows}
			onContactDetailsChange={handleContactDetailsChange}
			{originalEntityAddresses}
			{originalCollectionEntityNotes}
			{entityAddresses}
			onCollectionEntityNotesChange={handleCollectionEntityNotesChange}
			{entityNotes}
			onEntityNotesChange={handleEntityNotesChange}
			{relationships}
			onRelationshipsChange={handleRelationshipsChange}
			{collectionEntityNotes}
			onEntityAddressesChange={handleEntityAddressesChange}
			{dropzoneUrlDialogForm}
		/>
	{/if}

	{#if activeTab}
		<Link
			target="_blank"
			rel="noopener noreferrer"
			href={`${Routes.ArtworksAndActivities}?showResults=true&association=${organisation?.reference_id}`}
		>
			<Button dataCy="show-activities" size="md" variant="secondary">
				Show activities
			</Button>
		</Link>
	{/if}
</div>

<PageSaveBar
	{loading}
	disabled={deleting ||
		loading ||
		!organisationDetails.name ||
		!areContactDetailsValid(contactDetailsRows) ||
		!areEntityAddressesValid(entityAddresses) ||
		!areEntityNotesValid(entityNotes) ||
		!areEntityCollectionNotesValid(collectionEntityNotes) ||
		!areEntityRelationshipsValid(relationships)}
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
/>
