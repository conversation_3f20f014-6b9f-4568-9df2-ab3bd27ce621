import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetAuctionGroupingsDocument } from '$lib/queries/__generated__/getAuctionGroupings.generated';
import { PipelineSourceItem } from '$lib/types/types';
import type { AuctionGroupingDetailsPageLoadEvent } from '$routes/auction-groupings/[id]/types';

export const auctionGroupingDetailsPageLoad = async ({
	parent,
	params,
}: AuctionGroupingDetailsPageLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();

	const auctionGroupingId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const res =
		auctionGroupingId === PARAM_NEW
			? {
					auction_group: [],
				}
			: await gqlClient.request(
					GetAuctionGroupingsDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: auctionGroupingId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const auctionGrouping = res?.auction_group[0];

	const pipelineSource = await (async () => {
		if (auctionGroupingId === PARAM_NEW) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{ collection: PipelineSourceItem.AuctionGroup, id: params?.id },
			getAuthorizationHeaders(data)
		);

		return res.getDatasource?.data_source;
	})();

	return {
		...data,
		auctionGrouping,
		pipelineSource,
	};
};
