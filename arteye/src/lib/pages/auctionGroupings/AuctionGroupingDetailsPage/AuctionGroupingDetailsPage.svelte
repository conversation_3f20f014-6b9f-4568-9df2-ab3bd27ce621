<script lang="ts">
	import classNames from 'classnames';
	import { AuctionGroupingDetailsCard } from './AuctionGroupingDetailsCard';
	import type { AuctionGroupingDetailsForm } from './AuctionGroupingDetailsCard/AuctionGroupingDetailsCard.svelte';
	import { invalidateAll, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { UpdateAuctionGroupItemDocument } from '$lib/queries/__generated__/updateAuctionGroupingItem.generated';
	import { mutateAuctionGrouping } from '$lib/utils/mutation-handlers/mutateAuctionGrouping/mutateAuctionGrouping';
	import type { AuctionGroupingDetailsPageData } from '$routes/auction-groupings/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let data = $derived(getPageData<AuctionGroupingDetailsPageData>(page.data));

	let auctionGrouping = $derived(
		data.auctionGrouping as AuctionGroupingDetailsPageData['auctionGrouping']
	);

	let auctionGroupingDetailsForm = $state(
		(() => ({
			id: auctionGrouping?.id || '',
			title: auctionGrouping?.title || '',
			auctions: auctionGrouping?.auctions?.map((auction) => {
				return {
					...auction?.auction_id,
					updateHistory: {
						date_created: auction?.date_created || '',
						user_created: {
							first_name: auction?.user_created?.first_name || '',
							last_name: auction?.user_created?.last_name || '',
						},
					},
				};
			}),
		}))()
	) as AuctionGroupingDetailsForm;

	$effect(() => {
		auctionGroupingDetailsForm = {
			id: auctionGrouping?.id,
			title: auctionGrouping?.title,
			auctions: auctionGrouping?.auctions?.map((auction) => {
				return {
					...auction?.auction_id,
					updateHistory: {
						date_created: auction?.date_created || '',
						user_created: {
							first_name: auction?.user_created?.first_name || '',
							last_name: auction?.user_created?.last_name || '',
						},
					},
				};
			}),
		} as AuctionGroupingDetailsForm;
	});

	let loading = $state(false);
	let showSaveBar = $state(false);

	let crumbs = $derived([
		{
			label: 'Search auction groupings',
			href: userRoutes.routes.auction_group,
		},
		{ label: `${auctionGrouping?.title || 'New Auction Grouping'}` },
	]);

	let deleting = $state(false);

	const dataCy = 'auction-grouping-details';

	const handleClickDeleteAuctionGrouping = async () => {
		try {
			deleting = true;
			await gqlClient.request(
				UpdateAuctionGroupItemDocument,
				{
					id: auctionGrouping.id,
					data: { status: { key: Status_Enum.Archived } },
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'This auction grouping has been successfully deleted.',
			});

			goto(userRoutes.routes.auction_group);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while deleting this auction grouping.',
			});

			deleting = false;
		}
	};

	const handleAuctionGroupingDetailsForm = (
		auctionGroupingDetails: AuctionGroupingDetailsForm
	) => {
		auctionGroupingDetailsForm = auctionGroupingDetails;
		showSaveBar = true;
	};

	const handleSaveClick = async () => {
		loading = true;
		try {
			const headers = getAuthorizationHeaders(data);

			const auctionGroupingId = await mutateAuctionGrouping({
				auctionGroupingDetailsForm,
				headers,
			});

			showToast({
				variant: 'success',
				message: `This auction grouping has been successfully updated.`,
			});

			if (auctionGroupingDetailsForm?.id) {
				await invalidateAll();
			} else {
				await goto(`${Routes.AuctionGroupings}/${auctionGroupingId}`);
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong during this auction grouping update. Please contact the support team.',
			});
		} finally {
			showSaveBar = false;
			loading = false;
		}
	};
</script>

<div class={classNames({ 'pointer-events-none': loading })}>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if auctionGrouping}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<Button
					onclick={() => {
						navigator.clipboard.writeText(`${auctionGrouping?.id}`);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy auction grouping id
				</Button>

				<Button
					onclick={handleClickDeleteAuctionGrouping}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if auctionGrouping}
		<CreateUpdate
			updateHistory={auctionGrouping}
			class="block mb-5 mt-[-12px]"
			pipelineSource={data.pipelineSource}
		/>
	{/if}

	<AuctionGroupingDetailsCard
		{auctionGroupingDetailsForm}
		onChange={handleAuctionGroupingDetailsForm}
		class="mb-4"
	/>
</div>

<PageSaveBar
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
	{loading}
	disabled={loading ||
		!auctionGroupingDetailsForm?.title ||
		!auctionGroupingDetailsForm?.auctions?.length ||
		!auctionGroupingDetailsForm.auctions.every(
			(auction) => auction.isDeleted || (!auction.isDeleted && !!auction.id)
		)}
/>
