import type { AuctionGroupingDetailsPageData } from '$routes/auction-groupings/[id]/types';

export type AuctionType = NonNullable<
	NonNullable<
		NonNullable<
			AuctionGroupingDetailsPageData['auctionGrouping']['auctions']
		>[number]
	>['auction_id']
> & {
	isDeleted?: boolean;
	isNew?: boolean;
	updateHistory: {
		date_created: string;
		user_created: {
			first_name: string;
			last_name: string;
		};
	};
};

export enum AuctionFieldName {
	Name = 'name',
	NumberOfLots = 'numberOfLots',
}
