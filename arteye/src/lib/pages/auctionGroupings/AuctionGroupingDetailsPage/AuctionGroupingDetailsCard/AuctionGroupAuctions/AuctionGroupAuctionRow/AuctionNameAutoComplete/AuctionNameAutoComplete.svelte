<script lang="ts" module>
	export const formatAuction = (
		auction: AuctionSearchQuery['auctionSearch']['data'][number]
	) => {
		const url = (() => {
			return `${Routes.Auctions}/${auction?.id}`;
		})();

		const auctionsDetails = [
			auction?.auction_house?.organisation?.name,
			auction?.local_auction_end_date
				? dayjs(auction?.local_auction_end_date.slice(0, -1)).format(
						'DD/MM/YYYY'
					)
				: '',
			auction?.currency?.code,
			auction?.sale_number,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${auction?.sale_name}`,
			line2: url,
			line3: `${auction?.auction_types?.[0]?.auction_type_key?.key}`,
			line4: `${auction?.id}`,
			line1Suffix: auctionsDetails ? `(${auctionsDetails})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Routes } from '$lib/constants/routes';
	import {
		type AuctionSearchQuery,
		AuctionSearchDocument,
		type AuctionSearchQueryVariables,
	} from '$lib/custom-queries/__generated__/auctionSearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { AuctionSearchSortField, SortDirection } from '$gql/types-custom';

	let auctions: AuctionSearchQuery['auctionSearch']['data'] | [] = [];

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		placeholder?: string;
		dataCy: string;
		onRemoveSelectedOption: () => void;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: AuctionSearchQuery['auctionSearch']['data'][number];
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search auction',
		dataCy,
		onRemoveSelectedOption,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const auction = auctions.find(
			(auction) => auction.id === e.detail.value.line4
		);
		if (onChange && auction) {
			onChange({
				detail: {
					value: auction,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getVariables = (value: string): AuctionSearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : 200,
				filters: {
					auctionNameOrId: value,
				},
				sort: [
					{
						direction: SortDirection.Desc,
						field: AuctionSearchSortField.AuctionStartDate,
					},
				],
			},
		};
	};

	const getOptions = (data: AuctionSearchQuery | undefined) => {
		setTimeout(() => {
			auctions = data?.auctionSearch?.data || [];
		}, 0);

		return [...(data?.auctionSearch?.data || []).map(formatAuction)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="auction"
		{onRemoveSelectedOption}
		dataCy={`${dataCy}-auction`}
		{placeholder}
		emptyValueResponse={{ auctionSearch: { data: [] } }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientCustom}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={AuctionSearchDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-auction-name-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				if (onRemoveSelectedOption) {
					onRemoveSelectedOption();
				}
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
