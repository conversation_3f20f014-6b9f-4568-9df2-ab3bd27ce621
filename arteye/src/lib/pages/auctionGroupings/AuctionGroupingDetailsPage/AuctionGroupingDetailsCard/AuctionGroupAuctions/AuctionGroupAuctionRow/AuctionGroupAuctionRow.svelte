<script lang="ts">
	import { AuctionNameAutoComplete } from './AuctionNameAutoComplete';
	import { type AuctionType, AuctionFieldName } from './types';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { AuctionSearchResponse } from '$gql/types-custom';

	interface Props {
		index: number;
		auction: AuctionType;
		headers: { fieldName?: AuctionFieldName }[];
		selectedOption?: OptionType | null;
		onChange: (auction: AuctionSearchResponse['data'][number]) => void;
		onDelete: (auction: AuctionType) => void;
	}

	let {
		index,
		auction,
		headers,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
	}: Props = $props();

	const dataCy = 'auction-row';

	const handleDeleteAuction = () => {
		onDelete(auction);
	};

	const handleEntityClear = () => {
		onChange({} as AuctionSearchResponse['data'][number]);
	};

	const handleEntityChange = (e: {
		detail: {
			value: AuctionSearchResponse['data'][number];
		};
	}) => {
		onChange(e.detail.value);

		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={auction.updateHistory} />

					<Button
						onclick={handleDeleteAuction}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === AuctionFieldName.Name}
			<TableCell {dataCy}>
				{#snippet custom()}
					<AuctionNameAutoComplete
						bind:selectedOption
						{dataCy}
						onRemoveSelectedOption={handleEntityClear}
						onChange={handleEntityChange}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === AuctionFieldName.NumberOfLots}
			<TableCell {dataCy}>
				{#snippet custom()}
					{#if auction.aggregations?.[0]?.lot_count}
						<Txt variant="body2">{auction.aggregations?.[0]?.lot_count}</Txt>
					{/if}
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
