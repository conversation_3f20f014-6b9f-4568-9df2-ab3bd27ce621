<script lang="ts">
	import classNames from 'classnames';
	import {
		AuctionFieldName,
		AuctionGroupAuctionRow,
	} from './AuctionGroupAuctionRow';
	import type { AuctionType } from './AuctionGroupAuctionRow';
	import { formatAuction } from './AuctionGroupAuctionRow/AuctionNameAutoComplete/AuctionNameAutoComplete.svelte';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import type { AuctionSearchQuery } from '$lib/custom-queries/__generated__/auctionSearch.generated';

	const headers = [
		{
			title: 'Name or ID',
			fieldName: AuctionFieldName.Name,
		},
		{
			title: 'No. of Lots',
			fieldName: AuctionFieldName.NumberOfLots,
		},
		{ title: '' },
	];

	interface Props {
		auctions?: AuctionType[];
		onChange: (auctions: AuctionType[]) => void;
	}

	let { auctions = $bindable([]), onChange }: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(auctions || []).map((auction) => {
			if (!auction) return null;

			return formatAuction(
				auction as AuctionSearchQuery['auctionSearch']['data'][number]
			);
		})
	);

	const onAuctionChange =
		(index: number) =>
		(auction: AuctionSearchQuery['auctionSearch']['data'][number]) => {
			const updatedAuctions = (auctions || []).map((item, i) =>
				i === index
					? {
							...auction,
							isNew: true,
						}
					: item
			) as AuctionType[];

			onChange(updatedAuctions);
		};

	const handleAuctionDelete = (auction: AuctionType) => {
		const updatedAuctions = (auctions || []).map((item) => {
			if (item?.id === auction.id) {
				return {
					...auction,
					isDeleted: true,
				};
			}
			return item;
		});
		onChange(updatedAuctions);
	};

	const dataCy = 'auctions-grouping';

	const handleAuctionGroupingAdd = () => {
		auctions = [
			...(auctions || []).map((auction) => auction),
			{
				isNew: true,
			},
		] as AuctionType[];
		selectedOptions = [...selectedOptions, null];
	};
</script>

<div>
	<InputLabel {dataCy} required variant="label3" class="mb-2"
		>Auctions</InputLabel
	>

	<div class="max-lg:overflow-x-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1200px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName}
						<TableHeader
							{dataCy}
							class={classNames({
								'w-[800px]': header.fieldName === AuctionFieldName.Name,
							})}
						>
							{#snippet custom()}
								<div class="flex items-center">
									<Txt variant="label3">
										{header.title}
									</Txt>
									{#if header.fieldName === AuctionFieldName.Name}
										<Mandatory />
									{/if}
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleAuctionGroupingAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if auctions}
					{#each auctions as auction, index (auction.id)}
						{#if !auction.isDeleted}
							<AuctionGroupAuctionRow
								{headers}
								{auction}
								{index}
								bind:selectedOption={selectedOptions[index]}
								onChange={onAuctionChange(index)}
								onDelete={handleAuctionDelete}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</div>
