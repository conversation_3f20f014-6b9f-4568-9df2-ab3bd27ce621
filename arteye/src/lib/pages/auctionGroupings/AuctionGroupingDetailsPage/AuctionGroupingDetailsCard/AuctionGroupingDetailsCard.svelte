<script module lang="ts">
	export type AuctionGroupingDetailsForm = Partial<
		Pick<
			NonNullable<AuctionGroupingDetailsPageData['auctionGrouping']>,
			'id' | 'title'
		> & {
			auctions: AuctionType[];
		}
	>;
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { AuctionGroupAuctions } from './AuctionGroupAuctions';
	import type { AuctionType } from './AuctionGroupAuctions/AuctionGroupAuctionRow';
	import { page } from '$app/state';
	import { AccordionItem } from '$global/components/Accordion';
	import { Input } from '$global/components/Input';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { AuctionGroupingDetailsPageData } from '$routes/auction-groupings/[id]/types';

	interface Props {
		auctionGroupingDetailsForm: AuctionGroupingDetailsForm;
		onChange: (form: AuctionGroupingDetailsForm) => void;
		class?: string;
	}

	let { ...props }: Props = $props();

	let data = $derived(getPageData<AuctionGroupingDetailsPageData>(page.data));

	let auctionGrouping = $derived(data.auctionGrouping);

	const dataCy = 'auction-grouping-details-card';

	const handleInputChange =
		(field: keyof AuctionGroupingDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			props.onChange({ ...props.auctionGroupingDetailsForm, [field]: value });
		};

	const handleAuctionGroupChange = (auctions: AuctionType[]) => {
		props.onChange({ ...props.auctionGroupingDetailsForm, auctions });
	};
</script>

<AccordionItem
	{dataCy}
	title="Auction grouping details"
	class={twMerge('rounded-md border bg-white', props.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4">
		<div class="mb-4 grid grid-cols-5 gap-4">
			<div class="col-span-5">
				<Input
					dataCy={`${dataCy}-title`}
					name="title"
					required
					placeholder=""
					label="Title"
					value={props.auctionGroupingDetailsForm?.title || ''}
					size="sm"
					onkeyup={handleInputChange('title')}
				/>
			</div>
		</div>

		<div class="mb-4">
			<AuctionGroupAuctions
				auctions={props.auctionGroupingDetailsForm.auctions}
				onChange={handleAuctionGroupChange}
			/>
		</div>
	</div>
</AccordionItem>
