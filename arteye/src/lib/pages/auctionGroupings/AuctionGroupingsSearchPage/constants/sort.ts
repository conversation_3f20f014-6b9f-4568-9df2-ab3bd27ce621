import { sortByField } from '$lib/utils/sortByField/sortByField';

export enum AuctionGroupingsSort {
	TitleAsc = 'title',
	TitleDesc = '-title',

	DateCreatedAsc = 'date_created',
	DateCreatedDesc = '-date_created',

	DateUpdatedAsc = 'date_updated',
	DateUpdatedDesc = '-date_updated',

	AuctionsCountAsc = 'aggregations.auction_count',
	AuctionsCountDesc = '-aggregations.auction_count',
}

export const AUCTION_GROUPINGS_SORT_OPTIONS = sortByField(
	[
		{
			label: 'Auction grouping title (A-Z)',
			value: AuctionGroupingsSort.TitleAsc,
		},
		{
			label: 'Auction grouping title (Z-A)',
			value: AuctionGroupingsSort.TitleDesc,
		},
		{ label: 'Date created (Asc)', value: AuctionGroupingsSort.DateCreatedAsc },
		{
			label: 'Date created (Desc)',
			value: AuctionGroupingsSort.DateCreatedDesc,
		},
		{ label: 'Date updated (Asc)', value: AuctionGroupingsSort.DateUpdatedAsc },
		{
			label: 'Date updated (Desc)',
			value: AuctionGroupingsSort.DateUpdatedDesc,
		},

		{
			label: 'No. of auctions (Asc)',
			value: AuctionGroupingsSort.AuctionsCountAsc,
		},
		{
			label: 'No. of auctions (Desc)',
			value: AuctionGroupingsSort.AuctionsCountDesc,
		},
	],
	'label'
);

export const AUCTION_GROUPINGS_DEFAULT_SORT = AuctionGroupingsSort.TitleAsc;
