<script lang="ts">
	import { AuctionGroupingsSearchForm } from './AuctionGroupingsSearchForm';
	import type { AuctionGroupingsSearchFormFields } from './AuctionGroupingsSearchForm/AuctionGroupingsSearchForm.svelte';
	import { AuctionGroupingsTable } from './AuctionGroupingsTable';
	import { AuctionGroupingsSearchParam } from './constants/search';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { AuctionGroupingsSearchPageData } from '$routes/auction-groupings/types';

	let data = $derived(getPageData<AuctionGroupingsSearchPageData>(page.data));
	let showResults = $derived(data.showResults);

	const formatParamString = (
		values: AuctionGroupingsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const { sort, title } = values;

		const params: Record<AuctionGroupingsSearchParam, string> = {
			[AuctionGroupingsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[AuctionGroupingsSearchParam.ShowResults]: showResults,
			[AuctionGroupingsSearchParam.Title]: title,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<AuctionGroupingsSearchForm {formatParamString} />
</div>

{#if showResults}
	<AuctionGroupingsTable />
{/if}
