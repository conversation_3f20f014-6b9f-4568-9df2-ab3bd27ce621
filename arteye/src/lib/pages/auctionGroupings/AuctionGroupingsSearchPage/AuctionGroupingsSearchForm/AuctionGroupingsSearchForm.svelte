<script module lang="ts">
	export interface AuctionGroupingsSearchFormFields {
		[AuctionGroupingsSearchParam.Sort]: string[];
		[AuctionGroupingsSearchParam.Title]: string;
	}
</script>

<script lang="ts">
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { AuctionGroupingsSearchParam } from '../constants/search';
	import { AUCTION_GROUPINGS_SORT_OPTIONS } from '../constants/sort';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { Routes } from '$lib/constants/routes';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';

	interface Props {
		formatParamString: (
			data: AuctionGroupingsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => void;
	}

	let { formatParamString }: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			AuctionGroupingsSearchParam.Sort,
			AUCTION_GROUPINGS_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			AuctionGroupingsSearchParam.Sort,
			AUCTION_GROUPINGS_SORT_OPTIONS
		);
	});

	let title = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionGroupingsSearchParam.Title,
		})
	);

	const dataCyPrefix = 'search';

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url.pathname !== Routes.AuctionGroupings
			) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				await setArteyeSearch(
					Searches.AuctionGrouping,
					`${Routes.AuctionGroupings}?${queryParams}`
				);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		title,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		title = '';
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search auction groupings</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.AuctionGroupings}/new`}
			class="hidden lg:block"
		>
			Create New</LinkButton
		>
	</div>
	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-auction-grouping-title`}
				name="title"
				placeholder="Enter title"
				label="Auction grouping title"
				bind:value={title}
				size="sm"
			/>
		</div>
		<div class="col-span-1">
			<MultiSelect
				name="sort_by"
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				bind:selected={sort}
				placeholder="Sort by"
				options={AUCTION_GROUPINGS_SORT_OPTIONS}
				class="col-span-1"
				size="sm"
			/>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
