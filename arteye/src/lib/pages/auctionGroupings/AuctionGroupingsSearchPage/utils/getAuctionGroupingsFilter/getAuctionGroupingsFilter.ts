import { AuctionGroupingsSearchParam } from '../../constants/search';
import type { Auction_Group_Filter } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';

export const getAuctionGroupingsFilter = (
	searchParams: URLSearchParams
): Auction_Group_Filter => {
	const _and: Auction_Group_Filter[] = [
		{ status: { key: { _neq: Status_Enum.Archived } } },
	];

	const title = getDecodedSearchParam({
		searchParams,
		key: AuctionGroupingsSearchParam.Title,
	});

	if (title) {
		const filterItem: Auction_Group_Filter = {
			title: { _icontains: title },
		};

		_and.push(filterItem);
	}

	return {
		_and,
	};
};
