import { AuctionGroupingsSearchParam } from '../../constants/search';
import { AUCTION_GROUPINGS_DEFAULT_SORT } from '../../constants/sort';
import { getAuctionGroupingsFilter } from '../getAuctionGroupingsFilter/getAuctionGroupingsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import { gqlClient } from '$lib/gqlClient';
import type { GetAuctionGroupingsQuery } from '$lib/queries/__generated__/getAuctionGroupings.generated';
import { GetAuctionGroupingsDocument } from '$lib/queries/__generated__/getAuctionGroupings.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSort } from '$lib/utils/getSort/getSort';
import type { AuctionGroupingsSearchPageLoadEvent } from '$routes/auction-groupings/types';

const getOffset = (searchParams: URLSearchParams, limit = TABLE_PAGE_SIZE) => {
	const page = Number(
		getDecodedSearchParam({
			searchParams,
			key: SearchParam.Page,
		})
	);

	const offset = page ? (page - 1) * limit : 0;

	return offset;
};

export const auctionGroupingsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: AuctionGroupingsSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;

	let res: GetAuctionGroupingsQuery | null = null;
	const data = existingData || {};
	let auctionGroupings: GetAuctionGroupingsQuery['auction_group'] = [];
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: AuctionGroupingsSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filter = getAuctionGroupingsFilter(searchParams);
		const sort = getSort(searchParams, AUCTION_GROUPINGS_DEFAULT_SORT);
		const offset = getOffset(searchParams);

		res = await gqlClient.request(
			GetAuctionGroupingsDocument,
			{
				filter,
				sort,
				limit: TABLE_PAGE_SIZE,
				offset,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		auctionGroupings = res.auction_group || [];
		resultsCount = res.auction_group_aggregated?.[0].count?.id || 0;
	}

	return {
		...parentData,
		...data,
		auctionGroupings,
		resultsCount,
		showResults,
		currentPage,
	};
};
