import { GalleriesSearchParam } from '../../constants/search';
import { getGalleriesFilter } from '../getGalleriesFilter/getGalleriesFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type { GallerySearchInput } from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import {
	GallerySearchDocument,
	type GallerySearchQuery,
} from '$lib/custom-queries/__generated__/gallerySearch.generated';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import type { GalleriesSearchPageLoadEvent } from '$routes/galleries/types';

export const galleriesSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: GalleriesSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;
	const data = existingData || {};

	let res: GallerySearchQuery | null = null;
	let galleries: GallerySearchQuery['gallerySearch']['data'] | null = null;
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: GalleriesSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filters = getGalleriesFilter(searchParams);
		const offset = getOffset(searchParams);

		const input: GallerySearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
		};

		res = await gqlClientCustom.request(
			GallerySearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		galleries = res.gallerySearch.data || [];
		resultsCount = res.gallerySearchCount || 0;
	}

	return {
		...parentData,
		...data,
		galleries,
		resultsCount,
		showResults,
		currentPage,
	};
};
