// import { redirect } from '@sveltejs/kit';
// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import { gqlClient } from '$lib/gqlClient';
// import { GetEntityAttributeTypesDocument } from '$lib/queries/__generated__/getEntityAttributeTypes.generated';
// import type { GalleriesSearchPageServerLoadEvent } from '$routes/galleries/types';

// export const galleriesSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: GalleriesSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Gallery];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	const authHeaders = getAuthorizationHeaders(data);

// 	const attributeTypesReq = gqlClient.request(
// 		GetEntityAttributeTypesDocument,
// 		{},
// 		authHeaders
// 	);

// 	const [attributeTypesRes] = await Promise.all([attributeTypesReq]);

// 	const attributeTypes = attributeTypesRes?.entity_attribute_type;

// 	return {
// 		...data,
// 		attributeTypes,
// 	};
// };
