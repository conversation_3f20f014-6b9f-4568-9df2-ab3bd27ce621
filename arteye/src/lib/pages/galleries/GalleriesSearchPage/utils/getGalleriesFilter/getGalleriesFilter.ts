import { GalleriesSearchParam } from '../../constants/search';
import type {
	Entity_Attribute_Type_Enum,
	GallerySearchFilters,
	IntValueFilter,
	ValueFilterOperator,
} from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';
export const getGalleriesFilter = (searchParams: URLSearchParams) => {
	let filters: GallerySearchFilters = {};

	const name = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.Name,
	});

	const attributes = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.Attributes,
	});

	const yearFounded = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.YearFounded,
	});

	const yearFoundedRange = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.YearFoundedRange,
	});

	const yearDissolved = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.YearDissolved,
	});

	const yearDissolvedRange = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.YearDissolvedRange,
	});

	const addressCity = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.AddressCity,
	});

	const addressCountry = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.AddressCountry,
	});

	const representsArtist = getDecodedSearchParam({
		searchParams,
		key: GalleriesSearchParam.RepresentsArtist,
	});

	if (name) {
		filters = {
			...filters,
			nameOrId: name,
		};
	}

	if (attributes) {
		filters = {
			...filters,
			attributes: attributes.split(PARAM_SEPARATOR).map((attribute) => {
				return attribute as Entity_Attribute_Type_Enum;
			}),
		};
	}

	if (yearFounded) {
		filters = {
			...filters,
			yearFounded: getValueFilter({
				value: yearFounded,
				range: yearFoundedRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (yearDissolved) {
		filters = {
			...filters,
			yearDissolved: getValueFilter({
				value: yearDissolved,
				range: yearDissolvedRange as ValueFilterOperator,
				type: 'number',
			}) as IntValueFilter,
		};
	}

	if (addressCity) {
		filters = {
			...filters,
			location: {
				...filters.location,
				cityNameOrCode: addressCity,
			},
		};
	}

	if (addressCountry) {
		filters = {
			...filters,
			location: {
				...filters.location,
				countryNameOrCode: addressCountry,
			},
		};
	}

	if (representsArtist) {
		filters = {
			...filters,
			represents: representsArtist,
		};
	}

	return filters;
};
