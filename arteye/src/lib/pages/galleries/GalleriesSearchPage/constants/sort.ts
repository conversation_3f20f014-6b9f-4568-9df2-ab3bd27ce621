import { GallerySearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const GALLERIES_SORT_OPTIONS = sortByField(
	[
		{ label: 'Gallery name (A-Z)', value: GallerySearchSortField.Name },
		{
			label: 'Gallery name (Z-A)',
			value: getDescendingDirection(GallerySearchSortField.Name),
		},
		{ label: 'Location (A-Z)', value: GallerySearchSortField.Country },
		{
			label: 'Location (Z-A)',
			value: getDescendingDirection(GallerySearchSortField.Country),
		},
		{ label: 'Date created (Asc)', value: GallerySearchSortField.DateCreated },
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(GallerySearchSortField.DateCreated),
		},
		{ label: 'Date updated (Asc)', value: GallerySearchSortField.DateUpdated },
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(GallerySearchSortField.DateUpdated),
		},
		{
			label: 'Number of activities (Asc)',
			value: GallerySearchSortField.NumberOfActivities,
		},
		{
			label: 'Number of activities (Desc)',
			value: getDescendingDirection(GallerySearchSortField.NumberOfActivities),
		},
	],
	'label'
);

export const GALLERIES_DEFAULT_SORT = GallerySearchSortField.Name;
