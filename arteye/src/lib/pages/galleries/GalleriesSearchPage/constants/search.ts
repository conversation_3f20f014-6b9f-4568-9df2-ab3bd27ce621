import { SearchParam } from '$lib/types/types';

export enum GalleriesSearchParam {
	ShowResults = SearchParam.ShowResults,
	Sort = SearchParam.Sort,
	Name = 'name',
	Attributes = 'attributes',
	YearFounded = 'yearFounded',
	YearFoundedRange = 'yearFoundedRange',
	YearDissolved = 'yearDissolved',
	YearDissolvedRange = 'yearDissolvedRange',
	AddressCity = 'addressCity',
	AddressCountry = 'addressCountry',
	RepresentsArtist = 'representsArtist',
}
