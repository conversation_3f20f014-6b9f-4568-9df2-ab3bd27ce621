<script lang="ts">
	import { GalleriesSearchParam } from './constants/search';
	import { GalleriesSearchForm } from './GalleriesSearchForm';
	import type { GalleriesSearchFormFields } from './GalleriesSearchForm/GalleriesSearchForm.svelte';
	import { GalleriesTable } from './GalleriesTable';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { GalleriesSearchPageData } from '$routes/galleries/types';

	let data = $derived(getPageData<GalleriesSearchPageData>(page.data));
	let attributeTypes = $derived(data.user?.dropdowns.attributeTypes || []);
	let showResults = $derived(data.showResults);

	let attributesOptions: MultiSelectOption[] = $state([]);

	$effect(() => {
		attributesOptions = attributeTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	const formatParamString = (
		{
			sort,
			name,
			attributes,
			yearFounded,
			yearFoundedRange,
			yearDissolved,
			yearDissolvedRange,
			addressCity,
			addressCountry,
			representsArtist,
		}: GalleriesSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const params: Record<GalleriesSearchParam, string> = {
			[GalleriesSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[GalleriesSearchParam.ShowResults]: showResults,
			[GalleriesSearchParam.Name]: name,
			[GalleriesSearchParam.Attributes]:
				getSearchParamFromStringArray(attributes),
			[GalleriesSearchParam.YearFounded]: yearFounded,
			[GalleriesSearchParam.YearFoundedRange]: yearFounded
				? yearFoundedRange
				: '',
			[GalleriesSearchParam.YearDissolved]: yearDissolved,
			[GalleriesSearchParam.YearDissolvedRange]: yearDissolved
				? yearDissolvedRange
				: '',
			[GalleriesSearchParam.AddressCity]: addressCity,
			[GalleriesSearchParam.AddressCountry]: addressCountry,
			[GalleriesSearchParam.RepresentsArtist]: representsArtist,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<GalleriesSearchForm {attributesOptions} {formatParamString} />
</div>

{#if showResults}
	<GalleriesTable />
{/if}
