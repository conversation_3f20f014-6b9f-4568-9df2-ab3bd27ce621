import { error } from '@sveltejs/kit';
import { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetGalleryDetailsDocument } from '$lib/queries/__generated__/getGalleryDetails.generated';
import { GetRelationshipsDocument } from '$lib/queries/__generated__/getRelationships.generated';
import { PipelineSourceItem } from '$lib/types/types';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { GalleryPageLoadEvent } from '$routes/galleries/[id]/types';

export const galleryDetailsPageLoad = async ({
	parent,
	params,
}: GalleryPageLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();
	const galleryId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const galleryReq = (async () => {
		if (!galleryId) {
			return null;
		}

		const res = await gqlClient.request(
			GetGalleryDetailsDocument,
			{
				filter: {
					_and: [
						{
							id: {
								_eq: galleryId,
							},
						},
						{ status: { key: { _neq: Status_Enum.Archived } } },
					],
				},
			},
			authHeaders
		);

		return res?.gallery?.[0];
	})();

	const relationshipsReq = (async () => {
		if (!galleryId) {
			return [];
		}

		const relationshipsRes = await gqlClient.request(
			GetRelationshipsDocument,
			{
				filter: {
					_and: [
						{ status: { key: { _neq: Status_Enum.Archived } } },
						{
							_or: [
								{
									from_entity: { gallery: { id: { _eq: galleryId } } },
								},
								{
									to_entity: { gallery: { id: { _eq: galleryId } } },
								},
							],
						},
					],
				},
			},
			authHeaders
		);

		return relationshipsRes.relationship;
	})();

	const pipelineSourceReq = (async () => {
		if (galleryId === PARAM_NEW || !galleryId) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{
				collection: PipelineSourceItem.Gallery,
				id: galleryId,
			},
			authHeaders
		);

		return res.getDatasource?.data_source;
	})();

	const legacyIdReq =
		galleryId === PARAM_NEW || isOnDev() || !galleryId
			? { getLegacyId: { legacyId: '' } }
			: gqlClientCustom.request(
					GetLegacyIdDocument,
					{
						collection: 'gallery',
						id: galleryId,
					},
					getAuthorizationHeaders(data)
				);

	const [
		gallery,
		relationships,
		pipelineSource,
		legacyIdRes,
		{ dropzoneUrlDialogForm },
	] = await Promise.all([
		galleryReq,
		relationshipsReq,
		pipelineSourceReq,
		legacyIdReq,
		getDropzoneUrlDialogSuperform(),
	]);

	if (!gallery && galleryId) {
		error(404, 'Gallery cannot be found');
	}

	const legacyId = legacyIdRes?.getLegacyId?.legacyId;

	return {
		...data,
		legacyId,
		gallery,
		relationships,
		pipelineSource,
		dropzoneUrlDialogForm,
	};
};
