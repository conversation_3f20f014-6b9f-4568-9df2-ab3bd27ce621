<script module lang="ts">
	export type AuctionDetailsForm = Partial<
		Pick<
			NonNullable<AuctionDetailsPageData['auction']>,
			| 'id'
			| 'auction_house'
			| 'auction_types'
			| 'sale_name'
			| 'sale_number'
			| 'currency'
			| 'sale_url'
			| 'auction_start_date'
			| 'auction_end_date'
			| 'auction_timezone'
		>
	> & {
		auction_start_date_date?: string | null | undefined;
		auction_start_date_time?: string | null | undefined;
		auction_end_date_date?: string | null | undefined;
		auction_end_date_time?: string | null | undefined;
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { twMerge } from 'tailwind-merge';
	import AuctionHouseAutoComplete, {
		formatActionHouse,
		formatEntity,
	} from './AuctionHouseAutoComplete/AuctionHouseAutoComplete.svelte';
	import { page } from '$app/state';
	import { AccordionItem } from '$global/components/Accordion';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Autocomplete } from '$global/components/QueryAutocomplete/Autocomplete';
	import { Option } from '$global/components/QueryAutocomplete/Option';
	import { SelectedOption } from '$global/components/QueryAutocomplete/SelectedOption';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Auction_Auction_Type } from '$gql/types';
	import type { GetEntitiesQuery } from '$lib/queries/__generated__/getEntities.generated';
	import type { AuctionDetailsPageData } from '$routes/auctions/[id]/types';

	interface Props {
		auctionDetailsForm: AuctionDetailsForm;
		onChange: (form: AuctionDetailsForm) => void;
		onDeleteTypes: (auctionTypes: Auction_Auction_Type[]) => void;
		class?: string;
	}

	let { ...props }: Props = $props();

	let data = $derived(getPageData<AuctionDetailsPageData>(page.data));
	let auctionTypes = $derived(data.auctionTypes);
	let currencies = $derived(data.user?.dropdowns?.currencies || []);
	let timezones = $derived(data.timezones);

	let timezoneSelectedOption: OptionType | null = $state(
		props.auctionDetailsForm?.auction_timezone
			? { line1: props.auctionDetailsForm?.auction_timezone.timezone }
			: null
	);

	const autocompleteValue = writable('');

	let timezonesOptions = $derived(
		timezones
			?.map((timezone) => ({
				line1: `${timezone?.timezone}`,
			}))
			.filter((option) =>
				option.line1.toLowerCase().includes($autocompleteValue.toLowerCase())
			)
			.sort((optionA, optionB) => optionA.line1.length - optionB.line1.length)
	);

	let auctionTypeOptions = $derived(
		auctionTypes?.map((auction) => ({
			label: `${auction?.name}`,
			value: `${auction?.key}`,
		}))
	);

	let currencyOptions = $derived(
		(() => {
			const currencyMap = currencies?.reduce<
				Record<string, (typeof currencies)[number]>
			>((acc, currency) => {
				return {
					...acc,
					[currency?.code]: currency,
				};
			}, {});

			let mainCurrencyCodes = [
				'USD',
				'GBP',
				'EUR',
				'HKD',
				'CHF',
				'JPY',
				'CNY',
				'SGD',
				'CAD',
				'AUD',
			];

			let mainCurrencies = mainCurrencyCodes
				.filter((code) => !!currencyMap[code])
				.map((code) => {
					return currencyMap[code];
				});

			let otherCurrencies = currencies?.filter(
				(currency) => !mainCurrencyCodes.includes(currency?.code)
			);

			return [...mainCurrencies, ...otherCurrencies]?.map((currency) => ({
				label: `${currency?.code}`,
				value: `${currency?.code}`,
			}));
		})()
	);

	const dataCy = 'auction-details-card';

	const handleInputChange =
		(field: keyof AuctionDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			props.onChange({ ...props.auctionDetailsForm, [field]: value });
		};

	const handleAuctionTypesChange = (
		event: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		let auctionTypes = selected.map((sel) => {
			let match = (props.auctionDetailsForm.auction_types || []).find(
				(type) => type?.auction_type_key?.key === sel.value
			);
			if (match) {
				return match;
			} else {
				return {
					auction_type_key: { key: sel.value, name: sel.label },
					auction_id: { id: props.auctionDetailsForm.id },
				};
			}
		}) as Auction_Auction_Type[];

		let deleting = (props.auctionDetailsForm?.auction_types || []).filter(
			(type) =>
				!selected.some((sel) => sel.value === type?.auction_type_key?.key)
		) as Auction_Auction_Type[];

		if (deleting.length > 0) {
			props.onDeleteTypes(deleting);
		}

		props.onChange({
			...props.auctionDetailsForm,
			auction_types: auctionTypes,
		});
	};

	const handleChangeCurrency = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		props.onChange({
			...props.auctionDetailsForm,
			currency: !selected.length
				? null
				: {
						name: selected?.[0].label,
						code: selected?.[0].value,
					},
		});
	};

	const handleRemoveTime = () => {
		props.onChange({
			...props.auctionDetailsForm,
			auction_timezone: null,
		});
	};

	const handleChangeTimezone = (e: { detail: { value: OptionType } }) => {
		const timezone = timezones.find(
			(timezone) => timezone.timezone === e.detail.value.line1
		);

		if (timezone) {
			props.onChange({
				...props.auctionDetailsForm,
				auction_timezone: timezone,
			});
		}

		return Promise.resolve();
	};

	let selectedOption = $state(
		props.auctionDetailsForm.auction_house
			? formatActionHouse(props.auctionDetailsForm.auction_house)
			: null
	);

	const handleAuctionHouseChange = (e: {
		detail: {
			value: NonNullable<GetEntitiesQuery['entity'][number]>;
		};
	}) => {
		props.onChange({
			...props.auctionDetailsForm,
			auction_house: {
				organisation: e.detail.value.organisation,
			},
		});

		selectedOption = formatEntity(e.detail.value);
		return Promise.resolve();
	};

	const handleTimezoneKeyUp = (e: Event) => {
		autocompleteValue.set((e.target as EventTarget & { value: string }).value);
	};

	const getSelectedCurrencyOptions = () => {
		return currencyOptions?.filter(
			(option) => option.value === props.auctionDetailsForm.currency?.code
		);
	};
</script>

<AccordionItem
	dataCy="test"
	title="Auction details"
	class={twMerge('rounded-md border bg-white', props.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4">
		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-2">
				<InputLabel
					{dataCy}
					required
					variant="label3"
					classes={{ container: 'mb-2' }}
				>
					Auction house
				</InputLabel>

				<AuctionHouseAutoComplete
					bind:selectedOption
					{dataCy}
					onChange={handleAuctionHouseChange}
				/>
			</div>
			<div class="col-span-1">
				<MultiSelect
					name="auction-types"
					dataCy={`${dataCy}-auction-types`}
					label="Auction type/s"
					required
					selected={(props.auctionDetailsForm?.auction_types || [])
						.map((auction_type) => ({
							label: `${auction_type?.auction_type_key?.name}`,
							value: `${auction_type?.auction_type_key?.key}`,
						}))
						.filter(
							(attr, i, arr) =>
								arr.findIndex((attr2) => attr.value === attr2.value) === i
						)}
					placeholder="Type or select"
					options={auctionTypeOptions}
					class="col-span-1"
					size="sm"
					onChange={handleAuctionTypesChange}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-sale-name`}
					name="sale-name"
					required
					placeholder=""
					label="Sale name"
					value={props.auctionDetailsForm?.sale_name || ''}
					size="sm"
					onkeyup={handleInputChange('sale_name')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-sale-number`}
					name="sale-number"
					placeholder=""
					label="Sale number"
					value={getDayFromDirectus(props.auctionDetailsForm.sale_number || '')}
					size="sm"
					onkeyup={handleInputChange('sale_number')}
				/>
			</div>
		</div>

		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<MultiSelect
					name="currency"
					dataCy={`${dataCy}-currency`}
					label="Currency"
					required
					selected={getSelectedCurrencyOptions()}
					placeholder="Type or select"
					options={currencyOptions}
					size="sm"
					onChange={handleChangeCurrency}
					maxSelect={1}
				/>
			</div>
			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-sale-url`}
					name="sale-url"
					placeholder=""
					label="Sale url"
					value={props.auctionDetailsForm?.sale_url || ''}
					size="sm"
					onkeyup={handleInputChange('sale_url')}
				/>
			</div>
			<div class="col-span-1">
				<InputLabel dataCy={`${dataCy}-auction-start-date`} class="mb-[7px]">
					Auction start date
				</InputLabel>
				<div class="flex gap-1">
					<Input
						dataCy={`${dataCy}-auction-start-date`}
						type="date"
						name="auction-start-date"
						placeholder=""
						label=""
						value={getDayFromDirectus(
							props.auctionDetailsForm.auction_start_date_date || ''
						)}
						size="sm"
						onkeyup={handleInputChange('auction_start_date_date')}
						onchange={handleInputChange('auction_start_date_date')}
					/>

					<Input
						dataCy={`${dataCy}-auction-start-time`}
						type="time"
						name="auction-start-time"
						placeholder=""
						class="min-w-[110px]"
						value={props.auctionDetailsForm.auction_start_date_time || ''}
						size="sm"
						onkeyup={handleInputChange('auction_start_date_time')}
						onchange={handleInputChange('auction_start_date_time')}
					/>
				</div>
			</div>

			<div class="col-span-1">
				<InputLabel dataCy={`${dataCy}-auction-start-date`} class="mb-[7px]">
					Auction end date
				</InputLabel>
				<div class="flex gap-1">
					<Input
						dataCy={`${dataCy}-auction-end-date`}
						type="date"
						name="auction-end-date"
						placeholder=""
						label=""
						value={getDayFromDirectus(
							props.auctionDetailsForm.auction_end_date_date || ''
						)}
						size="sm"
						onkeyup={handleInputChange('auction_end_date_date')}
						onchange={handleInputChange('auction_end_date_date')}
					/>

					<Input
						dataCy={`${dataCy}-auction-end-time`}
						type="time"
						name="auction-end-time"
						placeholder=""
						class="min-w-[110px]"
						value={props.auctionDetailsForm.auction_end_date_time || ''}
						size="sm"
						onkeyup={handleInputChange('auction_end_date_time')}
						onchange={handleInputChange('auction_end_date_time')}
					/>
				</div>
			</div>

			<div class="col-span-1">
				<InputLabel {dataCy} variant="label3" class="mb-2">Time zone</InputLabel
				>

				<Autocomplete
					onChange={handleChangeTimezone}
					onkeyup={handleTimezoneKeyUp}
					onRemoveSelectedOption={handleRemoveTime}
					bind:selectedOption={timezoneSelectedOption}
					showResultsWhenEmpty
					placeholder="-Select-"
					size="sm"
					name="time-zone"
					dataCy={`${dataCy}-time-zone`}
					OptionComponent={Option}
					SelectedOptionComponent={SelectedOption}
					value={autocompleteValue}
					options={timezonesOptions}
					classes={{
						noResults: 'py-2',
						listWithOptions:
							'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
						option: {
							line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
							line2: 'hidden',
							line3: 'hidden',
							line4: 'hidden',
							line5: 'hidden',
						},
						selectedOption: {
							line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
							button: 'max-w-full',
							wrapper: 'py-1.5 bg-white',
							line2: 'hidden',
							line3: 'hidden',
							line4: 'hidden',
							line5: 'hidden',
						},
					}}
				>
					{#snippet noResults()}
						<span
							class="font-silka text-[0.75rem] sm:text-[0.75rem] font-[400] px-3"
						>
							No timezone found
						</span>
					{/snippet}
				</Autocomplete>
			</div>
		</div>
	</div>
</AccordionItem>
