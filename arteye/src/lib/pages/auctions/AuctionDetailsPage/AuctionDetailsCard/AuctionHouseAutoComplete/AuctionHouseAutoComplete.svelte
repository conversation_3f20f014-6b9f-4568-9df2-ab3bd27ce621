<script lang="ts" module>
	export const formatEntity = (entity: GetEntitiesQuery['entity'][number]) => {
		const url = (() => {
			if (entity.type?.key === 'person') {
				if (entity?.artist) {
					return `${Routes.Artists}/${entity?.artist?.id}`;
				} else {
					return `${Routes.People}/${entity?.person?.id}`;
				}
			}
			return `${Routes.Organisations}/${entity?.organisation?.id}`;
		})();

		const location = [
			entity?.organisation?.location?.name,
			entity?.organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
	export const formatActionHouse = (
		auctionHouse: AuctionDetailsForm['auction_house']
	) => {
		const url = (() => {
			return `${Routes.Organisations}/${auctionHouse?.organisation?.id}`;
		})();

		const location = [
			auctionHouse?.organisation?.location?.name,
			auctionHouse?.organisation?.location?.country?.name,
		]
			.filter(Boolean)
			.join(', ');

		return {
			line1: `${auctionHouse?.organisation?.name}`,
			line2: url,
			line3: `${auctionHouse?.organisation?.type?.[0]?.organisation_type_key?.key}`,
			line4: `${auctionHouse?.organisation?.id}`,
			line1Suffix: location ? `(${location})` : '',
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import type { AuctionDetailsForm } from '../AuctionDetailsCard.svelte';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Status_Enum } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetEntitiesQuery,
		GetEntitiesQueryVariables,
	} from '$lib/queries/__generated__/getEntities.generated';
	import { GetEntitiesDocument } from '$lib/queries/__generated__/getEntities.generated';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	let entities: GetEntitiesQuery['entity'] | [] = [];

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: NonNullable<GetEntitiesQuery['entity'][number]>;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getVariables = (value: string): GetEntitiesQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			sort: ['organisation.location.name', '-aggregations.activity_count'],
			filter: {
				_and: [
					{
						organisation: {
							type: {
								organisation_type_key: {
									key: {
										_eq: 'AUCTION_HOUSE',
									},
								},
							},
						},
					},
					{ status: { key: { _neq: Status_Enum.Archived } } },
					...(value ? [{ name: { _icontains: value } }] : []),
				],
			},
		};
	};

	const getOptions = (data: GetEntitiesQuery | undefined) => {
		setTimeout(() => {
			entities = data?.entity || [];
		}, 0);

		return [...(data?.entity || []).map(formatEntity)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between items-center rounded border border-gray-200 bg-white px-3 h-[32px]':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		emptyValueResponse={{ entity: [] }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClient}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={GetEntitiesDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-auction-house-autocomplete`}
				>No results found.</NoResults
			>{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
