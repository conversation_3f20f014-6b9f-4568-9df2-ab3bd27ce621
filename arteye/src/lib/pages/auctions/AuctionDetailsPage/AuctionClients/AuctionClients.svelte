<script lang="ts" module>
	export type AuctionClientsType = AuctionClientType[];
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import {
		AuctionClientRow,
		type AuctionClientType,
		AuctionClientFieldName,
	} from './AuctionClientRow';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import type { Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { formatEntity } from '$lib/components/details-pages/EntityCards/RelationshipsCard/RelationshipRow/EntityAutocomplete/EntityAutocomplete.svelte';
	import type { AuctionDetailsPageData } from '$routes/auctions/[id]/types';

	const headers = [
		{
			title: 'Paddle number',
			fieldName: AuctionClientFieldName.PaddleNumber,
		},
		{ title: 'Entity name', fieldName: AuctionClientFieldName.Entity },
		{
			title: 'No. lots purchased',
			fieldName: AuctionClientFieldName.NbBidsPurchased,
		},
		{ title: '' },
	];

	interface Props {
		auctionId: string | null | undefined;
		auctionClients: AuctionClientsType;
		auctionClientStats: AuctionDetailsPageData['auctionClientStats'];
		onChange: (notes: AuctionClientsType) => void;
		class?: string;
	}

	let {
		auctionId,
		auctionClients = $bindable(),
		auctionClientStats,
		onChange,
		...rest
	}: Props = $props();

	const onAuctionClientsChange =
		(index: number) => (newAuctionClient: AuctionClientType) => {
			onChange(
				auctionClients.map((auctionClient, i) =>
					i === index ? newAuctionClient : auctionClient
				)
			);
		};

	const dataCy = 'relationships';

	const handleRelationshipAdd = () => {
		auctionClients = [
			...auctionClients,
			{
				paddle_number: '',
				isNew: true,
			} as AuctionClientType,
		];
	};
</script>

<AccordionItem
	dataCy="test"
	title="Auction clients"
	class={twMerge('rounded-md border bg-white', rest.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4 max-lg:overflow-x-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1200px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName}
						<TableHeader {dataCy}>
							{header.title}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleRelationshipAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if auctionClients}
					{#each auctionClients as auctionClient, index}
						{@const isPaddleNumberDuplicated =
							auctionClients.filter(
								(a) =>
									auctionClient.paddle_number &&
									a.paddle_number?.toUpperCase() ===
										auctionClient.paddle_number.toUpperCase()
							).length > 1}

						{#if !auctionClient.isDeleted}
							<AuctionClientRow
								{headers}
								{auctionId}
								{isPaddleNumberDuplicated}
								{auctionClientStats}
								{auctionClient}
								{index}
								onChange={onAuctionClientsChange(index)}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</AccordionItem>
