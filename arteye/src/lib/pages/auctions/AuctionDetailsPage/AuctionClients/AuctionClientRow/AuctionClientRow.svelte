<script lang="ts">
	import { formatEntity } from '../../AuctionDetailsCard/AuctionHouseAutoComplete/AuctionHouseAutoComplete.svelte';
	import type { AuctionClientType } from './types';
	import { AuctionClientFieldName } from './types';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { LinkButton } from '$global/components/LinkButton';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { EntityAutocomplete } from '$lib/components/details-pages/EntityCards/RelationshipsCard/RelationshipRow/EntityAutocomplete';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import { Routes } from '$lib/constants/routes';
	import type { AuctionDetailsPageData } from '$routes/auctions/[id]/types';

	interface Props {
		index: number;
		auctionId: string | null | undefined;
		isPaddleNumberDuplicated: boolean;
		auctionClientStats: AuctionDetailsPageData['auctionClientStats'];
		auctionClient: AuctionClientType;
		headers: { fieldName?: AuctionClientFieldName }[];
		onChange: (relationship: AuctionClientType) => void;
	}

	let {
		index,
		auctionId,
		isPaddleNumberDuplicated,
		auctionClientStats,
		auctionClient,
		headers,
		onChange,
	}: Props = $props();

	const dataCy = 'auction-client-row';

	let selectedOption = $state<OptionType | null>(null);

	$effect(() => {
		if (!auctionClient.entity?.id) {
			selectedOption = null;
			return;
		}

		selectedOption = formatEntity(auctionClient.entity);
	});

	const handleDeleteAuctionClient = () => {
		onChange({
			...auctionClient,
			isDeleted: true,
		});
	};

	const handlePaddleNumberKeyUp = (event: Event) => {
		onChange({
			...auctionClient,
			['paddle_number']: `${(event.target as HTMLInputElement).value}`,
			isUpdated: true,
		});
	};

	const handleEntityRemove = () => {
		onChange({
			...auctionClient,
			entity: null,
			isUpdated: true,
		});
	};

	const handleEntityChange = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const option = e.detail.value;

		onChange({
			...auctionClient,
			isUpdated: true,
			entity: {
				id: `${option.line4}`,
				type: {
					key: `${option.line3}`,
				},
				name: `${option.line1}`,
			},
			subTitle: `${option.line5}`,
		});

		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			{@const lotsIds = auctionClientStats[auctionClient.id]?.lotsIds}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center gap-1">
					<UpdateHistoryTooltip updateHistory={auctionClient} />

					{#if lotsIds?.length && auctionId}
						<LinkButton
							newTab
							href={`${
								Routes.ArtworksAndActivities
							}?showResults=true&auctionNameOrId=${auctionId}&lotNumber=${lotsIds.join(
								','
							)}&searchType=advanced`}
							dataCy={`${dataCy}-view-lots`}
							variant="secondary"
							size="sm"
						>
							View lots
						</LinkButton>
					{/if}

					<Button
						onclick={handleDeleteAuctionClient}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === AuctionClientFieldName.PaddleNumber}
			<TableCell {dataCy} class="">
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-paddle-number`}
						name="paddle-number"
						placeholder="Paddle Number"
						value={auctionClient.paddle_number}
						onkeyup={handlePaddleNumberKeyUp}
						size="sm"
						error={isPaddleNumberDuplicated
							? 'This paddle number is duplicated'
							: ''}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === AuctionClientFieldName.Entity}
			<TableCell {dataCy}>
				{#snippet custom()}
					<EntityAutocomplete
						{selectedOption}
						{dataCy}
						onRemove={handleEntityRemove}
						onChange={handleEntityChange}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === AuctionClientFieldName.NbBidsPurchased}
			<TableCell {dataCy}>
				{auctionClientStats[auctionClient.id]?.nbLotsPurchased || 0}
			</TableCell>
		{/if}
	{/each}
</TableRow>
