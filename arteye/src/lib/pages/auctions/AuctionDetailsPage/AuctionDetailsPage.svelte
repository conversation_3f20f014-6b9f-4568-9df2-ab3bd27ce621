<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { AuctionClients } from './AuctionClients';
	import { AuctionDetailsCard } from './AuctionDetailsCard';
	import type { AuctionDetailsForm } from './AuctionDetailsCard';
	import { AuctionImage } from './AuctionImage';
	import type { AuctionImageType } from './AuctionImage';
	import { invalidateAll, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Auction_Auction_Type } from '$gql/types';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { UpdateAuctionItemDocument } from '$lib/queries/__generated__/updateAuctionItem.generated';
	import { formatAuctionImage } from '$lib/utils/exhibitionFormatters/exhibitionFormatters';
	import { getQueryStrings } from '$lib/utils/getQueryStrings/getQueryStrings';
	import { mutateAuction } from '$lib/utils/mutation-handlers/mutateAuction/mutateAuction';
	import type { AuctionDetailsPageData } from '$routes/auctions/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let data = $derived(getPageData<AuctionDetailsPageData>(page.data));

	let auction = $derived(data.auction as AuctionDetailsPageData['auction']);
	let auctionClients = $state((() => data.auctionClients)()) as (NonNullable<
		AuctionDetailsPageData['auctionClients']
	>[number] & { isNew?: boolean; isDeleted?: boolean })[];

	$effect(() => {
		auctionClients = data.auctionClients as (NonNullable<
			AuctionDetailsPageData['auctionClients']
		>[number] & { isNew?: boolean; isDeleted?: boolean })[];
	});

	let auctionClientStats = $derived(
		data.auctionClientStats as AuctionDetailsPageData['auctionClientStats']
	);

	let auctionDetailsForm = $state(
		(() => ({
			id: auction?.id,
			auction_house: auction?.auction_house,
			auction_types: auction?.auction_types,
			sale_name: auction?.sale_name,
			sale_number: auction?.sale_number,
			currency: auction?.currency,
			sale_url: auction?.sale_url,
			auction_start_date_date: auction?.local_auction_start_date
				? dayjs(auction?.local_auction_start_date.slice(0, -1)).format(
						'YYYY-MM-DD'
					)
				: null,
			auction_start_date_time: auction?.local_auction_start_date
				? dayjs(auction?.local_auction_start_date.slice(0, -1)).format('HH:mm')
				: null,
			auction_end_date_date: auction?.local_auction_end_date
				? dayjs(auction?.local_auction_end_date.slice(0, -1)).format(
						'YYYY-MM-DD'
					)
				: null,
			auction_end_date_time: auction?.local_auction_end_date
				? dayjs(auction?.local_auction_end_date.slice(0, -1)).format('HH:mm')
				: null,
			auction_timezone: auction?.auction_timezone,
		}))() as AuctionDetailsForm
	);

	$effect(() => {
		auctionDetailsForm = {
			id: auction?.id,
			auction_house: auction?.auction_house,
			auction_types: auction?.auction_types,
			sale_name: auction?.sale_name,
			sale_number: auction?.sale_number,
			currency: auction?.currency,
			sale_url: auction?.sale_url,
			auction_start_date_date: auction?.local_auction_start_date
				? dayjs(auction?.local_auction_start_date.slice(0, -1)).format(
						'YYYY-MM-DD'
					)
				: null,
			auction_start_date_time: auction?.local_auction_start_date
				? dayjs(auction?.local_auction_start_date.slice(0, -1)).format('HH:mm')
				: null,
			auction_end_date_date: auction?.local_auction_end_date
				? dayjs(auction?.local_auction_end_date.slice(0, -1)).format(
						'YYYY-MM-DD'
					)
				: null,
			auction_end_date_time: auction?.local_auction_end_date
				? dayjs(auction?.local_auction_end_date.slice(0, -1)).format('HH:mm')
				: null,
			auction_timezone: auction?.auction_timezone,
		} as AuctionDetailsForm;
	});

	let auctionTypesToDelete = $state([]) as Auction_Auction_Type[];

	$effect(() => {
		auctionTypesToDelete = [] as Auction_Auction_Type[];
	});

	let auctionImage = $state(
		(() => formatAuctionImage(auction, page.data.user.access_token))()
	) as ReturnType<typeof formatAuctionImage>;

	$effect(() => {
		auctionImage = formatAuctionImage(auction, page.data.user.access_token);
	});

	let crumbs = $derived([
		{
			label: 'Search auctions',
			href: userRoutes.routes.auction,
		},
		{ label: `${auction?.sale_name || 'New Auction'}` },
	]);

	let loading = $state(false);
	let showSaveBar = $state(false);
	let deleting = $state(false);

	const dataCy = 'auction-details';

	const handleClickDeleteAuction = async () => {
		try {
			deleting = true;
			await gqlClient.request(
				UpdateAuctionItemDocument,
				{
					id: auction.id,
					data: { status: { key: Status_Enum.Archived } },
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'This auction has been successfully deleted.',
			});

			goto(userRoutes.routes.auction);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while deleting this auction.',
			});

			deleting = false;
		}
	};

	const handleAuctionImageChange = (image: AuctionImageType) => {
		auctionImage = image;
		showSaveBar = true;
	};

	const handleAuctionDetailsForm = (auctionDetails: AuctionDetailsForm) => {
		auctionDetailsForm = auctionDetails;
		showSaveBar = true;
	};

	const handleDeleteTypes = (auctionTypes: Auction_Auction_Type[]) => {
		auctionTypesToDelete = auctionTypes;
		showSaveBar = true;
	};

	const handleAuctionClientsChange = (
		newAuctionClients: typeof auctionClients
	) => {
		auctionClients = newAuctionClients;
		showSaveBar = true;
	};

	const create = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			const auctionId = await mutateAuction({
				auctionDetailsForm,
				auctionClients,
				auctionImage,
				headers,
			});

			showToast({
				variant: 'success',
				message: `This auction has been successfully created.`,
			});

			await goto(`${Routes.Auctions}/${auctionId}`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while creating this auction. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const update = async () => {
		try {
			const headers = getAuthorizationHeaders(data);

			await mutateAuction({
				auctionDetailsForm,
				auctionClients,
				auctionImage,
				headers,
			});

			showToast({
				variant: 'success',
				message: `This auction has been successfully updated.`,
			});

			await invalidateAll();
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while updating this auction. Please contact the support team.',
			});
		} finally {
			loading = false;
			showSaveBar = false;
		}
	};

	const handleSaveClick = () => {
		loading = true;

		if (auctionDetailsForm?.id) {
			update();
		} else {
			create();
		}
	};
</script>

<div class={classNames('pb-24', { 'pointer-events-none': loading })}>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if auction?.id}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&activityType=AUCTION&auctionNameOrId=${auction.id}&searchType=advanced&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					dataCy={`${dataCy}-view-lots`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					view lots
				</LinkButton>

				<Button
					onclick={() => {
						navigator.clipboard.writeText(`${auction?.id}`);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy auction id
				</Button>

				<Button
					onclick={handleClickDeleteAuction}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
			</div>
		{/if}
	</div>

	{#if auction?.id}
		<CreateUpdate
			updateHistory={auction}
			pipelineSource={data.pipelineSource}
			class="block mb-5 mt-[-12px]"
		/>
	{/if}

	<AuctionDetailsCard
		{auctionDetailsForm}
		onChange={handleAuctionDetailsForm}
		onDeleteTypes={handleDeleteTypes}
		class="mb-4"
	/>

	<AuctionClients
		auctionId={auctionDetailsForm?.id}
		{auctionClients}
		{auctionClientStats}
		onChange={handleAuctionClientsChange}
		class="mb-4"
	/>
	<AuctionImage
		{auctionImage}
		onAuctionImageChange={handleAuctionImageChange}
	/>
</div>

<PageSaveBar
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
	{loading}
	disabled={loading ||
		// (!!auctionDetailsForm?.auction_start_date_date &&
		// 	!auctionDetailsForm?.auction_start_date_time) ||
		// (!!auctionDetailsForm?.auction_start_date_time &&
		// 	!auctionDetailsForm?.auction_start_date_date) ||
		// (!!auctionDetailsForm?.auction_end_date_date &&
		// 	!auctionDetailsForm?.auction_end_date_time) ||
		// (!!auctionDetailsForm?.auction_end_date_time &&
		// 	!auctionDetailsForm?.auction_end_date_date) ||
		!auctionDetailsForm.auction_house ||
		!auctionDetailsForm?.auction_types?.length ||
		!auctionDetailsForm?.sale_name ||
		!auctionDetailsForm?.currency ||
		!auctionClients.every(
			(auctionClient) =>
				auctionClient.isDeleted ||
				(!auctionClient.isDeleted &&
					(auctionClient.entity || auctionClient.paddle_number))
		)}
/>
