<script lang="ts">
	import { type AuctionImageType } from './types';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import type { DropzoneFile } from '$global/components/Dropzone';
	import { Dropzone } from '$global/components/Dropzone';
	import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
	import type { AuctionDetailsPageData } from '$routes/auctions/[id]/types';

	interface Props {
		auctionImage: AuctionImageType;
		onAuctionImageChange: (image: AuctionImageType) => void;
	}

	let { auctionImage, onAuctionImageChange }: Props = $props();

	const accept = ['image/png', 'image/jpeg'];
	const maxSize = 100000000;

	let files: DropzoneFile[] = $state([]);

	const handleSubmitProfileImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		onAuctionImageChange({
			file: newFile,
			url: base64Image.url,
			filename_download: '',
			id: '',
			storage: '',
		});
	};

	const handleClickDeleteImage = () => {
		onAuctionImageChange(null);
	};

	const dataCyPrefix = 'auction-image-upload';
</script>

<AccordionItem
	dataCy="test"
	title="Auction image"
	class="rounded-md border bg-white"
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4 pb-6">
		<div class="relative flex h-[200px] w-full items-center p-4">
			<div
				class="relative flex h-[200px] w-full items-center justify-center p-4"
			>
				{#if auctionImage}
					<img
						class="max-h-40 object-contain"
						src={auctionImage.url}
						alt="profile"
					/>

					<Button
						dataCy={`${dataCyPrefix}-delete-profile`}
						class="absolute bottom-2 right-2 h-[2rem] w-[2rem] px-0"
						variant="secondary"
						onclick={handleClickDeleteImage}
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				{:else}
					<Dropzone
						{maxSize}
						bind:files
						dataCy={dataCyPrefix}
						class="h-full w-full"
						showFiles={false}
						{accept}
						onSubmitFiles={handleSubmitProfileImage}
					/>
				{/if}
			</div>
		</div>
	</div>
</AccordionItem>
