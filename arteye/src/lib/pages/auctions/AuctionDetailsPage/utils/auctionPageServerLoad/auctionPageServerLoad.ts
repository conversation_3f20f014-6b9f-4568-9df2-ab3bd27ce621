import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { GetDatasourceDocument } from '$lib/custom-queries/__generated__/getDatasource.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetAuctionClientDocument } from '$lib/queries/__generated__/getAuctionClient.generated';
import { GetAuctionClientStatsDocument } from '$lib/queries/__generated__/getAuctionClientStats.generated';
import { GetAuctionsDocument } from '$lib/queries/__generated__/getAuctions.generated';
import { GetAuctionTypesDocument } from '$lib/queries/__generated__/getAuctionTypes.generated';
import { GetCurrencyDocument } from '$lib/queries/__generated__/getCurrency.generated';
import { GetTimezoneDocument } from '$lib/queries/__generated__/getTimezones.generated';
import { PipelineSourceItem } from '$lib/types/types';
import type { AuctionDetailsPageServerLoadEvent } from '$routes/auctions/[id]/types';
export const auctionDetailsPageServerLoad = async ({
	parent,
	params,
}: AuctionDetailsPageServerLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();

	const auctionId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const auctionsReq =
		auctionId === PARAM_NEW
			? {
					auction: [],
				}
			: await gqlClient.request(
					GetAuctionsDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: auctionId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const auctionClientsReq =
		auctionId === PARAM_NEW
			? {
					auction_client: [],
				}
			: await gqlClient.request(
					GetAuctionClientDocument,
					{
						filter: {
							_and: [
								{ auction: { id: { _eq: auctionId } } },
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
						limit: 10000,
					},
					authHeaders
				);

	const auctionTypeReq = gqlClient.request(
		GetAuctionTypesDocument,
		{},
		authHeaders
	);

	// const currencyReq = gqlClient.request(
	// 	GetCurrencyDocument,
	// 	{
	// 		limit: -1,
	// 	},
	// 	authHeaders
	// );

	const timeZoneReq = gqlClient.request(
		GetTimezoneDocument,
		{
			limit: -1,
		},
		authHeaders
	);

	const [
		auctionsRes,
		auctionClientsRes,
		auctionTypeRes,
		// currencyRes,
		timeZoneRes,
	] = await Promise.all([
		auctionsReq,
		auctionClientsReq,
		auctionTypeReq,
		// currencyReq,
		timeZoneReq,
	]);

	const auction = auctionsRes?.auction[0] || {};
	const auctionClients = auctionClientsRes?.auction_client || [];
	const auctionTypes = auctionTypeRes?.auction_type || [];
	// const currencies = currencyRes?.currency || [];
	const timezones = timeZoneRes?.timezone || [];
	const auctionClientsIds = auctionClients?.map(
		(auctionClient) => auctionClient?.id
	);

	const auctionClientStatsPromises = auctionClientsIds?.map(
		(auctionClientsId) =>
			gqlClient.request(
				GetAuctionClientStatsDocument,
				{ id: auctionClientsId },
				authHeaders
			)
	);

	const auctionClientStatsRes = await Promise.all(auctionClientStatsPromises);

	const auctionClientStats = auctionClientsIds.reduce(
		(accumulator, auctionClientsId, index) => {
			const stats = auctionClientStatsRes[index];

			return {
				...accumulator,
				[auctionClientsId]: {
					nbLots: stats?.auction_bid_aggregated?.[0]?.countDistinct?.id,
					lotsIds: Array.from(
						new Set(
							stats?.auction_bid
								?.map((bid) => bid?.auction_lot?.lot_number)
								?.filter(Boolean)
						)
					) as string[],
					nbLotsPurchased:
						stats?.auction_lot_aggregated?.[0]?.countDistinct?.id,
				},
			};
		},
		{} as Record<
			string,
			{
				nbLots: number | null | undefined;
				lotsIds: string[];
				nbLotsPurchased: number | null | undefined;
			}
		>
	);

	const pipelineSource = await (async () => {
		if (auctionId === PARAM_NEW) {
			return '';
		}

		const res = await gqlClientCustom.request(
			GetDatasourceDocument,
			{ collection: PipelineSourceItem.Auction, id: auctionId },
			getAuthorizationHeaders(data)
		);

		return res.getDatasource?.data_source;
	})();

	return {
		...data,
		auction,
		auctionClients,
		auctionClientStats,
		auctionTypes,
		// currencies,
		timezones,
		pipelineSource,
	};
};
