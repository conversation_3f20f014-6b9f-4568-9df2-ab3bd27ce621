<script lang="ts">
	import { AuctionsSearchForm } from './AuctionsSearchForm';
	import type { AuctionsSearchFormFields } from './AuctionsSearchForm/AuctionsSearchForm.svelte';
	import { AuctionsTable } from './AuctionsTable';
	import { AuctionsSearchParam } from './constants/search';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { AuctionsSearchPageData } from '$routes/auctions/types';

	let data = $derived(getPageData<AuctionsSearchPageData>(page.data));
	let auctionTypes = $derived(data.user?.dropdowns.auctionTypes || []);
	let showResults = $derived(data.showResults);

	let auctionTypesOptions: MultiSelectOption[] = $state([]);

	$effect(() => {
		auctionTypesOptions = auctionTypes.map<MultiSelectOption>((type) => ({
			label: type?.name || '',
			value: type?.key || '',
		}));
	});

	const formatParamString = (
		values: AuctionsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			sort,
			name,
			houseName,
			types,
			startDate,
			startDateRange,
			endDate,
			endDateRange,
			saleNumber,
			isUpcomingAuction,
		} = values;

		const params: Record<AuctionsSearchParam, string> = {
			[AuctionsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[AuctionsSearchParam.ShowResults]: showResults,
			[AuctionsSearchParam.Name]: name,
			[AuctionsSearchParam.HouseName]: houseName,
			[AuctionsSearchParam.Types]: getSearchParamFromStringArray(types),
			[AuctionsSearchParam.StartDate]: startDate,
			[AuctionsSearchParam.StartDateRange]: startDate ? startDateRange : '',
			[AuctionsSearchParam.EndDate]: endDate,
			[AuctionsSearchParam.EndDateRange]: endDate ? endDateRange : '',
			[AuctionsSearchParam.SaleNumber]: saleNumber,
			[AuctionsSearchParam.IsUpcomingAuction]: isUpcomingAuction
				? isUpcomingAuction.toString()
				: '',
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<AuctionsSearchForm {auctionTypesOptions} {formatParamString} />
</div>

{#if showResults}
	<AuctionsTable />
{/if}
