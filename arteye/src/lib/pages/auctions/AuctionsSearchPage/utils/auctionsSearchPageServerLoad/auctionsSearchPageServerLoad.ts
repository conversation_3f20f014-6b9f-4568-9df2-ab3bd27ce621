// import { redirect } from '@sveltejs/kit';
// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import { gqlClient } from '$lib/gqlClient';
// import { GetAuctionTypesDocument } from '$lib/queries/__generated__/getAuctionTypes.generated';
// import type { AuctionsSearchPageServerLoadEvent } from '$routes/auctions/types';

// export const auctionsSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: AuctionsSearchPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Auction];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	const authHeaders = getAuthorizationHeaders(data);

// 	const auctionTypesReq = gqlClient.request(
// 		GetAuctionTypesDocument,
// 		{},
// 		authHeaders
// 	);

// 	const [auctionTypesRes] = await Promise.all([auctionTypesReq]);

// 	const auctionTypes = auctionTypesRes?.auction_type;

// 	return {
// 		...data,
// 		auctionTypes,
// 	};
// };
