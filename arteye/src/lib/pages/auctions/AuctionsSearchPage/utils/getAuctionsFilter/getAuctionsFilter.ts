import { AuctionsSearchParam } from '../../constants/search';
import { StringBoolean } from '$global/constants/string-boolean';
import type { Auction_Filter } from '$gql/types';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import type { SearchRange } from '$lib/constants/search-range-options';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSearchRangeFilterFullDate } from '$lib/utils/getSearchRangeFilterFullDate/getSearchRangeFilterFullDate';
import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';

export const getAuctionsFilter = (
	searchParams: URLSearchParams
): Auction_Filter => {
	const _and: Auction_Filter[] = [
		{ status: { key: { _neq: Status_Enum.Archived } } },
	];

	const name = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.Name,
	});

	const houseName = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.HouseName,
	});

	const types = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.Types,
	});

	const startDate = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.StartDate,
	});

	const startDateRange = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.StartDateRange,
	});

	const endDate = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.EndDate,
	});

	const endDateRange = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.EndDateRange,
	});

	const saleNumber = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.SaleNumber,
	});

	const isUpcomingAuction = getDecodedSearchParam({
		searchParams,
		key: AuctionsSearchParam.IsUpcomingAuction,
	});

	if (name) {
		const filter = isValidUUID(name)
			? { id: { _eq: name } }
			: { sale_name: { _icontains: name } };

		const filterItem: Auction_Filter = {
			...filter,
		};

		_and.push(filterItem);
	}

	if (houseName) {
		const filterItem: Auction_Filter = {
			auction_house: {
				organisation: {
					name: {
						_icontains: houseName,
					},
				},
			},
		};

		_and.push(filterItem);
	}

	if (types) {
		const filterItem: Auction_Filter = {
			_or: types.split(PARAM_SEPARATOR).map<Auction_Filter>((type) => {
				return {
					auction_types: {
						auction_type_key: {
							key: {
								_icontains: type,
							},
						},
					},
				};
			}),
		};

		_and.push(filterItem);
	}

	if (startDate) {
		const filterItem: Auction_Filter = {
			local_auction_start_date: getSearchRangeFilterFullDate({
				value: startDate,
				range: startDateRange as SearchRange,
			}),
		};

		_and.push(filterItem);
	}

	if (endDate) {
		const filterItem: Auction_Filter = {
			local_auction_end_date: getSearchRangeFilterFullDate({
				value: endDate,
				range: endDateRange as SearchRange,
			}),
		};

		_and.push(filterItem);
	}

	if (saleNumber) {
		const filterItem: Auction_Filter = {
			sale_number: { _icontains: saleNumber },
		};

		_and.push(filterItem);
	}

	if (isUpcomingAuction === StringBoolean.True) {
		const filterItem: Auction_Filter = {
			auction_start_date: {
				_gt: new Date().toISOString(),
			},
		};

		_and.push(filterItem);
	}

	return {
		_and,
	};
};
