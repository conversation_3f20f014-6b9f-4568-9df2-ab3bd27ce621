import { AuctionsSearchParam } from '../../constants/search';
import { AUCTIONS_DEFAULT_SORT } from '../../constants/sort';
import { getAuctionsFilter } from '../getAuctionsFilter/getAuctionsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import { gqlClient } from '$lib/gqlClient';
import type { GetAuctionsQuery } from '$lib/queries/__generated__/getAuctions.generated';
import { GetAuctionsDocument } from '$lib/queries/__generated__/getAuctions.generated';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getOffset } from '$lib/utils/getOffset/getOffset';
import { getSort } from '$lib/utils/getSort/getSort';
import type { AuctionsSearchPageLoadEvent } from '$routes/auctions/types';

export const auctionsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: AuctionsSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;

	let res: GetAuctionsQuery | null = null;
	const data = existingData || {};
	let auctions: GetAuctionsQuery['auction'] = [];
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: AuctionsSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filter = getAuctionsFilter(searchParams);
		const sort = getSort(searchParams, AUCTIONS_DEFAULT_SORT);
		const offset = getOffset(searchParams);

		res = await gqlClient.request(
			GetAuctionsDocument,
			{
				filter,
				sort,
				limit: TABLE_PAGE_SIZE,
				offset,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		auctions = res.auction || [];
		resultsCount = res.auction_aggregated?.[0].count?.id || 0;
	}

	return {
		...parentData,
		...data,
		auctions,
		resultsCount,
		showResults,
		currentPage,
	};
};
