<script module lang="ts">
	export interface AuctionsSearchFormFields {
		[AuctionsSearchParam.Sort]: string[];
		[AuctionsSearchParam.Name]: string;
		[AuctionsSearchParam.HouseName]: string;
		[AuctionsSearchParam.Types]: string[];
		[AuctionsSearchParam.StartDate]: string;
		[AuctionsSearchParam.StartDateRange]: string;
		[AuctionsSearchParam.EndDate]: string;
		[AuctionsSearchParam.EndDateRange]: string;
		[AuctionsSearchParam.SaleNumber]: string;
		[AuctionsSearchParam.IsUpcomingAuction]: boolean;
	}
</script>

<script lang="ts">
	import { AuctionsSearchParam } from '../constants/search';
	import { AUCTIONS_SORT_OPTIONS } from '../constants/sort';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { Routes } from '$lib/constants/routes';
	import {
		SEARCH_RANGE_OPTIONS,
		SEARCH_RANGE_PLACEHOLDERS,
		SearchRange,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getBooleanFromParam } from '$lib/utils/getBooleanFromParam/getBooleanFromParam';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { getValuesFromSelectOptions } from '$lib/utils/getValuesFromSelectOptions/getValuesFromSelectOptions';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';

	interface Props {
		auctionTypesOptions?: MultiSelectOption[];
		formatParamString: (
			data: AuctionsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let { auctionTypesOptions = [], formatParamString }: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			AuctionsSearchParam.Sort,
			AUCTIONS_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			AuctionsSearchParam.Sort,
			AUCTIONS_SORT_OPTIONS
		);
	});

	let name = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.Name,
		})
	);
	let houseName = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.HouseName,
		})
	);
	let types: MultiSelectOption[] = $state([]);
	$effect(() => {
		types = findMultiselectOptions(
			page.url.searchParams,
			AuctionsSearchParam.Types,
			auctionTypesOptions
		);
	});
	let startDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.StartDateRange,
		})
	);
	let startDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.StartDate,
		})
	);
	let endDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.EndDateRange,
		})
	);
	let endDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.EndDate,
		})
	);
	let saleNumber = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: AuctionsSearchParam.SaleNumber,
		})
	);

	let isUpcomingAuction = $state(
		getBooleanFromParam(
			page.url.searchParams,
			AuctionsSearchParam.IsUpcomingAuction
		)
	);

	const dataCyPrefix = 'search';

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url.pathname !== Routes.Auctions
			) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				await setArteyeSearch(
					Searches.Auction,
					`${Routes.Auctions}?${queryParams}`
				);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		name,
		houseName,
		types: getValuesFromSelectOptions(types),
		startDateRange,
		startDate,
		endDateRange,
		endDate,
		saleNumber,
		isUpcomingAuction,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		name = '';
		houseName = '';
		types = [];
		startDateRange = SearchRange.EqualTo;
		startDate = '';
		endDateRange = SearchRange.EqualTo;
		endDate = '';
		saleNumber = '';
		isUpcomingAuction = false;
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search auctions</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.Auctions}/new`}
			class="hidden lg:block"
		>
			Create New
		</LinkButton>
	</div>
	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-auction-name`}
				name="name"
				placeholder="Enter name"
				label="Auction name or ID"
				bind:value={name}
				size="sm"
			/>
		</div>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-auction-house-name`}
				name="house-name"
				placeholder="Enter auction house name"
				label="Auction house"
				bind:value={houseName}
				size="sm"
			/>
		</div>

		<MultiSelect
			name="type"
			dataCy={`${dataCyPrefix}-type`}
			label="Auction type"
			bind:selected={types}
			placeholder="Type or select"
			options={auctionTypesOptions}
			class="col-span-1"
			size="sm"
		/>

		<div
			class="col-span-5 flex flex-col lg:grid grid-cols-5 justify-center gap-4"
		>
			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-sale-number`}
					name="sale-number"
					placeholder=""
					label="Sale number"
					bind:value={saleNumber}
					size="sm"
				/>
			</div>
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Start date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-start-date`}
					name="start_date"
					bind:selectValue={startDateRange}
					bind:inputValue={startDate}
					options={SEARCH_RANGE_OPTIONS}
					placeholder={SEARCH_RANGE_PLACEHOLDERS[startDateRange]}
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="End date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-end-date`}
					name="end_dates"
					bind:selectValue={endDateRange}
					bind:inputValue={endDate}
					options={SEARCH_RANGE_OPTIONS}
					placeholder={SEARCH_RANGE_PLACEHOLDERS[endDateRange]}
				/>
			</div>
			<div class="col-span-1 flex h-full">
				<InputLabel
					dataCy={`${dataCyPrefix}-is-upcoming-upcoming`}
					variant="body3"
				>
					<Checkbox
						dataCy={`${dataCyPrefix}-is-upcoming-upcoming`}
						bind:checked={isUpcomingAuction}
					/>
					Is upcoming auction
				</InputLabel>
			</div>

			<div class="col-span-1">
				<MultiSelect
					name="sort_by"
					dataCy={`${dataCyPrefix}-sort-by`}
					label="Sort by"
					bind:selected={sort}
					placeholder="Sort by"
					options={AUCTIONS_SORT_OPTIONS}
					class="col-span-1"
					size="sm"
				/>
			</div>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
