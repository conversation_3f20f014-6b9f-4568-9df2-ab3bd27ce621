<script lang="ts">
	import dayjs from 'dayjs';
	import { AuctionsSort } from '../constants/sort';
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { TableHeaderSortArrow } from '$lib/components/TableHeaderSortArrow';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { Routes } from '$lib/constants/routes';
	import type { GetAuctionsQuery } from '$lib/queries/__generated__/getAuctions.generated';
	import { SearchParam } from '$lib/types/types';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import type { AuctionsSearchPageData } from '$routes/auctions/types';

	let loading = false;

	let data = $derived(getPageData<AuctionsSearchPageData>(page.data));

	let resultsCount = $derived(data.resultsCount);
	let auctions = $derived(data.auctions);
	let searchParams = $derived(page.url.searchParams);
	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
		sort?: {
			asc: string;
			desc: string;
		};
	}

	interface Row {
		auction: GetAuctionsQuery['auction'][0];
		fields: {
			[HeaderFieldName.Name]: string;
			[HeaderFieldName.HouseName]: string;
			[HeaderFieldName.Types]: string;
			[HeaderFieldName.SaleNumber]: string;
			[HeaderFieldName.StartDate]: string;
			[HeaderFieldName.EndDate]: string;
			[HeaderFieldName.NumberOfLots]: string | number;
			[HeaderFieldName.ViewAuction]: string;
			[HeaderFieldName.Action]: '';
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.Name,
			title: 'Auction name',
			sort: {
				asc: AuctionsSort.NameAsc,
				desc: AuctionsSort.NameDesc,
			},
		},
		{
			fieldName: HeaderFieldName.HouseName,
			title: 'Auction house',
			sort: {
				asc: AuctionsSort.HouseNameAsc,
				desc: AuctionsSort.HouseNameDesc,
			},
		},
		{
			fieldName: HeaderFieldName.Types,
			title: 'Types',
			sort: {
				asc: AuctionsSort.AuctionTypeAsc,
				desc: AuctionsSort.AuctionTypeDesc,
			},
		},
		{
			fieldName: HeaderFieldName.SaleNumber,
			title: 'Sale number',
			sort: {
				asc: AuctionsSort.SaleNumberAsc,
				desc: AuctionsSort.SaleNumberDesc,
			},
		},
		{
			fieldName: HeaderFieldName.StartDate,
			title: 'Start date',
			sort: {
				asc: AuctionsSort.AuctionStartDateAsc,
				desc: AuctionsSort.AuctionStartDateDesc,
			},
		},
		{
			fieldName: HeaderFieldName.EndDate,
			title: 'End date',
			sort: {
				asc: AuctionsSort.AuctionEndDateAsc,
				desc: AuctionsSort.AuctionEndDateDesc,
			},
		},
		{
			fieldName: HeaderFieldName.NumberOfLots,
			title: 'No. of lots',
			sort: {
				asc: AuctionsSort.NumOfLotsAsc,
				desc: AuctionsSort.NumOfLotsDesc,
			},
		},
		{
			fieldName: HeaderFieldName.ViewAuction,
			title: 'View auction',
		},
		{
			fieldName: HeaderFieldName.Action,
			title: '',
		},
	];

	let rows = $derived(
		(auctions || []).map((auction) => ({
			auction,
			fields: {
				name: auction?.sale_name || '',
				houseName: auction?.auction_house?.organisation?.name || '',
				types:
					auction?.auction_types
						?.map((type) => type?.auction_type_key?.name || '')
						.join(', ') || '',
				saleNumber: auction?.sale_number || '',
				startDate: auction?.local_auction_start_date
					? dayjs(auction?.local_auction_start_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						)
					: '-',
				endDate: auction?.local_auction_end_date
					? dayjs(auction?.local_auction_end_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						)
					: '-',
				numberOfLots: auction.aggregations?.[0]?.lot_count || '-',
				viewAuction: auction?.sale_url || '',
				action: '',
			},
		})) satisfies Row[]
	);

	const dataCyPrefix = 'table';

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		searchParams.set(SearchParam.Page, page.toString());

		const searchParamString = searchParams.toString();

		goto(`?${searchParamString}`, { invalidateAll: true });
	};

	const handleRowClick = (auctionId: string) => {
		goto(`${Routes.Auctions}/${auctionId}`);
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border border-b-0 border-gray-200 bg-white p-4"
>
	<Txt variant="h6">
		{getPaginationResultsText({
			currentPage,
			total: resultsCount,
			pageSize: TABLE_PAGE_SIZE,
		})}
	</Txt>
</div>

<div class="w-full overflow-x-auto mb-4">
	<div class="min-w-[1200px]">
		{#if auctions !== null}
			<table class=" w-full table-fixed border-collapse rounded-b-md bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader dataCy={dataCyPrefix}>
							{#snippet custom()}
								{#if header.sort}
									<TableHeaderSortArrow
										sortParamKey={SearchParam.Sort}
										asc={header.sort.asc}
										desc={header.sort.desc}
										{searchParams}
									>
										{header.title}
									</TableHeaderSortArrow>
								{:else}
									<Txt variant="label3">{header.title}</Txt>
								{/if}
							{/snippet}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#if loading}
						<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
							<CircularProgress dataCy={dataCyPrefix} />
						</TableNoResults>
					{/if}

					{#if !loading}
						{#each rows as row, i}
							<TableRow
								index={i}
								dataCy={dataCyPrefix}
								onclick={() => handleRowClick(row.auction?.id as string)}
							>
								{#each headers as header, j}
									{@const value = row.fields[header.fieldName]}

									{@const auction = row.auction}

									{#if header.fieldName === HeaderFieldName.Action}
										<TableActionCell dataCy={dataCyPrefix} class="py-0">
											<Button
												class="bg-transparent border-none px-0"
												size="sm"
												variant="tertiary"
												dataCy={`${dataCyPrefix}-cell`}
												onclick={(e) => {
													e?.stopPropagation();
													navigator.clipboard.writeText(`${auction?.id}`);
												}}
											>
												<CopyIcon />
											</Button>
											<LinkButton
												dataCy={`${dataCyPrefix}-cell`}
												variant="tertiary"
												href={`${Routes.Auctions}/${auction.id}`}
												size="sm"
												buttonProps={{ class: 'bg-transparent border-none' }}
											>
												<ChevronRightIcon class="h-[14px] w-[14px]" />
											</LinkButton>
										</TableActionCell>
									{:else}
										<TableCell
											wrap={[0, 2].includes(j)}
											dataCy={dataCyPrefix}
											content={value}
											class="py-0"
										>
											{value}
										</TableCell>
									{/if}
								{/each}
							</TableRow>
						{/each}

						{#if rows.length === 0}
							<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
								No results found
							</TableNoResults>
						{/if}
					{/if}
				</TableBody>
			</table>
		{/if}
	</div>
</div>

{#key resultsCount}
	{#key currentPage}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				{#key currentPage}
					{#key auctions?.length}
						<Pagination
							{currentPage}
							total={resultsCount}
							limit={TABLE_PAGE_SIZE}
							dataCy={dataCyPrefix}
							onClick={handlePaginationClick}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	{/key}
{/key}
