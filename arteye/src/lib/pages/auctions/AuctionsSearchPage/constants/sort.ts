import { sortByField } from '$lib/utils/sortByField/sortByField';

export enum AuctionsSort {
	DateCreatedAsc = 'date_created',
	DateCreatedDesc = '-date_created',

	DateUpdatedAsc = 'date_updated',
	DateUpdatedDesc = '-date_updated',

	NameAsc = 'sale_name',
	NameDesc = '-sale_name',

	HouseNameAsc = 'auction_house.organisation.name',
	HouseNameDesc = '-auction_house.organisation.name',

	YearFoundedAsc = 'auction_house.organisation.year_founded',
	YearFoundedDesc = '-auction_house.organisation.year_founded',

	YearDissolvedAsc = 'auction_house.organisation.year_dissolved',
	YearDissolvedDesc = '-auction_house.organisation.year_dissolved',

	AuctionTypeAsc = 'auction_types.auction_type_key.name',
	AuctionTypeDesc = '-auction_types.auction_type_key.name',

	SaleNumberAsc = 'sale_number',
	SaleNumberDesc = '-sale_number',

	AuctionStartDateAsc = 'auction_start_date',
	AuctionStartDateDesc = '-auction_start_date',

	AuctionEndDateAsc = 'auction_end_date',
	AuctionEndDateDesc = '-auction_end_date',

	NumOfLotsAsc = 'aggregations.lot_count',
	NumOfLotsDesc = '-aggregations.lot_count',
}

export const AUCTIONS_SORT_OPTIONS = sortByField(
	[
		{ label: 'Date created (Asc)', value: AuctionsSort.DateCreatedAsc },
		{ label: 'Date created (Desc)', value: AuctionsSort.DateCreatedDesc },
		{ label: 'Date updated (Asc)', value: AuctionsSort.DateUpdatedAsc },
		{ label: 'Date updated (Desc)', value: AuctionsSort.DateUpdatedDesc },
		{ label: 'Auction name (A-Z)', value: AuctionsSort.NameAsc },
		{ label: 'Auction name (Z-A)', value: AuctionsSort.NameDesc },
		{ label: 'Auction house name (A-Z)', value: AuctionsSort.HouseNameAsc },
		{ label: 'Auction house name (Z-A)', value: AuctionsSort.HouseNameDesc },
		{ label: 'Year founded (Asc)', value: AuctionsSort.YearFoundedAsc },
		{ label: 'Year founded (Desc)', value: AuctionsSort.YearFoundedDesc },
		{ label: 'Year dissolved (A-Z)', value: AuctionsSort.YearDissolvedAsc },
		{ label: 'Year dissolved (Z-A)', value: AuctionsSort.YearDissolvedDesc },
		{ label: 'Auction type (A-Z)', value: AuctionsSort.AuctionTypeAsc },
		{ label: 'Auction type (Z-A)', value: AuctionsSort.AuctionTypeDesc },
		{ label: 'Sale number (Asc)', value: AuctionsSort.SaleNumberAsc },
		{ label: 'Sale number (Desc)', value: AuctionsSort.SaleNumberDesc },
		{
			label: 'Auction start date (Asc)',
			value: AuctionsSort.AuctionStartDateAsc,
		},
		{
			label: 'Auction start date (Desc)',
			value: AuctionsSort.AuctionStartDateDesc,
		},
		{ label: 'Auction end date (Asc)', value: AuctionsSort.AuctionEndDateAsc },
		{
			label: 'Auction end date (Desc)',
			value: AuctionsSort.AuctionEndDateDesc,
		},
		{ label: 'No. of lots (Asc)', value: AuctionsSort.NumOfLotsAsc },
		{ label: 'No. of lots (Desc)', value: AuctionsSort.NumOfLotsDesc },
	],
	'label'
);

export const AUCTIONS_DEFAULT_SORT = AuctionsSort.NameAsc;
