import type { Report_Config_Report_Param } from '$gql/types';

export const sortParams = (
	selectedOptionParams:
		| (Report_Config_Report_Param | null | undefined)[]
		| null
		| undefined
) => {
	if (!selectedOptionParams) {
		return [];
	}
	const paramsArray = selectedOptionParams ?? [];
	return paramsArray.slice().sort((a, b) => {
		const aRequired = a?.report_param_id?.required ?? false;
		const bRequired = b?.report_param_id?.required ?? false;
		const aName = a?.report_param_id?.param_name ?? '';
		const bName = b?.report_param_id?.param_name ?? '';

		// Sort by `required` first
		if (aRequired !== bRequired) return aRequired ? -1 : 1;

		// Then sort alphabetically by `param_name`
		return aName.localeCompare(bName);
	});
};
