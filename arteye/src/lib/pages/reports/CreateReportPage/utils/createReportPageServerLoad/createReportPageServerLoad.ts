import { gqlClient } from '../../../../../gqlClient';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetReportConfigsDocument } from '$lib/queries/__generated__/getReportConfigs.generated';
import type { ReportsCreatePageServerLoadEvent } from '$routes/reports/create/types';

export const createReportPageServerLoad = async ({
	parent,
}: ReportsCreatePageServerLoadEvent) => {
	const data = await parent();

	const response = await gqlClient.request(
		GetReportConfigsDocument,
		{},
		getAuthorizationHeaders(data)
	);

	const reportsConfig = response?.report_config || [];

	return {
		reportsConfig,
		...data,
	};
};
