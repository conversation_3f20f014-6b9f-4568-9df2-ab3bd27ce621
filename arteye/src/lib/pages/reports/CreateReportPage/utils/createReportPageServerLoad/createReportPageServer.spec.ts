import * as svelteKitModule from '@sveltejs/kit';
import type { DocumentNode } from 'graphql';
import { createReportPageServerLoad } from './createReportPageServerLoad';
import { mockReportConfig } from '$lib/mocks/reports';
import { GetReportConfigsDocument } from '$lib/queries/__generated__/getReportConfigs.generated';

const mockRequest = vi.fn();

vi.mock('$lib/gqlClient', () => ({
	gqlClient: {
		request: (document: DocumentNode, variables: object) =>
			mockRequest(document, variables),
	},
}));

describe('Given the load function for CreateReportsPage', () => {
	let result: Awaited<ReturnType<typeof createReportPageServerLoad>>;

	beforeEach(() => {
		vi.spyOn(svelteKitModule, 'error').mockImplementation((code, message) => {
			throw new Error(`${code} ${message}`);
		});
	});

	describe('When called and the config data is valid', () => {
		beforeEach(async () => {
			vi.clearAllMocks();
			mockRequest.mockResolvedValue({
				report_config: mockReportConfig,
			});

			result = await createReportPageServerLoad({
				parent: vi.fn().mockResolvedValue({}),
			} as unknown as Parameters<typeof createReportPageServerLoad>[0]);
		});

		test('It should request the config data with the correct params', () => {
			expect(mockRequest).toHaveBeenCalledTimes(1);
			expect(mockRequest).toHaveBeenCalledWith(GetReportConfigsDocument, {});
		});
	});
});
