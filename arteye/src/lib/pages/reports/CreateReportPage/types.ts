export enum ReportParamType {
	Boolean = 'boolean',
	Date = 'date',
	Number = 'number',
	Text = 'text',
	TextArea = 'textarea',
}

export enum OutputFormat {
	Pdf = 'PDF',
	Csv = 'CSV',
}

export enum ViewType {
	List = 'list',
	Column = 'column',
}

export enum IdType {
	Artwork = 'artwork',
	Activity = 'activity',
}

export enum IdField {
	ArtworkIds = 'artwork_ids',
	ActivityIds = 'activity_ids',
}
