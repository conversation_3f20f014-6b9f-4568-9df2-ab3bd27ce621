<script lang="ts">
	import { createMutation } from '@tanstack/svelte-query';
	import {
		ReportParamType,
		OutputFormat,
		ViewType,
		IdType,
		IdField,
	} from './types';
	import { sortParams } from './utils/sortParams/sortParams';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Checkbox } from '$global/components/Checkbox';
	import { Container } from '$global/components/Container';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { LinkButton } from '$global/components/LinkButton';
	import { Radio } from '$global/components/Radio';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Report_Config_Report_Param } from '$gql/types';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import { CreateReportItemDocument } from '$lib/queries/__generated__/createReport.generated';
	import { getMutation } from '$lib/query-utils/getMutation';
	import { getQueryStrings } from '$lib/utils/getQueryStrings/getQueryStrings';
	import type { ReportsCreatePageData } from '$routes/reports/create/types';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';

	const dataCy = 'create-report-page';
	const crumbs = [
		{ label: 'Reports', href: getQueryStrings(Routes.Reports) },
		{ label: 'Create new report' },
	];

	let idsBlurred = $state(false);
	let reportsConfig = $derived(
		page.data.reportsConfig as ReportsCreatePageData['reportsConfig']
	);
	let data = $derived(getPageData<ReportsCreatePageData>(page.data));
	let loading = $state(false);

	let reportTitle = $state('');
	let reportType = $state('');

	let reportOptions = $derived(
		reportsConfig?.map((report) => ({
			label: report.type,
			value: report.type,
		}))
	);

	let selectedOptionParams = $derived(
		sortParams(
			reportsConfig?.find((config) => config?.type === reportType)
				?.params as Report_Config_Report_Param[]
		)
	);

	let form = $state(
		{} as {
			[key: string]: string | undefined;
			customQuery?: string;
			outputFormat?: string;
			artwork_ids?: string;
			activity_ids?: string;
		}
	);
	let formBooleans = $state({} as { [key: string]: boolean });

	let outputFormat = $state(OutputFormat.Pdf);
	let viewType = $state(ViewType.List);

	let idType = $state(IdType.Artwork);
	let idField = $derived(
		idType === IdType.Artwork ? IdField.ArtworkIds : IdField.ActivityIds
	);

	const LIST_VIEW_PARAM_NAME = 'list_view';

	// Parameters that have custom UI handling for the Custom report type and should be excluded from always being initialised
	const CUSTOM_HANDLED_PARAMS = [
		LIST_VIEW_PARAM_NAME,
		IdField.ArtworkIds,
		IdField.ActivityIds,
	];

	$effect(() => {
		if (selectedOptionParams) {
			formBooleans = selectedOptionParams.reduce(
				(acc, param) => {
					if (
						param?.report_param_id?.type === ReportParamType.Boolean &&
						!CUSTOM_HANDLED_PARAMS.includes(param?.report_param_id?.param_name)
					) {
						acc[param.report_param_id.param_name] = false;
					}
					return acc;
				},
				{} as { [key: string]: boolean }
			);
		}
	});

	let createReportMutation = $derived(
		createMutation(
			getMutation(CreateReportItemDocument, getAuthorizationHeaders(data))
		)
	);

	let error = $state('');

	const handleInputChange = () => {
		error = '';
	};

	const createReport = async () => {
		error = '';
		loading = true;

		const listViewParams =
			outputFormat === OutputFormat.Pdf
				? { [LIST_VIEW_PARAM_NAME]: viewType === ViewType.List }
				: {};

		const idTypeParams = { [idField]: getIds()?.join(',') };

		const dataObject = {
			title: reportTitle,
			status: 'Pending',
			params: {
				name: `${data?.user?.firstName} ${data?.user?.lastName}`,
				...formBooleans,
				...form,
				...listViewParams,
				...idTypeParams,
			},
			config: {
				id: reportsConfig.find((config) => config.type === reportType)?.id,
				type: reportType,
			},
		};

		try {
			await $createReportMutation.mutateAsync({
				data: dataObject,
			});

			goto(`${Routes.Reports}?showResults=true`);
		} catch (e) {
			loading = false;
			error = e instanceof Error ? e.message : 'An unknown error occurred';
		}
	};

	let isValid = $derived(
		(!!reportTitle &&
			!!reportType &&
			selectedOptionParams?.every((param) => {
				if (param?.report_param_id?.required) {
					if (param?.report_param_id?.type === ReportParamType.Boolean) {
						return true;
					} else {
						return !!form[param?.report_param_id?.param_name];
					}
				}
				return true;
			})) ??
			false
	);

	const getIds = () =>
		form[idField]
			?.split(',')
			?.map((val) => val.trim())
			?.filter(Boolean);
</script>

<PageBody>
	<Breadcrumbs {dataCy} {crumbs} class="pl-0" txtVariant="h5" />
	<Container
		{dataCy}
		class="flex h-full flex-grow flex-col overflow-x-auto py-10"
	>
		<div class="mx-auto mb-8 flex w-[30rem] flex-col items-center">
			<Txt variant="h3" class="mb-4">Create new report</Txt>
			<Txt variant="body2"
				>To create a new report please complete the fields below.</Txt
			>
		</div>
		<div class="mx-auto flex w-[30rem] flex-col items-start">
			<Input
				label="Report title"
				placeholder="Enter a title for your report"
				name="reportTitle"
				class="mb-4"
				classes={{ wrapper: 'flex w-full' }}
				{dataCy}
				required
				bind:value={reportTitle}
				oninput={handleInputChange}
			/>
			<Select
				{dataCy}
				name="reportType"
				label="Report type"
				ariaLabel="Report type"
				class="mb-4 flex w-full"
				placeholder="Select"
				required
				options={reportOptions}
				bind:value={reportType}
			/>
			{#if reportType === 'Custom'}
				{#each selectedOptionParams as param}
					{#if param?.report_param_id?.param_name === IdField.ArtworkIds}
						<div class="mb-4 w-full">
							<fieldset class="mb-4">
								<Txt variant="label3" class="mb-4">ID Type</Txt>
								<div class="flex flex-col gap-3 ml-4">
									<InputLabel dataCy="artwork-ids" class="w-fit">
										<Radio
											bind:group={idType}
											id="artwork-ids"
											dataCy="artwork-ids"
											name="idType"
											value={IdType.Artwork}
											size="md"
										/>Artwork Id's
									</InputLabel>

									<InputLabel dataCy="activity-ids" class="w-fit">
										<Radio
											bind:group={idType}
											id="activity-ids"
											dataCy="activity-ids"
											name="idType"
											value={IdType.Activity}
											size="md"
										/>Activity Id's
									</InputLabel>
								</div>
							</fieldset>

							<Input
								label={idType === IdType.Artwork
									? 'Artwork Ids'
									: 'Activity Ids'}
								placeholder={`Enter ${idType} IDs`}
								name={idField}
								classes={{ wrapper: 'flex w-full', helper: 'mt-1' }}
								{dataCy}
								rows={5}
								onblur={() => {
									idsBlurred = true;
								}}
								bind:value={form[idField]}
								oninput={handleInputChange}
								error={(() => {
									if (!idsBlurred) {
										return '';
									}

									const invalidIds = (getIds() || []).filter(
										(id) => !isValidUUID(id)
									);

									if (!invalidIds.length) {
										return '';
									} else if (invalidIds.length === 1) {
										return `${invalidIds[0]} is not a valid id`;
									} else {
										return `${invalidIds.join(', ')} are not valid ids`;
									}
								})()}
							/>
						</div>
					{:else if param?.report_param_id?.param_name === LIST_VIEW_PARAM_NAME}
						<div>
							<fieldset class="mb-4">
								<Txt variant="label3" class="mb-4">Output Format</Txt>
								<div class="flex flex-col gap-3 ml-4">
									<InputLabel dataCy="pdf-format" class="w-fit">
										<Radio
											bind:group={outputFormat}
											id="pdf-format"
											dataCy="pdf-format"
											name="outputFormat"
											value={OutputFormat.Pdf}
											size="md"
										/>{OutputFormat.Pdf}
									</InputLabel>

									<InputLabel dataCy="csv-format" class="w-fit">
										<Radio
											bind:group={outputFormat}
											id="csv-format"
											dataCy="csv-format"
											name="outputFormat"
											value={OutputFormat.Csv}
											size="md"
										/>{OutputFormat.Csv}
									</InputLabel>
								</div>
							</fieldset>

							{#if outputFormat === OutputFormat.Pdf}
								<fieldset class="mb-4">
									<Txt variant="label3" class="mb-4">View Type</Txt>
									<div class="flex flex-col gap-3">
										<InputLabel dataCy="list-view" class="w-fit ml-4">
											<Radio
												bind:group={viewType}
												id="list-view"
												dataCy="list-view"
												name="viewType"
												value={ViewType.List}
												size="md"
											/>List View
										</InputLabel>

										<InputLabel dataCy="column-view" class="w-fit  ml-4">
											<Radio
												bind:group={viewType}
												id="column-view"
												dataCy="column-view"
												name="viewType"
												value={ViewType.Column}
												size="md"
											/>Grid View
										</InputLabel>
									</div>
								</fieldset>
							{/if}
						</div>
					{:else if param?.report_param_id?.type === ReportParamType.Boolean && param?.report_param_id?.param_name}
						<div class="mb-4">
							<InputLabel dataCy="test" variant="label3" class="select-none">
								<Checkbox
									{dataCy}
									name={param.report_param_id.param_name}
									checked={formBooleans[param.report_param_id.param_name] ??
										false}
									onChange={(checked) => {
										if (param.report_param_id?.param_name) {
											formBooleans[param.report_param_id.param_name] =
												Boolean(checked);
										}
									}}
								/>
								{param.report_param_id.param_name
									?.replace(/_/g, ' ')
									?.replace(/\b\w/, (c) => c.toUpperCase())}
							</InputLabel>
						</div>
					{/if}
					{#if param?.report_param_id?.type === ReportParamType.TextArea}
						<Input
							label={param?.report_param_id?.label || ''}
							placeholder={`Enter ${param?.report_param_id?.label}`}
							name={param?.report_param_id?.param_name}
							class="mb-4"
							type="text"
							rows={5}
							classes={{ wrapper: 'flex w-full' }}
							{dataCy}
							required={param?.report_param_id?.required}
							bind:value={form[param?.report_param_id?.param_name]}
							oninput={handleInputChange}
						/>
					{/if}
				{/each}
			{:else if selectedOptionParams}
				{#each selectedOptionParams as param}
					{#if param?.report_param_id?.type === ReportParamType.Boolean && param?.report_param_id?.param_name}
						<div class="mb-4">
							<InputLabel dataCy="test" variant="label3" class="select-none">
								<Checkbox
									{dataCy}
									name={param.report_param_id.param_name}
									checked={formBooleans[param.report_param_id.param_name] ??
										false}
									onChange={(checked) => {
										if (param.report_param_id?.param_name) {
											formBooleans[param.report_param_id.param_name] =
												Boolean(checked);
										}
									}}
								/>
								{param.report_param_id.param_name
									?.replace(/_/g, ' ')
									?.replace(/\b\w/, (c) => c.toUpperCase())}
							</InputLabel>
						</div>
					{/if}
					{#if param?.report_param_id?.type === ReportParamType.Date}
						<Input
							label={`${param?.report_param_id?.label} (DD/MM/YYYY)`}
							placeholder="DD/MM/YYYY"
							name={param?.report_param_id?.param_name}
							class="mb-4"
							classes={{ wrapper: 'flex w-full' }}
							{dataCy}
							type="date"
							required={param?.report_param_id?.required}
							bind:value={form[param?.report_param_id?.param_name]}
							oninput={handleInputChange}
						/>
					{/if}
					{#if param?.report_param_id?.type === ReportParamType.Number}
						<Input
							label={param?.report_param_id?.label || ''}
							placeholder="0"
							name={param?.report_param_id?.param_name}
							class="mb-4"
							classes={{ wrapper: 'flex w-full' }}
							{dataCy}
							required={param?.report_param_id?.required}
							bind:value={form[param?.report_param_id?.param_name]}
							oninput={handleInputChange}
						/>
					{/if}
					{#if param?.report_param_id?.type === ReportParamType.Text}
						<Input
							label={param?.report_param_id?.label || ''}
							placeholder={`Enter ${param?.report_param_id?.label}`}
							name={param?.report_param_id?.param_name}
							class="mb-4"
							classes={{ wrapper: 'flex w-full' }}
							{dataCy}
							required={param?.report_param_id?.required}
							bind:value={form[param?.report_param_id?.param_name]}
							oninput={handleInputChange}
						/>
					{/if}
					{#if param?.report_param_id?.type === ReportParamType.TextArea}
						<Input
							label={param?.report_param_id?.label || ''}
							placeholder={`Enter ${param?.report_param_id?.label}`}
							name={param?.report_param_id?.param_name}
							class="mb-4"
							type="text"
							rows={5}
							classes={{ wrapper: 'flex w-full' }}
							{dataCy}
							required={param?.report_param_id?.required}
							bind:value={form[param?.report_param_id?.param_name]}
							oninput={handleInputChange}
						/>
					{/if}
				{/each}
			{/if}
			<div class="flex w-full">
				<LinkButton
					class="sm:mr-4 sm:w-1/3"
					{dataCy}
					variant="tertiary"
					size="md"
					fullWidth
					buttonProps={{
						class: 'bg-color-none border-none',
					}}
					href={Routes.Reports}
				>
					Back
					{#snippet leading()}
						<ChevronLeftIcon class="ml-2 h-4 w-4" />
					{/snippet}
				</LinkButton>
				<Button
					{dataCy}
					{loading}
					class="flex w-full"
					type="submit"
					variant="primary"
					size="md"
					fullWidth
					onclick={createReport}
					disabled={!isValid}
				>
					GENERATE REPORT
				</Button>
			</div>
			{#if error}
				<div class="x-auto mt-2 flex w-full justify-center border">
					<Txt variant="body2" class="text-red-500">{error}</Txt>
				</div>
			{/if}
		</div>
	</Container>
</PageBody>
