import { redirect } from '@sveltejs/kit';
import { ReportsSearchParam } from '../../constants/search';
import { getReportsFilter } from '../getReportsFilter/getReportsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import { Routes } from '$lib/constants/routes';
import { gqlClient } from '$lib/gqlClient';
import type { GetReportsQuery } from '$lib/queries/__generated__/getReports.generated';
import { GetReportsDocument } from '$lib/queries/__generated__/getReports.generated';
import type { User } from '$lib/types/types';
import { DirectusPolicyId, SearchParam } from '$lib/types/types';
import { canAccessReports } from '$lib/utils/canAccessReports/canAccessReports';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import type { ReportsSearchPageLoadEvent } from '$routes/reports/types';

const getOffset = (searchParams: URLSearchParams) => {
	const page = Number(
		getDecodedSearchParam({
			searchParams,
			key: SearchParam.Page,
		})
	);

	const offset = page ? (page - 1) * TABLE_PAGE_SIZE : 0;

	return offset;
};

export const reportsPageLoad = async ({
	data: existingData,
	parent,
	url,
}: ReportsSearchPageLoadEvent) => {
	const parentData = await parent();

	let res: GetReportsQuery | null = null;
	const data = existingData || {};

	if (!canAccessReports(data.user?.policies || [])) {
		redirect(303, Routes.Home);
	}

	let reports: GetReportsQuery['report'] = [];

	let resultsCount = 0;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;

	const showResults = getDecodedSearchParam({
		searchParams,
		key: ReportsSearchParam.ShowResults,
	});

	if (showResults === StringBoolean.True) {
		const filter = getReportsFilter(searchParams);
		const offset = getOffset(searchParams);

		res = await gqlClient.request(
			GetReportsDocument,
			{
				filter,
				limit: TABLE_PAGE_SIZE,
				offset,
				sort: '-date_created',
			},
			getAuthorizationHeaders(data)
		);
	}

	if (res) {
		reports = res.report;
		resultsCount = res.report_aggregated?.[0].count?.id || 0;
	}

	return {
		...parentData,
		...data,
		reports,
		resultsCount,
		showResults: showResults === StringBoolean.True,
		currentPage,
	};
};
