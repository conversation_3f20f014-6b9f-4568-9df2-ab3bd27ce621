import { redirect } from '@sveltejs/kit';
import { Cookies } from '$lib/constants/cookies';
import { Searches } from '$lib/constants/searches';
import type { ReportsSearchPageServerLoadEvent } from '$routes/reports/types';

export const reportsPageServerLoad = async ({
	parent,
	url,
	cookies,
}: ReportsSearchPageServerLoadEvent) => {
	const data = await parent();

	const prevArtistUrl = JSON.parse(
		(cookies.get(Cookies.Searches) as Searches) || {}
	)?.[Searches.Report];

	if (prevArtistUrl && !url.searchParams.toString().length) {
		redirect(302, prevArtistUrl);
	}

	return {
		...data,
	};
};
