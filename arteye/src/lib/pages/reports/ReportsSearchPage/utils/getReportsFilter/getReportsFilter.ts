import { ReportsSearchParam } from '../../constants/search';
import type { Report_Filter } from '$gql/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';

export const getReportsFilter = (
	searchParams: URLSearchParams
): Report_Filter => {
	const _and: Report_Filter[] = [];

	const title = getDecodedSearchParam({
		searchParams,
		key: ReportsSearchParam.Title,
	});

	if (title) {
		const filterItem: Report_Filter = {
			title: { _icontains: title },
		};

		_and.push(filterItem);
	}

	return {
		_and,
	};
};
