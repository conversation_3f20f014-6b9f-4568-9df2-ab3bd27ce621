<script lang="ts">
	import { ReportsSearchParam } from './constants/search';
	import type { ReportsSearchFormFields } from './ReportsSearchForm/ReportsSearchForm.svelte';
	import ReportsSearchForm from './ReportsSearchForm/ReportsSearchForm.svelte';
	import { ReportsTable } from './ReportsTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { ReportsSearchPageData } from '$routes/reports/types';

	let data = $derived(getPageData<ReportsSearchPageData>(page.data));
	let showResults = $derived(data.showResults);

	const formatParamString = (
		values: ReportsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const { sort, title } = values;

		const params: Record<ReportsSearchParam, string> = {
			[ReportsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[ReportsSearchParam.ShowResults]: showResults,
			[ReportsSearchParam.Title]: title,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<ReportsSearchForm {formatParamString} />
</div>

{#if showResults}
	<ReportsTable />
{/if}
