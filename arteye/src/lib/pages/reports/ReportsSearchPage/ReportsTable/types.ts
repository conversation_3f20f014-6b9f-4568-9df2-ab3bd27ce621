export enum ReportsHeaderFieldName {
	Title = 'title',
	Type = 'type',
	CreatedDate = 'createdDate',
	CreatedBy = 'createdBy',
	Status = 'status',
	Delete = 'delete',
	DownloadCsv = 'downloadCsv',
	DownloadPdf = 'downloadPdf',
}

export interface ReportsRow {
	fields: {
		[ReportsHeaderFieldName.Title]: string;
		[ReportsHeaderFieldName.Type]: string;
		[ReportsHeaderFieldName.CreatedDate]: string;
		[ReportsHeaderFieldName.CreatedBy]: string;
		[ReportsHeaderFieldName.Status]: string;
		[ReportsHeaderFieldName.Delete]: string;
		[ReportsHeaderFieldName.DownloadCsv]?: string;
		[ReportsHeaderFieldName.DownloadPdf]?: string;
	};
}
