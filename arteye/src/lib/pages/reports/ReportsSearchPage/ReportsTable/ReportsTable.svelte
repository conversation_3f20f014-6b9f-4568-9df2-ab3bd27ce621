<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import { DeleteReportDialog } from './DeleteReportDialog';
	import { ReportsHeaderFieldName } from './types';
	import { transformReports } from './utils/transformReports/transformReports';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { DownloadIcon } from '$global/assets/icons/DownloadIcon';
	import { Link } from '$global/components/Link';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { SearchParam } from '$lib/types/types';
	import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import type { ReportsSearchPageData } from '$routes/reports/types';

	interface Header {
		fieldName: ReportsHeaderFieldName;
		title: string;
	}

	let headers: Header[] = $state([
		{
			fieldName: ReportsHeaderFieldName.Title,
			title: 'Report title',
		},
		{
			fieldName: ReportsHeaderFieldName.Type,
			title: 'Report type',
		},
		{
			fieldName: ReportsHeaderFieldName.CreatedDate,
			title: 'Created date',
		},
		{
			fieldName: ReportsHeaderFieldName.CreatedBy,
			title: 'Created by',
		},
		{
			fieldName: ReportsHeaderFieldName.Status,
			title: 'Status',
		},
		{
			fieldName: ReportsHeaderFieldName.Delete,
			title: 'Delete report',
		},
		{
			fieldName: ReportsHeaderFieldName.DownloadCsv,
			title: 'Download CSV report',
		},
		{
			fieldName: ReportsHeaderFieldName.DownloadPdf,
			title: 'Download PDF report',
		},
	]);

	let data = $derived(getPageData<ReportsSearchPageData>(page.data));
	let reports = $derived(data.reports as ReportsSearchPageData['reports']);
	let searchParams = $derived(page.url.searchParams);
	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);
	let resultsCount = $derived(
		data.resultsCount as ReportsSearchPageData['resultsCount']
	);

	let transformedReports = $state(
		(() => transformReports(reports))()
	) as ReturnType<typeof transformReports>;

	$effect(() => {
		transformedReports = transformReports(reports);
	});
	let deletedReportId = $state('');

	const dialogStores = createDialog();

	const dataCyPrefix = 'artists-table';

	const handleOpenDeleteDialog = (id: string) => {
		deletedReportId = id;
		dialogStores.states.open.set(true);
	};

	const handleSuccessfulDelete = () => {
		transformedReports = transformedReports.map((report) => {
			if (report.fields.id === deletedReportId) {
				return {
					...report,
					fields: {
						...report.fields,
						status: 'Archived',
					},
				};
			}
			return report;
		});
	};

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		searchParams.set(SearchParam.Page, page.toString());

		const searchParamString = searchParams.toString();

		goto(`?${searchParamString}`, { invalidateAll: true });
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border-x border-t border-gray-200 bg-white"
>
	<Txt variant="h6" class="p-4">
		{getPaginationResultsText({
			currentPage,
			total: resultsCount,
			pageSize: TABLE_PAGE_SIZE,
		})}
	</Txt>
</div>

<div class="w-full overflow-x-auto mb-4">
	<div class="min-w-[1200px]">
		<table class=" w-full table-fixed rounded-md bg-white">
			<TableHeaderRow dataCy={dataCyPrefix}>
				{#each headers as header, i}
					<TableHeader dataCy={dataCyPrefix}>
						{header.title}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody dataCy={dataCyPrefix}>
				{#if reports?.length === 0}
					<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
						<Txt variant="body2">No reports found.</Txt>
					</TableNoResults>
				{:else}
					{#each transformedReports as row, i}
						<TableRow index={i} dataCy={dataCyPrefix}>
							{#each headers as header}
								{@const value = row.fields[header.fieldName]}

								{#if header.fieldName === ReportsHeaderFieldName.DownloadCsv}
									<TableActionCell
										dataCy={`{dataCyPrefix}-${header.fieldName}-cell`}
										class="h-[32px] py-0"
									>
										<span class="flex w-full items-start justify-start">
											{#if row.fields.status !== 'Archived'}
												<Link
													dataCy={`${dataCyPrefix}-download-pdf`}
													target="_blank"
													rel="noopener noreferrer"
													href={getImageUrl(
														row.fields.downloadCsv,
														data.user?.access_token
													)}
													class="flex items-center"
												>
													<Txt
														variant="body2"
														class={classNames('mr-1 text-blue-500', {
															'text-gray-300': !row.fields.downloadCsv,
														})}>CSV</Txt
													>
													<DownloadIcon
														class="h-4 w-4"
														color={row.fields.downloadCsv
															? 'blue-500'
															: 'gray-300'}
													/>
												</Link>
											{:else}
												<Txt class="text-gray-500">-</Txt>
											{/if}
										</span>
									</TableActionCell>
								{:else if header.fieldName === ReportsHeaderFieldName.DownloadPdf}
									<TableActionCell
										dataCy={`{dataCyPrefix}-${header.fieldName}-cell`}
										class="h-[32px] py-0"
									>
										<span class="flex w-full items-start justify-start">
											{#if row.fields.status !== 'Archived'}
												<Link
													dataCy={`${dataCyPrefix}-download-pdf`}
													target="_blank"
													rel="noopener noreferrer"
													href={getImageUrl(
														row.fields.downloadPdf,
														data.user?.access_token
													)}
													class="flex items-center"
												>
													<Txt
														variant="body2"
														class={classNames('mr-1 text-blue-500', {
															'text-gray-300': !row.fields.downloadPdf,
														})}>PDF</Txt
													>
													<DownloadIcon
														class="h-4 w-4"
														color={row.fields.downloadPdf
															? 'blue-500'
															: 'gray-300'}
													/>
												</Link>
											{:else}
												<Txt class="text-gray-500">-</Txt>
											{/if}
										</span>
									</TableActionCell>
								{:else if header.fieldName === ReportsHeaderFieldName.Delete}
									<TableCell
										dataCy={`{dataCyPrefix}-${header.fieldName}-cell`}
										class="h-[32px] py-0"
									>
										{#snippet custom()}
											{#if row.fields.status !== 'Archived'}
												<button
													data-cy={`${dataCyPrefix}-delete-report`}
													class="flex items-center"
													onclick={() =>
														handleOpenDeleteDialog(row.fields.id || '')}
												>
													<Txt variant="body2" class="mr-1 text-red-500"
														>Delete</Txt
													>
													<BinIcon color="red-500" class="h-4 w-4" />
												</button>
											{:else}
												<Txt class="text-gray-500">-</Txt>
											{/if}
										{/snippet}
									</TableCell>
								{:else}
									<TableCell
										dataCy={`{dataCyPrefix}-${header.fieldName}-cell`}
										content={value}
										class="h-[32px] py-0"
										classes={{
											text: classNames('', {
												'text-red-500':
													header.fieldName === ReportsHeaderFieldName.Status &&
													value === 'Failed',
												'text-gray-400': row.fields.status === 'Archived',
											}),
										}}
									>
										{value}
									</TableCell>
								{/if}
							{/each}
						</TableRow>
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</div>

<DeleteReportDialog
	dataCy={dataCyPrefix}
	id={deletedReportId}
	{dialogStores}
	onSuccessfulDelete={handleSuccessfulDelete}
/>

{#key resultsCount}
	{#key currentPage}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				{#key currentPage}
					{#key reports?.length}
						<Pagination
							{currentPage}
							total={resultsCount}
							limit={TABLE_PAGE_SIZE}
							dataCy={dataCyPrefix}
							onClick={handlePaginationClick}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	{/key}
{/key}
