import dayjs from 'dayjs';
import type { GetReportsQuery } from '$lib/queries/__generated__/getReports.generated';

export const transformReports = (reports: GetReportsQuery['report']) => {
	return reports?.map((report) => ({
		item: {},
		fields: {
			id: report?.id,
			title: report?.title,
			type: report?.config?.type,
			createdDate: dayjs(report?.date_created).format('DD/MM/YYYY'),
			createdBy: report?.user_created?.first_name,
			status: report?.status,
			delete: `${!!report?.pdf_file?.filename_disk}`,
			downloadCsv: report?.csv_file?.filename_disk,
			downloadPdf: report?.pdf_file?.filename_disk,
		},
	}));
};
