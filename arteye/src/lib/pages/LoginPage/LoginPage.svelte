<script lang="ts">
	import { page } from '$app/state';
	import { LoginPage } from '$global/features/auth/components/LoginPage';
	import { PageBody } from '$lib/components/PageBody';
	import { LoginVariant } from '$lib/constants/login';
</script>

<LoginPage
	class="fixed top-0 h-[100vh] w-full bg-gray-50"
	{PageBody}
	variant={LoginVariant}
	returnUrl={(() => {
		const returnUrlParam = page.url.searchParams.get('returnUrl');

		if (!returnUrlParam) {
			return '/';
		}

		const returnUrl = new URL(returnUrlParam);
		const returnUrlSearchParams = returnUrl.searchParams;

		if (
			returnUrlSearchParams.size === 1 &&
			returnUrlSearchParams.get('showResults') === 'true'
		) {
			return returnUrl.pathname;
		}

		return returnUrlParam;
	})()}
/>
