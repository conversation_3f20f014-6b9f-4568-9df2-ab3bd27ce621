import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../../../gql/types-system';

export type LogoutMutationVariables = Types.Exact<{
	refreshToken?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type LogoutMutation = {
	__typename?: 'Mutation';
	auth_logout?: boolean | null;
};

export const LogoutDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'logout' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'refreshToken' },
					},
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } },
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auth_logout' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'refresh_token' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'refreshToken' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<LogoutMutation, LogoutMutationVariables>;
