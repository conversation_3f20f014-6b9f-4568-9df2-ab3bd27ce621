<script lang="ts">
	import type {
		ImageAndDescriptionForm,
		SeriesImage,
	} from '../../../components/details-pages/ImageDescriptionAndTags';
	import { ImageDescriptionAndTags } from '../../../components/details-pages/ImageDescriptionAndTags';
	import type { SeriesInfoForm } from './SeriesInfoCard/SeriesInfoCard.svelte';
	import SeriesInfoCard from './SeriesInfoCard/SeriesInfoCard.svelte';
	import { SubSeriesCard } from './SubSeriesCard';
	import { archiveSeries } from './utils/archiveSeries/archiveSeries';
	import { createSeries } from './utils/createSeries/createSeries';
	import { formatImageAndDescriptionForm } from './utils/formatters/formatImageAndDescriptionForm/formatImageAndDescriptionForm';
	import { formatSeriesImage } from './utils/formatters/formatSeriesImage/formatSeriesImage';
	import { formatSeriesInfoForm } from './utils/formatters/formatSeriesInfoForm/formatSeriesInfoForm';
	import { mutateSeries } from './utils/mutateSeries/mutateSeries';
	import { mutateSeriesImage } from './utils/mutateSeriesImage/mutateSeriesImage';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { Link } from '$global/components/Link';
	import { showToast } from '$global/components/Toasts';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { Routes } from '$lib/constants/routes';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import {
		ArtActBasicSearchParam,
		ArtworkAdvancedSearchParam,
	} from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/constants/search';
	import { SearchParam } from '$lib/types/types';
	import type { SeriesDetailsPageData } from '$routes/series/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	let loading = $state(false);
	let showSaveBar = $state(false);
	let deleting = $state(false);

	let data = $derived(getPageData<SeriesDetailsPageData>(page.data));
	let series = $derived(data.series);
	let seriesId = $state((() => series?.id || '')()) as string;
	$effect(() => {
		seriesId = series?.id || '';
	});
	let headers = $derived(getAuthorizationHeaders(data));

	let image = $state(
		(() => formatSeriesImage({ series, headers }))()
	) as ReturnType<typeof formatSeriesImage>;
	$effect(() => {
		image = formatSeriesImage({ series, headers });
	});

	let seriesInfoForm = $state(
		(() => formatSeriesInfoForm(series))()
	) as ReturnType<typeof formatSeriesInfoForm>;
	$effect(() => {
		seriesInfoForm = formatSeriesInfoForm(series);
	});
	let imageAndDescriptionForm = $state(
		(() => formatImageAndDescriptionForm(series))()
	) as ReturnType<typeof formatImageAndDescriptionForm>;
	$effect(() => {
		imageAndDescriptionForm = formatImageAndDescriptionForm(series);
	});

	let crumbs = $derived([
		{ label: 'Artworks', href: userRoutes.routes.basic_search },
		{
			label: 'Series',
		},
	]);

	let tagsUpdateHistory = $derived({
		date_created: series?.date_created,
		user_created: series?.user_created,
		date_updated: series?.tags_last_updated,
		user_updated: series?.tags_last_updated_by,
	});

	const dataCy = 'series-details';

	const handleImageChange = (newImage: SeriesImage | null) => {
		image = newImage;
		showSaveBar = true;
	};

	const handleImageAndDescriptionFormChange = (
		form: ImageAndDescriptionForm
	) => {
		imageAndDescriptionForm = form;
		showSaveBar = true;
	};

	const handleClickDeleteSeries = async () => {
		try {
			deleting = true;

			await archiveSeries({ seriesId, headers });

			showToast({
				variant: 'success',
				message: 'Series successfully deleted.',
			});

			goto(`${Routes.Series}/new`);
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong. Please try again or contact the support team.',
			});
		} finally {
			deleting = false;
		}
	};

	const handleSaveClick = async () => {
		let isNewSeries = false;

		if (!seriesInfoForm.seriesTitle) {
			showToast({
				variant: 'warning',
				message: `A series title is required.`,
			});

			return;
		}

		const artistsToUpdate = Object.values(seriesInfoForm.artists).filter(
			(artist) => !artist.isDeleted
		).length;

		if (!artistsToUpdate) {
			showToast({
				variant: 'warning',
				message: `An artist is required.`,
			});

			return;
		}

		try {
			loading = true;

			if (!seriesId) {
				isNewSeries = true;
				seriesId = await createSeries({ headers });
			}

			await mutateSeries({
				series,
				seriesInfoForm,
				headers,
				seriesId,
				imageAndDescriptionForm,
			});

			await mutateSeriesImage({
				headers,
				seriesId,
				image,
			});

			const saveType = isNewSeries ? 'created' : 'updated';

			showToast({
				variant: 'success',
				message: `Series ${saveType} successfully.`,
			});

			showSaveBar = false;

			if (isNewSeries) {
				goto(`${Routes.Series}/${seriesId}`);
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong. Please try again or contact the support team.',
			});
		} finally {
			loading = false;
		}
	};

	const handleSeriesInfoChange = (form: SeriesInfoForm) => {
		seriesInfoForm = form;
		showSaveBar = true;
	};
</script>

<div>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />

		{#if seriesId}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<Button
					onclick={handleClickDeleteSeries}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>

				<Link
					href={`${Routes.ArtworksAndActivities}?${SearchParam.ShowResults}=${StringBoolean.True}&${ArtworkAdvancedSearchParam.SeriesTitle}=${seriesId}&searchType=advanced&${ArtActBasicSearchParam.ResultsTab}=${ArtworkAndActivitiesResultsTab.Artworks}`}
				>
					<Button
						dataCy={`${dataCy}-view-all-artworks`}
						variant="secondary"
						size="sm"
					>
						View all artworks
					</Button>
				</Link>
			</div>
		{/if}
	</div>

	{#if series}
		<CreateUpdate updateHistory={series} class="block mb-5 mt-[-12px]" />
	{/if}

	<div class="flex flex-col-reverse lg:grid grid-cols-3 gap-4">
		<div class="col-span-1">
			<ImageDescriptionAndTags
				{image}
				{imageAndDescriptionForm}
				onChange={handleImageAndDescriptionFormChange}
				onImageChange={handleImageChange}
				{tagsUpdateHistory}
			/>
		</div>
		<div class="col-span-2 flex flex-col lg:grid gap-4">
			<SeriesInfoCard
				{seriesInfoForm}
				onChange={handleSeriesInfoChange}
				{series}
			/>
			<SubSeriesCard subSeries={series?.sub_series} />
		</div>
	</div>
</div>

<PageSaveBar
	{loading}
	disabled={deleting || loading}
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
/>
