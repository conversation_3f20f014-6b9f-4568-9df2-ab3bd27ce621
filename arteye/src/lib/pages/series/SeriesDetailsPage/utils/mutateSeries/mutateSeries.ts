import type { ImageAndDescriptionForm } from '../../../../../components/details-pages/ImageDescriptionAndTags';
import type { SeriesInfoForm } from '../../SeriesInfoCard';
import { gqlClient } from '$lib/gqlClient';
import type { UpdateArtworkSeriesMutationVariables } from '$lib/mutations/__generated__/updateArtworkSeries.generated';
import { UpdateArtworkSeriesDocument } from '$lib/mutations/__generated__/updateArtworkSeries.generated';
import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

export const mutateSeries = ({
	series,
	seriesId,
	seriesInfoForm,
	imageAndDescriptionForm,
	headers,
}: {
	series: SeriesDetailsPageData['series'];
	seriesId: string;
	seriesInfoForm: SeriesInfoForm;
	imageAndDescriptionForm: ImageAndDescriptionForm;
	headers: {
		Authorization: string;
	};
}) => {
	const data: UpdateArtworkSeriesMutationVariables['data'] = {};

	const tagRelationIdMap = (series?.tags || []).reduce<Record<string, string>>(
		(acc, tag) => {
			acc[tag?.tag_tag?.tag || ''] = tag?.id || '';
			return acc;
		},
		{}
	);

	const categoryTags: UpdateArtworkSeriesMutationVariables['data']['tags'] =
		imageAndDescriptionForm.categoryTags.map((tag) => ({
			id: tagRelationIdMap[tag.value] || '',
			tag_tag: {
				tag: tag.value,
			},
		}));

	const subjectTags: UpdateArtworkSeriesMutationVariables['data']['tags'] =
		imageAndDescriptionForm.subjectTags.map((tag) => ({
			id: tagRelationIdMap[tag.value] || '',
			tag_tag: {
				tag: tag.value,
			},
		}));

	const tags = [...categoryTags, ...subjectTags];

	if (
		seriesInfoForm.parentSeries.isDeleted ||
		!seriesInfoForm.parentSeries.id
	) {
		data.parent_series = null;
	} else {
		data.parent_series = {
			id: seriesInfoForm.parentSeries.id,
		};
	}

	data.artists = Object.values(seriesInfoForm.artists)
		.filter((artist) => !artist.isDeleted)
		.map((artist) => ({
			artist_id: {
				id: artist.id,
			},
		}));

	data.title = seriesInfoForm.seriesTitle;
	data.crid = seriesInfoForm.crid;
	data.execution_start_year = parseInt(seriesInfoForm.executedStartYear);
	data.execution_end_year = parseInt(seriesInfoForm.executedEndYear);
	data.number_of_artworks = parseInt(seriesInfoForm.numberOfArtworks);
	data.is_heni_series = seriesInfoForm.isHeniSeries;
	data.description = imageAndDescriptionForm.description;

	data.tags = tags;

	return gqlClient.request(
		UpdateArtworkSeriesDocument,
		{
			id: seriesId,
			data,
		},
		headers
	);
};
