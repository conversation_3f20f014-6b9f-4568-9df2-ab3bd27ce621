import type { AuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { gqlClient } from '$lib/gqlClient';
import { CreateArtworkSeriesDocument } from '$lib/mutations/__generated__/createArtworkSeries.generated';

export const createSeries = async ({
	headers,
}: {
	headers: AuthorizationHeaders;
}) => {
	const res = await gqlClient.request(
		CreateArtworkSeriesDocument,
		{
			data: {
				title: '',
			},
		},
		headers
	);

	return res.create_artwork_series_item?.id || '';
};
