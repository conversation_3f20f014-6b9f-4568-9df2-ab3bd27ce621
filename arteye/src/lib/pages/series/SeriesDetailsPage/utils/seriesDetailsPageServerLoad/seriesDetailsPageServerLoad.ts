import { error } from '@sveltejs/kit';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
import { gqlClient } from '$lib/gqlClient';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { GetArtworkSeriesByIdDocument } from '$lib/queries/__generated__/getArtworkSeriesById.generated';
import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
import type { SeriesDetailsPageServerLoadEvent } from '$routes/series/[id]/types';

export const seriesDetailsPageServerLoad = async ({
	parent,
	params,
}: SeriesDetailsPageServerLoadEvent) => {
	const data = await parent();
	const authHeaders = getAuthorizationHeaders(data);
	const seriesId = params.id;

	const series = await (async () => {
		if (!seriesId) {
			return null;
		}

		const res = await gqlClient.request(
			GetArtworkSeriesByIdDocument,
			{
				id: seriesId,
			},
			authHeaders
		);

		return res.artwork_series_by_id;
	})();

	if (seriesId && !series) {
		error(404, 'Series not found');
	}

	// if (series?.artists && !isOnDev()) {
	// 	const legacyIdRes = await Promise.all(
	// 		series?.artists?.map((artist) =>
	// 			gqlClientCustom.request(
	// 				GetLegacyIdDocument,
	// 				{ id: artist?.artist_id?.id, collection: 'artist' },
	// 				getAuthorizationHeaders(data)
	// 			)
	// 		)
	// 	);

	// 	series.artists = series.artists.map((artist, i) => ({
	// 		...artist,
	// 		artist_id: {
	// 			...artist?.artist_id,
	// 			legacy_id: legacyIdRes[i]?.getLegacyId?.legacyId,
	// 		},
	// 	})) as typeof series.artists;
	// }

	return {
		...data,
		series,
	};
};
