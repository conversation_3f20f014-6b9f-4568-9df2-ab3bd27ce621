import type { SeriesImage } from '$lib/components/details-pages/ImageDescriptionAndTags';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkSeriesDocument } from '$lib/mutations/__generated__/updateArtworkSeries.generated';
import { uploadFile } from '$lib/utils/uploadFile/uploadFile';

export const mutateSeriesImage = async ({
	seriesId,
	image,
	headers,
}: {
	seriesId: string;
	image: SeriesImage | null;
	headers: {
		Authorization: string;
	};
}) => {
	const profileImagePayload = await (async () => {
		if (image) {
			const { url, file, ...restProfileImage } = image;

			if (!file) {
				return restProfileImage;
			}

			const uploadFileResponse = await uploadFile(file, headers);

			return uploadFileResponse;
		}

		return null;
	})();

	const res = await gqlClient.request(
		UpdateArtworkSeriesDocument,
		{
			id: seriesId,
			data: {
				image: profileImagePayload,
			},
		},
		headers
	);

	return res;
};
