import { Status_Enum } from '$gql/types-custom';
import { gqlClient } from '$lib/gqlClient';
import { UpdateArtworkSeriesDocument } from '$lib/mutations/__generated__/updateArtworkSeries.generated';
export const archiveSeries = async ({
	seriesId,
	headers,
}: {
	seriesId: string;
	headers: {
		Authorization: string;
	};
}) => {
	return gqlClient.request(
		UpdateArtworkSeriesDocument,
		{
			id: seriesId,
			data: {
				status: {
					key: Status_Enum.Archived,
					name: 'Archived',
				},
			},
		},
		headers
	);
};
