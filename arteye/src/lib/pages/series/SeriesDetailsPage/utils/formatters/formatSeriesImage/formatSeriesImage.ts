import type { SeriesImage } from '../../../../../../components/details-pages/ImageDescriptionAndTags';
import type { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { getImageUrl } from '$lib/utils/getImageUrl/getImageUrl';
import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

export const formatSeriesImage = ({
	series,
	headers,
}: {
	series: SeriesDetailsPageData['series'];
	headers: ReturnType<typeof getAuthorizationHeaders>;
}): SeriesImage | null => {
	if (!series?.image) return null;

	return {
		...series.image,
		url: getImageUrl(series?.image?.id, headers.Authorization) || '',
	};
};
