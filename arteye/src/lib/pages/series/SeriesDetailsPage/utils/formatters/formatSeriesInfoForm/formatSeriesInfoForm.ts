import type { SeriesInfoForm } from '../../../SeriesInfoCard/SeriesInfoCard.svelte';
import { Routes } from '$lib/constants/routes';
import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

export const formatSeriesInfoForm = (
	series: SeriesDetailsPageData['series']
): SeriesInfoForm => {
	const artistsList = (series?.artists || []).map((artist) => {
		const getSubtitle = () => {
			const nationality =
				artist?.artist_id?.person?.nationalities?.[0]?.country
					?.country_nationality;
			const yearBirthDeath =
				artist?.artist_id?.person?.year_birth &&
				artist?.artist_id?.person?.year_death
					? getYearBirthDeathString({
							yearBirth: artist?.artist_id?.person?.year_birth,
							yearDeath: artist?.artist_id?.person?.year_death,
							withWrapper: false,
						})
					: '';
			const artworkCount =
				artist?.artist_id?.aggregations?.[0]?.artwork_count || 0;

			if (nationality && yearBirthDeath) {
				return `(${nationality}, ${yearBirthDeath}) ${artworkCount} artworks`;
			}

			if (nationality) {
				return `(${nationality}) ${artworkCount} artworks`;
			}

			if (yearBirthDeath) {
				return `(${yearBirthDeath}) ${artworkCount} artworks`;
			}

			return '';
		};

		return {
			id: artist?.artist_id?.id || '',
			title: artist?.artist_id?.person?.entity?.name || '',
			subTitle: getSubtitle(),
			url: `${Routes.Artists}/${artist?.artist_id?.id}`,
			...(artist?.artist_id?.reference_id && {
				refId: `Ref ID: ${artist?.artist_id?.reference_id}`,
			}),
			// ...((artist?.artist_id as unknown as { legacy_id: string })
			// 	?.legacy_id && {
			// 	legacyId: `Legacy ID: ${(artist?.artist_id as unknown as { legacy_id: string })?.legacy_id}`,
			// }),
		};
	});

	const artistsMap = artistsList.reduce<SeriesInfoForm['artists']>(
		(acc, artist) => {
			acc[artist.id] = artist;
			return acc;
		},
		{}
	);

	const getParentSeriesSubtitle = () => {
		const artist = series?.parent_series?.artists?.[0]?.artist_id;
		const name = artist?.person?.entity?.name;
		const nationality =
			artist?.person?.nationalities?.[0]?.country?.country_nationality;
		const yearBirthDeath = getYearBirthDeathString({
			yearBirth: artist?.person?.year_birth,
			yearDeath: artist?.person?.year_death,
			withWrapper: false,
		});

		const string = [name, nationality, yearBirthDeath]
			.map((item) => (item ? item : ''))
			.join(', ');

		return string;
	};

	return {
		artists: artistsMap,
		seriesTitle: series?.title || '',
		crid: series?.crid || '',
		executedStartYear: `${series?.execution_start_year || ''}`,
		executedEndYear: `${series?.execution_end_year || ''}`,
		numberOfArtworks: `${series?.number_of_artworks || ''}`,
		parentSeries: {
			title: series?.parent_series?.title || '',
			subTitle: getParentSeriesSubtitle(),
			id: series?.parent_series?.id || '',
			url: `${Routes.Series}/${series?.parent_series?.id}`,
		},
		isHeniSeries: !!series?.is_heni_series,
	};
};
