import type { ImageAndDescriptionForm } from '../../../../../../components/details-pages/ImageDescriptionAndTags';
import { Tag_Type_Enum } from '$gql/types-custom';
import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

export const formatImageAndDescriptionForm = (
	series: SeriesDetailsPageData['series']
): ImageAndDescriptionForm => {
	return {
		description: series?.description || '',
		categoryTags: (series?.tags || [])
			.filter(
				(category) => category?.tag_tag?.type?.key === Tag_Type_Enum.Category
			)
			.map((category) => ({
				value: category?.tag_tag?.tag || '',
				label: category?.tag_tag?.tag || '',
			})),
		subjectTags: (series?.tags || [])
			.filter(
				(category) => category?.tag_tag?.type?.key === Tag_Type_Enum.Subject
			)
			.map((category) => ({
				value: category?.tag_tag?.tag || '',
				label: category?.tag_tag?.tag || '',
			})),
	};
};
