<script lang="ts">
	import { Link } from '$global/components/Link';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

	interface Props {
		subSeries?: NonNullable<SeriesDetailsPageData['series']>['sub_series'];
	}

	let { subSeries = [] }: Props = $props();

	const dataCy = 'sub-series';
</script>

{#if !!subSeries?.length}
	<div class="rounded-md border bg-white p-4">
		<div class="mb-6 flex flex-col lg:flex-row lg:items-center gap-3">
			<Txt variant="h5">Sub-series</Txt>
			<Txt variant="body3" class="mt-[4px] text-gray-500">
				New sub-series are automatically listed below when you designate this
				series as their parent.
			</Txt>
		</div>

		<div class="mb-4 flex flex-col gap-4">
			{#each subSeries as series}
				{@const artist = series?.artists?.[0]?.artist_id}

				<div
					class="flex flex-col lg:flex-row lg:items-center gap-2 rounded-md border bg-gray-100 p-3"
				>
					<Link
						data-cy={dataCy}
						href={`${Routes.Series}/${series?.id}`}
						rel="noopener noreferrer"
						target="_blank"
					>
						<Txt class="text-blue-600" variant="body2">{series?.title}</Txt>
					</Link>

					{#if artist}
						<Txt class=" text-gray-500" variant="body2">
							({artist?.person?.entity?.name}, {artist?.person
								?.nationalities?.[0]?.country?.name}, {getYearBirthDeathString({
								yearBirth: artist?.person?.year_birth || 0,
								yearDeath: artist?.person?.year_death || 0,
								withWrapper: false,
							})})
						</Txt>
					{/if}
				</div>
			{/each}
		</div>
	</div>
{/if}
