<script lang="ts" module>
	export const formatArtist = (
		artist: GetArtistsQuery['artist'][number] & {
			legacy_id?: string | null | undefined;
		}
	) => {
		const url = `${Routes.Artists}/${artist?.id}`;

		const getSubtitle = () => {
			const nationality =
				artist.person?.nationalities?.[0]?.country?.country_nationality;
			const yearBirthDeath =
				artist.person?.year_birth && artist.person?.year_death
					? getYearBirthDeathString({
							yearBirth: artist.person?.year_birth,
							yearDeath: artist.person?.year_death,
							withWrapper: false,
						})
					: '';

			const artworkCount = artist.aggregations?.[0]?.artwork_count || 0;

			if (nationality && yearBirthDeath) {
				return `(${nationality}, ${yearBirthDeath}) ${artworkCount} artworks`;
			}

			if (nationality) {
				return `(${nationality}) ${artworkCount} artworks`;
			}

			if (yearBirthDeath) {
				return `(${yearBirthDeath}) ${artworkCount} artworks`;
			}

			return '';
		};

		return {
			line1: `${artist.person?.entity?.name}`,
			line2: getSubtitle(),
			line3: url,
			line4: `${artist.id}`,
			...(artist.reference_id && {
				line5: `Ref ID: ${artist.reference_id}`,
			}),
			// ...(artist.legacy_id && {
			// 	line6: `Legacy ID: ${artist.legacy_id}`,
			// }),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Link } from '$global/components/Link';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Status_Enum } from '$gql/types-custom';
	import { AutocompleteOption } from '$lib/components/AutocompleteOption';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { CreateNewOption } from '$lib/components/CreateNewOption';
	import { Routes } from '$lib/constants/routes';
	import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { ArtActBasicSearchParam } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/constants/search';
	import type {
		GetArtistsQueryVariables,
		GetArtistsQuery,
	} from '$lib/queries/__generated__/getArtists.generated';
	import { GetArtistsDocument } from '$lib/queries/__generated__/getArtists.generated';
	import { SearchParam } from '$lib/types/types';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';
	import { isValidUUID } from '$lib/utils/isValidUUID/isValidUUID';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { dataCy, selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetArtistsQueryVariables => {
		if (!value) {
			return {
				limit: 5,
			};
		}

		return {
			limit: 5,
			filter: {
				_and: [
					{ status: { key: { _neq: Status_Enum.Archived } } },
					...(() => {
						if (!value) {
							return [];
						}

						if (isValidUUID(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [
							{
								person: {
									entity: {
										name: {
											_icontains: value,
										},
									},
								},
							},
						];
					})(),
				],
			},
		};
	};

	const getOptions = async (value: string) => {
		const res = await gqlClient.request(
			GetArtistsDocument,
			getVariables(value),
			getAuthorizationHeaders(data)
		);

		return [...(res?.artist || []).map(formatArtist)].sort(
			(a, b) => a.line1.length - b.line1.length
		);

		// if (isOnDev()) {
		// 	return [...(res?.artist || []).map(formatArtist)];
		// }

		// const legacyIdRes = await Promise.all(
		// 	res?.artist?.map((artist) =>
		// 		gqlClientCustom.request(
		// 			GetLegacyIdDocument,
		// 			{ id: artist?.id, collection: 'artist' },
		// 			getAuthorizationHeaders(data)
		// 		)
		// 	)
		// );

		// return [
		// 	...(res?.artist || []).map((artist, i) =>
		// 		formatArtist({
		// 			...artist,
		// 			legacy_id: legacyIdRes[i].getLegacyId?.legacyId,
		// 		})
		// 	),
		// ];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={selectedOption.line2 || ''}
			url={selectedOption.line3 || ''}
			refId={selectedOption.line5}
			legacyId={selectedOption.line6}
			onRemove={handleRemove}
			class="h-auto py-2"
		/>
	{:else}
		<div class="relative">
			<PromiseAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="artists"
				dataCy={`${dataCy}-artists`}
				placeholder="Start typing to search"
				showResultsWhenEmpty={false}
				classes={{
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
					noResults: 'p-0',
				}}
				{getOptions}
				{value}
				{selectedOption}
				showCustomList
				onChange={handleChange}
				debounceTimeout={300}
			>
				{#snippet list({ options }: { options: OptionType[] })}
					<div>
						{#each options as option, index}
							{@const title = option.line1 || ''}
							{@const subTitle = option.line2 || ''}
							{@const url = option.line3 || ''}
							{@const id = option.line4}

							{#if option.line1}
								<AutocompleteOption
									{dataCy}
									{title}
									{subTitle}
									{url}
									refId={option.line5}
									legacyId={option.line6}
									onClick={() => {
										$value = `${option.line1}`;
										onChange(option);
									}}
									class={classNames('h-auto py-2', {
										'border-t border-gray-200': !!index,
									})}
								>
									{#snippet right()}
										<Link
											href={`/artworks-and-activities?${SearchParam.ShowResults}=${StringBoolean.True}&${ArtActBasicSearchParam.Name}=${id}&${ArtActBasicSearchParam.ResultsTab}=${ArtworkAndActivitiesResultsTab.Artworks}`}
											target="_blank"
											rel="noopener noreferrer"
											data-cy={`${dataCy}-artist`}
											class="mr-1 flex items-center gap-1"
										>
											<ExternalIcon class="h-[18px] w-[18px]" />
											<Txt variant="label4" class="text-">ARTWORKS</Txt>
										</Link>
									{/snippet}
								</AutocompleteOption>
							{/if}
						{/each}

						<CreateNewOption
							{dataCy}
							url={`${Routes.Artists}/new`}
							startText="Cannot find the artist?"
							ctaText="Create new artist"
							endText="then come back to this artwork and link the artist here."
						/>
					</div>
				{/snippet}

				{#snippet noResults()}
					<div>
						<NoResults class="py-2 text-left" dataCy={`${dataCy}-artist`}>
							No results found.
						</NoResults>

						<CreateNewOption
							{dataCy}
							url={`${Routes.Artists}/new`}
							startText="Cannot find the artist?"
							ctaText="Create new artist"
							endText="then come back to this artwork and link the artist here."
						/>
					</div>
				{/snippet}
			</PromiseAutocomplete>
		</div>
	{/if}
</div>
