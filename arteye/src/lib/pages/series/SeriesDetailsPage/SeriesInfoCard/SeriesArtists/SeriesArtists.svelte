<script lang="ts">
	import type { SeriesInfoForm } from '../SeriesInfoCard.svelte';
	import { ArtistAutocomplete } from './ArtistAutocomplete';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		seriesInfoForm: SeriesInfoForm;
		onChange: (artists: SeriesInfoForm['artists']) => void;
	}

	let { seriesInfoForm, onChange }: Props = $props();

	const dataCy = 'series-artists';

	let showNew = $state(false);

	const handleChange = (option: OptionType) => {
		showNew = false;

		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';
		const refId = option.line5 || '';
		const legacyId = option.line6 || '';

		if (!id) return;

		if (!seriesInfoForm.artists[id]) {
			onChange({
				...seriesInfoForm.artists,
				[id]: {
					id,
					title,
					subTitle,
					url,
					refId,
					legacyId,
					isNew: true,
					isDeleted: false,
				},
			});
			return;
		}

		onChange({
			...seriesInfoForm.artists,
			[id]: {
				...seriesInfoForm.artists[id],
				id,
				title,
				subTitle,
				url,
				refId,
				legacyId,
				isDeleted: false,
			},
		});
	};

	const handleRemove = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';
		const refId = option.line5 || '';
		const legacyId = option.line6 || '';

		if (!id) return;

		onChange({
			...seriesInfoForm.artists,
			[id]: {
				id,
				title,
				subTitle,
				url,
				refId,
				legacyId,
				isDeleted: true,
			},
		});
	};

	let selectedArtists = $derived(
		Object.values(seriesInfoForm.artists)
			.filter((artist) => !artist.isDeleted)
			.map((artist) => {
				const option: OptionType = {
					line1: artist.title,
					line2: artist.subTitle,
					line3: artist.url,
					line4: artist.id,
					line5: artist.refId,
					line6: artist.legacyId,
				};

				return option;
			})
	);

	const handleClickAdd = () => {
		showNew = true;
	};
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="artists" required class="mb-2">
		Artists
	</InputLabel>

	<div class="mb-2 flex flex-col gap-4">
		{#each selectedArtists as option}
			<ArtistAutocomplete
				{dataCy}
				onChange={handleChange}
				onRemove={handleRemove}
				selectedOption={option}
			/>
		{/each}

		{#if showNew || !selectedArtists.length}
			<ArtistAutocomplete
				{dataCy}
				onChange={handleChange}
				onRemove={handleRemove}
				selectedOption={null}
			/>
		{/if}
	</div>

	<div class="flex justify-end">
		<Button
			onclick={handleClickAdd}
			dataCy={`${dataCy}-add`}
			variant="secondary"
			size="sm"
		>
			Add Artist
			{#snippet trailing()}
				<PlusIcon class="h-3 w-3" />
			{/snippet}
		</Button>
	</div>
</div>
