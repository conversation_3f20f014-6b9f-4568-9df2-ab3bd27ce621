<script lang="ts">
	import type { SeriesInfoForm } from '../SeriesInfoCard.svelte';
	import { ParentSeriesAutocomplete } from './ParentSeriesAutocomplete';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';

	interface Props {
		seriesInfoForm: SeriesInfoForm;
		onChange: (parentSeries: SeriesInfoForm['parentSeries']) => void;
	}

	let { seriesInfoForm, onChange }: Props = $props();

	const dataCy = 'parent-series';

	const handleChange = (option: OptionType) => {
		const title = option.line1 || '';
		const subTitle = option.line2 || '';
		const url = option.line3 || '';
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			...seriesInfoForm.parentSeries,
			id,
			title,
			subTitle,
			url,
			isDeleted: false,
		});
	};

	const handleRemove = (option: OptionType) => {
		const id = option.line4 || '';

		if (!id) return;

		onChange({
			...seriesInfoForm.parentSeries,
			isDeleted: true,
		});
	};

	let selectedOption = $derived(
		seriesInfoForm.parentSeries.isDeleted || !seriesInfoForm.parentSeries.id
			? null
			: {
					line1: seriesInfoForm.parentSeries.title || '',
					line2: seriesInfoForm.parentSeries.subTitle || '',
					line3: seriesInfoForm.parentSeries.url || '',
					line4: seriesInfoForm.parentSeries.id || '',
				}
	);
</script>

<div>
	<InputLabel variant="label3" {dataCy} for="parentSeries" class="mb-2">
		Parent series
	</InputLabel>

	<div class="mb-4 flex flex-col gap-4">
		<ParentSeriesAutocomplete
			onChange={handleChange}
			onRemove={handleRemove}
			{selectedOption}
		/>
	</div>
</div>
