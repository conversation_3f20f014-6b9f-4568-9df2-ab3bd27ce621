<script lang="ts" module>
	export const formatSeries = (
		series: GetArtworkSeriesQuery['artwork_series'][number]
	) => {
		const url = `${Routes.Series}/${series?.id}`;

		const getParentSeriesSubtitle = () => {
			const artist = series?.parent_series?.artists?.[0]?.artist_id;
			const name = artist?.person?.entity?.name;
			const nationality =
				artist?.person?.nationalities?.[0]?.country?.country_nationality;
			const yearBirthDeath = getYearBirthDeathString({
				yearBirth: artist?.person?.year_birth,
				yearDeath: artist?.person?.year_death,
				withWrapper: false,
			});

			const string = [name, nationality, yearBirthDeath]
				.map((item) => (item ? item : ''))
				.join(', ');

			return string;
		};

		return {
			line1: `${series.title}`,
			line2: getParentSeriesSubtitle(),
			line3: url,
			line4: `${series.id}`,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Status_Enum } from '$gql/types-custom';
	import { AutocompleteOption } from '$lib/components/AutocompleteOption';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { CreateNewOption } from '$lib/components/CreateNewOption';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import type {
		GetArtworkSeriesQueryVariables,
		GetArtworkSeriesQuery,
	} from '$lib/queries/__generated__/getArtworkSeries.generated';
	import { GetArtworkSeriesDocument } from '$lib/queries/__generated__/getArtworkSeries.generated';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';

	const dataCy = 'parent-series';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		selectedOption?: OptionType | null;
		onChange: (option: OptionType) => void;
		onRemove: (option: OptionType) => void;
	}

	let { selectedOption = null, onChange, onRemove }: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetArtworkSeriesQueryVariables => {
		if (!value) return {};

		const uuidRegex = new RegExp(
			'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
		);

		const isUuid = uuidRegex.test(value);

		return {
			limit: value.length < 3 ? 20 : -1,
			filter: {
				_and: [
					{
						status: { key: { _neq: Status_Enum.Archived } },
					},
					...(isUuid
						? [{ id: { _eq: value } }]
						: [
								{
									title: {
										_icontains: value,
									},
								},
							]),
				],
			},
		};
	};

	const getOptions = (data: GetArtworkSeriesQuery | undefined) => {
		return [...(data?.artwork_series || []).map(formatSeries)];
	};

	const handleRemove = () => {
		if (selectedOption) {
			onRemove(selectedOption);
		}
	};

	const handleChange = (e: { detail: { value: OptionType } }) => {
		const { value } = e.detail;

		if (value) {
			onChange(value);
		}

		return Promise.resolve();
	};
</script>

<div>
	{#if selectedOption && selectedOption.line1}
		<AutocompleteSelectedOption
			{dataCy}
			title={selectedOption.line1}
			subTitle={`(${selectedOption.line2})`}
			url={selectedOption.line3 || ''}
			onRemove={handleRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="parentSeries"
				dataCy={`${dataCy}-parent-series`}
				placeholder="Start typing to search"
				emptyValueResponse={{
					artwork_series: [],
				}}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClient}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
					noResults: 'p-0',
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={GetArtworkSeriesDocument}
				{value}
				selectedOption={null}
				onChange={handleChange}
				debounceTimeout={300}
				showCustomList
			>
				{#snippet list({ options }: { options: OptionType[] })}
					<div>
						{#each options as option, index}
							{@const title = option.line1 || ''}
							{@const subTitle = option.line2 || ''}
							{@const url = option.line3 || ''}

							{#if option.line1}
								<AutocompleteOption
									{dataCy}
									{title}
									{subTitle}
									{url}
									onClick={() => {
										$value = `${option.line1}`;
										onChange(option);
									}}
									class={classNames({
										'border-t border-gray-200': !!index,
									})}
								/>
							{/if}
						{/each}

						<CreateNewOption
							{dataCy}
							url={`${Routes.Series}/new`}
							startText="Cannot find the series?"
							ctaText="Create new series"
							endText="then come back to this artwork and link the series here."
						/>
					</div>
				{/snippet}

				{#snippet noResults()}
					<div>
						<NoResults
							class="py-2 text-left"
							dataCy={`${dataCy}-parent-series`}
						>
							No results found.
						</NoResults>

						<CreateNewOption
							{dataCy}
							url={`${Routes.Series}/new`}
							startText="Cannot find the series?"
							ctaText="Create new series"
							endText="then come back to this artwork and link the series here."
						/>
					</div>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
