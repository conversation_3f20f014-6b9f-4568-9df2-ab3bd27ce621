<script module lang="ts">
	export interface SeriesArtist {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
		refId?: string;
		legacyId?: string;
		isNew?: boolean;
		isDeleted?: boolean;
	}

	export interface ParentSeries {
		id: string;
		title: string;
		subTitle: string;
		url?: string;
		isNew?: boolean;
		isDeleted?: boolean;
	}

	export interface SeriesInfoForm {
		artists: Record<string, SeriesArtist>;
		seriesTitle: string;
		crid: string;
		executedStartYear: string;
		executedEndYear: string;
		numberOfArtworks: string;
		parentSeries: ParentSeries;
		isHeniSeries: boolean;
	}
</script>

<script lang="ts">
	import { ParentSeriesInput } from './ParentSeriesInput';
	import { SeriesArtists } from './SeriesArtists';
	import type { CheckboxValue } from '$global/components/Checkbox';
	import { Checkbox } from '$global/components/Checkbox';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel';
	import { Txt } from '$global/components/Txt';
	import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

	interface Props {
		seriesInfoForm: SeriesInfoForm;
		series: SeriesDetailsPageData['series'];
		onChange: (seriesInfoForm: SeriesInfoForm) => void;
	}

	let { seriesInfoForm, series, onChange }: Props = $props();

	const dataCy = 'series-info';

	const handleArtistsChange = (artists: SeriesInfoForm['artists']) => {
		onChange({
			...seriesInfoForm,
			artists,
		});
	};

	const handleInputChange = (field: keyof SeriesInfoForm) => (event: Event) => {
		const target = event.target as HTMLInputElement;
		const value = target.value;
		onChange({ ...seriesInfoForm, [field]: value });
	};

	const handleCheckboxChange =
		(field: keyof SeriesInfoForm) => (checked: CheckboxValue) => {
			onChange({ ...seriesInfoForm, [field]: !!checked });
		};

	const handleParentSeriesChange = (
		parentSeries: SeriesInfoForm['parentSeries']
	) => {
		onChange({
			...seriesInfoForm,
			parentSeries,
		});
	};
</script>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-6">Series information</Txt>

	<div class="mb-4">
		<SeriesArtists onChange={handleArtistsChange} {seriesInfoForm} />
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-2 gap-4">
		<div class="col-span-1">
			<Input
				dataCy={`${dataCy}-seriesTitle`}
				name="seriesTitle"
				placeholder="Series title"
				label="Series title"
				value={seriesInfoForm.seriesTitle}
				size="sm"
				onkeyup={handleInputChange('seriesTitle')}
				required
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCy}-crid`}
				name="crid"
				placeholder="E.g H-12-1"
				label="CRID"
				value={seriesInfoForm.crid}
				size="sm"
				onkeyup={handleInputChange('crid')}
			/>
		</div>
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-2 gap-4">
		<div class="col-span-1">
			<Input
				dataCy={`${dataCy}-executedStartYear`}
				name="executedStartYear"
				placeholder="YYYY"
				label="Executed start year"
				value={seriesInfoForm.executedStartYear}
				size="sm"
				onkeyup={handleInputChange('executedStartYear')}
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCy}-executedEndYear`}
				name="executedEndYear"
				placeholder="YYYY"
				label="Executed end year"
				value={seriesInfoForm.executedEndYear}
				size="sm"
				onkeyup={handleInputChange('executedEndYear')}
			/>
		</div>
	</div>

	<div class="mb-4 flex flex-col lg:grid grid-cols-2 gap-4">
		<div class="col-span-1">
			<Input
				dataCy={`${dataCy}-numberOfArtworks`}
				name="numberOfArtworks"
				placeholder="0"
				label="Number of artworks"
				value={seriesInfoForm.numberOfArtworks}
				size="sm"
				onkeyup={handleInputChange('numberOfArtworks')}
				tooltip="Copy tbc"
			/>
		</div>
		<div class="col-span-1 flex items-end">
			<InputLabel dataCy={`${dataCy}-isHeniSeries`} variant="body3">
				<Checkbox
					dataCy={`${dataCy}-isHeniSeries`}
					checked={seriesInfoForm.isHeniSeries}
					onChange={handleCheckboxChange('isHeniSeries')}
				/>
				Is HENI series
			</InputLabel>
		</div>
	</div>

	<div class="mb-4">
		<ParentSeriesInput onChange={handleParentSeriesChange} {seriesInfoForm} />
	</div>
</div>
