<script lang="ts">
	import dayjs from 'dayjs';
	import { HeaderFieldName } from './types';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { ChevronRightIcon } from '$global/assets/icons/ChevronRightIcon';
	import { CopyIcon } from '$global/assets/icons/CopyIcon';
	import { Button } from '$global/components/Button';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Link } from '$global/components/Link';
	import { LinkButton } from '$global/components/LinkButton';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableActionCell,
		TableNoResults,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { ExhibitionSearchSortField } from '$gql/types-custom';
	import { TableHeaderSortArrow } from '$lib/components/TableHeaderSortArrow';
	import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
	import { Routes } from '$lib/constants/routes';
	import { SearchParam } from '$lib/types/types';
	import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';
	import type { ExhibitionsSearchPageData } from '$routes/exhibitions/types';

	let loading = false;

	let data = $derived(getPageData<ExhibitionsSearchPageData>(page.data));

	let resultsCount = $derived(data.resultsCount);
	let exhibitions = $derived(data.exhibitions);
	let searchParams = $derived(page.url.searchParams);
	let currentPage = $derived(Number(searchParams.get(SearchParam.Page)) || 1);

	interface Header {
		fieldName: HeaderFieldName;
		title: string;
		sort?: {
			asc: string;
			desc: string;
		};
	}

	interface Row {
		exhibition: NonNullable<ExhibitionsSearchPageData['exhibitions']>[0];
		fields: {
			[HeaderFieldName.Title]: string;
			[HeaderFieldName.Location]: string;
			[HeaderFieldName.Organiser]: string;
			[HeaderFieldName.StartDate]: string;
			[HeaderFieldName.EndDate]: string;
			[HeaderFieldName.NumberOfArtworks]: string;
			[HeaderFieldName.ExhibitionUrl]: string;
			[HeaderFieldName.Action]: '';
		};
	}

	const headers: Header[] = [
		{
			fieldName: HeaderFieldName.Title,
			title: 'Exhibition title',
			sort: {
				asc: ExhibitionSearchSortField.Title,
				desc: getDescendingDirection(ExhibitionSearchSortField.Title),
			},
		},
		{
			fieldName: HeaderFieldName.Location,
			title: 'Location',
			sort: {
				asc: ExhibitionSearchSortField.Location,
				desc: getDescendingDirection(ExhibitionSearchSortField.Location),
			},
		},
		{
			fieldName: HeaderFieldName.Organiser,
			title: 'Organisers',
		},
		{
			fieldName: HeaderFieldName.StartDate,
			title: 'Start date',
			sort: {
				asc: ExhibitionSearchSortField.StartDate,
				desc: getDescendingDirection(ExhibitionSearchSortField.StartDate),
			},
		},
		{
			fieldName: HeaderFieldName.EndDate,
			title: 'End date',
			sort: {
				asc: ExhibitionSearchSortField.EndDate,
				desc: getDescendingDirection(ExhibitionSearchSortField.EndDate),
			},
		},
		{
			fieldName: HeaderFieldName.NumberOfArtworks,
			title: 'No. of artworks',
			sort: {
				asc: ExhibitionSearchSortField.NumberOfListings,
				desc: getDescendingDirection(
					ExhibitionSearchSortField.NumberOfListings
				),
			},
		},
		{
			fieldName: HeaderFieldName.ExhibitionUrl,
			title: 'View exhibition url',
		},
		{
			fieldName: HeaderFieldName.Action,
			title: '',
		},
	];

	let rows = $derived(
		(exhibitions || []).map((exhibition) => ({
			exhibition,
			fields: {
				title: exhibition?.title || '',
				location:
					exhibition?.venue_city?.name && exhibition?.venue_country?.name
						? `${exhibition.venue_city.name}, ${exhibition.venue_country.name}`
						: exhibition?.venue_city?.name ||
							exhibition?.venue_country?.name ||
							'',
				organiser:
					exhibition?.organisers
						?.map((type) => type?.entity_id?.name || '')
						.join(', ') || '',
				startDate: exhibition?.local_start_date
					? (dayjs(exhibition?.local_start_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						) as string)
					: '-',
				endDate: exhibition?.local_end_date
					? (dayjs(exhibition?.local_end_date.slice(0, -1)).format(
							'DD/MM/YYYY'
						) as string)
					: '-',
				numberOfArtworks: `${
					exhibition.aggregations?.[0]?.artwork_listing_count || 0
				}`,
				exhibitionUrl: exhibition?.exhibition_url || '',
				action: '',
			},
		})) satisfies Row[]
	);

	const dataCyPrefix = 'table';

	const handlePaginationClick = (e: Event | undefined, page: number) => {
		if (e) {
			e.preventDefault();
		}

		searchParams.set(SearchParam.Page, page.toString());

		const searchParamString = searchParams.toString();

		goto(`?${searchParamString}`, { invalidateAll: true });
	};

	const handleRowClick = (exhibitionId: string) => {
		goto(`${Routes.Exhibitions}/${exhibitionId}`);
	};
</script>

<div
	class="flex items-center justify-between rounded-t-md border border-b-0 border-gray-200 bg-white p-4"
>
	<Txt variant="h6">
		{getPaginationResultsText({
			currentPage,
			total: resultsCount,
			pageSize: TABLE_PAGE_SIZE,
		})}
	</Txt>
</div>

<div class="w-full overflow-x-auto mb-4">
	<div class="min-w-[1200px]">
		{#if exhibitions !== null}
			<table class=" w-full table-fixed border-collapse rounded-b-md bg-white">
				<TableHeaderRow dataCy={dataCyPrefix}>
					{#each headers as header, i}
						<TableHeader dataCy={dataCyPrefix}>
							{#snippet custom()}
								{#if header.sort}
									<TableHeaderSortArrow
										sortParamKey={SearchParam.Sort}
										asc={header.sort.asc}
										desc={header.sort.desc}
										{searchParams}
									>
										{header.title}
									</TableHeaderSortArrow>
								{:else}
									<Txt variant="label3">{header.title}</Txt>
								{/if}
							{/snippet}
						</TableHeader>
					{/each}
				</TableHeaderRow>

				<TableBody dataCy={dataCyPrefix}>
					{#if loading}
						<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
							<CircularProgress dataCy={dataCyPrefix} />
						</TableNoResults>
					{/if}

					{#if !loading}
						{#each rows as row, i}
							<TableRow
								index={i}
								dataCy={dataCyPrefix}
								onclick={() => handleRowClick(row.exhibition?.id as string)}
							>
								{#each headers as header}
									{@const value = row.fields[header.fieldName]}

									{@const exhibition = row.exhibition}

									{#if header.fieldName === HeaderFieldName.Action}
										<TableActionCell dataCy={dataCyPrefix} class="py-0">
											<Button
												class="bg-transparent border-none px-0"
												size="sm"
												variant="tertiary"
												dataCy={`${dataCyPrefix}-cell`}
												onclick={(e) => {
													e?.stopPropagation();
													navigator.clipboard.writeText(`${exhibition?.id}`);
												}}
											>
												<CopyIcon />
											</Button>
											<LinkButton
												dataCy={`${dataCyPrefix}-cell`}
												variant="tertiary"
												href={`${Routes.Exhibitions}/${exhibition.id}`}
												size="sm"
												buttonProps={{ class: 'bg-transparent border-none' }}
											>
												<ChevronRightIcon class="h-[14px] w-[14px]" />
											</LinkButton>
										</TableActionCell>
									{:else if header.fieldName === HeaderFieldName.ExhibitionUrl}
										<TableCell
											dataCy={dataCyPrefix}
											content={value}
											class="py-0"
										>
											<Link class="text-blue-500" href={value}>{value}</Link>
										</TableCell>
									{:else}
										<TableCell
											dataCy={dataCyPrefix}
											content={value}
											class="py-0"
										>
											{value}
										</TableCell>
									{/if}
								{/each}
							</TableRow>
						{/each}

						{#if rows.length === 0}
							<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}>
								No results found
							</TableNoResults>
						{/if}
					{/if}
				</TableBody>
			</table>
		{/if}
	</div>
</div>

{#key resultsCount}
	{#key currentPage}
		{#if resultsCount > TABLE_PAGE_SIZE}
			<div class="flex justify-end">
				{#key currentPage}
					{#key exhibitions?.length}
						<Pagination
							{currentPage}
							total={resultsCount}
							limit={TABLE_PAGE_SIZE}
							dataCy={dataCyPrefix}
							onClick={handlePaginationClick}
						/>
					{/key}
				{/key}
			</div>
		{/if}
	{/key}
{/key}
