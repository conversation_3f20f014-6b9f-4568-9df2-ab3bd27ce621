import { ExhibitionsSearchParam } from '../../constants/search';
import { EXHIBITIONS_DEFAULT_SORT } from '../../constants/sort';
import { getExhibitionsFilter } from '../getExhibitionsFilter/getExhibitionsFilter';
import { StringBoolean } from '$global/constants/string-boolean';
import type {
	ExhibitionSearchInput,
	ExhibitionSearchSortField,
} from '$gql/types-custom';
import { TABLE_PAGE_SIZE } from '$lib/constants/pagination';
import type { ExhibitionSearchQuery } from '$lib/custom-queries/__generated__/exhibitionSearch.generated';
import { ExhibitionSearchDocument } from '$lib/custom-queries/__generated__/exhibitionSearch.generated';
import { gqlClientCustom } from '$lib/gqlClientCustom';
import { SearchParam } from '$lib/types/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { ExhibitionsSearchPageLoadEvent } from '$routes/exhibitions/types';

const getOffset = (searchParams: URLSearchParams, limit = TABLE_PAGE_SIZE) => {
	const page = Number(
		getDecodedSearchParam({
			searchParams,
			key: SearchParam.Page,
		})
	);

	const offset = page ? (page - 1) * limit : 0;

	return offset;
};

export const exhibitionsSearchPageLoad = async ({
	data: existingData,
	parent,
	url,
}: ExhibitionsSearchPageLoadEvent) => {
	const parentData = await parent();
	const authorization = `Bearer ${parentData.user?.access_token}`;
	const searchParams = url.searchParams;
	const currentPage = Number(searchParams.get(SearchParam.Page)) || 1;
	const data = existingData || {};

	let res: ExhibitionSearchQuery | null = null;
	let exhibitions: ExhibitionSearchQuery['exhibitionSearch']['data'] | null =
		null;
	let resultsCount = 0;

	const showResults =
		getDecodedSearchParam({
			searchParams,
			key: ExhibitionsSearchParam.ShowResults,
		}) === StringBoolean.True;

	if (showResults) {
		const filters = getExhibitionsFilter(searchParams);
		const offset = getOffset(searchParams);

		const sort = getSortWithDirection<ExhibitionSearchSortField>(
			searchParams,
			EXHIBITIONS_DEFAULT_SORT
		);

		const input: ExhibitionSearchInput = {
			filters,
			limit: TABLE_PAGE_SIZE,
			offset,
			sort,
		};

		res = await gqlClientCustom.request(
			ExhibitionSearchDocument,
			{
				input,
			},
			{
				authorization,
			}
		);
	}

	if (res) {
		exhibitions = res.exhibitionSearch.data || [];
		resultsCount = res.exhibitionSearchCount || 0;
	}

	return {
		...parentData,
		...data,
		exhibitions,
		resultsCount,
		showResults,
		currentPage,
	};
};
