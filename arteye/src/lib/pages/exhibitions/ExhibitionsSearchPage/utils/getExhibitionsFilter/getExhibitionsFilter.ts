import { ExhibitionsSearchParam } from '../../constants/search';
import type { Exhibition_Filter } from '$gql/types';
import type {
	ExhibitionSearchFilters,
	IntValueFilter,
	ValueFilterOperator,
} from '$gql/types-custom';
import { Status_Enum } from '$gql/types-custom';
import type { SearchRange } from '$lib/constants/search-range-options';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';
import { getSearchRangeFilterFullDate } from '$lib/utils/getSearchRangeFilterFullDate/getSearchRangeFilterFullDate';

export const getExhibitionsFilter = (searchParams: URLSearchParams) => {
	let filters: ExhibitionSearchFilters = {};

	const title = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.Title,
	});

	const organiser = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.Organiser,
	});

	const artist = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.Artist,
	});

	const startDate = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.StartDate,
	});

	const startDateRange = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.StartDateRange,
	});

	const endDate = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.EndDate,
	});

	const endDateRange = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.EndDateRange,
	});

	const city = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.City,
	});

	const country = getDecodedSearchParam({
		searchParams,
		key: ExhibitionsSearchParam.Country,
	});

	if (title) {
		filters = {
			...filters,
			title,
		};
	}

	if (organiser) {
		filters = {
			...filters,
			organiserNameOrId: organiser,
		};
	}

	if (artist) {
		filters = {
			...filters,
			artistNameOrId: artist,
		};
	}

	if (startDate) {
		filters = {
			...filters,
			localStartDate: getValueFilter({
				value: startDate,
				range: startDateRange as ValueFilterOperator,
				type: 'date',
			}) as IntValueFilter,
		};
	}

	if (endDate) {
		filters = {
			...filters,
			localEndDate: getValueFilter({
				value: endDate,
				range: endDateRange as ValueFilterOperator,
				type: 'date',
			}) as IntValueFilter,
		};
	}

	if (city) {
		filters = {
			...filters,
			location: {
				...filters.location,
				cityNameOrCode: city,
			},
		};
	}

	if (country) {
		filters = {
			...filters,
			location: {
				...filters.location,
				countryNameOrCode: country,
			},
		};
	}

	return filters;
};
