// import { redirect } from '@sveltejs/kit';
// import { Cookies } from '$lib/constants/cookies';
// import { Searches } from '$lib/constants/searches';
// import type { RootPageServerLoadEvent } from '$routes/types';

// export const exhibitionsSearchPageServerLoad = async ({
// 	parent,
// 	cookies,
// 	url,
// }: RootPageServerLoadEvent) => {
// 	const data = await parent();

// 	const prevArtistUrl = JSON.parse(
// 		(cookies.get(Cookies.Searches) as Searches) || {}
// 	)?.[Searches.Exhibition];

// 	if (prevArtistUrl && !url.searchParams.toString().length) {
// 		redirect(302, prevArtistUrl);
// 	}

// 	return {
// 		...data,
// 	};
// };
