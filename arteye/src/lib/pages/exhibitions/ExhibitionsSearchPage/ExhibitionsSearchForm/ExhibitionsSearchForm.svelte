<script module lang="ts">
	export interface ExhibitionsSearchFormFields {
		[ExhibitionsSearchParam.Sort]: string[];
		[ExhibitionsSearchParam.Title]: string;
		[ExhibitionsSearchParam.Organiser]: string;
		[ExhibitionsSearchParam.StartDate]: string;
		[ExhibitionsSearchParam.StartDateRange]: string;
		[ExhibitionsSearchParam.EndDate]: string;
		[ExhibitionsSearchParam.EndDateRange]: string;
		[ExhibitionsSearchParam.City]: string;
		[ExhibitionsSearchParam.Country]: string;
		[ExhibitionsSearchParam.Artist]: string;
	}
</script>

<script lang="ts">
	import { setBeforeNavigatePromise } from '$lib/runes/navigation.svelte';
	import { ExhibitionsSearchParam } from '../constants/search';
	import { EXHIBITIONS_SORT_OPTIONS } from '../constants/sort';
	import { beforeNavigate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import { LinkButton } from '$global/components/LinkButton';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import { Txt } from '$global/components/Txt';
	import { StringBoolean } from '$global/constants/string-boolean';
	import { ValueFilterOperator } from '$gql/types-custom';
	import { Routes } from '$lib/constants/routes';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		NEW_SEARCH_RANGE_PLACEHOLDERS,
	} from '$lib/constants/search-range-options';
	import { Searches } from '$lib/constants/searches';
	import { findMultiselectOptions } from '$lib/utils/findMultiselectOptions/findMultiselectOptions';
	import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';
	import { getDecodedSearchRangeParam } from '$lib/utils/getDecodedSearchRangeParam/getDecodedSearchRangeParam';
	import { setArteyeSearch } from '$lib/utils/setArteyeSearch/setArteyeSearch';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	interface Props {
		formatParamString: (
			data: ExhibitionsSearchFormFields,
			showResults: StringBoolean,
			includePageParams: boolean
		) => string;
	}

	let { formatParamString }: Props = $props();

	let sort: MultiSelectOption[] = $state(
		findMultiselectOptions(
			page.url.searchParams,
			ExhibitionsSearchParam.Sort,
			EXHIBITIONS_SORT_OPTIONS
		)
	);

	$effect(() => {
		sort = findMultiselectOptions(
			page.url.searchParams,
			ExhibitionsSearchParam.Sort,
			EXHIBITIONS_SORT_OPTIONS
		);
	});

	let title = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.Title,
		})
	);
	let organiser = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.Organiser,
		})
	);
	let artist = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.Artist,
		})
	);
	let startDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.StartDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let startDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.StartDate,
		})
	);
	let endDateRange = $state(
		getDecodedSearchRangeParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.EndDateRange,
			defaultSearchRange: ValueFilterOperator.Equal,
		})
	);
	let endDate = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.EndDate,
		})
	);
	let city = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.City,
		})
	);
	let country = $state(
		getDecodedSearchParam({
			searchParams: page.url.searchParams,
			key: ExhibitionsSearchParam.Country,
		})
	);

	const dataCyPrefix = 'search';

	const getValuesFromSelectOptions = (options: { value: string }[]) =>
		options.map((option) => option.value);

	beforeNavigate(async ({ from, to }) => {
		const saveTab = async () => {
			if (
				typeof window !== 'undefined' &&
				to?.url.pathname !== Routes.Exhibitions
			) {
				const a = new URLSearchParams(`?${from?.url.href.split('?')[1]}`);
				const b = new URLSearchParams(
					`?${formatParamString(getParams(), StringBoolean.True, true)}`
				);

				a.sort();
				b.sort();

				const queryParams = formatParamString(
					getParams(),
					`${a.toString() === b.toString()}` as StringBoolean,
					true
				);

				await setArteyeSearch(
					Searches.Exhibition,
					`${Routes.Exhibitions}?${queryParams}`
				);
			}
		};

		const operation = saveTab();
		setBeforeNavigatePromise(operation);
		await operation;
	});

	const getParams = () => ({
		sort: getValuesFromSelectOptions(sort),
		title,
		organiser,
		artist,
		startDateRange,
		startDate,
		endDateRange,
		endDate,
		city,
		country,
	});

	const handleSearchClick = () => {
		const queryParams = formatParamString(
			getParams(),
			StringBoolean.True,
			false
		);

		goto(`?${queryParams}`);
	};

	const handleClearClick = () => {
		sort = [];
		title = '';
		organiser = '';
		artist = '';
		startDateRange = ValueFilterOperator.Equal;
		startDate = '';
		endDateRange = ValueFilterOperator.Equal;
		endDate = '';
		city = '';
		country = '';

		goto(userRoutes.routes.exhibition);
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search exhibitions</Txt>
		<LinkButton
			dataCy={`${dataCyPrefix}-create-new`}
			size="sm"
			href={`${Routes.Exhibitions}/new`}
			class="hidden lg:block"
		>
			Create New</LinkButton
		>
	</div>
	<div
		class="flex flex-col lg:grid grid-cols-5 gap-4 border-b border-gray-200 p-4"
	>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-exhibition-title`}
				name="title"
				placeholder="Enter title"
				label="Exhibition title"
				bind:value={title}
				size="sm"
			/>
		</div>
		<div class="col-span-2">
			<Input
				dataCy={`${dataCyPrefix}-exhibition-organiser`}
				name="organiser"
				placeholder="Organiser name"
				label="Organisers"
				bind:value={organiser}
				size="sm"
			/>
		</div>
		<div class="col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-exhibition-artist`}
				name="artist"
				placeholder="Artist name"
				label="Artist"
				bind:value={artist}
				size="sm"
			/>
		</div>

		<div
			class="col-span-5 flex flex-col lg:grid grid-cols-5 justify-center gap-4"
		>
			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="Start date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-start-date`}
					name="start_date"
					bind:selectValue={startDateRange}
					bind:inputValue={startDate}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[startDateRange]}
					tooltip="Select a start date and range"
					tooltipColor="gray-300"
				/>
			</div>

			<div class="col-span-1">
				<InputWithSelect
					size="sm"
					label="End date (DD/MM/YYYY)"
					dataCy={`${dataCyPrefix}-end-date`}
					name="end_dates"
					bind:selectValue={endDateRange}
					bind:inputValue={endDate}
					options={NEW_SEARCH_RANGE_OPTIONS}
					placeholder={NEW_SEARCH_RANGE_PLACEHOLDERS[endDateRange]}
					tooltip="Select an end date and range"
					tooltipColor="gray-300"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-city`}
					name="city"
					placeholder=""
					label="City"
					bind:value={city}
					size="sm"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCyPrefix}-country`}
					name="country"
					placeholder=""
					label="Country"
					bind:value={country}
					size="sm"
				/>
			</div>
			<div class="col-span-1">
				<MultiSelect
					name="sort_by"
					dataCy={`${dataCyPrefix}-sort-by`}
					label="Sort by"
					bind:selected={sort}
					placeholder="Sort by"
					options={EXHIBITIONS_SORT_OPTIONS}
					class="col-span-1"
					size="sm"
				/>
			</div>
		</div>
	</div>
	<div class="flex flex-col md:flex-row gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="md:w-[300px]"
			onclick={handleSearchClick}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
