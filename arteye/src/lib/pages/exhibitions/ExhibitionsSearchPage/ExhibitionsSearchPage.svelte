<script lang="ts">
	import { ExhibitionsSearchParam } from './constants/search';
	import { ExhibitionsSearchForm } from './ExhibitionsSearchForm';
	import type { ExhibitionsSearchFormFields } from './ExhibitionsSearchForm/ExhibitionsSearchForm.svelte';
	import { ExhibitionsTable } from './ExhibitionsTable';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import type { StringBoolean } from '$global/constants/string-boolean';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getPageParams } from '$lib/utils/getPageParams/getPageParams';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { ExhibitionsSearchPageData } from '$routes/exhibitions/types';

	let data = $derived(getPageData<ExhibitionsSearchPageData>(page.data));
	let showResults = $derived(data.showResults);

	const formatParamString = (
		values: ExhibitionsSearchFormFields,
		showResults: StringBoolean,
		includePageParams: boolean
	) => {
		const {
			sort,
			title,
			organiser,
			artist,
			startDate,
			startDateRange,
			endDate,
			endDateRange,
			city,
			country,
		} = values;

		const params: Record<ExhibitionsSearchParam, string> = {
			[ExhibitionsSearchParam.Sort]: getSearchParamFromStringArray(sort),
			[ExhibitionsSearchParam.ShowResults]: showResults,
			[ExhibitionsSearchParam.Title]: title,
			[ExhibitionsSearchParam.Organiser]: organiser,
			[ExhibitionsSearchParam.Artist]: artist,
			[ExhibitionsSearchParam.StartDate]: startDate,
			[ExhibitionsSearchParam.StartDateRange]: startDate ? startDateRange : '',
			[ExhibitionsSearchParam.EndDate]: endDate,
			[ExhibitionsSearchParam.EndDateRange]: endDate ? endDateRange : '',
			[ExhibitionsSearchParam.City]: city,
			[ExhibitionsSearchParam.Country]: country,
		};

		return `${getSearchParamString(params)}${includePageParams ? getPageParams(page.url.href) : ''}`;
	};
</script>

<div class="mb-6">
	<ExhibitionsSearchForm {formatParamString} />
</div>

{#if showResults}
	<ExhibitionsTable />
{/if}
