import { ExhibitionSearchSortField } from '$gql/types-custom';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const EXHIBITIONS_SORT_OPTIONS = sortByField(
	[
		{
			label: 'Date created (Asc)',
			value: ExhibitionSearchSortField.DateCreated,
		},
		{
			label: 'Date created (Desc)',
			value: getDescendingDirection(ExhibitionSearchSortField.DateCreated),
		},
		{
			label: 'Date updated (Asc)',
			value: ExhibitionSearchSortField.DateUpdated,
		},
		{
			label: 'Date updated (Desc)',
			value: getDescendingDirection(ExhibitionSearchSortField.DateUpdated),
		},
		{ label: 'Title (A-Z)', value: ExhibitionSearchSortField.Title },
		{
			label: 'Title (Z-A)',
			value: getDescendingDirection(ExhibitionSearchSortField.Title),
		},
		{ label: 'Start date (Asc)', value: ExhibitionSearchSortField.StartDate },
		{
			label: 'Start date (Desc)',
			value: getDescendingDirection(ExhibitionSearchSortField.StartDate),
		},
		{ label: 'End date (Asc)', value: ExhibitionSearchSortField.EndDate },
		{
			label: 'End date (Desc)',
			value: getDescendingDirection(ExhibitionSearchSortField.EndDate),
		},
		{ label: 'Location (A-Z)', value: ExhibitionSearchSortField.Location },
		{
			label: 'Location (Z-A)',
			value: getDescendingDirection(ExhibitionSearchSortField.Location),
		},
		{
			label: 'No. of artworks (Asc)',
			value: ExhibitionSearchSortField.NumberOfListings,
		},
		{
			label: 'No. of artworks (Desc)',
			value: getDescendingDirection(ExhibitionSearchSortField.NumberOfListings),
		},
	],
	'label'
);

export const EXHIBITIONS_DEFAULT_SORT = ExhibitionSearchSortField.Title;
