<script lang="ts">
	import { ArtistNameAutoComplete } from './ArtistNameAutoComplete';
	import { type ArtistType } from './types';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';

	interface Props {
		index: number;
		selectedOption?: OptionType | null;
		onChange: (artist: ArtistType | Record<never, never>) => void;
		onDelete: (index: number) => void;
	}

	let {
		index,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
	}: Props = $props();

	const dataCy = 'organiser-row';

	const handleDeleteOrganiser = () => {
		onDelete(index);
	};

	const onRemoveSelectedEntity = () => {
		onChange({});
	};

	const handleEntityChange = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		onChange({
			artist_id: { id: `${e.detail.value.line4}` },
			exhibition_id: { id: page.params.id },
		});
		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	<TableCell {dataCy}>
		{#snippet custom()}
			<ArtistNameAutoComplete
				bind:selectedOption
				{onRemoveSelectedEntity}
				{dataCy}
				onChange={handleEntityChange}
			/>
		{/snippet}
	</TableCell>

	<TableActionCell {dataCy} class="py-0">
		<Button
			onclick={handleDeleteOrganiser}
			dataCy={`${dataCy}-delete`}
			class="h-[2rem] w-[2rem] px-0"
			variant="secondary"
			size="xs"
		>
			<BinIcon class="h-3 w-3" />
		</Button>
	</TableActionCell>
</TableRow>
