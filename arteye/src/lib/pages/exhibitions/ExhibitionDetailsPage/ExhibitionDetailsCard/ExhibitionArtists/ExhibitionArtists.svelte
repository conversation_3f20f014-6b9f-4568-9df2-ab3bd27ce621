<script lang="ts">
	import { ExhibitionArtistRow } from './ExhibitionArtistRow';
	import type { ArtistType } from './ExhibitionArtistRow';
	import { formatArtist } from './ExhibitionArtistRow/ArtistNameAutoComplete/ArtistNameAutoComplete.svelte';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import type { ArtistSearchQuery } from '$lib/custom-queries/__generated__/artistSearch.generated';

	interface Props {
		artists?: ArtistType[];
		onChange: (entities: ArtistType[]) => void;
	}

	let { artists = $bindable([]), onChange }: Props = $props();

	const dataCy = 'artists';

	let artistsSelectedOptions: (OptionType | null)[] = $state(
		(artists || []).map((artist) => {
			if (!artist?.artist_id?.id) return null;

			return formatArtist(
				artist?.artist_id as Parameters<typeof formatArtist>[0]
			);
		})
	);

	const onArtistChange =
		(index: number) =>
		(
			artist:
				| ArtistSearchQuery['artistSearch']['data'][number]
				| Record<never, never>
		) => {
			onChange(
				(artists || []).map((existingArtist, i) =>
					i === index
						? {
								...artist,
								exhibition_id: {
									id: page.params.id,
								},
								id: existingArtist.id,
								isNew: existingArtist?.isNew,
								isDeleted: existingArtist?.isDeleted,
							}
						: existingArtist
				) as ArtistType[]
			);
		};

	const handleArtistDelete = (index: number) => {
		onChange(
			(artists || []).map((artist, i) => {
				if (i === index) {
					return {
						...artist,
						isDeleted: true,
					};
				}

				return artist;
			})
		);
	};

	const handleArtistAdd = () => {
		artists = [
			...(artists || []).map((artist) => artist),
			{
				isNew: true,
			},
		] as ArtistType[];

		artistsSelectedOptions = [...artistsSelectedOptions, null];
	};
</script>

<div>
	<InputLabel {dataCy} variant="label3" class="mb-2">Artists</InputLabel>
	<table class="w-full table-fixed rounded-md bg-white">
		<TableHeaderRow {dataCy}>
			<TableHeader {dataCy}>Name</TableHeader>
			<TableHeader {dataCy} class="flex justify-end">
				<Button
					onclick={handleArtistAdd}
					dataCy={`${dataCy}-add`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					size="xs"
				>
					<PlusIcon class="h-3 w-3" />
				</Button>
			</TableHeader>
		</TableHeaderRow>

		<TableBody {dataCy}>
			{#if artists}
				{#each artists as artist, index}
					{#if !artist.isDeleted}
						<ExhibitionArtistRow
							{index}
							bind:selectedOption={artistsSelectedOptions[index]}
							onChange={onArtistChange(index)}
							onDelete={handleArtistDelete}
						/>
					{/if}
				{/each}
			{/if}
		</TableBody>
	</table>
</div>
