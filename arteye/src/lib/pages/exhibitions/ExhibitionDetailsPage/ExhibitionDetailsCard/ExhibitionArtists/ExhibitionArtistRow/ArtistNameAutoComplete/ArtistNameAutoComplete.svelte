<script lang="ts" module>
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';

	export const formatArtist = (
		artist: ArtistSearchQuery['artistSearch']['data'][number] & {
			legacy_id?: string | null | undefined;
		}
	) => {
		const getSubtitle = () => {
			const nationality =
				artist?.person?.nationalities?.[0]?.country?.country_nationality;
			const yearBirthDeath =
				artist?.person?.year_birth && artist?.person?.year_death
					? getYearBirthDeathString({
							yearBirth: artist.person?.year_birth,
							yearDeath: artist.person?.year_death,
							withWrapper: false,
						})
					: '';

			const artworkCount = artist?.aggregations?.[0]?.artwork_count || 0;

			const refId = artist?.reference_id ? `, ${artist?.reference_id}` : '';

			if (nationality && yearBirthDeath) {
				return `(${nationality}, ${yearBirthDeath}) ${artworkCount} artworks${refId}`;
			}

			if (nationality) {
				return `(${nationality}) ${artworkCount} artworks${refId}`;
			}

			if (yearBirthDeath) {
				return `(${yearBirthDeath}) ${artworkCount} artworks${refId}`;
			}

			return '';
		};

		return {
			line1: `${artist?.person?.entity?.name}`,
			line1Suffix: getSubtitle(),
			line2: `${Routes.Artists}/${artist?.id}`,
			line4: `${artist?.id}`,
			...(artist?.reference_id && {
				line5: `Ref ID: ${artist?.reference_id}`,
			}),
			// ...(artist?.legacy_id && {
			// 	line6: `Legacy ID: ${artist?.legacy_id}`,
			// }),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Routes } from '$lib/constants/routes';
	import {
		ArtistSearchDocument,
		type ArtistSearchQuery,
		type ArtistSearchQueryVariables,
	} from '$lib/custom-queries/__generated__/artistSearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { SortDirection } from '$gql/types-custom';
	import { ArtistSearchSortField } from '$gql/types-custom';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		onRemoveSelectedEntity: () => void;
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		onRemoveSelectedEntity,
		placeholder = 'Search entity',
		dataCy,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): ArtistSearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : 50,
				filters: {
					nameOrId: value,
				},
				sort: [
					{
						direction: SortDirection.Desc,
						field: ArtistSearchSortField.NumberOfArtworks,
					},
				],
			},
		};
	};

	const getOptions = async (value: string) => {
		if (!value) {
			return [];
		}

		const res = await gqlClientCustom.request(
			ArtistSearchDocument,
			getVariables(value),
			getAuthorizationHeaders(data)
		);

		return (res?.artistSearch?.data || []).map((artist) =>
			formatArtist(artist as NonNullable<typeof artist>)
		);
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<PromiseAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		onRemoveSelectedOption={onRemoveSelectedEntity}
		showResultsWhenEmpty={false}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		{getOptions}
		{value}
		bind:selectedOption
		{onChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-organisers-autocomplete`}
				>No results found.</NoResults
			>{/snippet}
	</PromiseAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				onRemoveSelectedEntity();
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
