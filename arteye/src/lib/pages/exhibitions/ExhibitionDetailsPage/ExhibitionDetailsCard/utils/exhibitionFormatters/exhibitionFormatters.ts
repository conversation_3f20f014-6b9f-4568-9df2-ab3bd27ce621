import { getImageUrl } from '../../../../../../utils/getImageUrl/getImageUrl';
import type { ExhibitionImageType } from '$lib/pages/exhibitions/ExhibitionDetailsPage/ExhibitionImage';
import type { GetExhibitionsQuery } from '$lib/queries/__generated__/getExhibitions.generated';

export const formatExhibitionImage = (
	exhibition: GetExhibitionsQuery['exhibition'][0],
	accessToken: string | undefined
) => {
	return (
		exhibition?.cover_image
			? {
					...exhibition?.cover_image,
					url: getImageUrl(exhibition?.cover_image?.id, accessToken),
				}
			: null
	) as ExhibitionImageType;
};
