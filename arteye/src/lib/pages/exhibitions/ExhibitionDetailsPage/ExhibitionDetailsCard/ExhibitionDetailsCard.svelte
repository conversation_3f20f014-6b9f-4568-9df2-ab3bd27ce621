<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { ExhibitionAddress } from './ExhibitionAddress';
	import { ExhibitionArtists } from './ExhibitionArtists';
	import type { ArtistType } from './ExhibitionArtists/ExhibitionArtistRow';
	import { ExhibitionOrganisers } from './ExhibitionOrganisers';
	import type { OrganiserType } from './ExhibitionOrganisers/ExhibitionOrganisersRow';
	import type { ExhibitionDetailsForm } from './types';
	import { page } from '$app/state';
	import { AccordionItem } from '$global/components/Accordion';
	import { Input } from '$global/components/Input';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Exhibition_Attribute } from '$gql/types';
	import type { ExhibitionPageData } from '$routes/exhibitions/[id]/types';

	let data = $derived(getPageData<ExhibitionPageData>(page.data));
	let attributeTypes = $derived(
		data.user?.dropdowns?.exhibitionAttributeTypes || []
	);

	interface Props {
		exhibitionDetailsForm: ExhibitionDetailsForm;
		onChange: (form: ExhibitionDetailsForm) => void;
		onDeleteAttributes: (attributes: Exhibition_Attribute[]) => void;
		class?: string;
	}

	let { ...props }: Props = $props();

	let attributeOptions = $derived(
		attributeTypes?.map((attribute) => ({
			label: `${attribute?.name}`,
			value: `${attribute?.key}`,
		}))
	);

	const dataCy = 'exhibition-details-card';

	const handleOrganisersChange = (organisers: OrganiserType[]) => {
		props.onChange({ ...props.exhibitionDetailsForm, organisers });
	};

	const handleArtistsChange = (artists: ArtistType[]) => {
		props.onChange({ ...props.exhibitionDetailsForm, artists });
	};

	const handleInputChange =
		(field: keyof ExhibitionDetailsForm) => (event: Event) => {
			const target = event.target as HTMLInputElement;
			const value = target.value;
			props.onChange({ ...props.exhibitionDetailsForm, [field]: value });
		};

	const handleAttributeTypesChange = (
		event: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		let attributes = selected.map((sel) => {
			let match = (props.exhibitionDetailsForm.attributes || []).find(
				(ex) => ex?.type?.key === sel.value
			);
			if (match) {
				return match;
			} else {
				return {
					type: { key: sel.value, name: sel.label },
					exhibition: { id: props.exhibitionDetailsForm.id },
				};
			}
		}) as Exhibition_Attribute[];

		let deleting = (props.exhibitionDetailsForm?.attributes || []).filter(
			(item) => !selected.some((sel) => sel.value === item?.type?.key)
		) as Exhibition_Attribute[];

		if (deleting.length > 0) {
			props.onDeleteAttributes(deleting);
		}

		props.onChange({
			...props.exhibitionDetailsForm,
			attributes,
		});
	};
</script>

<AccordionItem
	{dataCy}
	title="Exhibition details"
	class={twMerge('rounded-md border bg-white', props.class)}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4">
		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-title`}
					name="title"
					placeholder=""
					required
					tooltip="The title of the exhibition"
					tooltipColor="gray-300"
					label="Title"
					value={props.exhibitionDetailsForm.title || ''}
					size="sm"
					onkeyup={handleInputChange('title')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-exhibitionUrl`}
					name="exhibitionUrl"
					placeholder=""
					label="Exhibition Url"
					value={props.exhibitionDetailsForm.exhibition_url || ''}
					size="sm"
					onkeyup={handleInputChange('exhibition_url')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-startDate`}
					name="startDate"
					placeholder=""
					label="Start Date"
					type="date"
					tooltip="Select a start date"
					tooltipColor="gray-300"
					value={getDayFromDirectus(
						props.exhibitionDetailsForm.start_date || ''
					)}
					size="sm"
					onkeyup={handleInputChange('start_date')}
					onchange={handleInputChange('start_date')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-endDate`}
					name="endDate"
					placeholder=""
					label="End Date"
					type="date"
					tooltip="Select an end date"
					tooltipColor="gray-300"
					value={getDayFromDirectus(props.exhibitionDetailsForm.end_date || '')}
					size="sm"
					onkeyup={handleInputChange('end_date')}
					onchange={handleInputChange('end_date')}
				/>
			</div>

			<MultiSelect
				name="attributes"
				dataCy={`${dataCy}-attributes`}
				label="Attributes"
				selected={(props.exhibitionDetailsForm.attributes || [])
					.map((attribute) => ({
						label: `${attribute?.type?.name}`,
						value: `${attribute?.type?.key}`,
					}))
					.filter(
						(attr, i, arr) =>
							arr.findIndex((attr2) => attr.value === attr2.value) === i
					)}
				placeholder="Type or select"
				options={attributeOptions}
				class="col-span-1"
				size="sm"
				onChange={handleAttributeTypesChange}
			/>
		</div>

		<div class="mb-4">
			<ExhibitionArtists
				artists={props.exhibitionDetailsForm.artists}
				onChange={handleArtistsChange}
			/>
		</div>

		<div class="mb-6 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-4">
				<Input
					dataCy={`${dataCy}-description`}
					name="description"
					placeholder=""
					rows={5}
					label="Description"
					tooltip="A description of the exhibition"
					tooltipColor="gray-300"
					value={props.exhibitionDetailsForm?.description || ''}
					size="sm"
					onkeyup={handleInputChange('description')}
				/>
			</div>
		</div>

		<div class="mb-4">
			<ExhibitionAddress
				exhibitionDetailsForm={props.exhibitionDetailsForm}
				{handleInputChange}
				onChange={props.onChange}
			/>
		</div>

		<div class="mb-4">
			<ExhibitionOrganisers
				exhibitionDetailsForm={props.exhibitionDetailsForm}
				organisers={props.exhibitionDetailsForm.organisers}
				onChange={handleOrganisersChange}
			/>
		</div>
	</div>
</AccordionItem>
