<script lang="ts">
	import type { ExhibitionDetailsForm } from '../../types';
	import { OrganisersNameAutoComplete } from './OrganisersNameAutoComplete';
	import { OrganisersFieldName, type OrganiserType } from './types';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';

	interface Props {
		index: number;
		headers: { fieldName?: OrganisersFieldName }[];
		exhibitionDetailsForm: ExhibitionDetailsForm;
		selectedOption?: OptionType | null;
		onChange: (organiser: OrganiserType | Record<never, never>) => void;
		onDelete: (index: number) => void;
	}

	let {
		index,
		headers,
		exhibitionDetailsForm,
		selectedOption = $bindable(null),
		onChange,
		onDelete,
	}: Props = $props();

	let organiser = $derived((exhibitionDetailsForm.organisers || [])[index]);

	const dataCy = 'organiser-row';

	const handleDeleteOrganiser = () => {
		onDelete(index);
	};

	const onRemoveSelectedEntity = () => {
		onChange({});
	};

	const handleEntityChange = (e: {
		detail: {
			value: OrganiserType;
		};
	}) => {
		onChange({ entity_id: e.detail.value });
		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={organiser.entity_id} />

					<Button
						onclick={handleDeleteOrganiser}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === OrganisersFieldName.Name}
			<TableCell {dataCy}>
				{#snippet custom()}
					<OrganisersNameAutoComplete
						{exhibitionDetailsForm}
						bind:selectedOption
						{onRemoveSelectedEntity}
						{dataCy}
						onChange={handleEntityChange}
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
