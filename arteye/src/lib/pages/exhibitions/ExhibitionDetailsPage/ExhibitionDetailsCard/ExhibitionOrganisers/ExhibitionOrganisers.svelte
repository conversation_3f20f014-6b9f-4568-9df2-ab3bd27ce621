<script lang="ts">
	import type { ExhibitionDetailsForm } from '../types';
	import {
		OrganisersFieldName,
		ExhibitionOrganisersRow,
	} from './ExhibitionOrganisersRow';
	import type { OrganiserType } from './ExhibitionOrganisersRow';
	import { formatEntityOrganiser } from './ExhibitionOrganisersRow/OrganisersNameAutoComplete/OrganisersNameAutoComplete.svelte';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { InputLabel } from '$global/components/InputLabel';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import type { EntitySearchQuery } from '$lib/custom-queries/__generated__/entitySearch.generated';
	import type { GetEntitiesQuery } from '$lib/queries/__generated__/getEntities.generated';

	const headers = [
		{
			title: 'Name',
			fieldName: OrganisersFieldName.Name,
		},
		{ title: '' },
	];

	interface Props {
		organisers?: OrganiserType[];
		exhibitionDetailsForm: ExhibitionDetailsForm;
		onChange: (entities: OrganiserType[]) => void;
	}

	let {
		organisers = $bindable([]),
		exhibitionDetailsForm,
		onChange,
	}: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(organisers || []).map((organiser) => {
			if (!organiser?.entity_id?.id) return null;

			return formatEntityOrganiser(
				organiser.entity_id as EntitySearchQuery['entitySearch']['data'][number]
			);
		})
	);

	const onOrganiserChange =
		(index: number) =>
		(organiser: GetEntitiesQuery['entity'][number] | Record<never, never>) => {
			const entities = (organisers || []).map((existingOrganiser, i) =>
				i === index
					? {
							...organiser,
							exhibition_id: {
								id: page.params.id,
							},
							id: existingOrganiser.id,
							isNew: existingOrganiser.isNew,
							isDeleted: existingOrganiser.isDeleted,
						}
					: existingOrganiser
			) as OrganiserType[];
			onChange(entities);
		};

	const handleOrganiserDelete = (index: number) => {
		const entities = (organisers || []).map((organiser, i) => {
			if (i === index) {
				return {
					...organiser,
					isDeleted: true,
				};
			}

			return organiser;
		});

		onChange(entities);
	};

	const dataCy = 'organisers';

	const handleOrganiserAdd = () => {
		organisers = [
			...(organisers || []).map((organiser) => organiser),
			{
				isNew: true,
			},
		] as OrganiserType[];

		selectedOptions = [...selectedOptions, null];
	};
</script>

<div>
	<InputLabel {dataCy} required variant="label3" class="mb-2">
		Organisers
	</InputLabel>

	<div class="max-lg:overflow-x-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[800px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName}
						<TableHeader {dataCy}>
							{header.title}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							<Button
								onclick={handleOrganiserAdd}
								dataCy={`${dataCy}-add`}
								class="h-[2rem] w-[2rem] px-0"
								variant="secondary"
								size="xs"
							>
								<PlusIcon class="h-3 w-3" />
							</Button>
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if organisers}
					{#each organisers as organiser, index}
						{#if !organiser.isDeleted}
							<ExhibitionOrganisersRow
								{headers}
								{exhibitionDetailsForm}
								{index}
								bind:selectedOption={selectedOptions[index]}
								onChange={onOrganiserChange(index)}
								onDelete={handleOrganiserDelete}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</div>
