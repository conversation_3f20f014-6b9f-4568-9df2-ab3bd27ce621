<script lang="ts" module>
	export const formatEntityOrganiser = (
		entity: EntitySearchQuery['entitySearch']['data'][number]
	) => {
		const url = (() => {
			if (entity?.type?.key === 'person') {
				if (entity?.artist) {
					return `${Routes.Artists}/${entity?.artist?.id}`;
				} else {
					return `${Routes.People}/${entity?.person?.id}`;
				}
			}

			return `${Routes.Organisations}/${entity?.organisation?.id}`;
		})();

		const location = (
			entity?.type?.key === 'person'
				? [
						entity?.addresses?.[0]?.city?.name,
						entity?.addresses?.[0]?.country?.name,
					]
				: [
						entity?.organisation?.location?.name,
						entity?.organisation?.location?.country?.name,
					]
		)
			.filter(Boolean)
			.join(', ');

		const dobDod = (() => {
			const yearBirth = entity?.person?.year_birth || '';
			const yearDeath = entity?.person?.year_death || '';

			return [yearBirth, yearDeath].filter(Boolean).join(' - ');
		})();

		return {
			line1: `${entity?.name}`,
			line2: url,
			line3: `${entity?.type?.key}`,
			line4: `${entity?.id}`,
			line1Suffix: (() => {
				const content = [
					location,
					entity?.type?.key === 'person' && dobDod ? dobDod : '',
				]
					.filter(Boolean)
					.join(', ');
				return content ? `(${content})` : '';
			})(),
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import type { OrganiserType } from '..';
	import type { ExhibitionDetailsForm } from '../../../types';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Routes } from '$lib/constants/routes';
	import {
		EntitySearchDocument,
		type EntitySearchQuery,
		type EntitySearchQueryVariables,
	} from '$lib/custom-queries/__generated__/entitySearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	let entities: EntitySearchQuery['entitySearch']['data'] = [];

	interface Props {
		onRemoveSelectedEntity: () => void;
		placeholder?: string;
		exhibitionDetailsForm: ExhibitionDetailsForm;
		dataCy: string;
		selectedOption?: OptionType | null;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OrganiserType;
					};
			  }) => Promise<void>);
	}

	let {
		onRemoveSelectedEntity,
		placeholder = 'Search entity',
		dataCy,
		exhibitionDetailsForm,
		selectedOption = $bindable(null),
		onChange = undefined,
	}: Props = $props();

	const handleChange = async (e: { detail: { value: OptionType } }) => {
		const entity = entities.find(
			(entity) => entity?.id && entity.id === e.detail.value.line4
		);
		if (onChange && entity) {
			onChange({
				detail: {
					value: entity as OrganiserType,
				},
			});
		}
	};

	let value = $state(writable(''));

	const getVariables = (value: string): EntitySearchQueryVariables => {
		return {
			input: {
				limit: 50,
				filters: {
					nameOrId: value,
					// ...(exhibitionDetailsForm?.venue_country && {
					// 	location: {
					// 		countryNameOrCode: exhibitionDetailsForm?.venue_country?.code,
					// 	},
					// }),
				},
			},
		};
	};

	const getOptions = (data: EntitySearchQuery | undefined) => {
		setTimeout(() => {
			entities = data?.entitySearch?.data || [];
		}, 0);

		return [...(data?.entitySearch?.data || []).map(formatEntityOrganiser)];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<QueryAutocomplete
		size="sm"
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="entity"
		dataCy={`${dataCy}-entity`}
		{placeholder}
		onRemoveSelectedOption={onRemoveSelectedEntity}
		emptyValueResponse={{ entitySearch: { data: [] } }}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientCustom}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line3: 'hidden',
				line4: 'hidden',
			},
			selectedOption: {
				button: 'max-w-full',
				line3: 'hidden',
				line4: 'hidden',
			},
		}}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		requestHeaders={getAuthorizationHeaders(data)}
		{getOptions}
		{getVariables}
		document={EntitySearchDocument}
		{value}
		bind:selectedOption
		onChange={handleChange}
	>
		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-exhibition-organisers-autocomplete`}
				>No results found.</NoResults
			>
		{/snippet}
	</QueryAutocomplete>

	{#if !!selectedOption}
		<button
			class="z-10"
			onclick={() => {
				selectedOption = null;
				onRemoveSelectedEntity();
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
