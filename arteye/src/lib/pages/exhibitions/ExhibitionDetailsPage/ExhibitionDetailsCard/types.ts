import type { ArtistType } from './ExhibitionArtists/ExhibitionArtistRow';
import type { OrganiserType } from './ExhibitionOrganisers/ExhibitionOrganisersRow';
import type { ExhibitionPageData } from '$routes/exhibitions/[id]/types';

export type ExhibitionDetailsForm = Partial<
	Pick<
		NonNullable<ExhibitionPageData['exhibition']>,
		| 'id'
		| 'title'
		| 'exhibition_url'
		| 'start_date'
		| 'end_date'
		| 'description'
		| 'attributes'
		| 'venue_country'
		| 'venue_city'
		| 'venue_address_1'
		| 'venue_address_2'
		| 'venue_address_3'
		| 'venue_post_code'
		| 'timezone'
	> & {
		organisers?: OrganiserType[];
		artists?: ArtistType[];
	}
>;
