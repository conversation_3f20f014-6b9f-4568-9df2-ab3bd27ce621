<script lang="ts">
	import { writable } from 'svelte/store';
	import type { ExhibitionDetailsForm } from '..';
	import { ExhibitionAddressesFieldName } from './types';
	import { page } from '$app/state';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableBody,
		TableCell,
		TableHeader,
		TableHeaderRow,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import {
		CityAutocomplete,
		formatLocation,
	} from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CityAutocomplete';
	import {
		CountrySelectedOption,
		formatCountry,
	} from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CountrySelectedOption';
	import type { ExhibitionPageData } from '$routes/exhibitions/[id]/types';

	interface Props {
		onChange: (form: ExhibitionDetailsForm) => void;
		exhibitionDetailsForm: ExhibitionDetailsForm;
		handleInputChange: (
			field: keyof ExhibitionDetailsForm
		) => (event: Event) => void;
	}

	let { onChange, exhibitionDetailsForm, handleInputChange }: Props = $props();

	let exhibition = $derived(
		page.data.exhibition as ExhibitionPageData['exhibition']
	);

	const dataCy = 'addresses';

	let cityValue = writable('');
	let countryValue = writable('');

	let selectedOption = $state(
		(() =>
			exhibition?.venue_city ? formatLocation(exhibition.venue_city) : null)()
	) as ReturnType<typeof formatLocation> | null;

	$effect(() => {
		selectedOption = exhibition?.venue_city
			? formatLocation(exhibition.venue_city)
			: null;
	});

	let countrySelectedOption = $state(
		(() =>
			exhibition?.venue_country
				? formatCountry(exhibition.venue_country)
				: null)()
	) as ReturnType<typeof formatCountry> | null;

	$effect(() => {
		countrySelectedOption = exhibition?.venue_country
			? formatCountry(exhibition.venue_country)
			: null;
	});

	const headers = [
		{
			fieldName: ExhibitionAddressesFieldName.Country,
			title: 'Country',
		},
		{
			fieldName: ExhibitionAddressesFieldName.City,
			title: 'Town/City',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line1,
			title: 'Street address 1',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line2,
			title: 'Street address 2',
		},
		{
			fieldName: ExhibitionAddressesFieldName.Line3,
			title: 'Street address 3',
		},
		{
			fieldName: ExhibitionAddressesFieldName.PostCode,
			title: 'Postcode/Zipcode',
		},
	];

	const handleClearCity = () => {
		onChange({
			...exhibitionDetailsForm,
			venue_city: null,
			venue_country: null,
		});

		countrySelectedOption = null;
	};

	const handleChangeCity = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const cityOption = e.detail.value;
		const country = cityOption.line4 ? JSON.parse(cityOption.line4) : null;

		onChange({
			...exhibitionDetailsForm,
			venue_city: {
				name: `${cityOption.line5}`,
				short_code: `${cityOption.line2}`,
				code: `${cityOption.line3}`,
			},
			...(cityOption.line4 && {
				venue_country: country,
			}),
		});

		countrySelectedOption = country ? formatCountry(country) : null;
		return Promise.resolve();
	};
</script>

<div>
	<Txt variant="label3" class="mb-2">Exhibition Address</Txt>

	<div class="max-lg:overflow-x-auto">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1200px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					<TableHeader {dataCy}>
						{header.title}
					</TableHeader>
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				<TableRow index={0} {dataCy}>
					{#each headers as header}
						{#if header.fieldName === ExhibitionAddressesFieldName.Country}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<CountrySelectedOption
										{dataCy}
										bind:selectedOption={countrySelectedOption}
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === ExhibitionAddressesFieldName.City}
							<TableCell {dataCy} class="">
								{#snippet custom()}
									<CityAutocomplete
										{dataCy}
										disabled={false}
										onRemoveSelectedOption={handleClearCity}
										bind:selectedOption
										onChange={handleChangeCity}
										value={cityValue}
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === ExhibitionAddressesFieldName.Line1}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-1`}
										name="line-1"
										placeholder="line 1"
										value={exhibitionDetailsForm.venue_address_1}
										onkeyup={handleInputChange('venue_address_1')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === ExhibitionAddressesFieldName.Line2}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-2`}
										name="line-2"
										placeholder="line 2"
										value={exhibitionDetailsForm.venue_address_2}
										onkeyup={handleInputChange('venue_address_2')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === ExhibitionAddressesFieldName.Line3}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-line-3`}
										name="line-3"
										placeholder="line 3"
										value={exhibitionDetailsForm.venue_address_3}
										onkeyup={handleInputChange('venue_address_3')}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{:else if header.fieldName === ExhibitionAddressesFieldName.PostCode}
							<TableCell {dataCy} class="py-0">
								{#snippet custom()}
									<Input
										dataCy={`${dataCy}-postcode`}
										name="postcode"
										placeholder="postcode"
										value={exhibitionDetailsForm.venue_post_code}
										onkeyup={handleInputChange(
											ExhibitionAddressesFieldName.PostCode
										)}
										size="sm"
									/>
								{/snippet}
							</TableCell>
						{/if}
					{/each}
				</TableRow>
			</TableBody>
		</table>
	</div>
</div>
