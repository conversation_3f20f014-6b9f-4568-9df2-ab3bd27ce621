import type { ExhibitionPageData } from '$routes/exhibitions/[id]/types';

export type CountryType = NonNullable<
	NonNullable<NonNullable<ExhibitionPageData['exhibition']>['venue_country']>
>;

export enum ExhibitionAddressesFieldName {
	Country = 'venue_country',
	City = 'venue_city',
	State = 'state',
	Line1 = 'line_1',
	Line2 = 'line_2',
	Line3 = 'line_3',
	PostCode = 'venue_post_code',
	Action = 'action',
}
