<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { ExhibitionDetailsCard } from './ExhibitionDetailsCard';
	import type { ExhibitionDetailsForm } from './ExhibitionDetailsCard';
	import { ExhibitionImage } from './ExhibitionImage';
	import type { ExhibitionImageType } from './ExhibitionImage';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Button } from '$global/components/Button';
	import { LinkButton } from '$global/components/LinkButton';
	import { showToast } from '$global/components/Toasts';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import type { Exhibition_Attribute } from '$gql/types';
	import { Status_Enum } from '$gql/types-custom';
	import { CreateUpdate } from '$lib/components/CreateUpdate';
	import { PageSaveBar } from '$lib/components/PageSaveBar';
	import { PARAM_NEW } from '$lib/constants/params';
	import { Routes } from '$lib/constants/routes';
	import { gqlClient } from '$lib/gqlClient';
	import { UpdateExhibitionItemDocument } from '$lib/mutations/__generated__/updateExhibition.generated';
	import { ArtworkAndActivitiesResultsTab } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/ArtworksResults';
	import { formatExhibitionImage } from '$lib/pages/exhibitions/ExhibitionDetailsPage/utils/exhibitionFormatters/exhibitionFormatters';
	import { getBackgroundContext } from '$lib/utils/getBackgroundContext/getBackgroundContext';
	import { getQueryStrings } from '$lib/utils/getQueryStrings/getQueryStrings';
	import { mutateExhibition } from '$lib/utils/mutation-handlers/mutateExhibition/mutateExhibition';
	import type { ExhibitionPageData } from '$routes/exhibitions/[id]/types';
	import { userRoutes } from '$lib/runes/userRoutes.svelte';

	const { updateExhibitionCoverImage } = getBackgroundContext();

	let data = $derived(getPageData<ExhibitionPageData>(page.data));
	let exhibition = $derived(
		data.exhibition as ExhibitionPageData['exhibition']
	);

	let exhibitionDetailsForm = $state(
		(() =>
			({
				id: page.data.exhibition?.id,
				title: page.data.exhibition?.title,
				exhibition_url: page.data.exhibition?.exhibition_url,
				start_date: dayjs(
					page.data.exhibition?.local_start_date.slice(0, -1)
				).format('YYYY-MM-DD'),
				end_date: dayjs(
					page.data.exhibition?.local_end_date.slice(0, -1)
				).format('YYYY-MM-DD'),
				description: page.data.exhibition?.description,
				attributes: page.data.exhibition?.attributes,
				venue_country: page.data.exhibition?.venue_country,
				venue_city: page.data.exhibition?.venue_city,
				venue_address_1: page.data.exhibition?.venue_address_1,
				venue_address_2: page.data.exhibition?.venue_address_2,
				venue_address_3: page.data.exhibition?.venue_address_3,
				venue_post_code: page.data.exhibition?.venue_post_code,
				artists:
					page.params.id === PARAM_NEW
						? [{ isNew: true }]
						: page.data.exhibition?.artists,
				organisers:
					page.params.id === PARAM_NEW
						? [{ isNew: true }]
						: page.data.exhibition?.organisers,
			}) as ExhibitionDetailsForm)()
	);

	let originalExhibitionArtists = $state(
		(() => page.data.exhibition?.artists || [])()
	);

	let originalExhibitionOrganisers = $state(
		(() => page.data.exhibition?.organisers || [])()
	);

	$effect(() => {
		exhibitionDetailsForm = {
			id: exhibition?.id,
			title: exhibition?.title,
			exhibition_url: exhibition?.exhibition_url,
			start_date: dayjs(
				page.data.exhibition?.local_start_date.slice(0, -1)
			).format('YYYY-MM-DD'),
			end_date: dayjs(page.data.exhibition?.local_end_date.slice(0, -1)).format(
				'YYYY-MM-DD'
			),
			description: exhibition?.description,
			attributes: exhibition?.attributes,
			venue_country: exhibition?.venue_country,
			venue_city: exhibition?.venue_city,
			venue_address_1: exhibition?.venue_address_1,
			venue_address_2: exhibition?.venue_address_2,
			venue_address_3: exhibition?.venue_address_3,
			venue_post_code: exhibition?.venue_post_code,
			artists: (page.params.id === PARAM_NEW
				? [{ isNew: true }]
				: exhibition?.artists) as ExhibitionDetailsForm['artists'],
			organisers: (page.params.id === PARAM_NEW
				? [{ isNew: true }]
				: exhibition?.organisers) as ExhibitionDetailsForm['organisers'],
		};

		originalExhibitionOrganisers = (exhibition?.organisers ||
			[]) as typeof originalExhibitionOrganisers;
		originalExhibitionArtists = (exhibition?.artists ||
			[]) as typeof originalExhibitionArtists;
	});

	let attributesToDelete = $state([]) as Exhibition_Attribute[];
	$effect(() => {
		attributesToDelete = [] as Exhibition_Attribute[];
	});

	let exhibitionImage = $state(
		(() => formatExhibitionImage(exhibition, page.data.user.access_token))()
	) as ReturnType<typeof formatExhibitionImage>;

	$effect(() => {
		exhibitionImage = formatExhibitionImage(
			exhibition,
			page.data.user.access_token
		);
	});

	let loading = $state(false);
	let showSaveBar = $state(false);

	let crumbs = $derived([
		{
			label: 'Search exhibitions',
			href: userRoutes.routes.exhibition,
		},
		{ label: `${exhibition?.title || 'New Exhibition'}` },
	]);

	let deleting = $state(false);

	const dataCy = 'exhibitions-details';

	const handleClickDeleteExhibition = async () => {
		try {
			deleting = true;
			await gqlClient.request(
				UpdateExhibitionItemDocument,
				{
					id: exhibition.id,
					data: { status: { key: Status_Enum.Archived } },
				},
				getAuthorizationHeaders(data)
			);

			showToast({
				variant: 'success',
				message: 'This exhibition has been successfully deleted.',
			});

			goto(userRoutes.routes.exhibition);
		} catch {
			showToast({
				variant: 'error',
				message: 'Something went wrong while deleting this exhibition.',
			});

			deleting = false;
		}
	};

	const handleExhibitionDetailsForm = (
		exhibitionDetails: ExhibitionDetailsForm
	) => {
		exhibitionDetailsForm = exhibitionDetails;
		showSaveBar = true;
	};

	const handleDeleteAttributes = (attributes: Exhibition_Attribute[]) => {
		attributesToDelete = attributes;
		showSaveBar = true;
	};

	const handleExhibitionImageChange = (image: ExhibitionImageType) => {
		exhibitionImage = image;
		showSaveBar = true;
	};

	const handleSaveClick = async () => {
		loading = true;
		try {
			const headers = getAuthorizationHeaders(data);

			const { exhibitionId } = await mutateExhibition({
				originalExhibitionArtists,
				originalExhibitionOrganisers,
				exhibitionDetailsForm,
				attributesToDelete,
				exhibitionImage,
				headers,
			});

			showToast({
				variant: 'success',
				message: 'Exhibition successfully saved.',
			});

			if (exhibitionId) {
				await updateExhibitionCoverImage(
					exhibitionId,
					exhibitionImage,
					headers
				);
			}

			if (exhibitionImage) {
				exhibitionImage.file = undefined;
			}

			if (!exhibitionDetailsForm.id) {
				(window.location as unknown as string) = `${Routes.Exhibitions}/${exhibitionId}`;
			}
		} catch {
			showToast({
				variant: 'error',
				message:
					'Something went wrong while saving this exhibition. Please contact the support team.',
			});
		} finally {
			showSaveBar = false;
			loading = false;
		}
	};
</script>

<div class={classNames({ 'pointer-events-none': loading })}>
	<div class="flex flex-col lg:flex-row gap-2 lg:items-center mb-4 lg:mb-0">
		<Breadcrumbs txtVariant="h5" {dataCy} {crumbs} class="ml-0 pl-0 sm:pl-0" />
		{#if exhibition}
			<div class="flex flex-col items-start lg:flex-row lg:items-center gap-2">
				<Button
					onclick={handleClickDeleteExhibition}
					dataCy={`${dataCy}-delete`}
					class="h-[2rem] w-[2rem] px-0"
					variant="secondary"
					disabled={loading || deleting}
					loading={deleting}
					size="xs"
				>
					<BinIcon class="h-3 w-3" />
				</Button>
				<LinkButton
					href={`${Routes.ArtworksAndActivities}?showResults=true&searchType=advanced&exhibitionName=${exhibition?.id}&resultsTab=${ArtworkAndActivitiesResultsTab.Artworks}`}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					VIEW ARTWORKS
				</LinkButton>
				<Button
					onclick={() => {
						navigator.clipboard.writeText(`${exhibition?.id}`);
					}}
					dataCy={`${dataCy}-copy-id`}
					class="h-[2rem]"
					variant="secondary"
					size="sm"
				>
					copy exhibition id
				</Button>
			</div>
		{/if}
	</div>

	{#if exhibition}
		<CreateUpdate class="mb-5 block mt-[-12px]" updateHistory={exhibition} />
	{/if}

	<ExhibitionDetailsCard
		{exhibitionDetailsForm}
		onDeleteAttributes={handleDeleteAttributes}
		onChange={handleExhibitionDetailsForm}
		class="mb-4"
	/>
	<ExhibitionImage
		{exhibitionImage}
		onExhibitionImageChange={handleExhibitionImageChange}
	/>
</div>

<PageSaveBar
	visible={showSaveBar}
	onSaveClick={handleSaveClick}
	{loading}
	disabled={loading ||
		!exhibitionDetailsForm?.title ||
		!(exhibitionDetailsForm?.organisers || []).every(
			(organiser: NonNullable<ExhibitionDetailsForm['organisers']>[number]) =>
				organiser.isDeleted || (!organiser.isDeleted && organiser.entity_id?.id)
		)}
/>
