import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Status_Enum } from '$gql/types-custom';
import { PARAM_NEW } from '$lib/constants/params';
import { gqlClient } from '$lib/gqlClient';
import { GetExhibitionsDocument } from '$lib/queries/__generated__/getExhibitions.generated';
import type { ExhibitionPageLoadEvent } from '$routes/exhibitions/[id]/types';

export const exhibitionPageLoad = async ({
	parent,
	params,
}: ExhibitionPageLoadEvent & {
	params: { id: string };
}) => {
	const data = await parent();

	const exhibitionId = params?.id;
	const authHeaders = getAuthorizationHeaders(data);

	const exhibitionsReq =
		exhibitionId === PARAM_NEW
			? {
					exhibition: [],
				}
			: await gqlClient.request(
					GetExhibitionsDocument,
					{
						filter: {
							_and: [
								{
									id: {
										_eq: exhibitionId,
									},
								},
								{ status: { key: { _neq: Status_Enum.Archived } } },
							],
						},
					},
					authHeaders
				);

	const [exhibitionsRes] = await Promise.all([exhibitionsReq]);

	const exhibition = exhibitionsRes?.exhibition[0];

	return {
		...data,
		exhibition,
	};
};
