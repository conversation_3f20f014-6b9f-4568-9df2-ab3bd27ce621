<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Container } from '$global/components/Container';
	import { Txt } from '$global/components/Txt';
	import { PageBody } from '$lib/components/PageBody';
	import { userRoutes, resetUserRoutes } from '$lib/runes/userRoutes.svelte';
	import { onMount } from 'svelte';

	const dataCyPrefix = 'home';

	onMount(() => {
		if (page.url.searchParams.get('invalidate') === 'true') {
			resetUserRoutes();

			fetch(`/api/clear-activity-cache`, {
				method: 'DELETE',
			});

			fetch('/api/clear-arteye-search', { method: 'DELETE' }).then(() => {
				goto(userRoutes.routes.basic_search, { replaceState: true });
			});
		} else {
			goto(userRoutes.routes.basic_search, { replaceState: true });
		}
	});
</script>

<svelte:head>
	<title>Arteye</title>
</svelte:head>

<PageBody>
	<Container dataCy={dataCyPrefix}>
		<Txt variant="h4" class="mb-6" />
	</Container>
</PageBody>
