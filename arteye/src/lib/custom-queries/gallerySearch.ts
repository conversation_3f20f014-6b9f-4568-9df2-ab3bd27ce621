import { gql } from 'graphql-tag';

export const QUERY = gql`
	query gallerySearch($input: GallerySearchInput!) {
		gallerySearch(input: $input) {
			data {
				id
				reference_id
				organisation {
					location {
						code
						country_nationality
						name
						short_code

						country {
							code
							country_nationality
							name
							short_code
						}
					}
					entity {
						name
						aggregations {
							activity_count
						}
					}
				}
			}
		}

		gallerySearchCount(input: $input)
	}
`;
