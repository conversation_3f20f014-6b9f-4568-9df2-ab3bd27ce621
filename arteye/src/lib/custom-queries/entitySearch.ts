import { gql } from 'graphql-tag';

export const QUERY = gql`
	query entitySearch($input: EntitySearchInput!) {
		entitySearch(input: $input) {
			data {
				id
				reference_id
				type {
					key
				}
				name
				person {
					id
					year_birth
					year_death
					type {
						person_type_key {
							key
							name
						}
					}
					nationalities {
						country {
							country_nationality
						}
					}
				}
				artist {
					id
				}
				gallery {
					id
				}
				organisation {
					id
					name
					type {
						organisation_type_key {
							key
							name
						}
					}
					location {
						code
						name
						short_code
						country {
							name
							code
							short_code
						}
						country_nationality
						type {
							key
						}
					}
				}
				addresses {
					city {
						name
					}
					country {
						name
					}
				}
			}
		}
	}
`;
