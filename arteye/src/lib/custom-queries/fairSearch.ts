import { gql } from 'graphql-tag';

export const QUERY = gql`
	query fairSearch($input: FairSearchInput!) {
		fairSearch(input: $input) {
			data {
				id
				title
				fair_organisation {
					organisation {
						name
					}
				}
				local_start_date
				local_end_date
				venue_country {
					code
					country_nationality
					name
					short_code
				}
				venue_city {
					code
					country_nationality
					name
					short_code
				}
				aggregations {
					exhibitor_count
				}
			}
		}

		fairSearchCount(input: $input)
	}
`;
