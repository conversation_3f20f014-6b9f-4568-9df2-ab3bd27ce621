import { gql } from 'graphql-tag';

export const QUERY = gql`
	query personSearch($input: PersonSearchInput!) {
		personSearch(input: $input) {
			data {
				id
				reference_id
				year_birth
				year_death
				entity {
					name
					aggregations {
						activity_count
					}
					attributes {
						type {
							description
							key
							name
						}
					}
				}
				nationalities {
					country {
						name
						country_nationality
					}
				}
				type {
					person_type_key {
						key
						name
					}
				}
				user_updated {
					role
					last_name
					id
					first_name
					email
				}
				user_created {
					role
					last_name
					id
					first_name
					email
				}
			}
		}

		personSearchCount(input: $input)
	}
`;
