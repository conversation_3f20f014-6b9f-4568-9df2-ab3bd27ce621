import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type ArtworkSearchQueryVariables = Types.Exact<{
	input: Types.ActivitySearchInput;
}>;

export type ArtworkSearchQuery = {
	__typename?: 'Query';
	artworkSearch: {
		__typename?: 'ArtworkSearchResponse';
		tokens: Array<{ __typename?: 'IdToken'; id: string; token: string } | null>;
		meta: {
			__typename?: 'Metadata';
			duration: number;
			elasticSearchDuration: number;
			nextToken?: string | null;
			previousToken?: string | null;
			totalCount: number;
		};
		artworks: Array<{
			__typename?: 'ArtworkAndSource';
			artwork: {
				__typename?: 'artwork';
				additional_info?: string | null;
				crid?: string | null;
				description?: string | null;
				dimensions_depth_cm?: number | null;
				dimensions_height_cm?: number | null;
				dimensions_width_cm?: number | null;
				execution_end_year?: number | null;
				execution_start_year?: number | null;
				id?: string | null;
				is_bundle?: boolean | null;
				is_full_set?: boolean | null;
				media?: string | null;
				number_of_artworks?: number | null;
				number_of_pieces?: number | null;
				title?: string | null;
				edition_info?: {
					__typename?: 'edition_info';
					edition_size_total?: number | null;
					artists_proof_size?: number | null;
					hors_de_commerce_size?: number | null;
				} | null;
				primary_image?: {
					__typename?: 'directus_files';
					width?: number | null;
					height?: number | null;
					id: string;
				} | null;
				artists?: Array<{
					__typename?: 'artwork_artist';
					artist_id?: {
						__typename?: 'artist';
						year_active_from?: number | null;
						year_active_to?: number | null;
						person?: {
							__typename?: 'person';
							first_name?: string | null;
							last_name?: string | null;
							year_birth?: number | null;
							year_death?: number | null;
							nationalities?: Array<{
								__typename?: 'person_nationality';
								country?: {
									__typename?: 'location';
									code?: string | null;
									country_nationality?: string | null;
									name?: string | null;
								} | null;
							} | null> | null;
						} | null;
					} | null;
				} | null> | null;
				activity?: Array<{
					__typename?: 'artwork_activity_artwork';
					artwork_activity?: {
						__typename?: 'artwork_activity';
						id?: string | null;
						source_page_url?: string | null;
						timestamp?: any | null;
						activity_status?: Array<{
							__typename?: 'artwork_activity_status';
							timestamp?: any | null;
							status?: {
								__typename?: 'status';
								key?: Types.Status_Enum | null;
								name?: string | null;
							} | null;
							type?: {
								__typename?: 'artwork_activity_status_type';
								key?: Types.Artwork_Activity_Status_Type_Enum | null;
								name?: string | null;
							} | null;
						} | null> | null;
						associations?: Array<{
							__typename?: 'artwork_activity_association';
							id?: string | null;
							entity?: {
								__typename?: 'entity';
								name?: string | null;
								id?: string | null;
								type?: {
									__typename?: 'entity_type';
									key?: Types.Entity_Type_Enum | null;
								} | null;
								person?: {
									__typename?: 'person';
									type?: Array<{
										__typename?: 'person_person_type';
										person_type_key?: {
											__typename?: 'person_type';
											name?: string | null;
										} | null;
									} | null> | null;
								} | null;
								organisation?: {
									__typename?: 'organisation';
									name?: string | null;
									location?: {
										__typename?: 'location';
										name?: string | null;
									} | null;
									type?: Array<{
										__typename?: 'organisation_organisation_type';
										organisation_type_key?: {
											__typename?: 'organisation_type';
											name?: string | null;
										} | null;
									} | null> | null;
								} | null;
							} | null;
							type?: {
								__typename?: 'artwork_activity_association_type';
								key?: Types.Artwork_Activity_Association_Type_Enum | null;
								name?: string | null;
							} | null;
						} | null> | null;
						type?: {
							__typename?: 'artwork_activity_type';
							key?: Types.Artwork_Activity_Type_Enum | null;
							name?: string | null;
						} | null;
						artwork_listing?: Array<{
							__typename?: 'artwork_listing';
							listing_type?: {
								__typename?: 'artwork_listing_type';
								key?: Types.Artwork_Listing_Type_Enum | null;
								name?: string | null;
							} | null;
							auction_lot?: {
								__typename?: 'auction_lot';
								sale_amount_includes_premium?: boolean | null;
								lot_number?: string | null;
								hammer_timestamp?: any | null;
								attributes?: Array<{
									__typename?: 'auction_lot_attribute';
									type?: {
										__typename?: 'auction_lot_attribute_type';
										description?: string | null;
										key?: Types.Auction_Lot_Attribute_Type_Enum | null;
										name?: string | null;
									} | null;
								} | null> | null;
								auction?: {
									__typename?: 'auction';
									auction_end_date?: any | null;
									auction_start_date?: any | null;
									sale_name?: string | null;
									sale_number?: string | null;
									sale_url?: string | null;
									auction_house?: {
										__typename?: 'auction_house';
										organisation?: {
											__typename?: 'organisation';
											name?: string | null;
											location?: {
												__typename?: 'location';
												name?: string | null;
											} | null;
										} | null;
									} | null;
									auction_types?: Array<{
										__typename?: 'auction_auction_type';
										auction_type_key?: {
											__typename?: 'auction_type';
											name?: string | null;
											key?: Types.Auction_Type_Enum | null;
										} | null;
									} | null> | null;
								} | null;
							} | null;
							exhibition_listing?: {
								__typename?: 'exhibition_artwork_listing';
								exhibition?: {
									__typename?: 'exhibition';
									title?: string | null;
									organisers?: Array<{
										__typename?: 'exhibition_organisers';
										entity_id?: {
											__typename?: 'entity';
											name?: string | null;
											organisation?: {
												__typename?: 'organisation';
												name?: string | null;
												location?: {
													__typename?: 'location';
													name?: string | null;
												} | null;
											} | null;
										} | null;
									} | null> | null;
								} | null;
							} | null;
							gallery_listing?: {
								__typename?: 'gallery_artwork_listing';
								gallery?: {
									__typename?: 'gallery';
									organisation?: {
										__typename?: 'organisation';
										name?: string | null;
										location?: {
											__typename?: 'location';
											name?: string | null;
										} | null;
									} | null;
								} | null;
							} | null;
							fair_listing?: {
								__typename?: 'fair_artwork_listing';
								fair_exhibitor?: {
									__typename?: 'fair_exhibitor';
									fair?: { __typename?: 'fair'; title?: string | null } | null;
									entity?: {
										__typename?: 'entity';
										name?: string | null;
										organisation?: {
											__typename?: 'organisation';
											location?: {
												__typename?: 'location';
												name?: string | null;
											} | null;
										} | null;
									} | null;
								} | null;
							} | null;
							known_price?: {
								__typename?: 'currency_amount';
								amount?: number | null;
								usd_amount?: number | null;
								currency?: {
									__typename?: 'currency';
									code?: string | null;
								} | null;
							} | null;
							price_high_estimate?: {
								__typename?: 'currency_amount';
								amount?: number | null;
								usd_amount?: number | null;
								currency?: {
									__typename?: 'currency';
									code?: string | null;
								} | null;
							} | null;
							price_low_estimate?: {
								__typename?: 'currency_amount';
								amount?: number | null;
								usd_amount?: number | null;
								currency?: {
									__typename?: 'currency';
									code?: string | null;
								} | null;
							} | null;
							sale_amount?: {
								__typename?: 'currency_amount';
								amount?: number | null;
								usd_amount?: number | null;
								currency?: {
									__typename?: 'currency';
									code?: string | null;
								} | null;
							} | null;
						} | null> | null;
					} | null;
				} | null> | null;
			};
		} | null>;
	};
};

export const ArtworkSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'artworkSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'ActivitySearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artworkSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'tokens' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'token' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'meta' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'duration' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'elasticSearchDuration' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nextToken' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'previousToken' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'totalCount' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artworks' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'edition_info' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'edition_size_total',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'artists_proof_size',
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'hors_de_commerce_size',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'primary_image' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'width' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'height' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'id' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'additional_info' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'crid' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'description' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_depth_cm',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_height_cm',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'dimensions_width_cm',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'execution_end_year',
															},
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'execution_start_year',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'is_bundle' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'is_full_set' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'media' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'number_of_artworks',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'number_of_pieces' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'artists' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'artist_id' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_active_from',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'year_active_to',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'person',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'first_name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'last_name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'year_birth',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'year_death',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'nationalities',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'country',
																											},
																											selectionSet: {
																												kind: 'SelectionSet',
																												selections: [
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'code',
																														},
																													},
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value:
																																'country_nationality',
																														},
																													},
																													{
																														kind: 'Field',
																														name: {
																															kind: 'Name',
																															value: 'name',
																														},
																													},
																												],
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'activity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'artwork_activity',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'FragmentSpread',
																					name: {
																						kind: 'Name',
																						value: 'ArtworkActivity',
																					},
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'activity_status',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'status',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'key',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'timestamp',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'type',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'key',
																											},
																										},
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivity' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'source_page_url' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'associations' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'entity' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'key' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'person_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork_listing' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'listing_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'auction_lot' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'sale_amount_includes_premium',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'lot_number' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'hammer_timestamp' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'attributes' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'description',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'auction' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction_end_date' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'auction_start_date',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_number' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'sale_url' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction_house' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'auction_types' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'auction_type_key',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'exhibition_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'title' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisers' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity_id' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'organisation',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'location',
																								},
																								selectionSet: {
																									kind: 'SelectionSet',
																									selections: [
																										{
																											kind: 'Field',
																											name: {
																												kind: 'Name',
																												value: 'name',
																											},
																										},
																									],
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'gallery_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'gallery' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'location' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'fair_listing' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fair_exhibitor' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'fair' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'title' },
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'organisation',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'location',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'known_price' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_high_estimate' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'price_low_estimate' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'sale_amount' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'amount' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'currency' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'usd_amount' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<ArtworkSearchQuery, ArtworkSearchQueryVariables>;
