import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type FairSearchQueryVariables = Types.Exact<{
	input: Types.FairSearchInput;
}>;

export type FairSearchQuery = {
	__typename?: 'Query';
	fairSearchCount: number;
	fairSearch: {
		__typename?: 'FairSearchResponse';
		data: Array<{
			__typename?: 'fair';
			id?: string | null;
			title?: string | null;
			local_start_date?: any | null;
			local_end_date?: any | null;
			fair_organisation?: {
				__typename?: 'fair_organisation';
				organisation?: {
					__typename?: 'organisation';
					name?: string | null;
				} | null;
			} | null;
			venue_country?: {
				__typename?: 'location';
				code?: string | null;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
			} | null;
			venue_city?: {
				__typename?: 'location';
				code?: string | null;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
			} | null;
			aggregations?: Array<{
				__typename?: 'fair_aggregation';
				exhibitor_count?: number | null;
			} | null> | null;
		}>;
	};
};

export const FairSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'fairSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'FairSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'fairSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fair_organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'organisation' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_city' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'aggregations' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'exhibitor_count' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'fairSearchCount' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<FairSearchQuery, FairSearchQueryVariables>;
