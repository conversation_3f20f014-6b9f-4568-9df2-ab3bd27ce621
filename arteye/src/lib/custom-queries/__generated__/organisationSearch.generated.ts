import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type OrganisationSearchQueryVariables = Types.Exact<{
	input: Types.OrganisationSearchInput;
}>;

export type OrganisationSearchQuery = {
	__typename?: 'Query';
	organisationSearchCount: number;
	organisationSearch: {
		__typename?: 'OrganisationSearchResponse';
		data: Array<{
			__typename?: 'organisation';
			reference_id?: number | null;
			name?: string | null;
			id?: string | null;
			type?: Array<{
				__typename?: 'organisation_organisation_type';
				organisation_type_key?: {
					__typename?: 'organisation_type';
					name?: string | null;
					key?: Types.Organisation_Type_Enum | null;
				} | null;
			} | null> | null;
			location?: {
				__typename?: 'location';
				code?: string | null;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
				country?: {
					__typename?: 'location';
					code?: string | null;
					country_nationality?: string | null;
					name?: string | null;
					short_code?: string | null;
				} | null;
			} | null;
			entity?: {
				__typename?: 'entity';
				name?: string | null;
				aggregations?: Array<{
					__typename?: 'entity_aggregation';
					activity_count?: number | null;
				} | null> | null;
			} | null;
		}>;
	};
};

export const OrganisationSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'organisationSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'OrganisationSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'organisationSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reference_id' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'organisation_type_key',
															},
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'aggregations' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'activity_count',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'organisationSearchCount' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	OrganisationSearchQuery,
	OrganisationSearchQueryVariables
>;
