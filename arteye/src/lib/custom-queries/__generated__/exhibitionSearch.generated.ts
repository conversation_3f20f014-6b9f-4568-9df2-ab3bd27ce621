import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type ExhibitionSearchQueryVariables = Types.Exact<{
	input: Types.ExhibitionSearchInput;
}>;

export type ExhibitionSearchQuery = {
	__typename?: 'Query';
	exhibitionSearchCount: number;
	exhibitionSearch: {
		__typename?: 'ExhibitionSearchResponse';
		data: Array<{
			__typename?: 'exhibition';
			title?: string | null;
			id?: string | null;
			local_start_date?: any | null;
			local_end_date?: any | null;
			exhibition_url?: string | null;
			venue_city?: {
				__typename?: 'location';
				code?: string | null;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
			} | null;
			venue_country?: {
				__typename?: 'location';
				code?: string | null;
				country_nationality?: string | null;
				name?: string | null;
				short_code?: string | null;
			} | null;
			aggregations?: Array<{
				__typename?: 'exhibition_aggregation';
				artwork_listing_count?: number | null;
			} | null> | null;
			organisers?: Array<{
				__typename?: 'exhibition_organisers';
				entity_id?: { __typename?: 'entity'; name?: string | null } | null;
			} | null> | null;
		}>;
	};
};

export const ExhibitionSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'exhibitionSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'ExhibitionSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'exhibitionSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_city' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'venue_country' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'code' },
														},
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'country_nationality',
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'short_code' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'aggregations' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: {
																kind: 'Name',
																value: 'artwork_listing_count',
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisers' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity_id' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_start_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'local_end_date' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'exhibition_url' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'exhibitionSearchCount' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	ExhibitionSearchQuery,
	ExhibitionSearchQueryVariables
>;
