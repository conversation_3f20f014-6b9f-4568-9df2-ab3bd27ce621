import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type UpdateAndReturnArtworkMutationVariables = Types.Exact<{
	artworks:
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type UpdateAndReturnArtworkMutation = {
	__typename?: 'Mutation';
	updateAndReturnArtwork: Array<{ __typename?: 'artwork'; id?: string | null }>;
};

export const UpdateAndReturnArtworkDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateAndReturnArtwork' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'artworks' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'String' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'updateAndReturnArtwork' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'artworks' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'artworks' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateAndReturnArtworkMutation,
	UpdateAndReturnArtworkMutationVariables
>;
