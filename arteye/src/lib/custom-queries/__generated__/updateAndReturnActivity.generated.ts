import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type UpdateAndReturnActivityMutationVariables = Types.Exact<{
	activityIds:
		| Array<Types.InputMaybe<Types.Scalars['String']['input']>>
		| Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type UpdateAndReturnActivityMutation = {
	__typename?: 'Mutation';
	updateAndReturnActivity: Array<{
		__typename?: 'artwork_activity';
		id?: string | null;
	}>;
};

export const UpdateAndReturnActivityDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'updateAndReturnActivity' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'activityIds' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'ListType',
							type: {
								kind: 'NamedType',
								name: { kind: 'Name', value: 'String' },
							},
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'updateAndReturnActivity' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'activityIds' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'activityIds' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	UpdateAndReturnActivityMutation,
	UpdateAndReturnActivityMutationVariables
>;
