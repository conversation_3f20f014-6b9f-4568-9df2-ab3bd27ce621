import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type TransferActivitiesMutationVariables = Types.Exact<{
	fromArtworkId: Types.Scalars['String']['input'];
	toArtworkId: Types.Scalars['String']['input'];
}>;

export type TransferActivitiesMutation = {
	__typename?: 'Mutation';
	transferActivities: {
		__typename?: 'ActivityTransferResponse';
		activitiesTransferred: Array<{
			__typename?: 'ActivityTransfer';
			activity: string;
			from_artwork: string;
			to_artwork: string;
		}>;
	};
};

export const TransferActivitiesDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'transferActivities' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'fromArtworkId' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'toArtworkId' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'transferActivities' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'fromArtworkId' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'fromArtworkId' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'toArtworkId' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'toArtworkId' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activitiesTransferred' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'activity' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'to_artwork' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	TransferActivitiesMutation,
	TransferActivitiesMutationVariables
>;
