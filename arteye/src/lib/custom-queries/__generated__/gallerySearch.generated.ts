import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type GallerySearchQueryVariables = Types.Exact<{
	input: Types.GallerySearchInput;
}>;

export type GallerySearchQuery = {
	__typename?: 'Query';
	gallerySearchCount: number;
	gallerySearch: {
		__typename?: 'GallerySearchResponse';
		data: Array<{
			__typename?: 'gallery';
			id?: string | null;
			reference_id?: number | null;
			organisation?: {
				__typename?: 'organisation';
				location?: {
					__typename?: 'location';
					code?: string | null;
					country_nationality?: string | null;
					name?: string | null;
					short_code?: string | null;
					country?: {
						__typename?: 'location';
						code?: string | null;
						country_nationality?: string | null;
						name?: string | null;
						short_code?: string | null;
					} | null;
				} | null;
				entity?: {
					__typename?: 'entity';
					name?: string | null;
					aggregations?: Array<{
						__typename?: 'entity_aggregation';
						activity_count?: number | null;
					} | null> | null;
				} | null;
			} | null;
		}>;
	};
};

export const GallerySearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'gallerySearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'GallerySearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'gallerySearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reference_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'organisation' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'location' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'code' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'short_code' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'country' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'code' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country_nationality',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'short_code',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'entity' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'aggregations',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'activity_count',
																					},
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'gallerySearchCount' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GallerySearchQuery, GallerySearchQueryVariables>;
