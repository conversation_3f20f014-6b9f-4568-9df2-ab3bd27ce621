import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type PersonSearchQueryVariables = Types.Exact<{
	input: Types.PersonSearchInput;
}>;

export type PersonSearchQuery = {
	__typename?: 'Query';
	personSearchCount: number;
	personSearch: {
		__typename?: 'PersonSearchResponse';
		data: Array<{
			__typename?: 'person';
			id?: string | null;
			reference_id?: number | null;
			year_birth?: number | null;
			year_death?: number | null;
			entity?: {
				__typename?: 'entity';
				name?: string | null;
				aggregations?: Array<{
					__typename?: 'entity_aggregation';
					activity_count?: number | null;
				} | null> | null;
				attributes?: Array<{
					__typename?: 'entity_attribute';
					type?: {
						__typename?: 'entity_attribute_type';
						description?: string | null;
						key?: Types.Entity_Attribute_Type_Enum | null;
						name?: string | null;
					} | null;
				} | null> | null;
			} | null;
			nationalities?: Array<{
				__typename?: 'person_nationality';
				country?: {
					__typename?: 'location';
					name?: string | null;
					country_nationality?: string | null;
				} | null;
			} | null> | null;
			type?: Array<{
				__typename?: 'person_person_type';
				person_type_key?: {
					__typename?: 'person_type';
					key?: Types.Person_Type_Enum | null;
					name?: string | null;
				} | null;
			} | null> | null;
			user_updated?: {
				__typename?: 'directus_users';
				role?: string | null;
				last_name?: string | null;
				id: string;
				first_name?: string | null;
				email?: string | null;
			} | null;
			user_created?: {
				__typename?: 'directus_users';
				role?: string | null;
				last_name?: string | null;
				id: string;
				first_name?: string | null;
				email?: string | null;
			} | null;
		} | null>;
	};
};

export const PersonSearchDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'personSearch' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'PersonSearchInput' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'personSearch' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'reference_id' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_birth' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'year_death' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'aggregations' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'activity_count',
																		},
																	},
																],
															},
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'attributes' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'type' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'description',
																					},
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'key' },
																				},
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'nationalities' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'country' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'country_nationality',
																		},
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'type' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person_type_key' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'name' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_updated' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'role' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'email' },
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'user_created' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'role' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'email' },
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'personSearchCount' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
					},
				],
			},
		},
	],
} as unknown as DocumentNode<PersonSearchQuery, PersonSearchQueryVariables>;
