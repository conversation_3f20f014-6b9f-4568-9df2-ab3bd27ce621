import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type GetDatasourceQueryVariables = Types.Exact<{
	collection: Types.Scalars['String']['input'];
	id: Types.Scalars['String']['input'];
}>;

export type GetDatasourceQuery = {
	__typename?: 'Query';
	getDatasource?: {
		__typename?: 'DatasourceResponse';
		id: string;
		data_source?: string | null;
		data_source_id?: string | null;
		ingestion_source?: string | null;
		ingestion_source_id?: string | null;
		type: Types.Pipeline_Source_Type_Enum;
	} | null;
};

export const GetDatasourceDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getDatasource' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'collection' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'getDatasource' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'collection' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'collection' },
								},
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'id' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'data_source' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data_source_id' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_source' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'ingestion_source_id' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'type' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetDatasourceQuery, GetDatasourceQueryVariables>;
