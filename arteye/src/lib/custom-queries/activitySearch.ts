import { gql } from 'graphql-tag';

export const ArtworkActivityFragment = gql`
	fragment ArtworkActivity on artwork_activity {
		id
		source_page_url
		timestamp
		associations {
			id
			entity {
				name
				id
				type {
					key
				}
				person {
					type {
						person_type_key {
							name
						}
					}
				}
				organisation {
					name
					location {
						name
					}
					type {
						organisation_type_key {
							name
						}
					}
				}
			}
			type {
				key
				name
			}
		}
		type {
			key
			name
		}
		artwork_listing {
			listing_type {
				key
				name
			}

			auction_lot {
				sale_amount_includes_premium
				lot_number
				hammer_timestamp
				attributes {
					type {
						description
						key
						name
					}
				}

				auction {
					auction_end_date
					auction_start_date
					sale_name
					sale_number
					sale_url
					auction_house {
						organisation {
							name
							location {
								name
							}
						}
					}
					auction_types {
						auction_type_key {
							name
							key
						}
					}
				}
			}

			exhibition_listing {
				exhibition {
					title
					organisers {
						entity_id {
							name
							organisation {
								name
								location {
									name
								}
							}
						}
					}
				}
			}

			gallery_listing {
				gallery {
					organisation {
						name
						location {
							name
						}
					}
				}
			}

			fair_listing {
				fair_exhibitor {
					fair {
						title
					}
					entity {
						name
						organisation {
							location {
								name
							}
						}
					}
				}
			}

			known_price {
				amount
				usd_amount
				currency {
					code
				}
			}

			price_high_estimate {
				amount
				usd_amount
				currency {
					code
				}
			}

			price_low_estimate {
				amount
				currency {
					code
				}
				usd_amount
			}

			sale_amount {
				amount
				currency {
					code
				}
				usd_amount
			}
		}
	}
`;

export const ACTIVITY_SEARCH_ID_ONLY = gql`
	query activitySearchIdOnly($input: ActivitySearchInput!) {
		activitySearch(input: $input) {
			activities {
				artwork_activity {
					id
				}
			}
			tokens {
				id
				token
			}
		}
	}
`;

export const ACTIVITY_SEARCH_BY_ID = gql`
	query activitySearchById($input: ActivitySearchInput!) {
		activitySearch(input: $input) {
			activities {
				artwork_activity {
					id
					date_updated
					date_created
					user_updated {
						first_name
						last_name
					}
					user_created {
						first_name
						last_name
					}
					timestamp
					artwork_listing {
						id

						date_updated
						date_created
						user_updated {
							first_name
							last_name
						}
						user_created {
							first_name
							last_name
						}

						exhibition_listing {
							id
							exhibition {
								id
								title

								venue_country {
									name
								}
								venue_city {
									name
								}
							}
						}
						gallery_listing {
							id
							gallery {
								id
								organisation {
									name
									location {
										name
									}
								}
							}
						}
						fair_listing {
							id
							fair_exhibitor {
								id
								entity {
									id
									name
									organisation {
										location {
											name
											country {
												name
											}
										}
									}
								}
								fair {
									id
									title

									venue_country {
										name
									}
									venue_city {
										name
									}
								}
							}
						}
						listing_type {
							name
							key
						}
						known_price {
							id
							usd_amount
							amount
							currency {
								full_name
								symbol
								name
								code
							}
						}
						sale_amount {
							id
							usd_amount
							amount
							currency {
								full_name
								symbol
								name
								code
							}
						}
						price_high_estimate {
							id
							usd_amount
							amount
							currency {
								full_name
								symbol
								name
								code
							}
						}
						price_low_estimate {
							id
							usd_amount
							amount
							currency {
								full_name
								symbol
								name
								code
							}
						}
						shipping
					}

					status {
						key
						name
					}

					type {
						key
						name
					}
				}
				pipeline_source {
					data_source
				}
			}
		}
	}
`;

export const ACTIVITY_SEARCH = gql`
	query activitySearch($input: ActivitySearchInput!) {
		activitySearch(input: $input) {
			tokens {
				id
				token
			}

			meta {
				duration
				elasticSearchDuration
				nextToken
				previousToken
				totalCount
			}

			activities {
				artwork_activity {
					...ArtworkActivity
					activity_status {
						status {
							key
							name
						}
						timestamp
						type {
							key
							name
						}
						artwork_activity {
							...ArtworkActivity
							activity_status {
								status {
									key
									name
								}
								timestamp
								type {
									key
									name
								}
							}
						}
					}
					artworks {
						edition_number
						edition_number_legacy
						edition_number_type {
							key
							name
						}
						artwork {
							edition_info {
								edition_size_total
								artists_proof_size
								hors_de_commerce_size
							}
							primary_image {
								width
								height
								id
							}
							additional_info
							crid
							description
							dimensions_depth_cm
							dimensions_height_cm
							dimensions_width_cm
							execution_end_year
							execution_start_year
							id
							is_bundle
							is_full_set
							media
							number_of_artworks
							number_of_pieces
							title
							artists {
								artist_id {
									year_active_from
									year_active_to
									person {
										first_name
										last_name
										year_birth
										year_death
										nationalities {
											country {
												code
												country_nationality
												name
											}
										}
									}
								}
							}
						}
					}

					activity_artwork_info {
						primary_image {
							width
							height
							id
						}
					}
				}
			}
		}
	}
	${ArtworkActivityFragment}
`;
