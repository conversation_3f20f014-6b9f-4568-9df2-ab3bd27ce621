import { gql } from 'graphql-tag';

export const QUERY = gql`
	query exhibitionSearch($input: ExhibitionSearchInput!) {
		exhibitionSearch(input: $input) {
			data {
				title
				id
				venue_city {
					code
					country_nationality
					name
					short_code
				}
				venue_country {
					code
					country_nationality
					name
					short_code
				}
				aggregations {
					artwork_listing_count
				}
				organisers {
					entity_id {
						name
					}
				}
				local_start_date
				local_end_date
				exhibition_url
			}
		}

		exhibitionSearchCount(input: $input)
	}
`;
