import { gql } from 'graphql-tag';

export const QUERY = gql`
	query auctionSearch($input: AuctionSearchInput!) {
		auctionSearch(input: $input) {
			data {
				id
				sale_name
				local_auction_start_date
				local_auction_end_date
				auction_house {
					organisation {
						name
					}
				}
				sale_number
				currency {
					name
					code
					symbol
				}
				auction_types {
					auction_type_key {
						name
						key
					}
				}
			}
		}
	}
`;
