import { gql } from 'graphql-tag';
import { ArtworkActivityFragment } from './activitySearch';

export const QUERY = gql`
	query artworkSearch($input: ActivitySearchInput!) {
		artworkSearch(input: $input) {
			tokens {
				id
				token
			}

			meta {
				duration
				elasticSearchDuration
				nextToken
				previousToken
				totalCount
			}

			artworks {
				artwork {
					edition_info {
						edition_size_total
						artists_proof_size
						hors_de_commerce_size
					}
					primary_image {
						width
						height
						id
					}
					additional_info
					crid
					description
					dimensions_depth_cm
					dimensions_height_cm
					dimensions_width_cm
					execution_end_year
					execution_start_year
					id
					is_bundle
					is_full_set
					media
					number_of_artworks
					number_of_pieces
					title
					artists {
						artist_id {
							year_active_from
							year_active_to
							person {
								first_name
								last_name
								year_birth
								year_death
								nationalities {
									country {
										code
										country_nationality
										name
									}
								}
							}
						}
					}
					activity {
						artwork_activity {
							...ArtworkActivity
							activity_status {
								status {
									key
									name
								}
								timestamp
								type {
									key
									name
								}
							}
						}
					}
				}
			}
		}
	}
	${ArtworkActivityFragment}
`;
