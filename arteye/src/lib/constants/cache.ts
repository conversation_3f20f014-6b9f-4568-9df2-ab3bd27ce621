export enum CacheEntries {
	Countries = 'countries',
	PersonTypes = 'person-types',
	AttributeTypes = 'attribute-types',
	EditionsNumberTypes = 'edition-number-types',
	ArtworkListingTypes = 'artwork-listing-types',
	ArtworkActivityTypes = 'artwork-activity-types',
	ArtworkActivityStatusTypes = 'artwork-activity-status-types',
	Currencies = 'currencies',
	ArtworkActivityAssociationTypes = 'artwork-activity-association-types',
	DataSources = 'data-sources',
	ArtworkTypes = 'artwork-types',
	AuctionTypes = 'auction-types',
	CategoryTags = 'category-types',
	SubjectTags = 'subject-tags',
	ArtworkDimensions = 'artwork-dimensions',
	HeniArtworkTypes = 'heni-artwork-types',
	OrganisationTypes = 'organisation-types',
	Genders = 'genders',
	EntityContactDetailTypes = 'entity-contact-detail-types',
	EntityNoteTypes = 'entity-note-types',
	RelationshipTypes = 'relationship-types',
	EntityCollectionNoteTypes = 'entity-collection-note-types',
	Timezones = 'timezones',
	ExhibitionAttributes = 'exhibition-attributes',
}
