import { ValueFilterOperator } from '$gql/types-custom';

export enum SearchRange {
	EqualTo = 'equal',
	Between = 'between',
	GreaterThan = 'greater_than',
	LessThan = 'less_than',
}

export const SEARCH_RANGE_OPTIONS = [
	{ label: 'Equal to', value: SearchRange.EqualTo },
	{ label: 'Between', value: SearchRange.Between },
	{ label: 'Greater than', value: SearchRange.GreaterThan },
	{ label: 'Less than', value: SearchRange.LessThan },
];

export const NEW_SEARCH_RANGE_OPTIONS = [
	{ label: 'Equal to', value: ValueFilterOperator.Equal },
	{ label: 'Between', value: ValueFilterOperator.Between },
	{ label: 'Greater than', value: ValueFilterOperator.GreaterThan },
	{ label: 'Less than', value: ValueFilterOperator.LessThan },
];

export const NEW_SEARCH_RANGE_PLACEHOLDERS: Record<string, string> = {
	[ValueFilterOperator.Equal]: 'YYYY',
	[ValueFilterOperator.Between]: 'YYYY-YYYY',
	[ValueFilterOperator.GreaterThan]: 'YYYY',
	[ValueFilterOperator.LessThan]: 'YYYY',
};

export const SEARCH_RANGE_PLACEHOLDERS: Record<string, string> = {
	[SearchRange.EqualTo]: 'YYYY',
	[SearchRange.Between]: 'YYYY-YYYY',
	[SearchRange.GreaterThan]: 'YYYY',
	[SearchRange.LessThan]: 'YYYY',
};

export const SEARCH_RANGE_PLACEHOLDERS_FULL: Record<string, string> = {
	[ValueFilterOperator.Equal]: 'DD/MM/YYYY',
	[ValueFilterOperator.Between]: 'DD/MM/YYYY-DD/MM/YYYY',
	[ValueFilterOperator.GreaterThan]: 'DD/MM/YYYY',
	[ValueFilterOperator.LessThan]: 'DD/MM/YYYY',
};
