import { BasicDevConfig } from './basic-dev-config';
import { PUBLIC_APP_ENV } from '$env/static/public';

export const env = PUBLIC_APP_ENV || 'dev';

interface AppConfig {
	ClientGraphqlApiDomain: string;
	GraphqlApiDomain: string;
	GraphqlApiUrl: string;
	TypeGenerationApiKey: string;
	SystemGraphqlApiUrl: string;
	Domain: string;
	DirectusUrl: string;
	CustomGraphqlApiUrl: string;
}

export const ProjectName = 'arteye';
export const AssetsUrl = 'https://resources.heni.com';

export const DevConfig: AppConfig = Object.freeze({
	...BasicDevConfig,
	GraphqlApiDomain:
		PUBLIC_APP_ENV === 'dev'
			? 'http://localhost:3006'
			: BasicDevConfig.GraphqlApiDomain,
	ClientGraphqlApiDomain: BasicDevConfig.GraphqlApiDomain,
	Domain:
		PUBLIC_APP_ENV === 'dev'
			? 'https://feather-dome.no-zero.net'
			: 'http://localhost:5173',
	DirectusUrl: 'https://fragile-star.no-zero.net',
});

// TypeGenerationApiKey not necessary for staging as the key is only used to generate the typed documents
export const StagingConfig: AppConfig = Object.freeze({
	CustomGraphqlApiUrl: 'https://baby-bratwurst.no-zero.net/custom-graphql',
	// GraphqlApiDomain: 'https://baby-bratwurst.no-zero.net',
	GraphqlApiDomain: 'http://0.0.0.0:4005',
	ClientGraphqlApiDomain: 'https://baby-bratwurst.no-zero.net',
	TypeGenerationApiKey: '',
	GraphqlApiUrl: 'https://baby-bratwurst.no-zero.net/graphql',
	SystemGraphqlApiUrl: 'https://baby-bratwurst.no-zero.net/graphql/system',
	Domain: 'https://sandy-monkey.no-zero.net',
	DirectusUrl: 'https://baby-bratwurst.no-zero.net',
});

// TypeGenerationApiKey not necessary for production as the key is only used to generate the typed documents
export const ProductionConfig: AppConfig = Object.freeze({
	CustomGraphqlApiUrl: 'https://marginal-mist.no-zero.net/custom-graphql',
	GraphqlApiDomain: 'https://marginal-mist.no-zero.net',
	// GraphqlApiDomain: 'http://0.0.0.0:3001',
	ClientGraphqlApiDomain: 'https://marginal-mist.no-zero.net',
	TypeGenerationApiKey: '',
	GraphqlApiUrl: 'https://marginal-mist.no-zero.net/graphql',
	SystemGraphqlApiUrl: 'https://marginal-mist.no-zero.net/graphql/system',
	Domain: 'https://sharp-monster.no-zero.net',
	DirectusUrl: 'https://marginal-mist.no-zero.net',
});

export const Config = (() => {
	switch (env) {
		case 'production':
			return ProductionConfig;
		case 'staging':
			return StagingConfig;
		default:
			return DevConfig;
	}
})();
