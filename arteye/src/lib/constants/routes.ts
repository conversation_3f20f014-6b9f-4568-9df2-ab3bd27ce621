export enum Routes {
	Home = '/',
	Reports = '/reports',
	ReportsCreate = '/reports/create',
	ArtworksAndActivities = '/artworks-and-activities',
	ArtworkDetails = '/artworks-and-activities/artwork',
	ActivityDetails = '/artworks-and-activities/activity',
	Artists = '/artists',
	People = '/people',
	Galleries = '/galleries',
	Organisations = '/organisations',
	Auctions = '/auctions',
	Fairs = '/fairs',
	Exhibitions = '/exhibitions',
	AuctionGroupings = '/auction-groupings',
	AddNewRepresentation = '/add-new-representation',
	Series = '/series',
}

export enum DirectusRoutes {
	Artwork = '/admin/content/artwork',
}
