<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { ExternalIcon } from '$global/assets/icons/ExternalIcon';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dataCy: string;
		url: string;
		startText?: string;
		ctaText?: string;
		endText?: string;
		class?: string;
	}

	let {
		dataCy,
		url,
		startText = '',
		ctaText = '',
		endText = '',
		...rest
	}: Props = $props();
	let dataCyPrefix = $derived(`${dataCy}-create-new-option`);
</script>

<div
	class={twMerge(
		'flex items-center gap-1 border-t border-gray-200 px-2 py-1',
		rest.class
	)}
>
	{#if startText}
		<Txt variant="body3" class="text-gray-500">
			{startText}
		</Txt>
	{/if}

	{#if ctaText}
		<Txt
			dataCy={dataCyPrefix}
			variant="body3"
			component="a"
			href={url}
			rel="noopener noreferrer"
			target="_blank"
			class="flex items-center gap-1 text-blue-500"
		>
			{ctaText}
			<ExternalIcon class="h-[16px] w-[16px]" color="blue-500" />
		</Txt>
	{/if}

	{#if endText}
		<Txt variant="body3" class="text-gray-500">
			{endText}
		</Txt>
	{/if}
</div>
