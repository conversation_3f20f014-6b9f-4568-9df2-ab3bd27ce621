<script lang="ts">
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import {
		type Option,
		type OptionClasses,
	} from '$global/components/QueryAutocomplete/types';

	interface Props {
		onRemove?: (() => void) | undefined;
		dataCy: string;
		option: Option;
		classes?: OptionClasses;
	}

	let { onRemove = undefined, dataCy, option, classes = {} }: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-selected-option`);
</script>

<div
	data-cy={dataCyPrefix}
	class="flex h-[32px] items-center justify-between gap-2 rounded border border-gray-200 pl-2"
>
	<LinkOption dataCy={dataCyPrefix} {option} {classes} />

	{#if onRemove}
		<Button
			variant="secondary"
			class="max-w-[5.25rem] !border-0"
			size="xs"
			dataCy={`${dataCyPrefix}-remove`}
			onclick={onRemove}
		>
			{#snippet leading()}
				<CrossIcon class="mr-1 h-4 w-4" />
			{/snippet}
		</Button>
	{/if}
</div>
