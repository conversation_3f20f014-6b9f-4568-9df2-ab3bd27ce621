<script lang="ts">
	import { Txt } from '$global/components/Txt';

	interface Props {
		title: string;
		value: string;
	}

	let { title, value }: Props = $props();
</script>

<div>
	<Txt variant="label3" class="text-[0.75rem] leading-[1rem] text-gray-500">
		<span class="text-nowrap">{title}:</span>
	</Txt>
	<Txt variant="body3" class="text-[0.75rem] leading-[1rem] text-gray-500">
		<span class="text-nowrap">{value}</span>
	</Txt>
</div>
