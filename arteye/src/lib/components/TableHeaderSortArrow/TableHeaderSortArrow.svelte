<script lang="ts">
	import classNames from 'classnames';
	import { goto } from '$app/navigation';
	import { ChevronDownIcon } from '$global/assets/icons/ChevronDownIcon';
	import { ChevronUpIcon } from '$global/assets/icons/ChevronUpIcon';
	import { Txt } from '$global/components/Txt';

	interface Props {
		searchParams: URLSearchParams;
		sortParamKey: string;
		asc: string;
		desc: string;
		children?: import('svelte').Snippet;
	}

	let { sortParamKey, asc, desc, children, searchParams }: Props = $props();

	let value = $derived(searchParams.get(sortParamKey));

	let isActive = $derived(value === asc || value === desc);

	let handleClick = $derived(() => {
		const sortValue = encodeURIComponent(value === asc ? desc : asc);

		searchParams.set(sortParamKey, sortValue);

		const params = searchParams.toString();

		goto(`?${params}`, { noScroll: true, invalidateAll: true });
	});
</script>

<button onclick={handleClick} class="flex items-center gap-2">
	<Txt
		variant="label3"
		class={classNames('whitespace-nowrap', {
			'font-bold': isActive,
		})}>{@render children?.()}</Txt
	>

	{#if value === asc}
		<ChevronUpIcon
			class="h-[16px] w-[16px]"
			color={isActive ? 'gray-900' : 'gray-400'}
		/>
	{:else}
		<ChevronDownIcon
			class="h-[16px] w-[16px]"
			color={isActive ? 'gray-900' : 'gray-400'}
		/>
	{/if}
</button>
