<script lang="ts">
	import { Button } from '$global/components/Button';

	interface Props {
		visible?: boolean;
		loading: boolean;
		disabled: boolean;
		onSaveClick: () => void;
	}

	let { visible = false, loading, disabled, onSaveClick }: Props = $props();

	const dataCy = 'page-save-bar';
</script>

{#if visible}
	<div
		class="fixed bottom-0 left-0 right-0 z-50 justify-end border-t border-gray-200 bg-white px-8 py-4 hidden md:flex"
	>
		<Button
			{loading}
			{disabled}
			dataCy={`${dataCy}-save-changes`}
			size="md"
			onclick={onSaveClick}
		>
			Save Changes
		</Button>
	</div>
{/if}
