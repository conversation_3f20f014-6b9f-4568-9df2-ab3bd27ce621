<script lang="ts">
	import { setContext } from 'svelte';
	import { Contexts } from '$lib/constants/constants';
	import { createBackgroundContext } from '$lib/utils/createBackgroundContext/createBackgroundContext';

	setContext(Contexts.Background, createBackgroundContext());

	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();
</script>

{@render children?.()}
