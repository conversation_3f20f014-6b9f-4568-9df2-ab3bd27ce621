<script lang="ts">
	import debounce from 'just-debounce-it';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import { showToast } from '$global/components/Toasts';
	import { Status_Enum, type Tag_Type_Enum } from '$gql/types-custom';
	import { gqlClient } from '$lib/gqlClient';
	import { CreateTagItemDocument } from '$lib/queries/__generated__/createTagItem.generated';
	import { GetTagsDocument } from '$lib/queries/__generated__/getTags.generated';

	interface Props {
		dataCy: string;
		selected: MultiSelectOption[];
		onChange: (selected: MultiSelectOption[]) => void;
		accessToken: string;
		typeKey: Tag_Type_Enum;
	}

	let { dataCy, accessToken, typeKey, onChange, selected }: Props = $props();

	const TAGS_MAP: Record<
		Tag_Type_Enum,
		{ name: string; placeholder: string; inputName: string }
	> = {
		CATEGORY: {
			name: 'Category',
			placeholder: 'Search for category tags',
			inputName: 'categoryTags',
		},
		SUBJECT: {
			name: 'Subject',
			placeholder: 'Search for subject tags',
			inputName: 'subjectTags',
		},
	};

	const dataCyPrefix = `${dataCy}-tags`;

	const INITIAL_OPTIONS: MultiSelectOption[] = [];

	let options: MultiSelectOption[] = $state([]);

	let loading = $state(false);

	const handleKeyUp = debounce(async (event: KeyboardEvent) => {
		const inputValue = (event.target as unknown as { value: string })?.value;

		if (!inputValue) {
			options = INITIAL_OPTIONS;
			return;
		}

		try {
			loading = true;

			const authorization = `Bearer ${accessToken}`;

			const res = await gqlClient.request(
				GetTagsDocument,
				{
					filter: {
						tag: { _istarts_with: inputValue },
						type: { key: { _eq: typeKey } },
						status: { key: { _eq: Status_Enum.Published } },
					},
					sort: 'tag',
				},
				{
					authorization,
				}
			);

			options = res.tag
				.map<MultiSelectOption>((tag) => ({
					value: tag.tag,
					label: tag.tag,
				}))
				.filter(
					(option) =>
						!selected.find(
							(selectedOption) => selectedOption.value === option.value
						)
				);

			if (!options.length) {
				options = [
					{
						value: inputValue,
						label: inputValue,
						createNew: true,
					},
				];
			}
		} catch {
			//
		} finally {
			loading = false;
		}
	}, 500);

	const handleNewTag = async (tag: string) => {
		try {
			loading = true;
			const authorization = `Bearer ${accessToken}`;

			options = INITIAL_OPTIONS;

			await new Promise((resolve) => setTimeout(resolve, 0));

			await gqlClient.request(
				CreateTagItemDocument,
				{
					data: {
						tag,
						type: {
							key: typeKey,
							name: TAGS_MAP[typeKey].name,
						},
					},
				},
				{
					authorization,
				}
			);

			onChange([
				...selected,
				{
					value: tag,
					label: tag,
				},
			]);
		} catch (error) {
			onChange([...selected.filter((option) => option.value !== tag)]);

			const notUniqueError = (
				error as unknown as {
					response: {
						errors: {
							message: string;
						}[];
					};
				}
			).response.errors.find((error) => error.message.includes('unique'));

			if (notUniqueError) {
				showToast({
					variant: 'warning',
					message: 'This tag already exists. Please try a different tag type.',
				});
			} else {
				showToast({
					variant: 'error',
					message: 'Something went wrong while creating this tag.',
				});
			}
		} finally {
			loading = false;
		}
	};

	const handleChange = (_: OnChangeEvent, selected: MultiSelectOption[]) => {
		const newTag = selected.find((option) => option.createNew);

		if (newTag) {
			handleNewTag(newTag.value);
		} else {
			onChange(selected);
		}
	};
</script>

<MultiSelect
	name={TAGS_MAP[typeKey].inputName}
	dataCy={dataCyPrefix}
	label={TAGS_MAP[typeKey].name}
	{selected}
	placeholder={TAGS_MAP[typeKey].placeholder}
	{options}
	size="sm"
	onKeyUp={handleKeyUp}
	onChange={handleChange}
	{loading}
	maxLabelLength={50}
	createNewOptionLabel="Create new tag:"
	noMatchingOptionsMsg=""
/>
