<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { Link } from '$global/components/Link';
	import { Txt } from '$global/components/Txt';

	interface Props {
		onClick: () => void;
		dataCy: string;
		title: string;
		subTitle?: string;
		refId?: string | null;
		legacyId?: string | null;
		url: string;
		class?: string;
		right?: import('svelte').Snippet;
	}

	let {
		onClick = () => void 0,
		dataCy,
		title,
		subTitle = '',
		refId,
		legacyId,
		url,
		right,
		...rest
	}: Props = $props();
	let dataCyPrefix = $derived(`${dataCy}-selected-option`);
</script>

<button
	data-cy={dataCyPrefix}
	class={twMerge(
		'flex h-[32px] w-full items-center justify-between gap-2 px-2',
		rest.class
	)}
	onclick={onClick}
>
	<div>
		<div class="flex items-center gap-1">
			<Link
				href={url}
				rel="noopener noreferrer"
				target="_blank"
				class="flex items-center gap-1"
			>
				<Txt
					class=" max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left text-blue-500"
					variant="label3"
				>
					{title}
				</Txt>
			</Link>

			{#if subTitle}
				<Txt
					class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal "
					variant="label3"
				>
					{subTitle}
				</Txt>
			{/if}
		</div>

		{#if refId}
			<Txt
				class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal "
				variant="label3"
			>
				{refId}
			</Txt>
		{/if}

		{#if legacyId}
			<Txt
				class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal "
				variant="label3"
			>
				{legacyId}
			</Txt>
		{/if}
	</div>

	{@render right?.()}
</button>
