<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { Button } from '$global/components/Button';
	import { Link } from '$global/components/Link';
	import { Txt } from '$global/components/Txt';

	interface Props {
		onRemove?: (() => void) | undefined;
		dataCy: string;
		title: string;
		subTitle?: string;
		url?: string;
		class?: string;
		refId?: string | null;
		legacyId?: string | null;
	}

	let {
		onRemove,
		dataCy,
		title,
		refId,
		legacyId,
		subTitle = '',
		url = '',
		...rest
	}: Props = $props();
	let dataCyPrefix = $derived(`${dataCy}-selected-option`);
</script>

<div
	data-cy={dataCyPrefix}
	class={twMerge(
		'flex h-[32px] w-full items-center justify-between gap-2 rounded border border-gray-200 pl-2',
		rest.class
	)}
>
	<div class="w-[calc(100%-36px)]">
		<div class="flex items-center gap-1">
			{#if url}
				<Link
					href={url}
					rel="noopener noreferrer"
					target="_blank"
					class="max-w-full"
				>
					<Txt
						class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left text-blue-500"
						variant="label3"
					>
						{title}
					</Txt>
				</Link>
			{:else}
				<Txt
					class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left text-blue-500"
					variant="label3"
				>
					{title}
				</Txt>
			{/if}

			{#if subTitle}
				<Txt
					class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal text-gray-500"
					variant="label3"
				>
					{subTitle}
				</Txt>
			{/if}
		</div>

		{#if refId}
			<Txt
				class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal"
				variant="label3"
			>
				{refId}
			</Txt>
		{/if}

		{#if legacyId}
			<Txt
				class="max-w-full overflow-hidden text-ellipsis whitespace-nowrap break-words text-left font-normal"
				variant="label3"
			>
				{legacyId}
			</Txt>
		{/if}
	</div>

	{#if onRemove}
		<Button
			variant="secondary"
			class="w-[32px] !border-0 bg-transparent"
			size="xs"
			dataCy={`${dataCy}-remove`}
			onclick={onRemove}
		>
			{#snippet leading()}
				<CrossIcon class="h-4 w-4" />
			{/snippet}
		</Button>
	{/if}
</div>
