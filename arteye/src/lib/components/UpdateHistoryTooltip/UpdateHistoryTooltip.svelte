<script lang="ts">
	import dayjs from 'dayjs';
	import type { UpdateHistory } from './types';
	import { TimeRefreshIcon } from '$global/assets/icons/TimeRefreshIcon';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';

	interface Props {
		updateHistory: UpdateHistory;
	}

	let { updateHistory }: Props = $props();

	let updatedName = $derived({
		first: updateHistory?.user_updated?.first_name || '',
		last: (updateHistory?.user_updated?.last_name || '').charAt(0),
	});

	let createdName = $derived({
		first: updateHistory?.user_created?.first_name || 'unknown',
		last: (updateHistory?.user_created?.last_name || '').charAt(0),
	});

	let getUpdated = $derived(() => {
		if (updateHistory?.date_updated) {
			return `${dayjs(updateHistory?.date_updated).format('HH:mm DD/MM/YYYY')}
				by ${updatedName.first} ${updatedName.last ? `${updatedName.last}.` : ''}`;
		}
		return '';
	});

	let getCreated = $derived(() => {
		if (updateHistory?.date_created) {
			return `${dayjs(updateHistory?.date_created).format('HH:mm DD/MM/YYYY')} 
				by
				${createdName.first} ${createdName.last ? `${createdName.last}.` : ''}`;
		}
		return '';
	});
</script>

<Tooltip dataCy="update-updateHistory" classes={{ content: 'p-2 max-w-none' }}>
	<span class="flex h-[24px] w-[24px] items-center justify-center">
		<TimeRefreshIcon color="gray-500" class="h-[18px] w-[18px]" />
	</span>

	{#snippet contentSlot()}
		<span>
			{#if updateHistory?.date_updated || updateHistory?.date_created}
				<span class="flex min-w-[200px] flex-col gap-2">
					{#if updateHistory?.date_updated && updateHistory?.user_updated?.first_name}
						<span>
							<Txt class="text-gray-500" variant="label4">Last updated by:</Txt>
							<Txt class="text-white" variant="label4">
								{getUpdated()}
							</Txt>
						</span>
					{/if}

					{#if updateHistory?.date_created && updateHistory?.user_created?.first_name}
						<span>
							<Txt class="text-gray-500" variant="label4">Created by:</Txt>
							<Txt class="text-white" variant="label4">
								{getCreated()}
							</Txt>
						</span>
					{/if}
				</span>
			{:else}
				<Txt class="text-white" variant="label4">No data available.</Txt>
			{/if}
		</span>
	{/snippet}
</Tooltip>
