<script lang="ts">
	import type { createDialog } from '@melt-ui/svelte';
	import { ChevronLeftIcon } from '$global/assets/icons/ChevronLeftIcon';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dataCy: string;
		dialogStores: ReturnType<typeof createDialog>;
		id?: string | undefined;
		confirmationText: string;
		onDelete?: (() => void) | undefined;
	}

	let {
		dataCy,
		dialogStores,
		confirmationText = '',
		onDelete = undefined,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-delete-dialog`);

	const handleClose = () => {
		dialogStores.states.open.set(false);
	};
</script>

<Dialog
	onClose={handleClose}
	{dialogStores}
	dataCy={dataCyPrefix}
	class="flex h-full max-h-none w-full items-center justify-center rounded-none p-[1.5rem] pt-[1.25rem] sm:max-h-[16rem] sm:max-w-[32rem] sm:rounded sm:p-[1.5rem] sm:pt-[1rem]"
>
	<div class="flex h-full flex-col items-center justify-center sm:h-[19rem]">
		<Txt variant="h3" class=" mb-4 mt-4 max-w-[24rem] text-center"
			>Confirm archive</Txt
		>
		<Txt variant="body2" class=" max-w-[26rem] text-center"
			>{confirmationText}</Txt
		>

		<div class="mt-6 flex w-full">
			<Button
				class="sm:mr-4 sm:w-1/3"
				{dataCy}
				variant="tertiary"
				size="md"
				fullWidth
				onclick={handleClose}
			>
				Back
				{#snippet leading()}
					<ChevronLeftIcon class="ml-2 h-4 w-4" />
				{/snippet}
			</Button>
			<Button dataCy={`${dataCyPrefix}-delete`} onclick={onDelete} size="md">
				CONFIRM ARCHIVE
			</Button>
		</div>
	</div></Dialog
>
