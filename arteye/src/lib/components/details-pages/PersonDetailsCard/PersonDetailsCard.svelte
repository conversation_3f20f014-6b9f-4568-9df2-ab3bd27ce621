<script module lang="ts">
	export type PersonalDetailsForm = Partial<
		Pick<
			NonNullable<PersonDetailsPageData['person']>,
			| 'id'
			| 'first_name'
			| 'middle_name'
			| 'last_name'
			| 'preferred_name'
			| 'type'
			| 'gender'
			| 'nationalities'
			| 'year_birth'
			| 'year_death'
			| 'net_worth_usd'
			| 'industry'
			| 'job_title'
			| 'biography'
			| 'date_created'
			| 'date_updated'
			| 'user_created'
			| 'user_updated'
		>
	> & {
		attributes: NonNullable<
			NonNullable<PersonDetailsPageData['person']>['entity']
		>['attributes'];
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { AccordionItem } from '$global/components/Accordion';
	import { Input } from '$global/components/Input';
	import { formatNumberWithSeparator } from '$global/components/Input/utils/formatNumberWithSeparator/formatNumberWithSeparator';
	import { formatWithThousandSeparator } from '$global/components/Input/utils/formatWithThousandSeparator/formatWithThousandSeparator';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { removeThousandSeparator } from '$global/components/Input/utils/removeThousandSeparator/removeThousandSeparator';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		onArtistPage?: boolean | undefined;
		personalDetailsForm: PersonalDetailsForm;
		onChange: (form: PersonalDetailsForm) => void;
		title?: string;
		personTypes: NonNullable<
			PersonDetailsPageData['user']
		>['dropdowns']['personTypes'];
		nationalities: NonNullable<
			PersonDetailsPageData['user']
		>['dropdowns']['countries'];
		genders: NonNullable<PersonDetailsPageData['user']>['dropdowns']['genders'];
		attributeTypes: NonNullable<
			PersonDetailsPageData['user']
		>['dropdowns']['attributeTypes'];
		children?: import('svelte').Snippet;
	}

	let {
		onArtistPage,
		personalDetailsForm = $bindable(),
		onChange,
		title = 'Person details',
		personTypes,
		nationalities,
		attributeTypes,
		genders,
		children,
	}: Props = $props();

	let personTypesOptions = $derived(
		personTypes.map((type) => ({
			label: type.name || '',
			value: type.key || '',
		}))
	);

	let genderOptions = $derived(
		genders.map((gender) => ({
			value: gender.key,
			label: gender.name,
		}))
	);

	let nationalitiesOptions = $derived(
		nationalities.map<MultiSelectOption>((country) => {
			let label = country?.country_nationality
				? `${country?.country_nationality} (${country?.name})`
				: `${country?.name}`;

			return {
				label,
				value: country?.short_code || '',
			};
		})
	);

	let attributeOptions = $derived(
		(attributeTypes || []).map((attribute) => ({
			label: `${attribute?.name}`,
			value: `${attribute?.key}`,
		}))
	);

	const dataCy = 'person-details-card';

	const handleNationalitiesChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		if (selected?.length > (personalDetailsForm.nationalities?.length || 0)) {
			const addedNationality = selected[selected.length - 1];
			const nationalityToAdd = nationalities?.find(
				(nationality) => nationality?.short_code === addedNationality?.value
			);

			onChange({
				...personalDetailsForm,
				nationalities: [
					...(personalDetailsForm.nationalities || []),
					{
						country: {
							short_code: `${nationalityToAdd?.short_code}`,
							code: `${nationalityToAdd?.code}`,
							name: `${nationalityToAdd?.name}`,
							type: {
								key: `${nationalityToAdd?.type?.key}`,
							},
						},
					},
				] as PersonalDetailsForm['nationalities'],
			});
		} else {
			const selectedNationalitiesShortCodes = selected?.map(
				(selectedOption) => selectedOption?.value
			);

			onChange({
				...personalDetailsForm,
				nationalities: (personalDetailsForm.nationalities || [])?.filter(
					(nationality) =>
						selectedNationalitiesShortCodes.includes(
							`${nationality?.country?.short_code}`
						)
				),
			});
		}
	};

	const handleNumberInputChange =
		(field: keyof PersonalDetailsForm) => (event?: Event | undefined) => {
			const target = event?.target as HTMLInputElement;
			const value = removeThousandSeparator(target.value);
			onChange({ ...personalDetailsForm, [field]: value });
		};

	const handleChangeGender = (event: SelectChangeEvent) => {
		const value = event.detail.value;
		const gender = genders.find((gender) => gender.key === value);
		onChange({ ...personalDetailsForm, gender });
	};

	const handleInputChange =
		(field: keyof PersonalDetailsForm) => (event?: Event | undefined) => {
			const target = event?.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...personalDetailsForm, [field]: value });
		};

	const handlePersonTypeChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		if (selected?.length > (personalDetailsForm.type?.length || 0)) {
			const addedPersonType = selected[selected.length - 1];

			onChange({
				...personalDetailsForm,
				type: [
					...(personalDetailsForm.type || []),
					{
						person_type_key: {
							key: addedPersonType.value,
							name: addedPersonType.label,
						},
					},
				] as PersonalDetailsForm['type'],
			});
		} else {
			const selectedPersonTypeKeys = selected?.map(
				(selectedOption) => selectedOption?.value
			);

			onChange({
				...personalDetailsForm,
				type: (personalDetailsForm.type || [])?.filter((personType) =>
					selectedPersonTypeKeys.includes(`${personType?.person_type_key?.key}`)
				),
			});
		}
	};

	const handleAttributesChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		onChange({
			...personalDetailsForm,
			attributes: selected.map((sel) => {
				return {
					type: { key: sel.value, name: sel.label },
				};
			}) as PersonalDetailsForm['attributes'],
		});
	};
</script>

<AccordionItem
	{dataCy}
	{title}
	class="rounded-md border bg-white"
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4">
		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-firstName`}
					name="firstName"
					placeholder=""
					required
					label="First name"
					value={personalDetailsForm.first_name}
					size="sm"
					onkeyup={handleInputChange('first_name')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-middleName`}
					name="middleName"
					placeholder=""
					label="Middle name"
					value={personalDetailsForm.middle_name}
					size="sm"
					onkeyup={handleInputChange('middle_name')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-lastName`}
					name="lastName"
					placeholder=""
					required
					label="Last name"
					value={personalDetailsForm.last_name}
					size="sm"
					onkeyup={handleInputChange('last_name')}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-preferredName`}
					name="preferredName"
					placeholder=""
					label="Preferred name"
					bind:value={personalDetailsForm.preferred_name}
					size="sm"
					onkeyup={handleInputChange('preferred_name')}
					tooltip="tbc"
				/>
			</div>

			<MultiSelect
				name="personType"
				dataCy={`${dataCy}-personType`}
				label="Person type"
				disabled={onArtistPage}
				selected={onArtistPage
					? [
							{
								value: 'ARTIST',
								label: 'Artist',
							},
						]
					: (personalDetailsForm.type || [])
							.map((personType) => ({
								label: `${personType?.person_type_key?.name}`,
								value: `${personType?.person_type_key?.key}`,
							}))
							.filter(
								(attr, i, arr) =>
									arr.findIndex((attr2) => attr.value === attr2.value) === i
							)}
				placeholder="Type or select"
				options={personTypesOptions}
				class="col-span-1"
				size="sm"
				onChange={handlePersonTypeChange}
			/>
		</div>

		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-1">
				<Select
					ariaLabel="Select a gender"
					dataCy={`${dataCy}-gender`}
					name="gender"
					placeholder=""
					label="Gender"
					options={genderOptions}
					value={personalDetailsForm.gender?.key || ''}
					size="sm"
					onchange={handleChangeGender}
					tooltip="tbc"
				/>
			</div>

			<div class="col-span-1">
				<MultiSelect
					name="nationalities"
					dataCy={`${dataCy}-nationalities`}
					label="Nationalities"
					selected={(personalDetailsForm.nationalities || [])
						.map((nationalityRow) => ({
							label: nationalityRow?.country?.name || '',
							value: nationalityRow?.country?.short_code || '',
						}))
						.filter(
							(attr, i, arr) =>
								arr.findIndex((attr2) => attr.value === attr2.value) === i
						)}
					placeholder="Type or select"
					options={nationalitiesOptions}
					size="sm"
					onChange={handleNationalitiesChange}
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-yearBirth`}
					name="yearBirth"
					placeholder=""
					label="Year of Birth"
					value={personalDetailsForm?.year_birth
						? `${personalDetailsForm?.year_birth}`
						: ''}
					size="sm"
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleNumberInputChange('year_birth')}
					onchange={handleNumberInputChange('year_birth')}
					tooltip="tbc"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-yearDeath`}
					name="yearDeath"
					placeholder=""
					label="Year of Death"
					value={personalDetailsForm?.year_death
						? `${personalDetailsForm?.year_death}`
						: ''}
					size="sm"
					onkeydown={handleKeyDownNumbersOnly}
					onkeyup={handleNumberInputChange('year_death')}
					onchange={handleNumberInputChange('year_death')}
					tooltip="tbc"
				/>
			</div>

			<div class="col-span-1">
				<Input
					dataCy={`${dataCy}-netWorth`}
					name="netWorth"
					placeholder=""
					label="Net Worth (USD)"
					value={personalDetailsForm?.net_worth_usd
						? `${formatNumberWithSeparator(personalDetailsForm?.net_worth_usd)}`
						: ''}
					size="sm"
					onkeydown={(e) => {
						handleKeyDownNumbersOnly(e, true);
						formatWithThousandSeparator(e);
					}}
					onkeyup={handleNumberInputChange('net_worth_usd')}
					onchange={handleNumberInputChange('net_worth_usd')}
					tooltip="tbc"
				/>
			</div>
		</div>

		<div class="mb-4 flex flex-col lg:grid grid-cols-5 gap-4">
			<div class="col-span-2 flex flex-col lg:grid grid-cols-2 gap-4">
				{#if !onArtistPage}
					<div class="col-span-1">
						<Input
							dataCy={`${dataCy}-industry`}
							name="industry"
							placeholder=""
							label="Industry"
							value={personalDetailsForm.industry}
							size="sm"
							onkeyup={handleInputChange('industry')}
							tooltip="tbc"
						/>
					</div>

					<div class="col-span-1">
						<Input
							dataCy={`${dataCy}-jobTitle`}
							name="jobTitle"
							placeholder=""
							label="Job Title"
							value={personalDetailsForm.job_title}
							size="sm"
							onkeyup={handleInputChange('job_title')}
							tooltip="tbc"
						/>
					</div>
				{/if}

				{#if children}
					{@render children?.()}
				{/if}
			</div>

			{#if !onArtistPage}
				<MultiSelect
					name="attributes"
					dataCy={`${dataCy}-attributes`}
					label="Attributes"
					selected={(personalDetailsForm.attributes || [])
						.map((attribute) => ({
							label: `${attribute?.type?.name}`,
							value: `${attribute?.type?.key}`,
						}))
						.filter(
							(attr, i, arr) =>
								arr.findIndex((attr2) => attr.value === attr2.value) === i
						)}
					placeholder="Type or select"
					options={attributeOptions}
					class="col-span-1"
					size="sm"
					onChange={handleAttributesChange}
				/>
			{/if}

			<div class={classNames('col-span-2', { 'col-span-3': onArtistPage })}>
				<Input
					dataCy={`${dataCy}-bio`}
					name="bio"
					placeholder=""
					label="Biography"
					rows={3}
					value={personalDetailsForm.biography}
					onkeyup={handleInputChange('biography')}
					size="sm"
					classes={{ wrapper: 'h-[calc(100%-28px)]' }}
					class="resize-y"
					tooltip="tbc"
				/>
			</div>
		</div>

		<div class="flex justify-end">
			<UpdateHistoryTooltip updateHistory={personalDetailsForm} />
		</div>
	</div>
</AccordionItem>
