<script lang="ts">
	import dayjs from 'dayjs';
	import { twMerge } from 'tailwind-merge';
	import { FastClockIcon } from '$global/assets/icons/FastClockIcon';
	import { Tooltip } from '$global/components/Tooltip';
	import { Txt } from '$global/components/Txt';

	interface Props {
		dataCy: string;
		dateUpdated: string | undefined | null;
		dateCreated: string | undefined | null;
		userUpdated:
			| {
					first_name?: string | null;
					last_name?: string | null;
			  }
			| null
			| undefined;
		userCreated:
			| {
					first_name?: string | null;
					last_name?: string | null;
			  }
			| null
			| undefined;
		class?: string;
	}

	let { ...props }: Props = $props();

	let dataCyPrefix = $derived(`${props.dataCy}-history`);
</script>

{#if props.dateUpdated || props.dateCreated}
	<Tooltip dataCy={dataCyPrefix} placement="top">
		<FastClockIcon class={twMerge('h-5 w-5', props.class)} color="gray-500" />
		{#snippet contentSlot()}
			<div>
				{#if props.dateUpdated}
					<Txt variant="label4" class="text-gray-500">Last edited:</Txt>
					<Txt variant="label4" class="text-gray-0"
						>{dayjs(props.dateUpdated).format(
							'HH:mm DD/MM/YYYY'
						)}{props.userUpdated
							? ` by ${[
									props.userUpdated.first_name,
									props.userUpdated.last_name,
								]
									.filter(Boolean)
									.join(' ')}`
							: ''}</Txt
					>
				{/if}
				{#if props.dateCreated}
					<Txt variant="label4" class="text-gray-500">Entry created:</Txt>
					<Txt variant="label4" class="text-gray-0"
						>{dayjs(props.dateCreated).format(
							'HH:mm DD/MM/YYYY'
						)}{props.userCreated
							? ` by ${[
									props.userCreated.first_name,
									props.userCreated.last_name,
								]
									.filter(Boolean)
									.join(' ')}`
							: ''}</Txt
					>
				{/if}
			</div>
		{/snippet}
	</Tooltip>
{/if}
