<script lang="ts" module>
	export const formatOrganisation = (
		organisation: OrganisationSearchQuery['organisationSearch']['data'][number]
	) => {
		return {
			line1: `${organisation?.name}`,
			line2: `${Routes.Organisations}/${organisation?.id}`,
			line3: JSON.stringify(organisation?.type),
			line4: JSON.stringify(organisation.location),
			line5: organisation?.id,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { CreateNewOption } from '$lib/components/CreateNewOption';
	import { Routes } from '$lib/constants/routes';

	import {
		OrganisationSearchDocument,
		type OrganisationSearchQuery,
		type OrganisationSearchQueryVariables,
	} from '$lib/custom-queries/__generated__/organisationSearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemove: () => void;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search organisation',
		dataCy,
		selectedOption = $bindable(null),
		onRemove,
		onChange = undefined,
	}: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): OrganisationSearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : -1,
				filters: { nameOrId: value },
			},
		};
	};

	const getOptions = (data: OrganisationSearchQuery | undefined) => {
		return [...(data?.organisationSearch?.data || []).map(formatOrganisation)];
	};

	const getParsedLocation = (
		location: string
	):
		| OrganisationSearchQuery['organisationSearch']['data'][number]['location']
		| null => {
		try {
			return JSON.parse(location);
		} catch (e) {
			return null;
		}
	};
</script>

{#if selectedOption && selectedOption.line1}
	{@const location = getParsedLocation(selectedOption.line4 || '')}

	<AutocompleteSelectedOption
		{dataCy}
		title={selectedOption.line1}
		subTitle={location ? `(${location.name})` : ''}
		url={selectedOption.line2 || ''}
		{onRemove}
	/>
{:else}
	<div class="relative">
		<QueryAutocomplete
			size="sm"
			OptionComponent={LinkOption}
			SelectedOptionComponent={LinkOption}
			name="organisation"
			dataCy={`${dataCy}-organisation`}
			{placeholder}
			emptyValueResponse={{
				organisationSearch: { data: [] },
				organisationSearchCount: 0,
			}}
			showResultsWhenEmpty={false}
			graphQlClient={gqlClientCustom}
			classes={{
				listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
				option: {
					line3: 'hidden',
					line4: 'hidden',
					line5: 'hidden',
				},
				noResults: 'p-0',
			}}
			class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
			requestHeaders={getAuthorizationHeaders(data)}
			{getOptions}
			{getVariables}
			document={OrganisationSearchDocument}
			{value}
			selectedOption={null}
			{onChange}
		>
			{#snippet noResults()}
				<div>
					<div class="flex items-center py-2">
						<NoResults
							class="text-left"
							dataCy={`${dataCy}-organisation-autocomplete`}
						>
							No results found.
						</NoResults>
					</div>

					<div class="hidden lg:block">
						<CreateNewOption
							{dataCy}
							url={`${Routes.Organisations}/new`}
							ctaText="Create new organisation"
						/>
					</div>
				</div>
			{/snippet}
		</QueryAutocomplete>
	</div>
{/if}
