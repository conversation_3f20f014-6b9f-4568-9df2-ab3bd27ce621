<script module lang="ts">
	export type OrganisationDetailsForm = Partial<
		Pick<
			NonNullable<OrganisationDetailsPageData['organisation']>,
			| 'id'
			| 'description'
			| 'year_founded'
			| 'year_dissolved'
			| 'name'
			| 'type'
			| 'parent'
			| 'location'
		>
	> & {
		attributes: NonNullable<
			NonNullable<OrganisationDetailsPageData['organisation']>['entity']
		>['attributes'];
	};
</script>

<script lang="ts">
	import LocationAutocomplete, {
		formatLocation,
	} from './LocationAutocomplete/LocationAutocomplete.svelte';
	import { OrganisationAutocomplete } from './OrganisationAutocomplete';
	import { formatOrganisation } from './OrganisationAutocomplete/OrganisationAutocomplete.svelte';
	import { Accordion, AccordionItem } from '$global/components/Accordion';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Input } from '$global/components/Input';
	import { handleKeyDownNumbersOnly } from '$global/components/Input/utils/handleKeyDownNumbersOnly/handleKeyDownNumbersOnly';
	import { InputLabel } from '$global/components/InputLabel';
	import type {
		MultiSelectOption,
		OnChangeEvent,
	} from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import type { OrganisationDetailsPageData as OrganisationDetailsPageData } from '$routes/organisations/[id]/types';

	interface Props {
		onGalleryPage?: boolean | undefined;
		organisationDetailsForm: OrganisationDetailsForm;
		onChange: (form: OrganisationDetailsForm) => void;
		organisationTypes: NonNullable<
			OrganisationDetailsPageData['user']
		>['dropdowns']['organisationTypes'];
		attributeTypes: NonNullable<
			OrganisationDetailsPageData['user']
		>['dropdowns']['attributeTypes'];
		organisation: OrganisationDetailsPageData['organisation'] | undefined;
	}

	let {
		onGalleryPage,
		organisationDetailsForm,
		onChange,
		organisationTypes,
		attributeTypes,
		organisation,
	}: Props = $props();

	let organisationTypesOptions = $derived(
		organisationTypes.map((type) => ({
			label: type.name || '',
			value: type.name || '',
		}))
	);

	let attributeOptions = $derived(
		(attributeTypes || []).map((attribute) => ({
			label: `${attribute?.name}`,
			value: `${attribute?.key}`,
		}))
	);

	let selectedParentOrgOption = $state(
		organisationDetailsForm?.parent
			? formatOrganisation(
					organisationDetailsForm?.parent as Parameters<
						typeof formatOrganisation
					>[0]
				)
			: null
	) as ReturnType<typeof formatOrganisation> | null;

	$effect(() => {
		selectedParentOrgOption = organisationDetailsForm?.parent
			? formatOrganisation(
					organisationDetailsForm?.parent as Parameters<
						typeof formatOrganisation
					>[0]
				)
			: null;
	});

	let selectedLocationOption = $state(
		organisationDetailsForm?.location
			? formatLocation(
					organisationDetailsForm?.location as Parameters<
						typeof formatLocation
					>[0]
				)
			: null
	) as ReturnType<typeof formatLocation> | null;

	$effect(() => {
		selectedLocationOption = organisationDetailsForm?.location
			? formatLocation(
					organisationDetailsForm?.location as Parameters<
						typeof formatLocation
					>[0]
				)
			: null;
	});

	const dataCy = 'organisation-details-card';

	const handleOrganisationTypeChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) => {
		if (selected?.length > (organisationDetailsForm.type?.length || 0)) {
			const addedOrganisationType = selected[selected.length - 1];
			const organisationTypeToAdd = organisationTypes?.find(
				(organisationType) =>
					organisationType?.name === addedOrganisationType?.value
			);

			onChange({
				...organisationDetailsForm,
				type: [
					...(organisationDetailsForm.type || []),
					{
						organisation_type_key: {
							name: organisationTypeToAdd?.name,
							key: organisationTypeToAdd?.key,
						},
					},
				] as OrganisationDetailsForm['type'],
			});
		} else {
			const selectedOrganisationTypeNames = selected?.map(
				(selectedOption) => selectedOption?.value
			);

			onChange({
				...organisationDetailsForm,
				type: (organisationDetailsForm.type || [])?.filter((organisationType) =>
					selectedOrganisationTypeNames.includes(
						`${organisationType?.organisation_type_key?.name}`
					)
				),
			});
		}
	};

	const handleNumberInputChange =
		({
			field,
			maxChars,
		}: {
			field: keyof OrganisationDetailsForm;
			maxChars?: number;
		}) =>
		(event?: Event | undefined) => {
			const target = event?.target as HTMLInputElement;
			let value = target.value;

			if (maxChars && value.length > maxChars) {
				value = value.slice(0, maxChars);
			}

			onChange({ ...organisationDetailsForm, [field]: +value });
		};

	const handleInputChange =
		(field: keyof OrganisationDetailsForm) => (event?: Event | undefined) => {
			const target = event?.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...organisationDetailsForm, [field]: value });
		};

	const handleParentOrganisationChange = (e: {
		detail: { value: OptionType };
	}) => {
		const option = e.detail.value;

		onChange({
			...organisationDetailsForm,
			parent: {
				name: `${option.line1}`,
				id: `${option.line5}`,
				type: JSON.parse(`${option.line3}`),
				location: JSON.parse(`${option.line4}`),
			},
		});

		return Promise.resolve();
	};

	const handleParentOrganisationRemove = () => {
		onChange({
			...organisationDetailsForm,
			parent: null,
		});
	};

	const handleLocationChange = (e: { detail: { value: OptionType } }) => {
		const option = e.detail.value;
		onChange({
			...organisationDetailsForm,
			location: {
				short_code: `${option.line2}`,
				name: `${option.line3}`,
				type: JSON.parse(`${option.line4}`),
				country: JSON.parse(`${option.line5}`),
				code: `${option.line6}`,
			},
		});

		return Promise.resolve();
	};

	const handleLocationRemove = () => {
		onChange({
			...organisationDetailsForm,
			location: null,
		});
	};

	const handleAttributesChange = (
		_: OnChangeEvent,
		selected: MultiSelectOption[]
	) =>
		onChange({
			...organisationDetailsForm,
			attributes: selected.map((sel) => {
				return {
					type: { key: sel.value, name: sel.label },
				};
			}) as OrganisationDetailsForm['attributes'],
		});
</script>

<Accordion class="mb-4">
	<AccordionItem
		dataCy="test"
		title="Organisation details"
		class="rounded-md border bg-white"
		classes={{ titleButton: 'px-4' }}
		defaultOpen
	>
		<div class="p-4">
			<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
				<MultiSelect
					name="organisation-types"
					dataCy={`${dataCy}-organisation-types`}
					label="Organisation Type/s"
					disabled={onGalleryPage}
					selected={(organisationDetailsForm.type || [])
						.map((organisationType) => ({
							label: organisationType?.organisation_type_key?.name || '',
							value: organisationType?.organisation_type_key?.name || '',
						}))
						.filter(
							(attr, i, arr) =>
								arr.findIndex((attr2) => attr.value === attr2.value) === i
						)}
					placeholder="Type or select"
					options={organisationTypesOptions}
					size="sm"
					onChange={handleOrganisationTypeChange}
				/>

				<div class="col-span-1">
					<Input
						dataCy={`${dataCy}-name`}
						name="name"
						placeholder="Organisation name"
						label="Name"
						required
						value={organisationDetailsForm.name}
						size="sm"
						onkeyup={handleInputChange('name')}
						tooltip="tbc"
					/>
				</div>

				<div class="col-span-1">
					<Input
						dataCy={`${dataCy}-year-founded`}
						name="year-founded"
						placeholder="Year founded"
						label="Year Founded"
						onkeydown={handleKeyDownNumbersOnly}
						value={organisationDetailsForm?.year_founded
							? `${organisationDetailsForm?.year_founded}`
							: ''}
						size="sm"
						onkeyup={handleNumberInputChange({
							field: 'year_founded',
						})}
						onchange={handleNumberInputChange({
							field: 'year_founded',
						})}
						maxlength={4}
						tooltip="tbc"
					/>
				</div>

				<div class="col-span-1">
					<Input
						dataCy={`${dataCy}-year-dissolved`}
						name="year-dissolved"
						placeholder="Year dissolved"
						label="Year Dissolved"
						onkeydown={handleKeyDownNumbersOnly}
						value={organisationDetailsForm?.year_dissolved
							? `${organisationDetailsForm?.year_dissolved}`
							: ''}
						size="sm"
						onkeyup={handleNumberInputChange({
							field: 'year_dissolved',
						})}
						onchange={handleNumberInputChange({
							field: 'year_dissolved',
						})}
						maxlength={4}
						tooltip="tbc"
					/>
				</div>
			</div>

			<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
				<div class="col-span-2">
					<Input
						dataCy={`${dataCy}-description`}
						name="description"
						placeholder="Description"
						label="Description"
						value={organisationDetailsForm.description}
						onkeyup={handleInputChange('description')}
						size="sm"
						class="resize-y"
						tooltip="tbc"
					/>
				</div>

				<div class="col-span-1">
					<div class="mb-2 flex justify-between">
						<InputLabel dataCy={`${dataCy}-description`}>
							Parent Organization
						</InputLabel>

						<InfoTooltip
							dataCy={`${dataCy}-description`}
							placement="top-end"
							content="tbc"
						/>
					</div>

					<OrganisationAutocomplete
						bind:selectedOption={selectedParentOrgOption}
						{dataCy}
						onChange={handleParentOrganisationChange}
						onRemove={handleParentOrganisationRemove}
					/>
				</div>

				<div class="col-span-1">
					<div class="mb-2 flex justify-between">
						<InputLabel dataCy={`${dataCy}-location`}>Location</InputLabel>

						<InfoTooltip
							dataCy={`${dataCy}-location`}
							placement="top-end"
							content="tbc"
						/>
					</div>

					<LocationAutocomplete
						bind:selectedOption={selectedLocationOption}
						{dataCy}
						onChange={handleLocationChange}
						onRemove={handleLocationRemove}
					/>
				</div>
			</div>

			<div class="mb-4 flex flex-col lg:grid grid-cols-4 gap-4">
				<div class="col-span-1">
					<MultiSelect
						name="attributes"
						dataCy={`${dataCy}-attributes`}
						label="Attributes"
						selected={(organisationDetailsForm.attributes || [])
							.map((attribute) => ({
								label: `${attribute?.type?.name}`,
								value: `${attribute?.type?.key}`,
							}))
							.filter(
								(attr, i, arr) =>
									arr.findIndex((attr2) => attr.value === attr2.value) === i
							)}
						placeholder="Type or select"
						options={attributeOptions}
						class="col-span-1"
						size="sm"
						onChange={handleAttributesChange}
					/>
				</div>
			</div>
		</div>
	</AccordionItem>
</Accordion>
