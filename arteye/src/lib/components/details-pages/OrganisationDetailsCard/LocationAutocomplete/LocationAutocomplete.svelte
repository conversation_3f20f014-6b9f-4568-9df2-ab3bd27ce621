<script lang="ts" module>
	export const formatLocation = (
		location: GetLocationsQuery['location'][number]
	) => {
		return {
			line1: `${location.name} (${location.short_code})`,
			line2: `${location.short_code}`,
			line3: `${location.name}`,
			line4: `${JSON.stringify(location.type)}`,
			line5: `${JSON.stringify(location.country)}`,
			line6: `${location.code}`,
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import Option from '$global/components/QueryAutocomplete/Option/Option.svelte';
	import SelectedOption from '$global/components/QueryAutocomplete/SelectedOption/SelectedOption.svelte';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import { gqlClient } from '$lib/gqlClient';

	import {
		GetLocationsDocument,
		type GetLocationsQuery,
		type GetLocationsQueryVariables,
	} from '$lib/queries/__generated__/getLocations.generated';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemove: () => void;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search location',
		dataCy,
		selectedOption = $bindable(null),
		onRemove,
		onChange = undefined,
	}: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetLocationsQueryVariables => {
		return {
			limit: value.length < 3 ? 20 : -1,
			filter: value
				? {
						_or: [
							{ name: { _icontains: value } },
							{ short_code: { _icontains: value } },
						],
					}
				: {},
		};
	};

	const getOptions = (data: GetLocationsQuery | undefined) => {
		return [...(data?.location || []).map(formatLocation)];
	};
</script>

{#if selectedOption && selectedOption.line1}
	<AutocompleteSelectedOption
		{dataCy}
		title={selectedOption.line1}
		{onRemove}
	/>
{:else}
	<div class={'relative'}>
		<QueryAutocomplete
			size="sm"
			OptionComponent={Option}
			SelectedOptionComponent={SelectedOption}
			name="location"
			dataCy={`${dataCy}-location`}
			{placeholder}
			emptyValueResponse={{
				location: [],
			}}
			showResultsWhenEmpty={false}
			graphQlClient={gqlClient}
			classes={{
				listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
				option: {
					line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
					line2: 'hidden',
					line3: 'hidden',
					line4: 'hidden',
					line5: 'hidden',
					line6: 'hidden',
				},
			}}
			{getOptions}
			requestHeaders={getAuthorizationHeaders(data)}
			{getVariables}
			document={GetLocationsDocument}
			{value}
			selectedOption={null}
			{onChange}
		>
			{#snippet noResults()}
				<NoResults class="text-left" dataCy={`${dataCy}-location-autocomplete`}
					>No location found.</NoResults
				>
			{/snippet}
		</QueryAutocomplete>
	</div>
{/if}
