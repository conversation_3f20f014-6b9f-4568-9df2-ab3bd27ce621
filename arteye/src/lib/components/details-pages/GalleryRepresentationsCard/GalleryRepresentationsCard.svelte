<script lang="ts" module>
	export type GalleryRepresentationsType =
		(GetGalleryRepresentationQuery['gallery_representation'][number] & {
			isDeleted?: boolean;
		})[];
</script>

<script lang="ts">
	import { createDialog } from '@melt-ui/svelte';
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Accordion, AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { Dialog } from '$global/components/Dialog';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableCell,
		TableActionCell,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import { Routes } from '$lib/constants/routes';
	import type { GetGalleryRepresentationQuery } from '$lib/queries/__generated__/getGalleryRepresentation.generated';

	interface Props {
		saveChangesDisabled: boolean;
		showSaveBar: boolean;
		onSave: () => Promise<string | undefined>;
		galleryRepresentations: GalleryRepresentationsType;
		onChange: (galleryRepresentations: GalleryRepresentationsType) => void;
		title?: string;
	}

	let {
		saveChangesDisabled,
		showSaveBar,
		onSave,
		galleryRepresentations,
		onChange,
		title = 'Person details',
	}: Props = $props();

	let saving = $state(false);

	const dialogStores = createDialog();

	const handleDeleteGalleryRepresentation = (index: number) => {
		onChange(
			galleryRepresentations?.map((galleryRepresentation, i) =>
				i !== index
					? galleryRepresentation
					: { ...galleryRepresentation, isDeleted: true }
			)
		);
	};

	const handleClickAdd = () => {
		if (showSaveBar) {
			dialogStores.states.open.set(true);
		} else {
			goto(`${page.url.pathname}${Routes.AddNewRepresentation}`);
		}
	};

	const handleClickBack = () => {
		dialogStores.states.open.set(false);
	};

	const handleClickSave = async () => {
		saving = true;
		const artistId = await onSave();
		goto(`${Routes.Artists}/${artistId}${Routes.AddNewRepresentation}`);
	};

	const dataCy = 'children-card';
</script>

<Accordion class="mb-4" multiple>
	<AccordionItem
		dataCy="test"
		{title}
		class="rounded-md border bg-white"
		classes={{ titleButton: 'px-4' }}
		defaultOpen
	>
		<div class="p-4 max-lg:overflow-x-scroll">
			<table class="w-full table-fixed rounded-md bg-white min-w-[1100px]">
				<TableHeaderRow {dataCy}>
					<TableHeader {dataCy}>Name</TableHeader>
					<TableHeader {dataCy}>Description</TableHeader>
					<TableHeader {dataCy}>Email date</TableHeader>
					<TableHeader {dataCy} class="flex justify-end">
						{#snippet custom()}
							<Button
								disabled={saveChangesDisabled}
								onclick={handleClickAdd}
								dataCy={`${dataCy}-add`}
								class={classNames('h-[2rem] w-[2rem] px-0', {
									invisible: !page.params.id,
								})}
								variant="secondary"
								size="xs"
							>
								<PlusIcon class="h-3 w-3" />
							</Button>
						{/snippet}
					</TableHeader>
				</TableHeaderRow>

				<TableBody {dataCy}>
					{#if galleryRepresentations}
						{#each galleryRepresentations as galleryRepresentation, index}
							{#if !galleryRepresentation.isDeleted}
								<TableRow {index} {dataCy}>
									<TableCell {dataCy}>
										{#snippet custom()}
											<div class="flex">
												<Txt
													component="a"
													variant="body2"
													class="text-blue-500"
													target="_blank"
													rel="noopener noreferrer"
													href={`${Routes.Galleries}/${galleryRepresentation?.gallery?.id}`}
												>
													{galleryRepresentation?.gallery?.organisation?.name}
												</Txt>

												{#if galleryRepresentation?.gallery?.organisation?.location?.name}
													<Txt variant="body2" class="text-gray-500">
														&nbsp;({galleryRepresentation?.gallery?.organisation
															?.location?.name})
													</Txt>
												{/if}
											</div>
										{/snippet}
									</TableCell>
									<TableCell
										content={galleryRepresentation?.description}
										{dataCy}
									>
										{galleryRepresentation?.description || ''}
									</TableCell>
									<TableCell
										content={dayjs(
											getDayFromDirectus(galleryRepresentation?.email_date)
										).format('DD/MM/YYYY')}
										{dataCy}
									>
										{#if galleryRepresentation?.email_date}
											{dayjs(
												getDayFromDirectus(galleryRepresentation?.email_date)
											).format('DD/MM/YYYY')}
										{/if}
									</TableCell>
									<TableActionCell {dataCy} class="flex justify-end">
										<div class="flex items-center justify-end gap-2">
											<UpdateHistoryTooltip
												updateHistory={galleryRepresentation}
											/>

											<Button
												onclick={() => handleDeleteGalleryRepresentation(index)}
												dataCy={`${dataCy}-delete`}
												class="h-[2rem] w-[2rem] px-0"
												variant="secondary"
												size="xs"
											>
												<BinIcon class="h-3 w-3" />
											</Button>
										</div>
									</TableActionCell>
								</TableRow>
							{/if}
						{/each}
					{/if}
				</TableBody>
			</table>
		</div>
	</AccordionItem>
</Accordion>

<Dialog
	{dataCy}
	{dialogStores}
	title={`Unsaved changes`}
	shouldClose={false}
	showCloseIcon={false}
>
	<Txt variant="body2">
		Would you like to save your changes first? You have unsaved changes that
		must be saved or discarded before adding a new artist or gallery
		representation.
	</Txt>
	<div
		class="mt-6 flex flex-col-reverse gap-2 xs:grid xs:grid-cols-2 xs:flex-row"
	>
		<Button
			dataCy={`${dataCy}-back`}
			size="lg"
			disabled={saving}
			variant="secondary"
			onclick={handleClickBack}>back</Button
		>
		<Button
			loading={saving}
			onclick={handleClickSave}
			dataCy={`${dataCy}-save`}
			size="lg">yes</Button
		>
	</div>
</Dialog>
