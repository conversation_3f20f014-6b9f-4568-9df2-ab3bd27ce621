<script lang="ts">
	import { Accordion, AccordionItem } from '$global/components/Accordion';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { Routes } from '$lib/constants/routes';
	import { type GetOrganisationsDetailsQuery } from '$lib/queries/__generated__/getOrganisationDetails.generated';
	interface Props {
		children: GetOrganisationsDetailsQuery['organisation'][number]['children'];
	}

	let { children }: Props = $props();

	const dataCy = 'children-card';
</script>

<Accordion class="mb-4" multiple>
	<AccordionItem
		dataCy="test"
		title="Children"
		class="rounded-md border bg-white"
		classes={{ titleButton: 'px-4' }}
		defaultOpen
	>
		<div class="p-4">
			<table class="w-full table-fixed rounded-md bg-white">
				<TableHeaderRow {dataCy}>
					<TableHeader {dataCy}>Name</TableHeader>
				</TableHeaderRow>

				<TableBody {dataCy}>
					{#if children}
						{#each children as child, index}
							<TableRow {index} {dataCy}>
								<TableCell {dataCy}>
									{#snippet custom()}
										<Txt
											component="a"
											variant="body2"
											class="text-blue-500"
											target="_blank"
											rel="noopener noreferrer"
											href={`${Routes.Organisations}/${child?.id}`}
										>
											{child?.name}
										</Txt>
									{/snippet}
								</TableCell>
							</TableRow>
						{/each}
					{/if}
				</TableBody>
			</table>
		</div>
	</AccordionItem>
</Accordion>
