<script lang="ts">
	import { ContactDetailsFieldName } from '..';
	import type { ContactDetailsRowItem, ContactDetailsHeader } from '..';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import { TableCell, TableRow } from '$global/components/Table';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { Entity_Contact_Detail_Type_Enum } from '$gql/types-custom';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		index: number;
		row: ContactDetailsRowItem;
		headers: ContactDetailsHeader[];
		onChange: (row: ContactDetailsRowItem) => void;
	}

	let { index, row, headers, onChange }: Props = $props();
	let data = $derived(getPageData<PersonDetailsPageData>(page.data));

	let entityContactDetailTypes = $derived(
		data.user?.dropdowns?.entityContactDetailTypes || []
	);
	let entityContactDetailTypesOptions = $derived(
		(entityContactDetailTypes || []).map((entityContactDetailType) => ({
			value: `${entityContactDetailType.key}`,
			label: `${entityContactDetailType.name}`,
		}))
	);

	const dataCy = 'address-row';

	const handleValueChange = (event?: Event | undefined) => {
		onChange({
			...row,
			value: (event?.target as HTMLInputElement).value,
		});
	};

	const handleTypeChange = (event: SelectChangeEvent) => {
		const contactType = entityContactDetailTypes?.find(
			(entityContactDetailType) =>
				entityContactDetailType?.key === event.detail.value
		);

		onChange({
			...row,
			type: `${contactType?.name}`,
			typeKey: `${contactType?.key}`,
		});
	};

	const handleDelete = () => {
		onChange({
			...row,
			isDeleted: true,
		});
	};

	let getPlaceholder = $derived(() => {
		const PLACEHOLDER_MAP: Record<Entity_Contact_Detail_Type_Enum, string> = {
			[Entity_Contact_Detail_Type_Enum.Email]: '<EMAIL>',
			[Entity_Contact_Detail_Type_Enum.Phone]: 'Enter phone number',
			[Entity_Contact_Detail_Type_Enum.Instagram]: '@username',
			[Entity_Contact_Detail_Type_Enum.Twitter]: '@username',
			[Entity_Contact_Detail_Type_Enum.Website]: 'Enter URL',
		};

		return PLACEHOLDER_MAP[row.typeKey as Entity_Contact_Detail_Type_Enum];
	});
</script>

{#if !row.isDeleted}
	<TableRow {index} {dataCy}>
		{#each headers as header}
			{#if header.fieldName === ContactDetailsFieldName.Type}
				<TableCell {dataCy} class="py-0">
					{#snippet custom()}
						<Select
							ariaLabel="Select a contact type"
							dataCy={`${dataCy}-contact-type`}
							name="contact_type"
							placeholder="contact type"
							options={entityContactDetailTypesOptions}
							value={row.typeKey}
							size="sm"
							onchange={handleTypeChange}
						/>
					{/snippet}
				</TableCell>
			{:else if header.fieldName === ContactDetailsFieldName.Value}
				<TableCell {dataCy} class="">
					{#snippet custom()}
						<Input
							dataCy={`${dataCy}-value`}
							name="value"
							placeholder={getPlaceholder()}
							value={row.value}
							onkeyup={handleValueChange}
							size="sm"
						/>
					{/snippet}
				</TableCell>
			{:else if header.fieldName === ContactDetailsFieldName.Action}
				<TableCell {dataCy} class="">
					{#snippet custom()}
						<div class="flex items-center justify-end gap-2">
							<UpdateHistoryTooltip updateHistory={row.item} />

							<Button
								onclick={handleDelete}
								dataCy={`${dataCy}-delete`}
								class="h-[2rem] w-[2rem] px-0"
								variant="secondary"
								size="xs"
							>
								<BinIcon class="h-3 w-3" />
							</Button>
						</div>
					{/snippet}
				</TableCell>
			{/if}
		{/each}
	</TableRow>
{/if}
