<script lang="ts">
	import cloneDeep from 'lodash.clonedeep';
	import { ContactDetailsRow } from './ContactDetailsRow';
	import {
		ContactDetailsFieldName,
		type ContactDetailsHeader,
		type ContactDetailsRowItem,
		type ContactDetails,
	} from './types';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { Button } from '$global/components/Button';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import {
		TableBody,
		TableHeader,
		TableHeaderRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';

	interface Props {
		rows: ContactDetailsRowItem[];
		onChange: (rows: ContactDetailsRowItem[]) => void;
	}

	let { rows, onChange }: Props = $props();

	let rowsForm = $derived(cloneDeep(rows));

	const headers: ContactDetailsHeader[] = [
		{
			fieldName: ContactDetailsFieldName.Type,
			title: 'Contact detail type',
		},
		{
			fieldName: ContactDetailsFieldName.Value,
			title: 'Value',
		},
		{
			fieldName: ContactDetailsFieldName.Action,
			title: '',
		},
	];

	const handleChange = (index: number) => (newRow: ContactDetailsRowItem) => {
		onChange(rowsForm.map((row, i) => (i === index ? newRow : row)));
	};

	const handleClickAdd = () => {
		onChange([
			...rowsForm,
			{
				id: '',
				item: null,
				[ContactDetailsFieldName.Type]: '',
				[ContactDetailsFieldName.TypeKey]: '',
				[ContactDetailsFieldName.Value]: '',
				isNew: true,
			},
		]);
	};

	const dataCy = 'contact-details';
</script>

{#key rowsForm.length}
	<div class="h-full rounded-md border bg-white">
		<div class="p-4">
			<Txt variant="h5" class="mb-6">Contact details</Txt>
			<div class="max-lg:overflow-x-scroll">
				<table
					class="w-full table-fixed rounded-md bg-white max-lg:min-w-[1100px]"
				>
					<TableHeaderRow {dataCy}>
						{#each headers as header}
							{#if header.fieldName === ContactDetailsFieldName.Action}
								<TableHeader {dataCy}>
									{#snippet custom()}
										<div class="flex justify-end">
											<Button
												onclick={handleClickAdd}
												dataCy={`${dataCy}-add`}
												class="h-[2rem] w-[2rem] px-0"
												variant="secondary"
												size="xs"
											>
												<PlusIcon class="h-3 w-3" />
											</Button>
										</div>
									{/snippet}
								</TableHeader>
							{:else}
								<TableHeader {dataCy}>
									{#snippet custom()}
										<div class="flex items-center">
											<Txt variant="label3">
												{header.title}
											</Txt>

											<Mandatory />
										</div>
									{/snippet}
								</TableHeader>
							{/if}
						{/each}
					</TableHeaderRow>

					<TableBody {dataCy}>
						{#each rowsForm as row, index}
							<ContactDetailsRow
								{headers}
								{row}
								{index}
								onChange={handleChange(index)}
							/>
						{/each}
					</TableBody>
				</table>
			</div>
		</div>
	</div>
{/key}
