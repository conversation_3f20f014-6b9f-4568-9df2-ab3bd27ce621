import type { EntityFragmentFragment } from '$lib/queries/__generated__/entityFragment.generated';

export enum ContactDetailsFieldName {
	Type = 'type',
	TypeKey = 'typeKey',
	Value = 'value',
	Action = 'action',
}

export interface ContactDetails {
	[ContactDetailsFieldName.Type]: string;
	[ContactDetailsFieldName.TypeKey]: string;
	[ContactDetailsFieldName.Value]: string;
}

export interface ContactDetailsRowItem {
	id: string;
	item: NonNullable<EntityFragmentFragment['contact_details']>[0] | null;
	[ContactDetailsFieldName.Type]: string;
	[ContactDetailsFieldName.TypeKey]: string;
	[ContactDetailsFieldName.Value]: string;
	isDeleted?: boolean;
	isNew?: boolean;
}

export interface ContactDetailsHeader {
	fieldName: ContactDetailsFieldName;
	title: string;
}
