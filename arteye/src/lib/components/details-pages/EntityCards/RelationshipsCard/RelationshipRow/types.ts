export type RelationshipItem = {
	id?: string;
	isDeleted?: boolean;
	isNew?: boolean;
	thirdPartyEntity?: {
		id: string;
		name: string;
		subTitle: string;
	} | null;
	relationshipType?: {
		typeKey: string;
	};
	thirdPartyEntityDirection?: RelationshipDirection;
	dateUpdated?: string;
	dateCreated?: string;
	startDate: string;
	endDate: string;
	userCreated?: { firstName: string; lastName: string };
	userUpdated?: { firstName: string; lastName: string };
	notes: string;
};

export enum RelationshipFieldName {
	RelationshipType = 'relationship_type',
	Entity = 'entity',
	FromTo = 'from_to',
	StartDate = 'start_date',
	EndDate = 'end_date',
	Notes = 'notes',
}

export enum RelationshipDirection {
	From = 'from',
	To = 'to',
}

export enum EntityDirection {
	FromEntity = 'from_entity',
	ToEntity = 'to_entity',
}
