<script lang="ts">
	import { EntityAutocomplete } from './EntityAutocomplete';
	import {
		RelationshipDirection,
		RelationshipFieldName,
		type RelationshipItem,
	} from './types';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { ArtistsIdPageData } from '$routes/artists/[id]/types';

	interface Props {
		index: number;
		relationship: RelationshipItem;
		headers: { fieldName?: RelationshipFieldName }[];
		onChange: (relationship: RelationshipItem) => void;
		selectedOption?: OptionType | null;
		currentEntity: {
			id: string;
			type:
				| {
						__typename?: 'entity_type';
						key: string;
				  }
				| null
				| undefined;
			name: string;
		};
	}

	let { index, relationship, headers, onChange }: Props = $props();

	let data = $derived(getPageData<ArtistsIdPageData>(page.data));

	let relationshipTypes = $derived(
		data.user?.dropdowns?.relationshipTypes || []
	);
	let relationshipTypesOptions = $derived(
		relationshipTypes.map((relationshipType) => ({
			label: `${relationshipType?.from_entity_type?.name} / ${relationshipType?.to_entity_type?.name}`,
			value: `${relationshipType?.key}`,
		}))
	);

	let selectedOption = $state<OptionType | null>(null);

	$effect(() => {
		if (!relationship.thirdPartyEntity?.id) {
			selectedOption = null;
			return;
		}

		selectedOption = {
			line1: relationship.thirdPartyEntity?.name || '',
			line2: '',
			line3: '',
			line4: relationship.thirdPartyEntity?.id || '',
			line5: relationship.thirdPartyEntity?.subTitle || '',
		};
	});

	const dataCy = 'relationship-row';

	const handleDeleteRelationship = () => {
		onChange({
			...relationship,
			isDeleted: true,
		});
	};

	const handleRelationshipDirectionChange = (event: SelectChangeEvent) => {
		const direction = (event.detail.value ||
			RelationshipDirection.From) as RelationshipDirection;

		onChange({
			...relationship,
			thirdPartyEntityDirection: direction,
		});
	};

	const handleRelationshipTypeChange = (event: SelectChangeEvent) => {
		const relationshipType = relationshipTypes.find(
			(relationshipType) => relationshipType.key === event.detail.value
		);

		onChange({
			...relationship,
			relationshipType: {
				typeKey: relationshipType?.key || '',
			},
		});
	};

	const handleInputChange =
		(field: keyof typeof relationship) => (event?: Event | undefined) => {
			onChange({
				...relationship,
				[field]: `${(event?.target as HTMLInputElement).value}`,
			});
		};

	const handleEntityClear = () => {
		onChange({
			...relationship,
			thirdPartyEntity: null,
		});
	};

	const handleEntityChange = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const value = e.detail.value;

		onChange({
			...relationship,
			thirdPartyEntity: {
				id: value.line4 || '',
				name: value.line1 || '',
				subTitle: value.line5 || '',
			},
		});

		return Promise.resolve();
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip
						updateHistory={{
							date_created: relationship.dateCreated,
							date_updated: relationship.dateUpdated,
							user_created: {
								first_name: relationship.userCreated?.firstName,
								last_name: relationship.userCreated?.lastName,
							},
							user_updated: {
								first_name: relationship.userUpdated?.firstName,
								last_name: relationship.userUpdated?.lastName,
							},
						}}
					/>

					<Button
						onclick={handleDeleteRelationship}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === RelationshipFieldName.RelationshipType}
			<TableCell {dataCy}>
				{#snippet custom()}
					<Select
						ariaLabel="Select a relationship type"
						dataCy={`${dataCy}-relationship-type`}
						name="relationship-type"
						placeholder="Relationship Type"
						options={relationshipTypesOptions}
						value={relationship?.relationshipType?.typeKey}
						size="sm"
						onchange={handleRelationshipTypeChange}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === RelationshipFieldName.StartDate}
			<TableCell {dataCy} class="">
				{#snippet custom()}
					<Input
						type="date"
						dataCy={`${dataCy}-start-date`}
						name="start-date"
						placeholder="Start Date"
						value={getDayFromDirectus(relationship.startDate)}
						onkeyup={handleInputChange('startDate')}
						onchange={handleInputChange('startDate')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === RelationshipFieldName.EndDate}
			<TableCell {dataCy} class="">
				{#snippet custom()}
					<Input
						type="date"
						dataCy={`${dataCy}-end-date`}
						name="end-date"
						placeholder="End Date"
						value={getDayFromDirectus(relationship.endDate)}
						onkeyup={handleInputChange('endDate')}
						onchange={handleInputChange('endDate')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === RelationshipFieldName.Notes}
			<TableCell {dataCy}>
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-note`}
						name="note"
						placeholder="Note"
						value={relationship.notes}
						onkeyup={handleInputChange('notes')}
						size="sm"
						rows={3}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === RelationshipFieldName.FromTo}
			<TableCell {dataCy}>
				{#snippet custom()}
					<Select
						ariaLabel="Select a relationship direction"
						dataCy={`${dataCy}-relationship-direction`}
						name="relationship-direction"
						placeholder="Relationship Direction"
						options={[
							{ label: 'From', value: RelationshipDirection.From },
							{ label: 'To', value: RelationshipDirection.To },
						]}
						value={relationship.thirdPartyEntityDirection}
						size="sm"
						onchange={handleRelationshipDirectionChange}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === RelationshipFieldName.Entity}
			<TableCell {dataCy}>
				{#snippet custom()}
					<EntityAutocomplete
						{selectedOption}
						{dataCy}
						onRemove={handleEntityClear}
						onChange={handleEntityChange}
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
