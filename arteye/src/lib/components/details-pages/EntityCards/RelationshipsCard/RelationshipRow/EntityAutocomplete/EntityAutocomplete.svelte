<script lang="ts" module>
	export const formatEntity = (
		entity: EntitySearchQuery['entitySearch']['data'][number]
	) => {
		return {
			line1: `${entity?.name}`,
			line2: getEntityUrl(entity as Parameters<typeof getEntityUrl>[0]),
			line3: `${entity?.type?.key}`,

			line4: `${entity?.id}`,
			line5: getEntityDetails(entity as Parameters<typeof getEntityDetails>[0]),
		};
	};
</script>

<script lang="ts">
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { AutocompleteSelectedOption } from '$lib/components/AutocompleteSelectedOption';
	import {
		EntitySearchDocument,
		type EntitySearchQuery,
		type EntitySearchQueryVariables,
	} from '$lib/custom-queries/__generated__/entitySearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';
	import { getEntityUrl } from '$lib/utils/getEntityUrl/getEntityUrl';
	import { getEntityDetails } from '$lib/utils/getEntityDetails/getEntityDetails';

	let data = $derived(getPageData<PersonDetailsPageData>(page.data));

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemove?: undefined | (() => void);
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search entities',
		dataCy,
		selectedOption = null,
		onRemove = undefined,
		onChange = undefined,
	}: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): EntitySearchQueryVariables => {
		return {
			input: {
				limit: value.length < 3 ? 20 : -1,
				filters: { nameOrId: value },
			},
		};
	};

	const getOptions = (queryData: EntitySearchQuery | undefined) => {
		return [...(queryData?.entitySearch?.data || []).map(formatEntity)];
	};
</script>

<div>
	{#if selectedOption}
		<AutocompleteSelectedOption
			dataCy={`${dataCy}-entity`}
			title={selectedOption.line1 || ''}
			subTitle={selectedOption.line5 || ''}
			url={selectedOption.line2 || ''}
			{onRemove}
		/>
	{:else}
		<div class="relative">
			<QueryAutocomplete
				size="sm"
				OptionComponent={LinkOption}
				SelectedOptionComponent={LinkOption}
				name="entity"
				dataCy={`${dataCy}-entity`}
				{placeholder}
				emptyValueResponse={{ entitySearch: { data: [] } }}
				showResultsWhenEmpty={false}
				graphQlClient={gqlClientCustom}
				classes={{
					listWithOptions:
						'!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
					option: {
						line3: 'hidden',
						line4: 'hidden',
					},
				}}
				requestHeaders={getAuthorizationHeaders(data)}
				{getOptions}
				{getVariables}
				document={EntitySearchDocument}
				{value}
				selectedOption={null}
				{onChange}
			>
				{#snippet noResults()}
					<NoResults class="text-left" dataCy={`${dataCy}-entity-autocomplete`}
						>No entity found.</NoResults
					>
				{/snippet}
			</QueryAutocomplete>
		</div>
	{/if}
</div>
