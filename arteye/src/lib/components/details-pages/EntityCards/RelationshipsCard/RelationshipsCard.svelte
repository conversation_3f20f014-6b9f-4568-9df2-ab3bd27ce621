<script lang="ts">
	import dayjs from 'dayjs';
	import { RelationshipRow } from './RelationshipRow';
	import {
		RelationshipFieldName,
		type RelationshipType,
	} from './RelationshipRow';
	import type { RelationshipItem } from './RelationshipRow/types';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import Mandatory from '$global/components/InputLabel/Mandatory/Mandatory.svelte';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';

	const headers = [
		{
			title: 'Relationship Type',
			fieldName: RelationshipFieldName.RelationshipType,
		},
		{ title: 'Direction', fieldName: RelationshipFieldName.FromTo },
		{ title: 'Entity', fieldName: RelationshipFieldName.Entity },
		{ title: 'Start Date', fieldName: RelationshipFieldName.StartDate },
		{ title: 'End Date', fieldName: RelationshipFieldName.EndDate },
		{ title: 'Notes', fieldName: RelationshipFieldName.Notes },
		{ title: '' },
	];

	interface Props {
		relationships: RelationshipItem[];
		onChange: (relationships: RelationshipItem[]) => void;
		currentEntity: {
			id: string;
			type:
				| {
						__typename?: 'entity_type';
						key: string;
				  }
				| null
				| undefined;
			name: string;
		};
	}

	let { relationships, onChange, currentEntity }: Props = $props();

	const onRelationshipChange =
		(index: number) => (newRelationship: RelationshipType) => {
			onChange(
				relationships.map((relationship, i) =>
					i === index ? newRelationship : relationship
				)
			);
		};

	const dataCy = 'relationships';

	const handleRelationshipAdd = () => {
		onChange([
			...relationships,
			{
				startDate: '',
				endDate: '',
				notes: '',
				isNew: true,
			},
		]);
	};
</script>

<div class="mb-12">
	<AccordionItem
		{dataCy}
		title="Relationships"
		class="rounded-md border bg-white"
		classes={{ titleButton: 'px-4' }}
		defaultOpen
	>
		<div class="p-4 max-lg:overflow-x-scroll">
			<table class="w-full table-fixed rounded-md bg-white min-w-[1100px]">
				<TableHeaderRow {dataCy}>
					{#each headers as header}
						{#if header.fieldName}
							<TableHeader {dataCy}>
								{#snippet custom()}
									<div class="flex items-center">
										<Txt variant="label3">
											{header.title}
										</Txt>
										{#if ![RelationshipFieldName.EndDate, RelationshipFieldName.Notes].includes(header.fieldName)}
											<Mandatory />
										{/if}
									</div>
								{/snippet}
							</TableHeader>
						{:else}
							<TableHeader {dataCy} class="flex justify-end">
								{#snippet custom()}
									<Button
										onclick={handleRelationshipAdd}
										dataCy={`${dataCy}-add`}
										class="h-[2rem] w-[2rem] px-0"
										variant="secondary"
										size="xs"
									>
										<PlusIcon class="h-3 w-3" />
									</Button>
								{/snippet}
							</TableHeader>
						{/if}
					{/each}
				</TableHeaderRow>

				<TableBody {dataCy}>
					{#if relationships}
						{#each relationships as relationship, index}
							{#if !relationship.isDeleted}
								<RelationshipRow
									{currentEntity}
									{headers}
									{relationship}
									{index}
									onChange={onRelationshipChange(index)}
								/>
							{/if}
						{/each}
					{/if}
				</TableBody>
			</table>
		</div>
	</AccordionItem>
</div>
