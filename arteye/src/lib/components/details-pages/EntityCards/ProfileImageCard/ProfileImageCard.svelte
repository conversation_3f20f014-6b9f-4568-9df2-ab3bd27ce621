<script module lang="ts">
	export type ProfileImageType =
		| (NonNullable<
				NonNullable<PersonDetailsPageData['person']>['entity']
		  >['profile_image'] & {
				file?: File;
				url?: string | null;
				uploadUrlSource?: string;
		  })
		| null
		| undefined;

	export type AdditionalImagesType =
		| {
				directus_files_id: NonNullable<
					NonNullable<
						NonNullable<
							NonNullable<PersonDetailsPageData['person']>['entity']
						>['additional_images']
					>[number]
				>['directus_files_id'] & {
					file?: File;
					url?: string | null;
					isDeleted?: boolean;
					uploadUrlSource?: string;
				};
		  }[]
		| null
		| undefined;
</script>

<script lang="ts">
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import type { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
	import { Dropzone } from '$global/components/Dropzone';
	import { Tabs } from '$global/components/Tabs';
	import { getSuperForm } from '$global/utils/getSuperForm';
	import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';

	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		profileImage: ProfileImageType;
		additionalImages: AdditionalImagesType;
		onProfileImageChange: (image: ProfileImageType) => void;
		onAdditionalImagesChange: (image: AdditionalImagesType) => void;
		dropzoneUrlDialogForm: Awaited<
			ReturnType<typeof getDropzoneUrlDialogSuperform>
		>['dropzoneUrlDialogForm'];
	}

	let {
		profileImage,
		additionalImages,
		onProfileImageChange,
		onAdditionalImagesChange,
		dropzoneUrlDialogForm,
	}: Props = $props();

	const tabs = [
		{
			id: '1',
			title: 'Profile image',
		},
		{
			id: '2',
			title: 'Additional images',
		},
	];

	let activeTab = $state(0);

	const dataCy = 'profile-image-card';

	const handleClickDeleteImage = () => {
		onProfileImageChange(null);
	};

	const handleClickDeleteAdditionalImage = (id: string) => {
		onAdditionalImagesChange(
			additionalImages?.map((additionalImage) =>
				additionalImage?.directus_files_id?.id === id
					? {
							directus_files_id: {
								...additionalImage.directus_files_id,
								isDeleted: true,
							},
						}
					: additionalImage
			)
		);
	};

	const handleSubmitProfileImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		onProfileImageChange({
			file: newFile,
			url: base64Image.url,
			filename_download: '',
			id: '',
			storage: '',
		});
	};

	const handleSubmitProfileImageUrl = (uploadUrlSource: string) => {
		onProfileImageChange({
			file: undefined,
			url: '',
			filename_download: '',
			id: '',
			storage: '',
			uploadUrlSource,
		});
	};

	const handleSubmitAdditionalImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		onAdditionalImagesChange([
			...(additionalImages || []),
			{
				directus_files_id: {
					file: newFile,
					url: base64Image.url,
					filename_download: '',
					id: `new_${
						additionalImages?.filter((additionalImage) =>
							additionalImage?.directus_files_id?.id?.startsWith('new')
						)?.length
					}`,
					storage: '',
				},
			},
		]);
	};

	const handleSubmitAdditionalImageUrl = (uploadUrlSource: string) => {
		onAdditionalImagesChange([
			...(additionalImages || []),
			{
				directus_files_id: {
					file: undefined,
					url: '',
					filename_download: '',
					id: `new_${
						additionalImages?.filter((additionalImage) =>
							additionalImage?.directus_files_id?.id?.startsWith('new')
						)?.length
					}`,
					storage: '',
					uploadUrlSource,
				},
			},
		]);
	};
</script>

<div class="h-full rounded-md border bg-white p-4 pt-0">
	<div class="overflow-x-auto w-full">
		<Tabs {dataCy} {tabs} bind:activeTab />
	</div>

	{#if !activeTab}
		<div
			class="relative flex h-[200px] w-full items-center justify-center bg-gray-200 p-4"
		>
			{#if profileImage}
				<img
					class="max-h-full"
					src={profileImage?.url || profileImage?.uploadUrlSource}
					alt="profile"
				/>

				<div
					class="absolute bottom-2 right-2 flex w-[70px] items-center justify-end gap-2"
				>
					<div
						class="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-gray-200"
					>
						<UpdateHistoryTooltip
							updateHistory={{
								date_updated: profileImage.modified_on,
								date_created: profileImage.created_on,
								user_updated: profileImage.modified_by,
								user_created: profileImage.uploaded_by,
							}}
						/>
					</div>

					<Button
						dataCy={`${dataCy}-delete-profile`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						onclick={handleClickDeleteImage}
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			{:else}
				<Dropzone
					{dropzoneUrlDialogForm}
					{getSuperForm}
					maxSize={100000000}
					multiple={false}
					accept={['image/jpeg', 'image/png']}
					dataCy={`${dataCy}`}
					onSubmitFiles={handleSubmitProfileImage}
					onSubmitUrl={handleSubmitProfileImageUrl}
					showFiles={false}
				/>
			{/if}
		</div>
	{:else}
		<div class="mb-4 grid grid-cols-3 gap-4">
			{#if additionalImages}
				{#each additionalImages as additionalImage}
					{#if !additionalImage?.directus_files_id?.isDeleted}
						<div
							class="relative flex flex-col border border-gray-200 pb-[100%]"
						>
							<div
								class="absolute left-0 top-0 flex h-full w-full items-center justify-center p-1"
							>
								<img
									src={additionalImage?.directus_files_id?.url ||
										additionalImage?.directus_files_id?.uploadUrlSource}
									class="max-h-full max-w-full"
									alt="additional"
								/>

								<div
									class="absolute bottom-2 right-2 flex w-[70px] items-center justify-end gap-2"
								>
									<div
										class="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-white"
									>
										<UpdateHistoryTooltip
											updateHistory={{
												date_updated:
													additionalImage?.directus_files_id.modified_on,
												date_created:
													additionalImage?.directus_files_id.created_on,
												user_updated:
													additionalImage?.directus_files_id.modified_by,
												user_created:
													additionalImage?.directus_files_id.uploaded_by,
											}}
										/>
									</div>

									<Button
										dataCy={`${dataCy}-delete-additional`}
										class="h-[1.5rem] w-[1.5rem] px-0"
										variant="secondary"
										size="xs"
										onclick={() =>
											handleClickDeleteAdditionalImage(
												additionalImage?.directus_files_id?.id
											)}
									>
										<BinIcon class="h-3 w-3" />
									</Button>
								</div>
							</div>
						</div>
					{/if}
				{/each}
			{/if}
		</div>
		<Dropzone
			{dropzoneUrlDialogForm}
			{getSuperForm}
			maxSize={100000000}
			multiple={false}
			accept={['image/jpeg', 'image/png']}
			dataCy={`${dataCy}`}
			onSubmitFiles={handleSubmitAdditionalImage}
			onSubmitUrl={handleSubmitAdditionalImageUrl}
			showFiles={false}
		/>
	{/if}
</div>
