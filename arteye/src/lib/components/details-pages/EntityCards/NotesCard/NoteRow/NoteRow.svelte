<script lang="ts">
	import { type NoteType, NoteFieldName } from './types';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		index: number;
		entityNote: NoteType;
		headers: { fieldName?: NoteFieldName }[];
		onChange: (address: NoteType) => void;
	}

	let { index, entityNote, headers, onChange }: Props = $props();

	let data = $derived(getPageData<PersonDetailsPageData>(page.data));

	let entityNoteTypes = $derived(data.user?.dropdowns?.entityNoteTypes || []);
	let entityNoteTypesOptions = $derived(
		entityNoteTypes.map((entityNoteType) => ({
			label: `${entityNoteType?.name}`,
			value: `${entityNoteType?.key}`,
		}))
	);

	const dataCy = 'note-row';

	const handleDeleteEntityNote = () => {
		onChange({
			...entityNote,
			isDeleted: true,
		});
	};

	const handleChangeNoteType = (event: SelectChangeEvent) => {
		const noteType = entityNoteTypes.find(
			(entityNoteType) => entityNoteType.key === event.detail.value
		);

		onChange({
			...entityNote,
			type: noteType,
		});
	};

	const handleDateChange =
		(field: keyof typeof entityNote) => (event: Event) => {
			onChange({
				...entityNote,
				[field]: `${(event.target as HTMLInputElement).value}`,
			});
		};

	const handleInputChange =
		(field: keyof typeof entityNote) => (event?: Event | undefined) => {
			onChange({
				...entityNote,
				[field]: (event?.target as HTMLInputElement).value,
			});
		};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if !header.fieldName}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={entityNote} />

					<Button
						onclick={handleDeleteEntityNote}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === NoteFieldName.NoteType}
			<TableCell {dataCy} class="align-top">
				{#snippet custom()}
					<Select
						ariaLabel="Select a note type"
						dataCy={`${dataCy}-note-type`}
						name="note-type"
						placeholder="Note Type"
						options={entityNoteTypesOptions}
						value={entityNote.type?.key}
						size="sm"
						onchange={handleChangeNoteType}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === NoteFieldName.SourceDate}
			<TableCell {dataCy} class="align-top">
				{#snippet custom()}
					<Input
						type="date"
						dataCy={`${dataCy}-timestamp`}
						name="timestamp"
						placeholder="timestamp"
						value={getDayFromDirectus(entityNote.timestamp)}
						onkeyup={handleDateChange('timestamp')}
						onchange={handleDateChange('timestamp')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === NoteFieldName.Notes}
			<TableCell {dataCy}>
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-note`}
						name="note"
						placeholder="Note"
						value={entityNote.note}
						onkeyup={handleInputChange('note')}
						size="sm"
						rows={3}
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
