<script lang="ts" module>
	export type NotesType = NoteType[];
</script>

<script lang="ts">
	import dayjs from 'dayjs';
	import { NoteRow } from './NoteRow';
	import { NoteFieldName, type NoteType } from './NoteRow';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';

	const headers = [
		{ title: 'Note Type', fieldName: NoteFieldName.NoteType },
		{ title: 'Source Date', fieldName: NoteFieldName.SourceDate },
		{ title: 'Notes', fieldName: NoteFieldName.Notes },
		{ title: '' },
	];

	interface Props {
		entityNotes: NotesType;
		onChange: (notes: NotesType) => void;
	}

	let { entityNotes = $bindable(), onChange }: Props = $props();

	const onEntityNoteRowChange = (index: number) => (note: NoteType) => {
		onChange(
			entityNotes.map((entityNote, i) => (i === index ? note : entityNote))
		);
	};

	const dataCy = 'notes';

	const handleAddressAdd = () => {
		entityNotes = [
			...entityNotes,
			{
				type: null,
				timestamp: dayjs().format('YYYY-MM-DD'),
				note: '',
				isNew: true,
			} as NoteType,
		];
	};
</script>

<AccordionItem
	dataCy="notes"
	title="Notes"
	class="rounded-md border bg-white"
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4 max-lg:overflow-x-scroll">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1100px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName}
						<TableHeader {dataCy}>
							{#snippet custom()}
								<div class="flex items-center">
									<Txt variant="label3">
										{header.title}
									</Txt>

									<Mandatory />
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleAddressAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if entityNotes}
					{#each entityNotes as entityNote, index}
						{#if !entityNote.isDeleted}
							<NoteRow
								{headers}
								{entityNote}
								{index}
								onChange={onEntityNoteRowChange(index)}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</AccordionItem>
