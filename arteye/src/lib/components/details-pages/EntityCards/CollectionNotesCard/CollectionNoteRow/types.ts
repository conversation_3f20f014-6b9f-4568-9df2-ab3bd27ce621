import type { PersonDetailsPageData } from '$routes/people/[id]/types';

export type CollectionNoteType = NonNullable<
	NonNullable<
		NonNullable<PersonDetailsPageData['person']>['entity']
	>['collection_notes']
>[number] & { isDeleted?: boolean; isNew?: boolean };

export enum CollectionNoteFieldName {
	SourceDate = 'source_date',
	NoteType = 'note_type',
	Notes = 'notes',
	ArtistSeries = 'artist_series',
	Action = 'action',
}
