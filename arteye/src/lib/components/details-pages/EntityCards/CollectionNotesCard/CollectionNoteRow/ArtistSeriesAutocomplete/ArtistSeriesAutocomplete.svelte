<script lang="ts" module>
	const getArtistSubtitle = ({
		countryNationality,
		yearBirth,
		yearDeath,
	}: {
		countryNationality: string;
		yearBirth: number;
		yearDeath: number;
	}) => {
		const yearBirthDeath = getYearBirthDeathString({
			yearBirth: yearBirth,
			yearDeath: yearDeath,
			withWrapper: false,
		});

		if (countryNationality && yearBirthDeath) {
			return `${countryNationality}, ${yearBirthDeath}`;
		}

		if (countryNationality) {
			return `${countryNationality}`;
		}

		if (yearBirthDeath) {
			return `${yearBirthDeath}`;
		}

		return '';
	};

	export const formatArtist = (
		artist: GetArtistSeriesQuery['artist'][number] & {
			legacy_id?: string | null | undefined;
		}
	) => {
		return {
			line1: `${artist.person?.entity?.name}`,
			line2: `${Config.Domain}${Routes.Artists}/${artist.id}`,
			line3: getArtistSubtitle({
				countryNationality:
					artist.person?.nationalities?.[0]?.country?.country_nationality || '',
				yearBirth: artist.person?.year_birth || 0,
				yearDeath: artist.person?.year_death || 0,
			}),
			line4: `${artist.id}`,
			line5: `${artist.person?.id}`,
			line6: 'artist',
			...(artist.reference_id && {
				line7: `Ref ID: ${artist.reference_id}`,
			}),
			// ...(artist.legacy_id && {
			// 	line8: `Legacy ID: ${artist.legacy_id}`,
			// }),
		};
	};

	export const formatArtworkSeries = (
		artworkSeries: GetArtistSeriesQuery['artwork_series'][number]
	) => {
		const getSubtitle = () => {
			const crid = artworkSeries.crid;
			const artist = artworkSeries.artists?.[0]?.artist_id;
			const artistName = artist?.person?.entity?.name || 'Unknown';
			const nationality =
				artist?.person?.nationalities?.[0]?.country?.country_nationality || '';

			const artistInfo =
				getArtistSubtitle({
					countryNationality: nationality,
					yearBirth: artist?.person?.year_birth || 0,
					yearDeath: artist?.person?.year_death || 0,
				}) || '-';

			if (crid && artistName && artistInfo) {
				return `${crid} (${artistName}, ${artistInfo})`;
			}

			if (crid && artistName) {
				return `${crid} (${artistName})`;
			}

			if (crid && artistInfo) {
				return `${crid} (${artistInfo})`;
			}

			if (artistName && artistInfo) {
				return `(${artistName}, ${artistInfo})`;
			}

			if (crid) {
				return `${crid}`;
			}

			return '';
		};

		return {
			line1: `${artworkSeries.title}`,
			line2: `${Config.Domain}${Routes.Series}/${artworkSeries.id}`,
			line3: getSubtitle(),
			line4: `${artworkSeries.title}`,
			line5: `${artworkSeries.is_heni_series}`,
			line6: 'series',
			line7: `${artworkSeries.id}`,
		};
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { writable } from 'svelte/store';
	import { page } from '$app/state';
	import { CrossIcon } from '$global/assets/icons/CrossIcon';
	import { SearchIcon } from '$global/assets/icons/SearchIcon';
	import { PromiseAutocomplete } from '$global/components/PromiseAutocomplete';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { LinkOption } from '$global/components/QueryAutocomplete/LinkOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { isUuidValid } from '$global/utils/isUuidValid/isUuidValid';
	import { Status_Enum } from '$gql/types-custom';
	import { Config } from '$lib/constants/config';
	import { Routes } from '$lib/constants/routes';
	import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
	import { gqlClient } from '$lib/gqlClient';
	import { gqlClientCustom } from '$lib/gqlClientCustom';
	import { GetArtistSeriesDocument } from '$lib/queries/__generated__/getArtistSeries.generated';
	import {
		type GetArtistSeriesQueryVariables,
		type GetArtistSeriesQuery,
	} from '$lib/queries/__generated__/getArtistSeries.generated';
	import { getYearBirthDeathString } from '$lib/utils/getYearBirthDeathString/getYearBirthDeathString';
	import { isOnDev } from '$lib/utils/isOnDev/isOnDev';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		placeholder?: string;
		dataCy: string;
		selectedOption?: OptionType | null;
		onRemoveSelectedOption: (() => void) | undefined;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search artist or artwork series',
		dataCy,
		selectedOption = $bindable(null),
		onRemoveSelectedOption,
		onChange = undefined,
	}: Props = $props();

	let value = $state(writable(''));

	const getVariables = (value: string): GetArtistSeriesQueryVariables => {
		return {
			artistFilter: {
				_and: [
					...(() => {
						if (!value) {
							return [];
						}

						if (isUuidValid(value)) {
							return [{ id: { _eq: value } }];
						}

						if (!isNaN(+value)) {
							return [{ reference_id: { _eq: value } }];
						}

						return [{ person: { entity: { name: { _icontains: value } } } }];
					})(),
					{ status: { key: { _neq: Status_Enum.Archived } } },
				],
			},
			seriesFilter: {
				_and: [
					{
						_or: value ? [{ title: { _icontains: value } }] : [],
					},
					{ status: { key: { _neq: Status_Enum.Archived } } },
				],
			},
		};
	};

	const getOptions = async (value: string) => {
		const res = await gqlClient.request(
			GetArtistSeriesDocument,
			getVariables(value),
			getAuthorizationHeaders(data)
		);

		return [
			...(res?.artist || []).map(formatArtist),
			...(res?.artwork_series || []).map(formatArtworkSeries),
		];

		// if (isOnDev()) {
		// 	return [
		// 		...(res?.artist || []).map(formatArtist),
		// 		...(res?.artwork_series || []).map(formatArtworkSeries),
		// 	];
		// }

		// const legacyIdsRes = await Promise.all(
		// 	res?.artist?.map((art) =>
		// 		gqlClientCustom.request(
		// 			GetLegacyIdDocument,
		// 			{ id: art?.id, collection: 'artist' },
		// 			getAuthorizationHeaders(data)
		// 		)
		// 	)
		// );

		// return [
		// 	...(res?.artist || []).map((artist, i) =>
		// 		formatArtist({
		// 			...artist,
		// 			legacy_id: legacyIdsRes[i]?.getLegacyId?.legacyId,
		// 		})
		// 	),
		// 	...(res?.artwork_series || []).map(formatArtworkSeries),
		// ];
	};
</script>

<div
	class={classNames('relative', {
		'flex justify-between rounded border border-gray-200 bg-white px-3 py-2':
			!!selectedOption,
	})}
>
	<PromiseAutocomplete
		size="sm"
		{onRemoveSelectedOption}
		class={classNames({ 'max-w-[calc(100%-16px)]': !!selectedOption })}
		OptionComponent={LinkOption}
		SelectedOptionComponent={LinkOption}
		name="gallery"
		dataCy={`${dataCy}-artist-series`}
		{placeholder}
		showResultsWhenEmpty={false}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			input: 'pl-8',
			option: {
				line4: 'hidden',
				line5: 'hidden',
				line3: 'text-gray-500',
				line6: 'hidden',
			},
			selectedOption: {
				line4: 'hidden',
				line5: 'hidden',
				line3: 'text-gray-500',
				line6: 'hidden',
			},
		}}
		{getOptions}
		{value}
		bind:selectedOption
		{onChange}
	>
		{#snippet icon()}
			<div
				class={'absolute left-2.5 top-[50%] z-10 -translate-y-1/2 text-gray-900'}
			>
				<SearchIcon class={'h-4 w-4'} data-cy={dataCy} />
			</div>
		{/snippet}

		{#snippet noResults()}
			<NoResults
				class="text-left"
				dataCy={`${dataCy}-artist-series-autocomplete`}
				>No artist or series found.</NoResults
			>{/snippet}
	</PromiseAutocomplete>

	{#if !!selectedOption}
		<button
			onclick={() => {
				selectedOption = null;
				if (onRemoveSelectedOption) {
					onRemoveSelectedOption();
				}
			}}
		>
			<CrossIcon class="h-3 w-3" />
		</button>
	{/if}
</div>
