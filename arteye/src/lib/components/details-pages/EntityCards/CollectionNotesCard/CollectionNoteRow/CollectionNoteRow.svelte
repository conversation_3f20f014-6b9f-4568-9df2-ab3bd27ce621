<script lang="ts">
	import { ArtistSeriesAutocomplete } from './ArtistSeriesAutocomplete';
	import { type CollectionNoteType, CollectionNoteFieldName } from './types';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { Select, type SelectChangeEvent } from '$global/components/Select';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import { getDayFromDirectus } from '$global/utils/getDayFromDirectus/getDayFromDirectus';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';
	import type { PersonDetailsPageData } from '$routes/people/[id]/types';

	interface Props {
		index: number;
		collectionEntityNote: CollectionNoteType;
		headers: { fieldName: CollectionNoteFieldName }[];
		onChange: (address: CollectionNoteType) => void;
		selectedOption?: OptionType | null;
	}

	let {
		index,
		collectionEntityNote,
		headers,
		onChange,
		selectedOption = $bindable(null),
	}: Props = $props();

	let data = $derived(getPageData<PersonDetailsPageData>(page.data));

	let collectionEntityTypes = $derived(
		data.user?.dropdowns?.entityCollectionNoteTypes || []
	);

	let collectionEntityTypesOptions = $derived(
		collectionEntityTypes.map((collectionEntityType) => ({
			label: `${collectionEntityType?.name}`,
			value: `${collectionEntityType?.key}`,
		}))
	);

	const dataCy = 'collection-note-row';

	const handleDeleteEntityNote = () => {
		onChange({
			...collectionEntityNote,
			isDeleted: true,
		});
	};

	const handleChangeNoteType = (event: SelectChangeEvent) => {
		const noteType = collectionEntityTypes.find(
			(collectionEntityType) => collectionEntityType.key === event.detail.value
		);

		onChange({
			...collectionEntityNote,
			type: noteType,
		});
	};

	const handleDateChange =
		(field: keyof typeof collectionEntityNote) =>
		(event?: Event | undefined) => {
			onChange({
				...collectionEntityNote,
				[field]: `${(event?.target as HTMLInputElement).value}`,
			});
		};

	const handleArtistSeriesChange = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const option = e.detail.value;
		const payload =
			option.line6 === 'series'
				? {
						artist: null,
						artwork_series: {
							id: `${option.line7}`,
							title: `${option.line4}`,
							is_heni_series: option.line5 === 'true',
						},
					}
				: {
						artwork_series: null,
						artist: {
							id: `${option.line4}`,
						},
					};

		onChange({
			...collectionEntityNote,
			...payload,
		});

		return Promise.resolve();
	};

	const handleInputChange =
		(field: keyof typeof collectionEntityNote) =>
		(event?: Event | undefined) => {
			onChange({
				...collectionEntityNote,
				[field]: (event?.target as HTMLInputElement).value,
			});
		};

	const handleClearArtistSeries = () => {
		onChange({
			...collectionEntityNote,
			artist: null,
			artwork_series: null,
		});
	};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if header.fieldName === CollectionNoteFieldName.Action}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={collectionEntityNote} />

					<Button
						onclick={handleDeleteEntityNote}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === CollectionNoteFieldName.NoteType}
			<TableCell {dataCy} class="align-top">
				{#snippet custom()}
					<Select
						ariaLabel="Select a note type"
						dataCy={`${dataCy}-note-type`}
						name="note-type"
						placeholder="Note Type"
						options={collectionEntityTypesOptions}
						value={collectionEntityNote.type?.key}
						size="sm"
						onchange={handleChangeNoteType}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === CollectionNoteFieldName.SourceDate}
			<TableCell {dataCy} class="align-top">
				{#snippet custom()}
					<Input
						type="date"
						dataCy={`${dataCy}-timestamp`}
						name="timestamp"
						placeholder="timestamp"
						value={getDayFromDirectus(collectionEntityNote.timestamp)}
						onkeyup={handleDateChange('timestamp')}
						onchange={handleDateChange('timestamp')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === CollectionNoteFieldName.ArtistSeries}
			<TableCell {dataCy} class="align-top">
				{#snippet custom()}
					<ArtistSeriesAutocomplete
						onRemoveSelectedOption={handleClearArtistSeries}
						onChange={handleArtistSeriesChange}
						{dataCy}
						bind:selectedOption
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === CollectionNoteFieldName.Notes}
			<TableCell {dataCy}>
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-note`}
						name="note"
						placeholder="Note"
						value={collectionEntityNote.note}
						onkeyup={handleInputChange('note')}
						size="sm"
						rows={3}
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
