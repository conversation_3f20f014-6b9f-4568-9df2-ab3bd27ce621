<script lang="ts" module>
	export type CollectionNotesType = CollectionNoteType[];
</script>

<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { onMount } from 'svelte';
	import { CollectionNoteRow } from './CollectionNoteRow';
	import {
		CollectionNoteFieldName,
		type CollectionNoteType,
	} from './CollectionNoteRow';
	import {
		formatArtist,
		formatArtworkSeries,
	} from './CollectionNoteRow/ArtistSeriesAutocomplete/ArtistSeriesAutocomplete.svelte';
	import { page } from '$app/state';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { InfoTooltip } from '$global/components/InfoTooltip';
	import { Mandatory } from '$global/components/InputLabel/Mandatory';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	// import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	// import { GetLegacyIdDocument } from '$lib/custom-queries/__generated__/getLegacyId.generated';
	// import { gqlClientCustom } from '$lib/gqlClientCustom';
	import type { EntityFragmentFragment } from '$lib/queries/__generated__/entityFragment.generated';
	// import { isOnDev } from '$lib/utils/isOnDev/isOnDev';

	const headers = [
		{ title: 'Note Type', fieldName: CollectionNoteFieldName.NoteType },
		{
			title: 'Source Date',
			fieldName: CollectionNoteFieldName.SourceDate,
			tooltip: 'tbc',
		},
		{ title: 'Artist/Series', fieldName: CollectionNoteFieldName.ArtistSeries },
		{ title: 'Notes', fieldName: CollectionNoteFieldName.Notes },
		{ title: '', fieldName: CollectionNoteFieldName.Action },
	];

	interface Props {
		originalCollectionEntityNotes: EntityFragmentFragment['collection_notes'];
		collectionEntityNotes: CollectionNotesType;
		onChange: (notes: CollectionNotesType) => void;
	}

	let {
		originalCollectionEntityNotes,
		collectionEntityNotes = $bindable(),
		onChange,
	}: Props = $props();

	let loadingLegacyIds = $state(false);

	let selectedOptions: (OptionType | null)[] = $state(
		(originalCollectionEntityNotes || []).map((collectionNote) => {
			if (!collectionNote?.artist && !collectionNote?.artwork_series) {
				return null;
			}

			if (collectionNote?.artist) {
				return formatArtist(collectionNote.artist);
			}

			if (collectionNote?.artwork_series) {
				return formatArtworkSeries(collectionNote.artwork_series);
			}

			return null;
		})
	);

	// onMount(() => {
	// 	const fetchLegacyIds = async () => {
	// 		if (!isOnDev()) {
	// 			const legacyIdRes = await Promise.all(
	// 				(collectionEntityNotes || [])?.map((note) =>
	// 					note?.artist?.id
	// 						? gqlClientCustom.request(
	// 								GetLegacyIdDocument,
	// 								{ id: note?.artist?.id, collection: 'artist' },
	// 								getAuthorizationHeaders(
	// 									page.data as { user: { access_token: string } }
	// 								)
	// 							)
	// 						: null
	// 				)
	// 			);

	// 			legacyIdRes.forEach((legacyIdR, i) => {
	// 				if (legacyIdR) {
	// 					selectedOptions[i] = formatArtist({
	// 						...collectionEntityNotes[i].artist,
	// 						legacy_id: legacyIdR?.getLegacyId?.legacyId,
	// 					} as Parameters<typeof formatArtist>[0]);
	// 				}
	// 			});
	// 		}

	// 		loadingLegacyIds = false;
	// 	};

	// 	fetchLegacyIds();
	// });

	const onEntitCollectionyNoteRowChange =
		(index: number) => (collectionNote: CollectionNoteType) => {
			onChange(
				collectionEntityNotes.map((collectionEntityNote, i) =>
					i === index ? collectionNote : collectionEntityNote
				)
			);
		};

	const dataCy = 'collection-notes';

	const handleAddressAdd = () => {
		collectionEntityNotes = [
			...collectionEntityNotes,
			{
				type: null,
				timestamp: dayjs().format('YYYY-MM-DD'),
				note: '',
				isNew: true,
			} as CollectionNoteType,
		];

		selectedOptions = [...selectedOptions, null];
	};
</script>

<AccordionItem
	dataCy="collection-notes"
	title="Collection notes"
	class={classNames('rounded-md border bg-white', {
		'pointer-events-none': loadingLegacyIds,
	})}
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<div class="p-4 max-lg:overflow-x-scroll">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1100px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName === CollectionNoteFieldName.Action}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleAddressAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy}>
							{#snippet custom()}
								<div class="flex items-center gap-2">
									<span class="flex items-center">
										<Txt variant="label3">
											{header.title}
										</Txt>
										<Mandatory />
									</span>

									{#if header.tooltip}
										<InfoTooltip
											dataCy={`${dataCy}-${header.fieldName}`}
											placement="top-end"
											content="tbc"
										/>
									{/if}
								</div>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if collectionEntityNotes}
					{#each collectionEntityNotes as collectionEntityNote, index}
						{#if !collectionEntityNote.isDeleted}
							<CollectionNoteRow
								{headers}
								{collectionEntityNote}
								{index}
								bind:selectedOption={selectedOptions[index]}
								onChange={onEntitCollectionyNoteRowChange(index)}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</AccordionItem>
