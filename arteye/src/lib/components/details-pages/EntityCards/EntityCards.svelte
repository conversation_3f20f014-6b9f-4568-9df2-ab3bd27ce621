<script lang="ts">
	import { AddressesCard, type AddressesType } from './AddressesCard';
	import { CollectionNotesCard } from './CollectionNotesCard';
	import type { CollectionNotesType } from './CollectionNotesCard/CollectionNotesCard.svelte';
	import { NotesCard } from './NotesCard';
	import { type NotesType } from './NotesCard/NotesCard.svelte';
	import {
		type AdditionalImagesType,
		ProfileImageCard,
		type ProfileImageType,
	} from './ProfileImageCard';
	import { RelationshipsCard } from './RelationshipsCard';
	import type { RelationshipItem } from './RelationshipsCard/RelationshipRow/types';
	import { Accordion } from '$global/components/Accordion';
	import type { getDropzoneUrlDialogSuperform } from '$global/components/Dropzone';
	import type { ContactDetailsRowItem } from '$lib/components/details-pages/EntityCards/ContactDetailsCard';
	import { ContactDetailsCard } from '$lib/components/details-pages/EntityCards/ContactDetailsCard';
	import { type EntityFragmentFragment } from '$lib/queries/__generated__/entityFragment.generated';

	interface Props {
		profileImage: ProfileImageType;
		onProfileImageChange: (image: ProfileImageType) => void;
		additionalImages: AdditionalImagesType;
		onAdditionalImagesChange: (image: AdditionalImagesType) => void;
		contactDetailsRows: ContactDetailsRowItem[];
		onContactDetailsChange: (rows: ContactDetailsRowItem[]) => void;
		originalEntityAddresses: EntityFragmentFragment['addresses'];
		entityAddresses: AddressesType;
		onEntityAddressesChange: (addresses: AddressesType) => void;
		entityNotes: NotesType;
		onEntityNotesChange: (notes: NotesType) => void;
		originalCollectionEntityNotes: EntityFragmentFragment['collection_notes'];
		collectionEntityNotes: CollectionNotesType;
		onCollectionEntityNotesChange: (notes: CollectionNotesType) => void;
		relationships: RelationshipItem[];
		onRelationshipsChange: (relationships: RelationshipItem[]) => void;
		currentEntity: {
			id: string;
			type:
				| {
						__typename?: 'entity_type';
						key: string;
				  }
				| null
				| undefined;
			name: string;
		};
		dropzoneUrlDialogForm: Awaited<
			ReturnType<typeof getDropzoneUrlDialogSuperform>
		>['dropzoneUrlDialogForm'];
	}

	let {
		profileImage,
		onProfileImageChange,
		additionalImages,
		onAdditionalImagesChange,
		contactDetailsRows,
		onContactDetailsChange,
		originalEntityAddresses,
		entityAddresses,
		onEntityAddressesChange,
		entityNotes,
		onEntityNotesChange,
		originalCollectionEntityNotes,
		collectionEntityNotes,
		onCollectionEntityNotesChange,
		relationships,
		onRelationshipsChange,
		currentEntity,
		dropzoneUrlDialogForm,
	}: Props = $props();
</script>

<div class="mb-4 flex flex-col lg:grid grid-cols-3 gap-4">
	<div class="col-span-1">
		<ProfileImageCard
			{profileImage}
			{onProfileImageChange}
			{onAdditionalImagesChange}
			{additionalImages}
			{dropzoneUrlDialogForm}
		/>
	</div>

	<div class="col-span-2">
		<ContactDetailsCard
			rows={contactDetailsRows}
			onChange={onContactDetailsChange}
		/>
	</div>
</div>

<Accordion class="mb-4 flex flex-col gap-4" multiple>
	<AddressesCard
		{originalEntityAddresses}
		{entityAddresses}
		onChange={onEntityAddressesChange}
	/>
	<NotesCard {entityNotes} onChange={onEntityNotesChange} />
	<CollectionNotesCard
		{originalCollectionEntityNotes}
		{collectionEntityNotes}
		onChange={onCollectionEntityNotesChange}
	/>
	<RelationshipsCard
		{currentEntity}
		{relationships}
		onChange={onRelationshipsChange}
	/>
</Accordion>
