<script module lang="ts">
	export type AddressesType = AddressType[];
</script>

<script lang="ts">
	import {
		AddressRow,
		type AddressType,
		PersonAddressesFieldName,
	} from './AddressRow';
	import { formatLocation } from './AddressRow/CityAutocomplete';
	import { PlusIcon } from '$global/assets/icons/PlusIcon';
	import { AccordionItem } from '$global/components/Accordion';
	import { Button } from '$global/components/Button';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableBody,
		TableHeader,
		TableHeaderRow,
	} from '$global/components/Table';
	import { Txt } from '$global/components/Txt';
	import { formatCountry } from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CountrySelectedOption';
	import { type EntityFragmentFragment } from '$lib/queries/__generated__/entityFragment.generated';

	interface Props {
		originalEntityAddresses: EntityFragmentFragment['addresses'];
		entityAddresses: AddressesType;
		onChange: (addresses: AddressesType) => void;
	}

	let { originalEntityAddresses, entityAddresses, onChange }: Props = $props();

	let selectedOptions: (OptionType | null)[] = $state(
		(originalEntityAddresses || []).map((address) => {
			if (address?.city) {
				return formatLocation({
					...address?.city,
					country: address?.country || null,
				});
			}

			return null;
		})
	);

	$effect(() => {
		selectedOptions = (originalEntityAddresses || []).map((address) => {
			if (address?.city) {
				return formatLocation({
					...address?.city,
					country: address?.country || null,
				});
			}

			return null;
		});
	});

	let countrySelectedOptions: (OptionType | null)[] = $state(
		(originalEntityAddresses || []).map((address) => {
			if (address?.country) {
				return formatCountry(address?.country);
			}

			return null;
		})
	);

	$effect(() => {
		countrySelectedOptions = (originalEntityAddresses || []).map((address) => {
			if (address?.country) {
				return formatCountry(address?.country);
			}

			return null;
		});
	});

	const onAddressRowChange = (index: number) => (address: AddressType) => {
		onChange(
			entityAddresses.map((entityAddress, i) =>
				i === index ? address : entityAddress
			)
		);
	};

	const handleAddressAdd = () => {
		selectedOptions = [...selectedOptions, null];
		countrySelectedOptions = [...countrySelectedOptions, null];

		onChange([
			...entityAddresses,
			{
				id: '',
				line_1: '',
				line_2: '',
				line_3: '',
				post_code: '',
				country: null,
				city: null,
			},
		]);
	};

	const dataCy = 'addresses';

	const headers = [
		{
			fieldName: PersonAddressesFieldName.Country,
			title: 'Country',
		},
		{
			fieldName: PersonAddressesFieldName.CountyOrState,
			title: 'County/State',
		},
		{
			fieldName: PersonAddressesFieldName.City,
			title: 'Town/City',
		},
		{
			fieldName: PersonAddressesFieldName.Line1,
			title: 'Street address 1',
		},
		{
			fieldName: PersonAddressesFieldName.Line2,
			title: 'Street address 2',
		},
		{
			fieldName: PersonAddressesFieldName.Line3,
			title: 'Street address 3',
		},
		{
			fieldName: PersonAddressesFieldName.PostCode,
			title: 'Postcode/Zipcode',
		},
		{
			fieldName: PersonAddressesFieldName.Action,
			title: '',
		},
	];
</script>

<AccordionItem
	dataCy="addresses"
	title="Addresses"
	class="rounded-md border bg-white"
	classes={{ titleButton: 'px-4' }}
	defaultOpen
>
	<Txt variant="body3" class="px-4">
		Please provide at least one input per address.
	</Txt>
	<div class="p-4 max-lg:overflow-x-scroll">
		<table class="w-full table-fixed rounded-md bg-white min-w-[1200px]">
			<TableHeaderRow {dataCy}>
				{#each headers as header}
					{#if header.fieldName !== PersonAddressesFieldName.Action}
						<TableHeader {dataCy}>
							{#snippet custom()}
								<div class="flex items-end">
									<Txt variant="label3">
										{header.title}
									</Txt>
								</div>
							{/snippet}
						</TableHeader>
					{:else}
						<TableHeader {dataCy} class="flex justify-end">
							{#snippet custom()}
								<Button
									onclick={handleAddressAdd}
									dataCy={`${dataCy}-add`}
									class="h-[2rem] w-[2rem] px-0"
									variant="secondary"
									size="xs"
								>
									<PlusIcon class="h-3 w-3" />
								</Button>
							{/snippet}
						</TableHeader>
					{/if}
				{/each}
			</TableHeaderRow>

			<TableBody {dataCy}>
				{#if entityAddresses}
					{#each entityAddresses as entityAddress, index}
						{#if !entityAddress.isDeleted}
							<AddressRow
								{headers}
								{entityAddress}
								{index}
								onChange={onAddressRowChange(index)}
								bind:selectedOption={selectedOptions[index]}
								bind:countrySelectedOption={countrySelectedOptions[index]}
							/>
						{/if}
					{/each}
				{/if}
			</TableBody>
		</table>
	</div>
</AccordionItem>
