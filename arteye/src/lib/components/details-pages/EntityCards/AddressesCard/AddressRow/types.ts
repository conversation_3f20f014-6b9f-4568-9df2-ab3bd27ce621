import type { PersonDetailsPageData } from '$routes/people/[id]/types';

export type AddressType = NonNullable<
	NonNullable<
		NonNullable<PersonDetailsPageData['person']>['entity']
	>['addresses']
>[number] & { isDeleted?: boolean; isNew?: boolean };

export enum PersonAddressesFieldName {
	Country = 'country',
	CountyOrState = 'county_or_state',
	City = 'city',
	Line1 = 'line_1',
	Line2 = 'line_2',
	Line3 = 'line_3',
	PostCode = 'post_code',
	Action = 'action',
}
