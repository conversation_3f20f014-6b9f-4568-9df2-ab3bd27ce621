<script lang="ts">
	import { writable } from 'svelte/store';
	import { CityAutocomplete } from './CityAutocomplete';
	import { type AddressType, PersonAddressesFieldName } from './types';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import { Input } from '$global/components/Input';
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import {
		TableActionCell,
		TableCell,
		TableRow,
	} from '$global/components/Table';
	import {
		CountrySelectedOption,
		formatCountry,
	} from '$lib/components/details-pages/EntityCards/AddressesCard/AddressRow/CountrySelectedOption';
	import { UpdateHistoryTooltip } from '$lib/components/UpdateHistoryTooltip';

	interface Props {
		selectedOption: OptionType | null;
		countrySelectedOption: OptionType | null;
		index: number;
		entityAddress: AddressType;
		headers: { fieldName: PersonAddressesFieldName }[];
		onChange: (address: AddressType) => void;
	}

	let {
		selectedOption = $bindable(),
		countrySelectedOption = $bindable(),
		index,
		entityAddress,
		headers,
		onChange,
	}: Props = $props();

	let cityValue = writable('');
	let countryValue = writable('');

	const dataCy = 'address-row';

	const handleDeleteAddress = () => {
		onChange({
			...entityAddress,
			isDeleted: true,
		});
	};

	const handleChangeCity = (e: {
		detail: {
			value: OptionType;
		};
	}) => {
		const cityOption = e.detail.value;
		const country = cityOption.line4 ? JSON.parse(cityOption.line4) : null;

		onChange({
			...entityAddress,
			city: {
				name: `${cityOption.line5}`,
				short_code: `${cityOption.line2}`,
				code: `${cityOption.line3}`,
			},
			country,
		});

		countrySelectedOption = country ? formatCountry(country) : null;
		return Promise.resolve();
	};

	const handleClearCity = () => {
		onChange({
			...entityAddress,
			city: null,
			country: null,
		});

		countrySelectedOption = null;
	};

	const handleInputChange =
		(field: keyof typeof entityAddress) => (event?: Event | undefined) => {
			onChange({
				...entityAddress,
				[field]: (event?.target as HTMLInputElement).value,
			});
		};
</script>

<TableRow {index} {dataCy}>
	{#each headers as header}
		{#if header.fieldName === PersonAddressesFieldName.Action}
			<TableActionCell {dataCy} class="py-0">
				<div class="flex items-center justify-end gap-2">
					<UpdateHistoryTooltip updateHistory={entityAddress} />

					<Button
						onclick={handleDeleteAddress}
						dataCy={`${dataCy}-delete`}
						class="h-[2rem] w-[2rem] px-0"
						variant="secondary"
						size="xs"
					>
						<BinIcon class="h-3 w-3" />
					</Button>
				</div>
			</TableActionCell>
		{:else if header.fieldName === PersonAddressesFieldName.Country}
			<TableCell {dataCy} class="py-0">
				{#snippet custom()}
					<CountrySelectedOption
						{dataCy}
						bind:selectedOption={countrySelectedOption}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.CountyOrState}
			<TableCell {dataCy} class="py-0">
				<Input
					dataCy={`${dataCy}-county-or-state`}
					name="county-or-state"
					value={entityAddress.region?.name}
					size="sm"
					disabled
				/>
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.City}
			<TableCell {dataCy} class="">
				{#snippet custom()}
					<CityAutocomplete
						disabled={false}
						onRemoveSelectedOption={handleClearCity}
						{dataCy}
						bind:selectedOption
						onChange={handleChangeCity}
						value={cityValue}
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.Line1}
			<TableCell {dataCy} class="py-0">
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-line-1`}
						name="line-1"
						placeholder="line 1"
						value={entityAddress.line_1}
						onkeyup={handleInputChange('line_1')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.Line2}
			<TableCell {dataCy} class="py-0">
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-line-2`}
						name="line-2"
						placeholder="line 2"
						value={entityAddress.line_2}
						onkeyup={handleInputChange('line_2')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.Line3}
			<TableCell {dataCy} class="py-0">
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-line-3`}
						name="line-3"
						placeholder="line 3"
						value={entityAddress.line_3}
						onkeyup={handleInputChange('line_3')}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{:else if header.fieldName === PersonAddressesFieldName.PostCode}
			<TableCell {dataCy} class="py-0">
				{#snippet custom()}
					<Input
						dataCy={`${dataCy}-postcode`}
						name="postcode"
						placeholder="postcode"
						value={entityAddress.post_code}
						onkeyup={handleInputChange(PersonAddressesFieldName.PostCode)}
						size="sm"
					/>
				{/snippet}
			</TableCell>
		{/if}
	{/each}
</TableRow>
