<script lang="ts" module>
	export const formatCountry = (
		location: GetLocationsQuery['location'][number]
	) => ({
		line1: `${location.name}`,
		line2: `${location.short_code}`,
		line3: `${location.code}`,
	});
</script>

<script lang="ts">
	import { type Option as OptionType } from '$global/components/QueryAutocomplete';
	import { SelectedOption } from '$global/components/QueryAutocomplete/SelectedOption';
	import { type GetLocationsQuery } from '$lib/queries/__generated__/getLocations.generated';

	interface Props {
		selectedOption?: OptionType | null;
		dataCy: string;
	}

	let { selectedOption = $bindable(null), dataCy }: Props = $props();
</script>

{#if selectedOption}
	<SelectedOption
		dataCy={`${dataCy}-country`}
		option={selectedOption}
		classes={{
			line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
			button: 'max-w-full',
			wrapper: 'py-1.5 bg-gray-100',
			line2: 'hidden',
			line3: 'hidden',
		}}
	/>
{/if}
