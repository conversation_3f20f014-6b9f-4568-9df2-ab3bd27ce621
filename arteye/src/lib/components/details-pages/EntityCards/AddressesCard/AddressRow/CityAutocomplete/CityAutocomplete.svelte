<script lang="ts" module>
	export const formatLocation = (
		location: LocationSearchQuery['locationSearch']['data'][number]
	) => ({
		line1: `${location.name}${location.country ? ` (${location.country.name})` : ''}`,
		line2: `${location.short_code}`,
		line3: `${location.code}`,
		line4: location.country
			? JSON.stringify({
					name: `${location.country.name}`,
					short_code: `${location.country.short_code}`,
					code: `${location.country.code}`,
				})
			: '',
		line5: `${location.name}`,
	});
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { type Writable } from 'svelte/store';
	import { page } from '$app/state';
	import {
		QueryAutocomplete,
		type Option as OptionType,
	} from '$global/components/QueryAutocomplete';
	import { NoResults } from '$global/components/QueryAutocomplete/Autocomplete/NoResults';
	import { Option } from '$global/components/QueryAutocomplete/Option';
	import { SelectedOption } from '$global/components/QueryAutocomplete/SelectedOption';
	import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
	import { Location_Type_Enum } from '$gql/types-custom';
	import {
		LocationSearchDocument,
		type LocationSearchQuery,
		type LocationSearchQueryVariables,
	} from '$lib/custom-queries/__generated__/locationSearch.generated';
	import { gqlClientCustom } from '$lib/gqlClientCustom';

	let data = $derived(
		page.data as {
			user: { access_token: string } | null;
		}
	);

	interface Props {
		placeholder?: string;
		dataCy: string;
		disabled: boolean;
		selectedOption?: OptionType | null;
		value: Writable<string>;
		onRemoveSelectedOption: (() => void) | undefined;
		onChange?:
			| undefined
			| ((e: {
					detail: {
						value: OptionType;
					};
			  }) => Promise<void>);
	}

	let {
		placeholder = 'Search city',
		dataCy,
		disabled,
		selectedOption = $bindable(null),
		value = $bindable(),
		onRemoveSelectedOption,
		onChange = undefined,
	}: Props = $props();

	const getVariables = (value: string): LocationSearchQueryVariables => {
		return {
			input: {
				filters: {
					type: Location_Type_Enum.City,
					name: value,
				},
			},
		};
	};

	const getOptions = (data: LocationSearchQuery | undefined) => {
		return (data?.locationSearch.data || []).map(formatLocation);
	};

	$effect(() => {
		if (!selectedOption) {
			value.set('');
		}
	});
</script>

<div class={'relative'}>
	<QueryAutocomplete
		size="sm"
		{disabled}
		autocomplete="nope"
		OptionComponent={Option}
		SelectedOptionComponent={SelectedOption}
		name="gallery"
		dataCy={`${dataCy}-city`}
		{placeholder}
		emptyValueResponse={{ locationSearch: { data: [] } }}
		{onRemoveSelectedOption}
		showResultsWhenEmpty={false}
		graphQlClient={gqlClientCustom}
		classes={{
			listWithOptions: '!max-h-[10.5rem] [&>div]:max-h-[10.5rem] !min-h-min',
			option: {
				line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
			},
			selectedOption: {
				line1: 'text-[0.75rem] sm:text-[0.75rem] font-[400]',
				button: 'max-w-full',
				wrapper: 'py-1.5 bg-white',
				line2: 'hidden',
				line3: 'hidden',
				line4: 'hidden',
				line5: 'hidden',
			},
		}}
		requestHeaders={getAuthorizationHeaders(data)}
		class={classNames({ 'max-w-[calc(100%-20px)]': !!selectedOption })}
		{getOptions}
		{getVariables}
		document={LocationSearchDocument}
		{value}
		bind:selectedOption
		{onChange}
	>
		{#snippet noResults()}
			<NoResults class="text-left" dataCy={`${dataCy}-city-autocomplete`}
				>No city found.</NoResults
			>{/snippet}
	</QueryAutocomplete>
</div>
