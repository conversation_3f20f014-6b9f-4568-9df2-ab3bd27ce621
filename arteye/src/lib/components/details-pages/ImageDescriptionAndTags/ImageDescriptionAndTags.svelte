<script module lang="ts">
	export interface ImageAndDescriptionForm {
		description: string;
		categoryTags: MultiSelectOption[];
		subjectTags: MultiSelectOption[];
	}

	export type SeriesImage = {
		url: string;
		file?: File;
	};
</script>

<script lang="ts">
	import classNames from 'classnames';
	import { page } from '$app/state';
	import { BinIcon } from '$global/assets/icons/BinIcon';
	import { Button } from '$global/components/Button';
	import Dropzone from '$global/components/Dropzone/Dropzone.svelte';
	import { Input } from '$global/components/Input';
	import type { MultiSelectOption } from '$global/components/MultiSelect';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { imageFileToBase64 } from '$global/utils/imageFileToBase64/imageFileToBase64';
	import { Tag_Type_Enum } from '$gql/types-custom';
	import { TagsMultiSelect } from '$lib/components/TagsMultiSelect';
	import type { UpdateHistory } from '$lib/components/UpdateHistoryTooltip';
	import UpdateHistoryTooltip from '$lib/components/UpdateHistoryTooltip/UpdateHistoryTooltip.svelte';
	import type { SeriesDetailsPageData } from '$routes/series/[id]/types';

	interface Props {
		imageAndDescriptionForm: ImageAndDescriptionForm;
		image: { url?: string | null } | null | undefined;
		onChange: (imageAndDescriptionForm: ImageAndDescriptionForm) => void;
		onImageChange: (image: { url: string; file: File } | null) => void;
		tagsUpdateHistory?: UpdateHistory;
	}

	let {
		imageAndDescriptionForm,
		image,
		onChange,
		onImageChange,
		tagsUpdateHistory,
	}: Props = $props();

	const dataCy = 'image-and-description-card';

	let data = $derived(getPageData<SeriesDetailsPageData>(page.data));

	const handleClickDeleteImage = () => {
		onImageChange(null);
	};

	const handleSubmitImage = async (newFiles: File[]) => {
		const newFile = newFiles[0];
		const base64Image = await imageFileToBase64(newFile);
		onImageChange({
			file: newFile,
			url: base64Image.url,
		});
	};

	const handleInputChange =
		(field: keyof ImageAndDescriptionForm) => (event?: Event | undefined) => {
			const target = event?.target as HTMLInputElement;
			const value = target.value;
			onChange({ ...imageAndDescriptionForm, [field]: value });
		};

	const handleCategoryTagsChange = (selected: MultiSelectOption[]) => {
		onChange({ ...imageAndDescriptionForm, categoryTags: selected });
	};

	const handleSubjectTagsChange = (selected: MultiSelectOption[]) => {
		onChange({ ...imageAndDescriptionForm, subjectTags: selected });
	};
</script>

<div class="rounded-md border bg-white p-4 mb-4">
	<Txt variant="h5" class="mb-6">Image &amp; Description</Txt>

	<div
		class={classNames(
			'relative mb-7 flex h-[200px] w-full items-center justify-center bg-gray-200',
			{ 'p-4': image }
		)}
	>
		{#if image}
			<img class="max-h-full" src={image?.url} alt="profile" />

			<Button
				dataCy={`${dataCy}-delete-profile`}
				class="absolute bottom-2 right-2 h-[2rem] w-[2rem] px-0"
				variant="secondary"
				onclick={handleClickDeleteImage}
				size="xs"
			>
				<BinIcon class="h-3 w-3" />
			</Button>
		{:else}
			<Dropzone
				multiple={false}
				accept={['image/jpeg', 'image/png']}
				dataCy={`${dataCy}`}
				onSubmitFiles={handleSubmitImage}
				showFiles={false}
				class="w-full h-full [&>div]:h-full [&>div]:!bg-gray-100 [&>div]:justify-center z-[2]"
			>
				{#snippet helper()}
					<Txt variant="body3" class="mt-1 mb-2">Max file size is 100MB</Txt>
				{/snippet}
			</Dropzone>
		{/if}
	</div>

	<Input
		dataCy={`${dataCy}-description`}
		name="description"
		placeholder="Description"
		label="Description"
		rows={6}
		value={imageAndDescriptionForm.description}
		onkeyup={handleInputChange('description')}
		size="sm"
		class="resize-y mb-4"
	/>
</div>

<div class="rounded-md border bg-white p-4">
	<Txt variant="h5" class="mb-6">Tags</Txt>

	<div class="mb-4">
		<TagsMultiSelect
			selected={imageAndDescriptionForm.categoryTags}
			onChange={handleCategoryTagsChange}
			typeKey={Tag_Type_Enum.Category}
			accessToken={data.user?.access_token || ''}
			dataCy={`${dataCy}-category`}
		/>
	</div>

	<div class="mb-4">
		<TagsMultiSelect
			selected={imageAndDescriptionForm.subjectTags}
			onChange={handleSubjectTagsChange}
			typeKey={Tag_Type_Enum.Subject}
			accessToken={data.user?.access_token || ''}
			dataCy={`${dataCy}-subject`}
		/>
	</div>

	{#if tagsUpdateHistory}
		<div class="flex justify-end">
			<UpdateHistoryTooltip updateHistory={tagsUpdateHistory} />
		</div>
	{/if}
</div>
