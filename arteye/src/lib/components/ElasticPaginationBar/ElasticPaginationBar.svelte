<script lang="ts">
	import classNames from 'classnames';
	import { goto, preloadData } from '$app/navigation';
	import { page } from '$app/state';
	import {
		PaginationDirection,
		PaginationPrevNext,
	} from '$global/components/PaginationPrevNext';
	import type { SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { PAGE_SIZE_OPTIONS } from '$lib/constants/page-size-options';
	import { ArtActSearchType } from '$lib/pages/artworks-and-activities/ArtworksAndActivitiesSearchPage/constants/search';
	import { SearchParam } from '$lib/types/types';
	import { getPaginationResultsText } from '$lib/utils/getPaginationResultsText/getPaginationResultsText';

	interface Props {
		dataCy: string;
		searchParams: URLSearchParams;
		total: number;
		pageSize: number;
		currentPage: number;
		nextToken: string;
		previousToken: string;
	}

	let {
		searchParams,
		total,
		pageSize,
		currentPage,
		nextToken,
		previousToken,
		dataCy,
	}: Props = $props();

	let dataCyPrefix = $derived(`${dataCy}-elastic-pagination`);

	$effect(() => {
		const newParams = new URLSearchParams(
			getNewSearchParams(PaginationDirection.Next).slice(1)
		);

		if (currentPage && newParams.get('paginationToken')) {
			preloadData(getNewSearchParams(PaginationDirection.Next));
		}
	});

	const getNewSearchParams = (direction: PaginationDirection) => {
		const paginationDirection = direction;

		const newPage =
			direction === PaginationDirection.Next
				? currentPage + 1
				: currentPage - 1;

		const paginationToken =
			direction === PaginationDirection.Next ? nextToken : previousToken;

		const searchParamsCopy = new URLSearchParams();

		for (const [key, value] of searchParams.entries()) {
			searchParamsCopy.set(key, value);
		}

		searchParamsCopy.set(
			SearchParam.PaginationDirection,
			encodeURIComponent(paginationDirection)
		);
		searchParamsCopy.set(
			SearchParam.PaginationToken,
			encodeURIComponent(paginationToken)
		);
		searchParamsCopy.set(SearchParam.ShowResults, 'true');
		searchParamsCopy.set(SearchParam.Page, encodeURIComponent(newPage));

		const searchParamString = searchParamsCopy.toString();
		return `?${searchParamString}`;
	};

	const handlePaginationClick = (
		e: Event | undefined,
		direction: PaginationDirection
	) => {
		if (e) {
			e.preventDefault();
		}

		goto(getNewSearchParams(direction), {
			noScroll: true,
		});
	};

	const handlePageSizeChange = (e: SelectChangeEvent) => {
		const newPageSize = e.detail.value;
		const searchParamsCopy = new URLSearchParams();

		for (const [key, value] of searchParams.entries()) {
			searchParamsCopy.set(key, value);
		}

		searchParamsCopy.set(SearchParam.ShowResults, 'true');
		searchParamsCopy.set(SearchParam.Page, '1');
		searchParamsCopy.set(SearchParam.PageSize, newPageSize);
		searchParamsCopy.delete(SearchParam.PaginationToken);
		searchParamsCopy.delete(SearchParam.PaginationDirection);

		const searchParamString = searchParamsCopy.toString();

		goto(`?${searchParamString}`, { invalidateAll: true, noScroll: true });
	};
</script>

{#key total}
	<div
		class={classNames(
			'mt-4 flex flex-col md:flex-row justify-between md:items-center fixed left-0 right-0 bottom-0 bg-white border-t border-gray-200 py-4 px-4 lg:px-12 z-30 gap-4',
			{
				hidden: [
					ArtActSearchType.ByImage,
					ArtActSearchType.MultipleIds,
				].includes(page.url.searchParams.get('searchType') as ArtActSearchType),
			}
		)}
	>
		<div class="flex items-center gap-1.5 justify-end md:justify-start">
			<Txt class=" font-normal" variant="label4">Showing</Txt>

			<div class="w-[75px]">
				<Select
					ariaLabel="Page size select"
					dataCy={`${dataCyPrefix}-page-size`}
					name="page_size"
					value={pageSize.toString()}
					size="sm"
					options={PAGE_SIZE_OPTIONS}
					onchange={handlePageSizeChange}
				/>
			</div>

			<Txt class=" font-normal" variant="label4">results per page</Txt>
		</div>

		{#if total}
			<div class="flex items-center justify-end gap-4">
				<Txt class=" font-normal" variant="label4">
					{getPaginationResultsText({
						currentPage,
						total,
						pageSize,
					})}
				</Txt>

				<PaginationPrevNext
					{currentPage}
					{total}
					limit={pageSize}
					dataCy={dataCyPrefix}
					onClick={handlePaginationClick}
				/>
			</div>
		{/if}
	</div>
{/key}
