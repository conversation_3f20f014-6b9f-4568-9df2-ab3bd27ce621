<script lang="ts">
	import dayjs from 'dayjs';
	import type { UpdateHistory } from '../UpdateHistoryTooltip/types';
	import { Txt } from '$global/components/Txt';

	interface Props {
		class?: string;
		updateHistory: UpdateHistory;
		pipelineSource?: string | null | undefined;
	}

	let { updateHistory, pipelineSource, ...rest }: Props = $props();

	let updatedName = $derived({
		first: updateHistory?.user_updated?.first_name || '',
		last: (updateHistory?.user_updated?.last_name || '').charAt(0),
	});

	let createdName = $derived({
		first: updateHistory?.user_created?.first_name || 'unknown',
		last: (updateHistory?.user_created?.last_name || '').charAt(0),
	});

	let getUpdated = $derived(() => {
		if (updateHistory?.date_updated) {
			return `${dayjs(updateHistory?.date_updated).format('HH:mm DD/MM/YYYY')}
				by ${updatedName.first} ${updatedName.last ? `${updatedName.last}.` : ''}`;
		}
		return '';
	});

	let getCreated = $derived(() => {
		if (updateHistory?.date_created) {
			return `${dayjs(updateHistory?.date_created).format('HH:mm DD/MM/YYYY')} 
				by
				${createdName.first} ${createdName.last ? `${createdName.last}.` : ''}`;
		}
		return '';
	});
</script>

<span class={rest.class}>
	{#if updateHistory?.date_updated || updateHistory?.date_created || pipelineSource}
		<span class="flex min-w-[200px] flex-col">
			{#if updateHistory?.date_updated && updateHistory?.user_updated?.first_name}
				<span>
					<Txt class="text-gray-500" component="span" variant="label4"
						>Last updated by:</Txt
					>
					<Txt class="text-gray-500" component="span" variant="label4">
						{getUpdated()}
					</Txt>
				</span>
			{/if}

			{#if updateHistory?.date_created && updateHistory?.user_created?.first_name}
				<span>
					<Txt class="text-gray-500" component="span" variant="label4"
						>Created by:</Txt
					>
					<Txt class="text-gray-500" component="span" variant="label4">
						{getCreated()}
					</Txt>
				</span>
			{/if}

			{#if pipelineSource}
				<span>
					<Txt class="text-gray-500" component="span" variant="label4"
						>Pipeline source:</Txt
					>
					<Txt class="text-gray-500" component="span" variant="label4">
						{pipelineSource}
					</Txt>
				</span>
			{/if}
		</span>
	{:else}
		<Txt class="text-gray-500" variant="label4">No data available.</Txt>
	{/if}
</span>
