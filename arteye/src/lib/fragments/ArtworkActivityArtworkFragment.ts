import { gql } from 'graphql-tag';

export const ArtworkActivityArtworkFragment = gql`
	fragment ArtworkActivityArtworkFragment on artwork_activity_artwork {
		id
		edition_number
		edition_number_legacy
		edition_number_type {
			key
			name
		}
		status {
			key
			name
		}
		artwork {
			activities_transferred_to(
				sort: ["-date_created"]
				limit: 1
				filter: { from_artwork: { status: { key: { _neq: "archived" } } } }
			) {
				from_artwork {
					id
				}
			}
			crid
			title
			id
			artists {
				artist_id {
					id
					person {
						entity {
							name
						}
						nationalities {
							country {
								name
								country_nationality
							}
						}
						year_birth
						year_death
					}
				}
			}
		}
	}
`;
