import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import * as Types from '../../../gql/types';

export type BidFragmentFragment = {
	__typename?: 'auction_bid';
	id: string;
	notes?: string | null;
	date_created?: any | null;
	date_updated?: any | null;
	timestamp: any;
	user_created?: {
		__typename?: 'directus_users';
		id?: string | null;
		first_name?: string | null;
		last_name?: string | null;
	} | null;
	user_updated?: {
		__typename?: 'directus_users';
		id?: string | null;
		first_name?: string | null;
		last_name?: string | null;
	} | null;
	bidder?: {
		__typename?: 'auction_lot_bidder';
		id: string;
		notes?: string | null;
		location_number?: number | null;
		bidder?: {
			__typename?: 'entity';
			id: string;
			name: string;
			person?: {
				__typename?: 'person';
				id: string;
				first_name?: string | null;
				last_name?: string | null;
			} | null;
		} | null;
		bidder_type?: {
			__typename?: 'auction_bidder_type';
			name: string;
			key: string;
		} | null;
		client?: {
			__typename?: 'auction_client';
			id: string;
			paddle_number?: string | null;
			entity?: {
				__typename?: 'entity';
				id: string;
				name: string;
				type?: { __typename?: 'entity_type'; key: string } | null;
			} | null;
		} | null;
	} | null;
	amount?: {
		__typename?: 'currency_amount';
		conversion_timestamp?: any | null;
		amount: number;
		usd_amount: number;
		currency?: {
			__typename?: 'currency';
			symbol?: string | null;
			name: string;
			code: string;
			full_name?: string | null;
		} | null;
	} | null;
};

export const BidFragmentFragmentDoc = {
	kind: 'Document',
	definitions: [
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'BidFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'auction_bid' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_created' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'date_updated' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_created' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_updated' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
							],
						},
					},
					{ kind: 'Field', name: { kind: 'Name', value: 'timestamp' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'bidder' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'notes' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'person' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'first_name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'last_name' },
														},
													],
												},
											},
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'bidder_type' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
										],
									},
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'location_number' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'client' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'status' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'key' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: '_neq' },
																				value: {
																					kind: 'StringValue',
																					value: 'archived',
																					block: false,
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'entity' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'name' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'type' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'key' },
																	},
																],
															},
														},
													],
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'paddle_number' },
											},
										],
									},
								},
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'amount' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'conversion_timestamp' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'amount' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'usd_amount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'currency' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'symbol' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'code' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'full_name' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<BidFragmentFragment, unknown>;
