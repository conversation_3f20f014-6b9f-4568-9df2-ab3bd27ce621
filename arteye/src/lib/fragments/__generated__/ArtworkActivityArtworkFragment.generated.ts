import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import * as Types from '../../../gql/types';

export type ArtworkActivityArtworkFragmentFragment = {
	__typename?: 'artwork_activity_artwork';
	id: string;
	edition_number?: string | null;
	edition_number_legacy?: string | null;
	edition_number_type?: {
		__typename?: 'edition_number_type';
		key: string;
		name?: string | null;
	} | null;
	status?: { __typename?: 'status'; key: string; name: string } | null;
	artwork?: {
		__typename?: 'artwork';
		crid?: string | null;
		title?: string | null;
		id: string;
		activities_transferred_to?: Array<{
			__typename?: 'artwork_activity_transfer';
			from_artwork?: { __typename?: 'artwork'; id: string } | null;
		} | null> | null;
		artists?: Array<{
			__typename?: 'artwork_artist';
			artist_id?: {
				__typename?: 'artist';
				id: string;
				person?: {
					__typename?: 'person';
					year_birth?: number | null;
					year_death?: number | null;
					entity?: { __typename?: 'entity'; name: string } | null;
					nationalities?: Array<{
						__typename?: 'person_nationality';
						country?: {
							__typename?: 'location';
							name?: string | null;
							country_nationality?: string | null;
						} | null;
					} | null> | null;
				} | null;
			} | null;
		} | null> | null;
	} | null;
};

export const ArtworkActivityArtworkFragmentFragmentDoc = {
	kind: 'Document',
	definitions: [
		{
			kind: 'FragmentDefinition',
			name: { kind: 'Name', value: 'ArtworkActivityArtworkFragment' },
			typeCondition: {
				kind: 'NamedType',
				name: { kind: 'Name', value: 'artwork_activity_artwork' },
			},
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
					{ kind: 'Field', name: { kind: 'Name', value: 'edition_number' } },
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_legacy' },
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'edition_number_type' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'status' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'key' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
							],
						},
					},
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'artwork' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'activities_transferred_to' },
									arguments: [
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'sort' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'StringValue',
														value: '-date_created',
														block: false,
													},
												],
											},
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'limit' },
											value: { kind: 'IntValue', value: '1' },
										},
										{
											kind: 'Argument',
											name: { kind: 'Name', value: 'filter' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: 'from_artwork' },
														value: {
															kind: 'ObjectValue',
															fields: [
																{
																	kind: 'ObjectField',
																	name: { kind: 'Name', value: 'status' },
																	value: {
																		kind: 'ObjectValue',
																		fields: [
																			{
																				kind: 'ObjectField',
																				name: { kind: 'Name', value: 'key' },
																				value: {
																					kind: 'ObjectValue',
																					fields: [
																						{
																							kind: 'ObjectField',
																							name: {
																								kind: 'Name',
																								value: '_neq',
																							},
																							value: {
																								kind: 'StringValue',
																								value: 'archived',
																								block: false,
																							},
																						},
																					],
																				},
																			},
																		],
																	},
																},
															],
														},
													},
												],
											},
										},
									],
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'from_artwork' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
													],
												},
											},
										],
									},
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'crid' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'artists' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artist_id' },
												selectionSet: {
													kind: 'SelectionSet',
													selections: [
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'id' },
														},
														{
															kind: 'Field',
															name: { kind: 'Name', value: 'person' },
															selectionSet: {
																kind: 'SelectionSet',
																selections: [
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'entity' },
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: { kind: 'Name', value: 'name' },
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: {
																			kind: 'Name',
																			value: 'nationalities',
																		},
																		selectionSet: {
																			kind: 'SelectionSet',
																			selections: [
																				{
																					kind: 'Field',
																					name: {
																						kind: 'Name',
																						value: 'country',
																					},
																					selectionSet: {
																						kind: 'SelectionSet',
																						selections: [
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'name',
																								},
																							},
																							{
																								kind: 'Field',
																								name: {
																									kind: 'Name',
																									value: 'country_nationality',
																								},
																							},
																						],
																					},
																				},
																			],
																		},
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_birth' },
																	},
																	{
																		kind: 'Field',
																		name: { kind: 'Name', value: 'year_death' },
																	},
																],
															},
														},
													],
												},
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<ArtworkActivityArtworkFragmentFragment, unknown>;
