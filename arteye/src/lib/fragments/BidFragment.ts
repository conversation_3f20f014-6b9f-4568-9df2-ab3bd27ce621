import { gql } from 'graphql-tag';

export const BidFragment = gql`
	fragment BidFragment on auction_bid {
		id
		notes
		date_created
		date_updated
		user_created {
			id
			first_name
			last_name
		}
		user_updated {
			id
			first_name
			last_name
		}
		timestamp
		bidder {
			id
			notes
			bidder {
				id
				name
				person {
					id
					first_name
					last_name
				}
			}
			bidder_type {
				name
				key
			}
			location_number
			client(filter: { status: { key: { _neq: "archived" } } }) {
				id
				entity {
					id
					name
					type {
						key
					}
				}
				paddle_number
			}
		}
		amount {
			conversion_timestamp
			amount
			usd_amount
			currency {
				symbol
				name
				code
				full_name
			}
		}
	}
`;
