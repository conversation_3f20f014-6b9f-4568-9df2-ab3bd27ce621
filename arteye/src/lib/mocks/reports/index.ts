export const mockReports = [
	{
		id: '1',
		title: 'Quarterly Financial Report',
		date_created: '2023-01-15T10:00:00Z',
		status: 'Complete',
		config: {
			type: 'financial',
		},
		user_created: {
			first_name: '<PERSON>',
		},
		pdf_file: {
			id: '1',
			filename_disk: 'quarterly_financial_report.pdf',
		},
	},
	{
		id: '2',
		title: 'Marketing Campaign Analysis',
		date_created: '2023-03-22T14:30:00Z',
		status: 'In Progress',
		config: {
			type: 'marketing',
		},
		user_created: {
			first_name: '<PERSON>',
		},
		pdf_file: {
			id: '2',
			filename_disk: 'marketing_campaign_analysis.pdf',
		},
	},
	{
		id: '3',
		title: 'User Engagement Statistics',
		date_created: '2023-05-10T09:15:00Z',
		status: 'Pending',
		config: {
			type: 'engagement',
		},
		user_created: {
			first_name: '<PERSON>',
		},
		pdf_file: {
			id: '3',
			filename_disk: 'user_engagement_statistics.pdf',
		},
	},
];

export const mockReportConfig = [
	{
		__typename: 'report_config',
		id: '1',
		sort: 1,
		status: 'active',
		type: 'financial',
		params: [
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Revenue',
					status: 'active',
					type: 'currency',
					description: 'Total revenue generated in the period',
					id: 'param1',
				},
			},
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Expenses',
					status: 'active',
					type: 'currency',
					description: 'Total expenses incurred in the period',
					id: 'param2',
				},
			},
		],
		user_created: {
			__typename: 'directus_users',
			id: 'user1',
			first_name: 'Alice',
		},
		user_updated: {
			__typename: 'directus_users',
			id: 'user2',
			first_name: 'Bob',
		},
	},
	{
		__typename: 'report_config',
		id: '2',
		sort: 2,
		status: 'Pending',
		type: 'marketing',
		params: [
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Campaign Cost',
					status: 'active',
					type: 'text',
					description: 'Total cost of the marketing campaign',
					id: 'param3',
				},
			},
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Leads Generated',
					status: 'active',
					type: 'number',
					description: 'Number of leads generated by the campaign',
					id: 'param4',
				},
			},
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'PPC',
					status: 'active',
					type: 'boolean',
					description: 'Number of leads generated by the campaign',
					id: 'param4',
				},
			},
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Starting when',
					status: 'active',
					type: 'date',
					description: 'Number of leads generated by the campaign',
					id: 'param4',
				},
			},
		],
		user_created: {
			__typename: 'directus_users',
			id: 'user3',
			first_name: 'Charlie',
		},
		user_updated: {
			__typename: 'directus_users',
			id: 'user4',
			first_name: 'Dana',
		},
	},
	{
		__typename: 'report_config',
		id: '3',
		sort: 3,
		status: 'active',
		type: 'engagement',
		params: [
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'User Signups',
					status: 'active',
					type: 'number',
					description: 'Number of new user signups',
					id: 'param5',
				},
			},
			{
				__typename: 'report_config_report_param',
				report_param_id: {
					__typename: 'report_param',
					param_name: 'Active Users',
					status: 'active',
					type: 'number',
					description: 'Number of active users',
					id: 'param6',
				},
			},
		],
		user_created: {
			__typename: 'directus_users',
			id: 'user5',
			first_name: 'Eve',
		},
		user_updated: {
			__typename: 'directus_users',
			id: 'user6',
			first_name: 'Frank',
		},
	},
];
