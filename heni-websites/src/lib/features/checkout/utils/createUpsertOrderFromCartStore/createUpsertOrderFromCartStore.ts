import { get, writable, type Writable } from 'svelte/store';
import { CheckoutErrorCodes } from '../../constants/checkout-error-codes';
import { AddressesFieldNames } from '../../constants/validation-schemas/addresses';
import { PersonalDetailsFieldNames } from '../../constants/validation-schemas/personal-details';
import { type Order } from '../../types';
import { getCartCurrency } from '../getCartCurrency';
import { getCheckoutContext } from '../getCheckoutContext';
import { handleOrderError } from '../handleOrderError';
import {
	fetchCart,
	type FetchCartParams,
	type FetchCartContext,
} from '../upsertCart';
import { page } from '$app/state';
import { getCookie } from '$global/utils/getCookie/getCookie';
import { track } from '$global/utils/track';
import { Config } from '$lib/constants/config';

export type UpsertOrderFromCartStore = ReturnType<
	typeof createUpsertOrderFromCartStore
>;

const order = writable(null) as Writable<null | Order>;
const loading = writable(false);

export const createUpsertOrderFromCartStore = () => {
	const upsertOrderFromCart = async (
		params: FetchCartParams,
		overrideConversionPriceToOne: boolean,
		context?: FetchCartContext | undefined,
		setCouponCodeInvalidError?: (reason: string) => void,
		allowAnyAllocation?: boolean | undefined
	) => {
		loading.update(() => true);

		const {
			cartPlatform: storedCartPlatform,
			cartItems: { cartId: storedCartId, clearCode },
			mixpanelEvents,
		} = getCheckoutContext();

		const cartId = context?.cartId || storedCartId;
		const cartPlatform = context?.cartPlatform || storedCartPlatform;

		const cartResponse = await fetchCart(
			params,
			{
				cartPlatform,
				cartId: writable('') as Parameters<typeof fetchCart>[1]['cartId'],
				// TODO cart: uncomment when not necessary anymore (when the cart performance has been improved)
				// cartId: [
				// 	'/editions/checkout/[dropPageSlug]',
				// 	'/primary/checkout/[dropPageSlug]',
				// 	'/primary/apply/[dropPageSlug]',
				// 	'/merch/checkout',
				// 	'/publishing/checkout',
				// 	'/shop/checkout',
				// ].includes(`${page.route.id}`)
				// 	? (writable('') as Parameters<typeof fetchCart>[1]['cartId'])
				// 	: cartId,
			},
			allowAnyAllocation
		);

		if (
			// TODO cart: uncomment when not necessary anymore (when the cart performance has been improved)
			// [
			// 	'/editions/checkout/[dropPageSlug]',
			// 	'/primary/checkout/[dropPageSlug]',
			// 	'/primary/apply/[dropPageSlug]',
			// 	'/merch/checkout',
			// 	'/publishing/checkout',
			// 	'/shop/checkout',
			// ].includes(`${page.route.id}`) &&
			!cartResponse?.id
		) {
			return;
		}

		const reason = cartResponse?.coupon?.reason;

		if (!params.extraFields?.[PersonalDetailsFieldNames.PhoneNumber]) {
			track(mixpanelEvents.MissingPhoneNumber, { cart_id: get(cartId) });
		}

		if (reason?.startsWith('The coupon code provided has expired')) {
			return Promise.reject({
				code: CheckoutErrorCodes.CouponError,
				message: reason,
			});
		} else if (
			reason?.startsWith('The coupon code') &&
			setCouponCodeInvalidError
		) {
			setCouponCodeInvalidError(reason);
			clearCode();
		}

		if (window?.fbq && params.cartItems) {
			let userData = {};

			try {
				if (window.fbq?.instance?.pixelsByID?.[Config.MetaPixelId]?.userData) {
					window.fbq.instance.pixelsByID[
						Config.MetaPixelId
					].userData.external_id = cartResponse.id;

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.em = (
						params.extraFields?.[PersonalDetailsFieldNames.Email] as string
					)?.toLowerCase();

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.fn = (
						params.extraFields?.[PersonalDetailsFieldNames.FirstName] as string
					)?.toLowerCase();

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.ln = (
						params.extraFields?.[PersonalDetailsFieldNames.LastName] as string
					)?.toLowerCase();

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.ct = (
						params.extraFields?.[AddressesFieldNames.BillingCity] as string
					)?.toLowerCase();

					if (params.extraFields?.[AddressesFieldNames.BillingRegion]) {
						window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.st = (
							params.extraFields?.[AddressesFieldNames.BillingRegion] as string
						)?.toLowerCase();
					}

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.country =
						(
							params.extraFields?.[AddressesFieldNames.BillingCountry] as string
						)?.toLowerCase();

					window.fbq.instance.pixelsByID[Config.MetaPixelId].userData.ph =
						params.extraFields?.[PersonalDetailsFieldNames.PhoneNumber];

					userData = {
						external_id: cartResponse.id,
						em: (
							params.extraFields?.[PersonalDetailsFieldNames.Email] as string
						)?.toLowerCase(),
						fn: (
							params.extraFields?.[
								PersonalDetailsFieldNames.FirstName
							] as string
						)?.toLowerCase(),
						ln: (
							params.extraFields?.[PersonalDetailsFieldNames.LastName] as string
						)?.toLowerCase(),
						ct: (
							params.extraFields?.[AddressesFieldNames.BillingCity] as string
						)?.toLowerCase(),
						country: (
							params.extraFields?.[AddressesFieldNames.BillingCountry] as string
						)?.toLowerCase(),
						ph: params.extraFields?.[PersonalDetailsFieldNames.PhoneNumber],
						...(params.extraFields?.[AddressesFieldNames.BillingRegion] && {
							st: (
								params.extraFields?.[
									AddressesFieldNames.BillingRegion
								] as string
							)?.toLowerCase(),
						}),
					};
				}
			} catch (e) {
				console.error('upsertOrderFromCart: Cannot set fbq user data', e);
			}

			try {
				const content = {
					currency: getCartCurrency(params.cartItems).toLowerCase(),
					value: overrideConversionPriceToOne ? 1 : cartResponse.amount.total,
					content_type: 'product',
					external_id: cartResponse.id,
					contents: Object.values(params.cartItems)
						.sort((cartItemA, cartItemB) => {
							const nameA = (cartItemA.sku || '').toLowerCase();
							const nameB = (cartItemB.sku || '').toLowerCase();

							if (nameA < nameB) {
								return -1;
							}

							if (nameA > nameB) {
								return 1;
							}

							return 0;
						})
						.map((cartItem) => ({
							id: cartItem.sku,
							quantity: cartItem.quantity,
						})),
				};

				const ids = { eventID: cartResponse.id, external_id: cartResponse.id };

				window.fbq('track', 'AddToCart', content, ids);
				track('FBQ_ADD_TO_CART', { content, ids, userData });
			} catch (e) {
				console.error('upsertOrderFromCart: cannot send event', e);
			}
		}

		const response = await fetch(
			`${Config.GraphqlApiDomain}/ecomm/upsertOrderFromCart/${
				[
					'/editions/checkout/[dropPageSlug]',
					'/primary/checkout/[dropPageSlug]',
					'/primary/apply/[dropPageSlug]',
					'/merch/checkout',
					'/publishing/checkout',
					'/shop/checkout',
				].includes(`${page.route.id}`)
					? cartResponse?.id
					: get(cartId)
			}`,
			{
				method: 'PUT',
				headers: {
					'User-Agent': window.navigator.userAgent,
					'Content-type': 'application/json',
					fbp: getCookie('_fbp'),
				} as HeadersInit,
				body: JSON.stringify({
					visitor: '',
					request_id: '',
				}),
			}
		);

		const jsonResponse = await response.json();
		loading.update(() => false);

		if (response.ok) {
			order.set(jsonResponse);
			return Promise.resolve(jsonResponse);
		} else {
			return handleOrderError(jsonResponse?.errors?.[0]);
		}
	};

	return { loading, order, upsertOrderFromCart };
};
