<script lang="ts">
	import { ArtworkCard } from '$global/components/ArtworkCard';
	import type { SanitisedPrimaryArtwork } from '$lib/websites/artists/pages/ArtistDetailPage/utils/sanitisePrimaryArtworks';
	import type { SanitisedPrimaryCollection } from '$lib/websites/artists/pages/ArtistDetailPage/utils/sanitisePrimaryCollections';
	import { PrimaryRoutes } from '$lib/websites/primary/constants/routes';

	interface Props {
		dataCy: string;
		item: SanitisedPrimaryArtwork | SanitisedPrimaryCollection;
	}

	let { dataCy, item }: Props = $props();
	const collection = item as SanitisedPrimaryCollection;

	let dataCyPrefix = $derived(`${dataCy}-primary-collection`);
</script>

<ArtworkCard
	dataCy={dataCyPrefix}
	line1={collection.name}
	line2={collection.description}
	variants={{
		line2: 'markdown',
	}}
	class="relative rounded border"
	classes={{
		line2: 'line-clamp-3',
	}}
	href={`${PrimaryRoutes.Collection}/${collection?.slug}`}
	image={collection.collection_featured_image}
	alt={collection.name}
/>
