<script lang="ts">
	import { ArtworkCardSection } from '../ArtworkCardSection';
	import { page } from '$app/state';
	import { HeniPrimaryLogo } from '$global/assets/logos/HeniPrimaryLogo';
	import { type ArtworkCardProps } from '$global/components/ArtworkCard';
	import { extractNonNull } from '$global/utils/extractNonNull/extractNonNull';
	import { getImageUrl } from '$global/utils/getImageUrl';
	import { GlobalRoutes } from '$lib/features/navigation/constants/global-routes';
	import type { GlobalHomePageData } from '$routes/types';
	interface Props {
		class?: string;
	}

	let { ...props }: Props = $props();

	const primaryArtworks: GlobalHomePageData['primaryArtworks'] =
		page.data.primaryArtworks;

	const primaryIntro = page.data.globalContent['primary_intro'];

	const cards = primaryArtworks.reduce(
		(
			accumulator: Omit<ArtworkCardProps, 'dataCy'>[],
			featuredPrimaryArtwork: GlobalHomePageData['primaryArtworks'][number]
		) => {
			if (!featuredPrimaryArtwork.primary_artwork) {
				return accumulator;
			}

			const primaryArtwork = extractNonNull(
				featuredPrimaryArtwork.primary_artwork,
				[
					'primary_collection',
					'artwork_featured_image',
					'primary_artist',
					'slug',
				],
				{
					primary_artist: (primary_artist) => primary_artist?.name,
				}
			);

			if (
				!primaryArtwork?.primary_collection?.name ||
				!primaryArtwork?.primary_collection?.slug
			) {
				return accumulator;
			}

			const primaryArtworkImage = extractNonNull(
				primaryArtwork?.artwork_featured_image,
				['filename_disk', 'width', 'height'],
				{}
			);

			if (!primaryArtworkImage) {
				return accumulator;
			}

			const artworkTitle = `${primaryArtwork.artwork_code || ''}${
				primaryArtwork.artwork_title
					? `${primaryArtwork.artwork_code ? '. ' : ''}${primaryArtwork.artwork_title}`
					: ''
			}`;

			return [
				...accumulator,
				{
					classes: { imageContainer: 'bg-gray-75' },
					href: `${GlobalRoutes.PrimaryHome}/collection/${primaryArtwork['primary_collection']['slug']}/artwork/${primaryArtwork.slug}`,
					line1: primaryArtwork['primary_artist'],
					line2: artworkTitle,
					...(primaryArtwork?.artwork_code !== artworkTitle &&
						!artworkTitle.includes(`${primaryArtwork?.artwork_code}`) && {
							line3: (primaryArtwork?.artwork_code || '')?.toUpperCase(),
						}),
					alt: artworkTitle,
					image: {
						...primaryArtworkImage,
						url: `${getImageUrl(primaryArtworkImage.filename_disk)}`,
					},
				},
			];
		},
		[] as ArtworkCardProps[]
	) as ArtworkCardProps[];
</script>

<ArtworkCardSection
	linkLabel="See HENI Primary home page"
	dataCy="home-primary"
	href={GlobalRoutes.PrimaryHome}
	Icon={HeniPrimaryLogo}
	{cards}
	class={props.class}
	buttonLabel="View primary"
>
	{primaryIntro}
</ArtworkCardSection>
