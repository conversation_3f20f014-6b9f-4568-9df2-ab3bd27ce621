import { OpenPanel, type TrackProperties } from '@openpanel/web';
import { PUBLIC_APP_ENV } from '$env/static/public';

export const op = new OpenPanel({
	apiUrl:
		PUBLIC_APP_ENV === 'production'
			? 'https://a.heni.com/api'
			: 'https://robust-event.no-zero.net/api',
	clientId:
		PUBLIC_APP_ENV === 'production'
			? '577dcfbe-9f98-4bb3-9a52-6f697c6b8dcf'
			: '5e8a998e-5d8a-4225-be86-e1257d495904',
	trackScreenViews: true,
	trackOutgoingLinks: true,
	trackAttributes: true
});

export const track = (event: string, props?: TrackProperties) => {
	op.track(event as unknown as string, {
		env: !PUBLIC_APP_ENV || ['dev', 'local'].includes(PUBLIC_APP_ENV) ? 'dev' : PUBLIC_APP_ENV,
		...(props || {})
	});
};
