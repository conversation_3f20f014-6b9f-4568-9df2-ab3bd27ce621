<script lang="ts" module>
	export interface ArtworkCardProps {
		alt: string;
		image: { url: string; width: number; height: number };
		line1: string;
		line2?: string;
		line3?: string;
		href?: string;
		class?: string;
		children?: import('svelte').Snippet;
		onClick?: () => void;
		dataCy: string;
		external?: boolean | undefined;
		whiteBorderFix?: boolean | undefined;
		variants?: {
			line1?: TxtVariant;
			line2?: TxtVariant | 'markdown';
			line3?: TxtVariant;
		};
		classes?: {
			image?: string;
			imageBorder?: string;
			imageContainer?: string;
			info?: string;
			card?: string;
			line1?: string;
			line2?: string;
		};
	}
</script>

<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { Link } from '../Link';
	import { Markdown } from '../Markdown';
	import { Txt, type TxtVariant } from '../Txt';
	import { WhiteBorderImageContainer } from '../WhiteBorderImageContainer';

	let {
		dataCy,
		external = false,
		whiteBorderFix = true,
		href = undefined,
		alt,
		image,
		line1,
		line2 = undefined,
		line3 = undefined,
		onClick = undefined,
		variants = {},
		classes = {},
		children,
		...rest
	}: ArtworkCardProps = $props();

	let dataCyPrefix = $derived(`${dataCy}-artwork-card`);
</script>

<Link
	{href}
	class={rest.class}
	data-cy={dataCyPrefix}
	{...external ? { rel: 'noopener noreferrer', target: '_blank' } : {}}
	onclick={onClick as () => void}
>
	<div class={twMerge('flex w-full flex-col transition-colors group', classes.card)}>
		<div class={twMerge('h-[18.75rem] items-center justify-center', classes.imageContainer)}>
			<WhiteBorderImageContainer
				class={twMerge('h-[18.75rem] p-[1rem]', classes.image)}
				{image}
				{alt}
				{whiteBorderFix}
				dataCy={dataCyPrefix}
				classes={{
					border: classes.imageBorder
				}}
			/>
		</div>
		<div
			class={twMerge(
				'flex h-[calc(100%-18.75rem)] flex-col justify-between p-4 group-hover:bg-gray-50',
				classes.info
			)}
		>
			<div class="flex flex-col gap-1">
				<Txt
					variant={variants.line1 || 'label2'}
					class={twMerge('text-gray-900', classes.line1)}
					dataCy={`${dataCyPrefix}-line-1`}
				>
					{line1}
				</Txt>

				{#if line2}
					{#if line2?.includes?.('<br>')}
						<Markdown
							color="gray"
							size="medium"
							source={line2}
							dataCy={`${dataCyPrefix}-line-2`}
							class="[&>p]:mb-0 [&>p]:font-[500] [&>p]:leading-[1rem] [&>p]:tracking-[0] [&>p]:text-gray-700"
						/>
					{:else if variants.line2 === 'markdown'}
						<Markdown
							color="black"
							size="small"
							source={line2}
							dataCy={`${dataCyPrefix}-line-2`}
							class={twMerge('[&>p]:mb-0 [&>p]:leading-[1rem] [&>p]:tracking-[0]', classes.line2)}
						/>
					{:else}
						<Txt
							variant={variants.line2 || 'label3'}
							class={twMerge('text-gray-700', classes.line2)}
							dataCy={`${dataCyPrefix}-line-2`}
						>
							{line2}
						</Txt>
					{/if}
				{/if}

				{#if line3}
					<Txt
						variant={variants.line3 || 'label3'}
						class="text-gray-700"
						dataCy={`${dataCyPrefix}-line-3`}
					>
						{line3}
					</Txt>
				{/if}
			</div>

			{@render children?.()}
		</div>
	</div>
</Link>
