{"name": "amd-insights", "version": "0.0.1", "private": true, "type": "module", "dependencies": {"@melt-ui/svelte": "0.86.2", "@sveltejs/adapter-node": "4.0.1", "@tanstack/svelte-query": "5.62.7", "dayjs": "^1.11.9", "gql-query-builder": "3.8.0", "graphql": "^16.7.1", "graphql-request": "^6.1.0", "svelte-multiselect": "11.1.1", "sveltekit-superforms": "2.26.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "zod": "3.25.51"}, "scripts": {"predev": "yarn check", "dev": "vite dev", "build": "vite build", "generate": "graphql-codegen", "preview": "vite preview", "sync": "rm -rf ./.svelte-kit && svelte-kit sync", "check": "rm -rf ./.svelte-kit && svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "types": "graphql-codegen --config ./codegen-system.ts && graphql-codegen --config ./codegen-custom.ts", "lint": "prettier --check . && eslint .", "format": "prettier --write . && eslint --fix .", "test": "vitest"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/near-operation-file-preset": "^2.5.0", "@graphql-codegen/typed-document-node": "^5.0.1", "@sveltejs/kit": "2.21.4", "@sveltejs/vite-plugin-svelte": "5.1.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/svelte": "^5.2.6", "@testing-library/user-event": "14.5.2", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vitest/coverage-v8": "^0.34.3", "autoprefixer": "^10.4.14", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-consistent-default-export-name": "^0.0.15", "eslint-plugin-import": "^2.28.0", "eslint-plugin-svelte": "^2.46.0", "html-entities": "2.5.2", "jsdom": "^24.1.1", "postcss": "^8.4.27", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "0.6.5", "svelte": "^5.9.1", "svelte-check": "^4.1.1", "svelte-persisted-store": "0.12.0", "tailwindcss": "^3.4.1", "tslib": "^2.6.0", "typescript": "^5.7.2", "vite": "6.2.7", "vitest": "3.0.7", "vitest-dom": "0.1.1"}}