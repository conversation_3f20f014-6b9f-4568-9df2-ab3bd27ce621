/* eslint-disable consistent-default-export-name/default-export-match-filename */
import type { CodegenConfig } from '@graphql-codegen/cli';
import { BasicDevConfig } from './src/lib/constants/basic-dev-config';

const codegenSystem: CodegenConfig = {
	hooks: { afterOneFileWrite: ['prettier --write', 'eslint --fix'] },
	schema: [
		{
			[BasicDevConfig.SystemGraphqlApiUrl]: {
				headers: {
					authorization: `Bearer ${BasicDevConfig.GraphqlApiKey}`,
				},
			},
		},
	],
	documents: 'src/**/system-queries/**/!(*.generated).{ts,tsx}',
	generates: {
		'./src/gql/types-system.ts': {
			plugins: ['typescript'],
		},
		'./src/': {
			preset: 'near-operation-file',
			presetConfig: {
				extension: '.generated.ts',
				baseTypesPath: './gql/types-system.ts',
				folder: '__generated__',
			},
			plugins: ['typescript-operations', 'typed-document-node'],
		},
	},
};

export default codegenSystem;
