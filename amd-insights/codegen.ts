import type { CodegenConfig } from '@graphql-codegen/cli';
import { BasicDevConfig } from './src/lib/constants/basic-dev-config';

const codegen: CodegenConfig = {
	hooks: { afterAllFileWrite: ['prettier --write', 'eslint --fix'] },
	schema: [
		{
			[BasicDevConfig.GraphqlApiUrl]: {
				headers: {
					authorization: `Bearer ${BasicDevConfig.GraphqlApiKey}`,
				},
			},
		},
	],
	documents: 'src/**/{queries,mutations}/**/!(*.generated).{ts,tsx}',
	generates: {
		'./src/gql/types.ts': {
			plugins: ['typescript'],
		},
		'./src/': {
			preset: 'near-operation-file',
			presetConfig: {
				extension: '.generated.ts',
				baseTypesPath: './gql/types.ts',
				folder: '__generated__',
			},
			plugins: ['typescript-operations', 'typed-document-node'],
		},
	},
};
export default codegen;
