import type { ServerLoadEvent } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { Config } from '$lib/constants/config';
import { Cookies } from '$lib/constants/cookies';

export const GET = async ({ cookies, params }: ServerLoadEvent) => {
	const userCookie = cookies.get(Cookies.User);

	if (!userCookie || !params.id) {
		error(403, 'Forbidden');
	}

	const user = JSON.parse(userCookie);
	const accessToken = user?.access_token;

	if (!accessToken) {
		error(403, 'Forbidden');
	}

	return fetch(
		`${Config.GraphqlApiDomain}/assets/${params.id}${
			accessToken ? `?access_token=${accessToken}` : ''
		}`
	);
};
