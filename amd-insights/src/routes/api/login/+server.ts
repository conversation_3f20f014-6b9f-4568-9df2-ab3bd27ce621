import { json, error } from '@sveltejs/kit';
import { PUBLIC_APP_ENV } from '$env/static/public';
import { generateLoginRoute } from '$global/features/auth/routes/generateLoginRoute/generateLoginRoute';
import { Cookies } from '$lib/constants/cookies';
import { LoginVariant } from '$lib/constants/login';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { LoginDocument } from '$lib/system-queries/__generated__/login.generated';

export const POST = generateLoginRoute({
	cookieKey: Cookies.User,
	gqlClient: gqlClientSystem,
	LoginDocument,
	variant: LoginVariant,
	json,
	error,
	env: PUBLIC_APP_ENV,
});
