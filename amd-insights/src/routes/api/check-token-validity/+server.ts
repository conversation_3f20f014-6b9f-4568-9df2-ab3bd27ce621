import { json, error } from '@sveltejs/kit';
import { PUBLIC_APP_ENV } from '$env/static/public';
import { generateCheckTokenValidity } from '$global/features/auth/routes/generateCheckTokenValidity/generateCheckTokenValidity';
import { Cookies } from '$lib/constants/cookies';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { AuthRefreshDocument } from '$lib/system-queries/__generated__/refresh.generated';

export const POST = generateCheckTokenValidity({
	json,
	error,
	env: PUBLIC_APP_ENV,
	RefreshDocument: AuthRefreshDocument,
	cookieKey: Cookies.User,
	gqlClient: gqlClientSystem,
});
