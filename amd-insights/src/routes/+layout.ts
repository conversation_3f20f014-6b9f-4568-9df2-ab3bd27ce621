import { QueryClient } from '@tanstack/svelte-query';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import type { LayoutLoad } from './$types';
import { browser } from '$app/environment';

dayjs.extend(customParseFormat);

export const load: LayoutLoad = async ({ data }) => {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				retry: false,
				enabled: browser,
				staleTime: Infinity,
				refetchOnMount: false,
				refetchOnWindowFocus: false,
			},
		},
	});

	return {
		...data,
		queryClient,
	};
};
