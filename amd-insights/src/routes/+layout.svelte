<script lang="ts">
	import '../app.css';
	import { QueryClientProvider } from '@tanstack/svelte-query';
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import { page } from '$app/state';
	import { LogOutIcon } from '$global/assets/icons/LogOutIcon';
	import { Button } from '$global/components/Button';
	import { Container } from '$global/components/Container';
	import { Toasts } from '$global/components/Toasts';
	import { Txt } from '$global/components/Txt';
	import { logout } from '$global/features/auth/utils/logout/logout';
	import { Routes } from '$lib/constants/routes';
	import type { FairInsightsPageData } from '$routes/fairs/types';

	interface Props {
		data: FairInsightsPageData;
		children?: import('svelte').Snippet;
	}

	let { children, data }: Props = $props();

	dayjs.extend(utc);
</script>

<QueryClientProvider client={data.queryClient}>
	<header
		class="fixed top-0 z-20 flex h-[4rem] w-full items-center bg-gray-900 lg:h-[5rem]"
	>
		<Container dataCy="header" class="flex h-full items-center justify-between">
			<Txt
				variant="h5"
				component="a"
				href={Routes.Home}
				class="font-[400] uppercase tracking-[2.52px] text-gray-0 sm:text-[1.125rem]"
				>amd insights</Txt
			>
			<div class="flex h-full items-center gap-4">
				{#if page.data.user}
					<Button
						onclick={logout}
						size="md"
						dataCy="header-log-out"
						variant="secondary"
					>
						log out
						{#snippet trailing()}
							<LogOutIcon color="gray-900" />
						{/snippet}
					</Button>
				{/if}
			</div>
		</Container>
	</header>
	{@render children?.()}
</QueryClientProvider>
<Toasts />
