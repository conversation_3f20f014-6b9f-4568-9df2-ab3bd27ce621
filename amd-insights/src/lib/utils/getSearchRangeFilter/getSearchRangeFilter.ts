import dayjs from 'dayjs';
import type { Big_Int_Filter_Operators } from '$gql/types';
import type { ValueFilterOperator } from '$gql/types-custom';
import { SearchRange } from '$lib/constants/search-range-options';

export const getValueFilter = ({
	value,
	range,
	type,
}: {
	value: string;
	range: ValueFilterOperator;
	type: 'number' | 'string' | 'date';
}) => {
	const [min, max] = value.split('-').map((val) => val.trim());

	if (type === 'date') {
		return {
			operator: range,
			min: !isNaN(+min) ? +min : dayjs(min, 'DD/MM/YYYY').format('YYYY-MM-DD'),
			...(max && {
				max: !isNaN(+max)
					? +max
					: dayjs(max, 'DD/MM/YYYY').format('YYYY-MM-DD'),
			}),
		};
	}

	return {
		operator: range,
		min: type === 'number' ? +min : min,
		max: type === 'number' ? +max : max,
	};
};

export const getSearchRangeFilter = ({
	value,
	range,
}: {
	value: string;
	range: SearchRange;
}): Big_Int_Filter_Operators => {
	if (range === SearchRange.GreaterThan) {
		return {
			_gt: Number(value),
		};
	}

	if (range === SearchRange.LessThan) {
		return {
			_lt: Number(value),
		};
	}

	if (range === SearchRange.Between) {
		const [min, max] = value.split('-');
		return {
			_between: [Number(min), Number(max)],
		};
	}

	if (range === SearchRange.EqualTo) {
		return {
			_eq: Number(value),
		};
	}

	return {};
};
