import type { ValueFilterOperator } from '$gql/types-custom';
import { SearchRange } from '$lib/constants/search-range-options';

export const getDecodedSearchRangeParam = ({
	searchParams,
	key,
	defaultSearchRange = SearchRange.EqualTo,
}: {
	searchParams: URLSearchParams;
	key: string;
	defaultSearchRange?: SearchRange | ValueFilterOperator;
}) => {
	return decodeURIComponent(searchParams.get(key) || defaultSearchRange);
};
