import { SortDirection } from '$gql/types-custom';
import { PARAM_SEPARATOR } from '$lib/constants/params';
// import { PARAM_SEPARATOR } from '$lib/constants/params';
import { SearchParam } from '$lib/types';
import { getDecodedSearchParam } from '$lib/utils/getDecodedSearchParam/getDecodedSearchParam';

const formatSort = <T extends string>(sortValue: T) => {
	if (sortValue.startsWith('-')) {
		return {
			field: sortValue.slice(1) as T,
			direction: SortDirection.Desc,
		};
	}

	return {
		field: sortValue as T,
		direction: SortDirection.Asc,
	};
};

export const getSortWithDirection = <T extends string>(
	searchParams: URLSearchParams,
	defaultSort?: T
) => {
	const sort = getDecodedSearchParam({
		searchParams,
		key: SearchParam.Sort,
	});

	if (!sort) {
		if (defaultSort) {
			return [formatSort<T>(defaultSort)];
		}
		return undefined;
	}

	const sortArray = sort.split(PARAM_SEPARATOR);

	return sortArray.map((sortValue) => formatSort<T>(sortValue as T));
};
