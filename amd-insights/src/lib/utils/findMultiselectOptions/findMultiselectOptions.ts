import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
import { PARAM_SEPARATOR } from '$lib/constants/params';

export const findMultiselectOptions = (
	searchParams: URLSearchParams,
	paramName: string,
	options: MultiSelectOption[]
) => {
	const param = searchParams.get(paramName);

	return param
		? (decodeURIComponent(param)
				.split(PARAM_SEPARATOR)
				.map((val) => options.find((option) => option.value === val))
				.filter(Boolean) as MultiSelectOption[])
		: [];
};
