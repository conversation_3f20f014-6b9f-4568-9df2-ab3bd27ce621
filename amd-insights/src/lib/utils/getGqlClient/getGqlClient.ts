import { GraphQLClient } from 'graphql-request';
import { browser } from '$app/environment';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { Config } from '$lib/constants/config';
import type { LayoutData } from '$routes/types';

export const getGqlClient = (data: LayoutData) => {
	return new GraphQLClient(Config.GraphqlApiUrl, {
		...(browser && {
			fetch: window.fetch,
			headers: {
				...getAuthorizationHeaders(data),
			},
		}),
	});
};

export const getCustomGqlClient = (data: LayoutData) => {
	return new GraphQLClient(Config.CustomGraphqlApiUrl, {
		...(browser && {
			fetch: window.fetch,
			headers: {
				...getAuthorizationHeaders(data),
			},
		}),
	});
};
