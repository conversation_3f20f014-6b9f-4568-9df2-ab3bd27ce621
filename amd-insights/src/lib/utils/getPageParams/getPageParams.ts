export const getPageParams = (href: string) => {
	if (!href) {
		return {};
	}

	const windowQueryParams = new URLSearchParams(`?${href.split('?')[1]}`);

	const params = {
		page: windowQueryParams.get('page') || undefined,
		pageSize: windowQueryParams.get('pageSize') || undefined,
	};

	const filteredParams = Object.keys(params).reduce(
		(accumulator: Record<string, string>, key: string) => {
			if (params[key as keyof typeof params]) {
				return {
					...accumulator,
					[key]: params[key as keyof typeof params] as string,
				};
			}

			return accumulator;
		},
		{} as Record<string, string>
	);

	const filteredParamsString = new URLSearchParams(filteredParams).toString();
	return filteredParamsString.length ? `&${filteredParamsString}` : '';
};
