import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { getFirstDateOfYear } from '../getFirstDateOfYear/getFirstDateOfYear';
import type { Big_Int_Filter_Operators } from '$gql/types';
import { SearchRange } from '$lib/constants/search-range-options';

dayjs.extend(customParseFormat);

const isFullDate = (date: string): boolean =>
	dayjs(date, 'DD/MM/YYYY', true).isValid();

export const getSearchRangeFilterFullDate = ({
	value,
	range,
}: {
	value: string;
	range: SearchRange;
}): Big_Int_Filter_Operators => {
	const fullDate = isFullDate(value);

	const date = fullDate ? value : getFirstDateOfYear(value);

	const formattedDate = dayjs(date, 'DD/MM/YYYY').format('YYYY-MM-DD');

	if (range === SearchRange.GreaterThan) {
		return {
			_gt: formattedDate,
		};
	}

	if (range === SearchRange.LessThan) {
		return {
			_lt: formattedDate,
		};
	}

	if (range === SearchRange.Between) {
		const [min, max] = value.split('-');
		const dateMin = isFullDate(min) ? min : getFirstDateOfYear(min);
		const dateMax = isFullDate(max) ? max : getFirstDateOfYear(max);

		return {
			_between: [
				dayjs(dateMin, 'DD/MM/YYYY').format('YYYY-MM-DD'),
				dayjs(dateMax, 'DD/MM/YYYY').format('YYYY-MM-DD'),
			],
		};
	}

	if (range === SearchRange.EqualTo) {
		const maxDate = fullDate
			? dayjs(date, 'DD/MM/YYYY')
					.endOf('day')
					.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
			: dayjs(date).add(1, 'year').format('YYYY-MM-DD');

		return {
			_between: [formattedDate, maxDate],
		};
	}

	return {};
};
