import { BasicDevConfig } from './basic-dev-config';
import { BasicLocalConfig } from './basic-local-config';
import { PUBLIC_APP_ENV } from '$env/static/public';

const env = PUBLIC_APP_ENV || 'dev';

interface AppConfig {
	GraphqlApiDomain: string;
	CustomArteyeGraphqlApiUrl: string;
	GraphqlApiUrl: string;
	CustomGraphqlApiUrl: string;
	GraphqlApiKey: string;
	SystemGraphqlApiUrl: string;
	Domain: string;
	ArteyeGraphqlApiDomain: string;
	ArteyeGraphqlApiUrl: string;
	ArteyeGraphqlApiKey: string;
	ArteyeDomain: string;
	ClientGraphqlApiDomain: string;
}

export const ProjectName = 'art-data';
export const AssetsUrl = 'https://resources.heni.com';

export const DevConfig: AppConfig = Object.freeze({
	...BasicDevConfig,
	Domain: 'https://indigo-summit.no-zero.net',
	GraphqlApiDomain: 'http://localhost:3003',
	ClientGraphqlApiDomain: 'https://deep-tree.no-zero.net',
});

export const LocalConfig: AppConfig = Object.freeze({
	...BasicLocalConfig,
	Domain: 'http://localhost:5173',
	ClientGraphqlApiDomain: 'https://deep-tree.no-zero.net',
});

// GraphqlApiKey not necessary for production as the key is only used to generate the typed documents
export const ProductionConfig: AppConfig = Object.freeze({
	GraphqlApiDomain: 'http://0.0.0.0:3003',
	ClientGraphqlApiDomain: 'https://general-lordship.no-zero.net',
	GraphqlApiKey: '',
	CustomGraphqlApiUrl: 'https://general-lordship.no-zero.net/custom-graphql',
	GraphqlApiUrl: 'https://general-lordship.no-zero.net/graphql',
	SystemGraphqlApiUrl: 'https://general-lordship.no-zero.net/graphql/system',
	Domain: 'https://amber-grove.no-zero.net',
	CustomArteyeGraphqlApiUrl: 'https://marginal-mist.no-zero.net/custom-graphql',
	ArteyeGraphqlApiUrl: 'https://marginal-mist.no-zero.net/graphql',
	ArteyeGraphqlApiDomain: 'https://marginal-mist.no-zero.net',
	ArteyeGraphqlApiKey: 'W3mXriQXPyIma8J-GzLRKOZY1sCd3JnT',
	PhoneAuthenticationUrl: 'https://fair-authentication.toons.byorl.org',
	ArteyeDomain: 'https://sharp-monster.no-zero.net',
});

export const Config = (() => {
	switch (env) {
		case 'dev':
			return DevConfig;
		case 'local':
			return LocalConfig;
		case 'production':
			return ProductionConfig;
		default:
			return LocalConfig;
	}
})();
