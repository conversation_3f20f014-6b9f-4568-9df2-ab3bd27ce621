import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-system';

export type GetRoleQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetRoleQuery = {
	__typename?: 'Query';
	users_me?: {
		__typename?: 'directus_users';
		first_name?: string | null;
		role?: { __typename?: 'directus_roles'; name: string } | null;
	} | null;
};

export const GetRoleDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getRole' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'users_me' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'role' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<GetRoleQuery, GetRoleQueryVariables>;
