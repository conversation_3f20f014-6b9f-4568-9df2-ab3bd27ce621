import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-system';

export type AuthRefreshMutationVariables = Types.Exact<{
	refresh_token: Types.Scalars['String']['input'];
}>;

export type AuthRefreshMutation = {
	__typename?: 'Mutation';
	auth_refresh?: {
		__typename?: 'auth_tokens';
		access_token?: string | null;
		refresh_token?: string | null;
		expires?: any | null;
	} | null;
};

export const AuthRefreshDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'authRefresh' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'refresh_token' },
					},
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'String' },
						},
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'auth_refresh' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'refresh_token' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'refresh_token' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'access_token' },
								},
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'refresh_token' },
								},
								{ kind: 'Field', name: { kind: 'Name', value: 'expires' } },
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<AuthRefreshMutation, AuthRefreshMutationVariables>;
