import { getInitialVariables } from '../getInitialVariables/getInitialVariables';
import { getAuthorizationHeaders } from '$global/features/auth/utils/getAuthorizationHeaders/getAuthorizationHeaders';
import { FairImageInsightsSortFields } from '$gql/types-custom';
import type {
	FairProcessingState,
	ValueFilterOperator,
} from '$gql/types-custom';
import { type GetFairInsightsQueryVariables } from '$lib/custom-queries/__generated__/getFairInsights.generated';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { DEFAULT_FAIR_INSIGHTS_PAGE_SIZE } from '$lib/pages/FairInsightsPage/constants/search';
import {
	FairInsightsFilterFieldNames,
	FairInsightsProcessStateFilter,
} from '$lib/pages/FairInsightsPage/types';
import {
	GetUsersDocument,
	type GetUsersQuery,
} from '$lib/system-queries/__generated__/getUsers.generated';
import { getValueFilter } from '$lib/utils/getSearchRangeFilter/getSearchRangeFilter';
import { getSortWithDirection } from '$lib/utils/getSortWithDirection/getSortWithDirection';
import type { FairInsightsPageLoadEvent } from '$routes/fairs/types';

export const fairInsightsPageLoad = async ({
	parent,
	url,
}: FairInsightsPageLoadEvent) => {
	const parentData = await parent();
	const queryParamsVariables = getInitialVariables(url.searchParams);

	const sort = getSortWithDirection<FairImageInsightsSortFields>(
		url.searchParams,
		FairImageInsightsSortFields.Date
	);

	const queryVariables: GetFairInsightsQueryVariables = {
		input: {
			sort: sort?.length ? sort : undefined,
			limit: queryParamsVariables.pageSize
				? +queryParamsVariables.pageSize
				: DEFAULT_FAIR_INSIGHTS_PAGE_SIZE,
			offset:
				queryParamsVariables.page &&
				DEFAULT_FAIR_INSIGHTS_PAGE_SIZE * (queryParamsVariables.page - 1),
			...(queryParamsVariables.page && {
				offset:
					DEFAULT_FAIR_INSIGHTS_PAGE_SIZE * (queryParamsVariables.page - 1),
			}),
			filters: {
				...(queryParamsVariables[FairInsightsFilterFieldNames.FairTitle] && {
					fairTitle:
						queryParamsVariables[FairInsightsFilterFieldNames.FairTitle],
				}),
				...(queryParamsVariables[FairInsightsFilterFieldNames.Location] && {
					location: queryParamsVariables[FairInsightsFilterFieldNames.Location],
				}),
				...(queryParamsVariables[FairInsightsFilterFieldNames.Photographer] && {
					photographer:
						queryParamsVariables[FairInsightsFilterFieldNames.Photographer],
				}),
				...(queryParamsVariables[FairInsightsFilterFieldNames.DateTaken] && {
					dateTaken: getValueFilter({
						value: queryParamsVariables[FairInsightsFilterFieldNames.DateTaken],
						range: queryParamsVariables[
							FairInsightsFilterFieldNames.DateTakenRange
						] as ValueFilterOperator,
						type: 'date',
					}),
				}),
				...(queryParamsVariables[
					FairInsightsFilterFieldNames.ProcessingState
				] &&
					queryParamsVariables[FairInsightsFilterFieldNames.ProcessingState] !==
						FairInsightsProcessStateFilter.All && {
						processingState: queryParamsVariables[
							FairInsightsFilterFieldNames.ProcessingState
						] as unknown as FairProcessingState,
					}),
			},
		},
	};

	const usersResponse = await gqlClientSystem.request(
		GetUsersDocument,
		{},
		getAuthorizationHeaders(parentData)
	);

	const users = (usersResponse as GetUsersQuery)?.users;

	return {
		...parentData,
		users,
		queryVariables,
	};
};
