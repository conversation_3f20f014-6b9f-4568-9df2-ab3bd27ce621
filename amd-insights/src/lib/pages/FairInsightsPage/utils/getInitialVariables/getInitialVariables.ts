import { FairInsightsFilterFieldNames } from '../../types';
import type {
	FairInsightsProcessStateFilter,
	FairInsightsSearchFormFields,
} from '../../types';
import { PARAM_SEPARATOR } from '$lib/constants/params';
import {
	DEFAULT_FAIR_INSIGHTS_PROCESSING_STATE_FILTER,
	DEFAULT_FAIR_INSIGHTS_PAGE_SIZE,
} from '$lib/pages/FairInsightsPage/constants/search';
import { FAIR_INSIGHTS_DEFAULT_SORT } from '$lib/pages/FairInsightsPage/constants/sort';

const getArrayValuesFromQueryParams = <T>(
	queryParam: string,
	defaults: T[] = []
) => {
	if (!queryParam) {
		return defaults;
	}

	return decodeURIComponent(queryParam)
		.split(PARAM_SEPARATOR)
		.filter((value) => value);
};

const decodeQueryParam = (value: string) => {
	if (!value) {
		return null;
	}

	return decodeURIComponent(value);
};

export const getInitialVariables = (
	searchParams: URLSearchParams
): FairInsightsSearchFormFields => {
	const queryParams = Object.fromEntries(searchParams);

	return {
		[FairInsightsFilterFieldNames.Page]:
			+queryParams[FairInsightsFilterFieldNames.Page] || 1,
		[FairInsightsFilterFieldNames.PageSize]:
			+queryParams[FairInsightsFilterFieldNames.PageSize] ||
			DEFAULT_FAIR_INSIGHTS_PAGE_SIZE,
		[FairInsightsFilterFieldNames.FairTitle]:
			decodeQueryParam(queryParams[FairInsightsFilterFieldNames.FairTitle]) ||
			'',
		[FairInsightsFilterFieldNames.Location]:
			decodeQueryParam(queryParams[FairInsightsFilterFieldNames.Location]) ||
			'',
		[FairInsightsFilterFieldNames.Photographer]: getArrayValuesFromQueryParams(
			queryParams[FairInsightsFilterFieldNames.Photographer]
		),
		[FairInsightsFilterFieldNames.Sort]: getArrayValuesFromQueryParams(
			queryParams[FairInsightsFilterFieldNames.Sort],
			[FAIR_INSIGHTS_DEFAULT_SORT]
		),
		[FairInsightsFilterFieldNames.DateTaken]:
			decodeQueryParam(queryParams[FairInsightsFilterFieldNames.DateTaken]) ||
			'',
		[FairInsightsFilterFieldNames.DateTakenRange]:
			decodeQueryParam(
				queryParams[FairInsightsFilterFieldNames.DateTakenRange]
			) || '',
		[FairInsightsFilterFieldNames.ProcessingState]:
			(decodeQueryParam(
				queryParams[FairInsightsFilterFieldNames.ProcessingState]
			) as FairInsightsProcessStateFilter) ||
			DEFAULT_FAIR_INSIGHTS_PROCESSING_STATE_FILTER,
	};
};
