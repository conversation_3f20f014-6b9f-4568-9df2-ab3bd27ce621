import dayjs from 'dayjs';
import type { FairImageInsightsSortFields } from '$gql/types-custom';
import type { GetFairInsightsQuery } from '$lib/custom-queries/__generated__/getFairInsights.generated';
import { SortFieldToDataFieldMap } from '$lib/pages/FairInsightsPage/constants/sort';

export const sortFairInsights = (
	filteredFairInsights: GetFairInsightsQuery['insights_fairImageInsights']['data'],
	sort: string[]
) => {
	if (!sort || sort.length === 0) {
		return filteredFairInsights;
	}

	const getFieldValue = (
		insight: GetFairInsightsQuery['insights_fairImageInsights']['data'][0],
		field: keyof GetFairInsightsQuery['insights_fairImageInsights']['data'][0]
	) => {
		const value = (insight as any)[field];
		if (field === 'dateTaken') {
			return dayjs(value, 'YYYY-MM-DD').toDate();
		}
		return value;
	};

	return filteredFairInsights.sort((a, b) => {
		for (const field of sort) {
			const isDescending = field.startsWith('-');
			const sortField = (
				isDescending ? field.substring(1) : field
			) as FairImageInsightsSortFields;
			const sortKey = SortFieldToDataFieldMap[sortField];

			const valueA = getFieldValue(a, sortKey);
			const valueB = getFieldValue(b, sortKey);

			if (valueA < valueB) return isDescending ? 1 : -1;
			if (valueA > valueB) return isDescending ? -1 : 1;
		}
		return 0;
	});
};
