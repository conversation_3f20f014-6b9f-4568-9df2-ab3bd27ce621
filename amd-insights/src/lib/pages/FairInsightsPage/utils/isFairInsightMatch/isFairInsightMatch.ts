import dayjs from 'dayjs';
import { FairInsightsProcessStateFilter } from '$lib/pages/FairInsightsPage/types';
import type { GetUsersQuery } from '$lib/system-queries/__generated__/getUsers.generated';

export const matchString = (
	filter: string,
	value: string | undefined | null
) => {
	if (!filter) {
		return true;
	}

	return value?.toLowerCase().includes(filter.toLowerCase());
};

export const matchPhotographer = (
	filter: string[],
	value: string | undefined,
	users: GetUsersQuery['users']
): boolean => {
	if (!filter || !filter.length) {
		return true;
	}

	if (!value) {
		return false;
	}

	const usersToInclude = users.filter((u) => u?.id && filter?.includes(u.id));
	if (usersToInclude.length) {
		return usersToInclude
			.map((user) => `${user.first_name} ${user.last_name}`)
			.includes(value);
	}

	return false;
};

export const matchDate = (
	filter: string,
	value: string | undefined | null,
	dateRange: string
) => {
	if (!filter) {
		return true;
	}

	if (!value) {
		return false;
	}

	const filterDate = dayjs(filter, 'DD/MM/YYYY');
	const valueDate = dayjs(value, 'YYYY-MM-DD');

	switch (dateRange) {
		case 'Equal':
			return valueDate.isSame(filterDate);
		case 'GreaterThan':
			return valueDate.isAfter(filterDate);
		case 'LessThan':
			return valueDate.isBefore(filterDate);
		case 'Between': {
			const dateRange = filter.split('-');
			const filterDateStart = dayjs(dateRange[0], 'DD/MM/YYYY');
			const filterDateEnd = dayjs(dateRange[1], 'DD/MM/YYYY');
			return (
				valueDate.isAfter(filterDateStart) && valueDate.isBefore(filterDateEnd)
			);
		}
		default:
			return false;
	}
};

export const matchProcessingState = (filter: string, value: string) => {
	if (filter === FairInsightsProcessStateFilter.All) {
		return true;
	}

	return matchString(filter, value);
};
