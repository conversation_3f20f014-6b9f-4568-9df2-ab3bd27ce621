<script lang="ts">
	import { FAIR_INSIGHTS_SORT_OPTIONS } from '../constants/sort';
	import { page } from '$app/state';
	import { Button } from '$global/components/Button';
	import type { CheckboxValue } from '$global/components/Checkbox/index.js';
	import { Checkbox } from '$global/components/Checkbox/index.js';
	import { Input } from '$global/components/Input';
	import { InputLabel } from '$global/components/InputLabel/index.js';
	import type { InputWithSelectOption } from '$global/components/InputWithSelect';
	import { InputWithSelect } from '$global/components/InputWithSelect';
	import type { OnChangeEvent } from '$global/components/MultiSelect';
	import { MultiSelect } from '$global/components/MultiSelect';
	import type { MultiSelectOption } from '$global/components/MultiSelect/MultiSelect.svelte';
	import type { SelectChangeEvent } from '$global/components/Select';
	import { Select } from '$global/components/Select';
	import { Txt } from '$global/components/Txt';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { ValueFilterOperator } from '$gql/types-custom';
	import {
		NEW_SEARCH_RANGE_OPTIONS,
		SEARCH_RANGE_PLACEHOLDERS_FULL,
	} from '$lib/constants/search-range-options';
	import {
		DEFAULT_FAIR_INSIGHTS_PAGE_SIZE,
		DEFAULT_FAIR_INSIGHTS_PROCESSING_STATE_FILTER,
		FAIR_INSIGHTS_PAGE_SIZE_OPTIONS,
		FAIR_INSIGHTS_PROCESSING_STATE_OPTIONS,
	} from '$lib/pages/FairInsightsPage/constants/search';
	import type { FairInsightsSearchFormFields } from '$lib/pages/FairInsightsPage/types';
	import { FairInsightsFilterFieldNames } from '$lib/pages/FairInsightsPage/types';
	import type { FairInsightsPageData } from '$routes/fairs/types';

	interface Props {
		onSearch: () => void;
		onClear: () => void;
		onFilter: (field: FairInsightsFilterFieldNames, value: unknown) => void;
		onDynamicModeChange: (enabled: boolean) => void;
		dynamicModeEnabled: boolean;
		searchParams: FairInsightsSearchFormFields;
	}

	const data = getPageData<FairInsightsPageData>(page.data);
	const { users } = data;

	let {
		onSearch,
		onFilter,
		onClear,
		onDynamicModeChange,
		dynamicModeEnabled,
		searchParams,
	}: Props = $props();

	let userOptions: MultiSelectOption[] = users.map((user) => ({
		label: [user.first_name, user.last_name].filter(Boolean).join(' '),
		value: String(user.id),
	}));

	const findMultiselectOptions = (
		values: string[],
		options: MultiSelectOption[]
	) => {
		return values
			.map((val) => options.find((option) => option.value === val))
			.filter(Boolean) as MultiSelectOption[];
	};

	const sort: MultiSelectOption[] = $derived(
		findMultiselectOptions(
			searchParams[FairInsightsFilterFieldNames.Sort],
			FAIR_INSIGHTS_SORT_OPTIONS
		)
	);

	const fairTitle = $derived(
		searchParams[FairInsightsFilterFieldNames.FairTitle]
	);

	const photographer: MultiSelectOption[] = $derived(
		findMultiselectOptions(
			searchParams[FairInsightsFilterFieldNames.Photographer],
			userOptions
		)
	);

	const location = $derived(
		searchParams[FairInsightsFilterFieldNames.Location]
	);

	const dateTakenRange = $derived(
		searchParams[FairInsightsFilterFieldNames.DateTakenRange] ||
			ValueFilterOperator.Equal
	);

	const dateTaken = $derived(
		searchParams[FairInsightsFilterFieldNames.DateTaken]
	);

	const processingState = $derived(
		searchParams[FairInsightsFilterFieldNames.ProcessingState] ||
			(DEFAULT_FAIR_INSIGHTS_PROCESSING_STATE_FILTER as FairInsightsSearchFormFields[FairInsightsFilterFieldNames.ProcessingState])
	);

	const pageSize = $derived(
		searchParams[FairInsightsFilterFieldNames.PageSize] ||
			DEFAULT_FAIR_INSIGHTS_PAGE_SIZE
	);

	const dataCyPrefix = 'fair-insights-search'; // Unique prefix for data-cy attributes

	// Handles the click event for the search button
	const handleSearchClick = () => {
		onSearch();
	};

	// Handles the click event for the clear button
	const handleClearClick = () => {
		onClear();
	};

	const handleFilterChange = (
		field: FairInsightsFilterFieldNames,
		value: unknown
	) => {
		onFilter(field, value);
	};

	const handleFilterInputChange = (e?: Event) => {
		if (e) {
			const target = e.target as HTMLInputElement;
			const field = target.name as FairInsightsFilterFieldNames;
			const value = target.value;
			handleFilterChange(field, value);
		}
	};

	const handleMultiSelectChange =
		(field: FairInsightsFilterFieldNames) =>
		(_: OnChangeEvent, selected: MultiSelectOption[]) => {
			if (selected) {
				handleFilterChange(
					field,
					selected.map((option) => option.value)
				);
			}
		};

	const handleSelectChange =
		(field: FairInsightsFilterFieldNames) => (e: SelectChangeEvent) => {
			if (e) {
				handleFilterChange(field, e.detail.value);
			}
		};

	const handleInputSelectChange =
		(field: FairInsightsFilterFieldNames) => (e: InputWithSelectOption) => {
			if (e) {
				handleFilterChange(field, e.value);
			}
		};

	const handleDynamicModeChange = (enabled: CheckboxValue) => {
		if (enabled === 'indeterminate') {
			return;
		}

		onDynamicModeChange(enabled);
	};
</script>

<div class="rounded-md border border-gray-200 bg-white">
	<div class="flex items-center justify-between border-b border-gray-200 p-4">
		<Txt variant="h6">Search Fair Insights</Txt>
	</div>
	<div
		class="grid grid-cols-1 gap-4 border-b border-gray-200 p-4 md:grid-cols-4 lg:grid-cols-5"
	>
		<!-- Fair Title -->
		<div class="md:col-span-2 lg:col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-fair-title`}
				name={FairInsightsFilterFieldNames.FairTitle}
				placeholder="Enter fair title"
				label="Fair Title"
				value={fairTitle}
				size="sm"
				oninput={handleFilterInputChange}
			/>
		</div>

		<!-- Photographer -->
		<div class="md:col-span-2 lg:col-span-1">
			<MultiSelect
				name={FairInsightsFilterFieldNames.Photographer}
				dataCy={`${dataCyPrefix}-photographer`}
				label="Photographer"
				selected={photographer}
				placeholder="Photographer"
				options={userOptions}
				size="sm"
				onChange={handleMultiSelectChange(
					FairInsightsFilterFieldNames.Photographer
				)}
			/>
		</div>

		<!-- Location -->
		<div class="md:col-span-2 lg:col-span-1">
			<Input
				dataCy={`${dataCyPrefix}-location`}
				name={FairInsightsFilterFieldNames.Location}
				placeholder="Enter location"
				label="Location"
				value={location}
				size="sm"
				oninput={handleFilterInputChange}
			/>
		</div>

		<!-- Date Taken -->
		<div class="md:col-span-2 lg:col-span-1">
			<InputWithSelect
				size="sm"
				label="Date Taken"
				dataCy={`${dataCyPrefix}-date-taken`}
				name={FairInsightsFilterFieldNames.DateTaken}
				selectValue={dateTakenRange}
				inputValue={dateTaken}
				options={NEW_SEARCH_RANGE_OPTIONS}
				placeholder={SEARCH_RANGE_PLACEHOLDERS_FULL[dateTakenRange]}
				onchange={handleFilterInputChange}
				onSelectChange={handleInputSelectChange(
					FairInsightsFilterFieldNames.DateTakenRange
				)}
			/>
		</div>

		<!-- Processing State -->
		<div class="md:col-span-2 lg:col-span-1">
			<Select
				name={FairInsightsFilterFieldNames.ProcessingState}
				dataCy={`${dataCyPrefix}-processing-state`}
				label="Processing State"
				ariaLabel="Processing State"
				value={processingState}
				placeholder="Processing State"
				options={FAIR_INSIGHTS_PROCESSING_STATE_OPTIONS}
				size="sm"
				onchange={handleSelectChange(
					FairInsightsFilterFieldNames.ProcessingState
				)}
			/>
		</div>

		<!-- Sort By -->
		<div class="md:col-span-2 lg:col-span-1">
			<MultiSelect
				name={FairInsightsFilterFieldNames.Sort}
				dataCy={`${dataCyPrefix}-sort-by`}
				label="Sort by"
				selected={sort}
				placeholder="Sort by"
				options={FAIR_INSIGHTS_SORT_OPTIONS}
				size="sm"
				onChange={handleMultiSelectChange(FairInsightsFilterFieldNames.Sort)}
			/>
		</div>

		<!-- Page Size -->
		<div class="md:col-span-2 lg:col-span-1">
			<Select
				name={FairInsightsFilterFieldNames.PageSize}
				dataCy={`${dataCyPrefix}-page-szie`}
				label="Page Size"
				value={pageSize.toString()}
				placeholder="Page Size"
				ariaLabel="Page Size"
				options={FAIR_INSIGHTS_PAGE_SIZE_OPTIONS}
				size="sm"
				onchange={handleSelectChange(FairInsightsFilterFieldNames.PageSize)}
			/>
		</div>

		<!-- Dynamic Mode -->
		<div class="flex items-center justify-end md:col-span-2 lg:col-span-1">
			<div class="ml-auto">
				<InputLabel dataCy={`${dataCyPrefix}-dynamic-mode-label`}>
					<Checkbox
						dataCy={`${dataCyPrefix}-dynamic-mode`}
						bind:checked={dynamicModeEnabled}
						onChange={handleDynamicModeChange}
					/>
					<span class="ml-2">Enable dynamic mode</span>
				</InputLabel>
			</div>
		</div>
	</div>
	<div class="flex flex-wrap gap-4 p-4">
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-search`}
			class="w-full sm:w-auto sm:min-w-[200px]"
			onclick={handleSearchClick}
			disabled={dynamicModeEnabled}
		>
			Search
		</Button>
		<Button
			size="md"
			dataCy={`${dataCyPrefix}-clear`}
			variant="secondary"
			onclick={handleClearClick}
		>
			Clear search fields
		</Button>
	</div>
</div>
