import { FairImageInsightsSortFields } from '$gql/types-custom';
import type { GetFairInsightsQuery } from '$lib/custom-queries/__generated__/getFairInsights.generated';
import { getDescendingDirection } from '$lib/utils/getDescendingDirection/getDescendingDirection';
import { sortByField } from '$lib/utils/sortByField/sortByField';

export const SortFieldToDataFieldMap: Record<
	FairImageInsightsSortFields,
	keyof GetFairInsightsQuery['insights_fairImageInsights']['data'][0]
> = {
	[FairImageInsightsSortFields.Date]: 'dateTaken',
	[FairImageInsightsSortFields.FairTitle]: 'title',
	[FairImageInsightsSortFields.Photographer]: 'photographer',
	[FairImageInsightsSortFields.UploadedItems]: 'uploaded',
	[FairImageInsightsSortFields.CropsToCheck]: 'awaitingReview',
	[FairImageInsightsSortFields.CropsCompleted]: 'cropped',
	[FairImageInsightsSortFields.CropsToBeMatched]: 'cropsToBeMatched',
	[FairImageInsightsSortFields.ArtworksToProcess]: 'artworksAwaitingReview',
	[FairImageInsightsSortFields.ArtworksCompleted]: 'artworksCompleted',
};

export const SortFieldToLabelMap: Record<FairImageInsightsSortFields, string> =
	{
		[FairImageInsightsSortFields.Date]: 'Date of capture',
		[FairImageInsightsSortFields.FairTitle]: 'Fair Title',
		[FairImageInsightsSortFields.Photographer]: 'Photographer',
		[FairImageInsightsSortFields.UploadedItems]: 'No. Uploaded Items',
		[FairImageInsightsSortFields.CropsToCheck]: 'Crops to check',
		[FairImageInsightsSortFields.CropsCompleted]: 'Crops completed',
		[FairImageInsightsSortFields.CropsToBeMatched]: 'Crops to be matched',
		[FairImageInsightsSortFields.ArtworksToProcess]: 'Artworks to process',
		[FairImageInsightsSortFields.ArtworksCompleted]: 'Artworks completed',
	};

// Define the sort options based on the OCR text
export const FAIR_INSIGHTS_SORT_OPTIONS = sortByField(
	Object.values(FairImageInsightsSortFields).flatMap((key) => [
		{
			label: `${SortFieldToLabelMap[key]} (Asc)`,
			value: key,
		},
		{
			label: `${SortFieldToLabelMap[key]} (Desc)`,
			value: getDescendingDirection(key),
		},
	]),
	'label' // Sort the dropdown options alphabetically by label
);

// Define the default sort order based on OCR: "Ordered by the most recent date of capture"
export const FAIR_INSIGHTS_DEFAULT_SORT = getDescendingDirection(
	FairImageInsightsSortFields.Date
);
