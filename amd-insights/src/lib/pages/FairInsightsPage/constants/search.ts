import { FairInsightsProcessStateFilter } from '$lib/pages/FairInsightsPage/types';

export const DEFAULT_FAIR_INSIGHTS_PROCESSING_STATE_FILTER =
	FairInsightsProcessStateFilter.All;

export const FAIR_INSIGHTS_PROCESSING_STATE_OPTIONS = [
	{ label: 'All', value: FairInsightsProcessStateFilter.All },
	{
		label: 'Coordinate Extraction Failed',
		value: FairInsightsProcessStateFilter.HasCoordinateExtractionFailed,
	},
	{
		label: 'Awaiting Coordinate Extraction',
		value: FairInsightsProcessStateFilter.HasAwaitingCoordinateExtraction,
	},
	{
		label: 'Crops to check',
		value: FairInsightsProcessStateFilter.HasCropsToCheck,
	},
	{
		label: 'Awaiting Cropping',
		value: FairInsightsProcessStateFilter.HasAwaitingCropping,
	},
	{
		label: 'Cropping Failed',
		value: FairInsightsProcessStateFilter.HasCroppingFailed,
	},
	{
		label: 'Crops to be matched',
		value: FairInsightsProcessStateFilter.HasCropsToBeMatched,
	},
	{
		label: 'Artwork Processing Failed',
		value: FairInsightsProcessStateFilter.HasArtworkProcessingFailed,
	},

	{
		label: 'Artworks to process',
		value: FairInsightsProcessStateFilter.HasArtworksToProcess,
	},
	{
		label: 'Processing completed',
		value: FairInsightsProcessStateFilter.ProcessingCompleted,
	},
];

export const FAIR_INSIGHTS_PAGE_SIZE_OPTIONS = [10, 25, 50, 100].map((size) => {
	return { label: size.toString(), value: size.toString() };
});

export const DEFAULT_FAIR_INSIGHTS_PAGE_SIZE = 10;
