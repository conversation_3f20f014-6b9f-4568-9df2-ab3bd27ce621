<script lang="ts">
	import classNames from 'classnames';
	import dayjs from 'dayjs';
	import { CircularProgress } from '$global/components/CircularProgress';
	import { Pagination } from '$global/components/Pagination';
	import {
		TableHeaderRow,
		TableHeader,
		TableBody,
		TableRow,
		TableCell,
		TableNoResults,
		getCellWidth,
	} from '$global/components/Table';
	import { FairProcessingState } from '$gql/types-custom';
	import { type GetFairInsightsQuery } from '$lib/custom-queries/__generated__/getFairInsights.generated';

	interface Props {
		fairInsights:
			| GetFairInsightsQuery['insights_fairImageInsights']['data']
			| undefined;
		pageNumber: number | null | undefined;
		totalCount: number;
		onClickPage: (e: Event | undefined, page?: number | null) => void;
		isFetching: boolean;
		dynamicModeEnabled: boolean;
		pageSize: number;
	}

	let {
		pageNumber,
		onClickPage,
		fairInsights,
		isFetching,
		totalCount,
		dynamicModeEnabled,
		pageSize,
	}: Props = $props();

	const headers = [
		'Fair title',
		'Location',
		'Date Taken',
		'Photographer',
		'No. Upload Items',
		'Crops to-check',
		'Crops completed',
		'Crops to be matched',
		'Artworks to process',
		'Artworks processed',
		'Processing State',
	];

	const formatExhibition = (
		fairInsight: GetFairInsightsQuery['insights_fairImageInsights']['data'][number]
	) => {
		return [
			fairInsight.title,
			fairInsight.location || '',
			fairInsight.dateTaken
				? dayjs(fairInsight.dateTaken).format('DD-MM-YYYY')
				: '',
			fairInsight.photographer || '',
			fairInsight.uploaded,
			fairInsight.awaitingReview,
			fairInsight.cropped,
			fairInsight.cropsToBeMatched,
			fairInsight.artworksAwaitingReview,
			fairInsight.artworksCompleted,
			fairInsight.processingState,
		];
	};

	// When to show completed row as green
	const isComplete = (
		fairInsight: GetFairInsightsQuery['insights_fairImageInsights']['data'][number]
	) => {
		return (
			fairInsight.processingState === FairProcessingState.ProcessingCompleted
		);
	};

	// When a row has anything that's failed
	const hasFailed = (
		fairInsight: GetFairInsightsQuery['insights_fairImageInsights']['data'][number]
	) => {
		return (
			fairInsight.processingState ===
				FairProcessingState.HasCoordinateExtractionFailed ||
			fairInsight.processingState === FairProcessingState.HasCroppingFailed ||
			fairInsight.processingState ===
				FairProcessingState.HasArtworkProcessingFailed
		);
	};

	const dataCyPrefix = 'fair-insights-table';
</script>

<table class="w-full table-auto bg-white">
	<TableHeaderRow dataCy={dataCyPrefix}>
		{#each headers as header, i}
			<TableHeader
				dataCy={dataCyPrefix}
				width={getCellWidth(i, '0rem', headers)}
			>
				{header}
			</TableHeader>
		{/each}
	</TableHeaderRow>
	{#if fairInsights}
		<TableBody dataCy={dataCyPrefix}>
			{#each fairInsights as fairInsight, i}
				<TableRow
					index={i}
					dataCy={dataCyPrefix}
					class={classNames(
						{
							'bg-red-400': hasFailed(fairInsight),
						},
						{
							'bg-green-400': isComplete(fairInsight),
						}
					)}
				>
					{@const formattedFairInsight = formatExhibition(fairInsight)}
					{#each formattedFairInsight as formattedFairInsightValue, j}
						<TableCell
							dataCy={dataCyPrefix}
							width={getCellWidth(i, '0rem', headers)}
							content={formattedFairInsightValue}
						>
							{formattedFairInsightValue}
						</TableCell>
					{/each}
				</TableRow>
			{/each}

			{#if !fairInsights.length}
				<TableNoResults dataCy={dataCyPrefix} colspan={headers.length}
					>No fair insights to display
				</TableNoResults>
			{/if}
		</TableBody>
	{/if}
</table>

{#if isFetching}
	<div class="mt-3 flex justify-center">
		<CircularProgress dataCy={dataCyPrefix} />
	</div>
{:else if !dynamicModeEnabled}
	<div class="mt-2 flex justify-end">
		<Pagination
			onClick={onClickPage}
			dataCy={dataCyPrefix}
			currentPage={pageNumber || 1}
			limit={pageSize}
			total={totalCount}
		/>
	</div>
{/if}
