export enum FairInsightsFilterFieldNames {
	FairTitle = 'fairTitle',
	Photographer = 'photographer',
	Location = 'location',
	DateTaken = 'dateTaken',
	DateTakenRange = 'dateTakenRange',
	ProcessingState = 'processingState',
	Sort = 'sort',
	Page = 'page',
	PageSize = 'pageSize',
}

export enum FairInsightsProcessStateFilter {
	All = 'All',
	HasCoordinateExtractionFailed = 'HasCoordinateExtractionFailed',
	HasAwaitingCoordinateExtraction = 'HasAwaitingCoordinateExtraction',
	HasCropsToCheck = 'HasCropsToCheck',
	HasAwaitingCropping = 'HasAwaitingCropping',
	HasCroppingFailed = 'HasCroppingFailed',
	HasArtworkProcessingFailed = 'HasArtworkProcessingFailed',
	HasCropsToBeMatched = 'HasCropsToBeMatched',
	HasArtworksToProcess = 'HasArtworksToProcess',
	ProcessingCompleted = 'ProcessingCompleted',
}

export interface FairInsightsSearchFormFields {
	[FairInsightsFilterFieldNames.Sort]: string[];
	[FairInsightsFilterFieldNames.FairTitle]: string;
	[FairInsightsFilterFieldNames.Photographer]: string[];
	[FairInsightsFilterFieldNames.Location]: string;
	[FairInsightsFilterFieldNames.DateTaken]: string;
	[FairInsightsFilterFieldNames.DateTakenRange]: string;
	[FairInsightsFilterFieldNames.ProcessingState]: FairInsightsProcessStateFilter;
	[FairInsightsFilterFieldNames.PageSize]: number;
	[FairInsightsFilterFieldNames.Page]: number | null | undefined;
}
