<script lang="ts">
	import type { Nullable } from '@melt-ui/svelte/internal/types';
	import { FairInsightsTable } from './FairInsightsTable';
	import type { FairInsightsSearchFormFields } from './types';
	import { FairInsightsFilterFieldNames } from './types';
	import { getInitialVariables } from './utils/getInitialVariables/getInitialVariables';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Breadcrumbs } from '$global/components/Breadcrumbs';
	import { Container } from '$global/components/Container';
	import { getPageData } from '$global/utils/getPageData/getPageData';
	import { getInfiniteData } from '$global/utils/infinite-loading/getInfiniteData';
	import { infiniteQuery } from '$global/utils/infinite-loading/infiniteQuery';
	import { PageBody } from '$lib/components/PageBody';
	import { Routes } from '$lib/constants/routes';
	import {
		GetFairInsightsDocument,
		type GetFairInsightsQuery,
	} from '$lib/custom-queries/__generated__/getFairInsights.generated';
	import { DEFAULT_FAIR_INSIGHTS_PAGE_SIZE } from '$lib/pages/FairInsightsPage/constants/search';
	import { FairInsightsSearchForm } from '$lib/pages/FairInsightsPage/FairInsightsSearchForm';
	import {
		matchDate,
		matchPhotographer,
		matchProcessingState,
		matchString,
	} from '$lib/pages/FairInsightsPage/utils/isFairInsightMatch/isFairInsightMatch';
	import { sortFairInsights } from '$lib/pages/FairInsightsPage/utils/sortFairInsights/sortFairInsights';
	import { getCustomGqlClient } from '$lib/utils/getGqlClient/getGqlClient';
	import { getSearchParamFromStringArray } from '$lib/utils/getSearchParamFromStringArray/getSearchParamFromStringArray';
	import { getSearchParamString } from '$lib/utils/getSearchParamString/getSearchParamString';
	import type { FairInsightsPageData } from '$routes/fairs/types';

	const dataCyPrefix = 'fair-insights';

	const crumbs = [{ label: 'Insights', href: Routes.Home }, { label: 'Fairs' }];

	let searchParams = $state(getInitialVariables(page.url.searchParams));

	let dynamicModeEnabled = $state(false);

	const data = getPageData<FairInsightsPageData>(page.data);
	const { users } = data;

	let queryVariables = $derived(page.data.queryVariables);
	const gqlClient = getCustomGqlClient(page.data as FairInsightsPageData);

	let query = $derived(
		infiniteQuery({
			gqlClient,
			variables: queryVariables,
			document: GetFairInsightsDocument,
			limit: DEFAULT_FAIR_INSIGHTS_PAGE_SIZE,
		})
	);

	let fairInsights = $derived(
		getInfiniteData({
			query: $query,
			transform: (data) => {
				return data.insights_fairImageInsights.data;
			},
		})
	);

	let totalCount = $derived(
		$query.data?.pages[0]?.insights_fairImageInsights.totalCount || 0
	);

	let filteredFairInsights: Nullable<
		GetFairInsightsQuery['insights_fairImageInsights']['data']
	> = $state(null);

	const handlePage = (e: Event | undefined, page?: number | null) => {
		if (e) {
			e.preventDefault();
		}
		searchParams = {
			...searchParams,
			[FairInsightsFilterFieldNames.Page]: page,
		};
		handleSearch();
	};

	const handleSearch = () => {
		const queryParams = formatParamString(searchParams);
		// Navigate to the search results page with the new query parameters
		goto(`${Routes.Fairs}?${queryParams}`); // Adjust Routes key
	};

	const handleClear = () => {
		const initialSearchParams = getInitialVariables(new URLSearchParams());
		Object.keys(searchParams).forEach((key) => {
			searchParams = {
				...searchParams,
				[key]: initialSearchParams[key as FairInsightsFilterFieldNames],
			};
		});
	};

	const handleDynamicModeChanged = (enabled: boolean) => {
		dynamicModeEnabled = enabled;
		filteredFairInsights = enabled ? fairInsights || null : null;
	};

	const handleFilter = (field: FairInsightsFilterFieldNames, value: any) => {
		searchParams = { ...searchParams, [field]: value };

		if (!fairInsights || !dynamicModeEnabled) {
			return;
		}

		filteredFairInsights = sortFairInsights(
			fairInsights.filter((insight) => {
				return (
					matchString(
						searchParams[FairInsightsFilterFieldNames.FairTitle],
						insight.title
					) &&
					matchString(
						searchParams[FairInsightsFilterFieldNames.Location],
						insight.location
					) &&
					matchPhotographer(
						searchParams[FairInsightsFilterFieldNames.Photographer],
						insight.photographer,
						users
					) &&
					matchDate(
						searchParams[FairInsightsFilterFieldNames.DateTaken],
						insight.dateTaken,
						searchParams[FairInsightsFilterFieldNames.DateTakenRange]
					) &&
					matchProcessingState(
						searchParams[FairInsightsFilterFieldNames.ProcessingState],
						insight.processingState
					)
				);
			}),
			searchParams[FairInsightsFilterFieldNames.Sort]
		);
	};

	const formatParamString = (values: FairInsightsSearchFormFields) => {
		const {
			sort,
			fairTitle,
			location,
			photographer,
			dateTaken,
			dateTakenRange,
			processingState,
			pageSize,
			page,
		} = values;

		const params: Record<FairInsightsFilterFieldNames, string | null> = {
			[FairInsightsFilterFieldNames.Sort]: getSearchParamFromStringArray(sort),
			[FairInsightsFilterFieldNames.FairTitle]: fairTitle,
			[FairInsightsFilterFieldNames.Location]: location,
			[FairInsightsFilterFieldNames.Photographer]:
				getSearchParamFromStringArray(photographer),
			[FairInsightsFilterFieldNames.DateTaken]: dateTaken,
			[FairInsightsFilterFieldNames.DateTakenRange]: dateTakenRange,
			[FairInsightsFilterFieldNames.ProcessingState]: processingState,
			[FairInsightsFilterFieldNames.PageSize]: pageSize.toString(),
			[FairInsightsFilterFieldNames.Page]: page?.toString() || '1',
		};

		return getSearchParamString(params);
	};
</script>

<PageBody>
	<Breadcrumbs
		dataCy={dataCyPrefix}
		class="mb-11 mt-0 max-w-[3000px] lg:mb-11 lg:mt-0"
		{crumbs}
	/>
	<Container dataCy={dataCyPrefix} class="max-w-[3000px]">
		<FairInsightsSearchForm
			onSearch={handleSearch}
			onFilter={handleFilter}
			onClear={handleClear}
			onDynamicModeChange={handleDynamicModeChanged}
			{dynamicModeEnabled}
			{searchParams}
		/>

		<FairInsightsTable
			pageNumber={searchParams.page}
			{totalCount}
			onClickPage={handlePage}
			fairInsights={filteredFairInsights || fairInsights}
			isFetching={$query.isFetching}
			{dynamicModeEnabled}
			pageSize={searchParams.pageSize}
		/>
	</Container>
</PageBody>
