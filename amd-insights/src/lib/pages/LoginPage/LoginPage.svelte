<script lang="ts">
	import { page } from '$app/state';
	import { LoginPage } from '$global/features/auth/components/LoginPage';
	import { PageBody } from '$lib/components/PageBody';
	import { LoginVariant } from '$lib/constants/login';
</script>

<LoginPage
	class="fixed top-0 h-[100vh] w-full bg-gray-50"
	{PageBody}
	variant={LoginVariant}
	returnUrl={page.url.href.split('?returnUrl=')[1] || '/'}
/>
