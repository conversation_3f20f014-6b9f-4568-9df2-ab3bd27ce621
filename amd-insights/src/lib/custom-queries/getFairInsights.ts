import { gql } from 'graphql-tag';

export const GET_FAIR_INSIGHTS = gql`
	query getFairInsights($input: FairImageInsightsInput) {
		insights_fairImageInsights(input: $input) {
			totalCount
			data {
				fairId
				title
				location
				photographer
				dateTaken
				uploaded
				awaitingCoordinateExtraction
				coordinateExtractionFailed
				awaitingReview
				awaitingCropping
				cropped
				croppingFailed
				excluded
				cropsToBeMatched
				artworksTotal
				artworksAwaitingReview
				artworksCompleted
				artworksFailed
				processingState
			}
		}
	}
`;
