import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
import type * as Types from '../../../gql/types-custom';

export type GetFairInsightsQueryVariables = Types.Exact<{
	input?: Types.InputMaybe<Types.FairImageInsightsInput>;
}>;

export type GetFairInsightsQuery = {
	__typename?: 'Query';
	insights_fairImageInsights: {
		__typename?: 'FairImageInsightsResponse';
		totalCount: number;
		data: Array<{
			__typename?: 'FairImageInsights';
			fairId: string;
			title: string;
			location?: string | null;
			photographer: string;
			dateTaken?: any | null;
			uploaded: number;
			awaitingCoordinateExtraction: number;
			coordinateExtractionFailed: number;
			awaitingReview: number;
			awaitingCropping: number;
			cropped: number;
			croppingFailed: number;
			excluded: number;
			cropsToBeMatched: number;
			artworksTotal: number;
			artworksAwaitingReview: number;
			artworksCompleted: number;
			artworksFailed: number;
			processingState: Types.FairProcessingState;
		}>;
	};
};

export const GetFairInsightsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'getFairInsights' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: {
						kind: 'Variable',
						name: { kind: 'Name', value: 'input' },
					},
					type: {
						kind: 'NamedType',
						name: { kind: 'Name', value: 'FairImageInsightsInput' },
					},
				},
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insights_fairImageInsights' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'input' },
								value: {
									kind: 'Variable',
									name: { kind: 'Name', value: 'input' },
								},
							},
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'totalCount' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'data' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'fairId' },
											},
											{ kind: 'Field', name: { kind: 'Name', value: 'title' } },
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'location' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'photographer' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'dateTaken' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'uploaded' },
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'awaitingCoordinateExtraction',
												},
											},
											{
												kind: 'Field',
												name: {
													kind: 'Name',
													value: 'coordinateExtractionFailed',
												},
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'awaitingReview' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'awaitingCropping' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'cropped' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'croppingFailed' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'excluded' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'cropsToBeMatched' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworksTotal' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworksAwaitingReview' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworksCompleted' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'artworksFailed' },
											},
											{
												kind: 'Field',
												name: { kind: 'Name', value: 'processingState' },
											},
										],
									},
								},
							],
						},
					},
				],
			},
		},
	],
} as unknown as DocumentNode<
	GetFairInsightsQuery,
	GetFairInsightsQueryVariables
>;
