import { GraphQLClient } from 'graphql-request';
import { Config } from './constants/config';
import { browser } from '$app/environment';

let endpoint: string;

if (browser) {
	// Code is running in the browser
	endpoint = Config.GraphqlApiUrl;
} else {
	// Code is running on the server
	endpoint = `${Config.GraphqlApiDomain}/graphql`;
}

// Ensure endpoints are defined
if (!endpoint) {
	throw new Error(
		'GraphQL endpoint is not defined. Check environment variables.'
	);
}

export const gqlClient = new GraphQLClient(endpoint, {
	// Optional: add headers, fetch implementation etc.
	// fetch: fetch // Use SvelteKit's fetch for server-side load functions if needed
});

// Optional helper function to use SvelteKit's fetch in load functions
export function getClient(fetchFn?: typeof fetch): GraphQLClient {
	if (!browser && fetchFn) {
		// On server, potentially use the fetch provided by load functions
		// This helps with credential forwarding if needed, though maybe less relevant for internal calls
		return new GraphQLClient(endpoint, { fetch: fetchFn });
	}
	return gqlClient; // Return the default client instance
}
