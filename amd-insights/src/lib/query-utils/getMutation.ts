// TODO : write unit tests

import type { TypedDocumentNode } from '@graphql-typed-document-node/core';
import { type Variables } from 'graphql-request';
import { gqlClient } from '../gqlClient';
import { getMutation as globalGetMutation } from '$global/query-utils/getMutation';

export const getMutation = <T, V extends Variables = Variables>(
	document: TypedDocumentNode<T, V>,
	requestHeaders?: Parameters<typeof gqlClient.request>[0]['requestHeaders']
) => globalGetMutation(gqlClient, document, requestHeaders);
