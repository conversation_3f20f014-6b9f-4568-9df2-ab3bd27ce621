import { PUBLIC_APP_ENV } from '$env/static/public';
import { generateHooks } from '$global/features/auth/routes/generateHooks/generateHooks';
import { Cookies } from '$lib/constants/cookies';
import { gqlClientSystem } from '$lib/gqlClientSystem';
import { AuthRefreshDocument } from '$lib/system-queries/__generated__/refresh.generated';

export const handle = generateHooks({
	cookieKey: Cookies.User,
	RefreshDocument: AuthRefreshDocument,
	gqlClient: gqlClientSystem,
	env: PUBLIC_APP_ENV,
});
