export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	GraphQLBigInt: { input: any; output: any };
	GraphQLStringOrFloat: { input: any; output: any };
	JSON: { input: any; output: any };
};

export enum EventEnum {
	Create = 'create',
	Delete = 'delete',
	Update = 'update',
}

export type Library = {
	__typename?: 'Library';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	slug?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Library_Aggregated = {
	__typename?: 'Library_aggregated';
	avg?: Maybe<Library_Aggregated_Fields>;
	avgDistinct?: Maybe<Library_Aggregated_Fields>;
	count?: Maybe<Library_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Library_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Library_Aggregated_Fields>;
	min?: Maybe<Library_Aggregated_Fields>;
	sum?: Maybe<Library_Aggregated_Fields>;
	sumDistinct?: Maybe<Library_Aggregated_Fields>;
};

export type Library_Aggregated_Count = {
	__typename?: 'Library_aggregated_count';
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	slug?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Library_Aggregated_Fields = {
	__typename?: 'Library_aggregated_fields';
	sort?: Maybe<Scalars['Float']['output']>;
};

export type Library_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Library_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Library_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	slug?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
};

export type Library_Mutated = {
	__typename?: 'Library_mutated';
	data?: Maybe<Library>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Location = {
	__typename?: 'Location';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Location_Aggregated = {
	__typename?: 'Location_aggregated';
	avg?: Maybe<Location_Aggregated_Fields>;
	avgDistinct?: Maybe<Location_Aggregated_Fields>;
	count?: Maybe<Location_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Location_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Location_Aggregated_Fields>;
	min?: Maybe<Location_Aggregated_Fields>;
	sum?: Maybe<Location_Aggregated_Fields>;
	sumDistinct?: Maybe<Location_Aggregated_Fields>;
};

export type Location_Aggregated_Count = {
	__typename?: 'Location_aggregated_count';
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Location_Aggregated_Fields = {
	__typename?: 'Location_aggregated_fields';
	sort?: Maybe<Scalars['Float']['output']>;
};

export type Location_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Location_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Location_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
};

export type Location_Mutated = {
	__typename?: 'Location_mutated';
	data?: Maybe<Location>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Mutation = {
	__typename?: 'Mutation';
	create_Library_item?: Maybe<Library>;
	create_Library_items: Array<Library>;
	create_Location_item?: Maybe<Location>;
	create_Location_items: Array<Location>;
	create_Publication_Stock_item?: Maybe<Publication_Stock>;
	create_Publication_Stock_items: Array<Publication_Stock>;
	create_Publication_item?: Maybe<Publication>;
	create_Publication_items: Array<Publication>;
	create_Stock_item?: Maybe<Stock>;
	create_Stock_items: Array<Stock>;
	create_Subject_item?: Maybe<Subject>;
	create_Subject_items: Array<Subject>;
	create_Type_item?: Maybe<Type>;
	create_Type_items: Array<Type>;
	delete_Library_item?: Maybe<Delete_One>;
	delete_Library_items?: Maybe<Delete_Many>;
	delete_Location_item?: Maybe<Delete_One>;
	delete_Location_items?: Maybe<Delete_Many>;
	delete_Publication_Stock_item?: Maybe<Delete_One>;
	delete_Publication_Stock_items?: Maybe<Delete_Many>;
	delete_Publication_item?: Maybe<Delete_One>;
	delete_Publication_items?: Maybe<Delete_Many>;
	delete_Stock_item?: Maybe<Delete_One>;
	delete_Stock_items?: Maybe<Delete_Many>;
	delete_Subject_item?: Maybe<Delete_One>;
	delete_Subject_items?: Maybe<Delete_Many>;
	delete_Type_item?: Maybe<Delete_One>;
	delete_Type_items?: Maybe<Delete_Many>;
	update_Library_batch: Array<Library>;
	update_Library_item?: Maybe<Library>;
	update_Library_items: Array<Library>;
	update_Location_batch: Array<Location>;
	update_Location_item?: Maybe<Location>;
	update_Location_items: Array<Location>;
	update_Publication_Stock_batch: Array<Publication_Stock>;
	update_Publication_Stock_item?: Maybe<Publication_Stock>;
	update_Publication_Stock_items: Array<Publication_Stock>;
	update_Publication_batch: Array<Publication>;
	update_Publication_item?: Maybe<Publication>;
	update_Publication_items: Array<Publication>;
	update_Stock_batch: Array<Stock>;
	update_Stock_item?: Maybe<Stock>;
	update_Stock_items: Array<Stock>;
	update_Subject_batch: Array<Subject>;
	update_Subject_item?: Maybe<Subject>;
	update_Subject_items: Array<Subject>;
	update_Type_batch: Array<Type>;
	update_Type_item?: Maybe<Type>;
	update_Type_items: Array<Type>;
};

export type MutationCreate_Library_ItemArgs = {
	data: Create_Library_Input;
};

export type MutationCreate_Library_ItemsArgs = {
	data?: InputMaybe<Array<Create_Library_Input>>;
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Location_ItemArgs = {
	data: Create_Location_Input;
};

export type MutationCreate_Location_ItemsArgs = {
	data?: InputMaybe<Array<Create_Location_Input>>;
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Publication_Stock_ItemArgs = {
	data: Create_Publication_Stock_Input;
};

export type MutationCreate_Publication_Stock_ItemsArgs = {
	data?: InputMaybe<Array<Create_Publication_Stock_Input>>;
	filter?: InputMaybe<Publication_Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Publication_ItemArgs = {
	data: Create_Publication_Input;
};

export type MutationCreate_Publication_ItemsArgs = {
	data?: InputMaybe<Array<Create_Publication_Input>>;
	filter?: InputMaybe<Publication_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Stock_ItemArgs = {
	data: Create_Stock_Input;
};

export type MutationCreate_Stock_ItemsArgs = {
	data?: InputMaybe<Array<Create_Stock_Input>>;
	filter?: InputMaybe<Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Subject_ItemArgs = {
	data: Create_Subject_Input;
};

export type MutationCreate_Subject_ItemsArgs = {
	data?: InputMaybe<Array<Create_Subject_Input>>;
	filter?: InputMaybe<Subject_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Type_ItemArgs = {
	data: Create_Type_Input;
};

export type MutationCreate_Type_ItemsArgs = {
	data?: InputMaybe<Array<Create_Type_Input>>;
	filter?: InputMaybe<Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationDelete_Library_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Library_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Location_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Location_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Publication_Stock_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Publication_Stock_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Publication_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Publication_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Stock_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Stock_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Subject_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Subject_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Type_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Type_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationUpdate_Library_BatchArgs = {
	data?: InputMaybe<Array<Update_Library_Input>>;
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Library_ItemArgs = {
	data: Update_Library_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Library_ItemsArgs = {
	data: Update_Library_Input;
	filter?: InputMaybe<Library_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Location_BatchArgs = {
	data?: InputMaybe<Array<Update_Location_Input>>;
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Location_ItemArgs = {
	data: Update_Location_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Location_ItemsArgs = {
	data: Update_Location_Input;
	filter?: InputMaybe<Location_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Publication_Stock_BatchArgs = {
	data?: InputMaybe<Array<Update_Publication_Stock_Input>>;
	filter?: InputMaybe<Publication_Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Publication_Stock_ItemArgs = {
	data: Update_Publication_Stock_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Publication_Stock_ItemsArgs = {
	data: Update_Publication_Stock_Input;
	filter?: InputMaybe<Publication_Stock_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Publication_BatchArgs = {
	data?: InputMaybe<Array<Update_Publication_Input>>;
	filter?: InputMaybe<Publication_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Publication_ItemArgs = {
	data: Update_Publication_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Publication_ItemsArgs = {
	data: Update_Publication_Input;
	filter?: InputMaybe<Publication_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Stock_BatchArgs = {
	data?: InputMaybe<Array<Update_Stock_Input>>;
	filter?: InputMaybe<Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Stock_ItemArgs = {
	data: Update_Stock_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Stock_ItemsArgs = {
	data: Update_Stock_Input;
	filter?: InputMaybe<Stock_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Subject_BatchArgs = {
	data?: InputMaybe<Array<Update_Subject_Input>>;
	filter?: InputMaybe<Subject_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Subject_ItemArgs = {
	data: Update_Subject_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Subject_ItemsArgs = {
	data: Update_Subject_Input;
	filter?: InputMaybe<Subject_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Type_BatchArgs = {
	data?: InputMaybe<Array<Update_Type_Input>>;
	filter?: InputMaybe<Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Type_ItemArgs = {
	data: Update_Type_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Type_ItemsArgs = {
	data: Update_Type_Input;
	filter?: InputMaybe<Type_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Publication = {
	__typename?: 'Publication';
	/** Format: {author_1_last_name}, {author_1_first_name} / {author_2_last_name},  {author_2_first_name} */
	author?: Maybe<Scalars['String']['output']>;
	/** Softcover, Hardcover etc. */
	cover_details?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** ? Not sure where this goes in the UI */
	description?: Maybe<Scalars['String']['output']>;
	/** Format: {editor_1_last_name}, {editor_1_first_name} / {editor_2_last_name},  {editor_2_first_name} */
	editor?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	isbn?: Maybe<Scalars['String']['output']>;
	issn?: Maybe<Scalars['String']['output']>;
	/** Format: {language_1} / {language_2} */
	language?: Maybe<Scalars['String']['output']>;
	/** Temporary field only needed for migrations */
	legacy_filemaker_id?: Maybe<Scalars['String']['output']>;
	library?: Maybe<Library>;
	notes?: Maybe<Scalars['String']['output']>;
	/** The number of the article/newspaper/journal. This is a string since it could be a roman numeral. */
	number?: Maybe<Scalars['String']['output']>;
	pages?: Maybe<Scalars['String']['output']>;
	price?: Maybe<Scalars['String']['output']>;
	/** This should ideally be a Datetime but the data is a mess and just strings at the moment - this can be sorted out later */
	publication_date?: Maybe<Scalars['String']['output']>;
	published_location?: Maybe<Scalars['String']['output']>;
	publisher?: Maybe<Scalars['String']['output']>;
	/** The id / reference of the purchase order */
	purchase_order?: Maybe<Scalars['String']['output']>;
	resource_link?: Maybe<Scalars['String']['output']>;
	resource_title?: Maybe<Scalars['String']['output']>;
	/** Whether the image of the publication has been added to the Richter site */
	richter_image_is_on_site?: Maybe<Scalars['Boolean']['output']>;
	/** Where or not HENI researchers have indexed the book, for it to be published on the Richter site */
	richter_page_indexed?: Maybe<Scalars['Boolean']['output']>;
	/** True if this has an entry on the Richter website */
	richter_webpage_exists?: Maybe<Scalars['Boolean']['output']>;
	shelfmark?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	stock_information?: Maybe<Array<Maybe<Publication_Stock>>>;
	stock_information_func?: Maybe<Count_Functions>;
	/** The valid subject types can are available in the subjects table, but this field is stored as text */
	subject?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
	/** Must correspond to one of the types from the Type collection depending on the applicable library */
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
	/** This could be a number or a date, so it is a string. */
	volume?: Maybe<Scalars['String']['output']>;
};

export type PublicationImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PublicationLibraryArgs = {
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PublicationStock_InformationArgs = {
	filter?: InputMaybe<Publication_Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Publication_Stock = {
	__typename?: 'Publication_Stock';
	Publication_id?: Maybe<Publication>;
	Stock_id?: Maybe<Stock>;
	id: Scalars['ID']['output'];
};

export type Publication_StockPublication_IdArgs = {
	filter?: InputMaybe<Publication_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Publication_StockStock_IdArgs = {
	filter?: InputMaybe<Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Publication_Stock_Aggregated = {
	__typename?: 'Publication_Stock_aggregated';
	avg?: Maybe<Publication_Stock_Aggregated_Fields>;
	avgDistinct?: Maybe<Publication_Stock_Aggregated_Fields>;
	count?: Maybe<Publication_Stock_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Publication_Stock_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Publication_Stock_Aggregated_Fields>;
	min?: Maybe<Publication_Stock_Aggregated_Fields>;
	sum?: Maybe<Publication_Stock_Aggregated_Fields>;
	sumDistinct?: Maybe<Publication_Stock_Aggregated_Fields>;
};

export type Publication_Stock_Aggregated_Count = {
	__typename?: 'Publication_Stock_aggregated_count';
	Publication_id?: Maybe<Scalars['Int']['output']>;
	Stock_id?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Publication_Stock_Aggregated_Fields = {
	__typename?: 'Publication_Stock_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Publication_Stock_Filter = {
	Publication_id?: InputMaybe<Publication_Filter>;
	Stock_id?: InputMaybe<Stock_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Publication_Stock_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Publication_Stock_Filter>>>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Publication_Stock_Mutated = {
	__typename?: 'Publication_Stock_mutated';
	data?: Maybe<Publication_Stock>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Publication_Aggregated = {
	__typename?: 'Publication_aggregated';
	count?: Maybe<Publication_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Publication_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Publication_Aggregated_Count = {
	__typename?: 'Publication_aggregated_count';
	/** Format: {author_1_last_name}, {author_1_first_name} / {author_2_last_name},  {author_2_first_name} */
	author?: Maybe<Scalars['Int']['output']>;
	/** Softcover, Hardcover etc. */
	cover_details?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	/** ? Not sure where this goes in the UI */
	description?: Maybe<Scalars['Int']['output']>;
	/** Format: {editor_1_last_name}, {editor_1_first_name} / {editor_2_last_name},  {editor_2_first_name} */
	editor?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	image?: Maybe<Scalars['Int']['output']>;
	isbn?: Maybe<Scalars['Int']['output']>;
	issn?: Maybe<Scalars['Int']['output']>;
	/** Format: {language_1} / {language_2} */
	language?: Maybe<Scalars['Int']['output']>;
	/** Temporary field only needed for migrations */
	legacy_filemaker_id?: Maybe<Scalars['Int']['output']>;
	/** The source library the book belongs to (i.e. GR, or Joe) */
	library?: Maybe<Scalars['Int']['output']>;
	notes?: Maybe<Scalars['Int']['output']>;
	/** The number of the article/newspaper/journal. This is a string since it could be a roman numeral. */
	number?: Maybe<Scalars['Int']['output']>;
	pages?: Maybe<Scalars['Int']['output']>;
	price?: Maybe<Scalars['Int']['output']>;
	/** This should ideally be a Datetime but the data is a mess and just strings at the moment - this can be sorted out later */
	publication_date?: Maybe<Scalars['Int']['output']>;
	published_location?: Maybe<Scalars['Int']['output']>;
	publisher?: Maybe<Scalars['Int']['output']>;
	/** The id / reference of the purchase order */
	purchase_order?: Maybe<Scalars['Int']['output']>;
	resource_link?: Maybe<Scalars['Int']['output']>;
	resource_title?: Maybe<Scalars['Int']['output']>;
	/** Whether the image of the publication has been added to the Richter site */
	richter_image_is_on_site?: Maybe<Scalars['Int']['output']>;
	/** Where or not HENI researchers have indexed the book, for it to be published on the Richter site */
	richter_page_indexed?: Maybe<Scalars['Int']['output']>;
	/** True if this has an entry on the Richter website */
	richter_webpage_exists?: Maybe<Scalars['Int']['output']>;
	shelfmark?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	stock_information?: Maybe<Scalars['Int']['output']>;
	/** The valid subject types can are available in the subjects table, but this field is stored as text */
	subject?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	/** Must correspond to one of the types from the Type collection depending on the applicable library */
	type?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
	/** This could be a number or a date, so it is a string. */
	volume?: Maybe<Scalars['Int']['output']>;
};

export type Publication_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Publication_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Publication_Filter>>>;
	author?: InputMaybe<String_Filter_Operators>;
	cover_details?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	editor?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	isbn?: InputMaybe<String_Filter_Operators>;
	issn?: InputMaybe<String_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	legacy_filemaker_id?: InputMaybe<String_Filter_Operators>;
	library?: InputMaybe<Library_Filter>;
	notes?: InputMaybe<String_Filter_Operators>;
	number?: InputMaybe<String_Filter_Operators>;
	pages?: InputMaybe<String_Filter_Operators>;
	price?: InputMaybe<String_Filter_Operators>;
	publication_date?: InputMaybe<String_Filter_Operators>;
	published_location?: InputMaybe<String_Filter_Operators>;
	publisher?: InputMaybe<String_Filter_Operators>;
	purchase_order?: InputMaybe<String_Filter_Operators>;
	resource_link?: InputMaybe<String_Filter_Operators>;
	resource_title?: InputMaybe<String_Filter_Operators>;
	richter_image_is_on_site?: InputMaybe<Boolean_Filter_Operators>;
	richter_page_indexed?: InputMaybe<Boolean_Filter_Operators>;
	richter_webpage_exists?: InputMaybe<Boolean_Filter_Operators>;
	shelfmark?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	stock_information?: InputMaybe<Publication_Stock_Filter>;
	stock_information_func?: InputMaybe<Count_Function_Filter_Operators>;
	subject?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
	volume?: InputMaybe<String_Filter_Operators>;
};

export type Publication_Mutated = {
	__typename?: 'Publication_mutated';
	data?: Maybe<Publication>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Query = {
	__typename?: 'Query';
	Library: Array<Library>;
	Library_aggregated: Array<Library_Aggregated>;
	Library_by_id?: Maybe<Library>;
	Library_by_version?: Maybe<Version_Library>;
	Location: Array<Location>;
	Location_aggregated: Array<Location_Aggregated>;
	Location_by_id?: Maybe<Location>;
	Location_by_version?: Maybe<Version_Location>;
	Publication: Array<Publication>;
	Publication_Stock: Array<Publication_Stock>;
	Publication_Stock_aggregated: Array<Publication_Stock_Aggregated>;
	Publication_Stock_by_id?: Maybe<Publication_Stock>;
	Publication_Stock_by_version?: Maybe<Version_Publication_Stock>;
	Publication_aggregated: Array<Publication_Aggregated>;
	Publication_by_id?: Maybe<Publication>;
	Publication_by_version?: Maybe<Version_Publication>;
	Stock: Array<Stock>;
	Stock_aggregated: Array<Stock_Aggregated>;
	Stock_by_id?: Maybe<Stock>;
	Stock_by_version?: Maybe<Version_Stock>;
	Subject: Array<Subject>;
	Subject_aggregated: Array<Subject_Aggregated>;
	Subject_by_id?: Maybe<Subject>;
	Subject_by_version?: Maybe<Version_Subject>;
	Type: Array<Type>;
	Type_aggregated: Array<Type_Aggregated>;
	Type_by_id?: Maybe<Type>;
	Type_by_version?: Maybe<Version_Type>;
};

export type QueryLibraryArgs = {
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryLibrary_AggregatedArgs = {
	filter?: InputMaybe<Library_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryLibrary_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryLibrary_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QueryLocationArgs = {
	filter?: InputMaybe<Location_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryLocation_AggregatedArgs = {
	filter?: InputMaybe<Location_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryLocation_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryLocation_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QueryPublicationArgs = {
	filter?: InputMaybe<Publication_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPublication_StockArgs = {
	filter?: InputMaybe<Publication_Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPublication_Stock_AggregatedArgs = {
	filter?: InputMaybe<Publication_Stock_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPublication_Stock_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPublication_Stock_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QueryPublication_AggregatedArgs = {
	filter?: InputMaybe<Publication_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPublication_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPublication_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QueryStockArgs = {
	filter?: InputMaybe<Stock_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryStock_AggregatedArgs = {
	filter?: InputMaybe<Stock_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryStock_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryStock_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QuerySubjectArgs = {
	filter?: InputMaybe<Subject_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QuerySubject_AggregatedArgs = {
	filter?: InputMaybe<Subject_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QuerySubject_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QuerySubject_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type QueryTypeArgs = {
	filter?: InputMaybe<Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryType_AggregatedArgs = {
	filter?: InputMaybe<Type_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryType_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryType_By_VersionArgs = {
	id: Scalars['ID']['input'];
	version: Scalars['String']['input'];
};

export type Stock = {
	__typename?: 'Stock';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	/** maps to Unknown2 */
	notes?: Maybe<Scalars['String']['output']>;
	/** This would normally be a number but sometimes it seems to be a string like 3/3 */
	quantity?: Maybe<Scalars['String']['output']>;
	quantity_type?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Stock_Aggregated = {
	__typename?: 'Stock_aggregated';
	avg?: Maybe<Stock_Aggregated_Fields>;
	avgDistinct?: Maybe<Stock_Aggregated_Fields>;
	count?: Maybe<Stock_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Stock_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Stock_Aggregated_Fields>;
	min?: Maybe<Stock_Aggregated_Fields>;
	sum?: Maybe<Stock_Aggregated_Fields>;
	sumDistinct?: Maybe<Stock_Aggregated_Fields>;
};

export type Stock_Aggregated_Count = {
	__typename?: 'Stock_aggregated_count';
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	location?: Maybe<Scalars['Int']['output']>;
	/** maps to Unknown2 */
	notes?: Maybe<Scalars['Int']['output']>;
	/** This would normally be a number but sometimes it seems to be a string like 3/3 */
	quantity?: Maybe<Scalars['Int']['output']>;
	quantity_type?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Stock_Aggregated_Fields = {
	__typename?: 'Stock_aggregated_fields';
	sort?: Maybe<Scalars['Float']['output']>;
};

export type Stock_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Stock_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Stock_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	notes?: InputMaybe<String_Filter_Operators>;
	quantity?: InputMaybe<String_Filter_Operators>;
	quantity_type?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
};

export type Stock_Mutated = {
	__typename?: 'Stock_mutated';
	data?: Maybe<Stock>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Subject = {
	__typename?: 'Subject';
	/** This field may be used in some cases, such as for Joe's library, to create the shelfmark. The logic for this is hardcoded in the FE. */
	abbreviation?: Maybe<Scalars['String']['output']>;
	applicable_library?: Maybe<Library>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type SubjectApplicable_LibraryArgs = {
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Subject_Aggregated = {
	__typename?: 'Subject_aggregated';
	avg?: Maybe<Subject_Aggregated_Fields>;
	avgDistinct?: Maybe<Subject_Aggregated_Fields>;
	count?: Maybe<Subject_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Subject_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Subject_Aggregated_Fields>;
	min?: Maybe<Subject_Aggregated_Fields>;
	sum?: Maybe<Subject_Aggregated_Fields>;
	sumDistinct?: Maybe<Subject_Aggregated_Fields>;
};

export type Subject_Aggregated_Count = {
	__typename?: 'Subject_aggregated_count';
	/** This field may be used in some cases, such as for Joe's library, to create the shelfmark. The logic for this is hardcoded in the FE. */
	abbreviation?: Maybe<Scalars['Int']['output']>;
	/** Each subject should be tied to a specific library (i.e. GR or JH). If the same subject appears in multiple libraries, create separated entries linked to different libraries. */
	applicable_library?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Subject_Aggregated_Fields = {
	__typename?: 'Subject_aggregated_fields';
	sort?: Maybe<Scalars['Float']['output']>;
};

export type Subject_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Subject_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Subject_Filter>>>;
	abbreviation?: InputMaybe<String_Filter_Operators>;
	applicable_library?: InputMaybe<Library_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
};

export type Subject_Mutated = {
	__typename?: 'Subject_mutated';
	data?: Maybe<Subject>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Subscription = {
	__typename?: 'Subscription';
	Library_mutated?: Maybe<Library_Mutated>;
	Location_mutated?: Maybe<Location_Mutated>;
	Publication_Stock_mutated?: Maybe<Publication_Stock_Mutated>;
	Publication_mutated?: Maybe<Publication_Mutated>;
	Stock_mutated?: Maybe<Stock_Mutated>;
	Subject_mutated?: Maybe<Subject_Mutated>;
	Type_mutated?: Maybe<Type_Mutated>;
	directus_files_mutated?: Maybe<Directus_Files_Mutated>;
};

export type SubscriptionLibrary_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionLocation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPublication_Stock_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPublication_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionStock_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionSubject_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionType_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type Type = {
	__typename?: 'Type';
	applicable_library?: Maybe<Library>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	show_in_dropdown?: Maybe<Scalars['Boolean']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type TypeApplicable_LibraryArgs = {
	filter?: InputMaybe<Library_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Type_Aggregated = {
	__typename?: 'Type_aggregated';
	avg?: Maybe<Type_Aggregated_Fields>;
	avgDistinct?: Maybe<Type_Aggregated_Fields>;
	count?: Maybe<Type_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Type_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Type_Aggregated_Fields>;
	min?: Maybe<Type_Aggregated_Fields>;
	sum?: Maybe<Type_Aggregated_Fields>;
	sumDistinct?: Maybe<Type_Aggregated_Fields>;
};

export type Type_Aggregated_Count = {
	__typename?: 'Type_aggregated_count';
	/** Each type should be tied to a specific library (i.e. GR or JH). If the same subject appears in multiple libraries, create separated entries linked to different libraries. */
	applicable_library?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	show_in_dropdown?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Type_Aggregated_Fields = {
	__typename?: 'Type_aggregated_fields';
	sort?: Maybe<Scalars['Float']['output']>;
};

export type Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Type_Filter>>>;
	applicable_library?: InputMaybe<Library_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	show_in_dropdown?: InputMaybe<Boolean_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<String_Filter_Operators>;
	user_updated?: InputMaybe<String_Filter_Operators>;
};

export type Type_Mutated = {
	__typename?: 'Type_mutated';
	data?: Maybe<Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Big_Int_Filter_Operators = {
	_between?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_eq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nbetween?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_neq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Boolean_Filter_Operators = {
	_eq?: InputMaybe<Scalars['Boolean']['input']>;
	_neq?: InputMaybe<Scalars['Boolean']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Count_Function_Filter_Operators = {
	count?: InputMaybe<Number_Filter_Operators>;
};

export type Count_Functions = {
	__typename?: 'count_functions';
	count?: Maybe<Scalars['Int']['output']>;
};

export type Create_Library_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	slug?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Location_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title: Scalars['String']['input'];
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Publication_Stock_Input = {
	Publication_id?: InputMaybe<Create_Publication_Input>;
	Stock_id?: InputMaybe<Create_Stock_Input>;
	id?: InputMaybe<Scalars['ID']['input']>;
};

export type Create_Publication_Input = {
	/** Format: {author_1_last_name}, {author_1_first_name} / {author_2_last_name},  {author_2_first_name} */
	author?: InputMaybe<Scalars['String']['input']>;
	/** Softcover, Hardcover etc. */
	cover_details?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	/** ? Not sure where this goes in the UI */
	description?: InputMaybe<Scalars['String']['input']>;
	/** Format: {editor_1_last_name}, {editor_1_first_name} / {editor_2_last_name},  {editor_2_first_name} */
	editor?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	image?: InputMaybe<Scalars['String']['input']>;
	isbn?: InputMaybe<Scalars['String']['input']>;
	issn?: InputMaybe<Scalars['String']['input']>;
	/** Format: {language_1} / {language_2} */
	language?: InputMaybe<Scalars['String']['input']>;
	/** Temporary field only needed for migrations */
	legacy_filemaker_id?: InputMaybe<Scalars['String']['input']>;
	library?: InputMaybe<Create_Library_Input>;
	notes?: InputMaybe<Scalars['String']['input']>;
	/** The number of the article/newspaper/journal. This is a string since it could be a roman numeral. */
	number?: InputMaybe<Scalars['String']['input']>;
	pages?: InputMaybe<Scalars['String']['input']>;
	price?: InputMaybe<Scalars['String']['input']>;
	/** This should ideally be a Datetime but the data is a mess and just strings at the moment - this can be sorted out later */
	publication_date?: InputMaybe<Scalars['String']['input']>;
	published_location?: InputMaybe<Scalars['String']['input']>;
	publisher?: InputMaybe<Scalars['String']['input']>;
	/** The id / reference of the purchase order */
	purchase_order?: InputMaybe<Scalars['String']['input']>;
	resource_link?: InputMaybe<Scalars['String']['input']>;
	resource_title?: InputMaybe<Scalars['String']['input']>;
	/** Whether the image of the publication has been added to the Richter site */
	richter_image_is_on_site?: InputMaybe<Scalars['Boolean']['input']>;
	/** Where or not HENI researchers have indexed the book, for it to be published on the Richter site */
	richter_page_indexed?: InputMaybe<Scalars['Boolean']['input']>;
	/** True if this has an entry on the Richter website */
	richter_webpage_exists?: InputMaybe<Scalars['Boolean']['input']>;
	shelfmark?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	stock_information?: InputMaybe<
		Array<InputMaybe<Create_Publication_Stock_Input>>
	>;
	/** The valid subject types can are available in the subjects table, but this field is stored as text */
	subject?: InputMaybe<Scalars['String']['input']>;
	title: Scalars['String']['input'];
	/** Must correspond to one of the types from the Type collection depending on the applicable library */
	type?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
	/** This could be a number or a date, so it is a string. */
	volume?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Stock_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	/** maps to Unknown2 */
	notes?: InputMaybe<Scalars['String']['input']>;
	/** This would normally be a number but sometimes it seems to be a string like 3/3 */
	quantity?: InputMaybe<Scalars['String']['input']>;
	quantity_type?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Subject_Input = {
	/** This field may be used in some cases, such as for Joe's library, to create the shelfmark. The logic for this is hardcoded in the FE. */
	abbreviation?: InputMaybe<Scalars['String']['input']>;
	applicable_library?: InputMaybe<Create_Library_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title: Scalars['String']['input'];
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Create_Type_Input = {
	applicable_library?: InputMaybe<Create_Library_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	show_in_dropdown?: InputMaybe<Scalars['Boolean']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title: Scalars['String']['input'];
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Date_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_gt?: InputMaybe<Scalars['String']['input']>;
	_gte?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_lt?: InputMaybe<Scalars['String']['input']>;
	_lte?: InputMaybe<Scalars['String']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Datetime_Function_Filter_Operators = {
	day?: InputMaybe<Number_Filter_Operators>;
	hour?: InputMaybe<Number_Filter_Operators>;
	minute?: InputMaybe<Number_Filter_Operators>;
	month?: InputMaybe<Number_Filter_Operators>;
	second?: InputMaybe<Number_Filter_Operators>;
	week?: InputMaybe<Number_Filter_Operators>;
	weekday?: InputMaybe<Number_Filter_Operators>;
	year?: InputMaybe<Number_Filter_Operators>;
};

export type Datetime_Functions = {
	__typename?: 'datetime_functions';
	day?: Maybe<Scalars['Int']['output']>;
	hour?: Maybe<Scalars['Int']['output']>;
	minute?: Maybe<Scalars['Int']['output']>;
	month?: Maybe<Scalars['Int']['output']>;
	second?: Maybe<Scalars['Int']['output']>;
	week?: Maybe<Scalars['Int']['output']>;
	weekday?: Maybe<Scalars['Int']['output']>;
	year?: Maybe<Scalars['Int']['output']>;
};

export type Delete_Many = {
	__typename?: 'delete_many';
	ids: Array<Maybe<Scalars['ID']['output']>>;
};

export type Delete_One = {
	__typename?: 'delete_one';
	id: Scalars['ID']['output'];
};

export type Directus_Files = {
	__typename?: 'directus_files';
	charset?: Maybe<Scalars['String']['output']>;
	created_on?: Maybe<Scalars['Date']['output']>;
	created_on_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	duration?: Maybe<Scalars['Int']['output']>;
	embed?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	filename_download: Scalars['String']['output'];
	filesize?: Maybe<Scalars['GraphQLBigInt']['output']>;
	focal_point_x?: Maybe<Scalars['Int']['output']>;
	focal_point_y?: Maybe<Scalars['Int']['output']>;
	folder?: Maybe<Scalars['String']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	metadata?: Maybe<Scalars['JSON']['output']>;
	metadata_func?: Maybe<Count_Functions>;
	modified_by?: Maybe<Scalars['String']['output']>;
	modified_on?: Maybe<Scalars['Date']['output']>;
	modified_on_func?: Maybe<Datetime_Functions>;
	storage: Scalars['String']['output'];
	tags?: Maybe<Scalars['JSON']['output']>;
	tags_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	tus_data?: Maybe<Scalars['JSON']['output']>;
	tus_data_func?: Maybe<Count_Functions>;
	tus_id?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	uploaded_by?: Maybe<Scalars['String']['output']>;
	uploaded_on?: Maybe<Scalars['Date']['output']>;
	uploaded_on_func?: Maybe<Datetime_Functions>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Files_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	charset?: InputMaybe<String_Filter_Operators>;
	created_on?: InputMaybe<Date_Filter_Operators>;
	created_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	duration?: InputMaybe<Number_Filter_Operators>;
	embed?: InputMaybe<String_Filter_Operators>;
	filename_disk?: InputMaybe<String_Filter_Operators>;
	filename_download?: InputMaybe<String_Filter_Operators>;
	filesize?: InputMaybe<Big_Int_Filter_Operators>;
	focal_point_x?: InputMaybe<Number_Filter_Operators>;
	focal_point_y?: InputMaybe<Number_Filter_Operators>;
	folder?: InputMaybe<String_Filter_Operators>;
	height?: InputMaybe<Number_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	metadata?: InputMaybe<String_Filter_Operators>;
	metadata_func?: InputMaybe<Count_Function_Filter_Operators>;
	modified_by?: InputMaybe<String_Filter_Operators>;
	modified_on?: InputMaybe<Date_Filter_Operators>;
	modified_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	storage?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	tus_data?: InputMaybe<String_Filter_Operators>;
	tus_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	tus_id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	uploaded_by?: InputMaybe<String_Filter_Operators>;
	uploaded_on?: InputMaybe<Date_Filter_Operators>;
	uploaded_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Files_Mutated = {
	__typename?: 'directus_files_mutated';
	data?: Maybe<Directus_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Number_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nin?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type String_Filter_Operators = {
	_contains?: InputMaybe<Scalars['String']['input']>;
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_ends_with?: InputMaybe<Scalars['String']['input']>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_icontains?: InputMaybe<Scalars['String']['input']>;
	_iends_with?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_istarts_with?: InputMaybe<Scalars['String']['input']>;
	_ncontains?: InputMaybe<Scalars['String']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nends_with?: InputMaybe<Scalars['String']['input']>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_niends_with?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nistarts_with?: InputMaybe<Scalars['String']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_nstarts_with?: InputMaybe<Scalars['String']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
	_starts_with?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Library_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	slug?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Location_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Publication_Stock_Input = {
	Publication_id?: InputMaybe<Update_Publication_Input>;
	Stock_id?: InputMaybe<Update_Stock_Input>;
	id?: InputMaybe<Scalars['ID']['input']>;
};

export type Update_Publication_Input = {
	/** Format: {author_1_last_name}, {author_1_first_name} / {author_2_last_name},  {author_2_first_name} */
	author?: InputMaybe<Scalars['String']['input']>;
	/** Softcover, Hardcover etc. */
	cover_details?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	/** ? Not sure where this goes in the UI */
	description?: InputMaybe<Scalars['String']['input']>;
	/** Format: {editor_1_last_name}, {editor_1_first_name} / {editor_2_last_name},  {editor_2_first_name} */
	editor?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	image?: InputMaybe<Scalars['String']['input']>;
	isbn?: InputMaybe<Scalars['String']['input']>;
	issn?: InputMaybe<Scalars['String']['input']>;
	/** Format: {language_1} / {language_2} */
	language?: InputMaybe<Scalars['String']['input']>;
	/** Temporary field only needed for migrations */
	legacy_filemaker_id?: InputMaybe<Scalars['String']['input']>;
	library?: InputMaybe<Update_Library_Input>;
	notes?: InputMaybe<Scalars['String']['input']>;
	/** The number of the article/newspaper/journal. This is a string since it could be a roman numeral. */
	number?: InputMaybe<Scalars['String']['input']>;
	pages?: InputMaybe<Scalars['String']['input']>;
	price?: InputMaybe<Scalars['String']['input']>;
	/** This should ideally be a Datetime but the data is a mess and just strings at the moment - this can be sorted out later */
	publication_date?: InputMaybe<Scalars['String']['input']>;
	published_location?: InputMaybe<Scalars['String']['input']>;
	publisher?: InputMaybe<Scalars['String']['input']>;
	/** The id / reference of the purchase order */
	purchase_order?: InputMaybe<Scalars['String']['input']>;
	resource_link?: InputMaybe<Scalars['String']['input']>;
	resource_title?: InputMaybe<Scalars['String']['input']>;
	/** Whether the image of the publication has been added to the Richter site */
	richter_image_is_on_site?: InputMaybe<Scalars['Boolean']['input']>;
	/** Where or not HENI researchers have indexed the book, for it to be published on the Richter site */
	richter_page_indexed?: InputMaybe<Scalars['Boolean']['input']>;
	/** True if this has an entry on the Richter website */
	richter_webpage_exists?: InputMaybe<Scalars['Boolean']['input']>;
	shelfmark?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	stock_information?: InputMaybe<
		Array<InputMaybe<Update_Publication_Stock_Input>>
	>;
	/** The valid subject types can are available in the subjects table, but this field is stored as text */
	subject?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	/** Must correspond to one of the types from the Type collection depending on the applicable library */
	type?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
	/** This could be a number or a date, so it is a string. */
	volume?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Stock_Input = {
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	/** maps to Unknown2 */
	notes?: InputMaybe<Scalars['String']['input']>;
	/** This would normally be a number but sometimes it seems to be a string like 3/3 */
	quantity?: InputMaybe<Scalars['String']['input']>;
	quantity_type?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Subject_Input = {
	/** This field may be used in some cases, such as for Joe's library, to create the shelfmark. The logic for this is hardcoded in the FE. */
	abbreviation?: InputMaybe<Scalars['String']['input']>;
	applicable_library?: InputMaybe<Update_Library_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Type_Input = {
	applicable_library?: InputMaybe<Update_Library_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	show_in_dropdown?: InputMaybe<Scalars['Boolean']['input']>;
	sort?: InputMaybe<Scalars['Int']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Scalars['String']['input']>;
	user_updated?: InputMaybe<Scalars['String']['input']>;
};

export type Version_Library = {
	__typename?: 'version_Library';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	slug?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Version_Location = {
	__typename?: 'version_Location';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Version_Publication = {
	__typename?: 'version_Publication';
	/** Format: {author_1_last_name}, {author_1_first_name} / {author_2_last_name},  {author_2_first_name} */
	author?: Maybe<Scalars['String']['output']>;
	/** Softcover, Hardcover etc. */
	cover_details?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	/** ? Not sure where this goes in the UI */
	description?: Maybe<Scalars['String']['output']>;
	/** Format: {editor_1_last_name}, {editor_1_first_name} / {editor_2_last_name},  {editor_2_first_name} */
	editor?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	image?: Maybe<Scalars['String']['output']>;
	isbn?: Maybe<Scalars['String']['output']>;
	issn?: Maybe<Scalars['String']['output']>;
	/** Format: {language_1} / {language_2} */
	language?: Maybe<Scalars['String']['output']>;
	/** Temporary field only needed for migrations */
	legacy_filemaker_id?: Maybe<Scalars['String']['output']>;
	library?: Maybe<Scalars['JSON']['output']>;
	notes?: Maybe<Scalars['String']['output']>;
	/** The number of the article/newspaper/journal. This is a string since it could be a roman numeral. */
	number?: Maybe<Scalars['String']['output']>;
	pages?: Maybe<Scalars['String']['output']>;
	price?: Maybe<Scalars['String']['output']>;
	/** This should ideally be a Datetime but the data is a mess and just strings at the moment - this can be sorted out later */
	publication_date?: Maybe<Scalars['String']['output']>;
	published_location?: Maybe<Scalars['String']['output']>;
	publisher?: Maybe<Scalars['String']['output']>;
	/** The id / reference of the purchase order */
	purchase_order?: Maybe<Scalars['String']['output']>;
	resource_link?: Maybe<Scalars['String']['output']>;
	resource_title?: Maybe<Scalars['String']['output']>;
	/** Whether the image of the publication has been added to the Richter site */
	richter_image_is_on_site?: Maybe<Scalars['Boolean']['output']>;
	/** Where or not HENI researchers have indexed the book, for it to be published on the Richter site */
	richter_page_indexed?: Maybe<Scalars['Boolean']['output']>;
	/** True if this has an entry on the Richter website */
	richter_webpage_exists?: Maybe<Scalars['Boolean']['output']>;
	shelfmark?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	stock_information?: Maybe<Scalars['JSON']['output']>;
	/** The valid subject types can are available in the subjects table, but this field is stored as text */
	subject?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	/** Must correspond to one of the types from the Type collection depending on the applicable library */
	type?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
	/** This could be a number or a date, so it is a string. */
	volume?: Maybe<Scalars['String']['output']>;
};

export type Version_Publication_Stock = {
	__typename?: 'version_Publication_Stock';
	Publication_id?: Maybe<Scalars['JSON']['output']>;
	Stock_id?: Maybe<Scalars['JSON']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
};

export type Version_Stock = {
	__typename?: 'version_Stock';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	/** maps to Unknown2 */
	notes?: Maybe<Scalars['String']['output']>;
	/** This would normally be a number but sometimes it seems to be a string like 3/3 */
	quantity?: Maybe<Scalars['String']['output']>;
	quantity_type?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Version_Subject = {
	__typename?: 'version_Subject';
	/** This field may be used in some cases, such as for Joe's library, to create the shelfmark. The logic for this is hardcoded in the FE. */
	abbreviation?: Maybe<Scalars['String']['output']>;
	applicable_library?: Maybe<Scalars['JSON']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};

export type Version_Type = {
	__typename?: 'version_Type';
	applicable_library?: Maybe<Scalars['JSON']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	show_in_dropdown?: Maybe<Scalars['Boolean']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Scalars['String']['output']>;
};
