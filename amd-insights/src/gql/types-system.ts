export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	GraphQLBigInt: { input: any; output: any };
	GraphQLStringOrFloat: { input: any; output: any };
	Hash: { input: any; output: any };
	JSON: { input: any; output: any };
	Void: { input: any; output: any };
};

export type Artist_Details = {
	__typename?: 'Artist_Details';
	artwork_details?: Maybe<Artwork_Details>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Country>;
	original_extraction_response?: Maybe<Scalars['JSON']['output']>;
	original_extraction_response_func?: Maybe<Count_Functions>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	raw_nationality?: Maybe<Scalars['String']['output']>;
	reference_artist_id?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type Artist_DetailsArtwork_DetailsArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_DetailsNationalityArgs = {
	filter?: InputMaybe<Country_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_DetailsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_DetailsUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artist_Details_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Details_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Details_Filter>>>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	nationality?: InputMaybe<Country_Filter>;
	original_extraction_response?: InputMaybe<String_Filter_Operators>;
	original_extraction_response_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_artist_id?: InputMaybe<String_Filter_Operators>;
	raw_nationality?: InputMaybe<String_Filter_Operators>;
	reference_artist_id?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_birth?: InputMaybe<Number_Filter_Operators>;
	year_death?: InputMaybe<Number_Filter_Operators>;
};

export type Artist_Details_Mutated = {
	__typename?: 'Artist_Details_mutated';
	data?: Maybe<Artist_Details>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artist_Details_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artist_Details_Filter>>>;
	_none?: InputMaybe<Artist_Details_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artist_Details_Filter>>>;
	_some?: InputMaybe<Artist_Details_Filter>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	nationality?: InputMaybe<Country_Filter>;
	original_extraction_response?: InputMaybe<String_Filter_Operators>;
	original_extraction_response_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_artist_id?: InputMaybe<String_Filter_Operators>;
	raw_nationality?: InputMaybe<String_Filter_Operators>;
	reference_artist_id?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_birth?: InputMaybe<Number_Filter_Operators>;
	year_death?: InputMaybe<Number_Filter_Operators>;
};

export type Artlogic_Link = {
	__typename?: 'Artlogic_Link';
	art_event_feed?: Maybe<Art_Event_Feed>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	processed_artist?: Maybe<Processed_Artist>;
	processed_gallery?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	review_status?: Maybe<Checklist_Review_Status>;
	scraped_at?: Maybe<Scalars['Date']['output']>;
	scraped_at_func?: Maybe<Datetime_Functions>;
	status?: Maybe<Artlogic_Link_Status>;
	title: Scalars['String']['output'];
	url: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artlogic_LinkArt_Event_FeedArgs = {
	filter?: InputMaybe<Art_Event_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkProcessed_ArtistArgs = {
	filter?: InputMaybe<Processed_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkProcessed_GalleryArgs = {
	filter?: InputMaybe<Processed_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkReceipt_InfoArgs = {
	filter?: InputMaybe<Receipt_Information_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkReview_StatusArgs = {
	filter?: InputMaybe<Checklist_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkStatusArgs = {
	filter?: InputMaybe<Artlogic_Link_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_LinkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artlogic_Link_Status = {
	__typename?: 'Artlogic_Link_Status';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Artlogic_Link_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artlogic_Link_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artlogic_Link_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artlogic_Link_Status_Mutated = {
	__typename?: 'Artlogic_Link_Status_mutated';
	data?: Maybe<Artlogic_Link_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artlogic_Link_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artlogic_Link_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artlogic_Link_Filter>>>;
	art_event_feed?: InputMaybe<Art_Event_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	high_priority?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	includes_prices?: InputMaybe<Boolean_Filter_Operators>;
	processed_artist?: InputMaybe<Processed_Artist_Filter>;
	processed_gallery?: InputMaybe<Processed_Organisation_Filter>;
	receipt_info?: InputMaybe<Receipt_Information_Filter>;
	review_status?: InputMaybe<Checklist_Review_Status_Filter>;
	scraped_at?: InputMaybe<Date_Filter_Operators>;
	scraped_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	status?: InputMaybe<Artlogic_Link_Status_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	url?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artlogic_Link_Mutated = {
	__typename?: 'Artlogic_Link_mutated';
	data?: Maybe<Artlogic_Link>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artlogic_Link_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artlogic_Link_Filter>>>;
	_none?: InputMaybe<Artlogic_Link_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artlogic_Link_Filter>>>;
	_some?: InputMaybe<Artlogic_Link_Filter>;
	art_event_feed?: InputMaybe<Art_Event_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	high_priority?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	includes_prices?: InputMaybe<Boolean_Filter_Operators>;
	processed_artist?: InputMaybe<Processed_Artist_Filter>;
	processed_gallery?: InputMaybe<Processed_Organisation_Filter>;
	receipt_info?: InputMaybe<Receipt_Information_Filter>;
	review_status?: InputMaybe<Checklist_Review_Status_Filter>;
	scraped_at?: InputMaybe<Date_Filter_Operators>;
	scraped_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	status?: InputMaybe<Artlogic_Link_Status_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	url?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Details = {
	__typename?: 'Artwork_Details';
	artist_proof_size?: Maybe<Scalars['Int']['output']>;
	artist_text?: Maybe<Scalars['String']['output']>;
	artists?: Maybe<Array<Maybe<Artist_Details>>>;
	artists_func?: Maybe<Count_Functions>;
	artwork_removed?: Maybe<Scalars['Boolean']['output']>;
	artwork_type?: Maybe<Artwork_Type>;
	condition?: Maybe<Scalars['String']['output']>;
	crid?: Maybe<Scalars['String']['output']>;
	crid_text?: Maybe<Scalars['String']['output']>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** This is the artwork description, which usually contains the other fields */
	description?: Maybe<Scalars['String']['output']>;
	dimension_type?: Maybe<Artwork_Dimension_Type>;
	dimensions?: Maybe<Scalars['String']['output']>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	/** Rename to edition size and number, or something. */
	edition_description?: Maybe<Scalars['String']['output']>;
	edition_is_numbered?: Maybe<Scalars['Boolean']['output']>;
	edition_is_unknown?: Maybe<Scalars['Boolean']['output']>;
	edition_number?: Maybe<Scalars['String']['output']>;
	edition_number_type?: Maybe<Edition_Number_Type>;
	estimate_high?: Maybe<Scalars['Float']['output']>;
	estimate_low?: Maybe<Scalars['Float']['output']>;
	estimate_raw?: Maybe<Scalars['String']['output']>;
	executed_year_end?: Maybe<Scalars['Int']['output']>;
	executed_year_start?: Maybe<Scalars['Int']['output']>;
	exhibition?: Maybe<Scalars['String']['output']>;
	general_proof_size?: Maybe<Scalars['Int']['output']>;
	house_of_commerce_size?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	lead?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	lot_attributes?: Maybe<
		Array<Maybe<Artwork_Details_Artwork_Lot_Symbol_Lookup>>
	>;
	lot_attributes_func?: Maybe<Count_Functions>;
	lot_number?: Maybe<Scalars['String']['output']>;
	media?: Maybe<Scalars['String']['output']>;
	/** When the artwork represents a bundle or full set of artworks, this is the number of artworks within the bundle or set. */
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	/** This identifies how many pieces the artwork consists of. Typically this will be 1 and does not apply to sets. Number of pieces should only apply to artwork pieces that are always sold together. If the number of pieces is greater than 1 then this artwork consists of multiple pieces. */
	number_of_pieces?: Maybe<Scalars['Int']['output']>;
	open_edition?: Maybe<Scalars['Boolean']['output']>;
	original_extraction_response?: Maybe<Scalars['JSON']['output']>;
	original_extraction_response_func?: Maybe<Count_Functions>;
	price?: Maybe<Scalars['Float']['output']>;
	price_includes_premium?: Maybe<Scalars['Boolean']['output']>;
	price_raw?: Maybe<Scalars['String']['output']>;
	processed_artwork_id?: Maybe<Scalars['String']['output']>;
	provenance?: Maybe<Scalars['String']['output']>;
	regular_edition_size?: Maybe<Scalars['Int']['output']>;
	sale_date?: Maybe<Scalars['Date']['output']>;
	sale_date_func?: Maybe<Datetime_Functions>;
	sale_date_tz?: Maybe<Scalars['String']['output']>;
	/** This relates to the artwork_activity_status_type, as defined in the processed DB. */
	sale_status?: Maybe<Scalars['String']['output']>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	/** i.e. number of editions in a series (virtues is 8, empresses is 5) */
	series_size?: Maybe<Scalars['Int']['output']>;
	shipping?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	/** includes both artist proofs, other types of prints + regular edition */
	total_edition_size?: Maybe<Scalars['Int']['output']>;
	url?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_DetailsArtistsArgs = {
	filter?: InputMaybe<Artist_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsArtwork_TypeArgs = {
	filter?: InputMaybe<Artwork_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsCurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsDimension_TypeArgs = {
	filter?: InputMaybe<Artwork_Dimension_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsEdition_Number_TypeArgs = {
	filter?: InputMaybe<Edition_Number_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsLot_AttributesArgs = {
	filter?: InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_DetailsUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Details_Artwork_Lot_Symbol_Lookup = {
	__typename?: 'Artwork_Details_artwork_lot_symbol_lookup';
	Artwork_Details_id?: Maybe<Artwork_Details>;
	artwork_lot_symbol_lookup_key?: Maybe<Artwork_Lot_Symbol_Lookup>;
	id: Scalars['ID']['output'];
};

export type Artwork_Details_Artwork_Lot_Symbol_LookupArtwork_Details_IdArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Details_Artwork_Lot_Symbol_LookupArtwork_Lot_Symbol_Lookup_KeyArgs =
	{
		filter?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter = {
	Artwork_Details_id?: InputMaybe<Artwork_Details_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>>
	>;
	_or?: InputMaybe<
		Array<InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>>
	>;
	artwork_lot_symbol_lookup_key?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Details_Artwork_Lot_Symbol_Lookup_Mutated = {
	__typename?: 'Artwork_Details_artwork_lot_symbol_lookup_mutated';
	data?: Maybe<Artwork_Details_Artwork_Lot_Symbol_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Details_Artwork_Lot_Symbol_Lookup_Quantifier_Filter = {
	Artwork_Details_id?: InputMaybe<Artwork_Details_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>>
	>;
	_none?: InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>;
	_or?: InputMaybe<
		Array<InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>>
	>;
	_some?: InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Filter>;
	artwork_lot_symbol_lookup_key?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Details_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Details_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Details_Filter>>>;
	artist_proof_size?: InputMaybe<Number_Filter_Operators>;
	artist_text?: InputMaybe<String_Filter_Operators>;
	artists?: InputMaybe<Artist_Details_Quantifier_Filter>;
	artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	artwork_removed?: InputMaybe<Boolean_Filter_Operators>;
	artwork_type?: InputMaybe<Artwork_Type_Filter>;
	condition?: InputMaybe<String_Filter_Operators>;
	crid?: InputMaybe<String_Filter_Operators>;
	crid_text?: InputMaybe<String_Filter_Operators>;
	currency?: InputMaybe<Currency_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	dimension_type?: InputMaybe<Artwork_Dimension_Type_Filter>;
	dimensions?: InputMaybe<String_Filter_Operators>;
	dimensions_depth_cm?: InputMaybe<Number_Filter_Operators>;
	dimensions_height_cm?: InputMaybe<Number_Filter_Operators>;
	dimensions_width_cm?: InputMaybe<Number_Filter_Operators>;
	edition_description?: InputMaybe<String_Filter_Operators>;
	edition_is_numbered?: InputMaybe<Boolean_Filter_Operators>;
	edition_is_unknown?: InputMaybe<Boolean_Filter_Operators>;
	edition_number?: InputMaybe<String_Filter_Operators>;
	edition_number_type?: InputMaybe<Edition_Number_Type_Filter>;
	estimate_high?: InputMaybe<Number_Filter_Operators>;
	estimate_low?: InputMaybe<Number_Filter_Operators>;
	estimate_raw?: InputMaybe<String_Filter_Operators>;
	executed_year_end?: InputMaybe<Number_Filter_Operators>;
	executed_year_start?: InputMaybe<Number_Filter_Operators>;
	exhibition?: InputMaybe<String_Filter_Operators>;
	general_proof_size?: InputMaybe<Number_Filter_Operators>;
	house_of_commerce_size?: InputMaybe<Number_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_notes?: InputMaybe<String_Filter_Operators>;
	is_bundle?: InputMaybe<Boolean_Filter_Operators>;
	is_full_set?: InputMaybe<Boolean_Filter_Operators>;
	lead?: InputMaybe<String_Filter_Operators>;
	literature?: InputMaybe<String_Filter_Operators>;
	lot_attributes?: InputMaybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Quantifier_Filter>;
	lot_attributes_func?: InputMaybe<Count_Function_Filter_Operators>;
	lot_number?: InputMaybe<String_Filter_Operators>;
	media?: InputMaybe<String_Filter_Operators>;
	number_of_artworks?: InputMaybe<Number_Filter_Operators>;
	number_of_pieces?: InputMaybe<Number_Filter_Operators>;
	open_edition?: InputMaybe<Boolean_Filter_Operators>;
	original_extraction_response?: InputMaybe<String_Filter_Operators>;
	original_extraction_response_func?: InputMaybe<Count_Function_Filter_Operators>;
	price?: InputMaybe<Number_Filter_Operators>;
	price_includes_premium?: InputMaybe<Boolean_Filter_Operators>;
	price_raw?: InputMaybe<String_Filter_Operators>;
	processed_artwork_id?: InputMaybe<String_Filter_Operators>;
	provenance?: InputMaybe<String_Filter_Operators>;
	regular_edition_size?: InputMaybe<Number_Filter_Operators>;
	sale_date?: InputMaybe<Date_Filter_Operators>;
	sale_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	sale_date_tz?: InputMaybe<String_Filter_Operators>;
	sale_status?: InputMaybe<String_Filter_Operators>;
	saleroom_notice?: InputMaybe<String_Filter_Operators>;
	series_size?: InputMaybe<Number_Filter_Operators>;
	shipping?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	total_edition_size?: InputMaybe<Number_Filter_Operators>;
	url?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Artwork_Details_Mutated = {
	__typename?: 'Artwork_Details_mutated';
	data?: Maybe<Artwork_Details>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Dimension_Type = {
	__typename?: 'Artwork_Dimension_Type';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Dimension_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Dimension_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Dimension_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Dimension_Type_Mutated = {
	__typename?: 'Artwork_Dimension_Type_mutated';
	data?: Maybe<Artwork_Dimension_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Status_Type = {
	__typename?: 'Artwork_Status_Type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Status_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Status_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Status_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Status_Type_Mutated = {
	__typename?: 'Artwork_Status_Type_mutated';
	data?: Maybe<Artwork_Status_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Type = {
	__typename?: 'Artwork_Type';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Artwork_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Type_Mutated = {
	__typename?: 'Artwork_Type_mutated';
	data?: Maybe<Artwork_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Checklist_Review_Status = {
	__typename?: 'Checklist_Review_Status';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Checklist_Review_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Checklist_Review_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Checklist_Review_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Checklist_Review_Status_Mutated = {
	__typename?: 'Checklist_Review_Status_mutated';
	data?: Maybe<Checklist_Review_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Edition_Number_Type = {
	__typename?: 'Edition_Number_Type';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Edition_Number_Type_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Edition_Number_Type_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Edition_Number_Type_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Edition_Number_Type_Mutated = {
	__typename?: 'Edition_Number_Type_mutated';
	data?: Maybe<Edition_Number_Type>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export enum EventEnum {
	Create = 'create',
	Delete = 'delete',
	Update = 'update',
}

export type Event_Review_Status = {
	__typename?: 'Event_Review_Status';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Event_Review_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Event_Review_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Event_Review_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Event_Review_Status_Mutated = {
	__typename?: 'Event_Review_Status_mutated';
	data?: Maybe<Event_Review_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Failed_Jobs = {
	__typename?: 'Failed_Jobs';
	data?: Maybe<Scalars['JSON']['output']>;
	data_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	reason?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
};

export type Failed_Jobs_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Failed_Jobs_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Failed_Jobs_Filter>>>;
	data?: InputMaybe<String_Filter_Operators>;
	data_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	reason?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
};

export type Failed_Jobs_Mutated = {
	__typename?: 'Failed_Jobs_mutated';
	data?: Maybe<Failed_Jobs>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Exhibition = {
	__typename?: 'Ingestion_Exhibition';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	event_review_status?: Maybe<Event_Review_Status>;
	id: Scalars['ID']['output'];
	ingestion_data?: Maybe<Array<Maybe<Ingestion_Exhibition_Ingestion_Data>>>;
	ingestion_data_func?: Maybe<Count_Functions>;
	processed_exhibition?: Maybe<Processed_Exhibition>;
	/** Created from exhibition name + gallery (submitted by fe) */
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_ExhibitionEvent_Review_StatusArgs = {
	filter?: InputMaybe<Event_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_ExhibitionIngestion_DataArgs = {
	filter?: InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_ExhibitionProcessed_ExhibitionArgs = {
	filter?: InputMaybe<Processed_Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_ExhibitionUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_ExhibitionUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_ExhibitionVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Exhibition_Visit_Image = {
	__typename?: 'Ingestion_Exhibition_Visit_Image';
	Ingestion_Exhibition_id?: Maybe<Ingestion_Exhibition>;
	Visit_Image_id?: Maybe<Visit_Image>;
	id: Scalars['ID']['output'];
};

export type Ingestion_Exhibition_Visit_ImageIngestion_Exhibition_IdArgs = {
	filter?: InputMaybe<Ingestion_Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Exhibition_Visit_ImageVisit_Image_IdArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Exhibition_Visit_Image_Mutated = {
	__typename?: 'Ingestion_Exhibition_Visit_Image_mutated';
	data?: Maybe<Ingestion_Exhibition_Visit_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Exhibition_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Exhibition_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Exhibition_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Exhibition_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_exhibition?: InputMaybe<Processed_Exhibition_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Ingestion_Exhibition_Ingestion_Data = {
	__typename?: 'Ingestion_Exhibition_ingestion_data';
	Ingestion_Exhibition_id?: Maybe<Ingestion_Exhibition>;
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Ingestion_Exhibition_Ingestion_Data_Item_Union>;
};

export type Ingestion_Exhibition_Ingestion_DataIngestion_Exhibition_IdArgs = {
	filter?: InputMaybe<Ingestion_Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Exhibition_Ingestion_Data_Filter = {
	Ingestion_Exhibition_id?: InputMaybe<Ingestion_Exhibition_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>>
	>;
	_or?: InputMaybe<
		Array<InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>>
	>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Exhibition_Ingestion_Data_Item_Union =
	| Artlogic_Link
	| Manual_Upload
	| Pdf;

export type Ingestion_Exhibition_Ingestion_Data_Mutated = {
	__typename?: 'Ingestion_Exhibition_ingestion_data_mutated';
	data?: Maybe<Ingestion_Exhibition_Ingestion_Data>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Exhibition_Ingestion_Data_Quantifier_Filter = {
	Ingestion_Exhibition_id?: InputMaybe<Ingestion_Exhibition_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>>
	>;
	_none?: InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>;
	_or?: InputMaybe<
		Array<InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>>
	>;
	_some?: InputMaybe<Ingestion_Exhibition_Ingestion_Data_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Exhibition_Mutated = {
	__typename?: 'Ingestion_Exhibition_mutated';
	data?: Maybe<Ingestion_Exhibition>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Exhibition_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Exhibition_Filter>>>;
	_none?: InputMaybe<Ingestion_Exhibition_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Exhibition_Filter>>>;
	_some?: InputMaybe<Ingestion_Exhibition_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Exhibition_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_exhibition?: InputMaybe<Processed_Exhibition_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Ingestion_Fair = {
	__typename?: 'Ingestion_Fair';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	event_review_status?: Maybe<Event_Review_Status>;
	id: Scalars['ID']['output'];
	images?: Maybe<Array<Maybe<Visit_Image>>>;
	images_func?: Maybe<Count_Functions>;
	ingestion_data?: Maybe<Array<Maybe<Ingestion_Fair_Ingestion_Data>>>;
	ingestion_data_func?: Maybe<Count_Functions>;
	is_priority: Scalars['Boolean']['output'];
	processed_fair?: Maybe<Processed_Fair>;
	status?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_FairEvent_Review_StatusArgs = {
	filter?: InputMaybe<Event_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairImagesArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairIngestion_DataArgs = {
	filter?: InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairProcessed_FairArgs = {
	filter?: InputMaybe<Processed_Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_FairVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Fair_Visit_Image = {
	__typename?: 'Ingestion_Fair_Visit_Image';
	Ingestion_Fair_id?: Maybe<Ingestion_Fair>;
	Visit_Image_id?: Maybe<Visit_Image>;
	id: Scalars['ID']['output'];
};

export type Ingestion_Fair_Visit_ImageIngestion_Fair_IdArgs = {
	filter?: InputMaybe<Ingestion_Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Fair_Visit_ImageVisit_Image_IdArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Fair_Visit_Image_Mutated = {
	__typename?: 'Ingestion_Fair_Visit_Image_mutated';
	data?: Maybe<Ingestion_Fair_Visit_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Fair_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Visit_Image_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Fair_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	is_priority?: InputMaybe<Boolean_Filter_Operators>;
	processed_fair?: InputMaybe<Processed_Fair_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Ingestion_Fair_Ingestion_Data = {
	__typename?: 'Ingestion_Fair_ingestion_data';
	Ingestion_Fair_id?: Maybe<Ingestion_Fair>;
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Ingestion_Fair_Ingestion_Data_Item_Union>;
};

export type Ingestion_Fair_Ingestion_DataIngestion_Fair_IdArgs = {
	filter?: InputMaybe<Ingestion_Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Fair_Ingestion_Data_Filter = {
	Ingestion_Fair_id?: InputMaybe<Ingestion_Fair_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Fair_Ingestion_Data_Item_Union =
	| Artlogic_Link
	| Manual_Upload
	| Pdf;

export type Ingestion_Fair_Ingestion_Data_Mutated = {
	__typename?: 'Ingestion_Fair_ingestion_data_mutated';
	data?: Maybe<Ingestion_Fair_Ingestion_Data>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Fair_Ingestion_Data_Quantifier_Filter = {
	Ingestion_Fair_id?: InputMaybe<Ingestion_Fair_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>>>;
	_none?: InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>>>;
	_some?: InputMaybe<Ingestion_Fair_Ingestion_Data_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Fair_Mutated = {
	__typename?: 'Ingestion_Fair_mutated';
	data?: Maybe<Ingestion_Fair>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Fair_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Filter>>>;
	_none?: InputMaybe<Ingestion_Fair_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Fair_Filter>>>;
	_some?: InputMaybe<Ingestion_Fair_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Visit_Image_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Fair_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	is_priority?: InputMaybe<Boolean_Filter_Operators>;
	processed_fair?: InputMaybe<Processed_Fair_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Ingestion_Gallery_Offering = {
	__typename?: 'Ingestion_Gallery_Offering';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	event_review_status?: Maybe<Event_Review_Status>;
	/** not used in v1 */
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	ingestion_data?: Maybe<
		Array<Maybe<Ingestion_Gallery_Offering_Ingestion_Data>>
	>;
	ingestion_data_func?: Maybe<Count_Functions>;
	processed_gallery?: Maybe<Processed_Organisation>;
	/** A title for the gallery offering (for example, Gagosian Summer Newsletter) */
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_Gallery_OfferingEvent_Review_StatusArgs = {
	filter?: InputMaybe<Event_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_OfferingIngestion_DataArgs = {
	filter?: InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_OfferingProcessed_GalleryArgs = {
	filter?: InputMaybe<Processed_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_OfferingUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_OfferingUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_OfferingVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_Offering_Visit = {
	__typename?: 'Ingestion_Gallery_Offering_Visit';
	Ingestion_Gallery_Offering_id?: Maybe<Ingestion_Gallery_Offering>;
	Visit_id?: Maybe<Visit>;
	id: Scalars['ID']['output'];
};

export type Ingestion_Gallery_Offering_VisitIngestion_Gallery_Offering_IdArgs =
	{
		filter?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Ingestion_Gallery_Offering_VisitVisit_IdArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Ingestion_Gallery_Offering_Visit_Mutated = {
	__typename?: 'Ingestion_Gallery_Offering_Visit_mutated';
	data?: Maybe<Ingestion_Gallery_Offering_Visit>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Gallery_Offering_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Gallery_Offering_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Gallery_Offering_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	for_freelancers?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_gallery?: InputMaybe<Processed_Organisation_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Ingestion_Gallery_Offering_Ingestion_Data = {
	__typename?: 'Ingestion_Gallery_Offering_ingestion_data';
	Ingestion_Gallery_Offering_id?: Maybe<Ingestion_Gallery_Offering>;
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Ingestion_Gallery_Offering_Ingestion_Data_Item_Union>;
};

export type Ingestion_Gallery_Offering_Ingestion_DataIngestion_Gallery_Offering_IdArgs =
	{
		filter?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Ingestion_Gallery_Offering_Ingestion_Data_Filter = {
	Ingestion_Gallery_Offering_id?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>>
	>;
	_or?: InputMaybe<
		Array<InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>>
	>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Gallery_Offering_Ingestion_Data_Item_Union =
	| Artlogic_Link
	| Manual_Upload
	| Pdf;

export type Ingestion_Gallery_Offering_Ingestion_Data_Mutated = {
	__typename?: 'Ingestion_Gallery_Offering_ingestion_data_mutated';
	data?: Maybe<Ingestion_Gallery_Offering_Ingestion_Data>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Gallery_Offering_Ingestion_Data_Quantifier_Filter = {
	Ingestion_Gallery_Offering_id?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
	_and?: InputMaybe<
		Array<InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>>
	>;
	_none?: InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>;
	_or?: InputMaybe<
		Array<InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>>
	>;
	_some?: InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item__Artlogic_Link?: InputMaybe<Artlogic_Link_Filter>;
	item__Manual_Upload?: InputMaybe<Manual_Upload_Filter>;
	item__PDF?: InputMaybe<Pdf_Filter>;
};

export type Ingestion_Gallery_Offering_Mutated = {
	__typename?: 'Ingestion_Gallery_Offering_mutated';
	data?: Maybe<Ingestion_Gallery_Offering>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Ingestion_Gallery_Offering_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Ingestion_Gallery_Offering_Filter>>>;
	_none?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Ingestion_Gallery_Offering_Filter>>>;
	_some?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	event_review_status?: InputMaybe<Event_Review_Status_Filter>;
	for_freelancers?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_data?: InputMaybe<Ingestion_Gallery_Offering_Ingestion_Data_Quantifier_Filter>;
	ingestion_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_gallery?: InputMaybe<Processed_Organisation_Filter>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Manual_Upload = {
	__typename?: 'Manual_Upload';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** Not used in V1. Enabled if a freelancer should be able to see this. */
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	/** Whether or not the manual upload has prices included in it. Used only for info purposes within the app for data admins. */
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	manually_added_artworks?: Maybe<Array<Maybe<Manually_Added_Artwork>>>;
	manually_added_artworks_func?: Maybe<Count_Functions>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	reference_files?: Maybe<Array<Maybe<Manual_Upload_Files>>>;
	reference_files_func?: Maybe<Count_Functions>;
	review_status?: Maybe<Checklist_Review_Status>;
	/** When the pdf is submitted (i.e. when status of the artworks is changed to awaiting label parser), this field should be set to True. */
	submitted_for_review?: Maybe<Scalars['Boolean']['output']>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Manual_UploadManually_Added_ArtworksArgs = {
	filter?: InputMaybe<Manually_Added_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadProcessed_Fair_Exhibitor_OrgArgs = {
	filter?: InputMaybe<Processed_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadReceipt_InfoArgs = {
	filter?: InputMaybe<Receipt_Information_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadReference_FilesArgs = {
	filter?: InputMaybe<Manual_Upload_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadReview_StatusArgs = {
	filter?: InputMaybe<Checklist_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_UploadUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_Upload_Files = {
	__typename?: 'Manual_Upload_files';
	Manual_Upload_id?: Maybe<Manual_Upload>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Manual_Upload_FilesManual_Upload_IdArgs = {
	filter?: InputMaybe<Manual_Upload_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_Upload_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manual_Upload_Files_Filter = {
	Manual_Upload_id?: InputMaybe<Manual_Upload_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Manual_Upload_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Manual_Upload_Files_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Manual_Upload_Files_Mutated = {
	__typename?: 'Manual_Upload_files_mutated';
	data?: Maybe<Manual_Upload_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Manual_Upload_Files_Quantifier_Filter = {
	Manual_Upload_id?: InputMaybe<Manual_Upload_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Manual_Upload_Files_Filter>>>;
	_none?: InputMaybe<Manual_Upload_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Manual_Upload_Files_Filter>>>;
	_some?: InputMaybe<Manual_Upload_Files_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Manual_Upload_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Manual_Upload_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Manual_Upload_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	for_freelancers?: InputMaybe<Boolean_Filter_Operators>;
	high_priority?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	includes_prices?: InputMaybe<Boolean_Filter_Operators>;
	manually_added_artworks?: InputMaybe<Manually_Added_Artwork_Quantifier_Filter>;
	manually_added_artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	processed_fair_exhibitor_org?: InputMaybe<Processed_Organisation_Filter>;
	receipt_info?: InputMaybe<Receipt_Information_Filter>;
	reference_files?: InputMaybe<Manual_Upload_Files_Quantifier_Filter>;
	reference_files_func?: InputMaybe<Count_Function_Filter_Operators>;
	review_status?: InputMaybe<Checklist_Review_Status_Filter>;
	submitted_for_review?: InputMaybe<Boolean_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Manual_Upload_Mutated = {
	__typename?: 'Manual_Upload_mutated';
	data?: Maybe<Manual_Upload>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Manually_Added_Artwork = {
	__typename?: 'Manually_Added_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	images?: Maybe<Array<Maybe<Manually_Added_Artwork_Files>>>;
	images_func?: Maybe<Count_Functions>;
	ingestion_job?: Maybe<Failed_Jobs>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	manual_upload?: Maybe<Manual_Upload>;
	processed_activity_id?: Maybe<Scalars['ID']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Manually_Added_ArtworkArtwork_DetailsArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkImagesArgs = {
	filter?: InputMaybe<Manually_Added_Artwork_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkIngestion_JobArgs = {
	filter?: InputMaybe<Failed_Jobs_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkManual_UploadArgs = {
	filter?: InputMaybe<Manual_Upload_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkStatusArgs = {
	filter?: InputMaybe<Artwork_Status_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_Artwork_Files = {
	__typename?: 'Manually_Added_Artwork_files';
	Manually_Added_Artwork_id?: Maybe<Manually_Added_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Manually_Added_Artwork_FilesManually_Added_Artwork_IdArgs = {
	filter?: InputMaybe<Manually_Added_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_Artwork_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Manually_Added_Artwork_Files_Filter = {
	Manually_Added_Artwork_id?: InputMaybe<Manually_Added_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Files_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Manually_Added_Artwork_Files_Mutated = {
	__typename?: 'Manually_Added_Artwork_files_mutated';
	data?: Maybe<Manually_Added_Artwork_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Manually_Added_Artwork_Files_Quantifier_Filter = {
	Manually_Added_Artwork_id?: InputMaybe<Manually_Added_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Files_Filter>>>;
	_none?: InputMaybe<Manually_Added_Artwork_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Files_Filter>>>;
	_some?: InputMaybe<Manually_Added_Artwork_Files_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Manually_Added_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Filter>>>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Manually_Added_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	ingestion_notes?: InputMaybe<String_Filter_Operators>;
	manual_upload?: InputMaybe<Manual_Upload_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Manually_Added_Artwork_Mutated = {
	__typename?: 'Manually_Added_Artwork_mutated';
	data?: Maybe<Manually_Added_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Manually_Added_Artwork_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Filter>>>;
	_none?: InputMaybe<Manually_Added_Artwork_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Manually_Added_Artwork_Filter>>>;
	_some?: InputMaybe<Manually_Added_Artwork_Filter>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Manually_Added_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	ingestion_notes?: InputMaybe<String_Filter_Operators>;
	manual_upload?: InputMaybe<Manual_Upload_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Mutation = {
	__typename?: 'Mutation';
	auth_login?: Maybe<Auth_Tokens>;
	auth_logout?: Maybe<Scalars['Boolean']['output']>;
	auth_password_request?: Maybe<Scalars['Boolean']['output']>;
	auth_password_reset?: Maybe<Scalars['Boolean']['output']>;
	auth_refresh?: Maybe<Auth_Tokens>;
	create_comments_item?: Maybe<Directus_Comments>;
	create_comments_items: Array<Directus_Comments>;
	create_dashboards_item?: Maybe<Directus_Dashboards>;
	create_dashboards_items: Array<Directus_Dashboards>;
	create_files_item?: Maybe<Directus_Files>;
	create_files_items: Array<Directus_Files>;
	create_folders_item?: Maybe<Directus_Folders>;
	create_folders_items: Array<Directus_Folders>;
	create_panels_item?: Maybe<Directus_Panels>;
	create_panels_items: Array<Directus_Panels>;
	create_presets_item?: Maybe<Directus_Presets>;
	create_presets_items: Array<Directus_Presets>;
	create_shares_item?: Maybe<Directus_Shares>;
	create_shares_items: Array<Directus_Shares>;
	delete_comments_item?: Maybe<Delete_One>;
	delete_comments_items?: Maybe<Delete_Many>;
	delete_dashboards_item?: Maybe<Delete_One>;
	delete_dashboards_items?: Maybe<Delete_Many>;
	delete_files_item?: Maybe<Delete_One>;
	delete_files_items?: Maybe<Delete_Many>;
	delete_folders_item?: Maybe<Delete_One>;
	delete_folders_items?: Maybe<Delete_Many>;
	delete_panels_item?: Maybe<Delete_One>;
	delete_panels_items?: Maybe<Delete_Many>;
	delete_presets_item?: Maybe<Delete_One>;
	delete_presets_items?: Maybe<Delete_Many>;
	delete_shares_item?: Maybe<Delete_One>;
	delete_shares_items?: Maybe<Delete_Many>;
	import_file?: Maybe<Directus_Files>;
	update_comments_batch: Array<Directus_Comments>;
	update_comments_item?: Maybe<Directus_Comments>;
	update_comments_items: Array<Directus_Comments>;
	update_dashboards_batch: Array<Directus_Dashboards>;
	update_dashboards_item?: Maybe<Directus_Dashboards>;
	update_dashboards_items: Array<Directus_Dashboards>;
	update_files_batch: Array<Directus_Files>;
	update_files_item?: Maybe<Directus_Files>;
	update_files_items: Array<Directus_Files>;
	update_folders_batch: Array<Directus_Folders>;
	update_folders_item?: Maybe<Directus_Folders>;
	update_folders_items: Array<Directus_Folders>;
	update_notifications_batch: Array<Directus_Notifications>;
	update_notifications_item?: Maybe<Directus_Notifications>;
	update_notifications_items: Array<Directus_Notifications>;
	update_panels_batch: Array<Directus_Panels>;
	update_panels_item?: Maybe<Directus_Panels>;
	update_panels_items: Array<Directus_Panels>;
	update_presets_batch: Array<Directus_Presets>;
	update_presets_item?: Maybe<Directus_Presets>;
	update_presets_items: Array<Directus_Presets>;
	update_shares_batch: Array<Directus_Shares>;
	update_shares_item?: Maybe<Directus_Shares>;
	update_shares_items: Array<Directus_Shares>;
	update_users_batch: Array<Directus_Users>;
	update_users_item?: Maybe<Directus_Users>;
	update_users_items: Array<Directus_Users>;
	update_users_me?: Maybe<Directus_Users>;
	users_invite_accept?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_disable?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_enable?: Maybe<Scalars['Boolean']['output']>;
	users_me_tfa_generate?: Maybe<Users_Me_Tfa_Generate_Data>;
	users_register?: Maybe<Scalars['Boolean']['output']>;
	users_register_verify?: Maybe<Scalars['Boolean']['output']>;
	utils_cache_clear?: Maybe<Scalars['Void']['output']>;
	utils_hash_generate?: Maybe<Scalars['String']['output']>;
	utils_hash_verify?: Maybe<Scalars['Boolean']['output']>;
	utils_random_string?: Maybe<Scalars['String']['output']>;
	utils_revert?: Maybe<Scalars['Boolean']['output']>;
	utils_sort?: Maybe<Scalars['Boolean']['output']>;
};

export type MutationAuth_LoginArgs = {
	email: Scalars['String']['input'];
	mode?: InputMaybe<Auth_Mode>;
	otp?: InputMaybe<Scalars['String']['input']>;
	password: Scalars['String']['input'];
};

export type MutationAuth_LogoutArgs = {
	mode?: InputMaybe<Auth_Mode>;
	refresh_token?: InputMaybe<Scalars['String']['input']>;
};

export type MutationAuth_Password_RequestArgs = {
	email: Scalars['String']['input'];
	reset_url?: InputMaybe<Scalars['String']['input']>;
};

export type MutationAuth_Password_ResetArgs = {
	password: Scalars['String']['input'];
	token: Scalars['String']['input'];
};

export type MutationAuth_RefreshArgs = {
	mode?: InputMaybe<Auth_Mode>;
	refresh_token?: InputMaybe<Scalars['String']['input']>;
};

export type MutationCreate_Comments_ItemArgs = {
	data: Create_Directus_Comments_Input;
};

export type MutationCreate_Comments_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Comments_Input>>;
	filter?: InputMaybe<Directus_Comments_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Dashboards_ItemArgs = {
	data: Create_Directus_Dashboards_Input;
};

export type MutationCreate_Dashboards_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Dashboards_Input>>;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Files_ItemArgs = {
	data: Create_Directus_Files_Input;
};

export type MutationCreate_Files_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Files_Input>>;
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Folders_ItemArgs = {
	data: Create_Directus_Folders_Input;
};

export type MutationCreate_Folders_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Folders_Input>>;
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Panels_ItemArgs = {
	data: Create_Directus_Panels_Input;
};

export type MutationCreate_Panels_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Panels_Input>>;
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Presets_ItemArgs = {
	data: Create_Directus_Presets_Input;
};

export type MutationCreate_Presets_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Presets_Input>>;
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationCreate_Shares_ItemArgs = {
	data: Create_Directus_Shares_Input;
};

export type MutationCreate_Shares_ItemsArgs = {
	data?: InputMaybe<Array<Create_Directus_Shares_Input>>;
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationDelete_Comments_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Comments_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Dashboards_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Dashboards_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Files_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Files_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Folders_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Folders_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Panels_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Panels_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Presets_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Presets_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationDelete_Shares_ItemArgs = {
	id: Scalars['ID']['input'];
};

export type MutationDelete_Shares_ItemsArgs = {
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
};

export type MutationImport_FileArgs = {
	data?: InputMaybe<Create_Directus_Files_Input>;
	url: Scalars['String']['input'];
};

export type MutationUpdate_Comments_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Comments_Input>>;
	filter?: InputMaybe<Directus_Comments_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Comments_ItemArgs = {
	data: Update_Directus_Comments_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Comments_ItemsArgs = {
	data: Update_Directus_Comments_Input;
	filter?: InputMaybe<Directus_Comments_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Dashboards_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Dashboards_Input>>;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Dashboards_ItemArgs = {
	data: Update_Directus_Dashboards_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Dashboards_ItemsArgs = {
	data: Update_Directus_Dashboards_Input;
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Files_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Files_Input>>;
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Files_ItemArgs = {
	data: Update_Directus_Files_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Files_ItemsArgs = {
	data: Update_Directus_Files_Input;
	filter?: InputMaybe<Directus_Files_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Folders_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Folders_Input>>;
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Folders_ItemArgs = {
	data: Update_Directus_Folders_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Folders_ItemsArgs = {
	data: Update_Directus_Folders_Input;
	filter?: InputMaybe<Directus_Folders_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Notifications_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Notifications_Input>>;
	filter?: InputMaybe<Directus_Notifications_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Notifications_ItemArgs = {
	data: Update_Directus_Notifications_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Notifications_ItemsArgs = {
	data: Update_Directus_Notifications_Input;
	filter?: InputMaybe<Directus_Notifications_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Panels_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Panels_Input>>;
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Panels_ItemArgs = {
	data: Update_Directus_Panels_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Panels_ItemsArgs = {
	data: Update_Directus_Panels_Input;
	filter?: InputMaybe<Directus_Panels_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Presets_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Presets_Input>>;
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Presets_ItemArgs = {
	data: Update_Directus_Presets_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Presets_ItemsArgs = {
	data: Update_Directus_Presets_Input;
	filter?: InputMaybe<Directus_Presets_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Shares_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Shares_Input>>;
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Shares_ItemArgs = {
	data: Update_Directus_Shares_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Shares_ItemsArgs = {
	data: Update_Directus_Shares_Input;
	filter?: InputMaybe<Directus_Shares_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_BatchArgs = {
	data?: InputMaybe<Array<Update_Directus_Users_Input>>;
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_ItemArgs = {
	data: Update_Directus_Users_Input;
	id: Scalars['ID']['input'];
};

export type MutationUpdate_Users_ItemsArgs = {
	data: Update_Directus_Users_Input;
	filter?: InputMaybe<Directus_Users_Filter>;
	ids: Array<InputMaybe<Scalars['ID']['input']>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type MutationUpdate_Users_MeArgs = {
	data?: InputMaybe<Update_Directus_Users_Input>;
};

export type MutationUsers_Invite_AcceptArgs = {
	password: Scalars['String']['input'];
	token: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_DisableArgs = {
	otp: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_EnableArgs = {
	otp: Scalars['String']['input'];
	secret: Scalars['String']['input'];
};

export type MutationUsers_Me_Tfa_GenerateArgs = {
	password: Scalars['String']['input'];
};

export type MutationUsers_RegisterArgs = {
	email: Scalars['String']['input'];
	first_name?: InputMaybe<Scalars['String']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	password: Scalars['String']['input'];
	verification_url?: InputMaybe<Scalars['String']['input']>;
};

export type MutationUsers_Register_VerifyArgs = {
	token: Scalars['String']['input'];
};

export type MutationUtils_Hash_GenerateArgs = {
	string: Scalars['String']['input'];
};

export type MutationUtils_Hash_VerifyArgs = {
	hash: Scalars['String']['input'];
	string: Scalars['String']['input'];
};

export type MutationUtils_Random_StringArgs = {
	length?: InputMaybe<Scalars['Int']['input']>;
};

export type MutationUtils_RevertArgs = {
	revision: Scalars['ID']['input'];
};

export type MutationUtils_SortArgs = {
	collection: Scalars['String']['input'];
	item: Scalars['ID']['input'];
	to: Scalars['ID']['input'];
};

export type Pdf = {
	__typename?: 'PDF';
	ai_extracted_text?: Maybe<Scalars['String']['output']>;
	artworks?: Maybe<Array<Maybe<Pdf_Artwork>>>;
	artworks_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	discarded_images?: Maybe<Array<Maybe<Pdf_Discard_Image>>>;
	discarded_images_func?: Maybe<Count_Functions>;
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	/** Whether or not the pdf has prices included in it. Used only for info purposes within the app for data admins. */
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	pages?: Maybe<Array<Maybe<Pdf_Page>>>;
	pages_func?: Maybe<Count_Functions>;
	pdf_artwork_format_method?: Maybe<Scalars['String']['output']>;
	pdf_file?: Maybe<Directus_Files>;
	processed_artist?: Maybe<Processed_Artist>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	review_status?: Maybe<Checklist_Review_Status>;
	status?: Maybe<Pdf_Status>;
	/** When the manual upload is submitted (i.e. when status of the artworks is changed to awaiting label parser), this field should be set to True. */
	submitted_for_review?: Maybe<Scalars['Boolean']['output']>;
	/** The title or name of the catalogue, for example. */
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type PdfArtworksArgs = {
	filter?: InputMaybe<Pdf_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfDiscarded_ImagesArgs = {
	filter?: InputMaybe<Pdf_Discard_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfPagesArgs = {
	filter?: InputMaybe<Pdf_Page_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfPdf_FileArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfProcessed_ArtistArgs = {
	filter?: InputMaybe<Processed_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfProcessed_Fair_Exhibitor_OrgArgs = {
	filter?: InputMaybe<Processed_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfReceipt_InfoArgs = {
	filter?: InputMaybe<Receipt_Information_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfReview_StatusArgs = {
	filter?: InputMaybe<Checklist_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfStatusArgs = {
	filter?: InputMaybe<Pdf_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PdfUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork = {
	__typename?: 'PDF_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	extracted_images?: Maybe<Array<Maybe<Pdf_Artwork_Pdf_Image>>>;
	id: Scalars['ID']['output'];
	images?: Maybe<Array<Maybe<Pdf_Artwork_Files>>>;
	images_func?: Maybe<Count_Functions>;
	ingestion_job?: Maybe<Failed_Jobs>;
	pdf?: Maybe<Pdf>;
	processed_activity_id?: Maybe<Scalars['ID']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Pdf_ArtworkArtwork_DetailsArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkExtracted_ImagesArgs = {
	filter?: InputMaybe<Pdf_Artwork_Pdf_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkImagesArgs = {
	filter?: InputMaybe<Pdf_Artwork_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkIngestion_JobArgs = {
	filter?: InputMaybe<Failed_Jobs_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkPdfArgs = {
	filter?: InputMaybe<Pdf_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkStatusArgs = {
	filter?: InputMaybe<Artwork_Status_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork_Pdf_Image = {
	__typename?: 'PDF_Artwork_PDF_Image';
	PDF_Artwork_id?: Maybe<Pdf_Artwork>;
	PDF_Image_id?: Maybe<Pdf_Image>;
	id: Scalars['ID']['output'];
};

export type Pdf_Artwork_Pdf_ImagePdf_Artwork_IdArgs = {
	filter?: InputMaybe<Pdf_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork_Pdf_ImagePdf_Image_IdArgs = {
	filter?: InputMaybe<Pdf_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork_Pdf_Image_Filter = {
	PDF_Artwork_id?: InputMaybe<Pdf_Artwork_Filter>;
	PDF_Image_id?: InputMaybe<Pdf_Image_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Pdf_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Pdf_Image_Filter>>>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Pdf_Artwork_Pdf_Image_Mutated = {
	__typename?: 'PDF_Artwork_PDF_Image_mutated';
	data?: Maybe<Pdf_Artwork_Pdf_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Artwork_Pdf_Image_Quantifier_Filter = {
	PDF_Artwork_id?: InputMaybe<Pdf_Artwork_Filter>;
	PDF_Image_id?: InputMaybe<Pdf_Image_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Pdf_Image_Filter>>>;
	_none?: InputMaybe<Pdf_Artwork_Pdf_Image_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Pdf_Image_Filter>>>;
	_some?: InputMaybe<Pdf_Artwork_Pdf_Image_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Pdf_Artwork_Files = {
	__typename?: 'PDF_Artwork_files';
	PDF_Artwork_id?: Maybe<Pdf_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Pdf_Artwork_FilesPdf_Artwork_IdArgs = {
	filter?: InputMaybe<Pdf_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Artwork_Files_Filter = {
	PDF_Artwork_id?: InputMaybe<Pdf_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Files_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Pdf_Artwork_Files_Mutated = {
	__typename?: 'PDF_Artwork_files_mutated';
	data?: Maybe<Pdf_Artwork_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Artwork_Files_Quantifier_Filter = {
	PDF_Artwork_id?: InputMaybe<Pdf_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Files_Filter>>>;
	_none?: InputMaybe<Pdf_Artwork_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Files_Filter>>>;
	_some?: InputMaybe<Pdf_Artwork_Files_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Pdf_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Filter>>>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_images?: InputMaybe<Pdf_Artwork_Pdf_Image_Quantifier_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	images?: InputMaybe<Pdf_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	pdf?: InputMaybe<Pdf_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Pdf_Artwork_Mutated = {
	__typename?: 'PDF_Artwork_mutated';
	data?: Maybe<Pdf_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Artwork_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Filter>>>;
	_none?: InputMaybe<Pdf_Artwork_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Artwork_Filter>>>;
	_some?: InputMaybe<Pdf_Artwork_Filter>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_images?: InputMaybe<Pdf_Artwork_Pdf_Image_Quantifier_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	images?: InputMaybe<Pdf_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	pdf?: InputMaybe<Pdf_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Pdf_Discard_Image = {
	__typename?: 'PDF_Discard_Image';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	pdf?: Maybe<Pdf>;
	/** The time that the image was discarded on the frontend by the user. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Pdf_Discard_ImageImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Discard_ImagePdfArgs = {
	filter?: InputMaybe<Pdf_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Discard_ImageUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Discard_ImageUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Discard_Image_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Discard_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Discard_Image_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	pdf?: InputMaybe<Pdf_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Pdf_Discard_Image_Mutated = {
	__typename?: 'PDF_Discard_Image_mutated';
	data?: Maybe<Pdf_Discard_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Discard_Image_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Discard_Image_Filter>>>;
	_none?: InputMaybe<Pdf_Discard_Image_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Discard_Image_Filter>>>;
	_some?: InputMaybe<Pdf_Discard_Image_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	pdf?: InputMaybe<Pdf_Filter>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Pdf_Image = {
	__typename?: 'PDF_Image';
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	pdf_page?: Maybe<Pdf_Page>;
};

export type Pdf_ImageImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_ImagePdf_PageArgs = {
	filter?: InputMaybe<Pdf_Page_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Image_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Image_Filter>>>;
	id?: InputMaybe<Number_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	pdf_page?: InputMaybe<Pdf_Page_Filter>;
};

export type Pdf_Image_Mutated = {
	__typename?: 'PDF_Image_mutated';
	data?: Maybe<Pdf_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Image_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Image_Filter>>>;
	_none?: InputMaybe<Pdf_Image_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Image_Filter>>>;
	_some?: InputMaybe<Pdf_Image_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	pdf_page?: InputMaybe<Pdf_Page_Filter>;
};

export type Pdf_Page = {
	__typename?: 'PDF_Page';
	extracted_images?: Maybe<Array<Maybe<Pdf_Image>>>;
	extracted_images_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	page_number?: Maybe<Scalars['Int']['output']>;
	pdf?: Maybe<Pdf>;
	status?: Maybe<Scalars['String']['output']>;
	/** The text that appears on this page of the PDF. */
	text?: Maybe<Scalars['String']['output']>;
};

export type Pdf_PageExtracted_ImagesArgs = {
	filter?: InputMaybe<Pdf_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_PagePdfArgs = {
	filter?: InputMaybe<Pdf_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Page_Files = {
	__typename?: 'PDF_Page_files';
	PDF_Page_id?: Maybe<Pdf_Page>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
};

export type Pdf_Page_FilesPdf_Page_IdArgs = {
	filter?: InputMaybe<Pdf_Page_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Page_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Page_Files_Mutated = {
	__typename?: 'PDF_Page_files_mutated';
	data?: Maybe<Pdf_Page_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Page_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Page_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Page_Filter>>>;
	extracted_images?: InputMaybe<Pdf_Image_Quantifier_Filter>;
	extracted_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	page_number?: InputMaybe<Number_Filter_Operators>;
	pdf?: InputMaybe<Pdf_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	text?: InputMaybe<String_Filter_Operators>;
};

export type Pdf_Page_Mutated = {
	__typename?: 'PDF_Page_mutated';
	data?: Maybe<Pdf_Page>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Page_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Page_Filter>>>;
	_none?: InputMaybe<Pdf_Page_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Page_Filter>>>;
	_some?: InputMaybe<Pdf_Page_Filter>;
	extracted_images?: InputMaybe<Pdf_Image_Quantifier_Filter>;
	extracted_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	page_number?: InputMaybe<Number_Filter_Operators>;
	pdf?: InputMaybe<Pdf_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	text?: InputMaybe<String_Filter_Operators>;
};

export type Pdf_Status = {
	__typename?: 'PDF_Status';
	key: Scalars['ID']['output'];
	name: Scalars['String']['output'];
};

export type Pdf_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Pdf_Status_Mutated = {
	__typename?: 'PDF_Status_mutated';
	data?: Maybe<Pdf_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Upload_App = {
	__typename?: 'PDF_Upload_App';
	id: Scalars['ID']['output'];
	pdf?: Maybe<Directus_Files>;
	source_info?: Maybe<Scalars['String']['output']>;
};

export type Pdf_Upload_AppPdfArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Pdf_Upload_App_Mutated = {
	__typename?: 'PDF_Upload_App_mutated';
	data?: Maybe<Pdf_Upload_App>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Pdf_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Pdf_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Pdf_Filter>>>;
	ai_extracted_text?: InputMaybe<String_Filter_Operators>;
	artworks?: InputMaybe<Pdf_Artwork_Quantifier_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	discarded_images?: InputMaybe<Pdf_Discard_Image_Quantifier_Filter>;
	discarded_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	for_freelancers?: InputMaybe<Boolean_Filter_Operators>;
	high_priority?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	includes_prices?: InputMaybe<Boolean_Filter_Operators>;
	pages?: InputMaybe<Pdf_Page_Quantifier_Filter>;
	pages_func?: InputMaybe<Count_Function_Filter_Operators>;
	pdf_artwork_format_method?: InputMaybe<String_Filter_Operators>;
	pdf_file?: InputMaybe<Directus_Files_Filter>;
	processed_artist?: InputMaybe<Processed_Artist_Filter>;
	processed_fair_exhibitor_org?: InputMaybe<Processed_Organisation_Filter>;
	receipt_info?: InputMaybe<Receipt_Information_Filter>;
	review_status?: InputMaybe<Checklist_Review_Status_Filter>;
	status?: InputMaybe<Pdf_Status_Filter>;
	submitted_for_review?: InputMaybe<Boolean_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Pdf_Mutated = {
	__typename?: 'PDF_mutated';
	data?: Maybe<Pdf>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Artist = {
	__typename?: 'Processed_Artist';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	first_name?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
	processed_artist_id: Scalars['ID']['output'];
	/** A snapshot of the JSON content of the processed artist. This gets created each time the processed artist is recorded and linked up to an item, and also just before the related data is added to the master record ingestion schema. */
	processed_artist_json?: Maybe<Scalars['JSON']['output']>;
	processed_artist_json_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type Processed_ArtistUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_ArtistUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Processed_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Processed_Artist_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	first_name?: InputMaybe<String_Filter_Operators>;
	last_name?: InputMaybe<String_Filter_Operators>;
	nationality?: InputMaybe<String_Filter_Operators>;
	processed_artist_id?: InputMaybe<String_Filter_Operators>;
	processed_artist_json?: InputMaybe<String_Filter_Operators>;
	processed_artist_json_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	year_birth?: InputMaybe<Number_Filter_Operators>;
	year_death?: InputMaybe<Number_Filter_Operators>;
};

export type Processed_Artist_Mutated = {
	__typename?: 'Processed_Artist_mutated';
	data?: Maybe<Processed_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Artwork = {
	__typename?: 'Processed_Artwork';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	first_name?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	processed_artwork_id: Scalars['ID']['output'];
	processed_artwork_json?: Maybe<Scalars['JSON']['output']>;
	processed_artwork_json_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['String']['output']>;
	year_death?: Maybe<Scalars['String']['output']>;
};

export type Processed_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_Artwork_Mutated = {
	__typename?: 'Processed_Artwork_mutated';
	data?: Maybe<Processed_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Exhibition = {
	__typename?: 'Processed_Exhibition';
	artists?: Maybe<Scalars['JSON']['output']>;
	artists_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	end_date?: Maybe<Scalars['Date']['output']>;
	end_date_func?: Maybe<Datetime_Functions>;
	location?: Maybe<Scalars['String']['output']>;
	organisers?: Maybe<Scalars['String']['output']>;
	processed_exhibition_id: Scalars['ID']['output'];
	start_date?: Maybe<Scalars['Date']['output']>;
	start_date_func?: Maybe<Datetime_Functions>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Processed_ExhibitionUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_ExhibitionUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_Exhibition_Processed_Artist = {
	__typename?: 'Processed_Exhibition_Processed_Artist';
	Processed_Artist_processed_artist_id?: Maybe<Processed_Artist>;
	Processed_Exhibition_processed_exhibition_id?: Maybe<Processed_Exhibition>;
	id: Scalars['ID']['output'];
};

export type Processed_Exhibition_Processed_ArtistProcessed_Artist_Processed_Artist_IdArgs =
	{
		filter?: InputMaybe<Processed_Artist_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Processed_Exhibition_Processed_ArtistProcessed_Exhibition_Processed_Exhibition_IdArgs =
	{
		filter?: InputMaybe<Processed_Exhibition_Filter>;
		limit?: InputMaybe<Scalars['Int']['input']>;
		offset?: InputMaybe<Scalars['Int']['input']>;
		page?: InputMaybe<Scalars['Int']['input']>;
		search?: InputMaybe<Scalars['String']['input']>;
		sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	};

export type Processed_Exhibition_Processed_Artist_Mutated = {
	__typename?: 'Processed_Exhibition_Processed_Artist_mutated';
	data?: Maybe<Processed_Exhibition_Processed_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Exhibition_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Processed_Exhibition_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Processed_Exhibition_Filter>>>;
	artists?: InputMaybe<String_Filter_Operators>;
	artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	organisers?: InputMaybe<String_Filter_Operators>;
	processed_exhibition_id?: InputMaybe<String_Filter_Operators>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Processed_Exhibition_Mutated = {
	__typename?: 'Processed_Exhibition_mutated';
	data?: Maybe<Processed_Exhibition>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Fair = {
	__typename?: 'Processed_Fair';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	end_date?: Maybe<Scalars['Date']['output']>;
	end_date_func?: Maybe<Datetime_Functions>;
	location?: Maybe<Scalars['String']['output']>;
	processed_fair_id: Scalars['ID']['output'];
	start_date?: Maybe<Scalars['Date']['output']>;
	start_date_func?: Maybe<Datetime_Functions>;
	title: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Processed_FairUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_FairUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Processed_Fair_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Processed_Fair_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Processed_Fair_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	processed_fair_id?: InputMaybe<String_Filter_Operators>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Processed_Fair_Mutated = {
	__typename?: 'Processed_Fair_mutated';
	data?: Maybe<Processed_Fair>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Processed_Organisation = {
	__typename?: 'Processed_Organisation';
	entity?: Maybe<Scalars['ID']['output']>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	/** This will be brought in from the entity name (which will be synced across from organisation name) in the processed table */
	name: Scalars['String']['output'];
	/** The organisation_type as defined in the processed DB. */
	type?: Maybe<Scalars['String']['output']>;
};

export type Processed_Organisation_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Processed_Organisation_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Processed_Organisation_Filter>>>;
	entity?: InputMaybe<Id_Filter_Operators>;
	id?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
};

export type Processed_Organisation_Mutated = {
	__typename?: 'Processed_Organisation_mutated';
	data?: Maybe<Processed_Organisation>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Query = {
	__typename?: 'Query';
	activity: Array<Directus_Activity>;
	activity_aggregated: Array<Directus_Activity_Aggregated>;
	activity_by_id?: Maybe<Directus_Activity>;
	collections: Array<Directus_Collections>;
	collections_by_name?: Maybe<Directus_Collections>;
	comments: Array<Directus_Comments>;
	comments_aggregated: Array<Directus_Comments_Aggregated>;
	comments_by_id?: Maybe<Directus_Comments>;
	dashboards: Array<Directus_Dashboards>;
	dashboards_aggregated: Array<Directus_Dashboards_Aggregated>;
	dashboards_by_id?: Maybe<Directus_Dashboards>;
	fields: Array<Directus_Fields>;
	fields_by_name?: Maybe<Directus_Fields>;
	fields_in_collection: Array<Directus_Fields>;
	files: Array<Directus_Files>;
	files_aggregated: Array<Directus_Files_Aggregated>;
	files_by_id?: Maybe<Directus_Files>;
	flows: Array<Directus_Flows>;
	flows_aggregated: Array<Directus_Flows_Aggregated>;
	flows_by_id?: Maybe<Directus_Flows>;
	folders: Array<Directus_Folders>;
	folders_aggregated: Array<Directus_Folders_Aggregated>;
	folders_by_id?: Maybe<Directus_Folders>;
	notifications: Array<Directus_Notifications>;
	notifications_aggregated: Array<Directus_Notifications_Aggregated>;
	notifications_by_id?: Maybe<Directus_Notifications>;
	panels: Array<Directus_Panels>;
	panels_aggregated: Array<Directus_Panels_Aggregated>;
	panels_by_id?: Maybe<Directus_Panels>;
	presets: Array<Directus_Presets>;
	presets_aggregated: Array<Directus_Presets_Aggregated>;
	presets_by_id?: Maybe<Directus_Presets>;
	relations: Array<Directus_Relations>;
	relations_by_name?: Maybe<Directus_Relations>;
	relations_in_collection: Array<Directus_Relations>;
	revisions: Array<Directus_Revisions>;
	revisions_aggregated: Array<Directus_Revisions_Aggregated>;
	revisions_by_id?: Maybe<Directus_Revisions>;
	roles: Array<Directus_Roles>;
	roles_aggregated: Array<Directus_Roles_Aggregated>;
	roles_by_id?: Maybe<Directus_Roles>;
	roles_me?: Maybe<Array<Maybe<Directus_Roles>>>;
	server_health?: Maybe<Scalars['JSON']['output']>;
	server_info?: Maybe<Server_Info>;
	server_ping?: Maybe<Scalars['String']['output']>;
	server_specs_graphql?: Maybe<Scalars['String']['output']>;
	server_specs_oas?: Maybe<Scalars['JSON']['output']>;
	settings?: Maybe<Directus_Settings>;
	shares: Array<Directus_Shares>;
	shares_aggregated: Array<Directus_Shares_Aggregated>;
	shares_by_id?: Maybe<Directus_Shares>;
	translations: Array<Directus_Translations>;
	translations_aggregated: Array<Directus_Translations_Aggregated>;
	translations_by_id?: Maybe<Directus_Translations>;
	users: Array<Directus_Users>;
	users_aggregated: Array<Directus_Users_Aggregated>;
	users_by_id?: Maybe<Directus_Users>;
	users_me?: Maybe<Directus_Users>;
};

export type QueryActivityArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryActivity_AggregatedArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryActivity_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryCollections_By_NameArgs = {
	name: Scalars['String']['input'];
};

export type QueryCommentsArgs = {
	filter?: InputMaybe<Directus_Comments_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryComments_AggregatedArgs = {
	filter?: InputMaybe<Directus_Comments_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryComments_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryDashboardsArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryDashboards_AggregatedArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryDashboards_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFields_By_NameArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type QueryFields_In_CollectionArgs = {
	collection: Scalars['String']['input'];
};

export type QueryFilesArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFiles_AggregatedArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFiles_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFlowsArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFlows_AggregatedArgs = {
	filter?: InputMaybe<Directus_Flows_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFlows_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryFoldersArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFolders_AggregatedArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryFolders_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryNotificationsArgs = {
	filter?: InputMaybe<Directus_Notifications_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryNotifications_AggregatedArgs = {
	filter?: InputMaybe<Directus_Notifications_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryNotifications_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPanelsArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPanels_AggregatedArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPanels_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPresetsArgs = {
	filter?: InputMaybe<Directus_Presets_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPresets_AggregatedArgs = {
	filter?: InputMaybe<Directus_Presets_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryPresets_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryRelations_By_NameArgs = {
	collection: Scalars['String']['input'];
	field: Scalars['String']['input'];
};

export type QueryRelations_In_CollectionArgs = {
	collection: Scalars['String']['input'];
};

export type QueryRevisionsArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRevisions_AggregatedArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRevisions_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryRolesArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRoles_AggregatedArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryRoles_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryServer_Specs_GraphqlArgs = {
	scope?: InputMaybe<Graphql_Sdl_Scope>;
};

export type QuerySettingsArgs = {
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QuerySharesArgs = {
	filter?: InputMaybe<Directus_Shares_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryShares_AggregatedArgs = {
	filter?: InputMaybe<Directus_Shares_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryShares_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryTranslationsArgs = {
	filter?: InputMaybe<Directus_Translations_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryTranslations_AggregatedArgs = {
	filter?: InputMaybe<Directus_Translations_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryTranslations_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type QueryUsersArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryUsers_AggregatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	groupBy?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type QueryUsers_By_IdArgs = {
	id: Scalars['ID']['input'];
	version?: InputMaybe<Scalars['String']['input']>;
};

export type Receipt_Information = {
	__typename?: 'Receipt_Information';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	receive_date?: Maybe<Scalars['Date']['output']>;
	receive_date_func?: Maybe<Datetime_Functions>;
	/** The email or name of the person who received this pdf */
	receiver?: Maybe<Scalars['String']['output']>;
	/** The email address or name of the person who sent this pdf */
	sender?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['ID']['output']>;
	user_updated?: Maybe<Directus_Users>;
};

export type Receipt_InformationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Receipt_Information_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Receipt_Information_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Receipt_Information_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	receive_date?: InputMaybe<Date_Filter_Operators>;
	receive_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	receiver?: InputMaybe<String_Filter_Operators>;
	sender?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Id_Filter_Operators>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Receipt_Information_Mutated = {
	__typename?: 'Receipt_Information_mutated';
	data?: Maybe<Receipt_Information>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artist = {
	__typename?: 'Scraped_Artist';
	artist_details?: Maybe<Artist_Details>;
	/** This should not be changed. This is used for matching the artwork_feed.artist and Scraped_Artwork.artwork_details.artist to a Scraped_Artist. */
	artist_text_key: Scalars['String']['output'];
	data_source: Scalars['String']['output'];
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	scraped_artworks?: Maybe<Array<Maybe<Scraped_Artwork_Scraped_Artist>>>;
	scraped_artworks_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_ArtistArtist_DetailsArgs = {
	filter?: InputMaybe<Artist_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtistScraped_ArtworksArgs = {
	filter?: InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtistUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtistUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artist_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artist_Filter>>>;
	artist_details?: InputMaybe<Artist_Details_Filter>;
	artist_text_key?: InputMaybe<String_Filter_Operators>;
	data_source?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	scraped_artworks?: InputMaybe<Scraped_Artwork_Scraped_Artist_Quantifier_Filter>;
	scraped_artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Artist_Mutated = {
	__typename?: 'Scraped_Artist_mutated';
	data?: Maybe<Scraped_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artwork = {
	__typename?: 'Scraped_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	artwork_feed?: Maybe<Artwork_Feed>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	extracted_provenance_lines?: Maybe<Scalars['JSON']['output']>;
	extracted_provenance_lines_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	images?: Maybe<Array<Maybe<Scraped_Artwork_Files>>>;
	images_func?: Maybe<Count_Functions>;
	ingestion_job?: Maybe<Failed_Jobs>;
	processed_activity_id?: Maybe<Scalars['ID']['output']>;
	provenance_matched_activity_id?: Maybe<Scalars['String']['output']>;
	provenance_matched_artwork_id?: Maybe<Scalars['String']['output']>;
	scraped_artists?: Maybe<Array<Maybe<Scraped_Artwork_Scraped_Artist>>>;
	scraped_artists_func?: Maybe<Count_Functions>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_ArtworkArtwork_DetailsArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkArtwork_FeedArgs = {
	filter?: InputMaybe<Artwork_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkImagesArgs = {
	filter?: InputMaybe<Scraped_Artwork_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkIngestion_JobArgs = {
	filter?: InputMaybe<Failed_Jobs_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkScraped_ArtistsArgs = {
	filter?: InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkStatusArgs = {
	filter?: InputMaybe<Artwork_Status_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Artist_Details = {
	__typename?: 'Scraped_Artwork_Artist_Details';
	Artist_Details_id?: Maybe<Artist_Details>;
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	id: Scalars['ID']['output'];
};

export type Scraped_Artwork_Artist_DetailsArtist_Details_IdArgs = {
	filter?: InputMaybe<Artist_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Artist_DetailsScraped_Artwork_IdArgs = {
	filter?: InputMaybe<Scraped_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Artist_Details_Mutated = {
	__typename?: 'Scraped_Artwork_Artist_Details_mutated';
	data?: Maybe<Scraped_Artwork_Artist_Details>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artwork_Scraped_Artist = {
	__typename?: 'Scraped_Artwork_Scraped_Artist';
	Scraped_Artist_id?: Maybe<Scraped_Artist>;
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	id: Scalars['ID']['output'];
};

export type Scraped_Artwork_Scraped_ArtistScraped_Artist_IdArgs = {
	filter?: InputMaybe<Scraped_Artist_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Scraped_ArtistScraped_Artwork_IdArgs = {
	filter?: InputMaybe<Scraped_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Scraped_Artist_Filter = {
	Scraped_Artist_id?: InputMaybe<Scraped_Artist_Filter>;
	Scraped_Artwork_id?: InputMaybe<Scraped_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>>>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Scraped_Artwork_Scraped_Artist_Mutated = {
	__typename?: 'Scraped_Artwork_Scraped_Artist_mutated';
	data?: Maybe<Scraped_Artwork_Scraped_Artist>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artwork_Scraped_Artist_Quantifier_Filter = {
	Scraped_Artist_id?: InputMaybe<Scraped_Artist_Filter>;
	Scraped_Artwork_id?: InputMaybe<Scraped_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>>>;
	_none?: InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>>>;
	_some?: InputMaybe<Scraped_Artwork_Scraped_Artist_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Scraped_Artwork_Files = {
	__typename?: 'Scraped_Artwork_files';
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Scraped_Artwork_FilesScraped_Artwork_IdArgs = {
	filter?: InputMaybe<Scraped_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Artwork_Files_Filter = {
	Scraped_Artwork_id?: InputMaybe<Scraped_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Files_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Scraped_Artwork_Files_Mutated = {
	__typename?: 'Scraped_Artwork_files_mutated';
	data?: Maybe<Scraped_Artwork_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artwork_Files_Quantifier_Filter = {
	Scraped_Artwork_id?: InputMaybe<Scraped_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Files_Filter>>>;
	_none?: InputMaybe<Scraped_Artwork_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Files_Filter>>>;
	_some?: InputMaybe<Scraped_Artwork_Files_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Scraped_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Filter>>>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	artwork_feed?: InputMaybe<Artwork_Feed_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_provenance_lines?: InputMaybe<String_Filter_Operators>;
	extracted_provenance_lines_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Scraped_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	provenance_matched_activity_id?: InputMaybe<String_Filter_Operators>;
	provenance_matched_artwork_id?: InputMaybe<String_Filter_Operators>;
	scraped_artists?: InputMaybe<Scraped_Artwork_Scraped_Artist_Quantifier_Filter>;
	scraped_artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Artwork_Mutated = {
	__typename?: 'Scraped_Artwork_mutated';
	data?: Maybe<Scraped_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Artwork_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Filter>>>;
	_none?: InputMaybe<Scraped_Artwork_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Artwork_Filter>>>;
	_some?: InputMaybe<Scraped_Artwork_Filter>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	artwork_feed?: InputMaybe<Artwork_Feed_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_provenance_lines?: InputMaybe<String_Filter_Operators>;
	extracted_provenance_lines_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Scraped_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	provenance_matched_activity_id?: InputMaybe<String_Filter_Operators>;
	provenance_matched_artwork_id?: InputMaybe<String_Filter_Operators>;
	scraped_artists?: InputMaybe<Scraped_Artwork_Scraped_Artist_Quantifier_Filter>;
	scraped_artists_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Entity = {
	__typename?: 'Scraped_Entity';
	associated_entity_feed?: Maybe<Associated_Entity_Feed>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	processed_entity_id?: Maybe<Scalars['String']['output']>;
	processed_reference_id?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_EntityAssociated_Entity_FeedArgs = {
	filter?: InputMaybe<Associated_Entity_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_EntityUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_EntityUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Entity_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Entity_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Entity_Filter>>>;
	associated_entity_feed?: InputMaybe<Associated_Entity_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	processed_entity_id?: InputMaybe<String_Filter_Operators>;
	processed_reference_id?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Entity_Mutated = {
	__typename?: 'Scraped_Entity_mutated';
	data?: Maybe<Scraped_Entity>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Entity_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Entity_Filter>>>;
	_none?: InputMaybe<Scraped_Entity_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Entity_Filter>>>;
	_some?: InputMaybe<Scraped_Entity_Filter>;
	associated_entity_feed?: InputMaybe<Associated_Entity_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	processed_entity_id?: InputMaybe<String_Filter_Operators>;
	processed_reference_id?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Event = {
	__typename?: 'Scraped_Event';
	art_event_feed?: Maybe<Art_Event_Feed>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	processed_event_id?: Maybe<Scalars['ID']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_EventArt_Event_FeedArgs = {
	filter?: InputMaybe<Art_Event_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_EventUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_EventUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Scraped_Event_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Event_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Event_Filter>>>;
	art_event_feed?: InputMaybe<Art_Event_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	processed_event_id?: InputMaybe<Id_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Scraped_Event_Mutated = {
	__typename?: 'Scraped_Event_mutated';
	data?: Maybe<Scraped_Event>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Scraped_Event_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Scraped_Event_Filter>>>;
	_none?: InputMaybe<Scraped_Event_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Scraped_Event_Filter>>>;
	_some?: InputMaybe<Scraped_Event_Filter>;
	art_event_feed?: InputMaybe<Art_Event_Feed_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	processed_event_id?: InputMaybe<Id_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Subscription = {
	__typename?: 'Subscription';
	Artist_Details_mutated?: Maybe<Artist_Details_Mutated>;
	Artlogic_Link_Status_mutated?: Maybe<Artlogic_Link_Status_Mutated>;
	Artlogic_Link_mutated?: Maybe<Artlogic_Link_Mutated>;
	Artwork_Details_artwork_lot_symbol_lookup_mutated?: Maybe<Artwork_Details_Artwork_Lot_Symbol_Lookup_Mutated>;
	Artwork_Details_mutated?: Maybe<Artwork_Details_Mutated>;
	Artwork_Dimension_Type_mutated?: Maybe<Artwork_Dimension_Type_Mutated>;
	Artwork_Status_Type_mutated?: Maybe<Artwork_Status_Type_Mutated>;
	Artwork_Type_mutated?: Maybe<Artwork_Type_Mutated>;
	Checklist_Review_Status_mutated?: Maybe<Checklist_Review_Status_Mutated>;
	Edition_Number_Type_mutated?: Maybe<Edition_Number_Type_Mutated>;
	Event_Review_Status_mutated?: Maybe<Event_Review_Status_Mutated>;
	Failed_Jobs_mutated?: Maybe<Failed_Jobs_Mutated>;
	Ingestion_Exhibition_Visit_Image_mutated?: Maybe<Ingestion_Exhibition_Visit_Image_Mutated>;
	Ingestion_Exhibition_ingestion_data_mutated?: Maybe<Ingestion_Exhibition_Ingestion_Data_Mutated>;
	Ingestion_Exhibition_mutated?: Maybe<Ingestion_Exhibition_Mutated>;
	Ingestion_Fair_Visit_Image_mutated?: Maybe<Ingestion_Fair_Visit_Image_Mutated>;
	Ingestion_Fair_ingestion_data_mutated?: Maybe<Ingestion_Fair_Ingestion_Data_Mutated>;
	Ingestion_Fair_mutated?: Maybe<Ingestion_Fair_Mutated>;
	Ingestion_Gallery_Offering_Visit_mutated?: Maybe<Ingestion_Gallery_Offering_Visit_Mutated>;
	Ingestion_Gallery_Offering_ingestion_data_mutated?: Maybe<Ingestion_Gallery_Offering_Ingestion_Data_Mutated>;
	Ingestion_Gallery_Offering_mutated?: Maybe<Ingestion_Gallery_Offering_Mutated>;
	Manual_Upload_files_mutated?: Maybe<Manual_Upload_Files_Mutated>;
	Manual_Upload_mutated?: Maybe<Manual_Upload_Mutated>;
	Manually_Added_Artwork_files_mutated?: Maybe<Manually_Added_Artwork_Files_Mutated>;
	Manually_Added_Artwork_mutated?: Maybe<Manually_Added_Artwork_Mutated>;
	PDF_Artwork_PDF_Image_mutated?: Maybe<Pdf_Artwork_Pdf_Image_Mutated>;
	PDF_Artwork_files_mutated?: Maybe<Pdf_Artwork_Files_Mutated>;
	PDF_Artwork_mutated?: Maybe<Pdf_Artwork_Mutated>;
	PDF_Discard_Image_mutated?: Maybe<Pdf_Discard_Image_Mutated>;
	PDF_Image_mutated?: Maybe<Pdf_Image_Mutated>;
	PDF_Page_files_mutated?: Maybe<Pdf_Page_Files_Mutated>;
	PDF_Page_mutated?: Maybe<Pdf_Page_Mutated>;
	PDF_Status_mutated?: Maybe<Pdf_Status_Mutated>;
	PDF_Upload_App_mutated?: Maybe<Pdf_Upload_App_Mutated>;
	PDF_mutated?: Maybe<Pdf_Mutated>;
	Processed_Artist_mutated?: Maybe<Processed_Artist_Mutated>;
	Processed_Artwork_mutated?: Maybe<Processed_Artwork_Mutated>;
	Processed_Exhibition_Processed_Artist_mutated?: Maybe<Processed_Exhibition_Processed_Artist_Mutated>;
	Processed_Exhibition_mutated?: Maybe<Processed_Exhibition_Mutated>;
	Processed_Fair_mutated?: Maybe<Processed_Fair_Mutated>;
	Processed_Organisation_mutated?: Maybe<Processed_Organisation_Mutated>;
	Receipt_Information_mutated?: Maybe<Receipt_Information_Mutated>;
	Scraped_Artist_mutated?: Maybe<Scraped_Artist_Mutated>;
	Scraped_Artwork_Artist_Details_mutated?: Maybe<Scraped_Artwork_Artist_Details_Mutated>;
	Scraped_Artwork_Scraped_Artist_mutated?: Maybe<Scraped_Artwork_Scraped_Artist_Mutated>;
	Scraped_Artwork_files_mutated?: Maybe<Scraped_Artwork_Files_Mutated>;
	Scraped_Artwork_mutated?: Maybe<Scraped_Artwork_Mutated>;
	Scraped_Entity_mutated?: Maybe<Scraped_Entity_Mutated>;
	Scraped_Event_mutated?: Maybe<Scraped_Event_Mutated>;
	Visit_Artwork_files_mutated?: Maybe<Visit_Artwork_Files_Mutated>;
	Visit_Artwork_mutated?: Maybe<Visit_Artwork_Mutated>;
	Visit_Discard_Image_mutated?: Maybe<Visit_Discard_Image_Mutated>;
	Visit_Image_Status_mutated?: Maybe<Visit_Image_Status_Mutated>;
	Visit_Image_mutated?: Maybe<Visit_Image_Mutated>;
	Visit_Phone_Registration_Visit_Image_mutated?: Maybe<Visit_Phone_Registration_Visit_Image_Mutated>;
	Visit_Phone_Registration_mutated?: Maybe<Visit_Phone_Registration_Mutated>;
	Visit_Phone_Status_mutated?: Maybe<Visit_Phone_Status_Mutated>;
	Visit_Phone_mutated?: Maybe<Visit_Phone_Mutated>;
	Visit_mutated?: Maybe<Visit_Mutated>;
	Visit_visit_items_mutated?: Maybe<Visit_Visit_Items_Mutated>;
	art_event_feed_mutated?: Maybe<Art_Event_Feed_Mutated>;
	art_event_type_lookup_mutated?: Maybe<Art_Event_Type_Lookup_Mutated>;
	artwork_artwork_lot_symbol_link_mutated?: Maybe<Artwork_Artwork_Lot_Symbol_Link_Mutated>;
	artwork_associated_entity_link_mutated?: Maybe<Artwork_Associated_Entity_Link_Mutated>;
	artwork_feed_files_mutated?: Maybe<Artwork_Feed_Files_Mutated>;
	artwork_feed_mutated?: Maybe<Artwork_Feed_Mutated>;
	artwork_feed_revisions_mutated?: Maybe<Artwork_Feed_Revisions_Mutated>;
	artwork_lot_symbol_lookup_mutated?: Maybe<Artwork_Lot_Symbol_Lookup_Mutated>;
	artwork_sale_status_lookup_mutated?: Maybe<Artwork_Sale_Status_Lookup_Mutated>;
	artwork_type_lookup_mutated?: Maybe<Artwork_Type_Lookup_Mutated>;
	associated_entity_feed_mutated?: Maybe<Associated_Entity_Feed_Mutated>;
	association_type_lookup_mutated?: Maybe<Association_Type_Lookup_Mutated>;
	country_mutated?: Maybe<Country_Mutated>;
	currency_mutated?: Maybe<Currency_Mutated>;
	directus_activity_mutated?: Maybe<Directus_Activity_Mutated>;
	directus_comments_mutated?: Maybe<Directus_Comments_Mutated>;
	directus_dashboards_mutated?: Maybe<Directus_Dashboards_Mutated>;
	directus_files_mutated?: Maybe<Directus_Files_Mutated>;
	directus_flows_mutated?: Maybe<Directus_Flows_Mutated>;
	directus_folders_mutated?: Maybe<Directus_Folders_Mutated>;
	directus_notifications_mutated?: Maybe<Directus_Notifications_Mutated>;
	directus_panels_mutated?: Maybe<Directus_Panels_Mutated>;
	directus_presets_mutated?: Maybe<Directus_Presets_Mutated>;
	directus_revisions_mutated?: Maybe<Directus_Revisions_Mutated>;
	directus_roles_mutated?: Maybe<Directus_Roles_Mutated>;
	directus_settings_mutated?: Maybe<Directus_Settings_Mutated>;
	directus_shares_mutated?: Maybe<Directus_Shares_Mutated>;
	directus_translations_mutated?: Maybe<Directus_Translations_Mutated>;
	directus_users_mutated?: Maybe<Directus_Users_Mutated>;
	timezone_mutated?: Maybe<Timezone_Mutated>;
	uom_mutated?: Maybe<Uom_Mutated>;
};

export type SubscriptionArtist_Details_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtlogic_Link_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtlogic_Link_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Details_Artwork_Lot_Symbol_Lookup_MutatedArgs =
	{
		event?: InputMaybe<EventEnum>;
	};

export type SubscriptionArtwork_Details_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Dimension_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Status_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionChecklist_Review_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEdition_Number_Type_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionEvent_Review_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionFailed_Jobs_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Exhibition_Visit_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Exhibition_Ingestion_Data_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Exhibition_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Fair_Visit_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Fair_Ingestion_Data_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Fair_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Gallery_Offering_Visit_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionIngestion_Gallery_Offering_Ingestion_Data_MutatedArgs =
	{
		event?: InputMaybe<EventEnum>;
	};

export type SubscriptionIngestion_Gallery_Offering_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionManual_Upload_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionManual_Upload_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionManually_Added_Artwork_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionManually_Added_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Artwork_Pdf_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Artwork_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Discard_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Page_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Page_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_Upload_App_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionPdf_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Exhibition_Processed_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Exhibition_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Fair_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionProcessed_Organisation_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionReceipt_Information_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Artwork_Artist_Details_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Artwork_Scraped_Artist_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Artwork_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Entity_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionScraped_Event_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Artwork_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Artwork_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Discard_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Image_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Phone_Registration_Visit_Image_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Phone_Registration_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Phone_Status_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Phone_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionVisit_Visit_Items_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArt_Event_Feed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArt_Event_Type_Lookup_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Artwork_Lot_Symbol_Link_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Associated_Entity_Link_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Feed_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Feed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Feed_Revisions_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Lot_Symbol_Lookup_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Sale_Status_Lookup_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionArtwork_Type_Lookup_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAssociated_Entity_Feed_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionAssociation_Type_Lookup_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCountry_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionCurrency_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Activity_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Comments_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Dashboards_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Files_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Flows_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Folders_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Notifications_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Panels_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Presets_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Revisions_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Roles_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Settings_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Shares_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Translations_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionDirectus_Users_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionTimezone_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type SubscriptionUom_MutatedArgs = {
	event?: InputMaybe<EventEnum>;
};

export type Visit = {
	__typename?: 'Visit';
	artworks?: Maybe<Array<Maybe<Visit_Artwork>>>;
	artworks_func?: Maybe<Count_Functions>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	discarded_visit_images?: Maybe<Array<Maybe<Visit_Discard_Image>>>;
	discarded_visit_images_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	ingestion_exhibition?: Maybe<Array<Maybe<Ingestion_Exhibition>>>;
	ingestion_exhibition_func?: Maybe<Count_Functions>;
	ingestion_fair?: Maybe<Array<Maybe<Ingestion_Fair>>>;
	ingestion_fair_func?: Maybe<Count_Functions>;
	ingestion_gallery_offering?: Maybe<Array<Maybe<Ingestion_Gallery_Offering>>>;
	ingestion_gallery_offering_func?: Maybe<Count_Functions>;
	review_status?: Maybe<Checklist_Review_Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit_images?: Maybe<Array<Maybe<Visit_Image>>>;
	visit_images_func?: Maybe<Count_Functions>;
};

export type VisitArtworksArgs = {
	filter?: InputMaybe<Visit_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitDiscarded_Visit_ImagesArgs = {
	filter?: InputMaybe<Visit_Discard_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitIngestion_ExhibitionArgs = {
	filter?: InputMaybe<Ingestion_Exhibition_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitIngestion_FairArgs = {
	filter?: InputMaybe<Ingestion_Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitIngestion_Gallery_OfferingArgs = {
	filter?: InputMaybe<Ingestion_Gallery_Offering_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitReview_StatusArgs = {
	filter?: InputMaybe<Checklist_Review_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type VisitVisit_ImagesArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Artwork = {
	__typename?: 'Visit_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	artwork_image?: Maybe<Visit_Image>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	empty_artwork_image?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	images?: Maybe<Array<Maybe<Visit_Artwork_Files>>>;
	images_func?: Maybe<Count_Functions>;
	ingestion_job?: Maybe<Failed_Jobs>;
	is_installation_shot: Scalars['Boolean']['output'];
	label_image?: Maybe<Visit_Image>;
	label_text?: Maybe<Scalars['String']['output']>;
	processed_activity_id?: Maybe<Scalars['ID']['output']>;
	processed_artwork_id?: Maybe<Scalars['String']['output']>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Visit_ArtworkArtwork_DetailsArgs = {
	filter?: InputMaybe<Artwork_Details_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkArtwork_ImageArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkImagesArgs = {
	filter?: InputMaybe<Visit_Artwork_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkIngestion_JobArgs = {
	filter?: InputMaybe<Failed_Jobs_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkLabel_ImageArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkProcessed_Fair_Exhibitor_OrgArgs = {
	filter?: InputMaybe<Processed_Organisation_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkStatusArgs = {
	filter?: InputMaybe<Artwork_Status_Type_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ArtworkVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Artwork_Files = {
	__typename?: 'Visit_Artwork_files';
	Visit_Artwork_id?: Maybe<Visit_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
};

export type Visit_Artwork_FilesVisit_Artwork_IdArgs = {
	filter?: InputMaybe<Visit_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Artwork_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Artwork_Files_Filter = {
	Visit_Artwork_id?: InputMaybe<Visit_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Visit_Artwork_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Artwork_Files_Filter>>>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Visit_Artwork_Files_Mutated = {
	__typename?: 'Visit_Artwork_files_mutated';
	data?: Maybe<Visit_Artwork_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Artwork_Files_Quantifier_Filter = {
	Visit_Artwork_id?: InputMaybe<Visit_Artwork_Filter>;
	_and?: InputMaybe<Array<InputMaybe<Visit_Artwork_Files_Filter>>>;
	_none?: InputMaybe<Visit_Artwork_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Artwork_Files_Filter>>>;
	_some?: InputMaybe<Visit_Artwork_Files_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Visit_Artwork_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Artwork_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Artwork_Filter>>>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	artwork_image?: InputMaybe<Visit_Image_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	empty_artwork_image?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Visit_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	is_installation_shot?: InputMaybe<Boolean_Filter_Operators>;
	label_image?: InputMaybe<Visit_Image_Filter>;
	label_text?: InputMaybe<String_Filter_Operators>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	processed_artwork_id?: InputMaybe<String_Filter_Operators>;
	processed_fair_exhibitor_org?: InputMaybe<Processed_Organisation_Filter>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Visit_Artwork_Mutated = {
	__typename?: 'Visit_Artwork_mutated';
	data?: Maybe<Visit_Artwork>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Artwork_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Artwork_Filter>>>;
	_none?: InputMaybe<Visit_Artwork_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Artwork_Filter>>>;
	_some?: InputMaybe<Visit_Artwork_Filter>;
	artwork_details?: InputMaybe<Artwork_Details_Filter>;
	artwork_image?: InputMaybe<Visit_Image_Filter>;
	created_artwork_id?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	empty_artwork_image?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	images?: InputMaybe<Visit_Artwork_Files_Quantifier_Filter>;
	images_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_job?: InputMaybe<Failed_Jobs_Filter>;
	is_installation_shot?: InputMaybe<Boolean_Filter_Operators>;
	label_image?: InputMaybe<Visit_Image_Filter>;
	label_text?: InputMaybe<String_Filter_Operators>;
	processed_activity_id?: InputMaybe<Id_Filter_Operators>;
	processed_artwork_id?: InputMaybe<String_Filter_Operators>;
	processed_fair_exhibitor_org?: InputMaybe<Processed_Organisation_Filter>;
	sort?: InputMaybe<Number_Filter_Operators>;
	status?: InputMaybe<Artwork_Status_Type_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
};

export type Visit_Discard_Image = {
	__typename?: 'Visit_Discard_Image';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	/** The time that the image was discarded on the frontend by the user. */
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
	visit_image?: Maybe<Visit_Image>;
};

export type Visit_Discard_ImageUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Discard_ImageUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Discard_ImageVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Discard_ImageVisit_ImageArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Discard_Image_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Discard_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Discard_Image_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
	visit_image?: InputMaybe<Visit_Image_Filter>;
};

export type Visit_Discard_Image_Mutated = {
	__typename?: 'Visit_Discard_Image_mutated';
	data?: Maybe<Visit_Discard_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Discard_Image_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Discard_Image_Filter>>>;
	_none?: InputMaybe<Visit_Discard_Image_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Discard_Image_Filter>>>;
	_some?: InputMaybe<Visit_Discard_Image_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
	visit_image?: InputMaybe<Visit_Image_Filter>;
};

export type Visit_Image = {
	__typename?: 'Visit_Image';
	/** Should not be copied across in duplication flow. */
	crop_type?: Maybe<Scalars['String']['output']>;
	/** JSON array of the coordinates the data admin has submitted - useful to keep this + original for training purposes. { top left {x: 1, y: 2}, top right: {x: 2, y: 3} ....} }. Can be used to get rectangular crop coordinates by finding smallest rectangle within this space. Should not be copied across in duplication flow. */
	data_admin_submitted_coordinates?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	/** JSON array of the coordinates from the API - useful to keep this + original for training purposes. { top left {x: 1, y: 2}, top right: {x: 2, y: 3} ....} } API called when fair image is created */
	extracted_best_guess_coordinates_from_api?: Maybe<Scalars['JSON']['output']>;
	extracted_best_guess_coordinates_from_api_func?: Maybe<Count_Functions>;
	fair?: Maybe<Ingestion_Fair>;
	id: Scalars['ID']['output'];
	/** Other would be for things like booth images */
	image_content_type?: Maybe<Scalars['String']['output']>;
	image_taken_date?: Maybe<Scalars['Date']['output']>;
	image_taken_date_func?: Maybe<Datetime_Functions>;
	manually_added?: Maybe<Scalars['Boolean']['output']>;
	original_uncropped_image?: Maybe<Directus_Files>;
	perspective_cropped_image_with_dimensions?: Maybe<Directus_Files>;
	perspective_cropped_image_without_dimensions?: Maybe<Directus_Files>;
	photographer?: Maybe<Directus_Users>;
	/** If cropping is skipped (i.e. it is a booth photo, then this field will be true, but the data admin submitted coordinates will null) */
	processed_by_data_admin?: Maybe<Scalars['Boolean']['output']>;
	rectangular_cropped_image?: Maybe<Directus_Files>;
	source?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Visit_Image_Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
	visit_artwork?: Maybe<Visit_Artwork>;
	visit_phone_registration?: Maybe<
		Array<Maybe<Visit_Phone_Registration_Visit_Image>>
	>;
	visit_phone_registration_func?: Maybe<Count_Functions>;
};

export type Visit_ImageFairArgs = {
	filter?: InputMaybe<Ingestion_Fair_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageOriginal_Uncropped_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImagePerspective_Cropped_Image_With_DimensionsArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImagePerspective_Cropped_Image_Without_DimensionsArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImagePhotographerArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageRectangular_Cropped_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageStatusArgs = {
	filter?: InputMaybe<Visit_Image_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageVisitArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageVisit_ArtworkArgs = {
	filter?: InputMaybe<Visit_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_ImageVisit_Phone_RegistrationArgs = {
	filter?: InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Image_Status = {
	__typename?: 'Visit_Image_Status';
	key: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Visit_Image_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Image_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Image_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	sort?: InputMaybe<Number_Filter_Operators>;
};

export type Visit_Image_Status_Mutated = {
	__typename?: 'Visit_Image_Status_mutated';
	data?: Maybe<Visit_Image_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Image_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Image_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Image_Filter>>>;
	crop_type?: InputMaybe<String_Filter_Operators>;
	data_admin_submitted_coordinates?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_best_guess_coordinates_from_api?: InputMaybe<String_Filter_Operators>;
	extracted_best_guess_coordinates_from_api_func?: InputMaybe<Count_Function_Filter_Operators>;
	fair?: InputMaybe<Ingestion_Fair_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_content_type?: InputMaybe<String_Filter_Operators>;
	image_taken_date?: InputMaybe<Date_Filter_Operators>;
	image_taken_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	manually_added?: InputMaybe<Boolean_Filter_Operators>;
	original_uncropped_image?: InputMaybe<Directus_Files_Filter>;
	perspective_cropped_image_with_dimensions?: InputMaybe<Directus_Files_Filter>;
	perspective_cropped_image_without_dimensions?: InputMaybe<Directus_Files_Filter>;
	photographer?: InputMaybe<Directus_Users_Filter>;
	processed_by_data_admin?: InputMaybe<Boolean_Filter_Operators>;
	rectangular_cropped_image?: InputMaybe<Directus_Files_Filter>;
	source?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Visit_Image_Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
	visit_artwork?: InputMaybe<Visit_Artwork_Filter>;
	visit_phone_registration?: InputMaybe<Visit_Phone_Registration_Visit_Image_Quantifier_Filter>;
	visit_phone_registration_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Visit_Image_Mutated = {
	__typename?: 'Visit_Image_mutated';
	data?: Maybe<Visit_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Image_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Image_Filter>>>;
	_none?: InputMaybe<Visit_Image_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Image_Filter>>>;
	_some?: InputMaybe<Visit_Image_Filter>;
	crop_type?: InputMaybe<String_Filter_Operators>;
	data_admin_submitted_coordinates?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	extracted_best_guess_coordinates_from_api?: InputMaybe<String_Filter_Operators>;
	extracted_best_guess_coordinates_from_api_func?: InputMaybe<Count_Function_Filter_Operators>;
	fair?: InputMaybe<Ingestion_Fair_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_content_type?: InputMaybe<String_Filter_Operators>;
	image_taken_date?: InputMaybe<Date_Filter_Operators>;
	image_taken_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	manually_added?: InputMaybe<Boolean_Filter_Operators>;
	original_uncropped_image?: InputMaybe<Directus_Files_Filter>;
	perspective_cropped_image_with_dimensions?: InputMaybe<Directus_Files_Filter>;
	perspective_cropped_image_without_dimensions?: InputMaybe<Directus_Files_Filter>;
	photographer?: InputMaybe<Directus_Users_Filter>;
	processed_by_data_admin?: InputMaybe<Boolean_Filter_Operators>;
	rectangular_cropped_image?: InputMaybe<Directus_Files_Filter>;
	source?: InputMaybe<String_Filter_Operators>;
	status?: InputMaybe<Visit_Image_Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit?: InputMaybe<Visit_Filter>;
	visit_artwork?: InputMaybe<Visit_Artwork_Filter>;
	visit_phone_registration?: InputMaybe<Visit_Phone_Registration_Visit_Image_Quantifier_Filter>;
	visit_phone_registration_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Visit_Phone = {
	__typename?: 'Visit_Phone';
	authentication_status?: Maybe<Visit_Phone_Status>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	icloud_account: Scalars['String']['output'];
	id: Scalars['ID']['output'];
	/** Date the icloud sync last took place */
	last_sync_date?: Maybe<Scalars['Date']['output']>;
	last_sync_date_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Visit_PhoneAuthentication_StatusArgs = {
	filter?: InputMaybe<Visit_Phone_Status_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_PhoneUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_PhoneUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration = {
	__typename?: 'Visit_Phone_Registration';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	end_date: Scalars['Date']['output'];
	end_date_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	location: Scalars['String']['output'];
	phone?: Maybe<Visit_Phone>;
	photographer?: Maybe<Directus_Users>;
	start_date: Scalars['Date']['output'];
	start_date_func?: Maybe<Datetime_Functions>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Visit_Phone_RegistrationPhoneArgs = {
	filter?: InputMaybe<Visit_Phone_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_RegistrationPhotographerArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_RegistrationUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_RegistrationUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_Image = {
	__typename?: 'Visit_Phone_Registration_Visit_Image';
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	image_date: Scalars['Date']['output'];
	image_date_func?: Maybe<Datetime_Functions>;
	phone?: Maybe<Visit_Phone>;
	phone_registration?: Maybe<Visit_Phone_Registration>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit_image?: Maybe<Visit_Image>;
};

export type Visit_Phone_Registration_Visit_ImagePhoneArgs = {
	filter?: InputMaybe<Visit_Phone_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_ImagePhone_RegistrationArgs = {
	filter?: InputMaybe<Visit_Phone_Registration_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_ImageUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_ImageUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_ImageVisit_ImageArgs = {
	filter?: InputMaybe<Visit_Image_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Phone_Registration_Visit_Image_Filter = {
	_and?: InputMaybe<
		Array<InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>>
	>;
	_or?: InputMaybe<
		Array<InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>>
	>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_date?: InputMaybe<Date_Filter_Operators>;
	image_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	phone?: InputMaybe<Visit_Phone_Filter>;
	phone_registration?: InputMaybe<Visit_Phone_Registration_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit_image?: InputMaybe<Visit_Image_Filter>;
};

export type Visit_Phone_Registration_Visit_Image_Mutated = {
	__typename?: 'Visit_Phone_Registration_Visit_Image_mutated';
	data?: Maybe<Visit_Phone_Registration_Visit_Image>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Phone_Registration_Visit_Image_Quantifier_Filter = {
	_and?: InputMaybe<
		Array<InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>>
	>;
	_none?: InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>;
	_or?: InputMaybe<
		Array<InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>>
	>;
	_some?: InputMaybe<Visit_Phone_Registration_Visit_Image_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_date?: InputMaybe<Date_Filter_Operators>;
	image_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	phone?: InputMaybe<Visit_Phone_Filter>;
	phone_registration?: InputMaybe<Visit_Phone_Registration_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit_image?: InputMaybe<Visit_Image_Filter>;
};

export type Visit_Phone_Registration_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Phone_Registration_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Phone_Registration_Filter>>>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	end_date?: InputMaybe<Date_Filter_Operators>;
	end_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	phone?: InputMaybe<Visit_Phone_Filter>;
	photographer?: InputMaybe<Directus_Users_Filter>;
	start_date?: InputMaybe<Date_Filter_Operators>;
	start_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Visit_Phone_Registration_Mutated = {
	__typename?: 'Visit_Phone_Registration_mutated';
	data?: Maybe<Visit_Phone_Registration>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Phone_Status = {
	__typename?: 'Visit_Phone_Status';
	key: Scalars['ID']['output'];
};

export type Visit_Phone_Status_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Phone_Status_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Phone_Status_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Visit_Phone_Status_Mutated = {
	__typename?: 'Visit_Phone_Status_mutated';
	data?: Maybe<Visit_Phone_Status>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Phone_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Phone_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Phone_Filter>>>;
	authentication_status?: InputMaybe<Visit_Phone_Status_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	icloud_account?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	last_sync_date?: InputMaybe<Date_Filter_Operators>;
	last_sync_date_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Visit_Phone_Mutated = {
	__typename?: 'Visit_Phone_mutated';
	data?: Maybe<Visit_Phone>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Visit_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Visit_Filter>>>;
	artworks?: InputMaybe<Visit_Artwork_Quantifier_Filter>;
	artworks_func?: InputMaybe<Count_Function_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	discarded_visit_images?: InputMaybe<Visit_Discard_Image_Quantifier_Filter>;
	discarded_visit_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	ingestion_exhibition?: InputMaybe<Ingestion_Exhibition_Quantifier_Filter>;
	ingestion_exhibition_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_fair?: InputMaybe<Ingestion_Fair_Quantifier_Filter>;
	ingestion_fair_func?: InputMaybe<Count_Function_Filter_Operators>;
	ingestion_gallery_offering?: InputMaybe<Ingestion_Gallery_Offering_Quantifier_Filter>;
	ingestion_gallery_offering_func?: InputMaybe<Count_Function_Filter_Operators>;
	review_status?: InputMaybe<Checklist_Review_Status_Filter>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
	visit_images?: InputMaybe<Visit_Image_Quantifier_Filter>;
	visit_images_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Visit_Mutated = {
	__typename?: 'Visit_mutated';
	data?: Maybe<Visit>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Visit_Visit_Items = {
	__typename?: 'Visit_visit_items';
	Visit_id?: Maybe<Visit>;
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Visit_Visit_Items_Item_Union>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Visit_Visit_ItemsVisit_IdArgs = {
	filter?: InputMaybe<Visit_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Visit_Visit_Items_Item_Union = Visit_Artwork;

export type Visit_Visit_Items_Mutated = {
	__typename?: 'Visit_visit_items_mutated';
	data?: Maybe<Visit_Visit_Items>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Art_Event_Feed = {
	__typename?: 'art_event_feed';
	artlogic_link?: Maybe<Array<Maybe<Artlogic_Link>>>;
	artlogic_link_func?: Maybe<Count_Functions>;
	artwork_feed?: Maybe<Array<Maybe<Artwork_Feed>>>;
	artwork_feed_func?: Maybe<Count_Functions>;
	/** Kubernetes job name that created/updated the record. For historical data, migrated from app db, the value is `app-db-migration`. */
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['Date']['output']>;
	created_at_func?: Maybe<Datetime_Functions>;
	created_by?: Maybe<Directus_Users>;
	data_source: Scalars['String']['output'];
	description?: Maybe<Scalars['String']['output']>;
	ends_at?: Maybe<Scalars['Date']['output']>;
	ends_at_func?: Maybe<Datetime_Functions>;
	ends_at_tz?: Maybe<Scalars['String']['output']>;
	event_type?: Maybe<Art_Event_Type_Lookup>;
	external_id?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	image?: Maybe<Directus_Files>;
	image_url?: Maybe<Scalars['String']['output']>;
	is_charity_fundraiser?: Maybe<Scalars['Boolean']['output']>;
	is_closed?: Maybe<Scalars['Boolean']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	organization?: Maybe<Scalars['String']['output']>;
	pdf_url?: Maybe<Scalars['String']['output']>;
	processor_review_event?: Maybe<Array<Maybe<Scraped_Event>>>;
	processor_review_event_func?: Maybe<Count_Functions>;
	sale_number?: Maybe<Scalars['String']['output']>;
	starts_at?: Maybe<Scalars['Date']['output']>;
	starts_at_func?: Maybe<Datetime_Functions>;
	starts_at_tz?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['Date']['output']>;
	updated_at_func?: Maybe<Datetime_Functions>;
	updated_by?: Maybe<Directus_Users>;
	url: Scalars['String']['output'];
};

export type Art_Event_FeedArtlogic_LinkArgs = {
	filter?: InputMaybe<Artlogic_Link_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedArtwork_FeedArgs = {
	filter?: InputMaybe<Artwork_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedCreated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedEvent_TypeArgs = {
	filter?: InputMaybe<Art_Event_Type_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedProcessor_Review_EventArgs = {
	filter?: InputMaybe<Scraped_Event_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_FeedUpdated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Art_Event_Feed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Art_Event_Feed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Art_Event_Feed_Filter>>>;
	artlogic_link?: InputMaybe<Artlogic_Link_Quantifier_Filter>;
	artlogic_link_func?: InputMaybe<Count_Function_Filter_Operators>;
	artwork_feed?: InputMaybe<Artwork_Feed_Quantifier_Filter>;
	artwork_feed_func?: InputMaybe<Count_Function_Filter_Operators>;
	crawl_job?: InputMaybe<String_Filter_Operators>;
	created_at?: InputMaybe<Date_Filter_Operators>;
	created_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	created_by?: InputMaybe<Directus_Users_Filter>;
	data_source?: InputMaybe<String_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	ends_at?: InputMaybe<Date_Filter_Operators>;
	ends_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	ends_at_tz?: InputMaybe<String_Filter_Operators>;
	event_type?: InputMaybe<Art_Event_Type_Lookup_Filter>;
	external_id?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image?: InputMaybe<Directus_Files_Filter>;
	image_url?: InputMaybe<String_Filter_Operators>;
	is_charity_fundraiser?: InputMaybe<Boolean_Filter_Operators>;
	is_closed?: InputMaybe<Boolean_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	organization?: InputMaybe<String_Filter_Operators>;
	pdf_url?: InputMaybe<String_Filter_Operators>;
	processor_review_event?: InputMaybe<Scraped_Event_Quantifier_Filter>;
	processor_review_event_func?: InputMaybe<Count_Function_Filter_Operators>;
	sale_number?: InputMaybe<String_Filter_Operators>;
	starts_at?: InputMaybe<Date_Filter_Operators>;
	starts_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	starts_at_tz?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	updated_at?: InputMaybe<Date_Filter_Operators>;
	updated_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	updated_by?: InputMaybe<Directus_Users_Filter>;
	url?: InputMaybe<String_Filter_Operators>;
};

export type Art_Event_Feed_Mutated = {
	__typename?: 'art_event_feed_mutated';
	data?: Maybe<Art_Event_Feed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Art_Event_Type_Lookup = {
	__typename?: 'art_event_type_lookup';
	activity_type?: Maybe<Scalars['String']['output']>;
	auction_type?: Maybe<Scalars['String']['output']>;
	ignore_for_ingestion?: Maybe<Scalars['Boolean']['output']>;
	key: Scalars['ID']['output'];
	listing_type?: Maybe<Scalars['String']['output']>;
	/** Determines the "type" of artwork/sale displayed in the arteye processor app. */
	processor_type: Scalars['String']['output'];
};

export type Art_Event_Type_Lookup_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Art_Event_Type_Lookup_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Art_Event_Type_Lookup_Filter>>>;
	activity_type?: InputMaybe<String_Filter_Operators>;
	auction_type?: InputMaybe<String_Filter_Operators>;
	ignore_for_ingestion?: InputMaybe<Boolean_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	listing_type?: InputMaybe<String_Filter_Operators>;
	processor_type?: InputMaybe<String_Filter_Operators>;
};

export type Art_Event_Type_Lookup_Mutated = {
	__typename?: 'art_event_type_lookup_mutated';
	data?: Maybe<Art_Event_Type_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Artwork_Lot_Symbol_Link = {
	__typename?: 'artwork_artwork_lot_symbol_link';
	artwork_id?: Maybe<Artwork_Feed>;
	id: Scalars['ID']['output'];
	lot_symbol?: Maybe<Artwork_Lot_Symbol_Lookup>;
};

export type Artwork_Artwork_Lot_Symbol_LinkArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Artwork_Lot_Symbol_LinkLot_SymbolArgs = {
	filter?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Artwork_Lot_Symbol_Link_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Feed_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	lot_symbol?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
};

export type Artwork_Artwork_Lot_Symbol_Link_Mutated = {
	__typename?: 'artwork_artwork_lot_symbol_link_mutated';
	data?: Maybe<Artwork_Artwork_Lot_Symbol_Link>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Artwork_Lot_Symbol_Link_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>>>;
	_none?: InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>>>;
	_some?: InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>;
	artwork_id?: InputMaybe<Artwork_Feed_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
	lot_symbol?: InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>;
};

export type Artwork_Associated_Entity_Link = {
	__typename?: 'artwork_associated_entity_link';
	artwork_id?: Maybe<Artwork_Feed>;
	associated_entity_id?: Maybe<Associated_Entity_Feed>;
	association_type?: Maybe<Association_Type_Lookup>;
	id: Scalars['ID']['output'];
};

export type Artwork_Associated_Entity_LinkArtwork_IdArgs = {
	filter?: InputMaybe<Artwork_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Associated_Entity_LinkAssociated_Entity_IdArgs = {
	filter?: InputMaybe<Associated_Entity_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Associated_Entity_LinkAssociation_TypeArgs = {
	filter?: InputMaybe<Association_Type_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Associated_Entity_Link_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Associated_Entity_Link_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Associated_Entity_Link_Filter>>>;
	artwork_id?: InputMaybe<Artwork_Feed_Filter>;
	associated_entity_id?: InputMaybe<Associated_Entity_Feed_Filter>;
	association_type?: InputMaybe<Association_Type_Lookup_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
};

export type Artwork_Associated_Entity_Link_Mutated = {
	__typename?: 'artwork_associated_entity_link_mutated';
	data?: Maybe<Artwork_Associated_Entity_Link>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Associated_Entity_Link_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Associated_Entity_Link_Filter>>>;
	_none?: InputMaybe<Artwork_Associated_Entity_Link_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Associated_Entity_Link_Filter>>>;
	_some?: InputMaybe<Artwork_Associated_Entity_Link_Filter>;
	artwork_id?: InputMaybe<Artwork_Feed_Filter>;
	associated_entity_id?: InputMaybe<Associated_Entity_Feed_Filter>;
	association_type?: InputMaybe<Association_Type_Lookup_Filter>;
	id?: InputMaybe<Id_Filter_Operators>;
};

export type Artwork_Feed = {
	__typename?: 'artwork_feed';
	archived: Scalars['Boolean']['output'];
	artist?: Maybe<Scalars['String']['output']>;
	artist_clean_name?: Maybe<Scalars['String']['output']>;
	artist_nationality?: Maybe<Country>;
	artist_year_born?: Maybe<Scalars['Int']['output']>;
	artist_year_died?: Maybe<Scalars['Int']['output']>;
	artwork_type?: Maybe<Artwork_Type_Lookup>;
	associated_entities?: Maybe<Array<Maybe<Artwork_Associated_Entity_Link>>>;
	associated_entities_func?: Maybe<Count_Functions>;
	/** This is for sales lines that are extracted from the provenance field. This would be defined as { prov_lines: Array<{ line: String; lot: number; saledate: String }> }. */
	auction_records?: Maybe<Scalars['JSON']['output']>;
	auction_records_func?: Maybe<Count_Functions>;
	cr_number?: Maybe<Scalars['String']['output']>;
	/** Kubernetes job name that created/updated the record. For historical data, migrated from app db, the value is `app-db-migration`. */
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['Date']['output']>;
	created_at_func?: Maybe<Datetime_Functions>;
	created_by?: Maybe<Directus_Users>;
	data_source?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	dimensions?: Maybe<Scalars['String']['output']>;
	dimensions_depth?: Maybe<Scalars['Float']['output']>;
	dimensions_height?: Maybe<Scalars['Float']['output']>;
	dimensions_uom?: Maybe<Uom>;
	dimensions_width?: Maybe<Scalars['Float']['output']>;
	edition?: Maybe<Scalars['String']['output']>;
	edition_ap_size?: Maybe<Scalars['Int']['output']>;
	edition_hc_size?: Maybe<Scalars['Int']['output']>;
	edition_is_numbered?: Maybe<Scalars['Boolean']['output']>;
	edition_is_unlimited?: Maybe<Scalars['Boolean']['output']>;
	edition_number?: Maybe<Scalars['String']['output']>;
	edition_size?: Maybe<Scalars['Int']['output']>;
	edition_size_reg?: Maybe<Scalars['Int']['output']>;
	edition_size_unknown?: Maybe<Scalars['Boolean']['output']>;
	edition_sqn?: Maybe<Scalars['Int']['output']>;
	estimate?: Maybe<Scalars['String']['output']>;
	estimate_currency?: Maybe<Currency>;
	estimate_high?: Maybe<Scalars['Float']['output']>;
	estimate_low?: Maybe<Scalars['Float']['output']>;
	event_external_id?: Maybe<Scalars['String']['output']>;
	event_id?: Maybe<Art_Event_Feed>;
	event_session_sqn?: Maybe<Scalars['Int']['output']>;
	exhibited?: Maybe<Scalars['String']['output']>;
	external_id?: Maybe<Scalars['String']['output']>;
	extra_image_urls?: Maybe<Scalars['JSON']['output']>;
	extra_image_urls_func?: Maybe<Count_Functions>;
	extra_images?: Maybe<Array<Maybe<Artwork_Feed_Files>>>;
	extra_images_func?: Maybe<Count_Functions>;
	/** Has this item been processed before in the previous system. */
	historically_processed?: Maybe<Scalars['Boolean']['output']>;
	id: Scalars['ID']['output'];
	image_url?: Maybe<Scalars['String']['output']>;
	inscription_date?: Maybe<Scalars['String']['output']>;
	inscription_position?: Maybe<Scalars['String']['output']>;
	inscriptions?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	lot_condition?: Maybe<Scalars['String']['output']>;
	lot_description?: Maybe<Scalars['String']['output']>;
	lot_lead?: Maybe<Scalars['String']['output']>;
	lot_number?: Maybe<Scalars['String']['output']>;
	lot_shipping?: Maybe<Scalars['String']['output']>;
	lot_symbols?: Maybe<Array<Maybe<Artwork_Artwork_Lot_Symbol_Link>>>;
	lot_symbols_func?: Maybe<Count_Functions>;
	lot_title?: Maybe<Scalars['String']['output']>;
	mediums?: Maybe<Scalars['String']['output']>;
	price?: Maybe<Scalars['String']['output']>;
	price_amount?: Maybe<Scalars['Float']['output']>;
	price_currency?: Maybe<Currency>;
	price_includes_bp?: Maybe<Scalars['Boolean']['output']>;
	primary_image?: Maybe<Directus_Files>;
	processor_review_artwork?: Maybe<Array<Maybe<Scraped_Artwork>>>;
	processor_review_artwork_func?: Maybe<Count_Functions>;
	provenance?: Maybe<Scalars['String']['output']>;
	sale_ends_at?: Maybe<Scalars['Date']['output']>;
	sale_ends_at_func?: Maybe<Datetime_Functions>;
	sale_ends_at_tz?: Maybe<Scalars['String']['output']>;
	sale_is_closed?: Maybe<Scalars['Boolean']['output']>;
	sale_starts_at?: Maybe<Scalars['Date']['output']>;
	sale_starts_at_func?: Maybe<Datetime_Functions>;
	sale_starts_at_tz?: Maybe<Scalars['String']['output']>;
	sale_status?: Maybe<Artwork_Sale_Status_Lookup>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['Date']['output']>;
	updated_at_func?: Maybe<Datetime_Functions>;
	updated_by?: Maybe<Directus_Users>;
	url: Scalars['String']['output'];
	year_made?: Maybe<Scalars['String']['output']>;
	year_made_from?: Maybe<Scalars['Int']['output']>;
	year_made_to?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_FeedArtist_NationalityArgs = {
	filter?: InputMaybe<Country_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedArtwork_TypeArgs = {
	filter?: InputMaybe<Artwork_Type_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedAssociated_EntitiesArgs = {
	filter?: InputMaybe<Artwork_Associated_Entity_Link_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedCreated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedDimensions_UomArgs = {
	filter?: InputMaybe<Uom_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedEstimate_CurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedEvent_IdArgs = {
	filter?: InputMaybe<Art_Event_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedExtra_ImagesArgs = {
	filter?: InputMaybe<Artwork_Feed_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedLot_SymbolsArgs = {
	filter?: InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedPrice_CurrencyArgs = {
	filter?: InputMaybe<Currency_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedPrimary_ImageArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedProcessor_Review_ArtworkArgs = {
	filter?: InputMaybe<Scraped_Artwork_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedSale_StatusArgs = {
	filter?: InputMaybe<Artwork_Sale_Status_Lookup_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_FeedUpdated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Feed_Files = {
	__typename?: 'artwork_feed_files';
	artwork_feed_id?: Maybe<Artwork_Feed>;
	directus_files_id?: Maybe<Directus_Files>;
	id: Scalars['ID']['output'];
};

export type Artwork_Feed_FilesArtwork_Feed_IdArgs = {
	filter?: InputMaybe<Artwork_Feed_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Feed_FilesDirectus_Files_IdArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Artwork_Feed_Files_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Feed_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Feed_Files_Filter>>>;
	artwork_feed_id?: InputMaybe<Artwork_Feed_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Feed_Files_Mutated = {
	__typename?: 'artwork_feed_files_mutated';
	data?: Maybe<Artwork_Feed_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Feed_Files_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Feed_Files_Filter>>>;
	_none?: InputMaybe<Artwork_Feed_Files_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Feed_Files_Filter>>>;
	_some?: InputMaybe<Artwork_Feed_Files_Filter>;
	artwork_feed_id?: InputMaybe<Artwork_Feed_Filter>;
	directus_files_id?: InputMaybe<Directus_Files_Filter>;
	id?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Feed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Feed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Feed_Filter>>>;
	archived?: InputMaybe<Boolean_Filter_Operators>;
	artist?: InputMaybe<String_Filter_Operators>;
	artist_clean_name?: InputMaybe<String_Filter_Operators>;
	artist_nationality?: InputMaybe<Country_Filter>;
	artist_year_born?: InputMaybe<Number_Filter_Operators>;
	artist_year_died?: InputMaybe<Number_Filter_Operators>;
	artwork_type?: InputMaybe<Artwork_Type_Lookup_Filter>;
	associated_entities?: InputMaybe<Artwork_Associated_Entity_Link_Quantifier_Filter>;
	associated_entities_func?: InputMaybe<Count_Function_Filter_Operators>;
	auction_records?: InputMaybe<String_Filter_Operators>;
	auction_records_func?: InputMaybe<Count_Function_Filter_Operators>;
	cr_number?: InputMaybe<String_Filter_Operators>;
	crawl_job?: InputMaybe<String_Filter_Operators>;
	created_at?: InputMaybe<Date_Filter_Operators>;
	created_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	created_by?: InputMaybe<Directus_Users_Filter>;
	data_source?: InputMaybe<String_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	dimensions?: InputMaybe<String_Filter_Operators>;
	dimensions_depth?: InputMaybe<Number_Filter_Operators>;
	dimensions_height?: InputMaybe<Number_Filter_Operators>;
	dimensions_uom?: InputMaybe<Uom_Filter>;
	dimensions_width?: InputMaybe<Number_Filter_Operators>;
	edition?: InputMaybe<String_Filter_Operators>;
	edition_ap_size?: InputMaybe<Number_Filter_Operators>;
	edition_hc_size?: InputMaybe<Number_Filter_Operators>;
	edition_is_numbered?: InputMaybe<Boolean_Filter_Operators>;
	edition_is_unlimited?: InputMaybe<Boolean_Filter_Operators>;
	edition_number?: InputMaybe<String_Filter_Operators>;
	edition_size?: InputMaybe<Number_Filter_Operators>;
	edition_size_reg?: InputMaybe<Number_Filter_Operators>;
	edition_size_unknown?: InputMaybe<Boolean_Filter_Operators>;
	edition_sqn?: InputMaybe<Number_Filter_Operators>;
	estimate?: InputMaybe<String_Filter_Operators>;
	estimate_currency?: InputMaybe<Currency_Filter>;
	estimate_high?: InputMaybe<Number_Filter_Operators>;
	estimate_low?: InputMaybe<Number_Filter_Operators>;
	event_external_id?: InputMaybe<String_Filter_Operators>;
	event_id?: InputMaybe<Art_Event_Feed_Filter>;
	event_session_sqn?: InputMaybe<Number_Filter_Operators>;
	exhibited?: InputMaybe<String_Filter_Operators>;
	external_id?: InputMaybe<String_Filter_Operators>;
	extra_image_urls?: InputMaybe<String_Filter_Operators>;
	extra_image_urls_func?: InputMaybe<Count_Function_Filter_Operators>;
	extra_images?: InputMaybe<Artwork_Feed_Files_Quantifier_Filter>;
	extra_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	historically_processed?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_url?: InputMaybe<String_Filter_Operators>;
	inscription_date?: InputMaybe<String_Filter_Operators>;
	inscription_position?: InputMaybe<String_Filter_Operators>;
	inscriptions?: InputMaybe<String_Filter_Operators>;
	literature?: InputMaybe<String_Filter_Operators>;
	lot_condition?: InputMaybe<String_Filter_Operators>;
	lot_description?: InputMaybe<String_Filter_Operators>;
	lot_lead?: InputMaybe<String_Filter_Operators>;
	lot_number?: InputMaybe<String_Filter_Operators>;
	lot_shipping?: InputMaybe<String_Filter_Operators>;
	lot_symbols?: InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Quantifier_Filter>;
	lot_symbols_func?: InputMaybe<Count_Function_Filter_Operators>;
	lot_title?: InputMaybe<String_Filter_Operators>;
	mediums?: InputMaybe<String_Filter_Operators>;
	price?: InputMaybe<String_Filter_Operators>;
	price_amount?: InputMaybe<Number_Filter_Operators>;
	price_currency?: InputMaybe<Currency_Filter>;
	price_includes_bp?: InputMaybe<Boolean_Filter_Operators>;
	primary_image?: InputMaybe<Directus_Files_Filter>;
	processor_review_artwork?: InputMaybe<Scraped_Artwork_Quantifier_Filter>;
	processor_review_artwork_func?: InputMaybe<Count_Function_Filter_Operators>;
	provenance?: InputMaybe<String_Filter_Operators>;
	sale_ends_at?: InputMaybe<Date_Filter_Operators>;
	sale_ends_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	sale_ends_at_tz?: InputMaybe<String_Filter_Operators>;
	sale_is_closed?: InputMaybe<Boolean_Filter_Operators>;
	sale_starts_at?: InputMaybe<Date_Filter_Operators>;
	sale_starts_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	sale_starts_at_tz?: InputMaybe<String_Filter_Operators>;
	sale_status?: InputMaybe<Artwork_Sale_Status_Lookup_Filter>;
	saleroom_notice?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	updated_at?: InputMaybe<Date_Filter_Operators>;
	updated_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	updated_by?: InputMaybe<Directus_Users_Filter>;
	url?: InputMaybe<String_Filter_Operators>;
	year_made?: InputMaybe<String_Filter_Operators>;
	year_made_from?: InputMaybe<Number_Filter_Operators>;
	year_made_to?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Feed_Mutated = {
	__typename?: 'artwork_feed_mutated';
	data?: Maybe<Artwork_Feed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Feed_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Feed_Filter>>>;
	_none?: InputMaybe<Artwork_Feed_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Feed_Filter>>>;
	_some?: InputMaybe<Artwork_Feed_Filter>;
	archived?: InputMaybe<Boolean_Filter_Operators>;
	artist?: InputMaybe<String_Filter_Operators>;
	artist_clean_name?: InputMaybe<String_Filter_Operators>;
	artist_nationality?: InputMaybe<Country_Filter>;
	artist_year_born?: InputMaybe<Number_Filter_Operators>;
	artist_year_died?: InputMaybe<Number_Filter_Operators>;
	artwork_type?: InputMaybe<Artwork_Type_Lookup_Filter>;
	associated_entities?: InputMaybe<Artwork_Associated_Entity_Link_Quantifier_Filter>;
	associated_entities_func?: InputMaybe<Count_Function_Filter_Operators>;
	auction_records?: InputMaybe<String_Filter_Operators>;
	auction_records_func?: InputMaybe<Count_Function_Filter_Operators>;
	cr_number?: InputMaybe<String_Filter_Operators>;
	crawl_job?: InputMaybe<String_Filter_Operators>;
	created_at?: InputMaybe<Date_Filter_Operators>;
	created_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	created_by?: InputMaybe<Directus_Users_Filter>;
	data_source?: InputMaybe<String_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	dimensions?: InputMaybe<String_Filter_Operators>;
	dimensions_depth?: InputMaybe<Number_Filter_Operators>;
	dimensions_height?: InputMaybe<Number_Filter_Operators>;
	dimensions_uom?: InputMaybe<Uom_Filter>;
	dimensions_width?: InputMaybe<Number_Filter_Operators>;
	edition?: InputMaybe<String_Filter_Operators>;
	edition_ap_size?: InputMaybe<Number_Filter_Operators>;
	edition_hc_size?: InputMaybe<Number_Filter_Operators>;
	edition_is_numbered?: InputMaybe<Boolean_Filter_Operators>;
	edition_is_unlimited?: InputMaybe<Boolean_Filter_Operators>;
	edition_number?: InputMaybe<String_Filter_Operators>;
	edition_size?: InputMaybe<Number_Filter_Operators>;
	edition_size_reg?: InputMaybe<Number_Filter_Operators>;
	edition_size_unknown?: InputMaybe<Boolean_Filter_Operators>;
	edition_sqn?: InputMaybe<Number_Filter_Operators>;
	estimate?: InputMaybe<String_Filter_Operators>;
	estimate_currency?: InputMaybe<Currency_Filter>;
	estimate_high?: InputMaybe<Number_Filter_Operators>;
	estimate_low?: InputMaybe<Number_Filter_Operators>;
	event_external_id?: InputMaybe<String_Filter_Operators>;
	event_id?: InputMaybe<Art_Event_Feed_Filter>;
	event_session_sqn?: InputMaybe<Number_Filter_Operators>;
	exhibited?: InputMaybe<String_Filter_Operators>;
	external_id?: InputMaybe<String_Filter_Operators>;
	extra_image_urls?: InputMaybe<String_Filter_Operators>;
	extra_image_urls_func?: InputMaybe<Count_Function_Filter_Operators>;
	extra_images?: InputMaybe<Artwork_Feed_Files_Quantifier_Filter>;
	extra_images_func?: InputMaybe<Count_Function_Filter_Operators>;
	historically_processed?: InputMaybe<Boolean_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	image_url?: InputMaybe<String_Filter_Operators>;
	inscription_date?: InputMaybe<String_Filter_Operators>;
	inscription_position?: InputMaybe<String_Filter_Operators>;
	inscriptions?: InputMaybe<String_Filter_Operators>;
	literature?: InputMaybe<String_Filter_Operators>;
	lot_condition?: InputMaybe<String_Filter_Operators>;
	lot_description?: InputMaybe<String_Filter_Operators>;
	lot_lead?: InputMaybe<String_Filter_Operators>;
	lot_number?: InputMaybe<String_Filter_Operators>;
	lot_shipping?: InputMaybe<String_Filter_Operators>;
	lot_symbols?: InputMaybe<Artwork_Artwork_Lot_Symbol_Link_Quantifier_Filter>;
	lot_symbols_func?: InputMaybe<Count_Function_Filter_Operators>;
	lot_title?: InputMaybe<String_Filter_Operators>;
	mediums?: InputMaybe<String_Filter_Operators>;
	price?: InputMaybe<String_Filter_Operators>;
	price_amount?: InputMaybe<Number_Filter_Operators>;
	price_currency?: InputMaybe<Currency_Filter>;
	price_includes_bp?: InputMaybe<Boolean_Filter_Operators>;
	primary_image?: InputMaybe<Directus_Files_Filter>;
	processor_review_artwork?: InputMaybe<Scraped_Artwork_Quantifier_Filter>;
	processor_review_artwork_func?: InputMaybe<Count_Function_Filter_Operators>;
	provenance?: InputMaybe<String_Filter_Operators>;
	sale_ends_at?: InputMaybe<Date_Filter_Operators>;
	sale_ends_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	sale_ends_at_tz?: InputMaybe<String_Filter_Operators>;
	sale_is_closed?: InputMaybe<Boolean_Filter_Operators>;
	sale_starts_at?: InputMaybe<Date_Filter_Operators>;
	sale_starts_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	sale_starts_at_tz?: InputMaybe<String_Filter_Operators>;
	sale_status?: InputMaybe<Artwork_Sale_Status_Lookup_Filter>;
	saleroom_notice?: InputMaybe<String_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	updated_at?: InputMaybe<Date_Filter_Operators>;
	updated_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	updated_by?: InputMaybe<Directus_Users_Filter>;
	url?: InputMaybe<String_Filter_Operators>;
	year_made?: InputMaybe<String_Filter_Operators>;
	year_made_from?: InputMaybe<Number_Filter_Operators>;
	year_made_to?: InputMaybe<Number_Filter_Operators>;
};

export type Artwork_Feed_Revisions = {
	__typename?: 'artwork_feed_revisions';
	added: Scalars['JSON']['output'];
	added_func?: Maybe<Count_Functions>;
	changed: Scalars['JSON']['output'];
	changed_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	item_id: Scalars['ID']['output'];
	new_data: Scalars['JSON']['output'];
	new_data_func?: Maybe<Count_Functions>;
	old_data: Scalars['JSON']['output'];
	old_data_func?: Maybe<Count_Functions>;
	operation: Scalars['String']['output'];
	removed: Scalars['JSON']['output'];
	removed_func?: Maybe<Count_Functions>;
	revision_time?: Maybe<Scalars['Date']['output']>;
	revision_time_func?: Maybe<Datetime_Functions>;
	table_name: Scalars['String']['output'];
};

export type Artwork_Feed_Revisions_Mutated = {
	__typename?: 'artwork_feed_revisions_mutated';
	data?: Maybe<Artwork_Feed_Revisions>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Lot_Symbol_Lookup = {
	__typename?: 'artwork_lot_symbol_lookup';
	arteye_attribute_type?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	display_name?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
};

export type Artwork_Lot_Symbol_Lookup_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Lot_Symbol_Lookup_Filter>>>;
	arteye_attribute_type?: InputMaybe<String_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	display_name?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Lot_Symbol_Lookup_Mutated = {
	__typename?: 'artwork_lot_symbol_lookup_mutated';
	data?: Maybe<Artwork_Lot_Symbol_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Sale_Status_Lookup = {
	__typename?: 'artwork_sale_status_lookup';
	arteye_status?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
};

export type Artwork_Sale_Status_Lookup_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Sale_Status_Lookup_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Sale_Status_Lookup_Filter>>>;
	arteye_status?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Sale_Status_Lookup_Mutated = {
	__typename?: 'artwork_sale_status_lookup_mutated';
	data?: Maybe<Artwork_Sale_Status_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Artwork_Type_Lookup = {
	__typename?: 'artwork_type_lookup';
	arteye_type?: Maybe<Scalars['String']['output']>;
	key: Scalars['ID']['output'];
};

export type Artwork_Type_Lookup_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Artwork_Type_Lookup_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Artwork_Type_Lookup_Filter>>>;
	arteye_type?: InputMaybe<String_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Artwork_Type_Lookup_Mutated = {
	__typename?: 'artwork_type_lookup_mutated';
	data?: Maybe<Artwork_Type_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Associated_Entity_Feed = {
	__typename?: 'associated_entity_feed';
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['Date']['output']>;
	created_at_func?: Maybe<Datetime_Functions>;
	created_by?: Maybe<Directus_Users>;
	description?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	scraped_entity?: Maybe<Array<Maybe<Scraped_Entity>>>;
	scraped_entity_func?: Maybe<Count_Functions>;
	updated_at?: Maybe<Scalars['Date']['output']>;
	updated_at_func?: Maybe<Datetime_Functions>;
	updated_by?: Maybe<Directus_Users>;
	url: Scalars['String']['output'];
};

export type Associated_Entity_FeedCreated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Associated_Entity_FeedScraped_EntityArgs = {
	filter?: InputMaybe<Scraped_Entity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Associated_Entity_FeedUpdated_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Associated_Entity_Feed_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Associated_Entity_Feed_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Associated_Entity_Feed_Filter>>>;
	crawl_job?: InputMaybe<String_Filter_Operators>;
	created_at?: InputMaybe<Date_Filter_Operators>;
	created_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	created_by?: InputMaybe<Directus_Users_Filter>;
	description?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	scraped_entity?: InputMaybe<Scraped_Entity_Quantifier_Filter>;
	scraped_entity_func?: InputMaybe<Count_Function_Filter_Operators>;
	updated_at?: InputMaybe<Date_Filter_Operators>;
	updated_at_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	updated_by?: InputMaybe<Directus_Users_Filter>;
	url?: InputMaybe<String_Filter_Operators>;
};

export type Associated_Entity_Feed_Mutated = {
	__typename?: 'associated_entity_feed_mutated';
	data?: Maybe<Associated_Entity_Feed>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Association_Type_Lookup = {
	__typename?: 'association_type_lookup';
	key: Scalars['ID']['output'];
};

export type Association_Type_Lookup_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Association_Type_Lookup_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Association_Type_Lookup_Filter>>>;
	key?: InputMaybe<String_Filter_Operators>;
};

export type Association_Type_Lookup_Mutated = {
	__typename?: 'association_type_lookup_mutated';
	data?: Maybe<Association_Type_Lookup>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export enum Auth_Mode {
	Cookie = 'cookie',
	Json = 'json',
	Session = 'session',
}

export type Auth_Tokens = {
	__typename?: 'auth_tokens';
	access_token?: Maybe<Scalars['String']['output']>;
	expires?: Maybe<Scalars['GraphQLBigInt']['output']>;
	refresh_token?: Maybe<Scalars['String']['output']>;
};

export type Big_Int_Filter_Operators = {
	_between?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_eq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nbetween?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_neq?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['GraphQLBigInt']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Boolean_Filter_Operators = {
	_eq?: InputMaybe<Scalars['Boolean']['input']>;
	_neq?: InputMaybe<Scalars['Boolean']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Count_Function_Filter_Operators = {
	count?: InputMaybe<Number_Filter_Operators>;
};

export type Count_Functions = {
	__typename?: 'count_functions';
	count?: Maybe<Scalars['Int']['output']>;
};

export type Country = {
	__typename?: 'country';
	code: Scalars['ID']['output'];
	country_nationality?: Maybe<Scalars['String']['output']>;
	name: Scalars['String']['output'];
	nationality?: Maybe<Scalars['String']['output']>;
};

export type Country_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Country_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Country_Filter>>>;
	code?: InputMaybe<String_Filter_Operators>;
	country_nationality?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	nationality?: InputMaybe<String_Filter_Operators>;
};

export type Country_Mutated = {
	__typename?: 'country_mutated';
	data?: Maybe<Country>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Create_Directus_Comments_Input = {
	collection: Scalars['String']['input'];
	comment: Scalars['String']['input'];
	date_created?: InputMaybe<Scalars['Date']['input']>;
	date_updated?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item: Scalars['String']['input'];
	user_created?: InputMaybe<Scalars['ID']['input']>;
	user_updated?: InputMaybe<Scalars['ID']['input']>;
};

export type Create_Directus_Dashboards_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name: Scalars['String']['input'];
	note?: InputMaybe<Scalars['String']['input']>;
	panels?: InputMaybe<Array<InputMaybe<Create_Directus_Panels_Input>>>;
	user_created?: InputMaybe<Scalars['ID']['input']>;
};

export type Create_Directus_Files_Input = {
	charset?: InputMaybe<Scalars['String']['input']>;
	created_on?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	duration?: InputMaybe<Scalars['Int']['input']>;
	embed?: InputMaybe<Scalars['String']['input']>;
	filename_disk?: InputMaybe<Scalars['String']['input']>;
	filename_download: Scalars['String']['input'];
	filesize?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	focal_point_x?: InputMaybe<Scalars['Int']['input']>;
	focal_point_y?: InputMaybe<Scalars['Int']['input']>;
	folder?: InputMaybe<Create_Directus_Folders_Input>;
	height?: InputMaybe<Scalars['Int']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	metadata?: InputMaybe<Scalars['JSON']['input']>;
	modified_by?: InputMaybe<Scalars['ID']['input']>;
	modified_on?: InputMaybe<Scalars['Date']['input']>;
	storage: Scalars['String']['input'];
	tags?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	tus_data?: InputMaybe<Scalars['JSON']['input']>;
	tus_id?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	uploaded_by?: InputMaybe<Scalars['ID']['input']>;
	uploaded_on?: InputMaybe<Scalars['Date']['input']>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Create_Directus_Folders_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	name: Scalars['String']['input'];
	parent?: InputMaybe<Create_Directus_Folders_Input>;
};

export type Create_Directus_Panels_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	dashboard?: InputMaybe<Create_Directus_Dashboards_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	height: Scalars['Int']['input'];
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x: Scalars['Int']['input'];
	position_y: Scalars['Int']['input'];
	show_header: Scalars['Boolean']['input'];
	type: Scalars['String']['input'];
	user_created?: InputMaybe<Scalars['ID']['input']>;
	width: Scalars['Int']['input'];
};

export type Create_Directus_Presets_Input = {
	bookmark?: InputMaybe<Scalars['String']['input']>;
	collection?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	filter?: InputMaybe<Scalars['JSON']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	layout?: InputMaybe<Scalars['String']['input']>;
	layout_options?: InputMaybe<Scalars['JSON']['input']>;
	layout_query?: InputMaybe<Scalars['JSON']['input']>;
	refresh_interval?: InputMaybe<Scalars['Int']['input']>;
	role?: InputMaybe<Scalars['ID']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	user?: InputMaybe<Scalars['ID']['input']>;
};

export type Create_Directus_Shares_Input = {
	collection: Scalars['String']['input'];
	date_created?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item: Scalars['String']['input'];
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: InputMaybe<Scalars['Int']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: InputMaybe<Scalars['Hash']['input']>;
	role?: InputMaybe<Scalars['ID']['input']>;
	times_used?: InputMaybe<Scalars['Int']['input']>;
	user_created?: InputMaybe<Scalars['ID']['input']>;
};

export type Currency = {
	__typename?: 'currency';
	code: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	symbol?: Maybe<Scalars['String']['output']>;
};

export type Currency_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Currency_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Currency_Filter>>>;
	code?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	symbol?: InputMaybe<String_Filter_Operators>;
};

export type Currency_Mutated = {
	__typename?: 'currency_mutated';
	data?: Maybe<Currency>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Date_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_gt?: InputMaybe<Scalars['String']['input']>;
	_gte?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_lt?: InputMaybe<Scalars['String']['input']>;
	_lte?: InputMaybe<Scalars['String']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Datetime_Function_Filter_Operators = {
	day?: InputMaybe<Number_Filter_Operators>;
	hour?: InputMaybe<Number_Filter_Operators>;
	minute?: InputMaybe<Number_Filter_Operators>;
	month?: InputMaybe<Number_Filter_Operators>;
	second?: InputMaybe<Number_Filter_Operators>;
	week?: InputMaybe<Number_Filter_Operators>;
	weekday?: InputMaybe<Number_Filter_Operators>;
	year?: InputMaybe<Number_Filter_Operators>;
};

export type Datetime_Functions = {
	__typename?: 'datetime_functions';
	day?: Maybe<Scalars['Int']['output']>;
	hour?: Maybe<Scalars['Int']['output']>;
	minute?: Maybe<Scalars['Int']['output']>;
	month?: Maybe<Scalars['Int']['output']>;
	second?: Maybe<Scalars['Int']['output']>;
	week?: Maybe<Scalars['Int']['output']>;
	weekday?: Maybe<Scalars['Int']['output']>;
	year?: Maybe<Scalars['Int']['output']>;
};

export type Delete_Many = {
	__typename?: 'delete_many';
	ids: Array<Maybe<Scalars['ID']['output']>>;
};

export type Delete_One = {
	__typename?: 'delete_one';
	id: Scalars['ID']['output'];
};

export type Directus_Activity = {
	__typename?: 'directus_activity';
	action: Scalars['String']['output'];
	collection: Scalars['String']['output'];
	id: Scalars['ID']['output'];
	ip?: Maybe<Scalars['String']['output']>;
	item: Scalars['String']['output'];
	origin?: Maybe<Scalars['String']['output']>;
	revisions?: Maybe<Array<Maybe<Directus_Revisions>>>;
	revisions_func?: Maybe<Count_Functions>;
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
	user?: Maybe<Directus_Users>;
	user_agent?: Maybe<Scalars['String']['output']>;
};

export type Directus_ActivityRevisionsArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_ActivityUserArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Activity_Aggregated = {
	__typename?: 'directus_activity_aggregated';
	avg?: Maybe<Directus_Activity_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Activity_Aggregated_Fields>;
	count?: Maybe<Directus_Activity_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Activity_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Activity_Aggregated_Fields>;
	min?: Maybe<Directus_Activity_Aggregated_Fields>;
	sum?: Maybe<Directus_Activity_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Activity_Aggregated_Fields>;
};

export type Directus_Activity_Aggregated_Count = {
	__typename?: 'directus_activity_aggregated_count';
	action?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	ip?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	origin?: Maybe<Scalars['Int']['output']>;
	revisions?: Maybe<Scalars['Int']['output']>;
	timestamp?: Maybe<Scalars['Int']['output']>;
	user?: Maybe<Scalars['Int']['output']>;
	user_agent?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Activity_Aggregated_Fields = {
	__typename?: 'directus_activity_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Activity_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Activity_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Activity_Filter>>>;
	action?: InputMaybe<String_Filter_Operators>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	ip?: InputMaybe<String_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	origin?: InputMaybe<String_Filter_Operators>;
	revisions?: InputMaybe<Directus_Revisions_Quantifier_Filter>;
	revisions_func?: InputMaybe<Count_Function_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	user?: InputMaybe<Directus_Users_Filter>;
	user_agent?: InputMaybe<String_Filter_Operators>;
};

export type Directus_Activity_Mutated = {
	__typename?: 'directus_activity_mutated';
	data?: Maybe<Directus_Activity>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Collections = {
	__typename?: 'directus_collections';
	collection?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Collections_Meta>;
	schema?: Maybe<Directus_Collections_Schema>;
};

export type Directus_Collections_Meta = {
	__typename?: 'directus_collections_meta';
	accountability?: Maybe<Scalars['String']['output']>;
	archive_app_filter: Scalars['Boolean']['output'];
	archive_field?: Maybe<Scalars['String']['output']>;
	archive_value?: Maybe<Scalars['String']['output']>;
	collapse: Scalars['String']['output'];
	collection: Scalars['String']['output'];
	color?: Maybe<Scalars['String']['output']>;
	display_template?: Maybe<Scalars['String']['output']>;
	group?: Maybe<Scalars['String']['output']>;
	hidden: Scalars['Boolean']['output'];
	icon?: Maybe<Scalars['String']['output']>;
	item_duplication_fields?: Maybe<Scalars['JSON']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	preview_url?: Maybe<Scalars['String']['output']>;
	singleton: Scalars['Boolean']['output'];
	sort?: Maybe<Scalars['Int']['output']>;
	sort_field?: Maybe<Scalars['String']['output']>;
	translations?: Maybe<Scalars['JSON']['output']>;
	unarchive_value?: Maybe<Scalars['String']['output']>;
	versioning: Scalars['Boolean']['output'];
};

export type Directus_Collections_Schema = {
	__typename?: 'directus_collections_schema';
	comment?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
};

export type Directus_Comments = {
	__typename?: 'directus_comments';
	collection: Scalars['String']['output'];
	comment: Scalars['String']['output'];
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	date_updated?: Maybe<Scalars['Date']['output']>;
	date_updated_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Directus_CommentsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_CommentsUser_UpdatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Comments_Aggregated = {
	__typename?: 'directus_comments_aggregated';
	count?: Maybe<Directus_Comments_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Comments_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Comments_Aggregated_Count = {
	__typename?: 'directus_comments_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	comment?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	date_updated?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	user_updated?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Comments_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Comments_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Comments_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	comment?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_updated?: InputMaybe<Date_Filter_Operators>;
	date_updated_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	user_updated?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Comments_Mutated = {
	__typename?: 'directus_comments_mutated';
	data?: Maybe<Directus_Comments>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Dashboards = {
	__typename?: 'directus_dashboards';
	color?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	note?: Maybe<Scalars['String']['output']>;
	panels?: Maybe<Array<Maybe<Directus_Panels>>>;
	panels_func?: Maybe<Count_Functions>;
	user_created?: Maybe<Directus_Users>;
};

export type Directus_DashboardsPanelsArgs = {
	filter?: InputMaybe<Directus_Panels_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_DashboardsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Dashboards_Aggregated = {
	__typename?: 'directus_dashboards_aggregated';
	count?: Maybe<Directus_Dashboards_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Dashboards_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Dashboards_Aggregated_Count = {
	__typename?: 'directus_dashboards_aggregated_count';
	color?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	note?: Maybe<Scalars['Int']['output']>;
	panels?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Dashboards_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Dashboards_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Dashboards_Filter>>>;
	color?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	panels?: InputMaybe<Directus_Panels_Quantifier_Filter>;
	panels_func?: InputMaybe<Count_Function_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Dashboards_Mutated = {
	__typename?: 'directus_dashboards_mutated';
	data?: Maybe<Directus_Dashboards>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Fields = {
	__typename?: 'directus_fields';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Fields_Meta>;
	schema?: Maybe<Directus_Fields_Schema>;
	type?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields_Meta = {
	__typename?: 'directus_fields_meta';
	collection: Scalars['String']['output'];
	conditions?: Maybe<Scalars['JSON']['output']>;
	display?: Maybe<Scalars['String']['output']>;
	display_options?: Maybe<Scalars['JSON']['output']>;
	field: Scalars['String']['output'];
	group?: Maybe<Scalars['String']['output']>;
	hidden: Scalars['Boolean']['output'];
	id: Scalars['Int']['output'];
	interface?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	options?: Maybe<Scalars['JSON']['output']>;
	readonly: Scalars['Boolean']['output'];
	required?: Maybe<Scalars['Boolean']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	special?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	translations?: Maybe<Scalars['JSON']['output']>;
	validation?: Maybe<Scalars['JSON']['output']>;
	validation_message?: Maybe<Scalars['String']['output']>;
	width?: Maybe<Scalars['String']['output']>;
};

export type Directus_Fields_Schema = {
	__typename?: 'directus_fields_schema';
	comment?: Maybe<Scalars['String']['output']>;
	data_type?: Maybe<Scalars['String']['output']>;
	default_value?: Maybe<Scalars['String']['output']>;
	foreign_key_column?: Maybe<Scalars['String']['output']>;
	foreign_key_table?: Maybe<Scalars['String']['output']>;
	generation_expression?: Maybe<Scalars['String']['output']>;
	has_auto_increment?: Maybe<Scalars['Boolean']['output']>;
	is_generated?: Maybe<Scalars['Boolean']['output']>;
	is_indexed?: Maybe<Scalars['Boolean']['output']>;
	is_nullable?: Maybe<Scalars['Boolean']['output']>;
	is_primary_key?: Maybe<Scalars['Boolean']['output']>;
	is_unique?: Maybe<Scalars['Boolean']['output']>;
	max_length?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	numeric_precision?: Maybe<Scalars['Int']['output']>;
	numeric_scale?: Maybe<Scalars['Int']['output']>;
	table?: Maybe<Scalars['String']['output']>;
};

export type Directus_Files = {
	__typename?: 'directus_files';
	charset?: Maybe<Scalars['String']['output']>;
	created_on?: Maybe<Scalars['Date']['output']>;
	created_on_func?: Maybe<Datetime_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	duration?: Maybe<Scalars['Int']['output']>;
	embed?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	filename_download: Scalars['String']['output'];
	filesize?: Maybe<Scalars['GraphQLBigInt']['output']>;
	focal_point_x?: Maybe<Scalars['Int']['output']>;
	focal_point_y?: Maybe<Scalars['Int']['output']>;
	folder?: Maybe<Directus_Folders>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	metadata?: Maybe<Scalars['JSON']['output']>;
	metadata_func?: Maybe<Count_Functions>;
	modified_by?: Maybe<Directus_Users>;
	modified_on?: Maybe<Scalars['Date']['output']>;
	modified_on_func?: Maybe<Datetime_Functions>;
	storage: Scalars['String']['output'];
	tags?: Maybe<Scalars['JSON']['output']>;
	tags_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	tus_data?: Maybe<Scalars['JSON']['output']>;
	tus_data_func?: Maybe<Count_Functions>;
	tus_id?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	uploaded_by?: Maybe<Directus_Users>;
	uploaded_on?: Maybe<Scalars['Date']['output']>;
	uploaded_on_func?: Maybe<Datetime_Functions>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_FilesFolderArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FilesModified_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_FilesUploaded_ByArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Files_Aggregated = {
	__typename?: 'directus_files_aggregated';
	avg?: Maybe<Directus_Files_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Files_Aggregated_Fields>;
	count?: Maybe<Directus_Files_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Files_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Files_Aggregated_Fields>;
	min?: Maybe<Directus_Files_Aggregated_Fields>;
	sum?: Maybe<Directus_Files_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Files_Aggregated_Fields>;
};

export type Directus_Files_Aggregated_Count = {
	__typename?: 'directus_files_aggregated_count';
	charset?: Maybe<Scalars['Int']['output']>;
	created_on?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	duration?: Maybe<Scalars['Int']['output']>;
	embed?: Maybe<Scalars['Int']['output']>;
	filename_disk?: Maybe<Scalars['Int']['output']>;
	filename_download?: Maybe<Scalars['Int']['output']>;
	filesize?: Maybe<Scalars['Int']['output']>;
	focal_point_x?: Maybe<Scalars['Int']['output']>;
	focal_point_y?: Maybe<Scalars['Int']['output']>;
	folder?: Maybe<Scalars['Int']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	location?: Maybe<Scalars['Int']['output']>;
	metadata?: Maybe<Scalars['Int']['output']>;
	modified_by?: Maybe<Scalars['Int']['output']>;
	modified_on?: Maybe<Scalars['Int']['output']>;
	storage?: Maybe<Scalars['Int']['output']>;
	tags?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	tus_data?: Maybe<Scalars['Int']['output']>;
	tus_id?: Maybe<Scalars['Int']['output']>;
	type?: Maybe<Scalars['Int']['output']>;
	uploaded_by?: Maybe<Scalars['Int']['output']>;
	uploaded_on?: Maybe<Scalars['Int']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Files_Aggregated_Fields = {
	__typename?: 'directus_files_aggregated_fields';
	duration?: Maybe<Scalars['Float']['output']>;
	filesize?: Maybe<Scalars['Float']['output']>;
	focal_point_x?: Maybe<Scalars['Float']['output']>;
	focal_point_y?: Maybe<Scalars['Float']['output']>;
	height?: Maybe<Scalars['Float']['output']>;
	width?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Files_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Files_Filter>>>;
	charset?: InputMaybe<String_Filter_Operators>;
	created_on?: InputMaybe<Date_Filter_Operators>;
	created_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	duration?: InputMaybe<Number_Filter_Operators>;
	embed?: InputMaybe<String_Filter_Operators>;
	filename_disk?: InputMaybe<String_Filter_Operators>;
	filename_download?: InputMaybe<String_Filter_Operators>;
	filesize?: InputMaybe<Big_Int_Filter_Operators>;
	focal_point_x?: InputMaybe<Number_Filter_Operators>;
	focal_point_y?: InputMaybe<Number_Filter_Operators>;
	folder?: InputMaybe<Directus_Folders_Filter>;
	height?: InputMaybe<Number_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	metadata?: InputMaybe<String_Filter_Operators>;
	metadata_func?: InputMaybe<Count_Function_Filter_Operators>;
	modified_by?: InputMaybe<Directus_Users_Filter>;
	modified_on?: InputMaybe<Date_Filter_Operators>;
	modified_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	storage?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	tus_data?: InputMaybe<String_Filter_Operators>;
	tus_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	tus_id?: InputMaybe<String_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	uploaded_by?: InputMaybe<Directus_Users_Filter>;
	uploaded_on?: InputMaybe<Date_Filter_Operators>;
	uploaded_on_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Files_Mutated = {
	__typename?: 'directus_files_mutated';
	data?: Maybe<Directus_Files>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Flows = {
	__typename?: 'directus_flows';
	color?: Maybe<Scalars['String']['output']>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	options?: Maybe<Scalars['JSON']['output']>;
	options_func?: Maybe<Count_Functions>;
	status?: Maybe<Scalars['String']['output']>;
	trigger?: Maybe<Scalars['String']['output']>;
};

export type Directus_Flows_Aggregated = {
	__typename?: 'directus_flows_aggregated';
	count?: Maybe<Directus_Flows_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Flows_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Flows_Aggregated_Count = {
	__typename?: 'directus_flows_aggregated_count';
	color?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	options?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	trigger?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Flows_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Flows_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Flows_Filter>>>;
	color?: InputMaybe<String_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	status?: InputMaybe<String_Filter_Operators>;
	trigger?: InputMaybe<String_Filter_Operators>;
};

export type Directus_Flows_Mutated = {
	__typename?: 'directus_flows_mutated';
	data?: Maybe<Directus_Flows>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Folders = {
	__typename?: 'directus_folders';
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	parent?: Maybe<Directus_Folders>;
};

export type Directus_FoldersParentArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Folders_Aggregated = {
	__typename?: 'directus_folders_aggregated';
	count?: Maybe<Directus_Folders_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Folders_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Folders_Aggregated_Count = {
	__typename?: 'directus_folders_aggregated_count';
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	parent?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Folders_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Folders_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Folders_Filter>>>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Folders_Filter>;
};

export type Directus_Folders_Mutated = {
	__typename?: 'directus_folders_mutated';
	data?: Maybe<Directus_Folders>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Notifications = {
	__typename?: 'directus_notifications';
	collection?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	item?: Maybe<Scalars['String']['output']>;
	message?: Maybe<Scalars['String']['output']>;
	recipient?: Maybe<Directus_Users>;
	sender?: Maybe<Directus_Users>;
	status?: Maybe<Scalars['String']['output']>;
	subject: Scalars['String']['output'];
	timestamp?: Maybe<Scalars['Date']['output']>;
	timestamp_func?: Maybe<Datetime_Functions>;
};

export type Directus_NotificationsRecipientArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_NotificationsSenderArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Notifications_Aggregated = {
	__typename?: 'directus_notifications_aggregated';
	avg?: Maybe<Directus_Notifications_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Notifications_Aggregated_Fields>;
	count?: Maybe<Directus_Notifications_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Notifications_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Notifications_Aggregated_Fields>;
	min?: Maybe<Directus_Notifications_Aggregated_Fields>;
	sum?: Maybe<Directus_Notifications_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Notifications_Aggregated_Fields>;
};

export type Directus_Notifications_Aggregated_Count = {
	__typename?: 'directus_notifications_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	message?: Maybe<Scalars['Int']['output']>;
	recipient?: Maybe<Scalars['Int']['output']>;
	sender?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	subject?: Maybe<Scalars['Int']['output']>;
	timestamp?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Notifications_Aggregated_Fields = {
	__typename?: 'directus_notifications_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Notifications_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Notifications_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Notifications_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	message?: InputMaybe<String_Filter_Operators>;
	recipient?: InputMaybe<Directus_Users_Filter>;
	sender?: InputMaybe<Directus_Users_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	subject?: InputMaybe<String_Filter_Operators>;
	timestamp?: InputMaybe<Date_Filter_Operators>;
	timestamp_func?: InputMaybe<Datetime_Function_Filter_Operators>;
};

export type Directus_Notifications_Mutated = {
	__typename?: 'directus_notifications_mutated';
	data?: Maybe<Directus_Notifications>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Panels = {
	__typename?: 'directus_panels';
	color?: Maybe<Scalars['String']['output']>;
	dashboard?: Maybe<Directus_Dashboards>;
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	height: Scalars['Int']['output'];
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
	note?: Maybe<Scalars['String']['output']>;
	options?: Maybe<Scalars['JSON']['output']>;
	options_func?: Maybe<Count_Functions>;
	position_x: Scalars['Int']['output'];
	position_y: Scalars['Int']['output'];
	show_header: Scalars['Boolean']['output'];
	type: Scalars['String']['output'];
	user_created?: Maybe<Directus_Users>;
	width: Scalars['Int']['output'];
};

export type Directus_PanelsDashboardArgs = {
	filter?: InputMaybe<Directus_Dashboards_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_PanelsUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Panels_Aggregated = {
	__typename?: 'directus_panels_aggregated';
	avg?: Maybe<Directus_Panels_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Panels_Aggregated_Fields>;
	count?: Maybe<Directus_Panels_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Panels_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Panels_Aggregated_Fields>;
	min?: Maybe<Directus_Panels_Aggregated_Fields>;
	sum?: Maybe<Directus_Panels_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Panels_Aggregated_Fields>;
};

export type Directus_Panels_Aggregated_Count = {
	__typename?: 'directus_panels_aggregated_count';
	color?: Maybe<Scalars['Int']['output']>;
	dashboard?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	note?: Maybe<Scalars['Int']['output']>;
	options?: Maybe<Scalars['Int']['output']>;
	position_x?: Maybe<Scalars['Int']['output']>;
	position_y?: Maybe<Scalars['Int']['output']>;
	show_header?: Maybe<Scalars['Int']['output']>;
	type?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Panels_Aggregated_Fields = {
	__typename?: 'directus_panels_aggregated_fields';
	height?: Maybe<Scalars['Float']['output']>;
	position_x?: Maybe<Scalars['Float']['output']>;
	position_y?: Maybe<Scalars['Float']['output']>;
	width?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Panels_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	color?: InputMaybe<String_Filter_Operators>;
	dashboard?: InputMaybe<Directus_Dashboards_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	height?: InputMaybe<Number_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	position_x?: InputMaybe<Number_Filter_Operators>;
	position_y?: InputMaybe<Number_Filter_Operators>;
	show_header?: InputMaybe<Boolean_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Panels_Mutated = {
	__typename?: 'directus_panels_mutated';
	data?: Maybe<Directus_Panels>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Panels_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	_none?: InputMaybe<Directus_Panels_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Panels_Filter>>>;
	_some?: InputMaybe<Directus_Panels_Filter>;
	color?: InputMaybe<String_Filter_Operators>;
	dashboard?: InputMaybe<Directus_Dashboards_Filter>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	height?: InputMaybe<Number_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	note?: InputMaybe<String_Filter_Operators>;
	options?: InputMaybe<String_Filter_Operators>;
	options_func?: InputMaybe<Count_Function_Filter_Operators>;
	position_x?: InputMaybe<Number_Filter_Operators>;
	position_y?: InputMaybe<Number_Filter_Operators>;
	show_header?: InputMaybe<Boolean_Filter_Operators>;
	type?: InputMaybe<String_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
	width?: InputMaybe<Number_Filter_Operators>;
};

export type Directus_Presets = {
	__typename?: 'directus_presets';
	bookmark?: Maybe<Scalars['String']['output']>;
	collection?: Maybe<Scalars['String']['output']>;
	color?: Maybe<Scalars['String']['output']>;
	filter?: Maybe<Scalars['JSON']['output']>;
	filter_func?: Maybe<Count_Functions>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	layout?: Maybe<Scalars['String']['output']>;
	layout_options?: Maybe<Scalars['JSON']['output']>;
	layout_options_func?: Maybe<Count_Functions>;
	layout_query?: Maybe<Scalars['JSON']['output']>;
	layout_query_func?: Maybe<Count_Functions>;
	refresh_interval?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Directus_Roles>;
	search?: Maybe<Scalars['String']['output']>;
	user?: Maybe<Directus_Users>;
};

export type Directus_PresetsRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_PresetsUserArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Presets_Aggregated = {
	__typename?: 'directus_presets_aggregated';
	avg?: Maybe<Directus_Presets_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Presets_Aggregated_Fields>;
	count?: Maybe<Directus_Presets_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Presets_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Presets_Aggregated_Fields>;
	min?: Maybe<Directus_Presets_Aggregated_Fields>;
	sum?: Maybe<Directus_Presets_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Presets_Aggregated_Fields>;
};

export type Directus_Presets_Aggregated_Count = {
	__typename?: 'directus_presets_aggregated_count';
	bookmark?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	color?: Maybe<Scalars['Int']['output']>;
	filter?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	layout?: Maybe<Scalars['Int']['output']>;
	layout_options?: Maybe<Scalars['Int']['output']>;
	layout_query?: Maybe<Scalars['Int']['output']>;
	refresh_interval?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	search?: Maybe<Scalars['Int']['output']>;
	user?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Presets_Aggregated_Fields = {
	__typename?: 'directus_presets_aggregated_fields';
	id?: Maybe<Scalars['Float']['output']>;
	refresh_interval?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Presets_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Presets_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Presets_Filter>>>;
	bookmark?: InputMaybe<String_Filter_Operators>;
	collection?: InputMaybe<String_Filter_Operators>;
	color?: InputMaybe<String_Filter_Operators>;
	filter?: InputMaybe<String_Filter_Operators>;
	filter_func?: InputMaybe<Count_Function_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	layout?: InputMaybe<String_Filter_Operators>;
	layout_options?: InputMaybe<String_Filter_Operators>;
	layout_options_func?: InputMaybe<Count_Function_Filter_Operators>;
	layout_query?: InputMaybe<String_Filter_Operators>;
	layout_query_func?: InputMaybe<Count_Function_Filter_Operators>;
	refresh_interval?: InputMaybe<Number_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	search?: InputMaybe<String_Filter_Operators>;
	user?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Presets_Mutated = {
	__typename?: 'directus_presets_mutated';
	data?: Maybe<Directus_Presets>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Relations = {
	__typename?: 'directus_relations';
	collection?: Maybe<Scalars['String']['output']>;
	field?: Maybe<Scalars['String']['output']>;
	meta?: Maybe<Directus_Relations_Meta>;
	related_collection?: Maybe<Scalars['String']['output']>;
	schema?: Maybe<Directus_Relations_Schema>;
};

export type Directus_Relations_Meta = {
	__typename?: 'directus_relations_meta';
	id?: Maybe<Scalars['Int']['output']>;
	junction_field?: Maybe<Scalars['String']['output']>;
	many_collection?: Maybe<Scalars['String']['output']>;
	many_field?: Maybe<Scalars['String']['output']>;
	one_allowed_collections?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
	one_collection?: Maybe<Scalars['String']['output']>;
	one_collection_field?: Maybe<Scalars['String']['output']>;
	one_deselect_action?: Maybe<Scalars['String']['output']>;
	one_field?: Maybe<Scalars['String']['output']>;
	sort_field?: Maybe<Scalars['String']['output']>;
};

export type Directus_Relations_Schema = {
	__typename?: 'directus_relations_schema';
	column: Scalars['String']['output'];
	constraint_name?: Maybe<Scalars['String']['output']>;
	foreign_key_column: Scalars['String']['output'];
	foreign_key_table: Scalars['String']['output'];
	on_delete: Scalars['String']['output'];
	on_update: Scalars['String']['output'];
	table: Scalars['String']['output'];
};

export type Directus_Revisions = {
	__typename?: 'directus_revisions';
	activity?: Maybe<Directus_Activity>;
	collection: Scalars['String']['output'];
	data?: Maybe<Scalars['JSON']['output']>;
	data_func?: Maybe<Count_Functions>;
	delta?: Maybe<Scalars['JSON']['output']>;
	delta_func?: Maybe<Count_Functions>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	parent?: Maybe<Directus_Revisions>;
	version?: Maybe<Scalars['ID']['output']>;
};

export type Directus_RevisionsActivityArgs = {
	filter?: InputMaybe<Directus_Activity_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_RevisionsParentArgs = {
	filter?: InputMaybe<Directus_Revisions_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Revisions_Aggregated = {
	__typename?: 'directus_revisions_aggregated';
	avg?: Maybe<Directus_Revisions_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Revisions_Aggregated_Fields>;
	count?: Maybe<Directus_Revisions_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Revisions_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Revisions_Aggregated_Fields>;
	min?: Maybe<Directus_Revisions_Aggregated_Fields>;
	sum?: Maybe<Directus_Revisions_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Revisions_Aggregated_Fields>;
};

export type Directus_Revisions_Aggregated_Count = {
	__typename?: 'directus_revisions_aggregated_count';
	activity?: Maybe<Scalars['Int']['output']>;
	collection?: Maybe<Scalars['Int']['output']>;
	data?: Maybe<Scalars['Int']['output']>;
	delta?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	parent?: Maybe<Scalars['Int']['output']>;
	version?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Revisions_Aggregated_Fields = {
	__typename?: 'directus_revisions_aggregated_fields';
	activity?: Maybe<Scalars['Float']['output']>;
	id?: Maybe<Scalars['Float']['output']>;
	parent?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Revisions_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	activity?: InputMaybe<Directus_Activity_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	data?: InputMaybe<String_Filter_Operators>;
	data_func?: InputMaybe<Count_Function_Filter_Operators>;
	delta?: InputMaybe<String_Filter_Operators>;
	delta_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Revisions_Filter>;
	version?: InputMaybe<Id_Filter_Operators>;
};

export type Directus_Revisions_Mutated = {
	__typename?: 'directus_revisions_mutated';
	data?: Maybe<Directus_Revisions>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Revisions_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	_none?: InputMaybe<Directus_Revisions_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Revisions_Filter>>>;
	_some?: InputMaybe<Directus_Revisions_Filter>;
	activity?: InputMaybe<Directus_Activity_Filter>;
	collection?: InputMaybe<String_Filter_Operators>;
	data?: InputMaybe<String_Filter_Operators>;
	data_func?: InputMaybe<Count_Function_Filter_Operators>;
	delta?: InputMaybe<String_Filter_Operators>;
	delta_func?: InputMaybe<Count_Function_Filter_Operators>;
	id?: InputMaybe<Number_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Revisions_Filter>;
	version?: InputMaybe<Id_Filter_Operators>;
};

export type Directus_Roles = {
	__typename?: 'directus_roles';
	children?: Maybe<Array<Maybe<Directus_Roles>>>;
	children_func?: Maybe<Count_Functions>;
	description?: Maybe<Scalars['String']['output']>;
	icon?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	name: Scalars['String']['output'];
	parent?: Maybe<Directus_Roles>;
	policies?: Maybe<Scalars['String']['output']>;
	policies_func?: Maybe<Count_Functions>;
	users?: Maybe<Array<Maybe<Directus_Users>>>;
	users_func?: Maybe<Count_Functions>;
};

export type Directus_RolesChildrenArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_RolesParentArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_RolesUsersArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Roles_Aggregated = {
	__typename?: 'directus_roles_aggregated';
	count?: Maybe<Directus_Roles_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Roles_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Roles_Aggregated_Count = {
	__typename?: 'directus_roles_aggregated_count';
	/** $t:field_options.directus_roles.children_note */
	children?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	icon?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	/** $t:field_options.directus_roles.parent_note */
	parent?: Maybe<Scalars['Int']['output']>;
	policies?: Maybe<Scalars['Int']['output']>;
	users?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Roles_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	children?: InputMaybe<Directus_Roles_Quantifier_Filter>;
	children_func?: InputMaybe<Count_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Roles_Filter>;
	policies?: InputMaybe<String_Filter_Operators>;
	policies_func?: InputMaybe<Count_Function_Filter_Operators>;
	users?: InputMaybe<Directus_Users_Quantifier_Filter>;
	users_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Directus_Roles_Mutated = {
	__typename?: 'directus_roles_mutated';
	data?: Maybe<Directus_Roles>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Roles_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	_none?: InputMaybe<Directus_Roles_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Roles_Filter>>>;
	_some?: InputMaybe<Directus_Roles_Filter>;
	children?: InputMaybe<Directus_Roles_Quantifier_Filter>;
	children_func?: InputMaybe<Count_Function_Filter_Operators>;
	description?: InputMaybe<String_Filter_Operators>;
	icon?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	parent?: InputMaybe<Directus_Roles_Filter>;
	policies?: InputMaybe<String_Filter_Operators>;
	policies_func?: InputMaybe<Count_Function_Filter_Operators>;
	users?: InputMaybe<Directus_Users_Quantifier_Filter>;
	users_func?: InputMaybe<Count_Function_Filter_Operators>;
};

export type Directus_Settings = {
	__typename?: 'directus_settings';
	auth_login_attempts?: Maybe<Scalars['Int']['output']>;
	auth_password_policy?: Maybe<Scalars['String']['output']>;
	basemaps?: Maybe<Scalars['JSON']['output']>;
	basemaps_func?: Maybe<Count_Functions>;
	custom_aspect_ratios?: Maybe<Scalars['JSON']['output']>;
	custom_aspect_ratios_func?: Maybe<Count_Functions>;
	custom_css?: Maybe<Scalars['String']['output']>;
	default_appearance?: Maybe<Scalars['String']['output']>;
	default_language?: Maybe<Scalars['String']['output']>;
	default_theme_dark?: Maybe<Scalars['String']['output']>;
	default_theme_light?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	mapbox_key?: Maybe<Scalars['String']['output']>;
	module_bar?: Maybe<Scalars['JSON']['output']>;
	module_bar_func?: Maybe<Count_Functions>;
	/** $t:field_options.directus_settings.project_color_note */
	project_color?: Maybe<Scalars['String']['output']>;
	project_descriptor?: Maybe<Scalars['String']['output']>;
	project_logo?: Maybe<Directus_Files>;
	project_name?: Maybe<Scalars['String']['output']>;
	project_url?: Maybe<Scalars['String']['output']>;
	public_background?: Maybe<Directus_Files>;
	public_favicon?: Maybe<Directus_Files>;
	public_foreground?: Maybe<Directus_Files>;
	public_note?: Maybe<Scalars['String']['output']>;
	/** $t:fields.directus_settings.public_registration_note */
	public_registration: Scalars['Boolean']['output'];
	/** $t:fields.directus_settings.public_registration_email_filter_note */
	public_registration_email_filter?: Maybe<Scalars['JSON']['output']>;
	public_registration_email_filter_func?: Maybe<Count_Functions>;
	public_registration_role?: Maybe<Directus_Roles>;
	/** $t:fields.directus_settings.public_registration_verify_email_note */
	public_registration_verify_email?: Maybe<Scalars['Boolean']['output']>;
	report_bug_url?: Maybe<Scalars['String']['output']>;
	report_error_url?: Maybe<Scalars['String']['output']>;
	report_feature_url?: Maybe<Scalars['String']['output']>;
	storage_asset_presets?: Maybe<Scalars['JSON']['output']>;
	storage_asset_presets_func?: Maybe<Count_Functions>;
	storage_asset_transform?: Maybe<Scalars['String']['output']>;
	storage_default_folder?: Maybe<Directus_Folders>;
	theme_dark_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_dark_overrides_func?: Maybe<Count_Functions>;
	theme_light_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_light_overrides_func?: Maybe<Count_Functions>;
	visual_editor_urls?: Maybe<Scalars['JSON']['output']>;
	visual_editor_urls_func?: Maybe<Count_Functions>;
};

export type Directus_SettingsProject_LogoArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_BackgroundArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_FaviconArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_ForegroundArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsPublic_Registration_RoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SettingsStorage_Default_FolderArgs = {
	filter?: InputMaybe<Directus_Folders_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Settings_Mutated = {
	__typename?: 'directus_settings_mutated';
	data?: Maybe<Directus_Settings>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Shares = {
	__typename?: 'directus_shares';
	collection: Scalars['String']['output'];
	date_created?: Maybe<Scalars['Date']['output']>;
	date_created_func?: Maybe<Datetime_Functions>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: Maybe<Scalars['Date']['output']>;
	date_end_func?: Maybe<Datetime_Functions>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: Maybe<Scalars['Date']['output']>;
	date_start_func?: Maybe<Datetime_Functions>;
	id: Scalars['ID']['output'];
	item: Scalars['String']['output'];
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: Maybe<Scalars['Hash']['output']>;
	role?: Maybe<Directus_Roles>;
	times_used?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Directus_Users>;
};

export type Directus_SharesRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_SharesUser_CreatedArgs = {
	filter?: InputMaybe<Directus_Users_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Shares_Aggregated = {
	__typename?: 'directus_shares_aggregated';
	avg?: Maybe<Directus_Shares_Aggregated_Fields>;
	avgDistinct?: Maybe<Directus_Shares_Aggregated_Fields>;
	count?: Maybe<Directus_Shares_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Shares_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
	max?: Maybe<Directus_Shares_Aggregated_Fields>;
	min?: Maybe<Directus_Shares_Aggregated_Fields>;
	sum?: Maybe<Directus_Shares_Aggregated_Fields>;
	sumDistinct?: Maybe<Directus_Shares_Aggregated_Fields>;
};

export type Directus_Shares_Aggregated_Count = {
	__typename?: 'directus_shares_aggregated_count';
	collection?: Maybe<Scalars['Int']['output']>;
	date_created?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Int']['output']>;
	name?: Maybe<Scalars['Int']['output']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	times_used?: Maybe<Scalars['Int']['output']>;
	user_created?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Shares_Aggregated_Fields = {
	__typename?: 'directus_shares_aggregated_fields';
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: Maybe<Scalars['Float']['output']>;
	times_used?: Maybe<Scalars['Float']['output']>;
};

export type Directus_Shares_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Shares_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Shares_Filter>>>;
	collection?: InputMaybe<String_Filter_Operators>;
	date_created?: InputMaybe<Date_Filter_Operators>;
	date_created_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_end?: InputMaybe<Date_Filter_Operators>;
	date_end_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	date_start?: InputMaybe<Date_Filter_Operators>;
	date_start_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	item?: InputMaybe<String_Filter_Operators>;
	max_uses?: InputMaybe<Number_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
	password?: InputMaybe<Hash_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	times_used?: InputMaybe<Number_Filter_Operators>;
	user_created?: InputMaybe<Directus_Users_Filter>;
};

export type Directus_Shares_Mutated = {
	__typename?: 'directus_shares_mutated';
	data?: Maybe<Directus_Shares>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Translations = {
	__typename?: 'directus_translations';
	id: Scalars['ID']['output'];
	key: Scalars['String']['output'];
	language: Scalars['String']['output'];
	value: Scalars['String']['output'];
};

export type Directus_Translations_Aggregated = {
	__typename?: 'directus_translations_aggregated';
	count?: Maybe<Directus_Translations_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Translations_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Translations_Aggregated_Count = {
	__typename?: 'directus_translations_aggregated_count';
	id?: Maybe<Scalars['Int']['output']>;
	key?: Maybe<Scalars['Int']['output']>;
	language?: Maybe<Scalars['Int']['output']>;
	value?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Translations_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Translations_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Translations_Filter>>>;
	id?: InputMaybe<Id_Filter_Operators>;
	key?: InputMaybe<String_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	value?: InputMaybe<String_Filter_Operators>;
};

export type Directus_Translations_Mutated = {
	__typename?: 'directus_translations_mutated';
	data?: Maybe<Directus_Translations>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Users = {
	__typename?: 'directus_users';
	appearance?: Maybe<Scalars['String']['output']>;
	auth_data?: Maybe<Scalars['JSON']['output']>;
	auth_data_func?: Maybe<Count_Functions>;
	avatar?: Maybe<Directus_Files>;
	description?: Maybe<Scalars['String']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	email_notifications?: Maybe<Scalars['Boolean']['output']>;
	external_identifier?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['ID']['output']>;
	language?: Maybe<Scalars['String']['output']>;
	last_access?: Maybe<Scalars['Date']['output']>;
	last_access_func?: Maybe<Datetime_Functions>;
	last_name?: Maybe<Scalars['String']['output']>;
	last_page?: Maybe<Scalars['String']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	password?: Maybe<Scalars['Hash']['output']>;
	policies?: Maybe<Scalars['String']['output']>;
	policies_func?: Maybe<Count_Functions>;
	provider?: Maybe<Scalars['String']['output']>;
	role?: Maybe<Directus_Roles>;
	status?: Maybe<Scalars['String']['output']>;
	tags?: Maybe<Scalars['JSON']['output']>;
	tags_func?: Maybe<Count_Functions>;
	tfa_secret?: Maybe<Scalars['Hash']['output']>;
	theme_dark?: Maybe<Scalars['String']['output']>;
	theme_dark_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_dark_overrides_func?: Maybe<Count_Functions>;
	theme_light?: Maybe<Scalars['String']['output']>;
	theme_light_overrides?: Maybe<Scalars['JSON']['output']>;
	theme_light_overrides_func?: Maybe<Count_Functions>;
	title?: Maybe<Scalars['String']['output']>;
	token?: Maybe<Scalars['Hash']['output']>;
};

export type Directus_UsersAvatarArgs = {
	filter?: InputMaybe<Directus_Files_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_UsersRoleArgs = {
	filter?: InputMaybe<Directus_Roles_Filter>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	page?: InputMaybe<Scalars['Int']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	sort?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Directus_Users_Aggregated = {
	__typename?: 'directus_users_aggregated';
	count?: Maybe<Directus_Users_Aggregated_Count>;
	countAll?: Maybe<Scalars['Int']['output']>;
	countDistinct?: Maybe<Directus_Users_Aggregated_Count>;
	group?: Maybe<Scalars['JSON']['output']>;
};

export type Directus_Users_Aggregated_Count = {
	__typename?: 'directus_users_aggregated_count';
	appearance?: Maybe<Scalars['Int']['output']>;
	auth_data?: Maybe<Scalars['Int']['output']>;
	avatar?: Maybe<Scalars['Int']['output']>;
	description?: Maybe<Scalars['Int']['output']>;
	email?: Maybe<Scalars['Int']['output']>;
	email_notifications?: Maybe<Scalars['Int']['output']>;
	external_identifier?: Maybe<Scalars['Int']['output']>;
	first_name?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	language?: Maybe<Scalars['Int']['output']>;
	last_access?: Maybe<Scalars['Int']['output']>;
	last_name?: Maybe<Scalars['Int']['output']>;
	last_page?: Maybe<Scalars['Int']['output']>;
	location?: Maybe<Scalars['Int']['output']>;
	password?: Maybe<Scalars['Int']['output']>;
	policies?: Maybe<Scalars['Int']['output']>;
	provider?: Maybe<Scalars['Int']['output']>;
	role?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Scalars['Int']['output']>;
	tags?: Maybe<Scalars['Int']['output']>;
	tfa_secret?: Maybe<Scalars['Int']['output']>;
	theme_dark?: Maybe<Scalars['Int']['output']>;
	theme_dark_overrides?: Maybe<Scalars['Int']['output']>;
	theme_light?: Maybe<Scalars['Int']['output']>;
	theme_light_overrides?: Maybe<Scalars['Int']['output']>;
	title?: Maybe<Scalars['Int']['output']>;
	token?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Users_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	appearance?: InputMaybe<String_Filter_Operators>;
	auth_data?: InputMaybe<String_Filter_Operators>;
	auth_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	avatar?: InputMaybe<Directus_Files_Filter>;
	description?: InputMaybe<String_Filter_Operators>;
	email?: InputMaybe<String_Filter_Operators>;
	email_notifications?: InputMaybe<Boolean_Filter_Operators>;
	external_identifier?: InputMaybe<String_Filter_Operators>;
	first_name?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	last_access?: InputMaybe<Date_Filter_Operators>;
	last_access_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	last_name?: InputMaybe<String_Filter_Operators>;
	last_page?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	password?: InputMaybe<Hash_Filter_Operators>;
	policies?: InputMaybe<String_Filter_Operators>;
	policies_func?: InputMaybe<Count_Function_Filter_Operators>;
	provider?: InputMaybe<String_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	tfa_secret?: InputMaybe<Hash_Filter_Operators>;
	theme_dark?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	theme_light?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	token?: InputMaybe<Hash_Filter_Operators>;
};

export type Directus_Users_Mutated = {
	__typename?: 'directus_users_mutated';
	data?: Maybe<Directus_Users>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Directus_Users_Quantifier_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	_none?: InputMaybe<Directus_Users_Filter>;
	_or?: InputMaybe<Array<InputMaybe<Directus_Users_Filter>>>;
	_some?: InputMaybe<Directus_Users_Filter>;
	appearance?: InputMaybe<String_Filter_Operators>;
	auth_data?: InputMaybe<String_Filter_Operators>;
	auth_data_func?: InputMaybe<Count_Function_Filter_Operators>;
	avatar?: InputMaybe<Directus_Files_Filter>;
	description?: InputMaybe<String_Filter_Operators>;
	email?: InputMaybe<String_Filter_Operators>;
	email_notifications?: InputMaybe<Boolean_Filter_Operators>;
	external_identifier?: InputMaybe<String_Filter_Operators>;
	first_name?: InputMaybe<String_Filter_Operators>;
	id?: InputMaybe<Id_Filter_Operators>;
	language?: InputMaybe<String_Filter_Operators>;
	last_access?: InputMaybe<Date_Filter_Operators>;
	last_access_func?: InputMaybe<Datetime_Function_Filter_Operators>;
	last_name?: InputMaybe<String_Filter_Operators>;
	last_page?: InputMaybe<String_Filter_Operators>;
	location?: InputMaybe<String_Filter_Operators>;
	password?: InputMaybe<Hash_Filter_Operators>;
	policies?: InputMaybe<String_Filter_Operators>;
	policies_func?: InputMaybe<Count_Function_Filter_Operators>;
	provider?: InputMaybe<String_Filter_Operators>;
	role?: InputMaybe<Directus_Roles_Filter>;
	status?: InputMaybe<String_Filter_Operators>;
	tags?: InputMaybe<String_Filter_Operators>;
	tags_func?: InputMaybe<Count_Function_Filter_Operators>;
	tfa_secret?: InputMaybe<Hash_Filter_Operators>;
	theme_dark?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides?: InputMaybe<String_Filter_Operators>;
	theme_dark_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	theme_light?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides?: InputMaybe<String_Filter_Operators>;
	theme_light_overrides_func?: InputMaybe<Count_Function_Filter_Operators>;
	title?: InputMaybe<String_Filter_Operators>;
	token?: InputMaybe<Hash_Filter_Operators>;
};

export enum Graphql_Sdl_Scope {
	Items = 'items',
	System = 'system',
}

export type Hash_Filter_Operators = {
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Id_Filter_Operators = {
	_contains?: InputMaybe<Scalars['ID']['input']>;
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_ends_with?: InputMaybe<Scalars['ID']['input']>;
	_eq?: InputMaybe<Scalars['ID']['input']>;
	_icontains?: InputMaybe<Scalars['ID']['input']>;
	_iends_with?: InputMaybe<Scalars['ID']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
	_istarts_with?: InputMaybe<Scalars['ID']['input']>;
	_ncontains?: InputMaybe<Scalars['ID']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nends_with?: InputMaybe<Scalars['ID']['input']>;
	_neq?: InputMaybe<Scalars['ID']['input']>;
	_niends_with?: InputMaybe<Scalars['ID']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
	_nistarts_with?: InputMaybe<Scalars['ID']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_nstarts_with?: InputMaybe<Scalars['ID']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
	_starts_with?: InputMaybe<Scalars['ID']['input']>;
};

export type Number_Filter_Operators = {
	_between?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_eq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_gte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>>;
	_lt?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_lte?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nbetween?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_neq?: InputMaybe<Scalars['GraphQLStringOrFloat']['input']>;
	_nin?: InputMaybe<
		Array<InputMaybe<Scalars['GraphQLStringOrFloat']['input']>>
	>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Server_Info = {
	__typename?: 'server_info';
	project?: Maybe<Server_Info_Project>;
	queryLimit?: Maybe<Server_Info_Query_Limit>;
	rateLimit?: Maybe<Scalars['Boolean']['output']>;
	rateLimitGlobal?: Maybe<Scalars['Boolean']['output']>;
	websocket?: Maybe<Scalars['Boolean']['output']>;
};

export type Server_Info_Project = {
	__typename?: 'server_info_project';
	custom_css?: Maybe<Scalars['String']['output']>;
	default_language?: Maybe<Scalars['String']['output']>;
	project_color?: Maybe<Scalars['String']['output']>;
	project_descriptor?: Maybe<Scalars['String']['output']>;
	project_logo?: Maybe<Scalars['String']['output']>;
	project_name?: Maybe<Scalars['String']['output']>;
	public_background?: Maybe<Scalars['String']['output']>;
	public_foreground?: Maybe<Scalars['String']['output']>;
	public_note?: Maybe<Scalars['String']['output']>;
	public_registration?: Maybe<Scalars['Boolean']['output']>;
	public_registration_verify_email?: Maybe<Scalars['Boolean']['output']>;
};

export type Server_Info_Query_Limit = {
	__typename?: 'server_info_query_limit';
	default?: Maybe<Scalars['Int']['output']>;
	max?: Maybe<Scalars['Int']['output']>;
};

export type String_Filter_Operators = {
	_contains?: InputMaybe<Scalars['String']['input']>;
	_empty?: InputMaybe<Scalars['Boolean']['input']>;
	_ends_with?: InputMaybe<Scalars['String']['input']>;
	_eq?: InputMaybe<Scalars['String']['input']>;
	_icontains?: InputMaybe<Scalars['String']['input']>;
	_iends_with?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_istarts_with?: InputMaybe<Scalars['String']['input']>;
	_ncontains?: InputMaybe<Scalars['String']['input']>;
	_nempty?: InputMaybe<Scalars['Boolean']['input']>;
	_nends_with?: InputMaybe<Scalars['String']['input']>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	_niends_with?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	_nistarts_with?: InputMaybe<Scalars['String']['input']>;
	_nnull?: InputMaybe<Scalars['Boolean']['input']>;
	_nstarts_with?: InputMaybe<Scalars['String']['input']>;
	_null?: InputMaybe<Scalars['Boolean']['input']>;
	_starts_with?: InputMaybe<Scalars['String']['input']>;
};

export type Timezone = {
	__typename?: 'timezone';
	code: Scalars['ID']['output'];
};

export type Timezone_Mutated = {
	__typename?: 'timezone_mutated';
	data?: Maybe<Timezone>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Uom = {
	__typename?: 'uom';
	code: Scalars['ID']['output'];
	name?: Maybe<Scalars['String']['output']>;
};

export type Uom_Filter = {
	_and?: InputMaybe<Array<InputMaybe<Uom_Filter>>>;
	_or?: InputMaybe<Array<InputMaybe<Uom_Filter>>>;
	code?: InputMaybe<String_Filter_Operators>;
	name?: InputMaybe<String_Filter_Operators>;
};

export type Uom_Mutated = {
	__typename?: 'uom_mutated';
	data?: Maybe<Uom>;
	event?: Maybe<EventEnum>;
	key: Scalars['ID']['output'];
};

export type Update_Directus_Comments_Input = {
	comment?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Directus_Dashboards_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	panels?: InputMaybe<Array<InputMaybe<Update_Directus_Panels_Input>>>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Files_Input = {
	charset?: InputMaybe<Scalars['String']['input']>;
	created_on?: InputMaybe<Scalars['Date']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	duration?: InputMaybe<Scalars['Int']['input']>;
	embed?: InputMaybe<Scalars['String']['input']>;
	filename_disk?: InputMaybe<Scalars['String']['input']>;
	filename_download?: InputMaybe<Scalars['String']['input']>;
	filesize?: InputMaybe<Scalars['GraphQLBigInt']['input']>;
	focal_point_x?: InputMaybe<Scalars['Int']['input']>;
	focal_point_y?: InputMaybe<Scalars['Int']['input']>;
	folder?: InputMaybe<Update_Directus_Folders_Input>;
	height?: InputMaybe<Scalars['Int']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	metadata?: InputMaybe<Scalars['JSON']['input']>;
	modified_by?: InputMaybe<Update_Directus_Users_Input>;
	modified_on?: InputMaybe<Scalars['Date']['input']>;
	storage?: InputMaybe<Scalars['String']['input']>;
	tags?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
	tus_data?: InputMaybe<Scalars['JSON']['input']>;
	tus_id?: InputMaybe<Scalars['String']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	uploaded_by?: InputMaybe<Update_Directus_Users_Input>;
	uploaded_on?: InputMaybe<Scalars['Date']['input']>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Update_Directus_Folders_Input = {
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	parent?: InputMaybe<Update_Directus_Folders_Input>;
};

export type Update_Directus_Notifications_Input = {
	status?: InputMaybe<Scalars['String']['input']>;
};

export type Update_Directus_Panels_Input = {
	color?: InputMaybe<Scalars['String']['input']>;
	dashboard?: InputMaybe<Update_Directus_Dashboards_Input>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	height?: InputMaybe<Scalars['Int']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	note?: InputMaybe<Scalars['String']['input']>;
	options?: InputMaybe<Scalars['JSON']['input']>;
	position_x?: InputMaybe<Scalars['Int']['input']>;
	position_y?: InputMaybe<Scalars['Int']['input']>;
	show_header?: InputMaybe<Scalars['Boolean']['input']>;
	type?: InputMaybe<Scalars['String']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
	width?: InputMaybe<Scalars['Int']['input']>;
};

export type Update_Directus_Presets_Input = {
	bookmark?: InputMaybe<Scalars['String']['input']>;
	collection?: InputMaybe<Scalars['String']['input']>;
	color?: InputMaybe<Scalars['String']['input']>;
	filter?: InputMaybe<Scalars['JSON']['input']>;
	icon?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	layout?: InputMaybe<Scalars['String']['input']>;
	layout_options?: InputMaybe<Scalars['JSON']['input']>;
	layout_query?: InputMaybe<Scalars['JSON']['input']>;
	refresh_interval?: InputMaybe<Scalars['Int']['input']>;
	role?: InputMaybe<Scalars['ID']['input']>;
	search?: InputMaybe<Scalars['String']['input']>;
	user?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Shares_Input = {
	collection?: InputMaybe<Scalars['String']['input']>;
	date_created?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_end?: InputMaybe<Scalars['Date']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	date_start?: InputMaybe<Scalars['Date']['input']>;
	id?: InputMaybe<Scalars['ID']['input']>;
	item?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_unlimited */
	max_uses?: InputMaybe<Scalars['Int']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	/** $t:shared_leave_blank_for_passwordless_access */
	password?: InputMaybe<Scalars['Hash']['input']>;
	role?: InputMaybe<Scalars['ID']['input']>;
	times_used?: InputMaybe<Scalars['Int']['input']>;
	user_created?: InputMaybe<Update_Directus_Users_Input>;
};

export type Update_Directus_Users_Input = {
	appearance?: InputMaybe<Scalars['String']['input']>;
	avatar?: InputMaybe<Update_Directus_Files_Input>;
	description?: InputMaybe<Scalars['String']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	language?: InputMaybe<Scalars['String']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	password?: InputMaybe<Scalars['Hash']['input']>;
	tfa_secret?: InputMaybe<Scalars['Hash']['input']>;
	theme_dark?: InputMaybe<Scalars['String']['input']>;
	theme_dark_overrides?: InputMaybe<Scalars['JSON']['input']>;
	theme_light?: InputMaybe<Scalars['String']['input']>;
	theme_light_overrides?: InputMaybe<Scalars['JSON']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
};

export type Users_Me_Tfa_Generate_Data = {
	__typename?: 'users_me_tfa_generate_data';
	otpauth_url?: Maybe<Scalars['String']['output']>;
	secret?: Maybe<Scalars['String']['output']>;
};
