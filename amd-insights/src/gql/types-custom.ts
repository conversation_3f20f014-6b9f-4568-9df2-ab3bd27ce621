export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
	[K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
	[SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
	T extends { [key: string]: unknown },
	K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
	| T
	| {
			[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never;
	  };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	Date: { input: any; output: any };
	DateOrYear: { input: any; output: any };
	DateTime: { input: any; output: any };
	Time: { input: any; output: any };
};

export type ActivityTransfer = {
	__typename?: 'ActivityTransfer';
	activity: Scalars['String']['output'];
	from_artwork: Scalars['String']['output'];
	to_artwork: Scalars['String']['output'];
};

export type ActivityTransferResponse = {
	__typename?: 'ActivityTransferResponse';
	activitiesTransferred: Array<ActivityTransfer>;
};

export type Artist = {
	__typename?: 'Artist';
	id: Scalars['ID']['output'];
	person?: Maybe<Person>;
};

export type ArtistDetails = {
	__typename?: 'ArtistDetails';
	name?: Maybe<Scalars['String']['output']>;
	nationality_country_code?: Maybe<Scalars['String']['output']>;
	nationality_name?: Maybe<Scalars['String']['output']>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type ArtistDetailsInput = {
	name?: InputMaybe<Scalars['String']['input']>;
	nationality_country_code?: InputMaybe<Scalars['String']['input']>;
	year_birth?: InputMaybe<Scalars['Int']['input']>;
	year_death?: InputMaybe<Scalars['Int']['input']>;
};

export type Artist_Details = {
	__typename?: 'Artist_Details';
	artwork_details?: Maybe<Artwork_Details>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Country>;
	original_extraction_response?: Maybe<Scalars['String']['output']>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	raw_nationality?: Maybe<Scalars['String']['output']>;
	reference_artist_id?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type Artlogic_Link = {
	__typename?: 'Artlogic_Link';
	art_event_feed?: Maybe<Art_Event_Feed>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	processed_artist?: Maybe<Processed_Artist>;
	processed_gallery?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	review_status?: Maybe<Checklist_Review_Status>;
	scraped_at?: Maybe<Scalars['DateTime']['output']>;
	status?: Maybe<Artlogic_Link_Status>;
	title?: Maybe<Scalars['String']['output']>;
	url?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artlogic_Link_Status = {
	__typename?: 'Artlogic_Link_Status';
	key?: Maybe<Artlogic_Link_Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artlogic_Link_Status_Enum {
	AwaitingExtraction = 'AWAITING_EXTRACTION',
	Rescraped = 'RESCRAPED',
	Scraped = 'SCRAPED',
}

export type ArtworkMatch = {
	__typename?: 'ArtworkMatch';
	artists?: Maybe<Array<Maybe<Artist>>>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_type?: Maybe<Scalars['String']['output']>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	execution_end_year?: Maybe<Scalars['Int']['output']>;
	execution_start_year?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	media?: Maybe<Scalars['String']['output']>;
	primary_image?: Maybe<Directus_Files>;
	score: Scalars['Float']['output'];
	title?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Details = {
	__typename?: 'Artwork_Details';
	artist_proof_size?: Maybe<Scalars['Int']['output']>;
	artist_text?: Maybe<Scalars['String']['output']>;
	artists?: Maybe<Array<Maybe<Artist_Details>>>;
	artwork_removed?: Maybe<Scalars['Boolean']['output']>;
	artwork_type?: Maybe<Artwork_Type>;
	condition?: Maybe<Scalars['String']['output']>;
	crid?: Maybe<Scalars['String']['output']>;
	crid_text?: Maybe<Scalars['String']['output']>;
	currency?: Maybe<Currency>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	dimension_type?: Maybe<Artwork_Dimension_Type>;
	dimensions?: Maybe<Scalars['String']['output']>;
	dimensions_depth_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_height_cm?: Maybe<Scalars['Float']['output']>;
	dimensions_width_cm?: Maybe<Scalars['Float']['output']>;
	edition_description?: Maybe<Scalars['String']['output']>;
	edition_is_numbered?: Maybe<Scalars['Boolean']['output']>;
	edition_is_unknown?: Maybe<Scalars['Boolean']['output']>;
	edition_number?: Maybe<Scalars['String']['output']>;
	edition_number_type?: Maybe<Edition_Number_Type>;
	estimate_high?: Maybe<Scalars['Float']['output']>;
	estimate_low?: Maybe<Scalars['Float']['output']>;
	estimate_raw?: Maybe<Scalars['String']['output']>;
	executed_year_end?: Maybe<Scalars['Int']['output']>;
	executed_year_start?: Maybe<Scalars['Int']['output']>;
	exhibition?: Maybe<Scalars['String']['output']>;
	general_proof_size?: Maybe<Scalars['Int']['output']>;
	house_of_commerce_size?: Maybe<Scalars['Int']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	is_bundle?: Maybe<Scalars['Boolean']['output']>;
	is_full_set?: Maybe<Scalars['Boolean']['output']>;
	lead?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	lot_attributes?: Maybe<
		Array<Maybe<Artwork_Details_Artwork_Lot_Symbol_Lookup>>
	>;
	lot_number?: Maybe<Scalars['String']['output']>;
	media?: Maybe<Scalars['String']['output']>;
	number_of_artworks?: Maybe<Scalars['Int']['output']>;
	number_of_pieces?: Maybe<Scalars['Int']['output']>;
	open_edition?: Maybe<Scalars['Boolean']['output']>;
	original_extraction_response?: Maybe<Scalars['String']['output']>;
	price?: Maybe<Scalars['Float']['output']>;
	price_includes_premium?: Maybe<Scalars['Boolean']['output']>;
	price_raw?: Maybe<Scalars['String']['output']>;
	processed_artwork_id?: Maybe<Scalars['String']['output']>;
	provenance?: Maybe<Scalars['String']['output']>;
	regular_edition_size?: Maybe<Scalars['Int']['output']>;
	sale_date?: Maybe<Scalars['DateTime']['output']>;
	sale_date_tz?: Maybe<Scalars['String']['output']>;
	sale_status?: Maybe<Scalars['String']['output']>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	series_size?: Maybe<Scalars['Int']['output']>;
	shipping?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	total_edition_size?: Maybe<Scalars['Int']['output']>;
	url?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Artwork_Details_Artwork_Lot_Symbol_Lookup = {
	__typename?: 'Artwork_Details_artwork_lot_symbol_lookup';
	Artwork_Details_id?: Maybe<Artwork_Details>;
	artwork_lot_symbol_lookup_key?: Maybe<Artwork_Lot_Symbol_Lookup>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Dimension_Type = {
	__typename?: 'Artwork_Dimension_Type';
	key?: Maybe<Artwork_Dimension_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Dimension_Type_Enum {
	LargestArtwork = 'LARGEST_ARTWORK',
	OverallSize = 'OVERALL_SIZE',
	PerArtwork = 'PER_ARTWORK',
}

export type Artwork_Status_Type = {
	__typename?: 'Artwork_Status_Type';
	key?: Maybe<Artwork_Status_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export enum Artwork_Status_Type_Enum {
	AwaitingLabelParser = 'AWAITING_LABEL_PARSER',
	AwaitingOcrExtraction = 'AWAITING_OCR_EXTRACTION',
	AwaitingReview = 'AWAITING_REVIEW',
	Completed = 'COMPLETED',
	Draft = 'DRAFT',
	IngestionFailed = 'INGESTION_FAILED',
	LabelParserExtracting = 'LABEL_PARSER_EXTRACTING',
	LabelParserExtractionFailed = 'LABEL_PARSER_EXTRACTION_FAILED',
	OcrExtractionFailed = 'OCR_EXTRACTION_FAILED',
	ReviewedAndSubmitted = 'REVIEWED_AND_SUBMITTED',
	Skipped = 'SKIPPED',
}

export type Artwork_Type = {
	__typename?: 'Artwork_Type';
	key?: Maybe<Artwork_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Artwork_Type_Enum {
	Collage = 'COLLAGE',
	DigitalArtNft = 'DIGITAL_ART_NFT',
	Drawing = 'DRAWING',
	Other = 'OTHER',
	Painting = 'PAINTING',
	Photography = 'PHOTOGRAPHY',
	Print = 'PRINT',
	Sculpture = 'SCULPTURE',
}

export type Checklist_Review_Status = {
	__typename?: 'Checklist_Review_Status';
	key?: Maybe<Checklist_Review_Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Checklist_Review_Status_Enum {
	Completed = 'COMPLETED',
	Failed = 'FAILED',
	InProgress = 'IN_PROGRESS',
}

export type DateValueFilter = {
	max?: InputMaybe<Scalars['DateOrYear']['input']>;
	min: Scalars['DateOrYear']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type Edition_Number_Type = {
	__typename?: 'Edition_Number_Type';
	key?: Maybe<Edition_Number_Type_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Edition_Number_Type_Enum {
	ArtistsProof = 'ARTISTS_PROOF',
	GeneralProof = 'GENERAL_PROOF',
	HorsDeCommerce = 'HORS_DE_COMMERCE',
	Regular = 'REGULAR',
	Undefined = 'UNDEFINED',
	Unknown = 'UNKNOWN',
}

export type Event_Review_Status = {
	__typename?: 'Event_Review_Status';
	key?: Maybe<Event_Review_Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Event_Review_Status_Enum {
	Completed = 'COMPLETED',
	InProgress = 'IN_PROGRESS',
	RequiresAttention = 'REQUIRES_ATTENTION',
}

export type Failed_Jobs = {
	__typename?: 'Failed_Jobs';
	data?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	reason?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type FairImageInsights = {
	__typename?: 'FairImageInsights';
	artworksAwaitingReview: Scalars['Int']['output'];
	artworksCompleted: Scalars['Int']['output'];
	artworksFailed: Scalars['Int']['output'];
	artworksTotal: Scalars['Int']['output'];
	awaitingCoordinateExtraction: Scalars['Int']['output'];
	awaitingCropping: Scalars['Int']['output'];
	awaitingReview: Scalars['Int']['output'];
	coordinateExtractionFailed: Scalars['Int']['output'];
	cropped: Scalars['Int']['output'];
	croppingFailed: Scalars['Int']['output'];
	cropsToBeMatched: Scalars['Int']['output'];
	dateTaken?: Maybe<Scalars['Date']['output']>;
	excluded: Scalars['Int']['output'];
	fairId: Scalars['ID']['output'];
	location?: Maybe<Scalars['String']['output']>;
	photographer: Scalars['String']['output'];
	processingState: FairProcessingState;
	title: Scalars['String']['output'];
	uploaded: Scalars['Int']['output'];
};

export type FairImageInsightsInput = {
	filters?: InputMaybe<FairImageInsightsSearchFilters>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	sort?: InputMaybe<Array<InputMaybe<FairImageInsightsSortInput>>>;
};

export type FairImageInsightsResponse = {
	__typename?: 'FairImageInsightsResponse';
	data: Array<FairImageInsights>;
	totalCount: Scalars['Int']['output'];
};

export type FairImageInsightsSearchFilters = {
	dateTaken?: InputMaybe<DateValueFilter>;
	fairId?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	fairTitle?: InputMaybe<Scalars['String']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	photographer?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
	processingState?: InputMaybe<FairProcessingState>;
};

export enum FairImageInsightsSortFields {
	ArtworksCompleted = 'ARTWORKS_COMPLETED',
	ArtworksToProcess = 'ARTWORKS_TO_PROCESS',
	CropsCompleted = 'CROPS_COMPLETED',
	CropsToBeMatched = 'CROPS_TO_BE_MATCHED',
	CropsToCheck = 'CROPS_TO_CHECK',
	Date = 'DATE',
	FairTitle = 'FAIR_TITLE',
	Photographer = 'PHOTOGRAPHER',
	UploadedItems = 'UPLOADED_ITEMS',
}

export type FairImageInsightsSortInput = {
	direction?: InputMaybe<SortDirection>;
	field: FairImageInsightsSortFields;
};

export enum FairProcessingState {
	HasArtworkProcessingFailed = 'HasArtworkProcessingFailed',
	HasArtworksToProcess = 'HasArtworksToProcess',
	HasAwaitingCoordinateExtraction = 'HasAwaitingCoordinateExtraction',
	HasAwaitingCropping = 'HasAwaitingCropping',
	HasCoordinateExtractionFailed = 'HasCoordinateExtractionFailed',
	HasCroppingFailed = 'HasCroppingFailed',
	HasCropsToBeMatched = 'HasCropsToBeMatched',
	HasCropsToCheck = 'HasCropsToCheck',
	NotDetermined = 'NotDetermined',
	ProcessingCompleted = 'ProcessingCompleted',
}

export type FetchStatsInput = {
	scrapedArtworkIds: Array<Scalars['ID']['input']>;
};

export type FetchStatsResponse = {
	__typename?: 'FetchStatsResponse';
	stats: Array<StatRow>;
};

export type FieldSortInput = {
	field: GroupBy;
	order: SortOrder;
};

export type FloatValueFilter = {
	max?: InputMaybe<Scalars['Float']['input']>;
	min: Scalars['Float']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type GetScrapedArtistsInput = {
	scrapedArtworkIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GetScrapedArtistsResponseItem = {
	__typename?: 'GetScrapedArtistsResponseItem';
	artist_details?: Maybe<ArtistDetails>;
	artist_reference_id?: Maybe<Scalars['String']['output']>;
	artist_text_key: Scalars['String']['output'];
	data_source?: Maybe<Scalars['String']['output']>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	scraped_artist_id?: Maybe<Scalars['String']['output']>;
};

export type GetScrapedArtworksInput = {
	ids: Array<Scalars['ID']['input']>;
};

export type GetScrapedArtworksResponse = {
	__typename?: 'GetScrapedArtworksResponse';
	scrapedArtworks: Array<Scraped_Artwork>;
	scrapedEvents: Array<Scraped_Event>;
	unsentScrapedEvents: Array<Scraped_Event>;
};

export type GetScrapedEntitiesInput = {
	scrapedArtworkIds: Array<Scalars['ID']['input']>;
};

export type GetScrapedEntitiesResponseItem = {
	__typename?: 'GetScrapedEntitiesResponseItem';
	associated_entity_feed?: Maybe<Associated_Entity_Feed>;
	id: Scalars['String']['output'];
	processed_entity_id?: Maybe<Scalars['String']['output']>;
	processed_reference_id?: Maybe<Scalars['String']['output']>;
};

export enum GroupBy {
	Artist = 'artist',
	Datasource = 'datasource',
	DateScraped = 'dateScraped',
	Dealer = 'dealer',
	SaleDate = 'saleDate',
	SaleName = 'saleName',
	Type = 'type',
	UserAssigned = 'userAssigned',
}

export type Ingestion_Exhibition = {
	__typename?: 'Ingestion_Exhibition';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	event_review_status?: Maybe<Event_Review_Status>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_data?: Maybe<Array<Maybe<Ingestion_Exhibition_Ingestion_Data>>>;
	processed_exhibition?: Maybe<Processed_Exhibition>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_Exhibition_Visit_Image = {
	__typename?: 'Ingestion_Exhibition_Visit_Image';
	Ingestion_Exhibition_id?: Maybe<Ingestion_Exhibition>;
	Visit_Image_id?: Maybe<Visit_Image>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Ingestion_Exhibition_Ingestion_Data = {
	__typename?: 'Ingestion_Exhibition_ingestion_data';
	Ingestion_Exhibition_id?: Maybe<Ingestion_Exhibition>;
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['String']['output']>;
};

export type Ingestion_Fair = {
	__typename?: 'Ingestion_Fair';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	event_review_status?: Maybe<Event_Review_Status>;
	id?: Maybe<Scalars['String']['output']>;
	images?: Maybe<Array<Maybe<Visit_Image>>>;
	ingestion_data?: Maybe<Array<Maybe<Ingestion_Fair_Ingestion_Data>>>;
	is_priority?: Maybe<Scalars['Boolean']['output']>;
	processed_fair?: Maybe<Processed_Fair>;
	status?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_Fair_Visit_Image = {
	__typename?: 'Ingestion_Fair_Visit_Image';
	Ingestion_Fair_id?: Maybe<Ingestion_Fair>;
	Visit_Image_id?: Maybe<Visit_Image>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Ingestion_Fair_Ingestion_Data = {
	__typename?: 'Ingestion_Fair_ingestion_data';
	Ingestion_Fair_id?: Maybe<Ingestion_Fair>;
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['String']['output']>;
};

export type Ingestion_Gallery_Offering = {
	__typename?: 'Ingestion_Gallery_Offering';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	event_review_status?: Maybe<Event_Review_Status>;
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_data?: Maybe<
		Array<Maybe<Ingestion_Gallery_Offering_Ingestion_Data>>
	>;
	processed_gallery?: Maybe<Processed_Organisation>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Ingestion_Gallery_Offering_Visit = {
	__typename?: 'Ingestion_Gallery_Offering_Visit';
	Ingestion_Gallery_Offering_id?: Maybe<Ingestion_Gallery_Offering>;
	Visit_id?: Maybe<Visit>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Ingestion_Gallery_Offering_Ingestion_Data = {
	__typename?: 'Ingestion_Gallery_Offering_ingestion_data';
	Ingestion_Gallery_Offering_id?: Maybe<Ingestion_Gallery_Offering>;
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['String']['output']>;
};

export type IntValueFilter = {
	max?: InputMaybe<Scalars['Int']['input']>;
	min: Scalars['Int']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type Location = {
	__typename?: 'Location';
	code: Scalars['String']['output'];
	country_nationality?: Maybe<Scalars['String']['output']>;
	name: Scalars['String']['output'];
};

export type LocationFilter = {
	cityNameOrCode?: InputMaybe<Scalars['String']['input']>;
	countryNameOrCode?: InputMaybe<Scalars['String']['input']>;
};

export type Manual_Upload = {
	__typename?: 'Manual_Upload';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	manually_added_artworks?: Maybe<Array<Maybe<Manually_Added_Artwork>>>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	reference_files?: Maybe<Array<Maybe<Manual_Upload_Files>>>;
	review_status?: Maybe<Checklist_Review_Status>;
	submitted_for_review?: Maybe<Scalars['Boolean']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Manual_Upload_Files = {
	__typename?: 'Manual_Upload_files';
	Manual_Upload_id?: Maybe<Manual_Upload>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Manually_Added_Artwork = {
	__typename?: 'Manually_Added_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	images?: Maybe<Array<Maybe<Manually_Added_Artwork_Files>>>;
	ingestion_job?: Maybe<Failed_Jobs>;
	ingestion_notes?: Maybe<Scalars['String']['output']>;
	manual_upload?: Maybe<Manual_Upload>;
	processed_activity_id?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Manually_Added_Artwork_Files = {
	__typename?: 'Manually_Added_Artwork_files';
	Manually_Added_Artwork_id?: Maybe<Manually_Added_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type MatchProvenanceResult = {
	__typename?: 'MatchProvenanceResult';
	processed_activity_id?: Maybe<Scalars['ID']['output']>;
	provenance_matched_activity_id?: Maybe<Scalars['ID']['output']>;
	provenance_matched_artwork_id?: Maybe<Scalars['ID']['output']>;
	scraped_artwork_id: Scalars['ID']['output'];
};

export type Mutation = {
	__typename?: 'Mutation';
	cropVisitImage: Scalars['Boolean']['output'];
	matchProvenance: Array<Maybe<MatchProvenanceResult>>;
	perspectiveCropVisitImageWithDimensions: Directus_Files;
	submitProvenanceLinking: ActivityTransferResponse;
	updateScrapedArtists?: Maybe<Array<Maybe<UpdateScrapedArtistsResponseItem>>>;
};

export type MutationCropVisitImageArgs = {
	visitImageId: Scalars['ID']['input'];
};

export type MutationMatchProvenanceArgs = {
	ids: Array<Scalars['ID']['input']>;
};

export type MutationPerspectiveCropVisitImageWithDimensionsArgs = {
	coordinates?: InputMaybe<Scalars['String']['input']>;
	heightCm: Scalars['Int']['input'];
	visitImageId: Scalars['ID']['input'];
	widthCm: Scalars['Int']['input'];
};

export type MutationSubmitProvenanceLinkingArgs = {
	ids: Array<Scalars['ID']['input']>;
};

export type MutationUpdateScrapedArtistsArgs = {
	input?: InputMaybe<UpdateScrapedArtistInput>;
};

export type Pdf = {
	__typename?: 'PDF';
	ai_extracted_text?: Maybe<Scalars['String']['output']>;
	artworks?: Maybe<Array<Maybe<Pdf_Artwork>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	discarded_images?: Maybe<Array<Maybe<Pdf_Discard_Image>>>;
	for_freelancers?: Maybe<Scalars['Boolean']['output']>;
	high_priority?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	includes_prices?: Maybe<Scalars['Boolean']['output']>;
	pages?: Maybe<Array<Maybe<Pdf_Page>>>;
	pdf_artwork_format_method?: Maybe<Scalars['String']['output']>;
	pdf_file?: Maybe<Directus_Files>;
	processed_artist?: Maybe<Processed_Artist>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	receipt_info?: Maybe<Receipt_Information>;
	review_status?: Maybe<Checklist_Review_Status>;
	status?: Maybe<Pdf_Status>;
	submitted_for_review?: Maybe<Scalars['Boolean']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Pdf_Artwork = {
	__typename?: 'PDF_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	images?: Maybe<Array<Maybe<Pdf_Artwork_Files>>>;
	ingestion_job?: Maybe<Failed_Jobs>;
	pdf?: Maybe<Pdf>;
	processed_activity_id?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Pdf_Artwork_Pdf_Image = {
	__typename?: 'PDF_Artwork_PDF_Image';
	PDF_Artwork_id?: Maybe<Pdf_Artwork>;
	PDF_Image_id?: Maybe<Pdf_Image>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Pdf_Artwork_Files = {
	__typename?: 'PDF_Artwork_files';
	PDF_Artwork_id?: Maybe<Pdf_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Pdf_Discard_Image = {
	__typename?: 'PDF_Discard_Image';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image?: Maybe<Directus_Files>;
	pdf?: Maybe<Pdf>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Pdf_Image = {
	__typename?: 'PDF_Image';
	id?: Maybe<Scalars['Int']['output']>;
	image?: Maybe<Directus_Files>;
	pdf_page?: Maybe<Pdf_Page>;
};

export type Pdf_Page = {
	__typename?: 'PDF_Page';
	extracted_images?: Maybe<Array<Maybe<Pdf_Image>>>;
	id?: Maybe<Scalars['Int']['output']>;
	page_number?: Maybe<Scalars['Int']['output']>;
	pdf?: Maybe<Pdf>;
	status?: Maybe<Scalars['String']['output']>;
	text?: Maybe<Scalars['String']['output']>;
};

export type Pdf_Page_Files = {
	__typename?: 'PDF_Page_files';
	PDF_Page_id?: Maybe<Pdf_Page>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Pdf_Status = {
	__typename?: 'PDF_Status';
	key?: Maybe<Pdf_Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Pdf_Status_Enum {
	AwaitingImageExtraction = 'AWAITING_IMAGE_EXTRACTION',
	ImagesExtracted = 'IMAGES_EXTRACTED',
	ImagesExtracting = 'IMAGES_EXTRACTING',
	ImageExtractionFailed = 'IMAGE_EXTRACTION_FAILED',
}

export type Pdf_Upload_App = {
	__typename?: 'PDF_Upload_App';
	id?: Maybe<Scalars['Int']['output']>;
	pdf?: Maybe<Directus_Files>;
	source_info?: Maybe<Scalars['String']['output']>;
};

export type Person = {
	__typename?: 'Person';
	first_name?: Maybe<Scalars['String']['output']>;
	id: Scalars['ID']['output'];
	last_name?: Maybe<Scalars['String']['output']>;
	nationalities?: Maybe<Array<Maybe<Location>>>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type PhoneVisitGrouping = {
	__typename?: 'PhoneVisitGrouping';
	imageDate: Scalars['String']['output'];
	location?: Maybe<Scalars['String']['output']>;
	numberOfImages: Scalars['Int']['output'];
	phone: Scalars['Int']['output'];
	photographerEmail?: Maybe<Scalars['String']['output']>;
	photographerId?: Maybe<Scalars['String']['output']>;
};

export type PhoneVisitGroupingsInput = {
	imageDate?: InputMaybe<Scalars['String']['input']>;
	location?: InputMaybe<Scalars['String']['input']>;
	photographerEmail?: InputMaybe<Scalars['String']['input']>;
};

export type PhoneVisitGroupingsResponse = {
	__typename?: 'PhoneVisitGroupingsResponse';
	data?: Maybe<Array<Maybe<PhoneVisitGrouping>>>;
};

export type Processed_Artist = {
	__typename?: 'Processed_Artist';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	processed_artist_json?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['Int']['output']>;
	year_death?: Maybe<Scalars['Int']['output']>;
};

export type Processed_Artwork = {
	__typename?: 'Processed_Artwork';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
	processed_artist_id?: Maybe<Scalars['String']['output']>;
	processed_artwork_id?: Maybe<Scalars['String']['output']>;
	processed_artwork_json?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	year_birth?: Maybe<Scalars['String']['output']>;
	year_death?: Maybe<Scalars['String']['output']>;
};

export type Processed_Exhibition = {
	__typename?: 'Processed_Exhibition';
	artists?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	organisers?: Maybe<Scalars['String']['output']>;
	processed_exhibition_id?: Maybe<Scalars['String']['output']>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Processed_Exhibition_Processed_Artist = {
	__typename?: 'Processed_Exhibition_Processed_Artist';
	Processed_Artist_processed_artist_id?: Maybe<Processed_Artist>;
	Processed_Exhibition_processed_exhibition_id?: Maybe<Processed_Exhibition>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Processed_Fair = {
	__typename?: 'Processed_Fair';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	processed_fair_id?: Maybe<Scalars['String']['output']>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Processed_Organisation = {
	__typename?: 'Processed_Organisation';
	entity?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
};

export type Query = {
	__typename?: 'Query';
	fetchStats: FetchStatsResponse;
	findArtworkMatches: Array<ArtworkMatch>;
	getScrapedArtists?: Maybe<Array<Maybe<GetScrapedArtistsResponseItem>>>;
	getScrapedArtworks: GetScrapedArtworksResponse;
	getScrapedEntities: Array<GetScrapedEntitiesResponseItem>;
	insights_fairImageInsights: FairImageInsightsResponse;
	phoneVisitImageGroupings?: Maybe<PhoneVisitGroupingsResponse>;
	searchScrapeData?: Maybe<SearchScrapeDataResponse>;
	userToken?: Maybe<UserTokenResponse>;
};

export type QueryFetchStatsArgs = {
	input: FetchStatsInput;
};

export type QueryFindArtworkMatchesArgs = {
	imageId: Scalars['ID']['input'];
	limit?: InputMaybe<Scalars['Int']['input']>;
	title?: InputMaybe<Scalars['String']['input']>;
};

export type QueryGetScrapedArtistsArgs = {
	input?: InputMaybe<GetScrapedArtistsInput>;
};

export type QueryGetScrapedArtworksArgs = {
	input: GetScrapedArtworksInput;
};

export type QueryGetScrapedEntitiesArgs = {
	input: GetScrapedEntitiesInput;
};

export type QueryInsights_FairImageInsightsArgs = {
	input?: InputMaybe<FairImageInsightsInput>;
};

export type QueryPhoneVisitImageGroupingsArgs = {
	input?: InputMaybe<PhoneVisitGroupingsInput>;
};

export type QuerySearchScrapeDataArgs = {
	input: SearchScrapeDataInput;
};

export type Receipt_Information = {
	__typename?: 'Receipt_Information';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	receive_date?: Maybe<Scalars['DateTime']['output']>;
	receiver?: Maybe<Scalars['String']['output']>;
	sender?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Scalars['String']['output']>;
	user_updated?: Maybe<Directus_Users>;
};

export type ScrapedArtistInput = {
	artist_details?: InputMaybe<ArtistDetailsInput>;
	artist_reference_id?: InputMaybe<Scalars['String']['input']>;
	artist_text_key: Scalars['String']['input'];
	data_source?: InputMaybe<Scalars['String']['input']>;
	processed_artist_id?: InputMaybe<Scalars['String']['input']>;
	scraped_artist_id?: InputMaybe<Scalars['String']['input']>;
};

export enum ScrapedDataType {
	Activity = 'ACTIVITY',
	Auction = 'AUCTION',
	PrivateMarket = 'PRIVATE_MARKET',
}

export type Scraped_Artist = {
	__typename?: 'Scraped_Artist';
	artist_details?: Maybe<Artist_Details>;
	artist_text_key?: Maybe<Scalars['String']['output']>;
	data_source?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	scraped_artworks?: Maybe<Array<Maybe<Scraped_Artwork_Scraped_Artist>>>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_Artwork = {
	__typename?: 'Scraped_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	artwork_feed?: Maybe<Artwork_Feed>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	extracted_provenance_lines?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	images?: Maybe<Array<Maybe<Scraped_Artwork_Files>>>;
	ingestion_job?: Maybe<Failed_Jobs>;
	processed_activity_id?: Maybe<Scalars['String']['output']>;
	provenance_matched_activity_id?: Maybe<Scalars['String']['output']>;
	provenance_matched_artwork_id?: Maybe<Scalars['String']['output']>;
	scraped_artists?: Maybe<Array<Maybe<Scraped_Artwork_Scraped_Artist>>>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_Artwork_Artist_Details = {
	__typename?: 'Scraped_Artwork_Artist_Details';
	Artist_Details_id?: Maybe<Artist_Details>;
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Scraped_Artwork_Scraped_Artist = {
	__typename?: 'Scraped_Artwork_Scraped_Artist';
	Scraped_Artist_id?: Maybe<Scraped_Artist>;
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Scraped_Artwork_Files = {
	__typename?: 'Scraped_Artwork_files';
	Scraped_Artwork_id?: Maybe<Scraped_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Scraped_Entity = {
	__typename?: 'Scraped_Entity';
	associated_entity_feed?: Maybe<Associated_Entity_Feed>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	processed_entity_id?: Maybe<Scalars['String']['output']>;
	processed_reference_id?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Scraped_Event = {
	__typename?: 'Scraped_Event';
	art_event_feed?: Maybe<Art_Event_Feed>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	processed_event_id?: Maybe<Scalars['String']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type SearchFieldsInput = {
	artist?: InputMaybe<Scalars['String']['input']>;
	datasource?: InputMaybe<Scalars['String']['input']>;
	dateScraped?: InputMaybe<Scalars['String']['input']>;
	dateScrapedEnd?: InputMaybe<Scalars['String']['input']>;
	dealer?: InputMaybe<Scalars['String']['input']>;
	saleDate?: InputMaybe<Scalars['Date']['input']>;
	saleDateEnd?: InputMaybe<Scalars['Date']['input']>;
	saleName?: InputMaybe<Scalars['String']['input']>;
	showProcessed?: InputMaybe<Scalars['Boolean']['input']>;
	type?: InputMaybe<ScrapedDataType>;
	userAssignedId?: InputMaybe<Scalars['String']['input']>;
};

export type SearchScrapeData = {
	__typename?: 'SearchScrapeData';
	artist?: Maybe<Scalars['String']['output']>;
	artworkFeedIds?: Maybe<Scalars['String']['output']>;
	count?: Maybe<Scalars['Int']['output']>;
	datasource?: Maybe<Scalars['String']['output']>;
	dateScraped?: Maybe<Scalars['Date']['output']>;
	dealer?: Maybe<Scalars['String']['output']>;
	saleDate?: Maybe<Scalars['Date']['output']>;
	saleName?: Maybe<Scalars['String']['output']>;
	type?: Maybe<ScrapedDataType>;
	userAssigned?: Maybe<Scalars['String']['output']>;
};

export type SearchScrapeDataIDsResponse = {
	__typename?: 'SearchScrapeDataIDsResponse';
	ids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type SearchScrapeDataInput = {
	groupBy: Array<InputMaybe<GroupBy>>;
	limit: Scalars['Int']['input'];
	search?: InputMaybe<SearchFieldsInput>;
	sort?: InputMaybe<Array<InputMaybe<FieldSortInput>>>;
};

export type SearchScrapeDataResponse = {
	__typename?: 'SearchScrapeDataResponse';
	data?: Maybe<Array<Maybe<SearchScrapeData>>>;
	total: Scalars['Int']['output'];
};

export enum SortDirection {
	Asc = 'ASC',
	Desc = 'DESC',
}

export enum SortOrder {
	Asc = 'ASC',
	Desc = 'DESC',
}

export type StatRow = {
	__typename?: 'StatRow';
	changed?: Maybe<Scalars['Int']['output']>;
	changedPercentage?: Maybe<Scalars['Float']['output']>;
	field?: Maybe<Scalars['String']['output']>;
	reviewed?: Maybe<Scalars['Int']['output']>;
};

export type StringValueFilter = {
	max?: InputMaybe<Scalars['String']['input']>;
	min: Scalars['String']['input'];
	operator?: InputMaybe<ValueFilterOperator>;
};

export type UpdateScrapedArtistInput = {
	scrapedArtists?: InputMaybe<Array<InputMaybe<ScrapedArtistInput>>>;
	scrapedArtworkIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type UpdateScrapedArtistsResponseItem = {
	__typename?: 'UpdateScrapedArtistsResponseItem';
	artist_text_key?: Maybe<Scalars['String']['output']>;
	scraped_artist_id?: Maybe<Scalars['String']['output']>;
	scraped_artwork_ids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type UserTokenResponse = {
	__typename?: 'UserTokenResponse';
	token: Scalars['String']['output'];
};

export enum ValueFilterOperator {
	Between = 'Between',
	Equal = 'Equal',
	GreaterThan = 'GreaterThan',
	GreaterThanOrEqual = 'GreaterThanOrEqual',
	LessThan = 'LessThan',
	LessThanOrEqual = 'LessThanOrEqual',
}

export type Visit = {
	__typename?: 'Visit';
	artworks?: Maybe<Array<Maybe<Visit_Artwork>>>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	discarded_visit_images?: Maybe<Array<Maybe<Visit_Discard_Image>>>;
	id?: Maybe<Scalars['String']['output']>;
	ingestion_exhibition?: Maybe<Array<Maybe<Ingestion_Exhibition>>>;
	ingestion_fair?: Maybe<Array<Maybe<Ingestion_Fair>>>;
	ingestion_gallery_offering?: Maybe<Array<Maybe<Ingestion_Gallery_Offering>>>;
	review_status?: Maybe<Checklist_Review_Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit_images?: Maybe<Array<Maybe<Visit_Image>>>;
};

export type Visit_Artwork = {
	__typename?: 'Visit_Artwork';
	artwork_details?: Maybe<Artwork_Details>;
	artwork_image?: Maybe<Visit_Image>;
	created_artwork_id?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	empty_artwork_image?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	images?: Maybe<Array<Maybe<Visit_Artwork_Files>>>;
	ingestion_job?: Maybe<Failed_Jobs>;
	is_installation_shot?: Maybe<Scalars['Boolean']['output']>;
	label_image?: Maybe<Visit_Image>;
	label_text?: Maybe<Scalars['String']['output']>;
	processed_activity_id?: Maybe<Scalars['String']['output']>;
	processed_artwork_id?: Maybe<Scalars['String']['output']>;
	processed_fair_exhibitor_org?: Maybe<Processed_Organisation>;
	sort?: Maybe<Scalars['Int']['output']>;
	status?: Maybe<Artwork_Status_Type>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
};

export type Visit_Artwork_Files = {
	__typename?: 'Visit_Artwork_files';
	Visit_Artwork_id?: Maybe<Visit_Artwork>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Visit_Discard_Image = {
	__typename?: 'Visit_Discard_Image';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
	visit_image?: Maybe<Visit_Image>;
};

export type Visit_Image = {
	__typename?: 'Visit_Image';
	crop_type?: Maybe<Scalars['String']['output']>;
	data_admin_submitted_coordinates?: Maybe<Scalars['String']['output']>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	extracted_best_guess_coordinates_from_api?: Maybe<
		Scalars['String']['output']
	>;
	fair?: Maybe<Ingestion_Fair>;
	id?: Maybe<Scalars['String']['output']>;
	image_content_type?: Maybe<Scalars['String']['output']>;
	image_taken_date?: Maybe<Scalars['DateTime']['output']>;
	original_uncropped_image?: Maybe<Directus_Files>;
	perspective_cropped_image_with_dimensions?: Maybe<Directus_Files>;
	perspective_cropped_image_without_dimensions?: Maybe<Directus_Files>;
	photographer?: Maybe<Directus_Users>;
	processed_by_data_admin?: Maybe<Scalars['Boolean']['output']>;
	rectangular_cropped_image?: Maybe<Directus_Files>;
	source?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Visit_Image_Status>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit?: Maybe<Visit>;
	visit_artwork?: Maybe<Visit_Artwork>;
	visit_phone_registration?: Maybe<
		Array<Maybe<Visit_Phone_Registration_Visit_Image>>
	>;
};

export type Visit_Image_Status = {
	__typename?: 'Visit_Image_Status';
	key?: Maybe<Visit_Image_Status_Enum>;
	name?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export enum Visit_Image_Status_Enum {
	AwaitingCoordinateExtraction = 'AWAITING_COORDINATE_EXTRACTION',
	AwaitingCropping = 'AWAITING_CROPPING',
	AwaitingReview = 'AWAITING_REVIEW',
	CoordinateExtractionFailed = 'COORDINATE_EXTRACTION_FAILED',
	Cropped = 'CROPPED',
	CroppingFailed = 'CROPPING_FAILED',
	Excluded = 'EXCLUDED',
}

export type Visit_Phone = {
	__typename?: 'Visit_Phone';
	authentication_status?: Maybe<Visit_Phone_Status>;
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	icloud_account?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	last_sync_date?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Visit_Phone_Registration = {
	__typename?: 'Visit_Phone_Registration';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	end_date?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	phone?: Maybe<Visit_Phone>;
	photographer?: Maybe<Directus_Users>;
	start_date?: Maybe<Scalars['DateTime']['output']>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
};

export type Visit_Phone_Registration_Visit_Image = {
	__typename?: 'Visit_Phone_Registration_Visit_Image';
	date_created?: Maybe<Scalars['DateTime']['output']>;
	date_updated?: Maybe<Scalars['DateTime']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image_date?: Maybe<Scalars['DateTime']['output']>;
	phone?: Maybe<Visit_Phone>;
	phone_registration?: Maybe<Visit_Phone_Registration>;
	user_created?: Maybe<Directus_Users>;
	user_updated?: Maybe<Directus_Users>;
	visit_image?: Maybe<Visit_Image>;
};

export type Visit_Phone_Status = {
	__typename?: 'Visit_Phone_Status';
	key?: Maybe<Visit_Phone_Status_Enum>;
};

export enum Visit_Phone_Status_Enum {
	Authenticated = 'AUTHENTICATED',
	Unauthenticated = 'UNAUTHENTICATED',
}

export type Visit_Visit_Items = {
	__typename?: 'Visit_visit_items';
	Visit_id?: Maybe<Visit>;
	collection?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item?: Maybe<Scalars['String']['output']>;
	sort?: Maybe<Scalars['Int']['output']>;
};

export type Art_Event_Feed = {
	__typename?: 'art_event_feed';
	artlogic_link?: Maybe<Array<Maybe<Artlogic_Link>>>;
	artwork_feed?: Maybe<Array<Maybe<Artwork_Feed>>>;
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['DateTime']['output']>;
	created_by?: Maybe<Directus_Users>;
	data_source?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	ends_at?: Maybe<Scalars['DateTime']['output']>;
	ends_at_tz?: Maybe<Scalars['String']['output']>;
	event_type?: Maybe<Art_Event_Type_Lookup>;
	external_id?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image?: Maybe<Directus_Files>;
	image_url?: Maybe<Scalars['String']['output']>;
	is_charity_fundraiser?: Maybe<Scalars['Boolean']['output']>;
	is_closed?: Maybe<Scalars['Boolean']['output']>;
	location?: Maybe<Scalars['String']['output']>;
	organization?: Maybe<Scalars['String']['output']>;
	pdf_url?: Maybe<Scalars['String']['output']>;
	processor_review_event?: Maybe<Array<Maybe<Scraped_Event>>>;
	sale_number?: Maybe<Scalars['String']['output']>;
	starts_at?: Maybe<Scalars['DateTime']['output']>;
	starts_at_tz?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['DateTime']['output']>;
	updated_by?: Maybe<Directus_Users>;
	url?: Maybe<Scalars['String']['output']>;
};

export type Art_Event_Type_Lookup = {
	__typename?: 'art_event_type_lookup';
	activity_type?: Maybe<Scalars['String']['output']>;
	auction_type?: Maybe<Scalars['String']['output']>;
	ignore_for_ingestion?: Maybe<Scalars['Boolean']['output']>;
	key?: Maybe<Art_Event_Type_Lookup_Enum>;
	listing_type?: Maybe<Scalars['String']['output']>;
	processor_type?: Maybe<Scalars['String']['output']>;
};

export enum Art_Event_Type_Lookup_Enum {
	AftersaleAuction = 'aftersale_auction',
	BinAuction = 'bin_auction',
	Fair = 'fair',
	FixedPrice = 'fixed_price',
	GalleryExhibition = 'gallery_exhibition',
	InquiryRequired = 'inquiry_required',
	LiveAuction = 'live_auction',
	MuseumAcquisition = 'museum_acquisition',
	MuseumExhibition = 'museum_exhibition',
	NotReported = 'not_reported',
	OnlineAuction = 'online_auction',
	PrivateSale = 'private_sale',
	UnlimitedAuction = 'unlimited_auction',
}

export type Artwork_Artwork_Lot_Symbol_Link = {
	__typename?: 'artwork_artwork_lot_symbol_link';
	artwork_id?: Maybe<Artwork_Feed>;
	id?: Maybe<Scalars['String']['output']>;
	lot_symbol?: Maybe<Artwork_Lot_Symbol_Lookup>;
};

export type Artwork_Associated_Entity_Link = {
	__typename?: 'artwork_associated_entity_link';
	artwork_id?: Maybe<Artwork_Feed>;
	associated_entity_id?: Maybe<Associated_Entity_Feed>;
	association_type?: Maybe<Association_Type_Lookup>;
	id?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Classification_Lookup = {
	__typename?: 'artwork_classification_lookup';
	arteye_type?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Feed = {
	__typename?: 'artwork_feed';
	archived?: Maybe<Scalars['Boolean']['output']>;
	artist?: Maybe<Scalars['String']['output']>;
	artist_clean_name?: Maybe<Scalars['String']['output']>;
	artist_nationality?: Maybe<Country>;
	artist_year_born?: Maybe<Scalars['Int']['output']>;
	artist_year_died?: Maybe<Scalars['Int']['output']>;
	artwork_type?: Maybe<Artwork_Type_Lookup>;
	associated_entities?: Maybe<Array<Maybe<Artwork_Associated_Entity_Link>>>;
	auction_records?: Maybe<Scalars['String']['output']>;
	cr_number?: Maybe<Scalars['String']['output']>;
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['DateTime']['output']>;
	created_by?: Maybe<Directus_Users>;
	data_source?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	dimensions?: Maybe<Scalars['String']['output']>;
	dimensions_depth?: Maybe<Scalars['Float']['output']>;
	dimensions_height?: Maybe<Scalars['Float']['output']>;
	dimensions_uom?: Maybe<Uom>;
	dimensions_width?: Maybe<Scalars['Float']['output']>;
	edition?: Maybe<Scalars['String']['output']>;
	edition_ap_size?: Maybe<Scalars['Int']['output']>;
	edition_hc_size?: Maybe<Scalars['Int']['output']>;
	edition_is_numbered?: Maybe<Scalars['Boolean']['output']>;
	edition_is_unlimited?: Maybe<Scalars['Boolean']['output']>;
	edition_number?: Maybe<Scalars['String']['output']>;
	edition_size?: Maybe<Scalars['Int']['output']>;
	edition_size_reg?: Maybe<Scalars['Int']['output']>;
	edition_size_unknown?: Maybe<Scalars['Boolean']['output']>;
	edition_sqn?: Maybe<Scalars['Int']['output']>;
	estimate?: Maybe<Scalars['String']['output']>;
	estimate_currency?: Maybe<Currency>;
	estimate_high?: Maybe<Scalars['Float']['output']>;
	estimate_low?: Maybe<Scalars['Float']['output']>;
	event_external_id?: Maybe<Scalars['String']['output']>;
	event_id?: Maybe<Art_Event_Feed>;
	event_session_sqn?: Maybe<Scalars['Int']['output']>;
	exhibited?: Maybe<Scalars['String']['output']>;
	external_id?: Maybe<Scalars['String']['output']>;
	extra_image_urls?: Maybe<Scalars['String']['output']>;
	extra_images?: Maybe<Array<Maybe<Artwork_Feed_Files>>>;
	historically_processed?: Maybe<Scalars['Boolean']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	image_url?: Maybe<Scalars['String']['output']>;
	inscription_date?: Maybe<Scalars['String']['output']>;
	inscription_position?: Maybe<Scalars['String']['output']>;
	inscriptions?: Maybe<Scalars['String']['output']>;
	literature?: Maybe<Scalars['String']['output']>;
	lot_condition?: Maybe<Scalars['String']['output']>;
	lot_description?: Maybe<Scalars['String']['output']>;
	lot_lead?: Maybe<Scalars['String']['output']>;
	lot_number?: Maybe<Scalars['String']['output']>;
	lot_shipping?: Maybe<Scalars['String']['output']>;
	lot_symbols?: Maybe<Array<Maybe<Artwork_Artwork_Lot_Symbol_Link>>>;
	lot_title?: Maybe<Scalars['String']['output']>;
	mediums?: Maybe<Scalars['String']['output']>;
	price?: Maybe<Scalars['String']['output']>;
	price_amount?: Maybe<Scalars['Float']['output']>;
	price_currency?: Maybe<Currency>;
	price_includes_bp?: Maybe<Scalars['Boolean']['output']>;
	primary_image?: Maybe<Directus_Files>;
	processor_review_artwork?: Maybe<Array<Maybe<Scraped_Artwork>>>;
	provenance?: Maybe<Scalars['String']['output']>;
	sale_ends_at?: Maybe<Scalars['DateTime']['output']>;
	sale_ends_at_tz?: Maybe<Scalars['String']['output']>;
	sale_is_closed?: Maybe<Scalars['Boolean']['output']>;
	sale_starts_at?: Maybe<Scalars['DateTime']['output']>;
	sale_starts_at_tz?: Maybe<Scalars['String']['output']>;
	sale_status?: Maybe<Artwork_Sale_Status_Lookup>;
	saleroom_notice?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['DateTime']['output']>;
	updated_by?: Maybe<Directus_Users>;
	url?: Maybe<Scalars['String']['output']>;
	year_made?: Maybe<Scalars['String']['output']>;
	year_made_from?: Maybe<Scalars['Int']['output']>;
	year_made_to?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Feed_Artwork_Feed_Artwork_Lot_Symbol_Lookup = {
	__typename?: 'artwork_feed_artwork_feed_artwork_lot_symbol_lookup';
	artwork_feed_artwork_lot_symbol_lookup_id?: Maybe<Artwork_Feed_Artwork_Lot_Symbol_Lookup>;
	artwork_feed_id?: Maybe<Artwork_Feed>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Feed_Artwork_Lot_Symbol_Lookup = {
	__typename?: 'artwork_feed_artwork_lot_symbol_lookup';
	artwork_feed_id?: Maybe<Artwork_Feed>;
	artwork_lot_symbol_lookup_key?: Maybe<Artwork_Lot_Symbol_Lookup>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Feed_Files = {
	__typename?: 'artwork_feed_files';
	artwork_feed_id?: Maybe<Artwork_Feed>;
	directus_files_id?: Maybe<Directus_Files>;
	id?: Maybe<Scalars['Int']['output']>;
};

export type Artwork_Feed_Revisions = {
	__typename?: 'artwork_feed_revisions';
	added?: Maybe<Scalars['String']['output']>;
	changed?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	item_id?: Maybe<Scalars['String']['output']>;
	new_data?: Maybe<Scalars['String']['output']>;
	old_data?: Maybe<Scalars['String']['output']>;
	operation?: Maybe<Scalars['String']['output']>;
	removed?: Maybe<Scalars['String']['output']>;
	revision_time?: Maybe<Scalars['DateTime']['output']>;
	table_name?: Maybe<Scalars['String']['output']>;
};

export type Artwork_Lot_Symbol_Lookup = {
	__typename?: 'artwork_lot_symbol_lookup';
	arteye_attribute_type?: Maybe<Scalars['String']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	display_name?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Lot_Symbol_Lookup_Enum>;
};

export enum Artwork_Lot_Symbol_Lookup_Enum {
	CatalogueHighlight = 'CATALOGUE_HIGHLIGHT',
	OnCover = 'ON_COVER',
	ArtistsResaleRights = 'artists_resale_rights',
	GuaranteedSale = 'guaranteed_sale',
	InterestedParty = 'interested_party',
	IrrevocableBid = 'irrevocable_bid',
	NoReserve = 'no_reserve',
	OwnershipInterest = 'ownership_interest',
}

export type Artwork_Sale_Status_Lookup = {
	__typename?: 'artwork_sale_status_lookup';
	arteye_status?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Sale_Status_Lookup_Enum>;
};

export enum Artwork_Sale_Status_Lookup_Enum {
	BoughtIn = 'bought_in',
	InquiryRequired = 'inquiry_required',
	NotForSale = 'not_for_sale',
	OnSale = 'on_sale',
	Sold = 'sold',
	Withdrawn = 'withdrawn',
}

export type Artwork_Type_Lookup = {
	__typename?: 'artwork_type_lookup';
	arteye_type?: Maybe<Scalars['String']['output']>;
	key?: Maybe<Artwork_Type_Lookup_Enum>;
};

export enum Artwork_Type_Lookup_Enum {
	Ceramic = 'ceramic',
	DigitalArt = 'digital_art',
	Furniture = 'furniture',
	Glass = 'glass',
	Jewelry = 'jewelry',
	Lighting = 'lighting',
	Metalware = 'metalware',
	Miniature = 'miniature',
	Nft = 'nft',
	Other = 'other',
	Painting = 'painting',
	Photography = 'photography',
	Print = 'print',
	Sculpture = 'sculpture',
	Textile = 'textile',
	WorkOnPaper = 'work_on_paper',
}

export type Associated_Entity_Feed = {
	__typename?: 'associated_entity_feed';
	crawl_job?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['DateTime']['output']>;
	created_by?: Maybe<Directus_Users>;
	description?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	scraped_entity?: Maybe<Array<Maybe<Scraped_Entity>>>;
	updated_at?: Maybe<Scalars['DateTime']['output']>;
	updated_by?: Maybe<Directus_Users>;
	url?: Maybe<Scalars['String']['output']>;
};

export type Association_Type_Lookup = {
	__typename?: 'association_type_lookup';
	key?: Maybe<Association_Type_Lookup_Enum>;
};

export enum Association_Type_Lookup_Enum {
	Broker = 'broker',
	Buyer = 'buyer',
	Dealer = 'dealer',
	Donee = 'donee',
	Exhibitor = 'exhibitor',
	Seller = 'seller',
}

export type Country = {
	__typename?: 'country';
	code?: Maybe<Scalars['String']['output']>;
	country_nationality?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	nationality?: Maybe<Scalars['String']['output']>;
};

export type Currency = {
	__typename?: 'currency';
	code?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	symbol?: Maybe<Scalars['String']['output']>;
};

export type Currency_Rate = {
	__typename?: 'currency_rate';
	currency?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['String']['output']>;
	rate_usd?: Maybe<Scalars['Float']['output']>;
	timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export type Directus_Files = {
	__typename?: 'directus_files';
	description?: Maybe<Scalars['String']['output']>;
	filename_disk?: Maybe<Scalars['String']['output']>;
	filename_download?: Maybe<Scalars['String']['output']>;
	height?: Maybe<Scalars['Int']['output']>;
	id: Scalars['ID']['output'];
	storage?: Maybe<Scalars['String']['output']>;
	title?: Maybe<Scalars['String']['output']>;
	type?: Maybe<Scalars['String']['output']>;
	width?: Maybe<Scalars['Int']['output']>;
};

export type Directus_Users = {
	__typename?: 'directus_users';
	email?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	role?: Maybe<Scalars['String']['output']>;
};

export type Timezone = {
	__typename?: 'timezone';
	code?: Maybe<Scalars['String']['output']>;
};

export type Uom = {
	__typename?: 'uom';
	code?: Maybe<Scalars['String']['output']>;
	name?: Maybe<Scalars['String']['output']>;
};

export enum Uom_Enum {
	Cm = 'cm',
	Ft = 'ft',
	In = 'in',
	M = 'm',
}
